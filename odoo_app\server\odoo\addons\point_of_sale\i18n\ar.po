# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"This issue occurs because the quantity becomes zero after rounding during the conversion. To fix this, adjust the conversion factors or rounding method to ensure that even the smallest quantity in the original unit does not round down to zero in the target unit."
msgstr ""
"\n"
"تحدث هذه المشكلة لأن الكمية تصبح صفراً بعد تقريبها أثناء عملية التحويل. لإصلاح هذه المشكلة، قم بتعديل عوامل التحويل أو طريقة التقريب لضمان عدم تقريب أصغر كمية في الوحدة الأصلية إلى الصفر في الوحدة الهدف. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid " - From \"%s\" to \"%s\""
msgstr " - من \"%s\" إلى \"%s\" "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - closing"
msgstr "- الإغلاق "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - opening"
msgstr "- الفتح "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "استرداد الأموال "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "استخدم طابعة متصلة بجهاز IoT "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "% Disc"
msgstr "نسبة الخصم % "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (غير مستخدَم) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "%(vatLabel)s: %(vatId)s"
msgstr "%(vatLabel)s: %(vatId)s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s دفع نقطة البيع %s في %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "%s customer(s) found for \"%s\"."
msgstr "تم العثور على %s عميل (عملاء) لـ \"%s\". "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "%s fiscal position(s) added to the configuration."
msgstr "%s أوضاع مالية تمت إضافتها إلى التهيئة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order?"
msgstr ""
"%s يحتوي على مبلغ إجمالي قدره %s، هل أنت متأكد من أنك ترغب في حذف هذا الطلب؟"
" "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "%s product(s) found for \"%s\"."
msgstr "تم العثور على %s منتج (منتجات) لـ \"%s\". "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s untaxed"
msgstr "%s غير شامل الضريبة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s with %s"
msgstr "%s مع %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(استرجاع %(session)s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(أثناء الفتح) "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr "+ متجر جديد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "/pos/ticket and use the code below to request an invoice online"
msgstr "/نقطة البيع/التذكرة واستخدم الكود أدناه لطلب فاتورة عبر الإنترنت "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "0.00"
msgstr "0.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr "00014-001-0001"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10"
msgstr "10"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "100.00"
msgstr "100.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "1000"
msgstr "1000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000"
msgstr "10000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000.00"
msgstr "10000.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "123.45"
msgstr "123.45"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "1234567890"
msgstr "1234567890"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "2-03-2000 9:00 AM"
msgstr "2-03-2000 9:00 AM"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "45"
msgstr "45"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid ""
"5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr ""
"كود من 5 خانات مكون من حروف وأرقام ليتم استخدامه بواسطة مستخدم البوابة لطلب "
"فاتورة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "5.00"
msgstr "5.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "50.00"
msgstr "50.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "567789"
msgstr "567789"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "95.00"
msgstr "95.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "987657"
msgstr "987657"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "99.99"
msgstr "99.99"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"عربة التسوق \"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<i class=\"fa fa-info-circle me-1\" title=\"هذا الإعداد مشترك في كافة نقاط "
"البيع. \" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> تحرير"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>How to manage tax-included prices"
msgstr ""
"<i class=\"oi oi-fw oi-arrow-right\"/>كيفية التعامل مع الأسعار شاملة الضرائب"
" "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"<p>Dear %(client_name)s,<br/>Here is your electronic ticket for the "
"%(pos_name)s. </p>"
msgstr ""
"<p>عزيزنا %(client_name)s،<br/>إليك تذكرتك الإلكترونية لـ %(pos_name)s. </p>"
" "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p>أأنت جاهز لإلقاء نظرة على <b>واجهة نقطة البيع</b>؟ فلنبدأ جلستنا "
"الأولى.</p> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<span class=\"o_form_label\">الباركودات</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"هذه الإعدادات مشتركة في كافة نقاط البيع. \" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Cash Register</span>"
msgstr "<span class=\"o_stat_text\">آلة تسجيل النقد</span> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Journal Items</span>"
msgstr "<span class=\"o_stat_text\">عناصر دفتر اليومية</span> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>تخطي شاشة المعاينة</b></span> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "<span invisible=\"is_total_cost_computed\">TBD</span>"
msgstr "<span invisible=\"is_total_cost_computed\">بانتظار التحديد</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Balance</span>"
msgstr "<span>الرصيد</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Closing</span>"
msgstr "<span>جاري الإقفال</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>إعداد التقارير</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>عرض</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; أجهزة الدفع</strong>\n"
"                                    لتتمكن من تثبيت جهاز الدفع وجعله طريقة دفع مدمجة تماماً. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>الخصومات</strong>: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr "<strong>مبلغ تصل قيمته إلى:</strong> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Config names</strong>"
msgstr "<strong>تهيئة الأسماء</strong> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>End of session note:</strong>"
msgstr "<strong>ملاحظة نهاية الجلسة:</strong> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong>عدد الخصومات</strong>: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>ملاحظة افتتاح الجلسة:</strong> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "<strong>Source Invoice:</strong>"
msgstr "<strong>فاتورة المصدر:</strong> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Total</strong>"
msgstr "<strong>الإجمالي</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "؟ الضغط على \"تأكيد\" سيؤدي إلى تصديق الدفع. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "اسم العميل مطلوب "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"معرف فريد عالمياً لتهيئة نقطة البيع هذه، يُستخدَم لمنع التعارضات في البيانات"
" التي تم إنشاؤها بواسطة العميل. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr "رقم تسلسل يتصاعد في كل مرة يتابع فيها المستخدم جلسة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "رقم تسلسل يتصاعد مع كل طلب "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"الجلسة هي مدة زمنية، عادة ما تكون يومًا واحدًا، يمكنك خلالها البيع من خلال "
"نقطة البيع."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"هناك جلسة مفتوحة حالياً لنقطة البيع هذه. بعض الإعدادات يمكن تغييرها فقط بعد "
"إغلاق الجلسة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "رقم تسلسل فريد لكل جلسة للطلب "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "نص قصير سيتم استخدامه كتذييل في الإيصال المطبوع."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "نص قصير سيتم استخدامه كترويسة في الإيصال المطبوع. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:point_of_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"المنتج القابل للتخزين هو المنتج الذي يمكن إدارة مخزونه. يجب أن يكون تطبيق المخزون مثبتاً.\n"
"المنتج القابل للاستهلاء هو المنتج الذي لا يمكن إدارة مخزونه.\n"
"الخدمة هي منتج غير مادي تقوم بتقديمه. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"A valid product already exists for Point of Sale. Therefore, demonstration "
"products cannot be loaded."
msgstr ""
"هناك منتج صالح موجود بالفعل لنقطة البيع، وبالتالي، لا يمكن تحميل منتجات "
"العرض. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "ALL POS"
msgstr "كافة نقاط البيع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "المبلغ "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "قبول البقشيش من العملاء أو تحويل الباقي لبقشيش"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a PayTM payment terminal"
msgstr "قبول المدفوعات باستخدام جهاز دفع PayTM "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "قبول المدفوعات عن طريق جهاز دفع Six "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr "قبول المدفوعات عن طريق جهاز دفع Stripe "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "قبول المدفوعات عن طريق جهاز دفع Vantiv "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "قبول المدفوعات عن طريق جهاز دفع Adyen "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr "تحذير من خطأ بالوصول"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "الحساب "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "تقريب القيم المالية في الحساب "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "نموذج مخطط الحساب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "حركة الحساب"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr "مدفوعات الحساب التي تمثل إجمالي المدفوعات والدفع على أقساط. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "المحاسبة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "دفتر اليومية المحاسبي المستخدم لإصدار الفواتير."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"دفتر اليومية المحاسبي المستخدم لترحيل قيود دفتر يومية جلسة نقطة البيع "
"ومدفوعات فواتير نقطة البيع. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "نشط"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#, python-format
msgid "Add"
msgstr "إضافة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#, python-format
msgid "Add Customer Note"
msgstr "إضافة ملاحظة العميل"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Add Tip"
msgstr "إضافة بقشيش "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Add a 5-digit code on the receipt to allow the user to request the invoice "
"for an order on the portal."
msgstr ""
"أضف رمزاً مكوناً من 5 أرقام للسماح للمستخدم بطلب فاتورة الطلب على البوابة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid ""
"Add a QR code on the ticket, which the user can scan to request the invoice "
"linked to its order."
msgstr ""
"أضف رمز QR في التذكرة، والذي بوسع المستخدم مسحه لطلب الفاتورة المرتبطة "
"بطلبه. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Add a closing note..."
msgstr "إضافة ملاحظة للإقفال... "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr "إضافة رسالة مخصصة في الترويسة والتذييل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Add a customer"
msgstr "إضافة عميل"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "إضافة طريقة دفع جديدة "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr "إضافة طابعة طلبات جديدة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.xml:0
#, python-format
msgid "Add a note..."
msgstr "إضافة ملاحظة... "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Add an opening note..."
msgstr "إضافة ملاحظة للافتتاح... "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Add to order"
msgstr "إضافة إلى الطلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr "المعلومات الإضافية المطلوبة: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr "معلومات الفوترة الإضافية المطلوبة: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr "معلومات المستخدم الإضافية المطلوبة: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Address"
msgstr "العنوان"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr "يضيف زراً لإعداد الخصم الشامل "

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "المدير "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr "التحكم المتقدم في النقد "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "جهاز دفع Adyen "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "All active orders"
msgstr "كافة الطلبات النشطة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"يجب أن تكون عملة كافة قوائم الأسعار المتاحة هي نفس عملة الشركة أو العملة "
"المذكورة في دفتر يومية المبيعات المُعين لنقطة البيع هذه إذا كنت تستخدم تطبيق"
" المحاسبة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"يجب أن تكون عملة كافة طرق الدفع هي نفس العملة المذكورة في دفتر يومية "
"المبيعات أو عملة الشركة إن لم يكن قد تم ضبط عملة لدفتر اليومية. "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "كافة بنود المبيعات"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr "السماح بالشحن لاحقاً "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr "السماح لأمناء الصناديق بإعداد خصومات لكل بند "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr "السماح للطرفين بالوصول إلى الطلبات النشطة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr "السماح بالتسجيل والتبديل بين الموظفين المحددين "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr "مسموح به"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""
"يتيح لك معرفة ما إذا كانت التكلفة الإجمالية لبنود الطلب قد تم احتسابها "
"بالفعل أم لا "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr ""
"يتيح لك معرفة ما إذا كانت التكلفة الإجمالية قد تم احتسابها بالفعل أم لا "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
#, python-format
msgid "Amount"
msgstr "مبلغ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "فرق المبلغ المسموح به"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "المبلغ لتسويته "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "إجمالي المبلغ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"حدث خطأ أثناء محاولة إغلاق الجلسة. \n"
"سوف تتم إعادة توجيهك إلى الواجهة الخلفية لإغلاق الجلسة يدوياً. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"حدث خطأ أثناء تحميل أسعار المنتج. تأكد من أن كافة قوائم الأسعار متاحة في "
"نقطة البيع. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/main.js:0
#, python-format
msgid "An error occurred while loading the Point of Sale: \n"
msgstr "حدث خطأ أثناء تحميل نقطة البيع \n"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "تعريف داخلي لنقطة البيع."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr "معرف داخلي للطابعة"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "هناك جلسة أخرى مفتوحة لنقطة البيع هذه."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "مؤرشف"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "هل أنت متأكد أن العميل يريد الدفع"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "في أقرب وقت ممكن "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (faster)"
msgstr "عند إغلاق الجلسة (أسرع) "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"عند إغلاق الجلسة: يتم إنشاء عملية انتقاء للجلسة بأكملها عندما يتم إغلاقها \n"
"في الوقت الفعلي: يقوم كل طلب مُرسَل إلى الخادم بإنشاء عملية الانتقاء الخاصة به "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "الخصائص"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr "الفرق المسموح به "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr "التصديق التلقائي للمدفوعات عن طريق البطاقة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""
"جلسة تم إنشاؤها آلياً للأوامر التي لا تملك جلسة، وتم تجاهل كافة الضوابط فيها"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "طباعة الإيصالات تلقائيًا"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "فتح درج النقد تلقائياً. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr "تصديق الطلب تلقائياً "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr "يقوم تلقائياً بتصديق الطلبات المدفوعة عن طريق جهاز الدفع بالبطاقة. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr "متاح"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "فئات منتجات نقاط البيع المتاحة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr "قوائم الأسعار المتاحة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "متاح في نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "متوسط السعر"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr "العودة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Back"
msgstr "العودة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Backend"
msgstr "الواجهة الخلفية "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_background_image_1920
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_background_image_1920
msgid "Background Image"
msgstr "صورة الخلفية"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Badge ID"
msgstr "معرّف الشارة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Balance"
msgstr "الرصيد"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "البنك"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "المدفوعات البنكية"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "بند كشف الحساب البنكي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Barcode"
msgstr "باركود"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "تسمية الباركود"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "قاعدة الباركود"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "ماسح الباركود "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "ماسح الباركود/قاريء البطاقات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Base"
msgstr "قاعدة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "المبلغ الأساسي"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "فرق ما قبل الإغلاق "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr "عنوان الفوترة: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "الفواتير"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr "الفواتير والإيصالات "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"تمكن من تعزيز مبيعاتك باستخدام عدة أنواع من برامج الخصم: الكوبونات والعروض "
"وبطاقات الهدايا والولاء. يمكن تعيين شروط معينة (المنتجات، العملاء، الحد "
"الأدنى لمبلغ الشراء، المدة الزمنية). وقد تكون المكافآت خصومات (نسبة مئوية أو"
" مبالغ) أو منتجات مجانية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Buffer:"
msgstr "أفضل: "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "تجاوز الطباعة من المتصفح والطباعة من خلال جهاز وكيل."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "CANCELLED"
msgstr "ملغي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "CASH"
msgstr "النقد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "الباقي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Can't change customer"
msgstr "لا يمكن تغيير العميل"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "إلغاء"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "إلغاء طلب الدفع "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "تم الإلغاء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "لا يمكن تعديل النصيحة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "لا يمكن رد الباقي دون طريقة دفع نقدية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "اسم حامل البطاقة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#: model:pos.payment.method,name:point_of_sale.payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Cash"
msgstr "نقدي"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "النقد %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash In"
msgstr "الإيرادات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "الإيرادات/النفقات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "دفتر يومية الصندوق "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr "بنود النقد "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Cash Move 1"
msgstr "حركة النقد 1 "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Cash Opening"
msgstr "فتح النقد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash Out"
msgstr "النفقات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr "تقريب النقد "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr "تقريب النقد (نقطة البيع) "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "عمليات تقريب النقد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Cash control - closing"
msgstr "التحكم في النقد - الإقفال "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.js:0
#, python-format
msgid "Cash control - opening"
msgstr "التحكم في النقد - الفتح "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Loss)"
msgstr "الفروقات النقدية التي تمت ملاحظتها عند العد (الخسارة) "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Profit)"
msgstr "الفروقات النقدية التي تمت ملاحظتها عند العد (الربح) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
#, python-format
msgid "Cash in / out"
msgstr "الإيرادات / النفقات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "يتم تجاهل الإيرادات/النفقات لـ %s. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register"
msgstr "صندوق تسجيل النقد "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr "تقريب النقد "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "درج النقد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
#, python-format
msgid "Cashier"
msgstr "أمين الصندوق "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"تستخدم الفئات للتصفح عبر منتجاتك من خلال\n"
"                واجهة شاشات اللمس."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/category_selector/category_selector.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "الفئة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "اسم الفئة"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_ids
msgid "Category used in the Point of Sale."
msgstr "الفئة المستخدمة في نقطة البيع."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "الكراسي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Change"
msgstr "الباقي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Change Tip"
msgstr "تغيير البقشيش "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Change:"
msgstr "الباقي:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""
"قم بتحديد هذا الخيار إذا كان ينبغي وزن المنتج باستخدام الميزان الإلكتروني "
"المدمج."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""
"قم بتحديد هذا الخيار إذا كنت ترغب في أن يظهر هذا المنتج في نقطة البيع. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"قم بتحديد هذا الخيار إذا كنت ترغب في تجميع المنتجات التابعة لهذه الفئة في "
"أوامر نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "تحقق من المبلغ الموجود في الصندوق عند الفتح و الإغلاق. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"تحقق من اتصالك بالإنترنت ثم حاول المزامنة من جديد عن طريق الضغط على زر "
"الوايفاي الأحمر (أعلى يسار الشاشة). "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "الفئات التابعة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"اختر وضعاً مالياً محدداً في الطلب بناءً على نوع العميل (معفي من الضرائب، "
"الشراء في المحل مقابل الطلبية للتوصيل، وما إلى ذلك). "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "City"
msgstr "المدينة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr "اضغط هنا لإغلاق الجلسة "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "العميل"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Close"
msgstr "إغلاق"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "إغلاق الجلسة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "إغلاق الجلسة وترحيل القيود "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "معالج إغلاق الجلسة "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "تم الإغلاق والترحيل "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "التحكم في الإغلاق "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "تاريخ الإغلاق "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "ملاحظات الإغلاق "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing Session"
msgstr "إغلاق الجلسة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "فرق الإغلاق في %s (%s) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing note"
msgstr "ملاحظة الإغلاق "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Closing session error"
msgstr "خطأ في إغلاق الجلسة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "قيمة العملة/الورقة النقدية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#, python-format
msgid "Coins/Bills"
msgstr "العملات المعدنية/الأوراق النقدية "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__combo_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__combo_ids
msgid "Combinations"
msgstr "التركيبات "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "قم بجمع %s مدفوعات نقطة البيع من %s "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_id
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__detailed_type__combo
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__type__combo
msgid "Combo"
msgstr "مجموعة "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_combo
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Combo Choices"
msgstr "خيارات التركيبات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "بنود مركّبة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combo Name"
msgstr "اسم التركيبة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "أصل المجموعة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "Combo products cannot contains variants or attributes"
msgstr "لا يمكن أن تحتوي مجموعات المنتجات على متغيرات أو خصائص "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combos"
msgstr "التركيبات "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "الشركة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr "لدى الشركة شجرة حسابات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Complete the selection to proceed"
msgstr "قم بتكملة الخيارات للاستمرار "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "التهيئة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "التهيئة والإعدادات "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr "تهيئة نقطة بيع واحدة على الأقل. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "تأكيد"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#, python-format
msgid "Confirm?"
msgstr "تأكيد؟ "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr "قم بتوصيل الأجهزة بنقطة البيع دون الحاجة إلى جهاز IoT. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "قم بتوصيل الأجهزة بنقطة البيع دون الحاجة إلى جهاز IoT. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr "توصيل الأجهزة باستخدام جهاز IoT "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr "الأجهزة المتصلة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "متصل، غير مملوك"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "الاتصال بالوكيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Connection error"
msgstr "خطأ في الاتصال "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "تعذر الاتصال بجهاز IoT "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "تعذر الاتصال بالطابعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
#, python-format
msgid "Contact"
msgstr "جهة الاتصال"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Continue"
msgstr "استمرار "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "متابعة البيع"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "Continue with limited functionalities"
msgstr "الاستمرار مع وظائف محدودة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"Conversion Error: The following unit of measure conversions result in a zero"
" quantity due to rounding:"
msgstr ""
"خطأ في التحويل: تؤدي تحويلات وحدة القياس التالية إلى كمية مقدارها صفر بسبب "
"التقريب: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "معدل التحويل "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "معدل التحويل من عملة الشركة إلى عملة الطلب. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Cost:"
msgstr "التكلفة:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Counted"
msgstr "معدود "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Country"
msgstr "الدولة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__country_code
msgid "Country Code"
msgstr "رمز الدولة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Create"
msgstr "إنشاء"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "إنشاء طلب نقطة بيع جديد"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr "إنشاء نقطة بيع جديدة"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "إنشاء متغير جديد لمنتج "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "العملة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "سعر صرف العملة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "الجلسة الحالية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "المسؤول الحالي عن الجلسة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "حالة الجلسة الحالية"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "مُخصص"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr "الترويسة والتذييل المخصصة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__custom_attribute_value_ids
msgid "Custom Values"
msgstr "القيم المخصصة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "العميل"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "حساب العميل"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Customer Display"
msgstr "عرض العميل"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "شاشة العرض المواجهة للعميل "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "فاتورة العميل"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "ملاحظة العميل"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr "رابط بوابة العميل"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Customer Required"
msgstr "العميل مطلوب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen"
msgstr "شاشة العميل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen Connected"
msgstr "شاشة العميل متصلة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Customer Screen Unsupported. Please upgrade the IoT Box"
msgstr "شاشة العميل غير مدعومة. يرجى ترقية جهاز IoT "

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "العميل مطلوب لطريقة الدفع %s.  "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "لا يمكن تعديل نصائح العملاء مباشرة "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "العملاء"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_PRODUCT_NAME"
msgstr "DEMO_PRODUCT_NAME"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_REF"
msgstr "DEMO_REF"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "DETAILS"
msgstr "التفاصيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Daily Sale"
msgstr "المبيعات اليومية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Daily Sales Report"
msgstr "تقرير المبيعات اليومية "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr "تحتوي الجلسات اليومية على مبيعات من نقطة البيع الخاصة بك. "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "لوحة البيانات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#, python-format
msgid "DatePicker"
msgstr "محدِّد التواريخ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Days"
msgstr "الأيام"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Debug Window"
msgstr "نافذة إصلاح الأخطاء "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr "افتراضي "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "حساب المدينين الافتراضي (نقطة البيع) "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "الوضع المالي الافتراضي "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr "دفتر اليوميات الافتراضي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Default Price"
msgstr "السعر الافتراضي "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr "قائمة الأسعار الافتراضية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "ضريبة البيع الافتراضية"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "ضريبة المبيعات الافتراضية"

#. module: point_of_sale
#: model:account.tax,name:point_of_sale.pos_taxes_0
msgid "Default Tax for PoS"
msgstr "الضريبة الافتراضية لنقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr "الحساب الافتراضي المؤقت "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr "دفاتر اليومية الافتراضية للطلبات والفواتير "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "ضريبة المبيعات الافتراضية على المنتجات"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "وحدة القياس الافتراضية المستخدمة لكافة عمليات المخزون. "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "تعريف فئة جديدة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "قم بتحديد أصغر وحدة من العملة المستخدمة للدفع نقداً "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"يقوم بتحديد اسم طريقة الدفع التي سيتم عرضها في نقطة البيع عندما يتم تحديد "
"المدفوعات. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "تأخير التصديق "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "حذف الطلبات المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Paid Orders?"
msgstr "حذف الطلبات المدفوعة؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "حذف الطلبات غير المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Unpaid Orders?"
msgstr "حذف الطلبات غير المدفوعة؟ "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo 3-03-2000 5:00 PM"
msgstr "عرض توضيحي 3-03-2000 5:00 مساءً "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo Name"
msgstr "الاسم التجريبي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "Demo products are no longer available"
msgstr "لم تعد المنتجات التجريبية متاحة بعد الآن "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "منظم المكتب"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "لوحة المكتب "

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "مكاتب"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "حساب الوجهة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "حساب الوجهة للقراءة فقط "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Difference"
msgstr "الفرق"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "الفرق عند إغلاق جلسة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "الفرق بين رصيد الإقفال المتوقع والرصيد الفعلي."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "الموجز "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "خصم %"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "الخصم:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Disconnected"
msgstr "غير متصل"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
#, python-format
msgid "Discount"
msgstr "الخصم"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "خصم (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "إشعار الخصم "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Discount:"
msgstr "الخصم:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "منتج مخفض"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Discounts"
msgstr "الخصومات"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Discounts:"
msgstr "الخصومات: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Dismiss"
msgstr "صرف "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr "عرض الطلبات في شاشة تحضير الطعام "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد الملخص للمستخدم. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"Do you want to accept payments difference and post a profit/loss journal "
"entry?"
msgstr "هل ترغب في قبول فرق الدفع وترحيل قيود دفتر يومية الأرباح/الخسائر؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "هل ترغب في فتح قائمة العملاء لاختيار العميل؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Do you want to print using the web printer? "
msgstr "هل ترغب بالطباعة باستخدام طابعة الويب؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "تنزيل الأوامر المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "تنزيل الأوامر غير المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Download a report with all the sales of the current PoS Session"
msgstr "قم بتنزيل تقرير يحتوي على كافة مبيعات جلسة نقطة البيع الحالية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "تنزيل تقرير تتبع الكود للخطأ "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""
"تحتوي كل طابعة طلبات على عنوان IP يعرف وكيل صندوق/جهاز IoT\n"
"            حيث يمكن العثور علي الطابعة، وقائمة بفئات المنتج.\n"
"            ستقوم طابعة الطلبات بطباعة تحديثات المنتجات التي تنتمي لإحدى\n"
"            فئاتها فقط. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr "تحرير"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Electronic Scale"
msgstr "الميزان الإلكتروني"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Email sent."
msgstr "تم إرسال رسالة البريد الإلكتروني."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"بوسع الموظفين مسح شاراتهم أو إدخال رمز PIN لتسجيل الدخول إلى جلسة نقطة "
"البيع. بيانات الاعتماد هذه مهيئة في علامة تبويب *إعدادات الموارد البشرية* "
"لاستمارة الموظفين. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Empty Order"
msgstr "طلب فارغ "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"تمكين مسح الباركود مع ماسحة باركود متصلة عن بُعد وتمرير البطاقات في قارئة "
"بطاقات Vantiv. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "يقوم بتمكين دمج الميزان الإلكتروني. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "حدث خطأ أثناء تحميل الصورة. يرجى المحاولة مجدداً. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "الرصيد الختامي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error"
msgstr "خطأ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "خطأ مع تقرير تتبع الكود "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "خطأ! لا يمكنك إنشاء فئات متداخلة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "خطأ: لا يوجد اتصال بالإنترنت. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "بنود الطلب الموجودة بالفعل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Exit Pos"
msgstr "الخروج من نقطة البيع"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Expected"
msgstr "المتوقع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Expected delivery:"
msgstr "التوصيل المتوقع: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "تصدير الطلبات المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "تصدير الطلبات غير المدفوعة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "معلومات إضافية"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "القماش "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "عمليات الانتقاء الفاشلة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "تعذر طباعة التغييرات في الطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Financials"
msgstr "الشؤون المالية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "أوامر الاستيراد المنتهية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "الوضع المالي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "لم يتم العثور على الوضع المالي "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "الأوضاع المالية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr "قوائم البيانات المرنة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr "الضرائب المرنة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr "التذييل"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "لشاشات اللمس غير الدقيقة."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "فرض إغلاق الجلسة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force Done"
msgstr "فرض الانتهاء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force done"
msgstr "فرض الانتهاء "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "الحساب المستحق المفروض "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"يقوم بفرض تعيين عميل عند استخدام طريقة الدفع هذه وتقسيم قيود دفتر ليومية لكل"
" عميل. قد يبطئ ذلك من عملية الإقفال. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Free"
msgstr "مجاني"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "من مدفوعات الفاتورة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "اسم المنتج الكامل "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr "إنشاء كود في تذكرة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr "إنشاء المراجع لطلباتك "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr "الحصول على فاتورتي "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "يعرض ترتيب التسلسل عند عرض قائمة من فئات المنتجات."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr "خصومات شاملة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "Go Back"
msgstr "عد"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "الذهاب إلى "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "أكبر من المسموح به "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "تجميع المنتجات في نقطة البيع"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "فشل اتصال HTTPS بجهاز IoT "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "فعاليات المعدات"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "حالة المعدات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr "يحتوي على جلسة نشطة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "يحتوي على التحكم بالنقد "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr "يحتوي على صورة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "يحتوي على بنود يمكن استرداد أموالها "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr "الترويسة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Category Images"
msgstr "إخفاء صور الفئة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Product Images"
msgstr "إخفاء صور المنتج "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "إخفاء استخدام  جهاز الدفع بالبطاقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "المُعرف"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "مهم: تقرير خطأ من نقطة البيع بأودو"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr "عنوان IP"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "تعريف العميل "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"إذا كان بند الطلب هذا عملية استرداد أموال، يتم تحديد بند الطلب الذي سيتم "
"استرداده في هذا الحقل. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"إذا قمت بتوصيل كافة المنتجات دفعةً واحدة، ستتم جدولة عملية التوصيل بناءً على"
" مهلة التوصيل الأعلى للمنتجات. وإلا ستكون مبنية على الأقصر. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "عرض Iface المواجه للعملاء "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "صورة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Import Orders"
msgstr "استيراد الطلبات "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "تحسين أداء شاشات اللمس غير الدقيقة"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "ليتم حذف عملية بيع، يجب أن تكون جديدة أو ملغية."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time (accurate but slower)"
msgstr "في الوقت الفعلي (دقيق ولكن أبطأ) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "عنوان الشحن غير صحيح "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "التقريب غير صحيح "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "رسالة المعلومات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_start_categ_id
msgid "Initial Category"
msgstr "الفئة المبدئية"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"سوف تقوم عملية تثبيت مخطط الحسابات من الإعدادات العامة\n"
"                لتطبيقي المحاسبة/الفوترة بإنشاء طرق دفع عن طريق البنك \n"
"                والدفع نقداً تلقائياً. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "مدفوعات البطاقة المدمجة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "حساب وسيط "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr "حساب وسيط يُستخدم للعملاء غير المحددين. "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "الفئات الداخلية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Invalid action"
msgstr "إجراء غير صالح "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Invalid email."
msgstr "عنوان البريد الإلكتروني غير صالح "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "المخزون "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "إدارة المخزون"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "الفاتورة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr "دفتر يومية الفاتورة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Invoice Name"
msgstr "اسم الفاتورة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr "طلب فاتورة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "دفعة فاتورة لـ %s (%s) باستخدام %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Invoiced"
msgstr "مفوتر"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr "فواتير العملاء "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr "تاكيد الفوترة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr "جهاز IoT "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr "عنوان IP لجهاز IoT "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "مفوتر "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_kiosk_mode
msgid "Is Kiosk Mode"
msgstr "وضع الكشك "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "تم استرداد الأموال "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "تم احتساب إجمالي التكلفة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "يستخدم عملة الشركة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "حانة أو مطعم"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "نظام المحاسبة الكامل مثبت "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "هل تم منح بقشيش بالفعل؟ "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "هل تغير هذا الدفع؟ "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"لا يسمح بتعديل ضريبة مستخدمة في أمر نقطة بيع غير مرحل. يلزم إغلاق جلسات نقطة"
" البيع قبل تعديل الضريبة."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "It is not allowed to mix refunds and sales"
msgstr "لا يُسمح بالجمع بين عمليات استرداد الأموال والبيعات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "It is possible to print your tickets by making use of an IoT Box."
msgstr "هل يمكن طباعة تذاكرك عن طريق الاستفادة من جهاز IoT؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "اختبارات JavaScript"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "دفتر اليومية"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "القيمة الإجمالية للمؤشر الرئيسي لنقطة البيع"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "مصباح LED "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "بطاقة عنوان"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Language"
msgstr "اللغة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Laptop model x"
msgstr "لابتوب موديل X "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "أشرطة تمرير كبيرة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "المبلغ النقدي عند إغلاق الجلسة السابقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "تاريخ إغلاق الجلسة السابقة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr "أخر تغيير في التجهيز "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr "آخر حالة مطبوعة للطلب "

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "جلد"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Leave a reason here"
msgstr "اكتب سبباً هنا "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr "اتركه فارغاً حتى تستخدم الحساب الافتراضي من إعدادات الشركة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"اتركه فارغاً لاستخدام الحساب الافتراضي من إعدادات الشركة. \n"
"الحساب مستخدَم كحساب مستحقات عند إنشاء سجلات دفع المحاسبة لمدفوعات البنك. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"اتركه فارغاً لاستخدام الحساب الافتراضي من إعدادات الشركة. \n"
"يتخطى حساب المدينين الخاص بالشركة (لنقطة البيع) يُستخدَم في قيود اليومية. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "اتركه فارغاً لاستخدام حساب المدين الخاص بالعميل "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"اتركه فارغاً لاستخدام حساب المدين الخاص بالعميل. \n"
"يحدد دفتر اليومية الذي يتم تدوين المدفوعات المتراكمة فيه (أو مدفوعات الأفراد إذا تم تحديد العميل) بعد إغلاق الجلسة. \n"
"لدفتر يومية الصندوق، نقوم بكتابة الحساب الافتراضي في دفتر اليومية عن طريق بنود كشف الحساب. \n"
"لدفتر يومية البنك، نقوم بالكتابة للحساب المستحَق المحدد في طريقة الدفع هذه. \n"
"يُسمح فقط بدفاتر يومية الصندوق ودفاتر يومية البنك فقط. "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "درج أوراق"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr "خصومات البنود "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "رقم البند "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Load Order"
msgstr "تحميل الطلب "

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr "تحميل قائمة المنتج "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Loading Image Error"
msgstr "خطأ في تحميل الصورة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Loading..."
msgstr "جار التحميل..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "عرض مواجه للعملاء المحليين "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "تسجيل الدخول باستخدام رقم التسلسل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Logo"
msgstr "الشعار"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "اسم الدفعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Lot Number"
msgstr "رقم الدفعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "يتطلب الرقم التسلسلي/رقم الدفعة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "رقم الدفعة/الرقم التسلسلي "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "لوحة مغناطيسية"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "القيام بالدفع "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"قم بإتاحة عدة قوائم أسعار في نقطة البيع. كما يمكنك تطبيق قائمة أسعار معينة "
"لعملاء معينين من نموذج جهة الاتصال الخاص بهم (في تبويب المبيعات). لتصبح "
"صالحة للاستخدام، يجب إدراج قائمة الأسعار هنا كقائمة متاحة. وإلا سيتم تطبيق "
"القائمة الافتراضية."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"تأكد من استخدامك لجهاز IoT بإصدار v18.12 أو أعلى. قم بالتنقل إلى %s لقبول "
"شهادة جهاز IoT الخاص بك. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr "أدر العروض التي ستمنح العملاء خصومات أو هدايا "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Marc Demo"
msgstr "مارك ديمو "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "الهامش"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "الهامش (%) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Margin:"
msgstr "الهامش: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Margins & Costs"
msgstr "الهوامش والتكاليف "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "تم تجاوز الحد الأقصى "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "تم الوصول إلى القيمة القصوى "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid ""
"Meanwhile connection is back, Odoo Point of Sale will operate limited "
"operations. Check your connection or continue with limited functionalities"
msgstr ""
"ريثما يعود الاتصال بالإنترنت، ستعمل نقطة بيع أودو مع عمليات محدودة. تحقق من "
"اتصالك بالإنترنت أو استمر بالعمل مع وظائف محدودة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "الطريقة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr "اسم الطريقة "

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Misc"
msgstr "متنوعات"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr "الموارد البشرية لتطبيق نقطة البيع "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "حامل الشاشة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "More info"
msgstr "المزيد من المعلومات "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr "المزيد من الإعدادات: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#, python-format
msgid "More..."
msgstr "المزيد..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Multi Employees per Session"
msgstr "عدة موظفين لكل جلسة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Multiple Invoiced Orders Selected"
msgstr "تم تحديد عدة طلبات مفوترة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "جلساتي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "جديد"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "ملاحظة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
#, python-format
msgid "Name"
msgstr "الاسم"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "يتطلب عميلاً لإجراء الفوترة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"بحاجة إلى حساب الخسائر لدفاتر اليومية التالية لترحيل المبلغ الذي قد تمت "
"خسارته: %s \n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"بحاجة إلى حساب الأرباح لدفاتر اليومية التالية لترحيل المبالغ المكتسبة: %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Network Error"
msgstr "خطأ في الشبكة"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "جديد"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "New Order"
msgstr "طلب جديد"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "جلسة جديدة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.js:0
#, python-format
msgid "New amount"
msgstr "المبلغ الجديد "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "رف الصحف"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Next Order List"
msgstr "قائمة الطلبات التالية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "No"
msgstr "لا"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr "لا توجد نقطة بيع محددة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "No Taxes"
msgstr "دون ضرائب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid ""
"No barcode nomenclature has been configured. This can be changed in the "
"configuration settings."
msgstr "لم تتم تهيئة أي تسميات للباركود. يمكن تغيير ذلك في إعدادات التهيئة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr "لم يتم العثور على كشف مالي لهذه الجلسة. تعذّر تسجيل النقد المرتجع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"No chart of account configured, go to the \"configuration / settings\" menu,"
" and install one from the Invoicing tab."
msgstr ""
"لم تتم تهيئة شجرة حسابات، اذهب إلى قائمة \"التهيئة / الإعدادات\"، ثم قم "
"بتثبيت واحدة من علامة تبويب الفوترة. "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "لا يوجد رابط لفاتورة لـ%s."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "No more customer found for \"%s\"."
msgstr "لم يتم العثور على المزيد من العملاء لـ \"%s\". "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "No more product found for \"%s\"."
msgstr "لم يتم العثور على المزيد من المنتجات لـ \"%s\". "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__num_of_products
msgid "No of Products"
msgstr "عدد المنتجات "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr "لم يتم العثور على أي طلبات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products available. Explore"
msgstr "لا توجد منتجات متاحة. استكشف "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products found for"
msgstr "لم يتم العثور على منتجات لـ"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "No sale order found."
msgstr "لم يتم العثور على أمر بيع. "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr "لم يتم العثور على جلسات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "تسمية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "None"
msgstr "لا شيء"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Not Categorized"
msgstr "غير مصنّف "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "غير مفوترة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "Note"
msgstr "الملاحظات"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "الملاحظات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "رقم الطبعة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "عدد أوامر استرداد الأموال "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr "عدد جلسات الاسترجاع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "عدد العناصر التي قد تم استرداد ثمنها في بند الطلب هذا. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Number of transactions:"
msgstr "عدد المعاملات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "OK"
msgstr "موافق"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.office_combo_product_template
msgid "Office combo"
msgstr "مجموعة المكتب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Offline Orders"
msgstr "أوامر مقدمة دون الاتصال بالإنترنت "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#, python-format
msgid "Ok"
msgstr "موافق"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Ongoing"
msgstr "جاري"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"يسمح فقط بكمية سالبة لبند استرداد الأموال هذا. اضغط على علامة +/- لتعديل "
"الكمية التي ترغب باسترداد ثمنها. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Only administrators can edit receipt headers and footers"
msgstr "وحدهم المدراء بوسعهم تحرير ترويسات وتذييلات الإيصالات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "تطبيق التقريب عند الدفع نقداً فقط "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr "يمكن فقط استخدام دفاتر اليومية من نوع \"نقد\" أو \"بنك\" مع طرق الدفع. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr "فقط في طرق الدفع النقدية "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"لا يسمح بتعديل أسعار المنتج على الطلبات سوى للمستخدمين ذوي صلاحيات وصول "
"المدراء. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr "ندعم فقط صيغ الصورة المتوافقة مع الويب مثل .png و .jpeg "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "فتح صندوق النقد "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "قم بفتح جلسات نقطة البيع التي تستخدم طريقة الدفع هذه. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "جلسة مفتوحة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open session"
msgstr "فتح الجلسة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "فتح النافذة المنبثقة لتفاصيل الأموال "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "تم الفتح بواسطة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "تم الفتح بواسطة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Opening"
msgstr "الافتتاح "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening Cash Control"
msgstr "التحكم في المبلغ الافتتاحي "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "التحكم في الفتح "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "تاريخ الفتح"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "ملاحظات الفتح"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr "الرصيد الافتتاحي مجمعاً لكافة المعاملات النقدية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening cash"
msgstr "النقد الافتتاحي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening note"
msgstr "ملاحظة الفتح "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr "نوع العملية"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "تظهر أنواع العمليات في لوحة معلومات المخزون."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "الطلب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Order %s"
msgstr "طلب %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "لم يتم دفع مبلغ الطلب %s كلياً. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "عدد الطلبات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "تاريخ الطلب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr "تسلسل معرفات الطلب  "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "تسلسل معرفات بند الطلب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "بنود الطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tracking_number
#, python-format
msgid "Order Number"
msgstr "رقم الطلب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "طابعة الطلبات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr "طابعات الطلبات"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"تستخدم طابعات الطلبات في المطاعم والحانات لطباعة\n"
"            تحديثات الطلبات في المطبخ/الحانة عندما يقوم النادل بتحديث الطلب. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order Ref"
msgstr "مرجع الطلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "مرجع الطلب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "رقم تسلسل الطلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "بنود الطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Order number"
msgstr "رقم الطلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order reference"
msgstr "مرجع الطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "Order saved for later"
msgstr "طلب محفوظ لوقت لاحق "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""
"بنود الطلب في هذا الحقل هي البنود التي قامت باسترداد قيمة بند الطلب هذا. "

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "الطلبات "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "تحليل الطلبات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__lst_price
msgid "Original Price"
msgstr "السعر الأصلي "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr "الأجهزة الأخرى "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "المعلومات الأخرى "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Others"
msgstr "غير ذلك"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "حساب مستحق "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "طلب نقطة البيع"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "طلب نقطة البيع %s "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "بند طلب نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "بنود طلب نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "طلبات نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "بنود طلبات نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "طريقة الدفع في نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr "طابعة نقطة البيع"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "فئة منتج نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "مبيعات نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "جلسة نقطة البيع"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "بند طلب نقطة البيع %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "مدفوع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "الفئة الرئيسية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Partner"
msgstr "الشريك"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Pay"
msgstr "الدفع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "دفع قيمة الطلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PayTM"
msgstr "PayTM"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_paytm
msgid "PayTM Payment Terminal"
msgstr "جهاز دفع PayTM "

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "الدفع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "تاريخ الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "طريقة الدفع "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Payment Name Demo"
msgstr "اسم الدفع التجريبي "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "معلومات إيصال الدفع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "الرقم المرجعي للدفع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "حالة الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Payment Successful"
msgstr "تم الدفع بنجاح "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "أجهزة الدفع بالبطاقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "معرف معاملة الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Payment method"
msgstr "طريقة الدفع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr "طرق الدفع المتوفرة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment request pending"
msgstr "طلب الدفع قيد الانتظار "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment reversed"
msgstr "عكس الدفع "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "الدفعات"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Payments Difference"
msgstr "فرق الدفع "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "طرق الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Payments in"
msgstr "المدفوعات في "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Payments:"
msgstr "المدفوعات:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"الشخص الذي يستخدم صندوق تسجيل النقد. قد يكون البائع أو طالب أو موظف متدرب."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Phone"
msgstr "رقم الهاتف"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product categories are available"
msgstr "اختر أي فئات المنتجات تكون متاحة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "الانتقاء "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "عدد الانتقاءات "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "الانتقاء في نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr "نوع الانتقاء "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "عمليات الانتقاء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Picture"
msgstr "صورة"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "بلاستيك "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "يرجى تأكيد الكمية الكبيرة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_combo.py:0
#, python-format
msgid "Please add products in combo."
msgstr "يرجى إضافة المنتجات معاً. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "يرجى التحقق ما إذا كان جهاز IoT لا يزال متصلاً. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue. "
msgstr ""
"يرجى التحقق مما إذا كانت الطابعة لا تزال متصلة. \n"
"لا تسمح بعض المتصفحات باستدعاءات HTTP من المواقع الإلكترونية وإلى الأجهزة في الشبكة (لأسباب أمنية). إذا كان الأمر كذلك، ستحتاج إلى متابعة وثائق أودو عن 'الشهادة الموقعة ذاتياً لطابعات نقاط البيع الإلكترونية' و 'والاتصال الآمن (HTTPS)' لحل المشكلة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"يرجى إغلاق كافة جلسات نقطة البيع في هذه المدة قبل إغلاقها. الجلسات المفتوحة "
"هي: %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""
"يرجى إغلاق وتصديق جلسات نقاط البيع المفتوحة التالية قبل تعديل طريقة الدفع هذه. \n"
"الجلسات المفتوحة: %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "يرجى تهيئة طريقة دفع في نقطة البيع الخاصة بك. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Please create/select a Point of Sale above to show the configuration "
"options."
msgstr "يرجى إنشاء/تحديد نقطة بيع أعلاه لإظهار خيارات التهيئة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "يرجى تحديد حساب الدخل لهذا المنتج:\"%s\" (المعرف:%d). "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please define income account for this product: '%s' (id:%d)."
msgstr "يرجى تحديد حساب الدخل لهذا المنتج: '%s' (المعرف:%d). "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr ""
"يرجى إدخال معلومات الفوترة الخاصة بك <small class=\"text-muted\">أو</small> "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "Please fill all the required fields."
msgstr "يرجى ملء الحقول المطلوبة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Loss Account. This account will be "
"used to record cash difference."
msgstr ""
"الرجاء الذهاب إلى يومية %s وتحديد حساب خسارة. سوف يُستَخدَم هذا الحساب "
"لتسجيل الفرق في النقد. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Profit Account. This account will "
"be used to record cash difference."
msgstr ""
"الرجاء الذهاب إلى يومية %s وتحديد حساب ربح. سوف يُستَخدَم هذا الحساب لتسجيل "
"الفرق في النقد. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "يرجى طباعة الفاتورة من الواجهة الخلفية "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "يرجى اختيار شريك لعملية البيع. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "يرجى اختيار طريقة دفع. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "يرجى اختيار العميل "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr "واجهة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_attribute_custom_value__pos_order_line_id
msgid "PoS Order Line"
msgstr "بند طلب نقطة البيع "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "طلبات نقطة البيع "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr "فئات منتجات نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "فئة منتج نقطة البيع "

#. module: point_of_sale
#: model:account.tax.group,name:point_of_sale.pos_taxe_group_0
msgid "PoS Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "PoS order %s can not be processed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "نقطة البيع"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "نقطة البيع"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "تحليل نقطة البيع"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_ids
msgid "Point of Sale Category"
msgstr "فئة نقطة البيع"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "تهيئة نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "تهيئة نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "التقرير اليومي لنقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "تفاصيل نقطة البيع"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "تقرير تفاصيل نقطة البيع"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "تقرير فاتورة نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr "دفتر يومية نقطة البيع "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr "قائمة نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "معالج إكمال الدفع في نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "مجموعة مدراء نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "نوع عمليات نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "بنود طلب نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "طلبات نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "تقرير طلبات نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "طرق الدفع في نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "مدفوعات نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr "طابعة نقطة البيع "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "جلسة نقطة البيع"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "اختبارات نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "مجموعة مستخدمي نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr "نقاط البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr "رابط الوصول لبوابة العملاء"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr "قوائم الأسعار المسموح بها في نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "تهيئة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr "طابعة الطلبات في نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "طلب نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "عدد طلبات نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "بند طلب نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "الدفع في نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "فئات منتجات نقاط البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_refunded_invoice_ids
msgid "Pos Refunded Invoice"
msgstr "فاتورة نقطة البيع التي تم رد مبلغها "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr "فئة قابلة للتحديد في نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "جلسة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "مدة جلسة نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "حالة جلسة نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "اسم مستخدم جلسة نقطة البيع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "جلسات نقطة البيع "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr "جلسة نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr "صندوق نقطة البيع"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "لا يسمح بكمية موجبة "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "مُرحّل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Powered by Odoo"
msgstr "مُشغَّل بواسطة أودو "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr "شاشة تحضير الطعام "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model:ir.ui.menu,name:point_of_sale.menu_pos_preparation_printer
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr "طابعات التجهيز "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "قائمة الطلبات السابقة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Price"
msgstr "السعر"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr "مراقبة الأسعار "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_price
msgid "Price Extra"
msgstr "السعر الإضافي "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "تخفيض السعر من %s -> %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Price excl. Tax:"
msgstr "السعر غير شامل الضريبة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr "سعر إضافي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#, python-format
msgid "Price list"
msgstr "قائمة السعر"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "منتج مسعر"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "قائمه الأسعار"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "قوائم الأسعار"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "الأسعار "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "طباعة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/reprint_receipt_button/reprint_receipt_button.xml:0
#, python-format
msgid "Print Receipt"
msgstr "طباعة الإيصال"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Print a QR code on the receipt to allow the user to easily request the "
"invoice for an order."
msgstr ""
"قم بطباعة رمز QR على الإيصال للسماح للمستخدم بطلب فاتورة طلبه بكل سهولة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "طباعة تقرير يحتوي على كافة مبيعات جلسة نقطة البيع الحالية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "طباعة الطلبات في المطبخ، في الحانة، إلخ."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "طباعة الإيصالات تلقائياً بمجرد أن يتم تسجيل الدفعة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "الطباعة عن طريق الوكيل "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "فئات المنتجات المطبوعة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Printer"
msgstr "الطابعة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr "اسم الطابعة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "نوع الطابعة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr "الطابعات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing error"
msgstr "خطأ في الطباعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Printing failed"
msgstr "فشلت الطباعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "الطباعة غير مدعومة في بعض المتصفحات "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "مجموعة الشراء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Product"
msgstr "المنتج"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "القيمة المخصصة لخاصية المنتج "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "فئة المنتج"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo_line
msgid "Product Combo Items"
msgstr "عناصر كومبو المنتجات "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_combo
msgid "Product Combos"
msgstr "كومبو المنتجات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "معلومات المنتج"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__base_price
msgid "Product Price"
msgstr "سعر المنتج "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "أسعار المنتج"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "فئات المنتج"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "كمية المنتج"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "قالب المنتج"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "نوع المنتج"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "وحدة قياس المنتج"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "وحدة قياس المنتج"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "فئات وحدات قياس المنتج"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "متغيرات المنتج "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo
msgid "Product combo choices"
msgstr "خيارات كومبو المنتجات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Product information"
msgstr "معلومات المنتج "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"لم يتم تحميل المنتج. حاولنا تحميل المنتج من الخادم ولكن يوجد خطأ في الاتصال "
"بالشبكة. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr "أسعار المنتج على الإيصالات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr "ملاحظات المنتج"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "المنتجات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__combo_line_ids
msgid "Products in Combo"
msgstr "كومبو المنتجات "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr "المنتجات: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "العروض، الكوبونات، بطاقات الهدايا، وبرنامج الولاء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "تم الاتصال بالوكيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "تم قطع الاتصال بالوكيل "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "عنوان IP الوكيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "تحذير الوكيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Qty"
msgstr "الكمية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "الكمية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "REASON"
msgstr "السبب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "REFUNDED:"
msgstr "تم إرجاع الأموال: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "قراءة القيمة الموزونة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "هل أنت جاهز لإطلاق <b>نقطة البيع</b> الخاصة بك؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Reason"
msgstr "السبب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Receipt"
msgstr "الإيصال "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "الإيصال %s "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr "تذييل الإيصال"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr "ترويسة الإيصال "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "رقم الإيصال "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr "طابعة الإيصالات "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "تسجيل المدفوعات باستخدام جهاز الدفع بالبطاقة في دفتر اليومية هذا. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "جلسة الاستعادة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Ref 876787"
msgstr "Ref 876787"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "تحديث شاشة العرض"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Refund"
msgstr "استرداد الأموال "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "بنود الطلب المستردّة قيمته "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "استرداد أموال الطلبات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Refund and Sales not allowed"
msgstr "لا يُسمح برد الأموال والمبيعات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunded"
msgstr "تم استرداد الأموال "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "الطلب المستردّة قيمته "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "بند الطلب المستردّة قيمته "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "الطلبات المستردّة قيمتها "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "عدد الطلبات المستردّة قيمتها "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "الكمية المستردّة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunding"
msgstr "استرداد الأموال "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "الاستردادات "

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr "إعادة تحميل قائمة نقطة البيع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Remaining"
msgstr "المتبقي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "بقية الطلبات غير المتزامنة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Remove"
msgstr "إزالة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Replenishment"
msgstr "تجديد المخزون "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "إعادة طباعة الفاتورة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request invoice"
msgstr "طلب الفاتورة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Request sent"
msgstr "تم إرسال الطلب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Reset"
msgstr "إعادة الضبط "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restaurant Mode"
msgstr "وضع المطعم "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr "تقييد الفئات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "قصر صلاحيات تعديل السعر على المدراء "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr "قصر صلاحيات تعديل السعر على المدراء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Resume Order"
msgstr "الاستمرار بالطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Retry"
msgstr "إعادة المحاولة"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "إرجاع المنتجات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "تم الإرجاع "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of POS closing entry %s for order %s from session %s"
msgstr "عكس قيد إقفال نقطة البيع %s للطلب %s من الجلسة %s "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "عكس: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "تم إرسال طلب العكس إلى جهاز الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "عكس"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "الدفع العكسي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Review"
msgstr "مراجعة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Rounding"
msgstr "التقريب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "طريقة التقريب"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "خطأ في التقريب في بنود الدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "إجراء اختبارات JS في نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "SN"
msgstr "الرقم المتسلسل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "SOLD:"
msgstr "تم بيعها: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "عدد بنود البيع"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "بند البيع"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Sales"
msgstr "المبيعات"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "تفاصيل المبيعات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "دفتر يومية المبيعات"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Closing Note"
msgstr "مثال لملاحظة إغلاق "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Config Name"
msgstr "مثال لاسم التهيئة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Opening Note"
msgstr "مثال لملاحظة افتتاح "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Save"
msgstr "حفظ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "احفظ هذه الصفحة ثم عد لضبط الخاصية."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scale"
msgstr "الميزان"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan"
msgstr "مسح"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "مسح EAN-13"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Scan me to request an invoice for your purchase."
msgstr "امسحني لطلب فاتورة لعملية الشراء التي قمت بها. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "المسح عن طريق الوكيل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scanner"
msgstr "الماسح الضوئي "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Search Customers..."
msgstr "البحث عن العملاء... "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Search Orders..."
msgstr "البحث في الطلبات... "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "البحث في أمر البيع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Search more"
msgstr "البحث عن المزيد "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#, python-format
msgid "Select"
msgstr "تحديد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "اختيار الوضع المالي "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr "قم بتحديد نقطة البيع لبدء مشاركة الطلبات "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select a payment method to validate the order."
msgstr "قم بتحديد طريقة الدفع لتصديق الطلب. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Select the pricelist"
msgstr "اختيار قائمة الأسعار "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "قم بتحديد المنتج (المنتجات) لاسترداد قيمتها وتحديد الكمية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select the shipping date"
msgstr "قم بتحديد تاريخ الشحن "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__attribute_value_ids
msgid "Selected Attributes"
msgstr "تحديد الخصائص "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr "قم ببيع المنتجات ثم إيصالها لاحقاً. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send"
msgstr "إرسال"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "إرسال طلب دفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Send by email"
msgstr "الإرسال عبر البريد الإلكتروني "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "تعذر إرسال البريد الإلكتروني. يرجى المحاولة من جديد. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending in progress."
msgstr "جاري الإرسال. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "رقم التسلسل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "الرقم التسلسلي/رقم الدفعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Served by"
msgstr "خدم بواسطة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Server Error"
msgstr "خطأ في الخادم "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "الجلسة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session Control"
msgstr "التحكم في الجلسة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "معرف الجلسة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session ID:"
msgstr "معرّف الجلسة: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "قيد اليومية للجلسة "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr "تقرير الجلسة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Session ids:"
msgstr "مُعرفات الجلسة:"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "الجلسة مفتوحة منذ أكثر من 7 أيام "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "الجلسات"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr "تحديد الفرق الأقصى "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Set Weight"
msgstr "تعيين الوزن "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"قم بتحديد الفرق الأقصى المسموح به بين المبلغ المتوقع والمبلغ الفعلي عند "
"إغلاق الجلسة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"قم بتحديد الفرق الأقصى المسموح به بين المبلغ المتوقع والمبلغ الفعلي عند "
"إغلاق الجلسة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "تعيين الوضع المالي "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""
"تعيين أسعار متعددة لكل منتج بالإضافة إلى الخصومات المؤتمتة وما إلى ذلك. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new discount"
msgstr "تعيين الخصم الجديد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "تعيين الكمية الجديدة "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr "سيتم تطبيق الإعدادات المحددة في هذه الصفحة على نقطة البيع هذه. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr "مشاركة الطلبات المفتوحة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
#, python-format
msgid "Ship Later"
msgstr "الشحن لاحقاً"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr "تاريخ الشحن "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr "سياسة الشحن"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Shop"
msgstr "المتجر"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Shopping cart"
msgstr "عربة التسوق"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Category Images"
msgstr "إظهار صور الفئة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Product Images"
msgstr "إظهار صور المنتج "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr "إظهار نافذة الدفع والخروج للعملاء عن طريق شاشة عرض ثانية "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "إظهار القيمة المستحقة للعملاء مستخدمي الشاشات المتصلة عن بعد."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Show checkout to customers."
msgstr "إظهار نافذة الدفع والخروج للعملاء. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr "إظهار شاشة تسجيل دخول الموظفين "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr "إظهار الهوامش والتكاليف في معلومات المنتج "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr "عرض الطلبات في شاشة تحضير الطعام "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr "تسجيل الدخول"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Six"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "جهاز دفع Six "

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "الحجم"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "تجاوز شاشة المعاينة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr "تخطي بند عند إرسال تذكرة إلى طابعات المطبخ. "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "رف صغير"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "بعض الأرقام التسلسلية/أرقام الدفعات غير موجودة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"تعذر إرسال بعض الطلبات إلى الخادم جراء أخطاء في التهيئة. يمكنك الخروج من "
"نقطة البيع، ولكن لا تقم بإغلاق الجلسة قبل أن يتم حل المشكلة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"تعذر إرسال بعض الطلبات إلى الخادم جراء أخطاء في الاتصال بالإنترنت. يمكنك "
"الخروج من نقطة البيع، ولكن لا تقم بإغلاق الجلسة قبل أن يتم حل المشكلة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""
"بعض عمليات المعالجة الأولية قد فشلت إن لم يكن كلها وذلك بعد مزامنة الطلب. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr "مسار محدد "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr ""
"قم بتحديد الرقم التسلسلي/رقم الدفعة الخاص بالمنتج في بند طلب نقطة البيع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr "مسار محدد للطلبات التي سوف يتم توصيلها في وقت لاحق. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_start_category
msgid "Start Category"
msgstr "فئة البدء "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.js:0
#, python-format
msgid "Start adding products"
msgstr "ابدأ بإضافة المنتجات "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Start category should belong in the available categories."
msgstr "يجب أن تنتمي فئة البدء إلى الفئات المتاحة. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Start selling from a default product category"
msgstr "ابدأ بالبيع من فئة المنتج الافتراضية"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "الرصيد الافتتاحي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "State"
msgstr "الولاية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "الحالة"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "حركة المخزون"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "قاعدة المخزون"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock input for %s"
msgstr "مدخلات المخزون لـ %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock output for %s"
msgstr "مدخلات المخزون لـ %s "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "يجب تحديث بضاعة المخزون عند الإغلاق "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Street"
msgstr "الشارع"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr "Stripe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr "جهاز الدفع بالبطاقة Stripe "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
msgid "Subtotal"
msgstr "الناتج الفرعي"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "الإجمالي الفرعي دون ضرائب "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "الإجمالي الفرعي دون خصم "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "تم الاستيراد بنجاح"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "تم إجراء حركة نقدية %s بمبلغ %s. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "مجموع الإجماليات الفرعية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Summary"
msgstr "الملخص"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Switch Product View"
msgstr "تبديل نافذة عرض المنتج "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "المزامنة متصلة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "جاري الاتصال للمزامنة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "انقطع الاتصال عن المزامنة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "خطأ في المزامنة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "الإجمالي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "الضريبة"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "مبلغ الضريبة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr "عرض الضريبة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Tax ID"
msgstr "معرف الضريبة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "Tax ID: %(vatId)s"
msgstr "المعرّف الضريبي: %(vatId)s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Name"
msgstr "اسم الضريبة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "قيمة اختيار نظام الضريبة"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "السعر غير شامل للضريبة "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "السعر شامل للضريبة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "الضرائب"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on refunds"
msgstr "ضرائب استرداد الأموال "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on sales"
msgstr "ضرائب المبيعات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "الضرائب المُراد تطبيقها"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Taxes:"
msgstr "الضرائب:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuff"
msgstr "المسائل التقنية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuffs"
msgstr "المسائل التقنية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Tel:"
msgstr "هاتف:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Thank you for your purchase!"
msgstr "شكراً لشرائك! "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The %s must be filled in your details."
msgstr "يجب أن يكون %s مليئاً ببياناتك. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "عنوان IP أو اسم المضيف لجهاز الطابعة الوكيل "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"كود الدولة حسب المعيار الدولي أيزو المكون من حرفين.\n"
"يمكنك استخدام هذا الحقل لإجراء بحث سريع."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, customer, employee or action "
"associated with the scanned barcode."
msgstr ""
"لم تتمكن نقطة البيع من العثور على أي منتج أو عميل أو إجراء مرتبط بالباركود "
"الممسوح. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr "تدعم نقطة البيع فقط استراتيجية تقريب \"إضافة بند تقريب\". "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The Point of Sale order with the following reference %s was received by the Odoo server, but the order processing phase failed.\n"
"The datas received from the point of sale has been saved in the attachments.\n"
"Please contact your support service to assist you on restoring it"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The Ticket Number should be at least 14 characters long."
msgstr "يجب أن يكون رقم التذكرة مكوناً من 14 خانة على الأقل. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"لا يمكن أن يكون المبلغ أكبر من المبلغ المستحق إذا لم تكن لديك طريقة دفع "
"نقدية مهيئة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the transaction.\n"
"The rounding precision is %s so you should set %s or %s as payment amount instead of %s."
msgstr ""
"يجب أن يتم تقريب المبلغ في بنود الدفع حتى تتمكن من تصديق المعاملة.\n"
"دقة التقريب هي %s لذلك عليك تعيين %s أو %s كمبلغ للدفع عوضاً عن %s. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr "يجب أن تكون استراتيجية تقريب المال لنقطة البيع %(pos)s : '%(value)s' "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "يجب أن تتضمن قوائم الأسعار المتاحة قائمة الأسعار الافتراضية."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"يجب ألّا تنتمي قائمة الأسعار الافتراضية إلى أي شركة أو أن تنتمي إلى شركة "
"نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default tip product is missing. Please manually specify the tip product."
" (See Tips field.)"
msgstr ""
"المنتج الافتراضي مفقود. يرجى تحديد منتج البقشيش يدوياً (انظر إلى حقل "
"البقشيش.) "

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""
"منظم المكتب مثالي لتخزين جميع أنواع الأشياء الصغيرة وبما أن الصناديق الخمسة "
"منفصلة عن بعضها، يمكنك تحريكها ووضعها بالطريقة التي تناسبك وتناسب أغراضك. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"لم يتم تحميل الوضع المالي المستخدم في الطلب الرئيسي. تأكد من أنه قد تم "
"تحميله عن طريق إضافته في تهيئة نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The function to load %s has not been implemented."
msgstr "لم يتم تطبيق الوظيفة لتحميل %s. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"اسم المضيف أو عنوان الـip لوكيل الجهاز، سيتم الكشف التلقائي عنهم إذا تركت "
"هذه الخانة فارغة."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"يجب أن تكون عملة دفتر يومية الفاتورة هي نفس العملة المذكورة في دفتر يومية "
"المبيعات أو عملة الشركة إن لم يكن قد تم ضبط عملة لدفتر اليومية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"The maximum difference allowed is %s.\n"
"Please contact your manager to accept the closing difference."
msgstr ""
"الحد الأقصى للفرق المسموح به هو %s.\n"
"يرجى التواصل مع مديرك لقبول فرق الإقفال. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_bill.py:0
#, python-format
msgid "The name of the Coins/Bills must be a number."
msgstr "يجب أن يكون اسم العملات/النقود الورقية عدداً. "

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr "يجب أن يكون اسم جلسة نقطة البيع هذه فريدًا!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "عدد طلبات نقاط البيع المرتبطة بهذا العميل "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "تعذّر إرسال الطلب إلى الخادم بسبب خطأ مجهول "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "The order has been already paid."
msgstr "لقد تم دفع قيمة الطلب بالفعل. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"تمت مزامنة الطلب سابقاً. يرجى إنشاء الفاتورة من الواجهة الخلفية للطلب: "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr "طريقة الدفع المحددة غير مسموح بها في تهيئة جلسة نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "يجب أن تنتمي طرق دفع نقطة البيع %s إلى شركتها. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"ستعرض نقطة البيع فئة هذا المنتج افتراضيًا. إذا لم تحدد فئة معينة، ستعرض كافة"
" المنتجات المتاحة."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"ستعرض نقطة البيع فقط المنتجات التي تندرج ضمن إحدى أشجار الفئات المحددة. إذا "
"لم يتم تحديد أي فئة، سوف يتم إظهار كافة المنتجات المتاحة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured if any."
msgstr ""
"قائمة الأسعار المستخدمة إذا لم يتم تحديد عميل أو في حال لم يقم العميل بتهيئة"
" قائمة أسعار المبيعات، إن وجدت. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "نسبة العملة مقابل العملة ذات النسبة الممكن تطبيقها في تاريخ الطلب "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr "سوف يتم تخطي شاشة الإيصال إذا كان من الممكن طباعة الإيصال تلقائياً. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "ستتم طباعة الإيصال تلقائياً عند نهاية كل طلب. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"الكمية المطلوب استرداد قيمتها أكبر من الكمية التي قد تم طلبها. لقد تم طلب "
"استرداد مبالغ الكمية %s بينما يمكن استرداد مبالغ %s فقط. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr ""
"الكمية المطلوب استرداد قيمتها أكبر من الكمية التي يمكن استرداد قيمتها، وهي "
"%s. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo_line__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"تتم إدارة سعر البيع من قالب المنتج. اضغط على زر 'تهيئة المتغيرات' لتعيين "
"أسعار الخصائص الإضافية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "يحتاج العميل المحدد إلى عنوان. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"يجب ألّا تنتمي قائمة الأسعار المحددة إلى أي شركة أو أن تنتمي إلى شركة نقطة "
"البيع. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "حدث خطأ في الخادم أثناء استلام طلبك. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The session has been already closed by another User. All sales completed in "
"the meantime have been saved in a Rescue Session, which can be reviewed "
"anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""
"تم إغلاق الجلسة بالفعل بواسطة مستخدم آخر. تم حفظ كافة المبيعات التي تم "
"إكمالها في الوقت الحالي في جلسة استرجاع، والتي يمكن مراجعتها في أي وقت "
"وترحيلها إلى المحاسبة من لوحة بيانات نقطة البيع. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "هذه الجلسة مفتوحة منذ وقت طويل. ننصح بإغلاق الجلسة."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"تتم معالجة المعاملات بواسطة Adyen. قم بتعيين بيانات اعتماد Adyen الخاصة بك "
"في طريقة الدفع ذات الصلة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_paytm
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by PayTM. Set your PayTM credentials on the "
"related payment method."
msgstr ""
"تتم معالجة المعاملات بواسطة PayTM. قم بتعيين بيانات اعتماد PayTM الخاصة بك "
"في طريقة الدفع ذات الصلة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"تتم معالجة المعاملات بواسطة Six. قم بتعيين عنوان IP لجهاز الدفع بالبطاقة في "
"طريقة الدفع ذات الصلة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Stripe. Set your Stripe credentials on the"
" related payment method."
msgstr ""
"تتم معالجة المعاملات عن طريق Stripe. قم بإعداد بيانات اعتماد Stripe الخاصة "
"بك في طريقة الدفع ذات الصلة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"تتم معالجة المعاملات بواسطة Vantiv. قم بتعيين بيانات اعتماد Vantiv الخاصة بك"
" في طريقة الدفع ذات الصلة. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo__base_price
msgid ""
"The value from which pro-rating of the component price is based. This is to "
"ensure that whatever product the user chooses for a component, it will "
"always be they same price."
msgstr ""
"القيمة التي يعتمد عليها التصنيف التناسبي لسعر المكوِّن. هذا للتأكد من أن "
"المنتج الذي يختاره المستخدم لأحد المكونات، سيكون دائماً بنفس السعر. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "رصيد الإقفال المتوقع"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "لا توجد منتجات في هذه الفئة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"لا تزال هناك طلبات في حالة المسودة في هذه الجلسة. قم بدفع أو إلغاء الطلبات التالية لتصديق الجلسة: \n"
"%s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr "توجد طلبات غير متزامنة. هل ترغب في مزامنة تلك الطلبات؟ "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"هناك فرق بين المبالغ لترحيلها ومبالغ الطلبات. هذا الفرق غالباً نتيجة للضرائب"
" أو التغييرات التي أُجريت على تهيئة المحاسبة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "هناك عملية دفع إلكترونية قيد التنفيذ بالفعل. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"لا يوجد مخطط حسابات مهيأ في الشركة. يرجى الذهاب غلى إعدادات الفوترة لتثبيت "
"مخطط الحسابات. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"ليس هناك طريقة دفع نقدي متاحة في نقطة البيع هذه للتعامل مع الباقي.\n"
"\n"
"يرجى دفع المبلغ المحدد أو إضافة طريقة الدفع نقداً في تهيئة نقطة البيع "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash payment method for this PoS Session"
msgstr "لا توجد طريقة دفع نقدية لجلسة نقطة البيع هذه "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "لا يوجد صندوق لتسجيل النقد في هذه الجلسة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"يجب أن يكون هناك منتج واحد على الأقل في طلبك قبل أن تتمكن من تصديقه وفوترته."
" "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""
"طريقة الدفع النقدية هذه مستخدمة بالفعل في نقطة بيع أخرى. \n"
"يجب إنشاء طريقة دفع نقدية أخرى لنقطة البيع هذه. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"يعبر هذا الحقل عن الفارق الأقصى المسموح به بين الرصيد الختامي والقيمة "
"النظرية عند إغلاق الجلسة، لمستخدمي نقطة البيع من غير المدراء. إذا وصل "
"المستخدم إلى هذا الحد الأقصى، ستظهر له رسالة خطأ عند إغلاق الجلسة لإخباره "
"بأنه عليه التواصل مع مديره. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"وظيفة هذا الحقل هي إظهار مُعرف مجموعة مدراء نقطة البيع لعميل نقطة البيع. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"وظيفة هذا الحقل هي إظهار مُعرف مجموعة مستخدمي نقطة البيع لعميل نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "This invoice has been created from the point of sale session: %s"
msgstr "لقد تم إنشاء هذه الفاتورة من جلسة نقطة البيع: %s "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"يكون هذا مفيدًا للمطاعم التي تقدم خدمات الأكل داخل المطعم أو الوجبات السريعة"
" التي تتضمن رسوم ضريبية معينة."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr "دفتر اليومية هذا مرتبط بطريقة دفع. لا يمكنك تعديل نوعه "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with payment method %s that is being used by "
"order %s in the active pos session %s"
msgstr ""
"يرتبط دفتر اليومية هذا بطريقة الدفع %s المستخدمة من قِبَل الطلب %s في جلسة "
"نقاط البيع النشطة %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"ستؤدي هذه العملية إلى إتلاف كافة الطلبات غير المدفوعة في المتصفح. ستفقد كافة"
" البيانات غير المحفوظة وتغادر نقطة البيع. لا يمكن التراجع عن هذه العملية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"ستؤدي هذه العملية إلى إتلاف كافة الطلبات المدفوعة في ذاكرة التخزين المحلية. "
"ستفقد كافة البيانات. لا يمكن التراجع عن هذه العملية. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"يحتوي هذا الطلب على بنود استرداد الأموال لـ %s. لا يمكننا تغيير العميل "
"المرتبط به. قم بإنشاء طلب جديد للعميل الجديد. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "This order is empty"
msgstr "هذا الطلب فارغ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"هذا الطلب غير متزامن في الخادم بعد. تأكد من أنه متزامن ثم حاول مجدداً. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr "يستخدم هذا المنتج كمرجع في فواتير العميل."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"يتم إنشاء هذا التسلسل تلقائياً من قبل أودو ولكن يمكنك تغييره لتخصيص الأرقام "
"المرجعية لبنود طلباتك. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"يتم إنشاء هذا التسلسل تلقائياً من قبل أودو ولكن يمكنك تغييره لتخصيص الأرقام "
"المرجعية لطلباتك. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "هذه الجلسة مغلقة بالفعل. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "يتم تطبيق هذه الضريبة على كل منتج جديد يُنشأ في الدليل."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr "هذه الإعدادات مشتركة في كافة نقاط البيع. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr "رمز التذكرة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr "رقم التذكرة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Tip"
msgstr "بقشيش "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "مبلغ البقشيش "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr "وضع بقشيش للمنتج "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr "بقشيش"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "البقشيش: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "للإقفال"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "To Pay"
msgstr "للدفع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "To Refund:"
msgstr "لاسترداد الأموال: "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "الوزن بميزان "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"To delete a product, make sure all point of sale sessions are closed.\n"
"\n"
"Deleting a product available in a session would be like attempting to snatch a hamburger from a customer’s hand mid-bite; chaos will ensue as ketchup and mayo go flying everywhere!"
msgstr ""
"لحذف منتج، تأكد من إغلاق كافة جلسات نقاط البيع.\n"
"\n"
"سيكون حذف منتج متاح في جلسة ما أشبه بمحاولة انتزاع شطيرة همبرغر من يد عميل في منتصف قضمتها؛ ستحدث فوضى عارمة حيث يتطاير الكاتشب والمايونيز في كل مكان! "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "بانتظار الفوترة"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "لتسجيل طلبات جديدة، ابدأ جلسة جديدة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "لإرجاع المنتج( المنتجات)، ستحتاج إلى فتك جلسة في نقطة البيع %s "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "الإجمالي"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total (Tax excluded)"
msgstr "الإجمالي (غير شامل الضريبة) "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "إجمالي المعاملات النقدية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "التكلفة الإجمالية:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "إجمالي الخصم "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Total Due"
msgstr "الإجمالي المستحق"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "إجمالي الهامش: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "إجمالي المدفوع (مع التقريب) "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "إجمالي مبلغ المدفوعات "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "إجمالي السعر "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Price excl. Tax:"
msgstr "إجمالي السعر غير شامل الضريبة: "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "إجمالي مبلغ الدفع. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "إجمالي التكلفة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "إجمالي الكمية"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "الإجمالي:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "معاملة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "تم إلغاء المعاملة "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "تحويل "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr "نقطة بيع موثوقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr "تهيئة نقطة البيع الموثوقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__type
msgid "Type"
msgstr "النوع"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "نوع البطاقة المستخدمة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "UNSELECT"
msgstr "إلغاء التحديد "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"تعذر إغلاق وتصديق الجلسة. \n"
"يرجى ضبط حساب الضريبة المقابل في كل بند إعادة توزيع للضرائب التالية: \n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "تعذر تحميل الفاتورة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"تعذر تعديل تهيئة نقطة البيع هذه لأن لا يمكنك تعديل %s عندما تكون الجلسة "
"مفتوحة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid "Unable to parse barcode"
msgstr "تعذر تحليل الباركود "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "تعذر إظهار المعلومات عن هذا الخطأ. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "تعذر مزامنة الطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Unique Code:"
msgstr "الرمز الفريد: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr "الرمز الفريد "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Unit"
msgstr "الوحدة"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "سعر الوحدة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#, python-format
msgid "Unknown Barcode:"
msgstr "باركود غير معروف: "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "خطأ غير معروف"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "Unselect"
msgstr "إلغاء التحديد "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "صيغة ملف غير مدعومة"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Unsupported search operation"
msgstr "عملية البحث غير مدعومة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr "طلب غير متزامن "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "وحدة القياس"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "تحديث الكميات في المخزون "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Use QR code on ticket"
msgstr "استخدم رمز QR على التذكرة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "استخدام جهاز الدفع بالبطاقة "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr "استخدام قائمة أسعار."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "استخدام الباركودات لمسح المنتجات وبطاقات العملاء وما إلى ذلك. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr "استخدم الأوضاع المالية للحصول على ضرائب مختلفة لكل طلب "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Used to record product pickings. Products are consumed from its default "
"source location."
msgstr ""
"يُستخدم لتسجيل عمليات انتقاء المنتجات. يتم استهلاك المنتجات من موقعه المصدري"
" الافتراضي. "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "المستخدم"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "بطاقات عنوان المستخدم"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
msgid "Uuid"
msgstr "المعرف الفريد عالمياً "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Validate"
msgstr "تصديق "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (الولايات المتحدة الأمريكية وكندا) "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "جهاز الدفع بالبطاقة Vantiv "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Waiting for card"
msgstr "بانتظار البطاقة "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "رف حائط"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "المستودع "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr "المستودع (نقطة البيع) "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Weighing"
msgstr "تزن"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "منتج موزون"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "عندما تكون كافة المنتجات جاهزة "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid ""
"When disabled, only PoS manager can view the margin and cost of product "
"among the Product info."
msgstr ""
"عندما لا يكون مفعلاً، وحده مدير نقطة البيع بوسعه عرض هامش وتكاليف المنتج ضمن"
" معلومات المنتج. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"عندما تغلق جلسة، يتم إنشاء قيد واحد في دفتر اليومية المحاسبي التالي لكافة "
"الأوامر غير المفوترة. وتُسجل الفواتير على حدة."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "سبورة"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "قلم السبورة"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "With a"
msgstr "بـ"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Yes"
msgstr "نعم"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"لا يُسمح لك بتغيير تهيئة تقريب النقد عندما تكون هناك نقطة بيع مفتوحة "
"تستخدمه. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "لا يُسمح لك بغيير هذه الكمية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"أنت تحاول بيع منتجات بأرقام تسلسلية/أرقام دفعات، ولكن لم يتم تعيين أرقام لبعض المنتجات بعد. \n"
"هل ترغب بالاستمرار على أي حال؟ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "You can go to"
msgstr "بإمكانك الذهاب إلى "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can only unlink PoS order lines that are related to orders in new or "
"cancelled state."
msgstr ""
"يمكنك فقط إلغاء ربط بنود طلب نقطة البيع المرتبطة بالطلبات الجديدة أو الطلبات"
" الملغية. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"لا يمكنك: إنشاء أمر في نقطة البيع من الواجهة الخلفية، أو إزالة أعدادات قائمة"
" الأسعار، أو إنشاء أمر في نقطة البيع في اختبار بايثون باستخدام أداة نسخ "
"معلومات الاستمارات أو تحرير نافذة عرض الاستمارة في الاستوديو إذا لم يكن هناك"
" أمر في نقطة بيع "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid "You cannot archive '%s' as it is used by a POS configuration '%s'."
msgstr "لا يمكنك أرشفة '%s' حيث إنه مستخدم في تهيئة نقطة البيع '%s'. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"لا يمكنك إغلاق نقطة البيع بينما توجد فواتير لم يتم ترحيلها. \n"
"الفواتير: %s "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "لا يمكنك إغلاق نقطة البيع عندما تكون الطلبات في حالة المسودة "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "لا يمكنك إنشاء جلسة قبل تاريخ الإقفال المحاسبي. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr "لا يمكنك حذف فئة نقطة البيع عندما تكون هناك جلسة مفتوحة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You cannot delete a rounding method that is used in a Point of Sale "
"configuration."
msgstr "لا يمكنك حذف طريقة تقريب مستخدمة في تهيئة نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You cannot invoice orders belonging to different companies."
msgstr "لا يمكنك فوترة الطلبات التي تنتمي لشركات مختلفة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "You cannot save an empty order"
msgstr "لا يمكنك حفظ طلب فارغ "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You cannot share open orders with configuration that does not use the same "
"currency."
msgstr ""
"لا يمكنك مشاركة الطلبات المفتوحة التي بها تهيئة لا تستخدم نفس العملة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr "لا يمكنك استخدام نفس دفتر اليومية في عدة طرق دفع نقدية. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You do not have permission to open a POS session. Please try opening a "
"session with a different user"
msgstr ""
"لا تملك الإذن لفتح جلسة عمل نقاط البيع. يرجى محاولة فتح الجلسة عن طريق "
"مُستخدِم آخر "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr "لا تملك صلاحية الوصول إلى- بيانات التحكم في إقفال نقطة البيع. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"لقد قمت بتمكين خاصية \"التعرف على العميل\" لطريقة دفع %s، ولكن الطلب %s لا "
"يحتوي على عميل. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"You have selected orderlines from multiple invoiced orders. To proceed "
"refund, please select orderlines from the same invoiced order."
msgstr ""
"لقد قمت بتحديد بنود طلبات من عدة طلبات مفوترة. للاستمرار بعملية استرداد "
"الأموال، يرجى تحديد بنود الطلب من نفس الطلب المفوتر. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "عليك تقريب بنود الدفع الخاصة بك. إنها ليست مقربة. "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"يجب تعريف منتج لكل شيء تبيعه من خلال\n"
"                واجهة نقطة البيع."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "You must first remove this product from the %s combo"
msgstr "عليك إزالة هذا المنتج من %s الكومبو "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""
"يجب أن تكون لديك طريقة دفع مهيأة واحدة على الأقل حتى تتمكن من بدء جلسة. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "يجب أن يكون هناك حساب أرباح وخسائر في دفتر يومية الصندوق. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr "عليك تحديد العميل قبل أن تتمكن من فوترة أو شحن الطلب. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "عليك تعيين نقطة بيع لجلستك. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "You're offline"
msgstr "أنت غير متصل بالإنترنت "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Your Order"
msgstr "طلبك"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"جلسة نقطة البيع الخاصة بك مفتوحة منذ %(date)s. ننصحك بإغلاقها وإنشاء واحدة "
"جديدة. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""
"عنوانك غير موجود أو غير مكتمل. <br/>\n"
"                                يرجى التأكد من "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Zip"
msgstr "الرمز البريدي"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "available,"
msgstr "متاح، "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr "قبل الاستمرار. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "تنتمي إلى جلسة أخرى: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_tree
msgid "combos"
msgstr "الكومبو "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "create your own"
msgstr "أنشئ خاصتك "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "demo data"
msgstr "البيانات التجريبية "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "discount"
msgstr "خصم"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "e.g. Burger Menu"
msgstr "مثال: قائمة الهمبرغر "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "مثال: النقد "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr "مثلاً: عنوان الشركة، موقع الشركة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "مثال: متجر NYC "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "مثال: سياسة الإرجاع، شكراً لتسوقك معنا! "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "مثال: المشروبات الغازية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr "طابعة نقطة البيع الإلكترونية "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr "تعبئة كافة المعلومات ذات الصلة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr "لـ "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "for an order of"
msgstr "لطلب "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "forecasted"
msgstr "المتوقعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "in"
msgstr "في"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "in this category."
msgstr "في هذه الفئة. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "العناصر "

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "ملاحظة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "or"
msgstr "أو"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "orders:"
msgstr "الطلبات: "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr "جلسة استرجاع معلقة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "paid orders"
msgstr "الطلبات المدفوعة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr "qx9h1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "إرجاع "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the invoice"
msgstr "الفاتورة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the receipt"
msgstr "الإيصال "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "الطلبات غير المدفوعة "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "تعذّر استيراد الطلبات غير المدفوعة "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "باستخدام"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "كانت نسخًا من طلبات موجودة "
