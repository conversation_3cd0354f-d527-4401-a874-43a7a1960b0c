<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xmlrpc.server — Basic XML-RPC servers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xmlrpc.server.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xmlrpc/server.py The xmlrpc.server module provides a basic server framework for XML-RPC servers written in Python. Servers can either be free standing, using SimpleXMLRPCServer, or..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xmlrpc/server.py The xmlrpc.server module provides a basic server framework for XML-RPC servers written in Python. Servers can either be free standing, using SimpleXMLRPCServer, or..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xmlrpc.server — Basic XML-RPC servers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="ipaddress — IPv4/IPv6 manipulation library" href="ipaddress.html" />
    <link rel="prev" title="xmlrpc.client — XML-RPC client access" href="xmlrpc.client.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xmlrpc.server.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a><ul>
<li><a class="reference internal" href="#simplexmlrpcserver-objects">SimpleXMLRPCServer Objects</a><ul>
<li><a class="reference internal" href="#simplexmlrpcserver-example">SimpleXMLRPCServer Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#cgixmlrpcrequesthandler">CGIXMLRPCRequestHandler</a></li>
<li><a class="reference internal" href="#documenting-xmlrpc-server">Documenting XMLRPC server</a></li>
<li><a class="reference internal" href="#docxmlrpcserver-objects">DocXMLRPCServer Objects</a></li>
<li><a class="reference internal" href="#doccgixmlrpcrequesthandler">DocCGIXMLRPCRequestHandler</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.client.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ipaddress.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xmlrpc.server.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ipaddress.html" title="ipaddress — IPv4/IPv6 manipulation library"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.client.html" title="xmlrpc.client — XML-RPC client access"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xmlrpc.server">
<span id="xmlrpc-server-basic-xml-rpc-servers"></span><h1><a class="reference internal" href="#module-xmlrpc.server" title="xmlrpc.server: Basic XML-RPC server implementations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code></a> — Basic XML-RPC servers<a class="headerlink" href="#module-xmlrpc.server" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xmlrpc/server.py">Lib/xmlrpc/server.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-xmlrpc.server" title="xmlrpc.server: Basic XML-RPC server implementations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code></a> module provides a basic server framework for XML-RPC
servers written in Python.  Servers can either be free standing, using
<a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer" title="xmlrpc.server.SimpleXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCServer</span></code></a>, or embedded in a CGI environment, using
<a class="reference internal" href="#xmlrpc.server.CGIXMLRPCRequestHandler" title="xmlrpc.server.CGIXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIXMLRPCRequestHandler</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <a class="reference internal" href="#module-xmlrpc.server" title="xmlrpc.server: Basic XML-RPC server implementations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code></a> module is not secure against maliciously
constructed data.  If you need to parse untrusted or unauthenticated data see
<a class="reference internal" href="xml.html#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a>.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCServer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">SimpleXMLRPCServer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">addr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">requestHandler</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SimpleXMLRPCRequestHandler</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">logRequests</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bind_and_activate</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_builtin_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCServer" title="Link to this definition">¶</a></dt>
<dd><p>Create a new server instance.  This class provides methods for registration of
functions that can be called by the XML-RPC protocol.  The <em>requestHandler</em>
parameter should be a factory for request handler instances; it defaults to
<a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCRequestHandler" title="xmlrpc.server.SimpleXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCRequestHandler</span></code></a>.  The <em>addr</em> and <em>requestHandler</em> parameters
are passed to the <a class="reference internal" href="socketserver.html#socketserver.TCPServer" title="socketserver.TCPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">socketserver.TCPServer</span></code></a> constructor.  If <em>logRequests</em>
is true (the default), requests will be logged; setting this parameter to false
will turn off logging.   The <em>allow_none</em> and <em>encoding</em> parameters are passed
on to <a class="reference internal" href="xmlrpc.client.html#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a> and control the XML-RPC responses that will be returned
from the server. The <em>bind_and_activate</em> parameter controls whether
<code class="xref py py-meth docutils literal notranslate"><span class="pre">server_bind()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">server_activate()</span></code> are called immediately by the
constructor; it defaults to true. Setting it to false allows code to manipulate
the <em>allow_reuse_address</em> class variable before the address is bound.
The <em>use_builtin_types</em> parameter is passed to the
<a class="reference internal" href="xmlrpc.client.html#xmlrpc.client.loads" title="xmlrpc.client.loads"><code class="xref py py-func docutils literal notranslate"><span class="pre">loads()</span></code></a> function and controls which types are processed
when date/times values or binary data are received; it defaults to false.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>use_builtin_types</em> flag was added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">CGIXMLRPCRequestHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">allow_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_builtin_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>Create a new instance to handle XML-RPC requests in a CGI environment.  The
<em>allow_none</em> and <em>encoding</em> parameters are passed on to <a class="reference internal" href="xmlrpc.client.html#module-xmlrpc.client" title="xmlrpc.client: XML-RPC client access."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code></a>
and control the XML-RPC responses that will be returned from the server.
The <em>use_builtin_types</em> parameter is passed to the
<a class="reference internal" href="xmlrpc.client.html#xmlrpc.client.loads" title="xmlrpc.client.loads"><code class="xref py py-func docutils literal notranslate"><span class="pre">loads()</span></code></a> function and controls which types are processed
when date/times values or binary data are received; it defaults to false.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>use_builtin_types</em> flag was added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">SimpleXMLRPCRequestHandler</span></span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>Create a new request handler instance.  This request handler supports <code class="docutils literal notranslate"><span class="pre">POST</span></code>
requests and modifies logging so that the <em>logRequests</em> parameter to the
<a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer" title="xmlrpc.server.SimpleXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCServer</span></code></a> constructor parameter is honored.</p>
</dd></dl>

<section id="simplexmlrpcserver-objects">
<span id="simple-xmlrpc-servers"></span><h2>SimpleXMLRPCServer Objects<a class="headerlink" href="#simplexmlrpcserver-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer" title="xmlrpc.server.SimpleXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCServer</span></code></a> class is based on
<a class="reference internal" href="socketserver.html#socketserver.TCPServer" title="socketserver.TCPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">socketserver.TCPServer</span></code></a> and provides a means of creating simple, stand
alone XML-RPC servers.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCServer.register_function">
<span class="sig-prename descclassname"><span class="pre">SimpleXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">register_function</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCServer.register_function" title="Link to this definition">¶</a></dt>
<dd><p>Register a function that can respond to XML-RPC requests.  If <em>name</em> is given,
it will be the method name associated with <em>function</em>, otherwise
<a class="reference internal" href="../reference/datamodel.html#function.__name__" title="function.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">function.__name__</span></code></a> will be used.  <em>name</em> is a string, and may contain
characters not legal in Python identifiers, including the period character.</p>
<p>This method can also be used as a decorator.  When used as a decorator,
<em>name</em> can only be given as a keyword argument to register <em>function</em> under
<em>name</em>.  If no <em>name</em> is given, <a class="reference internal" href="../reference/datamodel.html#function.__name__" title="function.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">function.__name__</span></code></a> will be used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer.register_function" title="xmlrpc.server.SimpleXMLRPCServer.register_function"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_function()</span></code></a> can be used as a decorator.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCServer.register_instance">
<span class="sig-prename descclassname"><span class="pre">SimpleXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">register_instance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">instance</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_dotted_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCServer.register_instance" title="Link to this definition">¶</a></dt>
<dd><p>Register an object which is used to expose method names which have not been
registered using <a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer.register_function" title="xmlrpc.server.SimpleXMLRPCServer.register_function"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_function()</span></code></a>.  If <em>instance</em> contains a
<code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> method, it is called with the requested method name and the
parameters from the request.  Its API is <code class="docutils literal notranslate"><span class="pre">def</span> <span class="pre">_dispatch(self,</span> <span class="pre">method,</span> <span class="pre">params)</span></code>
(note that <em>params</em> does not represent a variable argument list).  If it calls
an underlying function to perform its task, that function is called as
<code class="docutils literal notranslate"><span class="pre">func(*params)</span></code>, expanding the parameter list. The return value from
<code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> is returned to the client as the result.  If <em>instance</em> does
not have a <code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> method, it is searched for an attribute matching
the name of the requested method.</p>
<p>If the optional <em>allow_dotted_names</em> argument is true and the instance does not
have a <code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> method, then if the requested method name contains
periods, each component of the method name is searched for individually, with
the effect that a simple hierarchical search is performed.  The value found from
this search is then called with the parameters from the request, and the return
value is passed back to the client.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Enabling the <em>allow_dotted_names</em> option allows intruders to access your
module’s global variables and may allow intruders to execute arbitrary code on
your machine.  Only use this option on a secure, closed network.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCServer.register_introspection_functions">
<span class="sig-prename descclassname"><span class="pre">SimpleXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">register_introspection_functions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCServer.register_introspection_functions" title="Link to this definition">¶</a></dt>
<dd><p>Registers the XML-RPC introspection functions <code class="docutils literal notranslate"><span class="pre">system.listMethods</span></code>,
<code class="docutils literal notranslate"><span class="pre">system.methodHelp</span></code> and <code class="docutils literal notranslate"><span class="pre">system.methodSignature</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCServer.register_multicall_functions">
<span class="sig-prename descclassname"><span class="pre">SimpleXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">register_multicall_functions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCServer.register_multicall_functions" title="Link to this definition">¶</a></dt>
<dd><p>Registers the XML-RPC multicall function system.multicall.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xmlrpc.server.SimpleXMLRPCRequestHandler.rpc_paths">
<span class="sig-prename descclassname"><span class="pre">SimpleXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">rpc_paths</span></span><a class="headerlink" href="#xmlrpc.server.SimpleXMLRPCRequestHandler.rpc_paths" title="Link to this definition">¶</a></dt>
<dd><p>An attribute value that must be a tuple listing valid path portions of the URL
for receiving XML-RPC requests.  Requests posted to other paths will result in a
404 “no such page” HTTP error.  If this tuple is empty, all paths will be
considered valid. The default value is <code class="docutils literal notranslate"><span class="pre">('/',</span> <span class="pre">'/RPC2')</span></code>.</p>
</dd></dl>

<section id="simplexmlrpcserver-example">
<span id="id1"></span><h3>SimpleXMLRPCServer Example<a class="headerlink" href="#simplexmlrpcserver-example" title="Link to this heading">¶</a></h3>
<p>Server code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>
<span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCRequestHandler</span>

<span class="c1"># Restrict to a particular path.</span>
<span class="k">class</span> <span class="nc">RequestHandler</span><span class="p">(</span><span class="n">SimpleXMLRPCRequestHandler</span><span class="p">):</span>
    <span class="n">rpc_paths</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;/RPC2&#39;</span><span class="p">,)</span>

<span class="c1"># Create server</span>
<span class="k">with</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s1">&#39;localhost&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">),</span>
                        <span class="n">requestHandler</span><span class="o">=</span><span class="n">RequestHandler</span><span class="p">)</span> <span class="k">as</span> <span class="n">server</span><span class="p">:</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_introspection_functions</span><span class="p">()</span>

    <span class="c1"># Register pow() function; this will use the value of</span>
    <span class="c1"># pow.__name__ as the name, which is just &#39;pow&#39;.</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span>

    <span class="c1"># Register a function under a different name</span>
    <span class="k">def</span> <span class="nf">adder_function</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">adder_function</span><span class="p">,</span> <span class="s1">&#39;add&#39;</span><span class="p">)</span>

    <span class="c1"># Register an instance; all the methods of the instance are</span>
    <span class="c1"># published as XML-RPC methods (in this case, just &#39;mul&#39;).</span>
    <span class="k">class</span> <span class="nc">MyFuncs</span><span class="p">:</span>
        <span class="k">def</span> <span class="nf">mul</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="n">y</span>

    <span class="n">server</span><span class="o">.</span><span class="n">register_instance</span><span class="p">(</span><span class="n">MyFuncs</span><span class="p">())</span>

    <span class="c1"># Run the server&#39;s main loop</span>
    <span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The following client code will call the methods made available by the preceding
server:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xmlrpc.client</span>

<span class="n">s</span> <span class="o">=</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">ServerProxy</span><span class="p">(</span><span class="s1">&#39;http://localhost:8000&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">pow</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">3</span><span class="p">))</span>  <span class="c1"># Returns 2**3 = 8</span>
<span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">3</span><span class="p">))</span>  <span class="c1"># Returns 5</span>
<span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">mul</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">))</span>  <span class="c1"># Returns 5*2 = 10</span>

<span class="c1"># Print list of available methods</span>
<span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">system</span><span class="o">.</span><span class="n">listMethods</span><span class="p">())</span>
</pre></div>
</div>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_function()</span></code> can also be used as a decorator. The previous server
example can register functions in a decorator way:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCServer</span>
<span class="kn">from</span> <span class="nn">xmlrpc.server</span> <span class="kn">import</span> <span class="n">SimpleXMLRPCRequestHandler</span>

<span class="k">class</span> <span class="nc">RequestHandler</span><span class="p">(</span><span class="n">SimpleXMLRPCRequestHandler</span><span class="p">):</span>
    <span class="n">rpc_paths</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;/RPC2&#39;</span><span class="p">,)</span>

<span class="k">with</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s1">&#39;localhost&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">),</span>
                        <span class="n">requestHandler</span><span class="o">=</span><span class="n">RequestHandler</span><span class="p">)</span> <span class="k">as</span> <span class="n">server</span><span class="p">:</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_introspection_functions</span><span class="p">()</span>

    <span class="c1"># Register pow() function; this will use the value of</span>
    <span class="c1"># pow.__name__ as the name, which is just &#39;pow&#39;.</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span>

    <span class="c1"># Register a function under a different name, using</span>
    <span class="c1"># register_function as a decorator. *name* can only be given</span>
    <span class="c1"># as a keyword argument.</span>
    <span class="nd">@server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s1">&#39;add&#39;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">adder_function</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">x</span> <span class="o">+</span> <span class="n">y</span>

    <span class="c1"># Register a function under function.__name__.</span>
    <span class="nd">@server</span><span class="o">.</span><span class="n">register_function</span>
    <span class="k">def</span> <span class="nf">mul</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="n">y</span>

    <span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>The following example included in the <code class="file docutils literal notranslate"><span class="pre">Lib/xmlrpc/server.py</span></code> module shows
a server allowing dotted names and registering a multicall function.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Enabling the <em>allow_dotted_names</em> option allows intruders to access your
module’s global variables and may allow intruders to execute arbitrary code on
your machine.  Only use this example only within a secure, closed network.</p>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">datetime</span>

<span class="k">class</span> <span class="nc">ExampleService</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">getData</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="s1">&#39;42&#39;</span>

    <span class="k">class</span> <span class="nc">currentTime</span><span class="p">:</span>
        <span class="nd">@staticmethod</span>
        <span class="k">def</span> <span class="nf">getCurrentTime</span><span class="p">():</span>
            <span class="k">return</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span>

<span class="k">with</span> <span class="n">SimpleXMLRPCServer</span><span class="p">((</span><span class="s2">&quot;localhost&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">))</span> <span class="k">as</span> <span class="n">server</span><span class="p">:</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">:</span> <span class="n">x</span><span class="o">+</span><span class="n">y</span><span class="p">,</span> <span class="s1">&#39;add&#39;</span><span class="p">)</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_instance</span><span class="p">(</span><span class="n">ExampleService</span><span class="p">(),</span> <span class="n">allow_dotted_names</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">server</span><span class="o">.</span><span class="n">register_multicall_functions</span><span class="p">()</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Serving XML-RPC on localhost port 8000&#39;</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">server</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">Keyboard interrupt received, exiting.&quot;</span><span class="p">)</span>
        <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>This ExampleService demo can be invoked from the command line:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">server</span>
</pre></div>
</div>
<p>The client that interacts with the above server is included in
<code class="docutils literal notranslate"><span class="pre">Lib/xmlrpc/client.py</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">server</span> <span class="o">=</span> <span class="n">ServerProxy</span><span class="p">(</span><span class="s2">&quot;http://localhost:8000&quot;</span><span class="p">)</span>

<span class="k">try</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">server</span><span class="o">.</span><span class="n">currentTime</span><span class="o">.</span><span class="n">getCurrentTime</span><span class="p">())</span>
<span class="k">except</span> <span class="n">Error</span> <span class="k">as</span> <span class="n">v</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ERROR&quot;</span><span class="p">,</span> <span class="n">v</span><span class="p">)</span>

<span class="n">multi</span> <span class="o">=</span> <span class="n">MultiCall</span><span class="p">(</span><span class="n">server</span><span class="p">)</span>
<span class="n">multi</span><span class="o">.</span><span class="n">getData</span><span class="p">()</span>
<span class="n">multi</span><span class="o">.</span><span class="n">pow</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">9</span><span class="p">)</span>
<span class="n">multi</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="k">try</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">response</span> <span class="ow">in</span> <span class="n">multi</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
<span class="k">except</span> <span class="n">Error</span> <span class="k">as</span> <span class="n">v</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;ERROR&quot;</span><span class="p">,</span> <span class="n">v</span><span class="p">)</span>
</pre></div>
</div>
<p>This client which interacts with the demo XMLRPC server can be invoked as:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">xmlrpc</span><span class="o">.</span><span class="n">client</span>
</pre></div>
</div>
</section>
</section>
<section id="cgixmlrpcrequesthandler">
<h2>CGIXMLRPCRequestHandler<a class="headerlink" href="#cgixmlrpcrequesthandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xmlrpc.server.CGIXMLRPCRequestHandler" title="xmlrpc.server.CGIXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIXMLRPCRequestHandler</span></code></a> class can be used to handle XML-RPC
requests sent to Python CGI scripts.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler.register_function">
<span class="sig-prename descclassname"><span class="pre">CGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">register_function</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_function" title="Link to this definition">¶</a></dt>
<dd><p>Register a function that can respond to XML-RPC requests.  If <em>name</em> is given,
it will be the method name associated with <em>function</em>, otherwise
<a class="reference internal" href="../reference/datamodel.html#function.__name__" title="function.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">function.__name__</span></code></a> will be used.  <em>name</em> is a string, and may contain
characters not legal in Python identifiers, including the period character.</p>
<p>This method can also be used as a decorator.  When used as a decorator,
<em>name</em> can only be given as a keyword argument to register <em>function</em> under
<em>name</em>.  If no <em>name</em> is given, <a class="reference internal" href="../reference/datamodel.html#function.__name__" title="function.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">function.__name__</span></code></a> will be used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_function" title="xmlrpc.server.CGIXMLRPCRequestHandler.register_function"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_function()</span></code></a> can be used as a decorator.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler.register_instance">
<span class="sig-prename descclassname"><span class="pre">CGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">register_instance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">instance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_instance" title="Link to this definition">¶</a></dt>
<dd><p>Register an object which is used to expose method names  which have not been
registered using <a class="reference internal" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_function" title="xmlrpc.server.CGIXMLRPCRequestHandler.register_function"><code class="xref py py-meth docutils literal notranslate"><span class="pre">register_function()</span></code></a>. If  instance contains a
<code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> method, it is called with the  requested method name and the
parameters from the  request; the return value is returned to the client as the
result. If instance does not have a <code class="xref py py-meth docutils literal notranslate"><span class="pre">_dispatch()</span></code> method, it is searched
for an attribute matching the name of the requested method; if  the requested
method name contains periods, each  component of the method name is searched for
individually,  with the effect that a simple hierarchical search is performed.
The value found from this search is then called with the  parameters from the
request, and the return value is passed  back to the client.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler.register_introspection_functions">
<span class="sig-prename descclassname"><span class="pre">CGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">register_introspection_functions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_introspection_functions" title="Link to this definition">¶</a></dt>
<dd><p>Register the XML-RPC introspection functions  <code class="docutils literal notranslate"><span class="pre">system.listMethods</span></code>,
<code class="docutils literal notranslate"><span class="pre">system.methodHelp</span></code> and  <code class="docutils literal notranslate"><span class="pre">system.methodSignature</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler.register_multicall_functions">
<span class="sig-prename descclassname"><span class="pre">CGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">register_multicall_functions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler.register_multicall_functions" title="Link to this definition">¶</a></dt>
<dd><p>Register the XML-RPC multicall function <code class="docutils literal notranslate"><span class="pre">system.multicall</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.CGIXMLRPCRequestHandler.handle_request">
<span class="sig-prename descclassname"><span class="pre">CGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">handle_request</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request_text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.CGIXMLRPCRequestHandler.handle_request" title="Link to this definition">¶</a></dt>
<dd><p>Handle an XML-RPC request. If <em>request_text</em> is given, it should be the POST
data provided by the HTTP server,  otherwise the contents of stdin will be used.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyFuncs</span><span class="p">:</span>
    <span class="k">def</span> <span class="nf">mul</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="n">y</span>


<span class="n">handler</span> <span class="o">=</span> <span class="n">CGIXMLRPCRequestHandler</span><span class="p">()</span>
<span class="n">handler</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span>
<span class="n">handler</span><span class="o">.</span><span class="n">register_function</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">:</span> <span class="n">x</span><span class="o">+</span><span class="n">y</span><span class="p">,</span> <span class="s1">&#39;add&#39;</span><span class="p">)</span>
<span class="n">handler</span><span class="o">.</span><span class="n">register_introspection_functions</span><span class="p">()</span>
<span class="n">handler</span><span class="o">.</span><span class="n">register_instance</span><span class="p">(</span><span class="n">MyFuncs</span><span class="p">())</span>
<span class="n">handler</span><span class="o">.</span><span class="n">handle_request</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="documenting-xmlrpc-server">
<h2>Documenting XMLRPC server<a class="headerlink" href="#documenting-xmlrpc-server" title="Link to this heading">¶</a></h2>
<p>These classes extend the above classes to serve HTML documentation in response
to HTTP GET requests.  Servers can either be free standing, using
<a class="reference internal" href="#xmlrpc.server.DocXMLRPCServer" title="xmlrpc.server.DocXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocXMLRPCServer</span></code></a>, or embedded in a CGI environment, using
<a class="reference internal" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler" title="xmlrpc.server.DocCGIXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocCGIXMLRPCRequestHandler</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.DocXMLRPCServer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">DocXMLRPCServer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">addr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">requestHandler</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DocXMLRPCRequestHandler</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">logRequests</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_none</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bind_and_activate</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use_builtin_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocXMLRPCServer" title="Link to this definition">¶</a></dt>
<dd><p>Create a new server instance. All parameters have the same meaning as for
<a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer" title="xmlrpc.server.SimpleXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCServer</span></code></a>; <em>requestHandler</em> defaults to
<a class="reference internal" href="#xmlrpc.server.DocXMLRPCRequestHandler" title="xmlrpc.server.DocXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocXMLRPCRequestHandler</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <em>use_builtin_types</em> flag was added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.DocCGIXMLRPCRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">DocCGIXMLRPCRequestHandler</span></span><a class="headerlink" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>Create a new instance to handle XML-RPC requests in a CGI environment.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xmlrpc.server.DocXMLRPCRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xmlrpc.server.</span></span><span class="sig-name descname"><span class="pre">DocXMLRPCRequestHandler</span></span><a class="headerlink" href="#xmlrpc.server.DocXMLRPCRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>Create a new request handler instance. This request handler supports XML-RPC
POST requests, documentation GET requests, and modifies logging so that the
<em>logRequests</em> parameter to the <a class="reference internal" href="#xmlrpc.server.DocXMLRPCServer" title="xmlrpc.server.DocXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocXMLRPCServer</span></code></a> constructor parameter is
honored.</p>
</dd></dl>

</section>
<section id="docxmlrpcserver-objects">
<span id="doc-xmlrpc-servers"></span><h2>DocXMLRPCServer Objects<a class="headerlink" href="#docxmlrpcserver-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xmlrpc.server.DocXMLRPCServer" title="xmlrpc.server.DocXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocXMLRPCServer</span></code></a> class is derived from <a class="reference internal" href="#xmlrpc.server.SimpleXMLRPCServer" title="xmlrpc.server.SimpleXMLRPCServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleXMLRPCServer</span></code></a>
and provides a means of creating self-documenting, stand alone XML-RPC
servers. HTTP POST requests are handled as XML-RPC method calls. HTTP GET
requests are handled by generating pydoc-style HTML documentation. This allows a
server to provide its own web-based documentation.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocXMLRPCServer.set_server_title">
<span class="sig-prename descclassname"><span class="pre">DocXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">set_server_title</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_title</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocXMLRPCServer.set_server_title" title="Link to this definition">¶</a></dt>
<dd><p>Set the title used in the generated HTML documentation. This title will be used
inside the HTML “title” element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocXMLRPCServer.set_server_name">
<span class="sig-prename descclassname"><span class="pre">DocXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">set_server_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocXMLRPCServer.set_server_name" title="Link to this definition">¶</a></dt>
<dd><p>Set the name used in the generated HTML documentation. This name will appear at
the top of the generated documentation inside a “h1” element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocXMLRPCServer.set_server_documentation">
<span class="sig-prename descclassname"><span class="pre">DocXMLRPCServer.</span></span><span class="sig-name descname"><span class="pre">set_server_documentation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_documentation</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocXMLRPCServer.set_server_documentation" title="Link to this definition">¶</a></dt>
<dd><p>Set the description used in the generated HTML documentation. This description
will appear as a paragraph, below the server name, in the documentation.</p>
</dd></dl>

</section>
<section id="doccgixmlrpcrequesthandler">
<h2>DocCGIXMLRPCRequestHandler<a class="headerlink" href="#doccgixmlrpcrequesthandler" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler" title="xmlrpc.server.DocCGIXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DocCGIXMLRPCRequestHandler</span></code></a> class is derived from
<a class="reference internal" href="#xmlrpc.server.CGIXMLRPCRequestHandler" title="xmlrpc.server.CGIXMLRPCRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIXMLRPCRequestHandler</span></code></a> and provides a means of creating
self-documenting, XML-RPC CGI scripts. HTTP POST requests are handled as XML-RPC
method calls. HTTP GET requests are handled by generating pydoc-style HTML
documentation. This allows a server to provide its own web-based documentation.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_title">
<span class="sig-prename descclassname"><span class="pre">DocCGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">set_server_title</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_title</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_title" title="Link to this definition">¶</a></dt>
<dd><p>Set the title used in the generated HTML documentation. This title will be used
inside the HTML “title” element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_name">
<span class="sig-prename descclassname"><span class="pre">DocCGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">set_server_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_name" title="Link to this definition">¶</a></dt>
<dd><p>Set the name used in the generated HTML documentation. This name will appear at
the top of the generated documentation inside a “h1” element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_documentation">
<span class="sig-prename descclassname"><span class="pre">DocCGIXMLRPCRequestHandler.</span></span><span class="sig-name descname"><span class="pre">set_server_documentation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_documentation</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xmlrpc.server.DocCGIXMLRPCRequestHandler.set_server_documentation" title="Link to this definition">¶</a></dt>
<dd><p>Set the description used in the generated HTML documentation. This description
will appear as a paragraph, below the server name, in the documentation.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a><ul>
<li><a class="reference internal" href="#simplexmlrpcserver-objects">SimpleXMLRPCServer Objects</a><ul>
<li><a class="reference internal" href="#simplexmlrpcserver-example">SimpleXMLRPCServer Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#cgixmlrpcrequesthandler">CGIXMLRPCRequestHandler</a></li>
<li><a class="reference internal" href="#documenting-xmlrpc-server">Documenting XMLRPC server</a></li>
<li><a class="reference internal" href="#docxmlrpcserver-objects">DocXMLRPCServer Objects</a></li>
<li><a class="reference internal" href="#doccgixmlrpcrequesthandler">DocCGIXMLRPCRequestHandler</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.client.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.client</span></code> — XML-RPC client access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ipaddress.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xmlrpc.server.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="ipaddress.html" title="ipaddress — IPv4/IPv6 manipulation library"
             >next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.client.html" title="xmlrpc.client — XML-RPC client access"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>