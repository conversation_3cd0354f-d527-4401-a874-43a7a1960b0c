<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-inherit="mail.ActivityMenu" t-inherit-mode="extension">
        <xpath expr="//*[@name='activityTitle']" position="after">
            <div t-if="group.type === 'meeting'">
                <t t-set="is_next_meeting" t-value="true"/>
                <t t-foreach="group.meetings" t-as="meeting" t-key="meeting.id">
                    <div class="o-calendar-meeting d-flex px-2">
                        <span class="flex-grow-1 text-truncate" t-att-class="!meeting.allday and is_next_meeting ? 'fw-bold' : ''" t-esc="meeting.name"/>
                        <span t-if="meeting.formattedStart">
                            <t t-if="meeting.allday">All Day</t>
                            <t t-else=''>
                                <t t-set="is_next_meeting" t-value="false"/>
                                <t t-esc="meeting.formattedStart"/>
                            </t>
                        </span>
                    </div>
                </t>
            </div>
        </xpath>
    </t>
</templates>
