<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="product.ProductDocumentKanbanView.Buttons" t-inherit="web.KanbanView.Buttons" t-inherit-mode="primary">
        <xpath expr="//div[hasclass('o_cp_buttons')]" position="inside">
            <input type="file" multiple="true" t-ref="uploadFileInput" class="o_input_file o_hidden" t-on-change.stop="onFileInputChange"/>
            <button type="button" t-attf-class="btn btn-primary o_mrp_documents_kanban_upload"
                t-on-click.stop.prevent="() => this.uploadFileInputRef.el.click()">
                Upload
            </button>
        </xpath>
    </t>
</templates>
