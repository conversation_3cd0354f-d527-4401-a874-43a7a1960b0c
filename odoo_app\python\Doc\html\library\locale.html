<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="locale — Internationalization services" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/locale.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/locale.py The locale module opens access to the POSIX locale database and functionality. The POSIX locale mechanism allows programmers to deal with certain cultural issues in an ap..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/locale.py The locale module opens access to the POSIX locale database and functionality. The POSIX locale mechanism allows programmers to deal with certain cultural issues in an ap..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>locale — Internationalization services &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Program Frameworks" href="frameworks.html" />
    <link rel="prev" title="gettext — Multilingual internationalization services" href="gettext.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/locale.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a><ul>
<li><a class="reference internal" href="#background-details-hints-tips-and-caveats">Background, details, hints, tips and caveats</a></li>
<li><a class="reference internal" href="#for-extension-writers-and-programs-that-embed-python">For extension writers and programs that embed Python</a></li>
<li><a class="reference internal" href="#access-to-message-catalogs">Access to message catalogs</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gettext.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="frameworks.html"
                          title="next chapter">Program Frameworks</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/locale.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="frameworks.html" title="Program Frameworks"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="gettext.html" title="gettext — Multilingual internationalization services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="i18n.html" accesskey="U">Internationalization</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-locale">
<span id="locale-internationalization-services"></span><h1><a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> — Internationalization services<a class="headerlink" href="#module-locale" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/locale.py">Lib/locale.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module opens access to the POSIX locale database and
functionality. The POSIX locale mechanism allows programmers to deal with
certain cultural issues in an application, without requiring the programmer to
know all the specifics of each country where the software is executed.</p>
<p id="index-0">The <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module is implemented on top of the <code class="xref py py-mod docutils literal notranslate"><span class="pre">_locale</span></code> module,
which in turn uses an ANSI C locale implementation if available.</p>
<p>The <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module defines the following exception and functions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="locale.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#locale.Error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when the locale passed to <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> is not
recognized.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.setlocale">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">setlocale</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locale</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.setlocale" title="Link to this definition">¶</a></dt>
<dd><p>If <em>locale</em> is given and not <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> modifies the locale
setting for the <em>category</em>. The available categories are listed in the data
description below. <em>locale</em> may be a string, or an iterable of two strings
(language code and encoding). If it’s an iterable, it’s converted to a locale
name using the locale aliasing engine. An empty string specifies the user’s
default settings. If the modification of the locale fails, the exception
<a class="reference internal" href="#locale.Error" title="locale.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a> is raised. If successful, the new locale setting is returned.</p>
<p>If <em>locale</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, the current setting for <em>category</em> is
returned.</p>
<p><a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> is not thread-safe on most systems. Applications typically
start with a call of</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">locale</span>
<span class="n">locale</span><span class="o">.</span><span class="n">setlocale</span><span class="p">(</span><span class="n">locale</span><span class="o">.</span><span class="n">LC_ALL</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>This sets the locale for all categories to the user’s default setting (typically
specified in the <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANG</span></code> environment variable).  If the locale is not
changed thereafter, using multithreading should not cause problems.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.localeconv">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">localeconv</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#locale.localeconv" title="Link to this definition">¶</a></dt>
<dd><p>Returns the database of the local conventions as a dictionary. This dictionary
has the following strings as keys:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Category</p></th>
<th class="head"><p>Key</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'decimal_point'</span></code></p></td>
<td><p>Decimal point character.</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'grouping'</span></code></p></td>
<td><p>Sequence of numbers specifying
which relative positions the
<code class="docutils literal notranslate"><span class="pre">'thousands_sep'</span></code> is
expected.  If the sequence is
terminated with
<a class="reference internal" href="#locale.CHAR_MAX" title="locale.CHAR_MAX"><code class="xref py py-const docutils literal notranslate"><span class="pre">CHAR_MAX</span></code></a>, no further
grouping is performed. If the
sequence terminates with a
<code class="docutils literal notranslate"><span class="pre">0</span></code>,  the last group size is
repeatedly used.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'thousands_sep'</span></code></p></td>
<td><p>Character used between groups.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#locale.LC_MONETARY" title="locale.LC_MONETARY"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_MONETARY</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'int_curr_symbol'</span></code></p></td>
<td><p>International currency symbol.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'currency_symbol'</span></code></p></td>
<td><p>Local currency symbol.</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'p_cs_precedes/n_cs_precedes'</span></code></p></td>
<td><p>Whether the currency symbol
precedes the value (for
positive resp. negative
values).</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'p_sep_by_space/n_sep_by_space'</span></code></p></td>
<td><p>Whether the currency symbol is
separated from the value  by a
space (for positive resp.
negative values).</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'mon_decimal_point'</span></code></p></td>
<td><p>Decimal point used for
monetary values.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'frac_digits'</span></code></p></td>
<td><p>Number of fractional digits
used in local formatting of
monetary values.</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'int_frac_digits'</span></code></p></td>
<td><p>Number of fractional digits
used in international
formatting of monetary values.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'mon_thousands_sep'</span></code></p></td>
<td><p>Group separator used for
monetary values.</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'mon_grouping'</span></code></p></td>
<td><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">'grouping'</span></code>,
used for monetary values.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'positive_sign'</span></code></p></td>
<td><p>Symbol used to annotate a
positive monetary value.</p></td>
</tr>
<tr class="row-odd"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'negative_sign'</span></code></p></td>
<td><p>Symbol used to annotate a
negative monetary value.</p></td>
</tr>
<tr class="row-even"><td></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'p_sign_posn/n_sign_posn'</span></code></p></td>
<td><p>The position of the sign (for
positive resp. negative
values), see below.</p></td>
</tr>
</tbody>
</table>
<p>All numeric values can be set to <a class="reference internal" href="#locale.CHAR_MAX" title="locale.CHAR_MAX"><code class="xref py py-const docutils literal notranslate"><span class="pre">CHAR_MAX</span></code></a> to indicate that there is no
value specified in this locale.</p>
<p>The possible values for <code class="docutils literal notranslate"><span class="pre">'p_sign_posn'</span></code> and <code class="docutils literal notranslate"><span class="pre">'n_sign_posn'</span></code> are given below.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value</p></th>
<th class="head"><p>Explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p>Currency and value are surrounded by
parentheses.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p>The sign should precede the value and
currency symbol.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p>The sign should follow the value and
currency symbol.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">3</span></code></p></td>
<td><p>The sign should immediately precede the
value.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>The sign should immediately follow the
value.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">CHAR_MAX</span></code></p></td>
<td><p>Nothing is specified in this locale.</p></td>
</tr>
</tbody>
</table>
<p>The function temporarily sets the <code class="docutils literal notranslate"><span class="pre">LC_CTYPE</span></code> locale to the <code class="docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code>
locale or the <code class="docutils literal notranslate"><span class="pre">LC_MONETARY</span></code> locale if locales are different and numeric or
monetary strings are non-ASCII. This temporary change affects other threads.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The function now temporarily sets the <code class="docutils literal notranslate"><span class="pre">LC_CTYPE</span></code> locale to the
<code class="docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code> locale in some cases.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.nl_langinfo">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">nl_langinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.nl_langinfo" title="Link to this definition">¶</a></dt>
<dd><p>Return some locale-specific information as a string.  This function is not
available on all systems, and the set of possible options might also vary
across platforms.  The possible argument values are numbers, for which
symbolic constants are available in the locale module.</p>
<p>The <a class="reference internal" href="#locale.nl_langinfo" title="locale.nl_langinfo"><code class="xref py py-func docutils literal notranslate"><span class="pre">nl_langinfo()</span></code></a> function accepts one of the following keys.  Most
descriptions are taken from the corresponding description in the GNU C
library.</p>
<dl class="py data">
<dt class="sig sig-object py" id="locale.CODESET">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">CODESET</span></span><a class="headerlink" href="#locale.CODESET" title="Link to this definition">¶</a></dt>
<dd><p>Get a string with the name of the character encoding used in the
selected locale.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.D_T_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">D_T_FMT</span></span><a class="headerlink" href="#locale.D_T_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a string that can be used as a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to
represent date and time in a locale-specific way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.D_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">D_FMT</span></span><a class="headerlink" href="#locale.D_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a string that can be used as a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to
represent a date in a locale-specific way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.T_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">T_FMT</span></span><a class="headerlink" href="#locale.T_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a string that can be used as a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to
represent a time in a locale-specific way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.T_FMT_AMPM">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">T_FMT_AMPM</span></span><a class="headerlink" href="#locale.T_FMT_AMPM" title="Link to this definition">¶</a></dt>
<dd><p>Get a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to represent time in the am/pm
format.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.DAY_1">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_1</span></span><a class="headerlink" href="#locale.DAY_1" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_2">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_2</span></span><a class="headerlink" href="#locale.DAY_2" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_3">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_3</span></span><a class="headerlink" href="#locale.DAY_3" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_4">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_4</span></span><a class="headerlink" href="#locale.DAY_4" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_5">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_5</span></span><a class="headerlink" href="#locale.DAY_5" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_6">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_6</span></span><a class="headerlink" href="#locale.DAY_6" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.DAY_7">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">DAY_7</span></span><a class="headerlink" href="#locale.DAY_7" title="Link to this definition">¶</a></dt>
<dd><p>Get the name of the n-th day of the week.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This follows the US convention of <a class="reference internal" href="#locale.DAY_1" title="locale.DAY_1"><code class="xref py py-const docutils literal notranslate"><span class="pre">DAY_1</span></code></a> being Sunday, not the
international convention (ISO 8601) that Monday is the first day of the
week.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ABDAY_1">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_1</span></span><a class="headerlink" href="#locale.ABDAY_1" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_2">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_2</span></span><a class="headerlink" href="#locale.ABDAY_2" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_3">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_3</span></span><a class="headerlink" href="#locale.ABDAY_3" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_4">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_4</span></span><a class="headerlink" href="#locale.ABDAY_4" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_5">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_5</span></span><a class="headerlink" href="#locale.ABDAY_5" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_6">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_6</span></span><a class="headerlink" href="#locale.ABDAY_6" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABDAY_7">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABDAY_7</span></span><a class="headerlink" href="#locale.ABDAY_7" title="Link to this definition">¶</a></dt>
<dd><p>Get the abbreviated name of the n-th day of the week.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.MON_1">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_1</span></span><a class="headerlink" href="#locale.MON_1" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_2">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_2</span></span><a class="headerlink" href="#locale.MON_2" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_3">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_3</span></span><a class="headerlink" href="#locale.MON_3" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_4">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_4</span></span><a class="headerlink" href="#locale.MON_4" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_5">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_5</span></span><a class="headerlink" href="#locale.MON_5" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_6">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_6</span></span><a class="headerlink" href="#locale.MON_6" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_7">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_7</span></span><a class="headerlink" href="#locale.MON_7" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_8">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_8</span></span><a class="headerlink" href="#locale.MON_8" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_9">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_9</span></span><a class="headerlink" href="#locale.MON_9" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_10">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_10</span></span><a class="headerlink" href="#locale.MON_10" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_11">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_11</span></span><a class="headerlink" href="#locale.MON_11" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.MON_12">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">MON_12</span></span><a class="headerlink" href="#locale.MON_12" title="Link to this definition">¶</a></dt>
<dd><p>Get the name of the n-th month.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ABMON_1">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_1</span></span><a class="headerlink" href="#locale.ABMON_1" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_2">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_2</span></span><a class="headerlink" href="#locale.ABMON_2" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_3">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_3</span></span><a class="headerlink" href="#locale.ABMON_3" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_4">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_4</span></span><a class="headerlink" href="#locale.ABMON_4" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_5">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_5</span></span><a class="headerlink" href="#locale.ABMON_5" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_6">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_6</span></span><a class="headerlink" href="#locale.ABMON_6" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_7">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_7</span></span><a class="headerlink" href="#locale.ABMON_7" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_8">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_8</span></span><a class="headerlink" href="#locale.ABMON_8" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_9">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_9</span></span><a class="headerlink" href="#locale.ABMON_9" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_10">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_10</span></span><a class="headerlink" href="#locale.ABMON_10" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_11">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_11</span></span><a class="headerlink" href="#locale.ABMON_11" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="locale.ABMON_12">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ABMON_12</span></span><a class="headerlink" href="#locale.ABMON_12" title="Link to this definition">¶</a></dt>
<dd><p>Get the abbreviated name of the n-th month.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.RADIXCHAR">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">RADIXCHAR</span></span><a class="headerlink" href="#locale.RADIXCHAR" title="Link to this definition">¶</a></dt>
<dd><p>Get the radix character (decimal dot, decimal comma, etc.).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.THOUSEP">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">THOUSEP</span></span><a class="headerlink" href="#locale.THOUSEP" title="Link to this definition">¶</a></dt>
<dd><p>Get the separator character for thousands (groups of three digits).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.YESEXPR">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">YESEXPR</span></span><a class="headerlink" href="#locale.YESEXPR" title="Link to this definition">¶</a></dt>
<dd><p>Get a regular expression that can be used with the regex function to
recognize a positive response to a yes/no question.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.NOEXPR">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">NOEXPR</span></span><a class="headerlink" href="#locale.NOEXPR" title="Link to this definition">¶</a></dt>
<dd><p>Get a regular expression that can be used with the <code class="docutils literal notranslate"><span class="pre">regex(3)</span></code> function to
recognize a negative response to a yes/no question.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The regular expressions for <a class="reference internal" href="#locale.YESEXPR" title="locale.YESEXPR"><code class="xref py py-const docutils literal notranslate"><span class="pre">YESEXPR</span></code></a> and
<a class="reference internal" href="#locale.NOEXPR" title="locale.NOEXPR"><code class="xref py py-const docutils literal notranslate"><span class="pre">NOEXPR</span></code></a> use syntax suitable for the
<code class="docutils literal notranslate"><span class="pre">regex</span></code> function from the C library, which might
differ from the syntax used in <a class="reference internal" href="re.html#module-re" title="re: Regular expression operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">re</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.CRNCYSTR">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">CRNCYSTR</span></span><a class="headerlink" href="#locale.CRNCYSTR" title="Link to this definition">¶</a></dt>
<dd><p>Get the currency symbol, preceded by “-” if the symbol should appear before
the value, “+” if the symbol should appear after the value, or “.” if the
symbol should replace the radix character.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ERA">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ERA</span></span><a class="headerlink" href="#locale.ERA" title="Link to this definition">¶</a></dt>
<dd><p>Get a string that represents the era used in the current locale.</p>
<p>Most locales do not define this value.  An example of a locale which does
define this value is the Japanese one.  In Japan, the traditional
representation of dates includes the name of the era corresponding to the
then-emperor’s reign.</p>
<p>Normally it should not be necessary to use this value directly. Specifying
the <code class="docutils literal notranslate"><span class="pre">E</span></code> modifier in their format strings causes the <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>
function to use this information.  The format of the returned string is not
specified, and therefore you should not assume knowledge of it on different
systems.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ERA_D_T_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ERA_D_T_FMT</span></span><a class="headerlink" href="#locale.ERA_D_T_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to represent date and time in a
locale-specific era-based way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ERA_D_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ERA_D_FMT</span></span><a class="headerlink" href="#locale.ERA_D_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to represent a date in a
locale-specific era-based way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ERA_T_FMT">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ERA_T_FMT</span></span><a class="headerlink" href="#locale.ERA_T_FMT" title="Link to this definition">¶</a></dt>
<dd><p>Get a format string for <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a> to represent a time in a
locale-specific era-based way.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.ALT_DIGITS">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">ALT_DIGITS</span></span><a class="headerlink" href="#locale.ALT_DIGITS" title="Link to this definition">¶</a></dt>
<dd><p>Get a representation of up to 100 values used to represent the values
0 to 99.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.getdefaultlocale">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">getdefaultlocale</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">envvars</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#locale.getdefaultlocale" title="Link to this definition">¶</a></dt>
<dd><p>Tries to determine the default locale settings and returns them as a tuple of
the form <code class="docutils literal notranslate"><span class="pre">(language</span> <span class="pre">code,</span> <span class="pre">encoding)</span></code>.</p>
<p>According to POSIX, a program which has not called <code class="docutils literal notranslate"><span class="pre">setlocale(LC_ALL,</span> <span class="pre">'')</span></code>
runs using the portable <code class="docutils literal notranslate"><span class="pre">'C'</span></code> locale.  Calling <code class="docutils literal notranslate"><span class="pre">setlocale(LC_ALL,</span> <span class="pre">'')</span></code> lets
it use the default locale as defined by the <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANG</span></code> variable.  Since we
do not want to interfere with the current locale setting we thus emulate the
behavior in the way described above.</p>
<p>To maintain compatibility with other platforms, not only the <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANG</span></code>
variable is tested, but a list of variables given as envvars parameter.  The
first found to be defined will be used.  <em>envvars</em> defaults to the search
path used in GNU gettext; it must always contain the variable name
<code class="docutils literal notranslate"><span class="pre">'LANG'</span></code>.  The GNU gettext search path contains <code class="docutils literal notranslate"><span class="pre">'LC_ALL'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'LC_CTYPE'</span></code>, <code class="docutils literal notranslate"><span class="pre">'LANG'</span></code> and <code class="docutils literal notranslate"><span class="pre">'LANGUAGE'</span></code>, in that order.</p>
<p>Except for the code <code class="docutils literal notranslate"><span class="pre">'C'</span></code>, the language code corresponds to <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1766.html"><strong>RFC 1766</strong></a>.
<em>language code</em> and <em>encoding</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code> if their values cannot be
determined.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.15.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.getlocale">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">getlocale</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">LC_CTYPE</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.getlocale" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current setting for the given locale category as sequence containing
<em>language code</em>, <em>encoding</em>. <em>category</em> may be one of the <code class="xref py py-const docutils literal notranslate"><span class="pre">LC_*</span></code> values
except <a class="reference internal" href="#locale.LC_ALL" title="locale.LC_ALL"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_ALL</span></code></a>.  It defaults to <a class="reference internal" href="#locale.LC_CTYPE" title="locale.LC_CTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_CTYPE</span></code></a>.</p>
<p>Except for the code <code class="docutils literal notranslate"><span class="pre">'C'</span></code>, the language code corresponds to <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1766.html"><strong>RFC 1766</strong></a>.
<em>language code</em> and <em>encoding</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code> if their values cannot be
determined.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.getpreferredencoding">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">getpreferredencoding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">do_setlocale</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.getpreferredencoding" title="Link to this definition">¶</a></dt>
<dd><p>Return the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a> used for text data, according to user
preferences.  User preferences are expressed differently on different
systems, and might not be available programmatically on some systems, so
this function only returns a guess.</p>
<p>On some systems, it is necessary to invoke <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> to obtain the
user preferences, so this function is not thread-safe. If invoking setlocale
is not necessary or desired, <em>do_setlocale</em> should be set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<p>On Android or if the <a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a> is enabled, always
return <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code>, the <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a> and the <em>do_setlocale</em>
argument are ignored.</p>
<p>The <a class="reference internal" href="../c-api/init_config.html#c-preinit"><span class="std std-ref">Python preinitialization</span></a> configures the LC_CTYPE
locale. See also the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The function now always returns <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> on Android or if the
<a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a> is enabled.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.getencoding">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">getencoding</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#locale.getencoding" title="Link to this definition">¶</a></dt>
<dd><p>Get the current <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a>:</p>
<ul class="simple">
<li><p>On Android and VxWorks, return <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code>.</p></li>
<li><p>On Unix, return the encoding of the current <a class="reference internal" href="#locale.LC_CTYPE" title="locale.LC_CTYPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">LC_CTYPE</span></code></a> locale.
Return <code class="docutils literal notranslate"><span class="pre">&quot;utf-8&quot;</span></code> if <code class="docutils literal notranslate"><span class="pre">nl_langinfo(CODESET)</span></code> returns an empty string:
for example, if the current LC_CTYPE locale is not supported.</p></li>
<li><p>On Windows, return the ANSI code page.</p></li>
</ul>
<p>The <a class="reference internal" href="../c-api/init_config.html#c-preinit"><span class="std std-ref">Python preinitialization</span></a> configures the LC_CTYPE
locale. See also the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.</p>
<p>This function is similar to
<a class="reference internal" href="#locale.getpreferredencoding" title="locale.getpreferredencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">getpreferredencoding(False)</span></code></a> except this
function ignores the <a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.normalize">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">localename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.normalize" title="Link to this definition">¶</a></dt>
<dd><p>Returns a normalized locale code for the given locale name.  The returned locale
code is formatted for use with <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a>.  If normalization fails, the
original name is returned unchanged.</p>
<p>If the given encoding is not known, the function defaults to the default
encoding for the locale code just like <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.resetlocale">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">resetlocale</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">LC_ALL</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.resetlocale" title="Link to this definition">¶</a></dt>
<dd><p>Sets the locale for <em>category</em> to the default setting.</p>
<p>The default setting is determined by calling <a class="reference internal" href="#locale.getdefaultlocale" title="locale.getdefaultlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">getdefaultlocale()</span></code></a>.
<em>category</em> defaults to <a class="reference internal" href="#locale.LC_ALL" title="locale.LC_ALL"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_ALL</span></code></a>.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.strcoll">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">strcoll</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">string2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.strcoll" title="Link to this definition">¶</a></dt>
<dd><p>Compares two strings according to the current <a class="reference internal" href="#locale.LC_COLLATE" title="locale.LC_COLLATE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_COLLATE</span></code></a> setting. As
any other compare function, returns a negative, or a positive value, or <code class="docutils literal notranslate"><span class="pre">0</span></code>,
depending on whether <em>string1</em> collates before or after <em>string2</em> or is equal to
it.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.strxfrm">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">strxfrm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.strxfrm" title="Link to this definition">¶</a></dt>
<dd><p>Transforms a string to one that can be used in locale-aware
comparisons.  For example, <code class="docutils literal notranslate"><span class="pre">strxfrm(s1)</span> <span class="pre">&lt;</span> <span class="pre">strxfrm(s2)</span></code> is
equivalent to <code class="docutils literal notranslate"><span class="pre">strcoll(s1,</span> <span class="pre">s2)</span> <span class="pre">&lt;</span> <span class="pre">0</span></code>.  This function can be used
when the same string is compared repeatedly, e.g. when collating a
sequence of strings.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.format_string">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">format_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grouping</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">monetary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.format_string" title="Link to this definition">¶</a></dt>
<dd><p>Formats a number <em>val</em> according to the current <a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a> setting.
The format follows the conventions of the <code class="docutils literal notranslate"><span class="pre">%</span></code> operator.  For floating point
values, the decimal point is modified if appropriate.  If <em>grouping</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>,
also takes the grouping into account.</p>
<p>If <em>monetary</em> is true, the conversion uses monetary thousands separator and
grouping strings.</p>
<p>Processes formatting specifiers as in <code class="docutils literal notranslate"><span class="pre">format</span> <span class="pre">%</span> <span class="pre">val</span></code>, but takes the current
locale settings into account.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <em>monetary</em> keyword parameter was added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.currency">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">currency</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">val</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symbol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grouping</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">international</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.currency" title="Link to this definition">¶</a></dt>
<dd><p>Formats a number <em>val</em> according to the current <a class="reference internal" href="#locale.LC_MONETARY" title="locale.LC_MONETARY"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_MONETARY</span></code></a> settings.</p>
<p>The returned string includes the currency symbol if <em>symbol</em> is true, which is
the default. If <em>grouping</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (which is not the default), grouping is done
with the value. If <em>international</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (which is not the default), the
international currency symbol is used.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function will not work with the ‘C’ locale, so you have to set a
locale via <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> first.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.str">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">str</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">float</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.str" title="Link to this definition">¶</a></dt>
<dd><p>Formats a floating point number using the same format as the built-in function
<code class="docutils literal notranslate"><span class="pre">str(float)</span></code>, but takes the decimal point into account.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.delocalize">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">delocalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.delocalize" title="Link to this definition">¶</a></dt>
<dd><p>Converts a string into a normalized number string, following the
<a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a> settings.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.localize">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">localize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">grouping</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">monetary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.localize" title="Link to this definition">¶</a></dt>
<dd><p>Converts a normalized number string into a formatted string following the
<a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a> settings.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.atof">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">atof</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">float</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.atof" title="Link to this definition">¶</a></dt>
<dd><p>Converts a string to a number, following the <a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a> settings,
by calling <em>func</em> on the result of calling <a class="reference internal" href="#locale.delocalize" title="locale.delocalize"><code class="xref py py-func docutils literal notranslate"><span class="pre">delocalize()</span></code></a> on <em>string</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.atoi">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">atoi</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.atoi" title="Link to this definition">¶</a></dt>
<dd><p>Converts a string to an integer, following the <a class="reference internal" href="#locale.LC_NUMERIC" title="locale.LC_NUMERIC"><code class="xref py py-const docutils literal notranslate"><span class="pre">LC_NUMERIC</span></code></a> conventions.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_CTYPE">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_CTYPE</span></span><a class="headerlink" href="#locale.LC_CTYPE" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for the character type functions.  Most importantly, this
category defines the text encoding, i.e. how bytes are interpreted as
Unicode codepoints.  See <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0538/"><strong>PEP 538</strong></a> and <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0540/"><strong>PEP 540</strong></a> for how this variable
might be automatically coerced to <code class="docutils literal notranslate"><span class="pre">C.UTF-8</span></code> to avoid issues created by
invalid settings in containers or incompatible settings passed over remote
SSH connections.</p>
<p>Python doesn’t internally use locale-dependent character transformation functions
from <code class="docutils literal notranslate"><span class="pre">ctype.h</span></code>. Instead, an internal <code class="docutils literal notranslate"><span class="pre">pyctype.h</span></code> provides locale-independent
equivalents like <code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_TOLOWER</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_COLLATE">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_COLLATE</span></span><a class="headerlink" href="#locale.LC_COLLATE" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for sorting strings.  The functions <a class="reference internal" href="#locale.strcoll" title="locale.strcoll"><code class="xref py py-func docutils literal notranslate"><span class="pre">strcoll()</span></code></a> and
<a class="reference internal" href="#locale.strxfrm" title="locale.strxfrm"><code class="xref py py-func docutils literal notranslate"><span class="pre">strxfrm()</span></code></a> of the <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module are affected.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_TIME">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_TIME</span></span><a class="headerlink" href="#locale.LC_TIME" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for the formatting of time.  The function <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>
follows these conventions.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_MONETARY">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_MONETARY</span></span><a class="headerlink" href="#locale.LC_MONETARY" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for formatting of monetary values.  The available options are
available from the <a class="reference internal" href="#locale.localeconv" title="locale.localeconv"><code class="xref py py-func docutils literal notranslate"><span class="pre">localeconv()</span></code></a> function.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_MESSAGES">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_MESSAGES</span></span><a class="headerlink" href="#locale.LC_MESSAGES" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for message display. Python currently does not support
application specific locale-aware messages.  Messages displayed by the operating
system, like those returned by <a class="reference internal" href="os.html#os.strerror" title="os.strerror"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.strerror()</span></code></a> might be affected by this
category.</p>
<p>This value may not be available on operating systems not conforming to the
POSIX standard, most notably Windows.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_NUMERIC">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_NUMERIC</span></span><a class="headerlink" href="#locale.LC_NUMERIC" title="Link to this definition">¶</a></dt>
<dd><p>Locale category for formatting numbers.  The functions <a class="reference internal" href="#locale.format_string" title="locale.format_string"><code class="xref py py-func docutils literal notranslate"><span class="pre">format_string()</span></code></a>,
<a class="reference internal" href="#locale.atoi" title="locale.atoi"><code class="xref py py-func docutils literal notranslate"><span class="pre">atoi()</span></code></a>, <a class="reference internal" href="#locale.atof" title="locale.atof"><code class="xref py py-func docutils literal notranslate"><span class="pre">atof()</span></code></a> and <a class="reference internal" href="#locale.str" title="locale.str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> of the <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module are
affected by that category.  All other numeric formatting operations are not
affected.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.LC_ALL">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">LC_ALL</span></span><a class="headerlink" href="#locale.LC_ALL" title="Link to this definition">¶</a></dt>
<dd><p>Combination of all locale settings.  If this flag is used when the locale is
changed, setting the locale for all categories is attempted. If that fails for
any category, no category is changed at all.  When the locale is retrieved using
this flag, a string indicating the setting for all categories is returned. This
string can be later used to restore the settings.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="locale.CHAR_MAX">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">CHAR_MAX</span></span><a class="headerlink" href="#locale.CHAR_MAX" title="Link to this definition">¶</a></dt>
<dd><p>This is a symbolic constant used for different values returned by
<a class="reference internal" href="#locale.localeconv" title="locale.localeconv"><code class="xref py py-func docutils literal notranslate"><span class="pre">localeconv()</span></code></a>.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">locale</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">loc</span> <span class="o">=</span> <span class="n">locale</span><span class="o">.</span><span class="n">getlocale</span><span class="p">()</span>  <span class="c1"># get current locale</span>
<span class="go"># use German locale; name might vary with platform</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">locale</span><span class="o">.</span><span class="n">setlocale</span><span class="p">(</span><span class="n">locale</span><span class="o">.</span><span class="n">LC_ALL</span><span class="p">,</span> <span class="s1">&#39;de_DE&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">locale</span><span class="o">.</span><span class="n">strcoll</span><span class="p">(</span><span class="s1">&#39;f</span><span class="se">\xe4</span><span class="s1">n&#39;</span><span class="p">,</span> <span class="s1">&#39;foo&#39;</span><span class="p">)</span>  <span class="c1"># compare a string containing an umlaut</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">locale</span><span class="o">.</span><span class="n">setlocale</span><span class="p">(</span><span class="n">locale</span><span class="o">.</span><span class="n">LC_ALL</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">)</span>   <span class="c1"># use user&#39;s preferred locale</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">locale</span><span class="o">.</span><span class="n">setlocale</span><span class="p">(</span><span class="n">locale</span><span class="o">.</span><span class="n">LC_ALL</span><span class="p">,</span> <span class="s1">&#39;C&#39;</span><span class="p">)</span>  <span class="c1"># use default (C) locale</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">locale</span><span class="o">.</span><span class="n">setlocale</span><span class="p">(</span><span class="n">locale</span><span class="o">.</span><span class="n">LC_ALL</span><span class="p">,</span> <span class="n">loc</span><span class="p">)</span>  <span class="c1"># restore saved locale</span>
</pre></div>
</div>
<section id="background-details-hints-tips-and-caveats">
<h2>Background, details, hints, tips and caveats<a class="headerlink" href="#background-details-hints-tips-and-caveats" title="Link to this heading">¶</a></h2>
<p>The C standard defines the locale as a program-wide property that may be
relatively expensive to change.  On top of that, some implementations are broken
in such a way that frequent locale changes may cause core dumps.  This makes the
locale somewhat painful to use correctly.</p>
<p>Initially, when a program is started, the locale is the <code class="docutils literal notranslate"><span class="pre">C</span></code> locale, no matter
what the user’s preferred locale is.  There is one exception: the
<a class="reference internal" href="#locale.LC_CTYPE" title="locale.LC_CTYPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">LC_CTYPE</span></code></a> category is changed at startup to set the current locale
encoding to the user’s preferred locale encoding. The program must explicitly
say that it wants the user’s preferred locale settings for other categories by
calling <code class="docutils literal notranslate"><span class="pre">setlocale(LC_ALL,</span> <span class="pre">'')</span></code>.</p>
<p>It is generally a bad idea to call <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a> in some library routine,
since as a side effect it affects the entire program.  Saving and restoring it
is almost as bad: it is expensive and affects other threads that happen to run
before the settings have been restored.</p>
<p>If, when coding a module for general use, you need a locale independent version
of an operation that is affected by the locale (such as
certain formats used with <a class="reference internal" href="time.html#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.strftime()</span></code></a>), you will have to find a way to
do it without using the standard library routine.  Even better is convincing
yourself that using locale settings is okay.  Only as a last resort should you
document that your module is not compatible with non-<code class="docutils literal notranslate"><span class="pre">C</span></code> locale settings.</p>
<p>The only way to perform numeric operations according to the locale is to use the
special functions defined by this module: <a class="reference internal" href="#locale.atof" title="locale.atof"><code class="xref py py-func docutils literal notranslate"><span class="pre">atof()</span></code></a>, <a class="reference internal" href="#locale.atoi" title="locale.atoi"><code class="xref py py-func docutils literal notranslate"><span class="pre">atoi()</span></code></a>,
<a class="reference internal" href="#locale.format_string" title="locale.format_string"><code class="xref py py-func docutils literal notranslate"><span class="pre">format_string()</span></code></a>, <a class="reference internal" href="#locale.str" title="locale.str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>.</p>
<p>There is no way to perform case conversions and character classifications
according to the locale.  For (Unicode) text strings these are done according
to the character value only, while for byte strings, the conversions and
classifications are done according to the ASCII value of the byte, and bytes
whose high bit is set (i.e., non-ASCII bytes) are never converted or considered
part of a character class such as letter or whitespace.</p>
</section>
<section id="for-extension-writers-and-programs-that-embed-python">
<span id="embedding-locale"></span><h2>For extension writers and programs that embed Python<a class="headerlink" href="#for-extension-writers-and-programs-that-embed-python" title="Link to this heading">¶</a></h2>
<p>Extension modules should never call <a class="reference internal" href="#locale.setlocale" title="locale.setlocale"><code class="xref py py-func docutils literal notranslate"><span class="pre">setlocale()</span></code></a>, except to find out what
the current locale is.  But since the return value can only be used portably to
restore it, that is not very useful (except perhaps to find out whether or not
the locale is <code class="docutils literal notranslate"><span class="pre">C</span></code>).</p>
<p>When Python code uses the <a class="reference internal" href="#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a> module to change the locale, this also
affects the embedding application.  If the embedding application doesn’t want
this to happen, it should remove the <code class="xref py py-mod docutils literal notranslate"><span class="pre">_locale</span></code> extension module (which does
all the work) from the table of built-in modules in the <code class="file docutils literal notranslate"><span class="pre">config.c</span></code> file,
and make sure that the <code class="xref py py-mod docutils literal notranslate"><span class="pre">_locale</span></code> module is not accessible as a shared
library.</p>
</section>
<section id="access-to-message-catalogs">
<span id="locale-gettext"></span><h2>Access to message catalogs<a class="headerlink" href="#access-to-message-catalogs" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="locale.gettext">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">gettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.gettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.dgettext">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">dgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.dgettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.dcgettext">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">dcgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.dcgettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.textdomain">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">textdomain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.textdomain" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.bindtextdomain">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">bindtextdomain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dir</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.bindtextdomain" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locale.bind_textdomain_codeset">
<span class="sig-prename descclassname"><span class="pre">locale.</span></span><span class="sig-name descname"><span class="pre">bind_textdomain_codeset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">codeset</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#locale.bind_textdomain_codeset" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The locale module exposes the C library’s gettext interface on systems that
provide this interface.  It consists of the functions <a class="reference internal" href="gettext.html#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-func docutils literal notranslate"><span class="pre">gettext()</span></code></a>,
<a class="reference internal" href="#locale.dgettext" title="locale.dgettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">dgettext()</span></code></a>, <a class="reference internal" href="#locale.dcgettext" title="locale.dcgettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">dcgettext()</span></code></a>, <a class="reference internal" href="#locale.textdomain" title="locale.textdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">textdomain()</span></code></a>, <a class="reference internal" href="#locale.bindtextdomain" title="locale.bindtextdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">bindtextdomain()</span></code></a>,
and <a class="reference internal" href="#locale.bind_textdomain_codeset" title="locale.bind_textdomain_codeset"><code class="xref py py-func docutils literal notranslate"><span class="pre">bind_textdomain_codeset()</span></code></a>.  These are similar to the same functions in
the <a class="reference internal" href="gettext.html#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module, but use the C library’s binary format for message
catalogs, and the C library’s search algorithms for locating message catalogs.</p>
<p>Python applications should normally find no need to invoke these functions, and
should use <a class="reference internal" href="gettext.html#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> instead.  A known exception to this rule are
applications that link with additional C libraries which internally invoke
C functions <code class="docutils literal notranslate"><span class="pre">gettext</span></code> or <code class="docutils literal notranslate"><span class="pre">dcgettext</span></code>.  For these applications, it may be
necessary to bind the text domain, so that the libraries can properly locate
their message catalogs.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a><ul>
<li><a class="reference internal" href="#background-details-hints-tips-and-caveats">Background, details, hints, tips and caveats</a></li>
<li><a class="reference internal" href="#for-extension-writers-and-programs-that-embed-python">For extension writers and programs that embed Python</a></li>
<li><a class="reference internal" href="#access-to-message-catalogs">Access to message catalogs</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gettext.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="frameworks.html"
                          title="next chapter">Program Frameworks</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/locale.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="frameworks.html" title="Program Frameworks"
             >next</a> |</li>
        <li class="right" >
          <a href="gettext.html" title="gettext — Multilingual internationalization services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="i18n.html" >Internationalization</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>