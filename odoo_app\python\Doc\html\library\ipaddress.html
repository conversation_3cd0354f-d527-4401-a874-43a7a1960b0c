<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="ipaddress — IPv4/IPv6 manipulation library" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/ipaddress.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/ipaddress.py ipaddress provides the capabilities to create, manipulate and operate on IPv4 and IPv6 addresses and networks. The functions and classes in this module make it straigh..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/ipaddress.py ipaddress provides the capabilities to create, manipulate and operate on IPv4 and IPv6 addresses and networks. The functions and classes in this module make it straigh..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>ipaddress — IPv4/IPv6 manipulation library &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Multimedia Services" href="mm.html" />
    <link rel="prev" title="xmlrpc.server — Basic XML-RPC servers" href="xmlrpc.server.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/ipaddress.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a><ul>
<li><a class="reference internal" href="#convenience-factory-functions">Convenience factory functions</a></li>
<li><a class="reference internal" href="#ip-addresses">IP Addresses</a><ul>
<li><a class="reference internal" href="#address-objects">Address objects</a></li>
<li><a class="reference internal" href="#conversion-to-strings-and-integers">Conversion to Strings and Integers</a></li>
<li><a class="reference internal" href="#operators">Operators</a><ul>
<li><a class="reference internal" href="#comparison-operators">Comparison operators</a></li>
<li><a class="reference internal" href="#arithmetic-operators">Arithmetic operators</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#ip-network-definitions">IP Network definitions</a><ul>
<li><a class="reference internal" href="#prefix-net-mask-and-host-mask">Prefix, net mask and host mask</a></li>
<li><a class="reference internal" href="#network-objects">Network objects</a></li>
<li><a class="reference internal" href="#id1">Operators</a><ul>
<li><a class="reference internal" href="#logical-operators">Logical operators</a></li>
<li><a class="reference internal" href="#iteration">Iteration</a></li>
<li><a class="reference internal" href="#networks-as-containers-of-addresses">Networks as containers of addresses</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#interface-objects">Interface objects</a><ul>
<li><a class="reference internal" href="#id2">Operators</a><ul>
<li><a class="reference internal" href="#id3">Logical operators</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#other-module-level-functions">Other Module Level Functions</a></li>
<li><a class="reference internal" href="#custom-exceptions">Custom Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.server.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mm.html"
                          title="next chapter">Multimedia Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ipaddress.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mm.html" title="Multimedia Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.server.html" title="xmlrpc.server — Basic XML-RPC servers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-ipaddress">
<span id="ipaddress-ipv4-ipv6-manipulation-library"></span><h1><a class="reference internal" href="#module-ipaddress" title="ipaddress: IPv4/IPv6 manipulation library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code></a> — IPv4/IPv6 manipulation library<a class="headerlink" href="#module-ipaddress" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/ipaddress.py">Lib/ipaddress.py</a></p>
<hr class="docutils" />
<p><a class="reference internal" href="#module-ipaddress" title="ipaddress: IPv4/IPv6 manipulation library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code></a> provides the capabilities to create, manipulate and
operate on IPv4 and IPv6 addresses and networks.</p>
<p>The functions and classes in this module make it straightforward to handle
various tasks related to IP addresses, including checking whether or not two
hosts are on the same subnet, iterating over all hosts in a particular
subnet, checking whether or not a string represents a valid IP address or
network definition, and so on.</p>
<p>This is the full module API reference—for an overview and introduction, see
<a class="reference internal" href="../howto/ipaddress.html#ipaddress-howto"><span class="std std-ref">An introduction to the ipaddress module</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<section id="convenience-factory-functions">
<h2>Convenience factory functions<a class="headerlink" href="#convenience-factory-functions" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-ipaddress" title="ipaddress: IPv4/IPv6 manipulation library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code></a> module provides factory functions to conveniently create
IP addresses, networks and interfaces:</p>
<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.ip_address">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">ip_address</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.ip_address" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> or <a class="reference internal" href="#ipaddress.IPv6Address" title="ipaddress.IPv6Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Address</span></code></a> object depending on
the IP address passed as argument.  Either IPv4 or IPv6 addresses may be
supplied; integers less than <code class="docutils literal notranslate"><span class="pre">2**32</span></code> will be considered to be IPv4 by default.
A <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if <em>address</em> does not represent a valid IPv4
or IPv6 address.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">)</span>
<span class="go">IPv4Address(&#39;***********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="s1">&#39;2001:db8::&#39;</span><span class="p">)</span>
<span class="go">IPv6Address(&#39;2001:db8::&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.ip_network">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">ip_network</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.ip_network" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> or <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a> object depending on
the IP address passed as argument.  <em>address</em> is a string or integer
representing the IP network.  Either IPv4 or IPv6 networks may be supplied;
integers less than <code class="docutils literal notranslate"><span class="pre">2**32</span></code> will be considered to be IPv4 by default.  <em>strict</em>
is passed to <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> or <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a> constructor.  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if <em>address</em> does not represent a valid IPv4 or
IPv6 address, or if the network has host bits set.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;***********/28&#39;</span><span class="p">)</span>
<span class="go">IPv4Network(&#39;***********/28&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.ip_interface">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">ip_interface</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.ip_interface" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="#ipaddress.IPv4Interface" title="ipaddress.IPv4Interface"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Interface</span></code></a> or <a class="reference internal" href="#ipaddress.IPv6Interface" title="ipaddress.IPv6Interface"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Interface</span></code></a> object depending
on the IP address passed as argument.  <em>address</em> is a string or integer
representing the IP address.  Either IPv4 or IPv6 addresses may be supplied;
integers less than <code class="docutils literal notranslate"><span class="pre">2**32</span></code> will be considered to be IPv4 by default.  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if <em>address</em> does not represent a valid IPv4 or
IPv6 address.</p>
</dd></dl>

<p>One downside of these convenience functions is that the need to handle both
IPv4 and IPv6 formats means that error messages provide minimal
information on the precise error, as the functions don’t know whether the
IPv4 or IPv6 format was intended. More detailed error reporting can be
obtained by calling the appropriate version specific class constructors
directly.</p>
</section>
<section id="ip-addresses">
<h2>IP Addresses<a class="headerlink" href="#ip-addresses" title="Link to this heading">¶</a></h2>
<section id="address-objects">
<h3>Address objects<a class="headerlink" href="#address-objects" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> and <a class="reference internal" href="#ipaddress.IPv6Address" title="ipaddress.IPv6Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Address</span></code></a> objects share a lot of common
attributes.  Some attributes that are only meaningful for IPv6 addresses are
also implemented by <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> objects, in order to make it easier to
write code that handles both IP versions correctly.  Address objects are
<a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>, so they can be used as keys in dictionaries.</p>
<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv4Address">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv4Address</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Address" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv4 address.  An <a class="reference internal" href="#ipaddress.AddressValueError" title="ipaddress.AddressValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AddressValueError</span></code></a> is raised if
<em>address</em> is not a valid IPv4 address.</p>
<p>The following constitutes a valid IPv4 address:</p>
<ol class="arabic simple">
<li><p>A string in decimal-dot notation, consisting of four decimal integers in
the inclusive range 0–255, separated by dots (e.g. <code class="docutils literal notranslate"><span class="pre">***********</span></code>). Each
integer represents an octet (byte) in the address. Leading zeroes are
not tolerated to prevent confusion with octal notation.</p></li>
<li><p>An integer that fits into 32 bits.</p></li>
<li><p>An integer packed into a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object of length 4 (most
significant octet first).</p></li>
</ol>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">)</span>
<span class="go">IPv4Address(&#39;***********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="mi">3232235521</span><span class="p">)</span>
<span class="go">IPv4Address(&#39;***********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;</span><span class="se">\xC0\xA8\x00\x01</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="go">IPv4Address(&#39;***********&#39;)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Leading zeros are tolerated, even in ambiguous cases that look like
octal notation.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9.5: </span>Leading zeros are no longer tolerated and are treated as an error.
IPv4 address strings are now parsed as strict as glibc
<a class="reference internal" href="socket.html#socket.inet_pton" title="socket.inet_pton"><code class="xref py py-func docutils literal notranslate"><span class="pre">inet_pton()</span></code></a>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#ipaddress.IPv4Address.version" title="Link to this definition">¶</a></dt>
<dd><p>The appropriate version number: <code class="docutils literal notranslate"><span class="pre">4</span></code> for IPv4, <code class="docutils literal notranslate"><span class="pre">6</span></code> for IPv6.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.max_prefixlen">
<span class="sig-name descname"><span class="pre">max_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv4Address.max_prefixlen" title="Link to this definition">¶</a></dt>
<dd><p>The total number of bits in the address representation for this
version: <code class="docutils literal notranslate"><span class="pre">32</span></code> for IPv4, <code class="docutils literal notranslate"><span class="pre">128</span></code> for IPv6.</p>
<p>The prefix defines the number of leading bits in an  address that
are compared to determine whether or not an address is part of a
network.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.compressed">
<span class="sig-name descname"><span class="pre">compressed</span></span><a class="headerlink" href="#ipaddress.IPv4Address.compressed" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.exploded">
<span class="sig-name descname"><span class="pre">exploded</span></span><a class="headerlink" href="#ipaddress.IPv4Address.exploded" title="Link to this definition">¶</a></dt>
<dd><p>The string representation in dotted decimal notation. Leading zeroes
are never included in the representation.</p>
<p>As IPv4 does not define a shorthand notation for addresses with octets
set to zero, these two attributes are always the same as <code class="docutils literal notranslate"><span class="pre">str(addr)</span></code>
for IPv4 addresses. Exposing these attributes makes it easier to
write display code that can handle both IPv4 and IPv6 addresses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.packed">
<span class="sig-name descname"><span class="pre">packed</span></span><a class="headerlink" href="#ipaddress.IPv4Address.packed" title="Link to this definition">¶</a></dt>
<dd><p>The binary representation of this address - a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object of
the appropriate length (most significant octet first). This is 4 bytes
for IPv4 and 16 bytes for IPv6.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.reverse_pointer">
<span class="sig-name descname"><span class="pre">reverse_pointer</span></span><a class="headerlink" href="#ipaddress.IPv4Address.reverse_pointer" title="Link to this definition">¶</a></dt>
<dd><p>The name of the reverse DNS PTR record for the IP address, e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="s2">&quot;127.0.0.1&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">reverse_pointer</span>
<span class="go">&#39;*********.in-addr.arpa&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="s2">&quot;2001:db8::1&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">reverse_pointer</span>
<span class="go">&#39;*******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.8.b.d.*******.2.ip6.arpa&#39;</span>
</pre></div>
</div>
<p>This is the name that could be used for performing a PTR lookup, not the
resolved hostname itself.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_multicast">
<span class="sig-name descname"><span class="pre">is_multicast</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_multicast" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is reserved for multicast use.  See
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3171.html"><strong>RFC 3171</strong></a> (for IPv4) or <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2373.html"><strong>RFC 2373</strong></a> (for IPv6).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_private">
<span class="sig-name descname"><span class="pre">is_private</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_private" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is allocated for private networks.  See
<a class="reference external" href="https://www.iana.org/assignments/iana-ipv4-special-registry/iana-ipv4-special-registry.xhtml">iana-ipv4-special-registry</a> (for IPv4) or <a class="reference external" href="https://www.iana.org/assignments/iana-ipv6-special-registry/iana-ipv6-special-registry.xhtml">iana-ipv6-special-registry</a>
(for IPv6).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_global">
<span class="sig-name descname"><span class="pre">is_global</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_global" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is allocated for public networks.  See
<a class="reference external" href="https://www.iana.org/assignments/iana-ipv4-special-registry/iana-ipv4-special-registry.xhtml">iana-ipv4-special-registry</a> (for IPv4) or <a class="reference external" href="https://www.iana.org/assignments/iana-ipv6-special-registry/iana-ipv6-special-registry.xhtml">iana-ipv6-special-registry</a>
(for IPv6).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_unspecified">
<span class="sig-name descname"><span class="pre">is_unspecified</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_unspecified" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is unspecified.  See <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5735.html"><strong>RFC 5735</strong></a> (for IPv4)
or <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2373.html"><strong>RFC 2373</strong></a> (for IPv6).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_reserved">
<span class="sig-name descname"><span class="pre">is_reserved</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_reserved" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is otherwise IETF reserved.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_loopback">
<span class="sig-name descname"><span class="pre">is_loopback</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_loopback" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if this is a loopback address.  See <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3330.html"><strong>RFC 3330</strong></a> (for IPv4)
or <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2373.html"><strong>RFC 2373</strong></a> (for IPv6).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.is_link_local">
<span class="sig-name descname"><span class="pre">is_link_local</span></span><a class="headerlink" href="#ipaddress.IPv4Address.is_link_local" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is reserved for link-local usage.  See
<span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3927.html"><strong>RFC 3927</strong></a>.</p>
</dd></dl>

</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Address.__format__">
<span class="sig-prename descclassname"><span class="pre">IPv4Address.</span></span><span class="sig-name descname"><span class="pre">__format__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Address.__format__" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string representation of the IP address, controlled by
an explicit format string.
<em>fmt</em> can be one of the following: <code class="docutils literal notranslate"><span class="pre">'s'</span></code>, the default option,
equivalent to <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a>, <code class="docutils literal notranslate"><span class="pre">'b'</span></code> for a zero-padded binary string,
<code class="docutils literal notranslate"><span class="pre">'X'</span></code> or <code class="docutils literal notranslate"><span class="pre">'x'</span></code> for an uppercase or lowercase hexadecimal
representation, or <code class="docutils literal notranslate"><span class="pre">'n'</span></code>, which is equivalent to <code class="docutils literal notranslate"><span class="pre">'b'</span></code> for IPv4
addresses and <code class="docutils literal notranslate"><span class="pre">'x'</span></code> for IPv6. For binary and hexadecimal
representations, the form specifier <code class="docutils literal notranslate"><span class="pre">'#'</span></code> and the grouping option
<code class="docutils literal notranslate"><span class="pre">'_'</span></code> are available. <code class="docutils literal notranslate"><span class="pre">__format__</span></code> is used by <code class="docutils literal notranslate"><span class="pre">format</span></code>, <code class="docutils literal notranslate"><span class="pre">str.format</span></code>
and f-strings.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">))</span>
<span class="go">&#39;***********&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">{:#b}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">))</span>
<span class="go">&#39;0b11000000101010000000000000000001&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s2">&quot;2001:db8::1000&quot;</span><span class="p">)</span><span class="si">:</span><span class="s1">s</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="go">&#39;2001:db8::1000&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;2001:db8::1000&#39;</span><span class="p">),</span> <span class="s1">&#39;_X&#39;</span><span class="p">)</span>
<span class="go">&#39;2001_0DB8_0000_0000_0000_0000_0000_1000&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;{:#_n}&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;2001:db8::1000&#39;</span><span class="p">))</span>
<span class="go">&#39;0x2001_0db8_0000_0000_0000_0000_0000_1000&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv6Address">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv6Address</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Address" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv6 address.  An <a class="reference internal" href="#ipaddress.AddressValueError" title="ipaddress.AddressValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AddressValueError</span></code></a> is raised if
<em>address</em> is not a valid IPv6 address.</p>
<p>The following constitutes a valid IPv6 address:</p>
<ol class="arabic">
<li><p>A string consisting of eight groups of four hexadecimal digits, each
group representing 16 bits.  The groups are separated by colons.
This describes an <em>exploded</em> (longhand) notation.  The string can
also be <em>compressed</em> (shorthand notation) by various means.  See
<span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4291.html"><strong>RFC 4291</strong></a> for details.  For example,
<code class="docutils literal notranslate"><span class="pre">&quot;0000:0000:0000:0000:0000:0abc:0007:0def&quot;</span></code> can be compressed to
<code class="docutils literal notranslate"><span class="pre">&quot;::abc:7:def&quot;</span></code>.</p>
<p>Optionally, the string may also have a scope zone ID, expressed
with a suffix <code class="docutils literal notranslate"><span class="pre">%scope_id</span></code>. If present, the scope ID must be non-empty,
and may not contain <code class="docutils literal notranslate"><span class="pre">%</span></code>.
See <span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4007.html"><strong>RFC 4007</strong></a> for details.
For example, <code class="docutils literal notranslate"><span class="pre">fe80::1234%1</span></code> might identify address <code class="docutils literal notranslate"><span class="pre">fe80::1234</span></code> on the first link of the node.</p>
</li>
<li><p>An integer that fits into 128 bits.</p></li>
<li><p>An integer packed into a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object of length 16, big-endian.</p></li>
</ol>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;2001:db8::1000&#39;</span><span class="p">)</span>
<span class="go">IPv6Address(&#39;2001:db8::1000&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;ff02::5678%1&#39;</span><span class="p">)</span>
<span class="go">IPv6Address(&#39;ff02::5678%1&#39;)</span>
</pre></div>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.compressed">
<span class="sig-name descname"><span class="pre">compressed</span></span><a class="headerlink" href="#ipaddress.IPv6Address.compressed" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The short form of the address representation, with leading zeroes in
groups omitted and the longest sequence of groups consisting entirely of
zeroes collapsed to a single empty group.</p>
<p>This is also the value returned by <code class="docutils literal notranslate"><span class="pre">str(addr)</span></code> for IPv6 addresses.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.exploded">
<span class="sig-name descname"><span class="pre">exploded</span></span><a class="headerlink" href="#ipaddress.IPv6Address.exploded" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The long form of the address representation, with all leading zeroes and
groups consisting entirely of zeroes included.</p>
<p>For the following attributes and methods, see the corresponding
documentation of the <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> class:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.packed">
<span class="sig-name descname"><span class="pre">packed</span></span><a class="headerlink" href="#ipaddress.IPv6Address.packed" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.reverse_pointer">
<span class="sig-name descname"><span class="pre">reverse_pointer</span></span><a class="headerlink" href="#ipaddress.IPv6Address.reverse_pointer" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#ipaddress.IPv6Address.version" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.max_prefixlen">
<span class="sig-name descname"><span class="pre">max_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv6Address.max_prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_multicast">
<span class="sig-name descname"><span class="pre">is_multicast</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_multicast" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_private">
<span class="sig-name descname"><span class="pre">is_private</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_private" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_global">
<span class="sig-name descname"><span class="pre">is_global</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_global" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_unspecified">
<span class="sig-name descname"><span class="pre">is_unspecified</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_unspecified" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_reserved">
<span class="sig-name descname"><span class="pre">is_reserved</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_reserved" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_loopback">
<span class="sig-name descname"><span class="pre">is_loopback</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_loopback" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_link_local">
<span class="sig-name descname"><span class="pre">is_link_local</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_link_local" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>is_global</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.is_site_local">
<span class="sig-name descname"><span class="pre">is_site_local</span></span><a class="headerlink" href="#ipaddress.IPv6Address.is_site_local" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the address is reserved for site-local usage.  Note that
the site-local address space has been deprecated by <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3879.html"><strong>RFC 3879</strong></a>. Use
<a class="reference internal" href="#ipaddress.IPv4Address.is_private" title="ipaddress.IPv4Address.is_private"><code class="xref py py-attr docutils literal notranslate"><span class="pre">is_private</span></code></a> to test if this address is in the
space of unique local addresses as defined by <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4193.html"><strong>RFC 4193</strong></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.ipv4_mapped">
<span class="sig-name descname"><span class="pre">ipv4_mapped</span></span><a class="headerlink" href="#ipaddress.IPv6Address.ipv4_mapped" title="Link to this definition">¶</a></dt>
<dd><p>For addresses that appear to be IPv4 mapped addresses (starting with
<code class="docutils literal notranslate"><span class="pre">::FFFF/96</span></code>), this property will report the embedded IPv4 address.
For any other address, this property will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.scope_id">
<span class="sig-name descname"><span class="pre">scope_id</span></span><a class="headerlink" href="#ipaddress.IPv6Address.scope_id" title="Link to this definition">¶</a></dt>
<dd><p>For scoped addresses as defined by <span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4007.html"><strong>RFC 4007</strong></a>, this property identifies
the particular zone of the address’s scope that the address belongs to,
as a string. When no scope zone is specified, this property will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.sixtofour">
<span class="sig-name descname"><span class="pre">sixtofour</span></span><a class="headerlink" href="#ipaddress.IPv6Address.sixtofour" title="Link to this definition">¶</a></dt>
<dd><p>For addresses that appear to be 6to4 addresses  (starting with
<code class="docutils literal notranslate"><span class="pre">2002::/16</span></code>) as defined by <span class="target" id="index-12"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3056.html"><strong>RFC 3056</strong></a>, this property will report
the embedded IPv4 address.  For any other address, this property will
be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.teredo">
<span class="sig-name descname"><span class="pre">teredo</span></span><a class="headerlink" href="#ipaddress.IPv6Address.teredo" title="Link to this definition">¶</a></dt>
<dd><p>For addresses that appear to be Teredo addresses (starting with
<code class="docutils literal notranslate"><span class="pre">2001::/32</span></code>) as defined by <span class="target" id="index-13"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4380.html"><strong>RFC 4380</strong></a>, this property will report
the embedded <code class="docutils literal notranslate"><span class="pre">(server,</span> <span class="pre">client)</span></code> IP address pair.  For any other
address, this property will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Address.__format__">
<span class="sig-prename descclassname"><span class="pre">IPv6Address.</span></span><span class="sig-name descname"><span class="pre">__format__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Address.__format__" title="Link to this definition">¶</a></dt>
<dd><p>Refer to the corresponding method documentation in
<a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

</section>
<section id="conversion-to-strings-and-integers">
<h3>Conversion to Strings and Integers<a class="headerlink" href="#conversion-to-strings-and-integers" title="Link to this heading">¶</a></h3>
<p>To interoperate with networking interfaces such as the socket module,
addresses must be converted to strings or integers. This is handled using
the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> and <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> builtin functions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">))</span>
<span class="go">&#39;***********&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">int</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***********&#39;</span><span class="p">))</span>
<span class="go">3232235521</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;::1&#39;</span><span class="p">))</span>
<span class="go">&#39;::1&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">int</span><span class="p">(</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;::1&#39;</span><span class="p">))</span>
<span class="go">1</span>
</pre></div>
</div>
<p>Note that IPv6 scoped addresses are converted to integers without scope zone ID.</p>
</section>
<section id="operators">
<h3>Operators<a class="headerlink" href="#operators" title="Link to this heading">¶</a></h3>
<p>Address objects support some operators.  Unless stated otherwise, operators can
only be applied between compatible objects (i.e. IPv4 with IPv4, IPv6 with
IPv6).</p>
<section id="comparison-operators">
<h4>Comparison operators<a class="headerlink" href="#comparison-operators" title="Link to this heading">¶</a></h4>
<p>Address objects can be compared with the usual set of comparison operators.
Same IPv6 addresses with different scope zone IDs are not equal.
Some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;127.0.0.1&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;127.0.0.1&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">!=</span> <span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;127.0.0.1&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;fe80::1234&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;fe80::1234%1&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;fe80::1234%1&#39;</span><span class="p">)</span> <span class="o">!=</span> <span class="n">IPv6Address</span><span class="p">(</span><span class="s1">&#39;fe80::1234%2&#39;</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
</section>
<section id="arithmetic-operators">
<h4>Arithmetic operators<a class="headerlink" href="#arithmetic-operators" title="Link to this heading">¶</a></h4>
<p>Integers can be added to or subtracted from address objects.  Some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">+</span> <span class="mi">3</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">-</span> <span class="mi">3</span>
<span class="go">IPv4Address(&#39;***************&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;***************&#39;</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">ipaddress.AddressValueError</span>: <span class="n">4294967296 (&gt;= 2**32) is not permitted as an IPv4 address</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="ip-network-definitions">
<h2>IP Network definitions<a class="headerlink" href="#ip-network-definitions" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> and <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a> objects provide a mechanism
for defining and inspecting IP network definitions.  A network definition
consists of a <em>mask</em> and a <em>network address</em>, and as such defines a range of
IP addresses that equal the network address when masked (binary AND) with the
mask.  For example, a network definition with the mask <code class="docutils literal notranslate"><span class="pre">*************</span></code> and
the network address <code class="docutils literal notranslate"><span class="pre">***********</span></code> consists of IP addresses in the inclusive
range <code class="docutils literal notranslate"><span class="pre">***********</span></code> to <code class="docutils literal notranslate"><span class="pre">*************</span></code>.</p>
<section id="prefix-net-mask-and-host-mask">
<h3>Prefix, net mask and host mask<a class="headerlink" href="#prefix-net-mask-and-host-mask" title="Link to this heading">¶</a></h3>
<p>There are several equivalent ways to specify IP network masks.  A <em>prefix</em>
<code class="docutils literal notranslate"><span class="pre">/&lt;nbits&gt;</span></code> is a notation that denotes how many high-order bits are set in
the network mask.  A <em>net mask</em> is an IP address with some number of
high-order bits set.  Thus the prefix <code class="docutils literal notranslate"><span class="pre">/24</span></code> is equivalent to the net mask
<code class="docutils literal notranslate"><span class="pre">*************</span></code> in IPv4, or <code class="docutils literal notranslate"><span class="pre">ffff:ff00::</span></code> in IPv6.  In addition, a
<em>host mask</em> is the logical inverse of a <em>net mask</em>, and is sometimes used
(for example in Cisco access control lists) to denote a network mask.  The
host mask equivalent to <code class="docutils literal notranslate"><span class="pre">/24</span></code> in IPv4 is <code class="docutils literal notranslate"><span class="pre">*********</span></code>.</p>
</section>
<section id="network-objects">
<h3>Network objects<a class="headerlink" href="#network-objects" title="Link to this heading">¶</a></h3>
<p>All attributes implemented by address objects are implemented by network
objects as well.  In addition, network objects implement additional attributes.
All of these are common between <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> and <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a>,
so to avoid duplication they are only documented for <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a>.
Network objects are <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>, so they can be used as keys in
dictionaries.</p>
<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv4Network">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv4Network</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv4 network definition.  <em>address</em> can be one of the following:</p>
<ol class="arabic">
<li><p>A string consisting of an IP address and an optional mask, separated by
a slash (<code class="docutils literal notranslate"><span class="pre">/</span></code>).  The IP address is the network address, and the mask
can be either a single number, which means it’s a <em>prefix</em>, or a string
representation of an IPv4 address.  If it’s the latter, the mask is
interpreted as a <em>net mask</em> if it starts with a non-zero field, or as a
<em>host mask</em> if it starts with a zero field, with the single exception of
an all-zero mask which is treated as a <em>net mask</em>.  If no mask is provided,
it’s considered to be <code class="docutils literal notranslate"><span class="pre">/32</span></code>.</p>
<p>For example, the following <em>address</em> specifications are equivalent:
<code class="docutils literal notranslate"><span class="pre">***********/24</span></code>, <code class="docutils literal notranslate"><span class="pre">***********/*************</span></code> and
<code class="docutils literal notranslate"><span class="pre">***********/*********</span></code>.</p>
</li>
<li><p>An integer that fits into 32 bits.  This is equivalent to a
single-address network, with the network address being <em>address</em> and
the mask being <code class="docutils literal notranslate"><span class="pre">/32</span></code>.</p></li>
<li><p>An integer packed into a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object of length 4, big-endian.
The interpretation is similar to an integer <em>address</em>.</p></li>
<li><p>A two-tuple of an address description and a netmask, where the address
description is either a string, a 32-bits integer, a 4-bytes packed
integer, or an existing IPv4Address object; and the netmask is either
an integer representing the prefix length (e.g. <code class="docutils literal notranslate"><span class="pre">24</span></code>) or a string
representing the prefix mask (e.g. <code class="docutils literal notranslate"><span class="pre">*************</span></code>).</p></li>
</ol>
<p>An <a class="reference internal" href="#ipaddress.AddressValueError" title="ipaddress.AddressValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AddressValueError</span></code></a> is raised if <em>address</em> is not a valid IPv4
address.  A <a class="reference internal" href="#ipaddress.NetmaskValueError" title="ipaddress.NetmaskValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NetmaskValueError</span></code></a> is raised if the mask is not valid for
an IPv4 address.</p>
<p>If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> and host bits are set in the supplied address,
then <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.  Otherwise, the host bits are masked out
to determine the appropriate network address.</p>
<p>Unless stated otherwise, all network methods accepting other network/address
objects will raise <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if the argument’s IP version is
incompatible to <code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the two-tuple form for the <em>address</em> constructor parameter.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#ipaddress.IPv4Network.version" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.max_prefixlen">
<span class="sig-name descname"><span class="pre">max_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv4Network.max_prefixlen" title="Link to this definition">¶</a></dt>
<dd><p>Refer to the corresponding attribute documentation in
<a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_multicast">
<span class="sig-name descname"><span class="pre">is_multicast</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_multicast" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_private">
<span class="sig-name descname"><span class="pre">is_private</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_private" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_unspecified">
<span class="sig-name descname"><span class="pre">is_unspecified</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_unspecified" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_reserved">
<span class="sig-name descname"><span class="pre">is_reserved</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_reserved" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_loopback">
<span class="sig-name descname"><span class="pre">is_loopback</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_loopback" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.is_link_local">
<span class="sig-name descname"><span class="pre">is_link_local</span></span><a class="headerlink" href="#ipaddress.IPv4Network.is_link_local" title="Link to this definition">¶</a></dt>
<dd><p>These attributes are true for the network as a whole if they are true
for both the network address and the broadcast address.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.network_address">
<span class="sig-name descname"><span class="pre">network_address</span></span><a class="headerlink" href="#ipaddress.IPv4Network.network_address" title="Link to this definition">¶</a></dt>
<dd><p>The network address for the network. The network address and the
prefix length together uniquely define a network.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.broadcast_address">
<span class="sig-name descname"><span class="pre">broadcast_address</span></span><a class="headerlink" href="#ipaddress.IPv4Network.broadcast_address" title="Link to this definition">¶</a></dt>
<dd><p>The broadcast address for the network. Packets sent to the broadcast
address should be received by every host on the network.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.hostmask">
<span class="sig-name descname"><span class="pre">hostmask</span></span><a class="headerlink" href="#ipaddress.IPv4Network.hostmask" title="Link to this definition">¶</a></dt>
<dd><p>The host mask, as an <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.netmask">
<span class="sig-name descname"><span class="pre">netmask</span></span><a class="headerlink" href="#ipaddress.IPv4Network.netmask" title="Link to this definition">¶</a></dt>
<dd><p>The net mask, as an <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.with_prefixlen">
<span class="sig-name descname"><span class="pre">with_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv4Network.with_prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.compressed">
<span class="sig-name descname"><span class="pre">compressed</span></span><a class="headerlink" href="#ipaddress.IPv4Network.compressed" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.exploded">
<span class="sig-name descname"><span class="pre">exploded</span></span><a class="headerlink" href="#ipaddress.IPv4Network.exploded" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the network, with the mask in prefix
notation.</p>
<p><code class="docutils literal notranslate"><span class="pre">with_prefixlen</span></code> and <code class="docutils literal notranslate"><span class="pre">compressed</span></code> are always the same as
<code class="docutils literal notranslate"><span class="pre">str(network)</span></code>.
<code class="docutils literal notranslate"><span class="pre">exploded</span></code> uses the exploded form the network address.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.with_netmask">
<span class="sig-name descname"><span class="pre">with_netmask</span></span><a class="headerlink" href="#ipaddress.IPv4Network.with_netmask" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the network, with the mask in net mask
notation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.with_hostmask">
<span class="sig-name descname"><span class="pre">with_hostmask</span></span><a class="headerlink" href="#ipaddress.IPv4Network.with_hostmask" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the network, with the mask in host mask
notation.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.num_addresses">
<span class="sig-name descname"><span class="pre">num_addresses</span></span><a class="headerlink" href="#ipaddress.IPv4Network.num_addresses" title="Link to this definition">¶</a></dt>
<dd><p>The total number of addresses in the network.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.prefixlen">
<span class="sig-name descname"><span class="pre">prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv4Network.prefixlen" title="Link to this definition">¶</a></dt>
<dd><p>Length of the network prefix, in bits.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.hosts">
<span class="sig-name descname"><span class="pre">hosts</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.hosts" title="Link to this definition">¶</a></dt>
<dd><p>Returns an iterator over the usable hosts in the network.  The usable
hosts are all the IP addresses that belong to the network, except the
network address itself and the network broadcast address.  For networks
with a mask length of 31, the network address and network broadcast
address are also included in the result. Networks with a mask of 32
will return a list containing the single host address.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/29&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">hosts</span><span class="p">())</span>  
<span class="go">[IPv4Address(&#39;*********&#39;), IPv4Address(&#39;*********&#39;),</span>
<span class="go"> IPv4Address(&#39;*********&#39;), IPv4Address(&#39;*********&#39;),</span>
<span class="go"> IPv4Address(&#39;*********&#39;), IPv4Address(&#39;*********&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/31&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">hosts</span><span class="p">())</span>
<span class="go">[IPv4Address(&#39;*********&#39;), IPv4Address(&#39;*********&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">hosts</span><span class="p">())</span>
<span class="go">[IPv4Address(&#39;*********&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.overlaps">
<span class="sig-name descname"><span class="pre">overlaps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.overlaps" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if this network is partly or wholly contained in <em>other</em> or
<em>other</em> is wholly contained in this network.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.address_exclude">
<span class="sig-name descname"><span class="pre">address_exclude</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">network</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.address_exclude" title="Link to this definition">¶</a></dt>
<dd><p>Computes the network definitions resulting from removing the given
<em>network</em> from this one.  Returns an iterator of network objects.
Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>network</em> is not completely contained in
this network.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">n1</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">n2</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">n1</span><span class="o">.</span><span class="n">address_exclude</span><span class="p">(</span><span class="n">n2</span><span class="p">))</span>  
<span class="go">[IPv4Network(&#39;*********/29&#39;), IPv4Network(&#39;*********/30&#39;),</span>
<span class="go"> IPv4Network(&#39;*********/31&#39;), IPv4Network(&#39;*********/32&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.subnets">
<span class="sig-name descname"><span class="pre">subnets</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefixlen_diff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.subnets" title="Link to this definition">¶</a></dt>
<dd><p>The subnets that join to make the current network definition, depending
on the argument values.  <em>prefixlen_diff</em> is the amount our prefix
length should be increased by.  <em>new_prefix</em> is the desired new
prefix of the subnets; it must be larger than our prefix.  One and
only one of <em>prefixlen_diff</em> and <em>new_prefix</em> must be set.  Returns an
iterator of network objects.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">subnets</span><span class="p">())</span>
<span class="go">[IPv4Network(&#39;*********/25&#39;), IPv4Network(&#39;*********28/25&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">subnets</span><span class="p">(</span><span class="n">prefixlen_diff</span><span class="o">=</span><span class="mi">2</span><span class="p">))</span>  
<span class="go">[IPv4Network(&#39;*********/26&#39;), IPv4Network(&#39;*********4/26&#39;),</span>
<span class="go"> IPv4Network(&#39;*********28/26&#39;), IPv4Network(&#39;*********92/26&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">subnets</span><span class="p">(</span><span class="n">new_prefix</span><span class="o">=</span><span class="mi">26</span><span class="p">))</span>  
<span class="go">[IPv4Network(&#39;*********/26&#39;), IPv4Network(&#39;*********4/26&#39;),</span>
<span class="go"> IPv4Network(&#39;*********28/26&#39;), IPv4Network(&#39;*********92/26&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">subnets</span><span class="p">(</span><span class="n">new_prefix</span><span class="o">=</span><span class="mi">23</span><span class="p">))</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="w">    </span><span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;new prefix must be longer&#39;</span><span class="p">)</span>
<span class="gr">ValueError</span>: <span class="n">new prefix must be longer</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">subnets</span><span class="p">(</span><span class="n">new_prefix</span><span class="o">=</span><span class="mi">25</span><span class="p">))</span>
<span class="go">[IPv4Network(&#39;*********/25&#39;), IPv4Network(&#39;*********28/25&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.supernet">
<span class="sig-name descname"><span class="pre">supernet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefixlen_diff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.supernet" title="Link to this definition">¶</a></dt>
<dd><p>The supernet containing this network definition, depending on the
argument values.  <em>prefixlen_diff</em> is the amount our prefix length
should be decreased by.  <em>new_prefix</em> is the desired new prefix of
the supernet; it must be smaller than our prefix.  One and only one
of <em>prefixlen_diff</em> and <em>new_prefix</em> must be set.  Returns a single
network object.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">supernet</span><span class="p">()</span>
<span class="go">IPv4Network(&#39;*********/23&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">supernet</span><span class="p">(</span><span class="n">prefixlen_diff</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="go">IPv4Network(&#39;*********/22&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">supernet</span><span class="p">(</span><span class="n">new_prefix</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
<span class="go">IPv4Network(&#39;*********/20&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.subnet_of">
<span class="sig-name descname"><span class="pre">subnet_of</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.subnet_of" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if this network is a subnet of <em>other</em>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;***********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*************/30&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="o">.</span><span class="n">subnet_of</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.supernet_of">
<span class="sig-name descname"><span class="pre">supernet_of</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.supernet_of" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if this network is a supernet of <em>other</em>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;***********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*************/30&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">supernet_of</span><span class="p">(</span><span class="n">b</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv4Network.compare_networks">
<span class="sig-name descname"><span class="pre">compare_networks</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Network.compare_networks" title="Link to this definition">¶</a></dt>
<dd><p>Compare this network to <em>other</em>.  In this comparison only the network
addresses are considered; host bits aren’t.  Returns either <code class="docutils literal notranslate"><span class="pre">-1</span></code>,
<code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">compare_networks</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">))</span>
<span class="go">-1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">compare_networks</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">))</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">compare_networks</span><span class="p">(</span><span class="n">ip_network</span><span class="p">(</span><span class="s1">&#39;*********/32&#39;</span><span class="p">))</span>
<span class="go">0</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>It uses the same ordering and comparison algorithm as “&lt;”, “==”, and “&gt;”</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv6Network">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv6Network</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv6 network definition.  <em>address</em> can be one of the following:</p>
<ol class="arabic">
<li><p>A string consisting of an IP address and an optional prefix length,
separated by a slash (<code class="docutils literal notranslate"><span class="pre">/</span></code>).  The IP address is the network address,
and the prefix length must be a single number, the <em>prefix</em>.  If no
prefix length is provided, it’s considered to be <code class="docutils literal notranslate"><span class="pre">/128</span></code>.</p>
<p>Note that currently expanded netmasks are not supported.  That means
<code class="docutils literal notranslate"><span class="pre">2001:db00::0/24</span></code> is a valid argument while <code class="docutils literal notranslate"><span class="pre">2001:db00::0/ffff:ff00::</span></code>
is not.</p>
</li>
<li><p>An integer that fits into 128 bits.  This is equivalent to a
single-address network, with the network address being <em>address</em> and
the mask being <code class="docutils literal notranslate"><span class="pre">/128</span></code>.</p></li>
<li><p>An integer packed into a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object of length 16, big-endian.
The interpretation is similar to an integer <em>address</em>.</p></li>
<li><p>A two-tuple of an address description and a netmask, where the address
description is either a string, a 128-bits integer, a 16-bytes packed
integer, or an existing IPv6Address object; and the netmask is an
integer representing the prefix length.</p></li>
</ol>
<p>An <a class="reference internal" href="#ipaddress.AddressValueError" title="ipaddress.AddressValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AddressValueError</span></code></a> is raised if <em>address</em> is not a valid IPv6
address.  A <a class="reference internal" href="#ipaddress.NetmaskValueError" title="ipaddress.NetmaskValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NetmaskValueError</span></code></a> is raised if the mask is not valid for
an IPv6 address.</p>
<p>If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> and host bits are set in the supplied address,
then <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.  Otherwise, the host bits are masked out
to determine the appropriate network address.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the two-tuple form for the <em>address</em> constructor parameter.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#ipaddress.IPv6Network.version" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.max_prefixlen">
<span class="sig-name descname"><span class="pre">max_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv6Network.max_prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_multicast">
<span class="sig-name descname"><span class="pre">is_multicast</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_multicast" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_private">
<span class="sig-name descname"><span class="pre">is_private</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_private" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_unspecified">
<span class="sig-name descname"><span class="pre">is_unspecified</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_unspecified" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_reserved">
<span class="sig-name descname"><span class="pre">is_reserved</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_reserved" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_loopback">
<span class="sig-name descname"><span class="pre">is_loopback</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_loopback" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_link_local">
<span class="sig-name descname"><span class="pre">is_link_local</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_link_local" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.network_address">
<span class="sig-name descname"><span class="pre">network_address</span></span><a class="headerlink" href="#ipaddress.IPv6Network.network_address" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.broadcast_address">
<span class="sig-name descname"><span class="pre">broadcast_address</span></span><a class="headerlink" href="#ipaddress.IPv6Network.broadcast_address" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.hostmask">
<span class="sig-name descname"><span class="pre">hostmask</span></span><a class="headerlink" href="#ipaddress.IPv6Network.hostmask" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.netmask">
<span class="sig-name descname"><span class="pre">netmask</span></span><a class="headerlink" href="#ipaddress.IPv6Network.netmask" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.with_prefixlen">
<span class="sig-name descname"><span class="pre">with_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv6Network.with_prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.compressed">
<span class="sig-name descname"><span class="pre">compressed</span></span><a class="headerlink" href="#ipaddress.IPv6Network.compressed" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.exploded">
<span class="sig-name descname"><span class="pre">exploded</span></span><a class="headerlink" href="#ipaddress.IPv6Network.exploded" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.with_netmask">
<span class="sig-name descname"><span class="pre">with_netmask</span></span><a class="headerlink" href="#ipaddress.IPv6Network.with_netmask" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.with_hostmask">
<span class="sig-name descname"><span class="pre">with_hostmask</span></span><a class="headerlink" href="#ipaddress.IPv6Network.with_hostmask" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.num_addresses">
<span class="sig-name descname"><span class="pre">num_addresses</span></span><a class="headerlink" href="#ipaddress.IPv6Network.num_addresses" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.prefixlen">
<span class="sig-name descname"><span class="pre">prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv6Network.prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.hosts">
<span class="sig-name descname"><span class="pre">hosts</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.hosts" title="Link to this definition">¶</a></dt>
<dd><p>Returns an iterator over the usable hosts in the network.  The usable
hosts are all the IP addresses that belong to the network, except the
Subnet-Router anycast address.  For networks with a mask length of 127,
the Subnet-Router anycast address is also included in the result.
Networks with a mask of 128 will return a list containing the
single host address.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.overlaps">
<span class="sig-name descname"><span class="pre">overlaps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.overlaps" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.address_exclude">
<span class="sig-name descname"><span class="pre">address_exclude</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">network</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.address_exclude" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.subnets">
<span class="sig-name descname"><span class="pre">subnets</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefixlen_diff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.subnets" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.supernet">
<span class="sig-name descname"><span class="pre">supernet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefixlen_diff</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.supernet" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.subnet_of">
<span class="sig-name descname"><span class="pre">subnet_of</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.subnet_of" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.supernet_of">
<span class="sig-name descname"><span class="pre">supernet_of</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.supernet_of" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.compare_networks">
<span class="sig-name descname"><span class="pre">compare_networks</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Network.compare_networks" title="Link to this definition">¶</a></dt>
<dd><p>Refer to the corresponding attribute documentation in
<a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Network.is_site_local">
<span class="sig-name descname"><span class="pre">is_site_local</span></span><a class="headerlink" href="#ipaddress.IPv6Network.is_site_local" title="Link to this definition">¶</a></dt>
<dd><p>These attribute is true for the network as a whole if it is true
for both the network address and the broadcast address.</p>
</dd></dl>

</dd></dl>

</section>
<section id="id1">
<h3>Operators<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<p>Network objects support some operators.  Unless stated otherwise, operators can
only be applied between compatible objects (i.e. IPv4 with IPv4, IPv6 with
IPv6).</p>
<section id="logical-operators">
<h4>Logical operators<a class="headerlink" href="#logical-operators" title="Link to this heading">¶</a></h4>
<p>Network objects can be compared with the usual set of logical operators.
Network objects are ordered first by network address, then by net mask.</p>
</section>
<section id="iteration">
<h4>Iteration<a class="headerlink" href="#iteration" title="Link to this heading">¶</a></h4>
<p>Network objects can be iterated to list all the addresses belonging to the
network.  For iteration, <em>all</em> hosts are returned, including unusable hosts
(for usable hosts, use the <a class="reference internal" href="#ipaddress.IPv4Network.hosts" title="ipaddress.IPv4Network.hosts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hosts()</span></code></a> method).  An
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">addr</span> <span class="ow">in</span> <span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">addr</span>
<span class="gp">...</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="go">IPv4Address(&#39;*********0&#39;)</span>
<span class="go">IPv4Address(&#39;*********1&#39;)</span>
<span class="go">IPv4Address(&#39;*********2&#39;)</span>
<span class="go">IPv4Address(&#39;*********3&#39;)</span>
<span class="go">IPv4Address(&#39;*********4&#39;)</span>
<span class="go">IPv4Address(&#39;*********5&#39;)</span>
</pre></div>
</div>
</section>
<section id="networks-as-containers-of-addresses">
<h4>Networks as containers of addresses<a class="headerlink" href="#networks-as-containers-of-addresses" title="Link to this heading">¶</a></h4>
<p>Network objects can act as containers of addresses.  Some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">)[</span><span class="mi">15</span><span class="p">]</span>
<span class="go">IPv4Address(&#39;*********5&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="ow">in</span> <span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="ow">in</span> <span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/28&#39;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="interface-objects">
<h2>Interface objects<a class="headerlink" href="#interface-objects" title="Link to this heading">¶</a></h2>
<p>Interface objects are <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>, so they can be used as keys in
dictionaries.</p>
<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv4Interface</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv4Interface" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv4 interface.  The meaning of <em>address</em> is as in the
constructor of <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a>, except that arbitrary host addresses
are always accepted.</p>
<p><a class="reference internal" href="#ipaddress.IPv4Interface" title="ipaddress.IPv4Interface"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Interface</span></code></a> is a subclass of <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a>, so it inherits
all the attributes from that class.  In addition, the following attributes
are available:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface.ip">
<span class="sig-name descname"><span class="pre">ip</span></span><a class="headerlink" href="#ipaddress.IPv4Interface.ip" title="Link to this definition">¶</a></dt>
<dd><p>The address (<a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a>) without network information.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span> <span class="o">=</span> <span class="n">IPv4Interface</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span><span class="o">.</span><span class="n">ip</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface.network">
<span class="sig-name descname"><span class="pre">network</span></span><a class="headerlink" href="#ipaddress.IPv4Interface.network" title="Link to this definition">¶</a></dt>
<dd><p>The network (<a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a>) this interface belongs to.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span> <span class="o">=</span> <span class="n">IPv4Interface</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span><span class="o">.</span><span class="n">network</span>
<span class="go">IPv4Network(&#39;*********/24&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface.with_prefixlen">
<span class="sig-name descname"><span class="pre">with_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv4Interface.with_prefixlen" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the interface with the mask in prefix notation.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span> <span class="o">=</span> <span class="n">IPv4Interface</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span><span class="o">.</span><span class="n">with_prefixlen</span>
<span class="go">&#39;*********/24&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface.with_netmask">
<span class="sig-name descname"><span class="pre">with_netmask</span></span><a class="headerlink" href="#ipaddress.IPv4Interface.with_netmask" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the interface with the network as a net mask.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span> <span class="o">=</span> <span class="n">IPv4Interface</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span><span class="o">.</span><span class="n">with_netmask</span>
<span class="go">&#39;*********/*************&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv4Interface.with_hostmask">
<span class="sig-name descname"><span class="pre">with_hostmask</span></span><a class="headerlink" href="#ipaddress.IPv4Interface.with_hostmask" title="Link to this definition">¶</a></dt>
<dd><p>A string representation of the interface with the network as a host mask.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span> <span class="o">=</span> <span class="n">IPv4Interface</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">interface</span><span class="o">.</span><span class="n">with_hostmask</span>
<span class="go">&#39;*********/*********&#39;</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">IPv6Interface</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.IPv6Interface" title="Link to this definition">¶</a></dt>
<dd><p>Construct an IPv6 interface.  The meaning of <em>address</em> is as in the
constructor of <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a>, except that arbitrary host addresses
are always accepted.</p>
<p><a class="reference internal" href="#ipaddress.IPv6Interface" title="ipaddress.IPv6Interface"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Interface</span></code></a> is a subclass of <a class="reference internal" href="#ipaddress.IPv6Address" title="ipaddress.IPv6Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Address</span></code></a>, so it inherits
all the attributes from that class.  In addition, the following attributes
are available:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface.ip">
<span class="sig-name descname"><span class="pre">ip</span></span><a class="headerlink" href="#ipaddress.IPv6Interface.ip" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface.network">
<span class="sig-name descname"><span class="pre">network</span></span><a class="headerlink" href="#ipaddress.IPv6Interface.network" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface.with_prefixlen">
<span class="sig-name descname"><span class="pre">with_prefixlen</span></span><a class="headerlink" href="#ipaddress.IPv6Interface.with_prefixlen" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface.with_netmask">
<span class="sig-name descname"><span class="pre">with_netmask</span></span><a class="headerlink" href="#ipaddress.IPv6Interface.with_netmask" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="ipaddress.IPv6Interface.with_hostmask">
<span class="sig-name descname"><span class="pre">with_hostmask</span></span><a class="headerlink" href="#ipaddress.IPv6Interface.with_hostmask" title="Link to this definition">¶</a></dt>
<dd><p>Refer to the corresponding attribute documentation in
<a class="reference internal" href="#ipaddress.IPv4Interface" title="ipaddress.IPv4Interface"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Interface</span></code></a>.</p>
</dd></dl>

</dd></dl>

<section id="id2">
<h3>Operators<a class="headerlink" href="#id2" title="Link to this heading">¶</a></h3>
<p>Interface objects support some operators.  Unless stated otherwise, operators
can only be applied between compatible objects (i.e. IPv4 with IPv4, IPv6 with
IPv6).</p>
<section id="id3">
<h4>Logical operators<a class="headerlink" href="#id3" title="Link to this heading">¶</a></h4>
<p>Interface objects can be compared with the usual set of logical operators.</p>
<p>For equality comparison (<code class="docutils literal notranslate"><span class="pre">==</span></code> and <code class="docutils literal notranslate"><span class="pre">!=</span></code>), both the IP address and network
must be the same for the objects to be equal.  An interface will not compare
equal to any address or network object.</p>
<p>For ordering (<code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, etc) the rules are different.  Interface and
address objects with the same IP version can be compared, and the address
objects will always sort before the interface objects.  Two interface objects
are first compared by their networks and, if those are the same, then by their
IP addresses.</p>
</section>
</section>
</section>
<section id="other-module-level-functions">
<h2>Other Module Level Functions<a class="headerlink" href="#other-module-level-functions" title="Link to this heading">¶</a></h2>
<p>The module also provides the following module level functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.v4_int_to_packed">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">v4_int_to_packed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.v4_int_to_packed" title="Link to this definition">¶</a></dt>
<dd><p>Represent an address as 4 packed bytes in network (big-endian) order.
<em>address</em> is an integer representation of an IPv4 IP address.  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if the integer is negative or too large to be an
IPv4 IP address.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="mi">3221225985</span><span class="p">)</span>
<span class="go">IPv4Address(&#39;*********&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">v4_int_to_packed</span><span class="p">(</span><span class="mi">3221225985</span><span class="p">)</span>
<span class="go">b&#39;\xc0\x00\x02\x01&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.v6_int_to_packed">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">v6_int_to_packed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.v6_int_to_packed" title="Link to this definition">¶</a></dt>
<dd><p>Represent an address as 16 packed bytes in network (big-endian) order.
<em>address</em> is an integer representation of an IPv6 IP address.  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if the integer is negative or too large to be an
IPv6 IP address.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.summarize_address_range">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">summarize_address_range</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">first</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">last</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.summarize_address_range" title="Link to this definition">¶</a></dt>
<dd><p>Return an iterator of the summarized network range given the first and last
IP addresses.  <em>first</em> is the first <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a> or
<a class="reference internal" href="#ipaddress.IPv6Address" title="ipaddress.IPv6Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Address</span></code></a> in the range and <em>last</em> is the last <a class="reference internal" href="#ipaddress.IPv4Address" title="ipaddress.IPv4Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Address</span></code></a>
or <a class="reference internal" href="#ipaddress.IPv6Address" title="ipaddress.IPv6Address"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Address</span></code></a> in the range.  A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised if
<em>first</em> or <em>last</em> are not IP addresses or are not of the same version.  A
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if <em>last</em> is not greater than <em>first</em> or if
<em>first</em> address version is not 4 or 6.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">ipaddr</span> <span class="k">for</span> <span class="n">ipaddr</span> <span class="ow">in</span> <span class="n">ipaddress</span><span class="o">.</span><span class="n">summarize_address_range</span><span class="p">(</span>
<span class="gp">... </span>   <span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">),</span>
<span class="gp">... </span>   <span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********30&#39;</span><span class="p">))]</span>
<span class="go">[IPv4Network(&#39;*********/25&#39;), IPv4Network(&#39;*********28/31&#39;), IPv4Network(&#39;*********30/32&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.collapse_addresses">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">collapse_addresses</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">addresses</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.collapse_addresses" title="Link to this definition">¶</a></dt>
<dd><p>Return an iterator of the collapsed <a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> or
<a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a> objects.  <em>addresses</em> is an iterator of
<a class="reference internal" href="#ipaddress.IPv4Network" title="ipaddress.IPv4Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv4Network</span></code></a> or <a class="reference internal" href="#ipaddress.IPv6Network" title="ipaddress.IPv6Network"><code class="xref py py-class docutils literal notranslate"><span class="pre">IPv6Network</span></code></a> objects.  A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is
raised if <em>addresses</em> contains mixed version objects.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">ipaddr</span> <span class="k">for</span> <span class="n">ipaddr</span> <span class="ow">in</span>
<span class="gp">... </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">collapse_addresses</span><span class="p">([</span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/25&#39;</span><span class="p">),</span>
<span class="gp">... </span><span class="n">ipaddress</span><span class="o">.</span><span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********28/25&#39;</span><span class="p">)])]</span>
<span class="go">[IPv4Network(&#39;*********/24&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ipaddress.get_mixed_type_key">
<span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">get_mixed_type_key</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.get_mixed_type_key" title="Link to this definition">¶</a></dt>
<dd><p>Return a key suitable for sorting between networks and addresses.  Address
and Network objects are not sortable by default; they’re fundamentally
different, so the expression:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">IPv4Address</span><span class="p">(</span><span class="s1">&#39;*********&#39;</span><span class="p">)</span> <span class="o">&lt;=</span> <span class="n">IPv4Network</span><span class="p">(</span><span class="s1">&#39;*********/24&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>doesn’t make sense.  There are some times however, where you may wish to
have <a class="reference internal" href="#module-ipaddress" title="ipaddress: IPv4/IPv6 manipulation library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code></a> sort these anyway.  If you need to do this, you can use
this function as the <em>key</em> argument to <a class="reference internal" href="functions.html#sorted" title="sorted"><code class="xref py py-func docutils literal notranslate"><span class="pre">sorted()</span></code></a>.</p>
<p><em>obj</em> is either a network or address object.</p>
</dd></dl>

</section>
<section id="custom-exceptions">
<h2>Custom Exceptions<a class="headerlink" href="#custom-exceptions" title="Link to this heading">¶</a></h2>
<p>To support more specific error reporting from class constructors, the
module defines the following exceptions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="ipaddress.AddressValueError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">AddressValueError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ValueError</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.AddressValueError" title="Link to this definition">¶</a></dt>
<dd><p>Any value error related to the address.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="ipaddress.NetmaskValueError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ipaddress.</span></span><span class="sig-name descname"><span class="pre">NetmaskValueError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ValueError</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ipaddress.NetmaskValueError" title="Link to this definition">¶</a></dt>
<dd><p>Any value error related to the net mask.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a><ul>
<li><a class="reference internal" href="#convenience-factory-functions">Convenience factory functions</a></li>
<li><a class="reference internal" href="#ip-addresses">IP Addresses</a><ul>
<li><a class="reference internal" href="#address-objects">Address objects</a></li>
<li><a class="reference internal" href="#conversion-to-strings-and-integers">Conversion to Strings and Integers</a></li>
<li><a class="reference internal" href="#operators">Operators</a><ul>
<li><a class="reference internal" href="#comparison-operators">Comparison operators</a></li>
<li><a class="reference internal" href="#arithmetic-operators">Arithmetic operators</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#ip-network-definitions">IP Network definitions</a><ul>
<li><a class="reference internal" href="#prefix-net-mask-and-host-mask">Prefix, net mask and host mask</a></li>
<li><a class="reference internal" href="#network-objects">Network objects</a></li>
<li><a class="reference internal" href="#id1">Operators</a><ul>
<li><a class="reference internal" href="#logical-operators">Logical operators</a></li>
<li><a class="reference internal" href="#iteration">Iteration</a></li>
<li><a class="reference internal" href="#networks-as-containers-of-addresses">Networks as containers of addresses</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#interface-objects">Interface objects</a><ul>
<li><a class="reference internal" href="#id2">Operators</a><ul>
<li><a class="reference internal" href="#id3">Logical operators</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#other-module-level-functions">Other Module Level Functions</a></li>
<li><a class="reference internal" href="#custom-exceptions">Custom Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xmlrpc.server.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc.server</span></code> — Basic XML-RPC servers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mm.html"
                          title="next chapter">Multimedia Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/ipaddress.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mm.html" title="Multimedia Services"
             >next</a> |</li>
        <li class="right" >
          <a href="xmlrpc.server.html" title="xmlrpc.server — Basic XML-RPC servers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">ipaddress</span></code> — IPv4/IPv6 manipulation library</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>