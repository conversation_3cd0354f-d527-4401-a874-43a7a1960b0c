<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="stringprep — Internet String Preparation" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/stringprep.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/stringprep.py When identifying things (such as host names) in the internet, it is often necessary to compare such identifications for “equality”. Exactly how this comparison is exe..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/stringprep.py When identifying things (such as host names) in the internet, it is often necessary to compare such identifications for “equality”. Exactly how this comparison is exe..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>stringprep — Internet String Preparation &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="readline — GNU readline interface" href="readline.html" />
    <link rel="prev" title="unicodedata — Unicode Database" href="unicodedata.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/stringprep.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="unicodedata.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="readline.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/stringprep.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="readline.html" title="readline — GNU readline interface"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="unicodedata.html" title="unicodedata — Unicode Database"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-stringprep">
<span id="stringprep-internet-string-preparation"></span><h1><a class="reference internal" href="#module-stringprep" title="stringprep: String preparation, as per RFC 3453"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code></a> — Internet String Preparation<a class="headerlink" href="#module-stringprep" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/stringprep.py">Lib/stringprep.py</a></p>
<hr class="docutils" />
<p>When identifying things (such as host names) in the internet, it is often
necessary to compare such identifications for “equality”. Exactly how this
comparison is executed may depend on the application domain, e.g. whether it
should be case-insensitive or not. It may be also necessary to restrict the
possible identifications, to allow only identifications consisting of
“printable” characters.</p>
<p><span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3454.html"><strong>RFC 3454</strong></a> defines a procedure for “preparing” Unicode strings in internet
protocols. Before passing strings onto the wire, they are processed with the
preparation procedure, after which they have a certain normalized form. The RFC
defines a set of tables, which can be combined into profiles. Each profile must
define which tables it uses, and what other optional parts of the <code class="docutils literal notranslate"><span class="pre">stringprep</span></code>
procedure are part of the profile. One example of a <code class="docutils literal notranslate"><span class="pre">stringprep</span></code> profile is
<code class="docutils literal notranslate"><span class="pre">nameprep</span></code>, which is used for internationalized domain names.</p>
<p>The module <a class="reference internal" href="#module-stringprep" title="stringprep: String preparation, as per RFC 3453"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code></a> only exposes the tables from <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3454.html"><strong>RFC 3454</strong></a>. As these
tables would be very large to represent as dictionaries or lists, the
module uses the Unicode character database internally. The module source code
itself was generated using the <code class="docutils literal notranslate"><span class="pre">mkstringprep.py</span></code> utility.</p>
<p>As a result, these tables are exposed as functions, not as data structures.
There are two kinds of tables in the RFC: sets and mappings. For a set,
<a class="reference internal" href="#module-stringprep" title="stringprep: String preparation, as per RFC 3453"><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code></a> provides the “characteristic function”, i.e. a function that
returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the parameter is part of the set. For mappings, it provides the
mapping function: given the key, it returns the associated value. Below is a
list of all functions available in the module.</p>
<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_a1">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_a1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_a1" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableA.1 (Unassigned code points in Unicode 3.2).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_b1">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_b1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_b1" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableB.1 (Commonly mapped to nothing).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.map_table_b2">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">map_table_b2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.map_table_b2" title="Link to this definition">¶</a></dt>
<dd><p>Return the mapped value for <em>code</em> according to tableB.2 (Mapping for
case-folding used with NFKC).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.map_table_b3">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">map_table_b3</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.map_table_b3" title="Link to this definition">¶</a></dt>
<dd><p>Return the mapped value for <em>code</em> according to tableB.3 (Mapping for
case-folding used with no normalization).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c11">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c11</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c11" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.1.1  (ASCII space characters).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c12">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c12</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c12" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.1.2  (Non-ASCII space characters).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c11_c12">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c11_c12</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c11_c12" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.1  (Space characters, union of C.1.1 and
C.1.2).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c21">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c21</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c21" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.2.1  (ASCII control characters).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c22">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c22</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c22" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.2.2  (Non-ASCII control characters).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c21_c22">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c21_c22</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c21_c22" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.2  (Control characters, union of C.2.1 and
C.2.2).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c3">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c3</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c3" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.3  (Private use).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c4">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c4</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c4" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.4  (Non-character code points).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c5">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c5</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c5" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.5  (Surrogate codes).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c6">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c6</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c6" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.6  (Inappropriate for plain text).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c7">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c7</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c7" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.7  (Inappropriate for canonical
representation).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c8">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c8</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c8" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.8  (Change display properties or are
deprecated).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_c9">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_c9</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_c9" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableC.9  (Tagging characters).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_d1">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_d1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_d1" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableD.1  (Characters with bidirectional property
“R” or “AL”).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="stringprep.in_table_d2">
<span class="sig-prename descclassname"><span class="pre">stringprep.</span></span><span class="sig-name descname"><span class="pre">in_table_d2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#stringprep.in_table_d2" title="Link to this definition">¶</a></dt>
<dd><p>Determine whether <em>code</em> is in tableD.2  (Characters with bidirectional property
“L”).</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="unicodedata.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="readline.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code> — GNU readline interface</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/stringprep.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="readline.html" title="readline — GNU readline interface"
             >next</a> |</li>
        <li class="right" >
          <a href="unicodedata.html" title="unicodedata — Unicode Database"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">stringprep</span></code> — Internet String Preparation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>