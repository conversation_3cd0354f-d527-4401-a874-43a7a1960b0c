<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Built-in Functions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/functions.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The Python interpreter has a number of functions and types built into it that are always available. They are listed here in alphabetical order.,,,, Built-in Functions,,, A, abs(), aiter(), all(), a..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The Python interpreter has a number of functions and types built into it that are always available. They are listed here in alphabetical order.,,,, Built-in Functions,,, A, abs(), aiter(), all(), a..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Built-in Functions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Built-in Constants" href="constants.html" />
    <link rel="prev" title="Introduction" href="intro.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/functions.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="intro.html"
                          title="previous chapter">Introduction</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="constants.html"
                          title="next chapter">Built-in Constants</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/functions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="constants.html" title="Built-in Constants"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Built-in Functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="built-in-functions">
<span id="built-in-funcs"></span><h1>Built-in Functions<a class="headerlink" href="#built-in-functions" title="Link to this heading">¶</a></h1>
<p>The Python interpreter has a number of functions and types built into it that
are always available.  They are listed here in alphabetical order.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head" colspan="4"><p>Built-in Functions</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><div class="line-block">
<div class="line"><strong>A</strong></div>
<div class="line"><a class="reference internal" href="#abs" title="abs"><code class="xref py py-func docutils literal notranslate"><span class="pre">abs()</span></code></a></div>
<div class="line"><a class="reference internal" href="#aiter" title="aiter"><code class="xref py py-func docutils literal notranslate"><span class="pre">aiter()</span></code></a></div>
<div class="line"><a class="reference internal" href="#all" title="all"><code class="xref py py-func docutils literal notranslate"><span class="pre">all()</span></code></a></div>
<div class="line"><a class="reference internal" href="#anext" title="anext"><code class="xref py py-func docutils literal notranslate"><span class="pre">anext()</span></code></a></div>
<div class="line"><a class="reference internal" href="#any" title="any"><code class="xref py py-func docutils literal notranslate"><span class="pre">any()</span></code></a></div>
<div class="line"><a class="reference internal" href="#ascii" title="ascii"><code class="xref py py-func docutils literal notranslate"><span class="pre">ascii()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>B</strong></div>
<div class="line"><a class="reference internal" href="#bin" title="bin"><code class="xref py py-func docutils literal notranslate"><span class="pre">bin()</span></code></a></div>
<div class="line"><a class="reference internal" href="#bool" title="bool"><code class="xref py py-func docutils literal notranslate"><span class="pre">bool()</span></code></a></div>
<div class="line"><a class="reference internal" href="#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-bytearray"><code class="docutils literal notranslate"><span class="pre">bytearray()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-bytes"><code class="docutils literal notranslate"><span class="pre">bytes()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>C</strong></div>
<div class="line"><a class="reference internal" href="#callable" title="callable"><code class="xref py py-func docutils literal notranslate"><span class="pre">callable()</span></code></a></div>
<div class="line"><a class="reference internal" href="#chr" title="chr"><code class="xref py py-func docutils literal notranslate"><span class="pre">chr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#classmethod" title="classmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">classmethod()</span></code></a></div>
<div class="line"><a class="reference internal" href="#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a></div>
<div class="line"><a class="reference internal" href="#complex" title="complex"><code class="xref py py-func docutils literal notranslate"><span class="pre">complex()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>D</strong></div>
<div class="line"><a class="reference internal" href="#delattr" title="delattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">delattr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-dict"><code class="docutils literal notranslate"><span class="pre">dict()</span></code></a></div>
<div class="line"><a class="reference internal" href="#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a></div>
<div class="line"><a class="reference internal" href="#divmod" title="divmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">divmod()</span></code></a></div>
<div class="line"><br /></div>
</div>
</td>
<td><div class="line-block">
<div class="line"><strong>E</strong></div>
<div class="line"><a class="reference internal" href="#enumerate" title="enumerate"><code class="xref py py-func docutils literal notranslate"><span class="pre">enumerate()</span></code></a></div>
<div class="line"><a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a></div>
<div class="line"><a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>F</strong></div>
<div class="line"><a class="reference internal" href="#filter" title="filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">filter()</span></code></a></div>
<div class="line"><a class="reference internal" href="#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a></div>
<div class="line"><a class="reference internal" href="#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-frozenset"><code class="docutils literal notranslate"><span class="pre">frozenset()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>G</strong></div>
<div class="line"><a class="reference internal" href="#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#globals" title="globals"><code class="xref py py-func docutils literal notranslate"><span class="pre">globals()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>H</strong></div>
<div class="line"><a class="reference internal" href="#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#hash" title="hash"><code class="xref py py-func docutils literal notranslate"><span class="pre">hash()</span></code></a></div>
<div class="line"><a class="reference internal" href="#help" title="help"><code class="xref py py-func docutils literal notranslate"><span class="pre">help()</span></code></a></div>
<div class="line"><a class="reference internal" href="#hex" title="hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">hex()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>I</strong></div>
<div class="line"><a class="reference internal" href="#id" title="id"><code class="xref py py-func docutils literal notranslate"><span class="pre">id()</span></code></a></div>
<div class="line"><a class="reference internal" href="#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a></div>
<div class="line"><a class="reference internal" href="#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a></div>
<div class="line"><a class="reference internal" href="#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a></div>
<div class="line"><a class="reference internal" href="#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a></div>
<div class="line"><a class="reference internal" href="#iter" title="iter"><code class="xref py py-func docutils literal notranslate"><span class="pre">iter()</span></code></a></div>
</div>
</td>
<td><div class="line-block">
<div class="line"><strong>L</strong></div>
<div class="line"><a class="reference internal" href="#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-list"><code class="docutils literal notranslate"><span class="pre">list()</span></code></a></div>
<div class="line"><a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>M</strong></div>
<div class="line"><a class="reference internal" href="#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a></div>
<div class="line"><a class="reference internal" href="#max" title="max"><code class="xref py py-func docutils literal notranslate"><span class="pre">max()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-memoryview"><code class="docutils literal notranslate"><span class="pre">memoryview()</span></code></a></div>
<div class="line"><a class="reference internal" href="#min" title="min"><code class="xref py py-func docutils literal notranslate"><span class="pre">min()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>N</strong></div>
<div class="line"><a class="reference internal" href="#next" title="next"><code class="xref py py-func docutils literal notranslate"><span class="pre">next()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>O</strong></div>
<div class="line"><a class="reference internal" href="#object" title="object"><code class="xref py py-func docutils literal notranslate"><span class="pre">object()</span></code></a></div>
<div class="line"><a class="reference internal" href="#oct" title="oct"><code class="xref py py-func docutils literal notranslate"><span class="pre">oct()</span></code></a></div>
<div class="line"><a class="reference internal" href="#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a></div>
<div class="line"><a class="reference internal" href="#ord" title="ord"><code class="xref py py-func docutils literal notranslate"><span class="pre">ord()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>P</strong></div>
<div class="line"><a class="reference internal" href="#pow" title="pow"><code class="xref py py-func docutils literal notranslate"><span class="pre">pow()</span></code></a></div>
<div class="line"><a class="reference internal" href="#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a></div>
<div class="line"><a class="reference internal" href="#property" title="property"><code class="xref py py-func docutils literal notranslate"><span class="pre">property()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><br /></div>
<div class="line"><br /></div>
<div class="line"><br /></div>
</div>
</td>
<td><div class="line-block">
<div class="line"><strong>R</strong></div>
<div class="line"><a class="reference internal" href="#func-range"><code class="docutils literal notranslate"><span class="pre">range()</span></code></a></div>
<div class="line"><a class="reference internal" href="#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#reversed" title="reversed"><code class="xref py py-func docutils literal notranslate"><span class="pre">reversed()</span></code></a></div>
<div class="line"><a class="reference internal" href="#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>S</strong></div>
<div class="line"><a class="reference internal" href="#func-set"><code class="docutils literal notranslate"><span class="pre">set()</span></code></a></div>
<div class="line"><a class="reference internal" href="#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a></div>
<div class="line"><a class="reference internal" href="#slice" title="slice"><code class="xref py py-func docutils literal notranslate"><span class="pre">slice()</span></code></a></div>
<div class="line"><a class="reference internal" href="#sorted" title="sorted"><code class="xref py py-func docutils literal notranslate"><span class="pre">sorted()</span></code></a></div>
<div class="line"><a class="reference internal" href="#staticmethod" title="staticmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">staticmethod()</span></code></a></div>
<div class="line"><a class="reference internal" href="#func-str"><code class="docutils literal notranslate"><span class="pre">str()</span></code></a></div>
<div class="line"><a class="reference internal" href="#sum" title="sum"><code class="xref py py-func docutils literal notranslate"><span class="pre">sum()</span></code></a></div>
<div class="line"><a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>T</strong></div>
<div class="line"><a class="reference internal" href="#func-tuple"><code class="docutils literal notranslate"><span class="pre">tuple()</span></code></a></div>
<div class="line"><a class="reference internal" href="#type" title="type"><code class="xref py py-func docutils literal notranslate"><span class="pre">type()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>V</strong></div>
<div class="line"><a class="reference internal" href="#vars" title="vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">vars()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>Z</strong></div>
<div class="line"><a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a></div>
<div class="line"><br /></div>
<div class="line"><strong>_</strong></div>
<div class="line"><a class="reference internal" href="#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a></div>
</div>
</td>
</tr>
</tbody>
</table>
<dl class="py function">
<dt class="sig sig-object py" id="abs">
<span class="sig-name descname"><span class="pre">abs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#abs" title="Link to this definition">¶</a></dt>
<dd><p>Return the absolute value of a number.  The argument may be an
integer, a floating point number, or an object implementing
<a class="reference internal" href="../reference/datamodel.html#object.__abs__" title="object.__abs__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__abs__()</span></code></a>.
If the argument is a complex number, its magnitude is returned.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="aiter">
<span class="sig-name descname"><span class="pre">aiter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">async_iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#aiter" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous iterator</span></a> for an <a class="reference internal" href="../glossary.html#term-asynchronous-iterable"><span class="xref std std-term">asynchronous iterable</span></a>.
Equivalent to calling <code class="docutils literal notranslate"><span class="pre">x.__aiter__()</span></code>.</p>
<p>Note: Unlike <a class="reference internal" href="#iter" title="iter"><code class="xref py py-func docutils literal notranslate"><span class="pre">iter()</span></code></a>, <a class="reference internal" href="#aiter" title="aiter"><code class="xref py py-func docutils literal notranslate"><span class="pre">aiter()</span></code></a> has no 2-argument variant.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="all">
<span class="sig-name descname"><span class="pre">all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#all" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if all elements of the <em>iterable</em> are true (or if the iterable
is empty).  Equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">all</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">element</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
    <span class="k">return</span> <span class="kc">True</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="anext">
<em class="property"><span class="pre">awaitable</span> </em><span class="sig-name descname"><span class="pre">anext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">async_iterator</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#anext" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">awaitable</span> </em><span class="sig-name descname"><span class="pre">anext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">async_iterator</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>When awaited, return the next item from the given <a class="reference internal" href="../glossary.html#term-asynchronous-iterator"><span class="xref std std-term">asynchronous
iterator</span></a>, or <em>default</em> if given and the iterator is exhausted.</p>
<p>This is the async variant of the <a class="reference internal" href="#next" title="next"><code class="xref py py-func docutils literal notranslate"><span class="pre">next()</span></code></a> builtin, and behaves
similarly.</p>
<p>This calls the <a class="reference internal" href="../reference/datamodel.html#object.__anext__" title="object.__anext__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__anext__()</span></code></a> method of <em>async_iterator</em>,
returning an <a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a>. Awaiting this returns the next value of the
iterator. If <em>default</em> is given, it is returned if the iterator is exhausted,
otherwise <a class="reference internal" href="exceptions.html#StopAsyncIteration" title="StopAsyncIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopAsyncIteration</span></code></a> is raised.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="any">
<span class="sig-name descname"><span class="pre">any</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#any" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if any element of the <em>iterable</em> is true.  If the iterable
is empty, return <code class="docutils literal notranslate"><span class="pre">False</span></code>.  Equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">any</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">element</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
    <span class="k">return</span> <span class="kc">False</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ascii">
<span class="sig-name descname"><span class="pre">ascii</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ascii" title="Link to this definition">¶</a></dt>
<dd><p>As <a class="reference internal" href="#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>, return a string containing a printable representation of an
object, but escape the non-ASCII characters in the string returned by
<a class="reference internal" href="#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> using <code class="docutils literal notranslate"><span class="pre">\x</span></code>, <code class="docutils literal notranslate"><span class="pre">\u</span></code>, or <code class="docutils literal notranslate"><span class="pre">\U</span></code> escapes.  This generates a string
similar to that returned by <a class="reference internal" href="#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> in Python 2.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="bin">
<span class="sig-name descname"><span class="pre">bin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#bin" title="Link to this definition">¶</a></dt>
<dd><p>Convert an integer number to a binary string prefixed with “0b”. The result
is a valid Python expression. If <em>x</em> is not a Python <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> object, it
has to define an <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> method that returns an integer. Some
examples:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">bin</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="go">&#39;0b11&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bin</span><span class="p">(</span><span class="o">-</span><span class="mi">10</span><span class="p">)</span>
<span class="go">&#39;-0b1010&#39;</span>
</pre></div>
</div>
<p>If the prefix “0b” is desired or not, you can use either of the following ways.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="mi">14</span><span class="p">,</span> <span class="s1">&#39;#b&#39;</span><span class="p">),</span> <span class="nb">format</span><span class="p">(</span><span class="mi">14</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">)</span>
<span class="go">(&#39;0b1110&#39;, &#39;1110&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">14</span><span class="si">:</span><span class="s1">#b</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">14</span><span class="si">:</span><span class="s1">b</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="go">(&#39;0b1110&#39;, &#39;1110&#39;)</span>
</pre></div>
</div>
<p>See also <a class="reference internal" href="#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> for more information.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="bool">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bool</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#bool" title="Link to this definition">¶</a></dt>
<dd><p>Return a Boolean value, i.e. one of <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code>.  <em>x</em> is converted
using the standard <a class="reference internal" href="stdtypes.html#truth"><span class="std std-ref">truth testing procedure</span></a>.  If <em>x</em> is false
or omitted, this returns <code class="docutils literal notranslate"><span class="pre">False</span></code>; otherwise, it returns <code class="docutils literal notranslate"><span class="pre">True</span></code>.  The
<a class="reference internal" href="#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a> class is a subclass of <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> (see <a class="reference internal" href="stdtypes.html#typesnumeric"><span class="std std-ref">Numeric Types — int, float, complex</span></a>).
It cannot be subclassed further.  Its only instances are <code class="docutils literal notranslate"><span class="pre">False</span></code> and
<code class="docutils literal notranslate"><span class="pre">True</span></code> (see <a class="reference internal" href="stdtypes.html#typebool"><span class="std std-ref">Boolean Type - bool</span></a>).</p>
<div class="versionchanged" id="index-0">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>x</em> is now a positional-only parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="breakpoint">
<span class="sig-name descname"><span class="pre">breakpoint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kws</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#breakpoint" title="Link to this definition">¶</a></dt>
<dd><p>This function drops you into the debugger at the call site.  Specifically,
it calls <a class="reference internal" href="sys.html#sys.breakpointhook" title="sys.breakpointhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code></a>, passing <code class="docutils literal notranslate"><span class="pre">args</span></code> and <code class="docutils literal notranslate"><span class="pre">kws</span></code> straight
through.  By default, <code class="docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code> calls
<a class="reference internal" href="pdb.html#pdb.set_trace" title="pdb.set_trace"><code class="xref py py-func docutils literal notranslate"><span class="pre">pdb.set_trace()</span></code></a> expecting no arguments.  In this case, it is
purely a convenience function so you don’t have to explicitly import
<a class="reference internal" href="pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> or type as much code to enter the debugger.  However,
<a class="reference internal" href="sys.html#sys.breakpointhook" title="sys.breakpointhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code></a> can be set to some other function and
<a class="reference internal" href="#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a> will automatically call that, allowing you to drop into
the debugger of choice.
If <a class="reference internal" href="sys.html#sys.breakpointhook" title="sys.breakpointhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code></a> is not accessible, this function will
raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.</p>
<p>By default, the behavior of <a class="reference internal" href="#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a> can be changed with
the <span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONBREAKPOINT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONBREAKPOINT</span></code></a> environment variable.
See <a class="reference internal" href="sys.html#sys.breakpointhook" title="sys.breakpointhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code></a> for usage details.</p>
<p>Note that this is not guaranteed if <a class="reference internal" href="sys.html#sys.breakpointhook" title="sys.breakpointhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code></a>
has been replaced.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">builtins.breakpoint</span></code> with argument <code class="docutils literal notranslate"><span class="pre">breakpointhook</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py class" id="func-bytearray">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytearray</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytearray</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytearray</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a new array of bytes.  The <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> class is a mutable
sequence of integers in the range 0 &lt;= x &lt; 256.  It has most of the usual
methods of mutable sequences, described in <a class="reference internal" href="stdtypes.html#typesseq-mutable"><span class="std std-ref">Mutable Sequence Types</span></a>, as well
as most methods that the <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> type has, see <a class="reference internal" href="stdtypes.html#bytes-methods"><span class="std std-ref">Bytes and Bytearray Operations</span></a>.</p>
<p>The optional <em>source</em> parameter can be used to initialize the array in a few
different ways:</p>
<ul class="simple">
<li><p>If it is a <em>string</em>, you must also give the <em>encoding</em> (and optionally,
<em>errors</em>) parameters; <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-func docutils literal notranslate"><span class="pre">bytearray()</span></code></a> then converts the string to
bytes using <a class="reference internal" href="stdtypes.html#str.encode" title="str.encode"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.encode()</span></code></a>.</p></li>
<li><p>If it is an <em>integer</em>, the array will have that size and will be
initialized with null bytes.</p></li>
<li><p>If it is an object conforming to the <a class="reference internal" href="../c-api/buffer.html#bufferobjects"><span class="std std-ref">buffer interface</span></a>,
a read-only buffer of the object will be used to initialize the bytes array.</p></li>
<li><p>If it is an <em>iterable</em>, it must be an iterable of integers in the range
<code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">&lt;=</span> <span class="pre">x</span> <span class="pre">&lt;</span> <span class="pre">256</span></code>, which are used as the initial contents of the array.</p></li>
</ul>
<p>Without an argument, an array of size 0 is created.</p>
<p>See also <a class="reference internal" href="stdtypes.html#binaryseq"><span class="std std-ref">Binary Sequence Types — bytes, bytearray, memoryview</span></a> and <a class="reference internal" href="stdtypes.html#typebytearray"><span class="std std-ref">Bytearray Objects</span></a>.</p>
</dd></dl>

<dl class="py class" id="func-bytes">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a new “bytes” object which is an immutable sequence of integers in
the range <code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">&lt;=</span> <span class="pre">x</span> <span class="pre">&lt;</span> <span class="pre">256</span></code>.  <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> is an immutable version of
<a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> – it has the same non-mutating methods and the same
indexing and slicing behavior.</p>
<p>Accordingly, constructor arguments are interpreted as for <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-func docutils literal notranslate"><span class="pre">bytearray()</span></code></a>.</p>
<p>Bytes objects can also be created with literals, see <a class="reference internal" href="../reference/lexical_analysis.html#strings"><span class="std std-ref">String and Bytes literals</span></a>.</p>
<p>See also <a class="reference internal" href="stdtypes.html#binaryseq"><span class="std std-ref">Binary Sequence Types — bytes, bytearray, memoryview</span></a>, <a class="reference internal" href="stdtypes.html#typebytes"><span class="std std-ref">Bytes Objects</span></a>, and <a class="reference internal" href="stdtypes.html#bytes-methods"><span class="std std-ref">Bytes and Bytearray Operations</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="callable">
<span class="sig-name descname"><span class="pre">callable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#callable" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if the <em>object</em> argument appears callable,
<a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> if not.  If this returns <code class="docutils literal notranslate"><span class="pre">True</span></code>, it is still possible that a
call fails, but if it is <code class="docutils literal notranslate"><span class="pre">False</span></code>, calling <em>object</em> will never succeed.
Note that classes are callable (calling a class returns a new instance);
instances are callable if their class has a <a class="reference internal" href="../reference/datamodel.html#object.__call__" title="object.__call__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__call__()</span></code></a> method.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>This function was first removed in Python 3.0 and then brought back
in Python 3.2.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="chr">
<span class="sig-name descname"><span class="pre">chr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">i</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#chr" title="Link to this definition">¶</a></dt>
<dd><p>Return the string representing a character whose Unicode code point is the
integer <em>i</em>.  For example, <code class="docutils literal notranslate"><span class="pre">chr(97)</span></code> returns the string <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, while
<code class="docutils literal notranslate"><span class="pre">chr(8364)</span></code> returns the string <code class="docutils literal notranslate"><span class="pre">'€'</span></code>. This is the inverse of <a class="reference internal" href="#ord" title="ord"><code class="xref py py-func docutils literal notranslate"><span class="pre">ord()</span></code></a>.</p>
<p>The valid range for the argument is from 0 through 1,114,111 (0x10FFFF in
base 16).  <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised if <em>i</em> is outside that range.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="classmethod">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-name descname"><span class="pre">classmethod</span></span><a class="headerlink" href="#classmethod" title="Link to this definition">¶</a></dt>
<dd><p>Transform a method into a class method.</p>
<p>A class method receives the class as an implicit first argument, just like an
instance method receives the instance. To declare a class method, use this
idiom:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">):</span> <span class="o">...</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&#64;classmethod</span></code> form is a function <a class="reference internal" href="../glossary.html#term-decorator"><span class="xref std std-term">decorator</span></a> – see
<a class="reference internal" href="../reference/compound_stmts.html#function"><span class="std std-ref">Function definitions</span></a> for details.</p>
<p>A class method can be called either on the class (such as <code class="docutils literal notranslate"><span class="pre">C.f()</span></code>) or on an instance (such
as <code class="docutils literal notranslate"><span class="pre">C().f()</span></code>).  The instance is ignored except for its class. If a class
method is called for a derived class, the derived class object is passed as the
implied first argument.</p>
<p>Class methods are different than C++ or Java static methods. If you want those,
see <a class="reference internal" href="#staticmethod" title="staticmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">staticmethod()</span></code></a> in this section.
For more information on class methods, see <a class="reference internal" href="../reference/datamodel.html#types"><span class="std std-ref">The standard type hierarchy</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Class methods can now wrap other <a class="reference internal" href="../glossary.html#term-descriptor"><span class="xref std std-term">descriptors</span></a> such as
<a class="reference internal" href="#property" title="property"><code class="xref py py-func docutils literal notranslate"><span class="pre">property()</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Class methods now inherit the method attributes (<code class="docutils literal notranslate"><span class="pre">__module__</span></code>,
<code class="docutils literal notranslate"><span class="pre">__name__</span></code>, <code class="docutils literal notranslate"><span class="pre">__qualname__</span></code>, <code class="docutils literal notranslate"><span class="pre">__doc__</span></code> and <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code>) and
have a new <code class="docutils literal notranslate"><span class="pre">__wrapped__</span></code> attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Class methods can no longer wrap other <a class="reference internal" href="../glossary.html#term-descriptor"><span class="xref std std-term">descriptors</span></a> such as
<a class="reference internal" href="#property" title="property"><code class="xref py py-func docutils literal notranslate"><span class="pre">property()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="compile">
<span class="sig-name descname"><span class="pre">compile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dont_inherit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optimize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#compile" title="Link to this definition">¶</a></dt>
<dd><p>Compile the <em>source</em> into a code or AST object.  Code objects can be executed
by <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a> or <a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>.  <em>source</em> can either be a normal string, a
byte string, or an AST object.  Refer to the <a class="reference internal" href="ast.html#module-ast" title="ast: Abstract Syntax Tree classes and manipulation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ast</span></code></a> module documentation
for information on how to work with AST objects.</p>
<p>The <em>filename</em> argument should give the file from which the code was read;
pass some recognizable value if it wasn’t read from a file (<code class="docutils literal notranslate"><span class="pre">'&lt;string&gt;'</span></code> is
commonly used).</p>
<p>The <em>mode</em> argument specifies what kind of code must be compiled; it can be
<code class="docutils literal notranslate"><span class="pre">'exec'</span></code> if <em>source</em> consists of a sequence of statements, <code class="docutils literal notranslate"><span class="pre">'eval'</span></code> if it
consists of a single expression, or <code class="docutils literal notranslate"><span class="pre">'single'</span></code> if it consists of a single
interactive statement (in the latter case, expression statements that
evaluate to something other than <code class="docutils literal notranslate"><span class="pre">None</span></code> will be printed).</p>
<p>The optional arguments <em>flags</em> and <em>dont_inherit</em> control which
<a class="reference internal" href="ast.html#ast-compiler-flags"><span class="std std-ref">compiler options</span></a> should be activated
and which <a class="reference internal" href="../reference/simple_stmts.html#future"><span class="std std-ref">future features</span></a> should be allowed. If neither
is present (or both are zero) the code is compiled with the same flags that
affect the code that is calling <a class="reference internal" href="#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>. If the <em>flags</em>
argument is given and <em>dont_inherit</em> is not (or is zero) then the compiler
options and the future statements specified by the <em>flags</em> argument are used
in addition to those that would be used anyway. If <em>dont_inherit</em> is a
non-zero integer then the <em>flags</em> argument is it – the flags (future
features and compiler options) in the surrounding code are ignored.</p>
<p>Compiler options and future statements are specified by bits which can be
bitwise ORed together to specify multiple options. The bitfield required to
specify a given future feature can be found as the
<a class="reference internal" href="__future__.html#future__._Feature.compiler_flag" title="__future__._Feature.compiler_flag"><code class="xref py py-attr docutils literal notranslate"><span class="pre">compiler_flag</span></code></a> attribute on the
<a class="reference internal" href="__future__.html#future__._Feature" title="__future__._Feature"><code class="xref py py-class docutils literal notranslate"><span class="pre">_Feature</span></code></a> instance in the <a class="reference internal" href="__future__.html#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a> module.
<a class="reference internal" href="ast.html#ast-compiler-flags"><span class="std std-ref">Compiler flags</span></a> can be found in <a class="reference internal" href="ast.html#module-ast" title="ast: Abstract Syntax Tree classes and manipulation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ast</span></code></a>
module, with <code class="docutils literal notranslate"><span class="pre">PyCF_</span></code> prefix.</p>
<p>The argument <em>optimize</em> specifies the optimization level of the compiler; the
default value of <code class="docutils literal notranslate"><span class="pre">-1</span></code> selects the optimization level of the interpreter as
given by <a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">-O</span></code></a> options.  Explicit levels are <code class="docutils literal notranslate"><span class="pre">0</span></code> (no optimization;
<code class="docutils literal notranslate"><span class="pre">__debug__</span></code> is true), <code class="docutils literal notranslate"><span class="pre">1</span></code> (asserts are removed, <code class="docutils literal notranslate"><span class="pre">__debug__</span></code> is false)
or <code class="docutils literal notranslate"><span class="pre">2</span></code> (docstrings are removed too).</p>
<p>This function raises <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> if the compiled source is invalid,
and <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if the source contains null bytes.</p>
<p>If you want to parse Python code into its AST representation, see
<a class="reference internal" href="ast.html#ast.parse" title="ast.parse"><code class="xref py py-func docutils literal notranslate"><span class="pre">ast.parse()</span></code></a>.</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">compile</span></code> with arguments
<code class="docutils literal notranslate"><span class="pre">source</span></code> and <code class="docutils literal notranslate"><span class="pre">filename</span></code>. This event may also be raised by implicit
compilation.</p>
</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When compiling a string with multi-line code in <code class="docutils literal notranslate"><span class="pre">'single'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'eval'</span></code> mode, input must be terminated by at least one newline
character.  This is to facilitate detection of incomplete and complete
statements in the <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-mod docutils literal notranslate"><span class="pre">code</span></code></a> module.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>It is possible to crash the Python interpreter with a
sufficiently large/complex string when compiling to an AST
object due to stack depth limitations in Python’s AST compiler.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Allowed use of Windows and Mac newlines.  Also, input in <code class="docutils literal notranslate"><span class="pre">'exec'</span></code> mode
does not have to end in a newline anymore.  Added the <em>optimize</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Previously, <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> was raised when null bytes were encountered
in <em>source</em>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span><code class="docutils literal notranslate"><span class="pre">ast.PyCF_ALLOW_TOP_LEVEL_AWAIT</span></code> can now be passed in flags to enable
support for top-level <code class="docutils literal notranslate"><span class="pre">await</span></code>, <code class="docutils literal notranslate"><span class="pre">async</span> <span class="pre">for</span></code>, and <code class="docutils literal notranslate"><span class="pre">async</span> <span class="pre">with</span></code>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="complex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">complex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">real</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">imag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#complex" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">complex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a complex number with the value <em>real</em> + <em>imag</em>*1j or convert a string
or number to a complex number.  If the first parameter is a string, it will
be interpreted as a complex number and the function must be called without a
second parameter.  The second parameter can never be a string. Each argument
may be any numeric type (including complex).  If <em>imag</em> is omitted, it
defaults to zero and the constructor serves as a numeric conversion like
<a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> and <a class="reference internal" href="#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.  If both arguments are omitted, returns
<code class="docutils literal notranslate"><span class="pre">0j</span></code>.</p>
<p>For a general Python object <code class="docutils literal notranslate"><span class="pre">x</span></code>, <code class="docutils literal notranslate"><span class="pre">complex(x)</span></code> delegates to
<code class="docutils literal notranslate"><span class="pre">x.__complex__()</span></code>.  If <a class="reference internal" href="../reference/datamodel.html#object.__complex__" title="object.__complex__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__complex__()</span></code></a> is not defined then it falls back
to <a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a>.  If <code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code> is not defined then it falls back
to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When converting from a string, the string must not contain whitespace
around the central <code class="docutils literal notranslate"><span class="pre">+</span></code> or <code class="docutils literal notranslate"><span class="pre">-</span></code> operator.  For example,
<code class="docutils literal notranslate"><span class="pre">complex('1+2j')</span></code> is fine, but <code class="docutils literal notranslate"><span class="pre">complex('1</span> <span class="pre">+</span> <span class="pre">2j')</span></code> raises
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</div>
<p>The complex type is described in <a class="reference internal" href="stdtypes.html#typesnumeric"><span class="std std-ref">Numeric Types — int, float, complex</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Grouping digits with underscores as in code literals is allowed.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Falls back to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> if <a class="reference internal" href="../reference/datamodel.html#object.__complex__" title="object.__complex__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__complex__()</span></code></a> and
<a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a> are not defined.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="delattr">
<span class="sig-name descname"><span class="pre">delattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#delattr" title="Link to this definition">¶</a></dt>
<dd><p>This is a relative of <a class="reference internal" href="#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a>.  The arguments are an object and a
string.  The string must be the name of one of the object’s attributes.  The
function deletes the named attribute, provided the object allows it.  For
example, <code class="docutils literal notranslate"><span class="pre">delattr(x,</span> <span class="pre">'foobar')</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">del</span> <span class="pre">x.foobar</span></code>.
<em>name</em> need not be a Python identifier (see <a class="reference internal" href="#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a>).</p>
</dd></dl>

<dl class="py class" id="func-dict">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">dict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwarg</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">dict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mapping</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwarg</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">dict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwarg</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Create a new dictionary.  The <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> object is the dictionary class.
See <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> and <a class="reference internal" href="stdtypes.html#typesmapping"><span class="std std-ref">Mapping Types — dict</span></a> for documentation about this class.</p>
<p>For other containers see the built-in <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>, <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a>, and
<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> classes, as well as the <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a> module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="dir">
<span class="sig-name descname"><span class="pre">dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#dir" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">dir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Without arguments, return the list of names in the current local scope.  With an
argument, attempt to return a list of valid attributes for that object.</p>
<p>If the object has a method named <a class="reference internal" href="../reference/datamodel.html#object.__dir__" title="object.__dir__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__dir__()</span></code></a>,
this method will be called and
must return the list of attributes. This allows objects that implement a custom
<a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__getattr__()</span></code></a> or <a class="reference internal" href="../reference/datamodel.html#object.__getattribute__" title="object.__getattribute__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__getattribute__()</span></code></a> function
to customize the way
<a class="reference internal" href="#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a> reports their attributes.</p>
<p>If the object does not provide <a class="reference internal" href="../reference/datamodel.html#object.__dir__" title="object.__dir__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__dir__()</span></code></a>,
the function tries its best to gather information from the object’s
<a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute, if defined, and
from its type object.  The resulting list is not necessarily complete and may
be inaccurate when the object has a custom <a class="reference internal" href="../reference/datamodel.html#object.__getattr__" title="object.__getattr__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__getattr__()</span></code></a>.</p>
<p>The default <a class="reference internal" href="#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a> mechanism behaves differently with different types of
objects, as it attempts to produce the most relevant, rather than complete,
information:</p>
<ul class="simple">
<li><p>If the object is a module object, the list contains the names of the module’s
attributes.</p></li>
<li><p>If the object is a type or class object, the list contains the names of its
attributes, and recursively of the attributes of its bases.</p></li>
<li><p>Otherwise, the list contains the object’s attributes’ names, the names of its
class’s attributes, and recursively of the attributes of its class’s base
classes.</p></li>
</ul>
<p>The resulting list is sorted alphabetically.  For example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">struct</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">()</span>   <span class="c1"># show the names in the module namespace  </span>
<span class="go">[&#39;__builtins__&#39;, &#39;__name__&#39;, &#39;struct&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">struct</span><span class="p">)</span>   <span class="c1"># show the names in the struct module </span>
<span class="go">[&#39;Struct&#39;, &#39;__all__&#39;, &#39;__builtins__&#39;, &#39;__cached__&#39;, &#39;__doc__&#39;, &#39;__file__&#39;,</span>
<span class="go"> &#39;__initializing__&#39;, &#39;__loader__&#39;, &#39;__name__&#39;, &#39;__package__&#39;,</span>
<span class="go"> &#39;_clearcache&#39;, &#39;calcsize&#39;, &#39;error&#39;, &#39;pack&#39;, &#39;pack_into&#39;,</span>
<span class="go"> &#39;unpack&#39;, &#39;unpack_from&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Shape</span><span class="p">:</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__dir__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="p">[</span><span class="s1">&#39;area&#39;</span><span class="p">,</span> <span class="s1">&#39;perimeter&#39;</span><span class="p">,</span> <span class="s1">&#39;location&#39;</span><span class="p">]</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">Shape</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="go">[&#39;area&#39;, &#39;location&#39;, &#39;perimeter&#39;]</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Because <a class="reference internal" href="#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a> is supplied primarily as a convenience for use at an
interactive prompt, it tries to supply an interesting set of names more
than it tries to supply a rigorously or consistently defined set of names,
and its detailed behavior may change across releases.  For example,
metaclass attributes are not in the result list when the argument is a
class.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="divmod">
<span class="sig-name descname"><span class="pre">divmod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#divmod" title="Link to this definition">¶</a></dt>
<dd><p>Take two (non-complex) numbers as arguments and return a pair of numbers
consisting of their quotient and remainder when using integer division.  With
mixed operand types, the rules for binary arithmetic operators apply.  For
integers, the result is the same as <code class="docutils literal notranslate"><span class="pre">(a</span> <span class="pre">//</span> <span class="pre">b,</span> <span class="pre">a</span> <span class="pre">%</span> <span class="pre">b)</span></code>. For floating point
numbers the result is <code class="docutils literal notranslate"><span class="pre">(q,</span> <span class="pre">a</span> <span class="pre">%</span> <span class="pre">b)</span></code>, where <em>q</em> is usually <code class="docutils literal notranslate"><span class="pre">math.floor(a</span> <span class="pre">/</span>
<span class="pre">b)</span></code> but may be 1 less than that.  In any case <code class="docutils literal notranslate"><span class="pre">q</span> <span class="pre">*</span> <span class="pre">b</span> <span class="pre">+</span> <span class="pre">a</span> <span class="pre">%</span> <span class="pre">b</span></code> is very
close to <em>a</em>, if <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">%</span> <span class="pre">b</span></code> is non-zero it has the same sign as <em>b</em>, and <code class="docutils literal notranslate"><span class="pre">0</span>
<span class="pre">&lt;=</span> <span class="pre">abs(a</span> <span class="pre">%</span> <span class="pre">b)</span> <span class="pre">&lt;</span> <span class="pre">abs(b)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enumerate">
<span class="sig-name descname"><span class="pre">enumerate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enumerate" title="Link to this definition">¶</a></dt>
<dd><p>Return an enumerate object. <em>iterable</em> must be a sequence, an
<a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>, or some other object which supports iteration.
The <a class="reference internal" href="stdtypes.html#iterator.__next__" title="iterator.__next__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__next__()</span></code></a> method of the iterator returned by
<a class="reference internal" href="#enumerate" title="enumerate"><code class="xref py py-func docutils literal notranslate"><span class="pre">enumerate()</span></code></a> returns a tuple containing a count (from <em>start</em> which
defaults to 0) and the values obtained from iterating over <em>iterable</em>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">seasons</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;Spring&#39;</span><span class="p">,</span> <span class="s1">&#39;Summer&#39;</span><span class="p">,</span> <span class="s1">&#39;Fall&#39;</span><span class="p">,</span> <span class="s1">&#39;Winter&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">enumerate</span><span class="p">(</span><span class="n">seasons</span><span class="p">))</span>
<span class="go">[(0, &#39;Spring&#39;), (1, &#39;Summer&#39;), (2, &#39;Fall&#39;), (3, &#39;Winter&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">enumerate</span><span class="p">(</span><span class="n">seasons</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>
<span class="go">[(1, &#39;Spring&#39;), (2, &#39;Summer&#39;), (3, &#39;Fall&#39;), (4, &#39;Winter&#39;)]</span>
</pre></div>
</div>
<p>Equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">enumerate</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
    <span class="n">n</span> <span class="o">=</span> <span class="n">start</span>
    <span class="k">for</span> <span class="n">elem</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">n</span><span class="p">,</span> <span class="n">elem</span>
        <span class="n">n</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function" id="func-eval">
<dt class="sig sig-object py" id="eval">
<span class="sig-name descname"><span class="pre">eval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#eval" title="Link to this definition">¶</a></dt>
<dd><p>The arguments are a string and optional globals and locals.  If provided,
<em>globals</em> must be a dictionary.  If provided, <em>locals</em> can be any mapping
object.</p>
<p>The <em>expression</em> argument is parsed and evaluated as a Python expression
(technically speaking, a condition list) using the <em>globals</em> and <em>locals</em>
dictionaries as global and local namespace.  If the <em>globals</em> dictionary is
present and does not contain a value for the key <code class="docutils literal notranslate"><span class="pre">__builtins__</span></code>, a
reference to the dictionary of the built-in module <a class="reference internal" href="builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a> is
inserted under that key before <em>expression</em> is parsed.  That way you can
control what builtins are available to the executed code by inserting your
own <code class="docutils literal notranslate"><span class="pre">__builtins__</span></code> dictionary into <em>globals</em> before passing it to
<a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>.  If the <em>locals</em> dictionary is omitted it defaults to the
<em>globals</em> dictionary.  If both dictionaries are omitted, the expression is
executed with the <em>globals</em> and <em>locals</em> in the environment where
<a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> is called.  Note, <em>eval()</em> does not have access to the
<a class="reference internal" href="../glossary.html#term-nested-scope"><span class="xref std std-term">nested scopes</span></a> (non-locals) in the enclosing
environment.</p>
<p>The return value is the result of
the evaluated expression. Syntax errors are reported as exceptions.  Example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">eval</span><span class="p">(</span><span class="s1">&#39;x+1&#39;</span><span class="p">)</span>
<span class="go">2</span>
</pre></div>
</div>
<p>This function can also be used to execute arbitrary code objects (such as
those created by <a class="reference internal" href="#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>).  In this case, pass a code object instead
of a string.  If the code object has been compiled with <code class="docutils literal notranslate"><span class="pre">'exec'</span></code> as the
<em>mode</em> argument, <a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>'s return value will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>Hints: dynamic execution of statements is supported by the <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>
function.  The <a class="reference internal" href="#globals" title="globals"><code class="xref py py-func docutils literal notranslate"><span class="pre">globals()</span></code></a> and <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> functions
return the current global and local dictionary, respectively, which may be
useful to pass around for use by <a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> or <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>.</p>
<p>If the given source is a string, then leading and trailing spaces and tabs
are stripped.</p>
<p>See <a class="reference internal" href="ast.html#ast.literal_eval" title="ast.literal_eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">ast.literal_eval()</span></code></a> for a function that can safely evaluate strings
with expressions containing only literals.</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">exec</span></code> with the code object
as the argument. Code compilation events may also be raised.</p>
</p>
</dd></dl>

<dl class="py function" id="index-2">
<dt class="sig sig-object py" id="exec">
<span class="sig-name descname"><span class="pre">exec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">closure</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#exec" title="Link to this definition">¶</a></dt>
<dd><p>This function supports dynamic execution of Python code. <em>object</em> must be
either a string or a code object.  If it is a string, the string is parsed as
a suite of Python statements which is then executed (unless a syntax error
occurs). <a class="footnote-reference brackets" href="#id2" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> If it is a code object, it is simply executed.  In all cases,
the code that’s executed is expected to be valid as file input (see the
section <a class="reference internal" href="../reference/toplevel_components.html#file-input"><span class="std std-ref">File input</span></a> in the Reference Manual). Be aware that the
<a class="reference internal" href="../reference/simple_stmts.html#nonlocal"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">nonlocal</span></code></a>, <a class="reference internal" href="../reference/simple_stmts.html#yield"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">yield</span></code></a>,  and <a class="reference internal" href="../reference/simple_stmts.html#return"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">return</span></code></a>
statements may not be used outside of
function definitions even within the context of code passed to the
<a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a> function. The return value is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>In all cases, if the optional parts are omitted, the code is executed in the
current scope.  If only <em>globals</em> is provided, it must be a dictionary
(and not a subclass of dictionary), which
will be used for both the global and the local variables.  If <em>globals</em> and
<em>locals</em> are given, they are used for the global and local variables,
respectively.  If provided, <em>locals</em> can be any mapping object.  Remember
that at the module level, globals and locals are the same dictionary. If exec
gets two separate objects as <em>globals</em> and <em>locals</em>, the code will be
executed as if it were embedded in a class definition.</p>
<p>If the <em>globals</em> dictionary does not contain a value for the key
<code class="docutils literal notranslate"><span class="pre">__builtins__</span></code>, a reference to the dictionary of the built-in module
<a class="reference internal" href="builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a> is inserted under that key.  That way you can control what
builtins are available to the executed code by inserting your own
<code class="docutils literal notranslate"><span class="pre">__builtins__</span></code> dictionary into <em>globals</em> before passing it to <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>.</p>
<p>The <em>closure</em> argument specifies a closure–a tuple of cellvars.
It’s only valid when the <em>object</em> is a code object containing free variables.
The length of the tuple must exactly match the number of free variables
referenced by the code object.</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">exec</span></code> with the code object
as the argument. Code compilation events may also be raised.</p>
</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The built-in functions <a class="reference internal" href="#globals" title="globals"><code class="xref py py-func docutils literal notranslate"><span class="pre">globals()</span></code></a> and <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> return the current
global and local dictionary, respectively, which may be useful to pass around
for use as the second and third argument to <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The default <em>locals</em> act as described for function <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> below:
modifications to the default <em>locals</em> dictionary should not be attempted.
Pass an explicit <em>locals</em> dictionary if you need to see effects of the
code on <em>locals</em> after function <a class="reference internal" href="#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a> returns.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <em>closure</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="filter">
<span class="sig-name descname"><span class="pre">filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#filter" title="Link to this definition">¶</a></dt>
<dd><p>Construct an iterator from those elements of <em>iterable</em> for which <em>function</em>
is true.  <em>iterable</em> may be either a sequence, a container which
supports iteration, or an iterator.  If <em>function</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the identity
function is assumed, that is, all elements of <em>iterable</em> that are false are
removed.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">filter(function,</span> <span class="pre">iterable)</span></code> is equivalent to the generator
expression <code class="docutils literal notranslate"><span class="pre">(item</span> <span class="pre">for</span> <span class="pre">item</span> <span class="pre">in</span> <span class="pre">iterable</span> <span class="pre">if</span> <span class="pre">function(item))</span></code> if function is
not <code class="docutils literal notranslate"><span class="pre">None</span></code> and <code class="docutils literal notranslate"><span class="pre">(item</span> <span class="pre">for</span> <span class="pre">item</span> <span class="pre">in</span> <span class="pre">iterable</span> <span class="pre">if</span> <span class="pre">item)</span></code> if function is
<code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>See <a class="reference internal" href="itertools.html#itertools.filterfalse" title="itertools.filterfalse"><code class="xref py py-func docutils literal notranslate"><span class="pre">itertools.filterfalse()</span></code></a> for the complementary function that returns
elements of <em>iterable</em> for which <em>function</em> is false.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="float">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">float</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#float" title="Link to this definition">¶</a></dt>
<dd><p id="index-3">Return a floating point number constructed from a number or string <em>x</em>.</p>
<p>If the argument is a string, it should contain a decimal number, optionally
preceded by a sign, and optionally embedded in whitespace.  The optional
sign may be <code class="docutils literal notranslate"><span class="pre">'+'</span></code> or <code class="docutils literal notranslate"><span class="pre">'-'</span></code>; a <code class="docutils literal notranslate"><span class="pre">'+'</span></code> sign has no effect on the value
produced.  The argument may also be a string representing a NaN
(not-a-number), or positive or negative infinity.  More precisely, the
input must conform to the <code class="docutils literal notranslate"><span class="pre">floatvalue</span></code> production rule in the following
grammar, after leading and trailing whitespace characters are removed:</p>
<pre>
<strong id="grammar-token-float-sign">sign       </strong> ::=  &quot;+&quot; | &quot;-&quot;
<strong id="grammar-token-float-infinity">infinity   </strong> ::=  &quot;Infinity&quot; | &quot;inf&quot;
<strong id="grammar-token-float-nan">nan        </strong> ::=  &quot;nan&quot;
<strong id="grammar-token-float-digit">digit      </strong> ::=  &lt;a Unicode decimal digit, i.e. characters in Unicode general category Nd&gt;
<strong id="grammar-token-float-digitpart">digitpart  </strong> ::=  <a class="reference internal" href="#grammar-token-float-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a> ([&quot;_&quot;] <a class="reference internal" href="#grammar-token-float-digit"><code class="xref docutils literal notranslate"><span class="pre">digit</span></code></a>)*
<strong id="grammar-token-float-number">number     </strong> ::=  [<a class="reference internal" href="#grammar-token-float-digitpart"><code class="xref docutils literal notranslate"><span class="pre">digitpart</span></code></a>] &quot;.&quot; <a class="reference internal" href="#grammar-token-float-digitpart"><code class="xref docutils literal notranslate"><span class="pre">digitpart</span></code></a> | <a class="reference internal" href="#grammar-token-float-digitpart"><code class="xref docutils literal notranslate"><span class="pre">digitpart</span></code></a> [&quot;.&quot;]
<strong id="grammar-token-float-exponent">exponent   </strong> ::=  (&quot;e&quot; | &quot;E&quot;) [&quot;+&quot; | &quot;-&quot;] <a class="reference internal" href="#grammar-token-float-digitpart"><code class="xref docutils literal notranslate"><span class="pre">digitpart</span></code></a>
<strong id="grammar-token-float-floatnumber">floatnumber</strong> ::=  number [<a class="reference internal" href="#grammar-token-float-exponent"><code class="xref docutils literal notranslate"><span class="pre">exponent</span></code></a>]
<strong id="grammar-token-float-floatvalue">floatvalue </strong> ::=  [<a class="reference internal" href="#grammar-token-float-sign"><code class="xref docutils literal notranslate"><span class="pre">sign</span></code></a>] (<a class="reference internal" href="#grammar-token-float-floatnumber"><code class="xref docutils literal notranslate"><span class="pre">floatnumber</span></code></a> | <a class="reference internal" href="#grammar-token-float-infinity"><code class="xref docutils literal notranslate"><span class="pre">infinity</span></code></a> | <a class="reference internal" href="#grammar-token-float-nan"><code class="xref docutils literal notranslate"><span class="pre">nan</span></code></a>)
</pre>
<p>Case is not significant, so, for example, “inf”, “Inf”, “INFINITY”, and
“iNfINity” are all acceptable spellings for positive infinity.</p>
<p>Otherwise, if the argument is an integer or a floating point number, a
floating point number with the same value (within Python’s floating point
precision) is returned.  If the argument is outside the range of a Python
float, an <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> will be raised.</p>
<p>For a general Python object <code class="docutils literal notranslate"><span class="pre">x</span></code>, <code class="docutils literal notranslate"><span class="pre">float(x)</span></code> delegates to
<code class="docutils literal notranslate"><span class="pre">x.__float__()</span></code>.  If <a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a> is not defined then it falls back
to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a>.</p>
<p>If no argument is given, <code class="docutils literal notranslate"><span class="pre">0.0</span></code> is returned.</p>
<p>Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;+1.23&#39;</span><span class="p">)</span>
<span class="go">1.23</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;   -12345</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="go">-12345.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;1e-003&#39;</span><span class="p">)</span>
<span class="go">0.001</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;+1E6&#39;</span><span class="p">)</span>
<span class="go">1000000.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;-Infinity&#39;</span><span class="p">)</span>
<span class="go">-inf</span>
</pre></div>
</div>
<p>The float type is described in <a class="reference internal" href="stdtypes.html#typesnumeric"><span class="std std-ref">Numeric Types — int, float, complex</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Grouping digits with underscores as in code literals is allowed.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>x</em> is now a positional-only parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Falls back to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> if <a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a> is not defined.</p>
</div>
</dd></dl>

<dl class="py function" id="index-4">
<dt class="sig sig-object py" id="format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format_spec</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#format" title="Link to this definition">¶</a></dt>
<dd><p>Convert a <em>value</em> to a “formatted” representation, as controlled by
<em>format_spec</em>.  The interpretation of <em>format_spec</em> will depend on the type
of the <em>value</em> argument; however, there is a standard formatting syntax that
is used by most built-in types: <a class="reference internal" href="string.html#formatspec"><span class="std std-ref">Format Specification Mini-Language</span></a>.</p>
<p>The default <em>format_spec</em> is an empty string which usually gives the same
effect as calling <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str(value)</span></code></a>.</p>
<p>A call to <code class="docutils literal notranslate"><span class="pre">format(value,</span> <span class="pre">format_spec)</span></code> is translated to
<code class="docutils literal notranslate"><span class="pre">type(value).__format__(value,</span> <span class="pre">format_spec)</span></code> which bypasses the instance
dictionary when searching for the value’s <a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a> method.
A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception is raised if the method search reaches
<a class="reference internal" href="#object" title="object"><code class="xref py py-mod docutils literal notranslate"><span class="pre">object</span></code></a> and the <em>format_spec</em> is non-empty, or if either the
<em>format_spec</em> or the return value are not strings.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><code class="docutils literal notranslate"><span class="pre">object().__format__(format_spec)</span></code> raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>
if <em>format_spec</em> is not an empty string.</p>
</div>
</dd></dl>

<dl class="py class" id="func-frozenset">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">frozenset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">set()</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a new <a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">frozenset</span></code></a> object, optionally with elements taken from
<em>iterable</em>.  <code class="docutils literal notranslate"><span class="pre">frozenset</span></code> is a built-in class.  See <a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">frozenset</span></code></a> and
<a class="reference internal" href="stdtypes.html#types-set"><span class="std std-ref">Set Types — set, frozenset</span></a> for documentation about this class.</p>
<p>For other containers see the built-in <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a>, <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>,
<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>, and <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> classes, as well as the <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a>
module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="getattr">
<span class="sig-name descname"><span class="pre">getattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#getattr" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">getattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return the value of the named attribute of <em>object</em>.  <em>name</em> must be a string.
If the string is the name of one of the object’s attributes, the result is the
value of that attribute.  For example, <code class="docutils literal notranslate"><span class="pre">getattr(x,</span> <span class="pre">'foobar')</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">x.foobar</span></code>.  If the named attribute does not exist, <em>default</em> is returned if
provided, otherwise <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> is raised.
<em>name</em> need not be a Python identifier (see <a class="reference internal" href="#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a>).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Since <a class="reference internal" href="../reference/expressions.html#private-name-mangling"><span class="std std-ref">private name mangling</span></a> happens at
compilation time, one must manually mangle a private attribute’s
(attributes with two leading underscores) name in order to retrieve it with
<a class="reference internal" href="#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="globals">
<span class="sig-name descname"><span class="pre">globals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#globals" title="Link to this definition">¶</a></dt>
<dd><p>Return the dictionary implementing the current module namespace. For code within
functions, this is set when the function is defined and remains the same
regardless of where the function is called.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hasattr">
<span class="sig-name descname"><span class="pre">hasattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hasattr" title="Link to this definition">¶</a></dt>
<dd><p>The arguments are an object and a string.  The result is <code class="docutils literal notranslate"><span class="pre">True</span></code> if the
string is the name of one of the object’s attributes, <code class="docutils literal notranslate"><span class="pre">False</span></code> if not. (This
is implemented by calling <code class="docutils literal notranslate"><span class="pre">getattr(object,</span> <span class="pre">name)</span></code> and seeing whether it
raises an <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> or not.)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hash">
<span class="sig-name descname"><span class="pre">hash</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hash" title="Link to this definition">¶</a></dt>
<dd><p>Return the hash value of the object (if it has one).  Hash values are
integers.  They are used to quickly compare dictionary keys during a
dictionary lookup.  Numeric values that compare equal have the same hash
value (even if they are of different types, as is the case for 1 and 1.0).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For objects with custom <a class="reference internal" href="../reference/datamodel.html#object.__hash__" title="object.__hash__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__hash__()</span></code></a> methods,
note that <a class="reference internal" href="#hash" title="hash"><code class="xref py py-func docutils literal notranslate"><span class="pre">hash()</span></code></a>
truncates the return value based on the bit width of the host machine.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="help">
<span class="sig-name descname"><span class="pre">help</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#help" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">help</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Invoke the built-in help system.  (This function is intended for interactive
use.)  If no argument is given, the interactive help system starts on the
interpreter console.  If the argument is a string, then the string is looked up
as the name of a module, function, class, method, keyword, or documentation
topic, and a help page is printed on the console.  If the argument is any other
kind of object, a help page on the object is generated.</p>
<p>Note that if a slash(/) appears in the parameter list of a function when
invoking <a class="reference internal" href="#help" title="help"><code class="xref py py-func docutils literal notranslate"><span class="pre">help()</span></code></a>, it means that the parameters prior to the slash are
positional-only. For more info, see
<a class="reference internal" href="../faq/programming.html#faq-positional-only-arguments"><span class="std std-ref">the FAQ entry on positional-only parameters</span></a>.</p>
<p>This function is added to the built-in namespace by the <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Changes to <a class="reference internal" href="pydoc.html#module-pydoc" title="pydoc: Documentation generator and online help system."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code></a> and <a class="reference internal" href="inspect.html#module-inspect" title="inspect: Extract information and source code from live objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code></a> mean that the reported
signatures for callables are now more comprehensive and consistent.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hex">
<span class="sig-name descname"><span class="pre">hex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hex" title="Link to this definition">¶</a></dt>
<dd><p>Convert an integer number to a lowercase hexadecimal string prefixed with
“0x”. If <em>x</em> is not a Python <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> object, it has to define an
<a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> method that returns an integer. Some examples:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">hex</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span>
<span class="go">&#39;0xff&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">hex</span><span class="p">(</span><span class="o">-</span><span class="mi">42</span><span class="p">)</span>
<span class="go">&#39;-0x2a&#39;</span>
</pre></div>
</div>
<p>If you want to convert an integer number to an uppercase or lower hexadecimal
string with prefix or not, you can use either of the following ways:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">%#x</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="mi">255</span><span class="p">,</span> <span class="s1">&#39;</span><span class="si">%x</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="mi">255</span><span class="p">,</span> <span class="s1">&#39;</span><span class="si">%X</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="mi">255</span>
<span class="go">(&#39;0xff&#39;, &#39;ff&#39;, &#39;FF&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="s1">&#39;#x&#39;</span><span class="p">),</span> <span class="nb">format</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="s1">&#39;x&#39;</span><span class="p">),</span> <span class="nb">format</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="s1">&#39;X&#39;</span><span class="p">)</span>
<span class="go">(&#39;0xff&#39;, &#39;ff&#39;, &#39;FF&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">255</span><span class="si">:</span><span class="s1">#x</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">255</span><span class="si">:</span><span class="s1">x</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">255</span><span class="si">:</span><span class="s1">X</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="go">(&#39;0xff&#39;, &#39;ff&#39;, &#39;FF&#39;)</span>
</pre></div>
</div>
<p>See also <a class="reference internal" href="#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> for more information.</p>
<p>See also <a class="reference internal" href="#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> for converting a hexadecimal string to an
integer using a base of 16.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>To obtain a hexadecimal string representation for a float, use the
<a class="reference internal" href="stdtypes.html#float.hex" title="float.hex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">float.hex()</span></code></a> method.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="id">
<span class="sig-name descname"><span class="pre">id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#id" title="Link to this definition">¶</a></dt>
<dd><p>Return the “identity” of an object.  This is an integer which
is guaranteed to be unique and constant for this object during its lifetime.
Two objects with non-overlapping lifetimes may have the same <a class="reference internal" href="#id" title="id"><code class="xref py py-func docutils literal notranslate"><span class="pre">id()</span></code></a>
value.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This is the address of the object in memory.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">builtins.id</span></code> with argument <code class="docutils literal notranslate"><span class="pre">id</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="input">
<span class="sig-name descname"><span class="pre">input</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#input" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">input</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prompt</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>If the <em>prompt</em> argument is present, it is written to standard output without
a trailing newline.  The function then reads a line from input, converts it
to a string (stripping a trailing newline), and returns that.  When EOF is
read, <a class="reference internal" href="exceptions.html#EOFError" title="EOFError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">EOFError</span></code></a> is raised.  Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s1">&#39;--&gt; &#39;</span><span class="p">)</span>  
<span class="go">--&gt; Monty Python&#39;s Flying Circus</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span>  
<span class="go">&quot;Monty Python&#39;s Flying Circus&quot;</span>
</pre></div>
</div>
<p>If the <a class="reference internal" href="readline.html#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module was loaded, then <a class="reference internal" href="#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a> will use it
to provide elaborate line editing and history features.</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">builtins.input</span></code> with
argument <code class="docutils literal notranslate"><span class="pre">prompt</span></code> before reading input</p>
</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">builtins.input/result</span></code>
with the result after successfully reading input.</p>
</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="int">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">int</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#int" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">int</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return an integer object constructed from a number or string <em>x</em>, or return
<code class="docutils literal notranslate"><span class="pre">0</span></code> if no arguments are given.  If <em>x</em> defines <a class="reference internal" href="../reference/datamodel.html#object.__int__" title="object.__int__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__int__()</span></code></a>,
<code class="docutils literal notranslate"><span class="pre">int(x)</span></code> returns <code class="docutils literal notranslate"><span class="pre">x.__int__()</span></code>.  If <em>x</em> defines <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a>,
it returns <code class="docutils literal notranslate"><span class="pre">x.__index__()</span></code>.  If <em>x</em> defines <a class="reference internal" href="../reference/datamodel.html#object.__trunc__" title="object.__trunc__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__trunc__()</span></code></a>,
it returns <code class="docutils literal notranslate"><span class="pre">x.__trunc__()</span></code>.
For floating point numbers, this truncates towards zero.</p>
<p>If <em>x</em> is not a number or if <em>base</em> is given, then <em>x</em> must be a string,
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>, or <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> instance representing an integer
in radix <em>base</em>.  Optionally, the string can be preceded by <code class="docutils literal notranslate"><span class="pre">+</span></code> or <code class="docutils literal notranslate"><span class="pre">-</span></code>
(with no space in between), have leading zeros, be surrounded by whitespace,
and have single underscores interspersed between digits.</p>
<p>A base-n integer string contains digits, each representing a value from 0 to
n-1. The values 0–9 can be represented by any Unicode decimal digit. The
values 10–35 can be represented by <code class="docutils literal notranslate"><span class="pre">a</span></code> to <code class="docutils literal notranslate"><span class="pre">z</span></code> (or <code class="docutils literal notranslate"><span class="pre">A</span></code> to <code class="docutils literal notranslate"><span class="pre">Z</span></code>). The
default <em>base</em> is 10. The allowed bases are 0 and 2–36. Base-2, -8, and -16
strings can be optionally prefixed with <code class="docutils literal notranslate"><span class="pre">0b</span></code>/<code class="docutils literal notranslate"><span class="pre">0B</span></code>, <code class="docutils literal notranslate"><span class="pre">0o</span></code>/<code class="docutils literal notranslate"><span class="pre">0O</span></code>, or
<code class="docutils literal notranslate"><span class="pre">0x</span></code>/<code class="docutils literal notranslate"><span class="pre">0X</span></code>, as with integer literals in code.  For base 0, the string is
interpreted in a similar way to an <a class="reference internal" href="../reference/lexical_analysis.html#integers"><span class="std std-ref">integer literal in code</span></a>,
in that the actual base is 2, 8, 10, or 16 as determined by the prefix. Base
0 also disallows leading zeros: <code class="docutils literal notranslate"><span class="pre">int('010',</span> <span class="pre">0)</span></code> is not legal, while
<code class="docutils literal notranslate"><span class="pre">int('010')</span></code> and <code class="docutils literal notranslate"><span class="pre">int('010',</span> <span class="pre">8)</span></code> are.</p>
<p>The integer type is described in <a class="reference internal" href="stdtypes.html#typesnumeric"><span class="std std-ref">Numeric Types — int, float, complex</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>If <em>base</em> is not an instance of <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> and the <em>base</em> object has a
<a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">base.__index__</span></code></a> method, that method is called
to obtain an integer for the base.  Previous versions used
<a class="reference internal" href="../reference/datamodel.html#object.__int__" title="object.__int__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">base.__int__</span></code></a> instead of <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">base.__index__</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Grouping digits with underscores as in code literals is allowed.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>x</em> is now a positional-only parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Falls back to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> if <a class="reference internal" href="../reference/datamodel.html#object.__int__" title="object.__int__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__int__()</span></code></a> is not defined.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The delegation to <a class="reference internal" href="../reference/datamodel.html#object.__trunc__" title="object.__trunc__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__trunc__()</span></code></a> is deprecated.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> string inputs and string representations can be limited to
help avoid denial of service attacks. A <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised when
the limit is exceeded while converting a string <em>x</em> to an <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> or
when converting an <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> into a string would exceed the limit.
See the <a class="reference internal" href="stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string conversion length limitation</span></a> documentation.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="isinstance">
<span class="sig-name descname"><span class="pre">isinstance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">classinfo</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#isinstance" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the <em>object</em> argument is an instance of the <em>classinfo</em>
argument, or of a (direct, indirect, or <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">virtual</span></a>) subclass thereof.  If <em>object</em> is not
an object of the given type, the function always returns <code class="docutils literal notranslate"><span class="pre">False</span></code>.
If <em>classinfo</em> is a tuple of type objects (or recursively, other such
tuples) or a <a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">Union Type</span></a> of multiple types, return <code class="docutils literal notranslate"><span class="pre">True</span></code> if
<em>object</em> is an instance of any of the types.
If <em>classinfo</em> is not a type or tuple of types and such tuples,
a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception is raised. <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> may not be
raised for an invalid type if an earlier check succeeds.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><em>classinfo</em> can be a <a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">Union Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="issubclass">
<span class="sig-name descname"><span class="pre">issubclass</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">class</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">classinfo</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#issubclass" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>class</em> is a subclass (direct, indirect, or <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">virtual</span></a>) of <em>classinfo</em>.  A
class is considered a subclass of itself. <em>classinfo</em> may be a tuple of class
objects (or recursively, other such tuples)
or a <a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">Union Type</span></a>, in which case return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>class</em> is a
subclass of any entry in <em>classinfo</em>.  In any other case, a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>
exception is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span><em>classinfo</em> can be a <a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">Union Type</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="iter">
<span class="sig-name descname"><span class="pre">iter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#iter" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">iter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sentinel</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> object.  The first argument is interpreted very
differently depending on the presence of the second argument. Without a
second argument, <em>object</em> must be a collection object which supports the
<a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a> protocol (the <a class="reference internal" href="../reference/datamodel.html#object.__iter__" title="object.__iter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code></a> method),
or it must support
the sequence protocol (the <a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a> method with integer arguments
starting at <code class="docutils literal notranslate"><span class="pre">0</span></code>).  If it does not support either of those protocols,
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised. If the second argument, <em>sentinel</em>, is given,
then <em>object</em> must be a callable object.  The iterator created in this case
will call <em>object</em> with no arguments for each call to its
<a class="reference internal" href="stdtypes.html#iterator.__next__" title="iterator.__next__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__next__()</span></code></a> method; if the value returned is equal to
<em>sentinel</em>, <a class="reference internal" href="exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> will be raised, otherwise the value will
be returned.</p>
<p>See also <a class="reference internal" href="stdtypes.html#typeiter"><span class="std std-ref">Iterator Types</span></a>.</p>
<p>One useful application of the second form of <a class="reference internal" href="#iter" title="iter"><code class="xref py py-func docutils literal notranslate"><span class="pre">iter()</span></code></a> is to build a
block-reader. For example, reading fixed-width blocks from a binary
database file until the end of file is reached:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">functools</span> <span class="kn">import</span> <span class="n">partial</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;mydata.db&#39;</span><span class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">block</span> <span class="ow">in</span> <span class="nb">iter</span><span class="p">(</span><span class="n">partial</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">,</span> <span class="mi">64</span><span class="p">),</span> <span class="sa">b</span><span class="s1">&#39;&#39;</span><span class="p">):</span>
        <span class="n">process_block</span><span class="p">(</span><span class="n">block</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="len">
<span class="sig-name descname"><span class="pre">len</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#len" title="Link to this definition">¶</a></dt>
<dd><p>Return the length (the number of items) of an object.  The argument may be a
sequence (such as a string, bytes, tuple, list, or range) or a collection
(such as a dictionary, set, or frozen set).</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> <code class="docutils literal notranslate"><span class="pre">len</span></code> raises <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> on lengths larger than
<a class="reference internal" href="sys.html#sys.maxsize" title="sys.maxsize"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.maxsize</span></code></a>, such as <a class="reference internal" href="stdtypes.html#range" title="range"><code class="xref py py-class docutils literal notranslate"><span class="pre">range(2</span> <span class="pre">**</span> <span class="pre">100)</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py class" id="func-list">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">list</span></span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Rather than being a function, <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> is actually a mutable
sequence type, as documented in <a class="reference internal" href="stdtypes.html#typesseq-list"><span class="std std-ref">Lists</span></a> and <a class="reference internal" href="stdtypes.html#typesseq"><span class="std std-ref">Sequence Types — list, tuple, range</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="locals">
<span class="sig-name descname"><span class="pre">locals</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#locals" title="Link to this definition">¶</a></dt>
<dd><p>Update and return a dictionary representing the current local symbol table.
Free variables are returned by <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a> when it is called in function
blocks, but not in class blocks. Note that at the module level, <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a>
and <a class="reference internal" href="#globals" title="globals"><code class="xref py py-func docutils literal notranslate"><span class="pre">globals()</span></code></a> are the same dictionary.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The contents of this dictionary should not be modified; changes may not
affect the values of local and free variables used by the interpreter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="map">
<span class="sig-name descname"><span class="pre">map</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">iterables</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#map" title="Link to this definition">¶</a></dt>
<dd><p>Return an iterator that applies <em>function</em> to every item of <em>iterable</em>,
yielding the results.  If additional <em>iterables</em> arguments are passed,
<em>function</em> must take that many arguments and is applied to the items from all
iterables in parallel.  With multiple iterables, the iterator stops when the
shortest iterable is exhausted.  For cases where the function inputs are
already arranged into argument tuples, see <a class="reference internal" href="itertools.html#itertools.starmap" title="itertools.starmap"><code class="xref py py-func docutils literal notranslate"><span class="pre">itertools.starmap()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="max">
<span class="sig-name descname"><span class="pre">max</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#max" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">max</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">max</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arg2</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return the largest item in an iterable or the largest of two or more
arguments.</p>
<p>If one positional argument is provided, it should be an <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a>.
The largest item in the iterable is returned.  If two or more positional
arguments are provided, the largest of the positional arguments is
returned.</p>
<p>There are two optional keyword-only arguments. The <em>key</em> argument specifies
a one-argument ordering function like that used for <a class="reference internal" href="stdtypes.html#list.sort" title="list.sort"><code class="xref py py-meth docutils literal notranslate"><span class="pre">list.sort()</span></code></a>. The
<em>default</em> argument specifies an object to return if the provided iterable is
empty. If the iterable is empty and <em>default</em> is not provided, a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>If multiple items are maximal, the function returns the first one
encountered.  This is consistent with other sort-stability preserving tools
such as <code class="docutils literal notranslate"><span class="pre">sorted(iterable,</span> <span class="pre">key=keyfunc,</span> <span class="pre">reverse=True)[0]</span></code> and
<code class="docutils literal notranslate"><span class="pre">heapq.nlargest(1,</span> <span class="pre">iterable,</span> <span class="pre">key=keyfunc)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>default</em> keyword-only parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>key</em> can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py class" id="func-memoryview">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">memoryview</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a “memory view” object created from the given argument.  See
<a class="reference internal" href="stdtypes.html#typememoryview"><span class="std std-ref">Memory Views</span></a> for more information.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="min">
<span class="sig-name descname"><span class="pre">min</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#min" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">min</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">min</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arg2</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return the smallest item in an iterable or the smallest of two or more
arguments.</p>
<p>If one positional argument is provided, it should be an <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a>.
The smallest item in the iterable is returned.  If two or more positional
arguments are provided, the smallest of the positional arguments is
returned.</p>
<p>There are two optional keyword-only arguments. The <em>key</em> argument specifies
a one-argument ordering function like that used for <a class="reference internal" href="stdtypes.html#list.sort" title="list.sort"><code class="xref py py-meth docutils literal notranslate"><span class="pre">list.sort()</span></code></a>. The
<em>default</em> argument specifies an object to return if the provided iterable is
empty. If the iterable is empty and <em>default</em> is not provided, a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>If multiple items are minimal, the function returns the first one
encountered.  This is consistent with other sort-stability preserving tools
such as <code class="docutils literal notranslate"><span class="pre">sorted(iterable,</span> <span class="pre">key=keyfunc)[0]</span></code> and <code class="docutils literal notranslate"><span class="pre">heapq.nsmallest(1,</span>
<span class="pre">iterable,</span> <span class="pre">key=keyfunc)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>default</em> keyword-only parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>key</em> can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="next">
<span class="sig-name descname"><span class="pre">next</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterator</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#next" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">next</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterator</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Retrieve the next item from the <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> by calling its
<a class="reference internal" href="stdtypes.html#iterator.__next__" title="iterator.__next__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__next__()</span></code></a> method.  If <em>default</em> is given, it is returned
if the iterator is exhausted, otherwise <a class="reference internal" href="exceptions.html#StopIteration" title="StopIteration"><code class="xref py py-exc docutils literal notranslate"><span class="pre">StopIteration</span></code></a> is raised.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="object">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">object</span></span><a class="headerlink" href="#object" title="Link to this definition">¶</a></dt>
<dd><p>Return a new featureless object.  <a class="reference internal" href="#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> is a base for all classes.
It has methods that are common to all instances of Python classes.  This
function does not accept any arguments.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> does <em>not</em> have a <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a>, so you can’t
assign arbitrary attributes to an instance of the <a class="reference internal" href="#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> class.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="oct">
<span class="sig-name descname"><span class="pre">oct</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#oct" title="Link to this definition">¶</a></dt>
<dd><p>Convert an integer number to an octal string prefixed with “0o”.  The result
is a valid Python expression. If <em>x</em> is not a Python <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> object, it
has to define an <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> method that returns an integer. For
example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">oct</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>
<span class="go">&#39;0o10&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">oct</span><span class="p">(</span><span class="o">-</span><span class="mi">56</span><span class="p">)</span>
<span class="go">&#39;-0o70&#39;</span>
</pre></div>
</div>
<p>If you want to convert an integer number to an octal string either with the prefix
“0o” or not, you can use either of the following ways.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;</span><span class="si">%#o</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="mi">10</span><span class="p">,</span> <span class="s1">&#39;</span><span class="si">%o</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="mi">10</span>
<span class="go">(&#39;0o12&#39;, &#39;12&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="s1">&#39;#o&#39;</span><span class="p">),</span> <span class="nb">format</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="s1">&#39;o&#39;</span><span class="p">)</span>
<span class="go">(&#39;0o12&#39;, &#39;12&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">10</span><span class="si">:</span><span class="s1">#o</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="mi">10</span><span class="si">:</span><span class="s1">o</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="go">(&#39;0o12&#39;, &#39;12&#39;)</span>
</pre></div>
</div>
<p>See also <a class="reference internal" href="#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> for more information.</p>
</dd></dl>

<dl class="py function" id="index-5">
<dt class="sig sig-object py" id="open">
<span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">closefd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opener</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#open" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>file</em> and return a corresponding <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>.  If the file
cannot be opened, an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised. See
<a class="reference internal" href="../tutorial/inputoutput.html#tut-files"><span class="std std-ref">Reading and Writing Files</span></a> for more examples of how to use this function.</p>
<p><em>file</em> is a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> giving the pathname (absolute or
relative to the current working directory) of the file to be opened or an
integer file descriptor of the file to be wrapped.  (If a file descriptor is
given, it is closed when the returned I/O object is closed unless <em>closefd</em>
is set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.)</p>
<p><em>mode</em> is an optional string that specifies the mode in which the file is
opened.  It defaults to <code class="docutils literal notranslate"><span class="pre">'r'</span></code> which means open for reading in text mode.
Other common values are <code class="docutils literal notranslate"><span class="pre">'w'</span></code> for writing (truncating the file if it
already exists), <code class="docutils literal notranslate"><span class="pre">'x'</span></code> for exclusive creation, and <code class="docutils literal notranslate"><span class="pre">'a'</span></code> for appending
(which on <em>some</em> Unix systems, means that <em>all</em> writes append to the end of
the file regardless of the current seek position).  In text mode, if
<em>encoding</em> is not specified the encoding used is platform-dependent:
<a class="reference internal" href="locale.html#locale.getencoding" title="locale.getencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.getencoding()</span></code></a> is called to get the current locale encoding.
(For reading and writing raw bytes use binary mode and leave
<em>encoding</em> unspecified.)  The available modes are:</p>
<span id="filemodes"></span><table class="docutils align-default" id="index-6">
<thead>
<tr class="row-odd"><th class="head"><p>Character</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r'</span></code></p></td>
<td><p>open for reading (default)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'w'</span></code></p></td>
<td><p>open for writing, truncating the file first</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'x'</span></code></p></td>
<td><p>open for exclusive creation, failing if the file already exists</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'a'</span></code></p></td>
<td><p>open for writing, appending to the end of file if it exists</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'b'</span></code></p></td>
<td><p>binary mode</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'t'</span></code></p></td>
<td><p>text mode (default)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'+'</span></code></p></td>
<td><p>open for updating (reading and writing)</p></td>
</tr>
</tbody>
</table>
<p>The default mode is <code class="docutils literal notranslate"><span class="pre">'r'</span></code> (open for reading text, a synonym of <code class="docutils literal notranslate"><span class="pre">'rt'</span></code>).
Modes <code class="docutils literal notranslate"><span class="pre">'w+'</span></code> and <code class="docutils literal notranslate"><span class="pre">'w+b'</span></code> open and truncate the file.  Modes <code class="docutils literal notranslate"><span class="pre">'r+'</span></code>
and <code class="docutils literal notranslate"><span class="pre">'r+b'</span></code> open the file with no truncation.</p>
<p>As mentioned in the <a class="reference internal" href="io.html#io-overview"><span class="std std-ref">Overview</span></a>, Python distinguishes between binary
and text I/O.  Files opened in binary mode (including <code class="docutils literal notranslate"><span class="pre">'b'</span></code> in the <em>mode</em>
argument) return contents as <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects without any decoding.  In
text mode (the default, or when <code class="docutils literal notranslate"><span class="pre">'t'</span></code> is included in the <em>mode</em> argument),
the contents of the file are returned as <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, the bytes having been
first decoded using a platform-dependent encoding or using the specified
<em>encoding</em> if given.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Python doesn’t depend on the underlying operating system’s notion of text
files; all the processing is done by Python itself, and is therefore
platform-independent.</p>
</div>
<p><em>buffering</em> is an optional integer used to set the buffering policy.  Pass 0
to switch buffering off (only allowed in binary mode), 1 to select line
buffering (only usable when writing in text mode), and an integer &gt; 1 to indicate the size
in bytes of a fixed-size chunk buffer. Note that specifying a buffer size this
way applies for binary buffered I/O, but <code class="docutils literal notranslate"><span class="pre">TextIOWrapper</span></code> (i.e., files opened
with <code class="docutils literal notranslate"><span class="pre">mode='r+'</span></code>) would have another buffering. To disable buffering in
<code class="docutils literal notranslate"><span class="pre">TextIOWrapper</span></code>, consider using the <code class="docutils literal notranslate"><span class="pre">write_through</span></code> flag for
<a class="reference internal" href="io.html#io.TextIOWrapper.reconfigure" title="io.TextIOWrapper.reconfigure"><code class="xref py py-func docutils literal notranslate"><span class="pre">io.TextIOWrapper.reconfigure()</span></code></a>. When no <em>buffering</em> argument is
given, the default buffering policy works as follows:</p>
<ul class="simple">
<li><p>Binary files are buffered in fixed-size chunks; the size of the buffer is
chosen using a heuristic trying to determine the underlying device’s “block
size” and falling back on <a class="reference internal" href="io.html#io.DEFAULT_BUFFER_SIZE" title="io.DEFAULT_BUFFER_SIZE"><code class="xref py py-const docutils literal notranslate"><span class="pre">io.DEFAULT_BUFFER_SIZE</span></code></a>.  On many systems,
the buffer will typically be 4096 or 8192 bytes long.</p></li>
<li><p>“Interactive” text files (files for which <a class="reference internal" href="io.html#io.IOBase.isatty" title="io.IOBase.isatty"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isatty()</span></code></a>
returns <code class="docutils literal notranslate"><span class="pre">True</span></code>) use line buffering.  Other text files use the policy
described above for binary files.</p></li>
</ul>
<p><em>encoding</em> is the name of the encoding used to decode or encode the file.
This should only be used in text mode.  The default encoding is platform
dependent (whatever <a class="reference internal" href="locale.html#locale.getencoding" title="locale.getencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.getencoding()</span></code></a> returns), but any
<a class="reference internal" href="../glossary.html#term-text-encoding"><span class="xref std std-term">text encoding</span></a> supported by Python can be used.
See the <a class="reference internal" href="codecs.html#module-codecs" title="codecs: Encode and decode data and streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">codecs</span></code></a> module for the list of supported encodings.</p>
<p><em>errors</em> is an optional string that specifies how encoding and decoding
errors are to be handled—this cannot be used in binary mode.
A variety of standard error handlers are available
(listed under <a class="reference internal" href="codecs.html#error-handlers"><span class="std std-ref">Error Handlers</span></a>), though any
error handling name that has been registered with
<a class="reference internal" href="codecs.html#codecs.register_error" title="codecs.register_error"><code class="xref py py-func docutils literal notranslate"><span class="pre">codecs.register_error()</span></code></a> is also valid.  The standard names
include:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">'strict'</span></code> to raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> exception if there is
an encoding error.  The default value of <code class="docutils literal notranslate"><span class="pre">None</span></code> has the same
effect.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'ignore'</span></code> ignores errors.  Note that ignoring encoding errors
can lead to data loss.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'replace'</span></code> causes a replacement marker (such as <code class="docutils literal notranslate"><span class="pre">'?'</span></code>) to be inserted
where there is malformed data.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'surrogateescape'</span></code> will represent any incorrect bytes as low
surrogate code units ranging from U+DC80 to U+DCFF.
These surrogate code units will then be turned back into
the same bytes when the <code class="docutils literal notranslate"><span class="pre">surrogateescape</span></code> error handler is used
when writing data.  This is useful for processing files in an
unknown encoding.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'xmlcharrefreplace'</span></code> is only supported when writing to a file.
Characters not supported by the encoding are replaced with the
appropriate XML character reference <code class="samp docutils literal notranslate"><span class="pre">&amp;#</span><em><span class="pre">nnn</span></em><span class="pre">;</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'backslashreplace'</span></code> replaces malformed data by Python’s backslashed
escape sequences.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'namereplace'</span></code> (also only supported when writing)
replaces unsupported characters with <code class="docutils literal notranslate"><span class="pre">\N{...}</span></code> escape sequences.</p></li>
</ul>
<p id="open-newline-parameter"><span id="index-7"></span><em>newline</em> determines how to parse newline characters from the stream.
It can be <code class="docutils literal notranslate"><span class="pre">None</span></code>, <code class="docutils literal notranslate"><span class="pre">''</span></code>, <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, <code class="docutils literal notranslate"><span class="pre">'\r'</span></code>, and
<code class="docutils literal notranslate"><span class="pre">'\r\n'</span></code>.  It works as follows:</p>
<ul class="simple">
<li><p>When reading input from the stream, if <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, universal
newlines mode is enabled.  Lines in the input can end in <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'\r'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'\r\n'</span></code>, and these are translated into <code class="docutils literal notranslate"><span class="pre">'\n'</span></code> before
being returned to the caller.  If it is <code class="docutils literal notranslate"><span class="pre">''</span></code>, universal newlines mode is
enabled, but line endings are returned to the caller untranslated.  If it
has any of the other legal values, input lines are only terminated by the
given string, and the line ending is returned to the caller untranslated.</p></li>
<li><p>When writing output to the stream, if <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, any <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>
characters written are translated to the system default line separator,
<a class="reference internal" href="os.html#os.linesep" title="os.linesep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.linesep</span></code></a>.  If <em>newline</em> is <code class="docutils literal notranslate"><span class="pre">''</span></code> or <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, no translation
takes place.  If <em>newline</em> is any of the other legal values, any <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>
characters written are translated to the given string.</p></li>
</ul>
<p>If <em>closefd</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code> and a file descriptor rather than a filename was
given, the underlying file descriptor will be kept open when the file is
closed.  If a filename is given <em>closefd</em> must be <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default);
otherwise, an error will be raised.</p>
<p>A custom opener can be used by passing a callable as <em>opener</em>. The underlying
file descriptor for the file object is then obtained by calling <em>opener</em> with
(<em>file</em>, <em>flags</em>). <em>opener</em> must return an open file descriptor (passing
<a class="reference internal" href="os.html#os.open" title="os.open"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.open</span></code></a> as <em>opener</em> results in functionality similar to passing
<code class="docutils literal notranslate"><span class="pre">None</span></code>).</p>
<p>The newly created file is <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">non-inheritable</span></a>.</p>
<p>The following example uses the <a class="reference internal" href="os.html#dir-fd"><span class="std std-ref">dir_fd</span></a> parameter of the
<a class="reference internal" href="os.html#os.open" title="os.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.open()</span></code></a> function to open a file relative to a given directory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dir_fd</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;somedir&#39;</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">O_RDONLY</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">opener</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">flags</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">os</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">flags</span><span class="p">,</span> <span class="n">dir_fd</span><span class="o">=</span><span class="n">dir_fd</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;spamspam.txt&#39;</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">opener</span><span class="o">=</span><span class="n">opener</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;This will be written to somedir/spamspam.txt&#39;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">f</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">close</span><span class="p">(</span><span class="n">dir_fd</span><span class="p">)</span>  <span class="c1"># don&#39;t leak a file descriptor</span>
</pre></div>
</div>
<p>The type of <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> returned by the <a class="reference internal" href="#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function
depends on the mode.  When <a class="reference internal" href="#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> is used to open a file in a text
mode (<code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'r'</span></code>, <code class="docutils literal notranslate"><span class="pre">'wt'</span></code>, <code class="docutils literal notranslate"><span class="pre">'rt'</span></code>, etc.), it returns a subclass of
<a class="reference internal" href="io.html#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOBase</span></code></a> (specifically <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>).  When used
to open a file in a binary mode with buffering, the returned class is a
subclass of <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a>.  The exact class varies: in read
binary mode, it returns an <a class="reference internal" href="io.html#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedReader</span></code></a>; in write binary and
append binary modes, it returns an <a class="reference internal" href="io.html#io.BufferedWriter" title="io.BufferedWriter"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedWriter</span></code></a>, and in
read/write mode, it returns an <a class="reference internal" href="io.html#io.BufferedRandom" title="io.BufferedRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedRandom</span></code></a>.  When buffering is
disabled, the raw stream, a subclass of <a class="reference internal" href="io.html#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.RawIOBase</span></code></a>,
<a class="reference internal" href="io.html#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.FileIO</span></code></a>, is returned.</p>
<p id="index-8">See also the file handling modules, such as <a class="reference internal" href="fileinput.html#module-fileinput" title="fileinput: Loop over standard input or a list of files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code></a>, <a class="reference internal" href="io.html#module-io" title="io: Core tools for working with streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code></a>
(where <a class="reference internal" href="#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> is declared), <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a>, <a class="reference internal" href="os.path.html#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a>, <a class="reference internal" href="tempfile.html#module-tempfile" title="tempfile: Generate temporary files and directories."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tempfile</span></code></a>,
and <a class="reference internal" href="shutil.html#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">open</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">file</span></code>, <code class="docutils literal notranslate"><span class="pre">mode</span></code>, <code class="docutils literal notranslate"><span class="pre">flags</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">mode</span></code> and <code class="docutils literal notranslate"><span class="pre">flags</span></code> arguments may have been modified or inferred from
the original call.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span></p>
<ul class="simple">
<li><p>The <em>opener</em> parameter was added.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">'x'</span></code> mode was added.</p></li>
<li><p><a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> used to be raised, it is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p></li>
<li><p><a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> is now raised if the file opened in exclusive
creation mode (<code class="docutils literal notranslate"><span class="pre">'x'</span></code>) already exists.</p></li>
</ul>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span></p>
<ul class="simple">
<li><p>The file is now non-inheritable.</p></li>
</ul>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span></p>
<ul class="simple">
<li><p>If the system call is interrupted and the signal handler does not raise an
exception, the function now retries the system call instead of raising an
<a class="reference internal" href="exceptions.html#InterruptedError" title="InterruptedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InterruptedError</span></code></a> exception (see <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">'namereplace'</span></code> error handler was added.</p></li>
</ul>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span></p>
<ul class="simple">
<li><p>Support added to accept objects implementing <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a>.</p></li>
<li><p>On Windows, opening a console buffer may return a subclass of
<a class="reference internal" href="io.html#io.RawIOBase" title="io.RawIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.RawIOBase</span></code></a> other than <a class="reference internal" href="io.html#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.FileIO</span></code></a>.</p></li>
</ul>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <code class="docutils literal notranslate"><span class="pre">'U'</span></code> mode has been removed.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="ord">
<span class="sig-name descname"><span class="pre">ord</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">c</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#ord" title="Link to this definition">¶</a></dt>
<dd><p>Given a string representing one Unicode character, return an integer
representing the Unicode code point of that character.  For example,
<code class="docutils literal notranslate"><span class="pre">ord('a')</span></code> returns the integer <code class="docutils literal notranslate"><span class="pre">97</span></code> and <code class="docutils literal notranslate"><span class="pre">ord('€')</span></code> (Euro sign)
returns <code class="docutils literal notranslate"><span class="pre">8364</span></code>.  This is the inverse of <a class="reference internal" href="#chr" title="chr"><code class="xref py py-func docutils literal notranslate"><span class="pre">chr()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pow">
<span class="sig-name descname"><span class="pre">pow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">base</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mod</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pow" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>base</em> to the power <em>exp</em>; if <em>mod</em> is present, return <em>base</em> to the
power <em>exp</em>, modulo <em>mod</em> (computed more efficiently than
<code class="docutils literal notranslate"><span class="pre">pow(base,</span> <span class="pre">exp)</span> <span class="pre">%</span> <span class="pre">mod</span></code>). The two-argument form <code class="docutils literal notranslate"><span class="pre">pow(base,</span> <span class="pre">exp)</span></code> is
equivalent to using the power operator: <code class="docutils literal notranslate"><span class="pre">base**exp</span></code>.</p>
<p>The arguments must have numeric types.  With mixed operand types, the
coercion rules for binary arithmetic operators apply.  For <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>
operands, the result has the same type as the operands (after coercion)
unless the second argument is negative; in that case, all arguments are
converted to float and a float result is delivered.  For example, <code class="docutils literal notranslate"><span class="pre">pow(10,</span> <span class="pre">2)</span></code>
returns <code class="docutils literal notranslate"><span class="pre">100</span></code>, but <code class="docutils literal notranslate"><span class="pre">pow(10,</span> <span class="pre">-2)</span></code> returns <code class="docutils literal notranslate"><span class="pre">0.01</span></code>.  For a negative base of
type <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> or <a class="reference internal" href="#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> and a non-integral exponent, a complex
result is delivered.  For example, <code class="docutils literal notranslate"><span class="pre">pow(-9,</span> <span class="pre">0.5)</span></code> returns a value close
to <code class="docutils literal notranslate"><span class="pre">3j</span></code>.</p>
<p>For <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> operands <em>base</em> and <em>exp</em>, if <em>mod</em> is present, <em>mod</em> must
also be of integer type and <em>mod</em> must be nonzero. If <em>mod</em> is present and
<em>exp</em> is negative, <em>base</em> must be relatively prime to <em>mod</em>. In that case,
<code class="docutils literal notranslate"><span class="pre">pow(inv_base,</span> <span class="pre">-exp,</span> <span class="pre">mod)</span></code> is returned, where <em>inv_base</em> is an inverse to
<em>base</em> modulo <em>mod</em>.</p>
<p>Here’s an example of computing an inverse for <code class="docutils literal notranslate"><span class="pre">38</span></code> modulo <code class="docutils literal notranslate"><span class="pre">97</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">pow</span><span class="p">(</span><span class="mi">38</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="n">mod</span><span class="o">=</span><span class="mi">97</span><span class="p">)</span>
<span class="go">23</span>
<span class="gp">&gt;&gt;&gt; </span><span class="mi">23</span> <span class="o">*</span> <span class="mi">38</span> <span class="o">%</span> <span class="mi">97</span> <span class="o">==</span> <span class="mi">1</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>For <a class="reference internal" href="#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> operands, the three-argument form of <code class="docutils literal notranslate"><span class="pre">pow</span></code> now allows
the second argument to be negative, permitting computation of modular
inverses.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Allow keyword arguments.  Formerly, only positional arguments were
supported.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="print">
<span class="sig-name descname"><span class="pre">print</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">objects</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sep</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'\n'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flush</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#print" title="Link to this definition">¶</a></dt>
<dd><p>Print <em>objects</em> to the text stream <em>file</em>, separated by <em>sep</em> and followed
by <em>end</em>.  <em>sep</em>, <em>end</em>, <em>file</em>, and <em>flush</em>, if present, must be given as keyword
arguments.</p>
<p>All non-keyword arguments are converted to strings like <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> does and
written to the stream, separated by <em>sep</em> and followed by <em>end</em>.  Both <em>sep</em>
and <em>end</em> must be strings; they can also be <code class="docutils literal notranslate"><span class="pre">None</span></code>, which means to use the
default values.  If no <em>objects</em> are given, <a class="reference internal" href="#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> will just write
<em>end</em>.</p>
<p>The <em>file</em> argument must be an object with a <code class="docutils literal notranslate"><span class="pre">write(string)</span></code> method; if it
is not present or <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> will be used.  Since printed
arguments are converted to text strings, <a class="reference internal" href="#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> cannot be used with
binary mode file objects.  For these, use <code class="docutils literal notranslate"><span class="pre">file.write(...)</span></code> instead.</p>
<p>Output buffering is usually determined by <em>file</em>.
However, if <em>flush</em> is true, the stream is forcibly flushed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>flush</em> keyword argument.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="property">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">property</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fget</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fdel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#property" title="Link to this definition">¶</a></dt>
<dd><p>Return a property attribute.</p>
<p><em>fget</em> is a function for getting an attribute value.  <em>fset</em> is a function
for setting an attribute value. <em>fdel</em> is a function for deleting an attribute
value.  And <em>doc</em> creates a docstring for the attribute.</p>
<p>A typical use is to define a managed attribute <code class="docutils literal notranslate"><span class="pre">x</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_x</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span> <span class="nf">getx</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_x</span>

    <span class="k">def</span> <span class="nf">setx</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_x</span> <span class="o">=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="nf">delx</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_x</span>

    <span class="n">x</span> <span class="o">=</span> <span class="nb">property</span><span class="p">(</span><span class="n">getx</span><span class="p">,</span> <span class="n">setx</span><span class="p">,</span> <span class="n">delx</span><span class="p">,</span> <span class="s2">&quot;I&#39;m the &#39;x&#39; property.&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>If <em>c</em> is an instance of <em>C</em>, <code class="docutils literal notranslate"><span class="pre">c.x</span></code> will invoke the getter,
<code class="docutils literal notranslate"><span class="pre">c.x</span> <span class="pre">=</span> <span class="pre">value</span></code> will invoke the setter, and <code class="docutils literal notranslate"><span class="pre">del</span> <span class="pre">c.x</span></code> the deleter.</p>
<p>If given, <em>doc</em> will be the docstring of the property attribute. Otherwise, the
property will copy <em>fget</em>’s docstring (if it exists).  This makes it possible to
create read-only properties easily using <a class="reference internal" href="#property" title="property"><code class="xref py py-func docutils literal notranslate"><span class="pre">property()</span></code></a> as a <a class="reference internal" href="../glossary.html#term-decorator"><span class="xref std std-term">decorator</span></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Parrot</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_voltage</span> <span class="o">=</span> <span class="mi">100000</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">voltage</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get the current voltage.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_voltage</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&#64;property</span></code> decorator turns the <code class="xref py py-meth docutils literal notranslate"><span class="pre">voltage()</span></code> method into a “getter”
for a read-only attribute with the same name, and it sets the docstring for
<em>voltage</em> to “Get the current voltage.”</p>
<dl class="py function">
<dt class="sig sig-object py" id="property.getter">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-name descname"><span class="pre">getter</span></span><a class="headerlink" href="#property.getter" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="property.setter">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-name descname"><span class="pre">setter</span></span><a class="headerlink" href="#property.setter" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="property.deleter">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-name descname"><span class="pre">deleter</span></span><a class="headerlink" href="#property.deleter" title="Link to this definition">¶</a></dt>
<dd><p>A property object has <code class="docutils literal notranslate"><span class="pre">getter</span></code>, <code class="docutils literal notranslate"><span class="pre">setter</span></code>,
and <code class="docutils literal notranslate"><span class="pre">deleter</span></code> methods usable as decorators that create a
copy of the property with the corresponding accessor function set to the
decorated function.  This is best explained with an example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_x</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">x</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;I&#39;m the &#39;x&#39; property.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_x</span>

    <span class="nd">@x</span><span class="o">.</span><span class="n">setter</span>
    <span class="k">def</span> <span class="nf">x</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_x</span> <span class="o">=</span> <span class="n">value</span>

    <span class="nd">@x</span><span class="o">.</span><span class="n">deleter</span>
    <span class="k">def</span> <span class="nf">x</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_x</span>
</pre></div>
</div>
<p>This code is exactly equivalent to the first example.  Be sure to give the
additional functions the same name as the original property (<code class="docutils literal notranslate"><span class="pre">x</span></code> in this
case.)</p>
<p>The returned property object also has the attributes <code class="docutils literal notranslate"><span class="pre">fget</span></code>, <code class="docutils literal notranslate"><span class="pre">fset</span></code>, and
<code class="docutils literal notranslate"><span class="pre">fdel</span></code> corresponding to the constructor arguments.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The docstrings of property objects are now writeable.</p>
</div>
</dd></dl>

<dl class="py class" id="func-range">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">range</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stop</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">range</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">step</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Rather than being a function, <a class="reference internal" href="stdtypes.html#range" title="range"><code class="xref py py-class docutils literal notranslate"><span class="pre">range</span></code></a> is actually an immutable
sequence type, as documented in <a class="reference internal" href="stdtypes.html#typesseq-range"><span class="std std-ref">Ranges</span></a> and <a class="reference internal" href="stdtypes.html#typesseq"><span class="std std-ref">Sequence Types — list, tuple, range</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="repr">
<span class="sig-name descname"><span class="pre">repr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#repr" title="Link to this definition">¶</a></dt>
<dd><p>Return a string containing a printable representation of an object.  For many
types, this function makes an attempt to return a string that would yield an
object with the same value when passed to <a class="reference internal" href="#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>; otherwise, the
representation is a string enclosed in angle brackets that contains the name
of the type of the object together with additional information often
including the name and address of the object.  A class can control what this
function returns for its instances
by defining a <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a> method.
If <a class="reference internal" href="sys.html#sys.displayhook" title="sys.displayhook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.displayhook()</span></code></a> is not accessible, this function will raise
<a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.</p>
<p>This class has a custom representation that can be evaluated:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Person</span><span class="p">:</span>
   <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">age</span><span class="p">):</span>
      <span class="bp">self</span><span class="o">.</span><span class="n">name</span> <span class="o">=</span> <span class="n">name</span>
      <span class="bp">self</span><span class="o">.</span><span class="n">age</span> <span class="o">=</span> <span class="n">age</span>

   <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
      <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;Person(&#39;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&#39;, </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">age</span><span class="si">}</span><span class="s2">)&quot;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="reversed">
<span class="sig-name descname"><span class="pre">reversed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">seq</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reversed" title="Link to this definition">¶</a></dt>
<dd><p>Return a reverse <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>.  <em>seq</em> must be an object which has
a <a class="reference internal" href="../reference/datamodel.html#object.__reversed__" title="object.__reversed__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__reversed__()</span></code></a> method or supports the sequence protocol (the
<a class="reference internal" href="../reference/datamodel.html#object.__len__" title="object.__len__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__len__()</span></code></a> method and the <a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a> method
with integer arguments starting at <code class="docutils literal notranslate"><span class="pre">0</span></code>).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="round">
<span class="sig-name descname"><span class="pre">round</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">number</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ndigits</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#round" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>number</em> rounded to <em>ndigits</em> precision after the decimal
point.  If <em>ndigits</em> is omitted or is <code class="docutils literal notranslate"><span class="pre">None</span></code>, it returns the
nearest integer to its input.</p>
<p>For the built-in types supporting <a class="reference internal" href="#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a>, values are rounded to the
closest multiple of 10 to the power minus <em>ndigits</em>; if two multiples are
equally close, rounding is done toward the even choice (so, for example,
both <code class="docutils literal notranslate"><span class="pre">round(0.5)</span></code> and <code class="docutils literal notranslate"><span class="pre">round(-0.5)</span></code> are <code class="docutils literal notranslate"><span class="pre">0</span></code>, and <code class="docutils literal notranslate"><span class="pre">round(1.5)</span></code> is
<code class="docutils literal notranslate"><span class="pre">2</span></code>).  Any integer value is valid for <em>ndigits</em> (positive, zero, or
negative).  The return value is an integer if <em>ndigits</em> is omitted or
<code class="docutils literal notranslate"><span class="pre">None</span></code>.
Otherwise, the return value has the same type as <em>number</em>.</p>
<p>For a general Python object <code class="docutils literal notranslate"><span class="pre">number</span></code>, <code class="docutils literal notranslate"><span class="pre">round</span></code> delegates to
<code class="docutils literal notranslate"><span class="pre">number.__round__</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The behavior of <a class="reference internal" href="#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a> for floats can be surprising: for example,
<code class="docutils literal notranslate"><span class="pre">round(2.675,</span> <span class="pre">2)</span></code> gives <code class="docutils literal notranslate"><span class="pre">2.67</span></code> instead of the expected <code class="docutils literal notranslate"><span class="pre">2.68</span></code>.
This is not a bug: it’s a result of the fact that most decimal fractions
can’t be represented exactly as a float.  See <a class="reference internal" href="../tutorial/floatingpoint.html#tut-fp-issues"><span class="std std-ref">Floating Point Arithmetic:  Issues and Limitations</span></a> for
more information.</p>
</div>
</dd></dl>

<dl class="py class" id="func-set">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set</span></span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a new <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a> object, optionally with elements taken from
<em>iterable</em>.  <code class="docutils literal notranslate"><span class="pre">set</span></code> is a built-in class.  See <a class="reference internal" href="stdtypes.html#set" title="set"><code class="xref py py-class docutils literal notranslate"><span class="pre">set</span></code></a> and
<a class="reference internal" href="stdtypes.html#types-set"><span class="std std-ref">Set Types — set, frozenset</span></a> for documentation about this class.</p>
<p>For other containers see the built-in <a class="reference internal" href="stdtypes.html#frozenset" title="frozenset"><code class="xref py py-class docutils literal notranslate"><span class="pre">frozenset</span></code></a>, <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>,
<a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>, and <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> classes, as well as the <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a>
module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="setattr">
<span class="sig-name descname"><span class="pre">setattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#setattr" title="Link to this definition">¶</a></dt>
<dd><p>This is the counterpart of <a class="reference internal" href="#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a>.  The arguments are an object, a
string, and an arbitrary value.  The string may name an existing attribute or a
new attribute.  The function assigns the value to the attribute, provided the
object allows it.  For example, <code class="docutils literal notranslate"><span class="pre">setattr(x,</span> <span class="pre">'foobar',</span> <span class="pre">123)</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">x.foobar</span> <span class="pre">=</span> <span class="pre">123</span></code>.</p>
<p><em>name</em> need not be a Python identifier as defined in <a class="reference internal" href="../reference/lexical_analysis.html#identifiers"><span class="std std-ref">Identifiers and keywords</span></a>
unless the object chooses to enforce that, for example in a custom
<a class="reference internal" href="../reference/datamodel.html#object.__getattribute__" title="object.__getattribute__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattribute__()</span></code></a> or via <a class="reference internal" href="../reference/datamodel.html#object.__slots__" title="object.__slots__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__slots__</span></code></a>.
An attribute whose name is not an identifier will not be accessible using
the dot notation, but is accessible through <a class="reference internal" href="#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> etc..</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Since <a class="reference internal" href="../reference/expressions.html#private-name-mangling"><span class="std std-ref">private name mangling</span></a> happens at
compilation time, one must manually mangle a private attribute’s
(attributes with two leading underscores) name in order to set it with
<a class="reference internal" href="#setattr" title="setattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">setattr()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="slice">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">slice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stop</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#slice" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">slice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">step</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a <a class="reference internal" href="../glossary.html#term-slice"><span class="xref std std-term">slice</span></a> object representing the set of indices specified by
<code class="docutils literal notranslate"><span class="pre">range(start,</span> <span class="pre">stop,</span> <span class="pre">step)</span></code>.  The <em>start</em> and <em>step</em> arguments default to
<code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="slice.start">
<span class="sig-name descname"><span class="pre">start</span></span><a class="headerlink" href="#slice.start" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="slice.stop">
<span class="sig-name descname"><span class="pre">stop</span></span><a class="headerlink" href="#slice.stop" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="slice.step">
<span class="sig-name descname"><span class="pre">step</span></span><a class="headerlink" href="#slice.step" title="Link to this definition">¶</a></dt>
<dd><p>Slice objects have read-only data attributes <code class="xref py py-attr docutils literal notranslate"><span class="pre">start</span></code>,
<code class="xref py py-attr docutils literal notranslate"><span class="pre">stop</span></code>, and <code class="xref py py-attr docutils literal notranslate"><span class="pre">step</span></code> which merely return the argument
values (or their default).  They have no other explicit functionality;
however, they are used by NumPy and other third-party packages.</p>
</dd></dl>

<p>Slice objects are also generated when extended indexing syntax is used.  For
example: <code class="docutils literal notranslate"><span class="pre">a[start:stop:step]</span></code> or <code class="docutils literal notranslate"><span class="pre">a[start:stop,</span> <span class="pre">i]</span></code>.  See
<a class="reference internal" href="itertools.html#itertools.islice" title="itertools.islice"><code class="xref py py-func docutils literal notranslate"><span class="pre">itertools.islice()</span></code></a> for an alternate version that returns an
<a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Slice objects are now <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a> (provided <a class="reference internal" href="#slice.start" title="slice.start"><code class="xref py py-attr docutils literal notranslate"><span class="pre">start</span></code></a>,
<a class="reference internal" href="#slice.stop" title="slice.stop"><code class="xref py py-attr docutils literal notranslate"><span class="pre">stop</span></code></a>, and <a class="reference internal" href="#slice.step" title="slice.step"><code class="xref py py-attr docutils literal notranslate"><span class="pre">step</span></code></a> are hashable).</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sorted">
<span class="sig-name descname"><span class="pre">sorted</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reverse</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sorted" title="Link to this definition">¶</a></dt>
<dd><p>Return a new sorted list from the items in <em>iterable</em>.</p>
<p>Has two optional arguments which must be specified as keyword arguments.</p>
<p><em>key</em> specifies a function of one argument that is used to extract a comparison
key from each element in <em>iterable</em> (for example, <code class="docutils literal notranslate"><span class="pre">key=str.lower</span></code>).  The
default value is <code class="docutils literal notranslate"><span class="pre">None</span></code> (compare the elements directly).</p>
<p><em>reverse</em> is a boolean value.  If set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, then the list elements are
sorted as if each comparison were reversed.</p>
<p>Use <a class="reference internal" href="functools.html#functools.cmp_to_key" title="functools.cmp_to_key"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.cmp_to_key()</span></code></a> to convert an old-style <em>cmp</em> function to a
<em>key</em> function.</p>
<p>The built-in <a class="reference internal" href="#sorted" title="sorted"><code class="xref py py-func docutils literal notranslate"><span class="pre">sorted()</span></code></a> function is guaranteed to be stable. A sort is
stable if it guarantees not to change the relative order of elements that
compare equal — this is helpful for sorting in multiple passes (for
example, sort by department, then by salary grade).</p>
<p>The sort algorithm uses only <code class="docutils literal notranslate"><span class="pre">&lt;</span></code> comparisons between items.  While
defining an <a class="reference internal" href="../reference/datamodel.html#object.__lt__" title="object.__lt__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__lt__()</span></code></a> method will suffice for sorting,
<span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0008/"><strong>PEP 8</strong></a> recommends that all six <a class="reference internal" href="../reference/expressions.html#comparisons"><span class="std std-ref">rich comparisons</span></a> be implemented.  This will help avoid bugs when using
the same data with other ordering tools such as <a class="reference internal" href="#max" title="max"><code class="xref py py-func docutils literal notranslate"><span class="pre">max()</span></code></a> that rely
on a different underlying method.  Implementing all six comparisons
also helps avoid confusion for mixed type comparisons which can call
reflected the <a class="reference internal" href="../reference/datamodel.html#object.__gt__" title="object.__gt__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__gt__()</span></code></a> method.</p>
<p>For sorting examples and a brief sorting tutorial, see <a class="reference internal" href="../howto/sorting.html#sortinghowto"><span class="std std-ref">Sorting Techniques</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="staticmethod">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-name descname"><span class="pre">staticmethod</span></span><a class="headerlink" href="#staticmethod" title="Link to this definition">¶</a></dt>
<dd><p>Transform a method into a static method.</p>
<p>A static method does not receive an implicit first argument. To declare a static
method, use this idiom:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="n">arg1</span><span class="p">,</span> <span class="n">arg2</span><span class="p">,</span> <span class="n">argN</span><span class="p">):</span> <span class="o">...</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&#64;staticmethod</span></code> form is a function <a class="reference internal" href="../glossary.html#term-decorator"><span class="xref std std-term">decorator</span></a> – see
<a class="reference internal" href="../reference/compound_stmts.html#function"><span class="std std-ref">Function definitions</span></a> for details.</p>
<p>A static method can be called either on the class (such as <code class="docutils literal notranslate"><span class="pre">C.f()</span></code>) or on
an instance (such as <code class="docutils literal notranslate"><span class="pre">C().f()</span></code>). Moreover, they can be called as regular
functions (such as <code class="docutils literal notranslate"><span class="pre">f()</span></code>).</p>
<p>Static methods in Python are similar to those found in Java or C++. Also, see
<a class="reference internal" href="#classmethod" title="classmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">classmethod()</span></code></a> for a variant that is useful for creating alternate class
constructors.</p>
<p>Like all decorators, it is also possible to call <code class="docutils literal notranslate"><span class="pre">staticmethod</span></code> as
a regular function and do something with its result.  This is needed
in some cases where you need a reference to a function from a class
body and you want to avoid the automatic transformation to instance
method.  For these cases, use this idiom:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">regular_function</span><span class="p">():</span>
    <span class="o">...</span>

<span class="k">class</span> <span class="nc">C</span><span class="p">:</span>
    <span class="n">method</span> <span class="o">=</span> <span class="nb">staticmethod</span><span class="p">(</span><span class="n">regular_function</span><span class="p">)</span>
</pre></div>
</div>
<p>For more information on static methods, see <a class="reference internal" href="../reference/datamodel.html#types"><span class="std std-ref">The standard type hierarchy</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Static methods now inherit the method attributes (<code class="docutils literal notranslate"><span class="pre">__module__</span></code>,
<code class="docutils literal notranslate"><span class="pre">__name__</span></code>, <code class="docutils literal notranslate"><span class="pre">__qualname__</span></code>, <code class="docutils literal notranslate"><span class="pre">__doc__</span></code> and <code class="docutils literal notranslate"><span class="pre">__annotations__</span></code>),
have a new <code class="docutils literal notranslate"><span class="pre">__wrapped__</span></code> attribute, and are now callable as regular
functions.</p>
</div>
</dd></dl>

<dl class="py class" id="func-str">
<span id="index-11"></span><dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">str</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">str</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">b''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'utf-8'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'strict'</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> version of <em>object</em>.  See <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> for details.</p>
<p><code class="docutils literal notranslate"><span class="pre">str</span></code> is the built-in string <a class="reference internal" href="../glossary.html#term-class"><span class="xref std std-term">class</span></a>.  For general information
about strings, see <a class="reference internal" href="stdtypes.html#textseq"><span class="std std-ref">Text Sequence Type — str</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sum">
<span class="sig-name descname"><span class="pre">sum</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sum" title="Link to this definition">¶</a></dt>
<dd><p>Sums <em>start</em> and the items of an <em>iterable</em> from left to right and returns the
total.  The <em>iterable</em>’s items are normally numbers, and the start value is not
allowed to be a string.</p>
<p>For some use cases, there are good alternatives to <a class="reference internal" href="#sum" title="sum"><code class="xref py py-func docutils literal notranslate"><span class="pre">sum()</span></code></a>.
The preferred, fast way to concatenate a sequence of strings is by calling
<code class="docutils literal notranslate"><span class="pre">''.join(sequence)</span></code>.  To add floating point values with extended precision,
see <a class="reference internal" href="math.html#math.fsum" title="math.fsum"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.fsum()</span></code></a>.  To concatenate a series of iterables, consider using
<a class="reference internal" href="itertools.html#itertools.chain" title="itertools.chain"><code class="xref py py-func docutils literal notranslate"><span class="pre">itertools.chain()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>start</em> parameter can be specified as a keyword argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Summation of floats switched to an algorithm
that gives higher accuracy on most builds.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="super">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">super</span></span><a class="headerlink" href="#super" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">super</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_or_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return a proxy object that delegates method calls to a parent or sibling
class of <em>type</em>.  This is useful for accessing inherited methods that have
been overridden in a class.</p>
<p>The <em>object_or_type</em> determines the <a class="reference internal" href="../glossary.html#term-method-resolution-order"><span class="xref std std-term">method resolution order</span></a>
to be searched.  The search starts from the class right after the
<em>type</em>.</p>
<p>For example, if <a class="reference internal" href="stdtypes.html#class.__mro__" title="class.__mro__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__mro__</span></code></a> of <em>object_or_type</em> is
<code class="docutils literal notranslate"><span class="pre">D</span> <span class="pre">-&gt;</span> <span class="pre">B</span> <span class="pre">-&gt;</span> <span class="pre">C</span> <span class="pre">-&gt;</span> <span class="pre">A</span> <span class="pre">-&gt;</span> <span class="pre">object</span></code> and the value of <em>type</em> is <code class="docutils literal notranslate"><span class="pre">B</span></code>,
then <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> searches <code class="docutils literal notranslate"><span class="pre">C</span> <span class="pre">-&gt;</span> <span class="pre">A</span> <span class="pre">-&gt;</span> <span class="pre">object</span></code>.</p>
<p>The <a class="reference internal" href="stdtypes.html#class.__mro__" title="class.__mro__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__mro__</span></code></a> attribute of the <em>object_or_type</em> lists the method
resolution search order used by both <a class="reference internal" href="#getattr" title="getattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">getattr()</span></code></a> and <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a>.  The
attribute is dynamic and can change whenever the inheritance hierarchy is
updated.</p>
<p>If the second argument is omitted, the super object returned is unbound.  If
the second argument is an object, <code class="docutils literal notranslate"><span class="pre">isinstance(obj,</span> <span class="pre">type)</span></code> must be true.  If
the second argument is a type, <code class="docutils literal notranslate"><span class="pre">issubclass(type2,</span> <span class="pre">type)</span></code> must be true (this
is useful for classmethods).</p>
<p>There are two typical use cases for <em>super</em>.  In a class hierarchy with
single inheritance, <em>super</em> can be used to refer to parent classes without
naming them explicitly, thus making the code more maintainable.  This use
closely parallels the use of <em>super</em> in other programming languages.</p>
<p>The second use case is to support cooperative multiple inheritance in a
dynamic execution environment.  This use case is unique to Python and is
not found in statically compiled languages or languages that only support
single inheritance.  This makes it possible to implement “diamond diagrams”
where multiple base classes implement the same method.  Good design dictates
that such implementations have the same calling signature in every case (because the
order of calls is determined at runtime, because that order adapts
to changes in the class hierarchy, and because that order can include
sibling classes that are unknown prior to runtime).</p>
<p>For both use cases, a typical superclass call looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">C</span><span class="p">(</span><span class="n">B</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">method</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">arg</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">method</span><span class="p">(</span><span class="n">arg</span><span class="p">)</span>    <span class="c1"># This does the same thing as:</span>
                               <span class="c1"># super(C, self).method(arg)</span>
</pre></div>
</div>
<p>In addition to method lookups, <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> also works for attribute
lookups.  One possible use case for this is calling <a class="reference internal" href="../glossary.html#term-descriptor"><span class="xref std std-term">descriptors</span></a>
in a parent or sibling class.</p>
<p>Note that <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> is implemented as part of the binding process for
explicit dotted attribute lookups such as <code class="docutils literal notranslate"><span class="pre">super().__getitem__(name)</span></code>.
It does so by implementing its own <a class="reference internal" href="../reference/datamodel.html#object.__getattribute__" title="object.__getattribute__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getattribute__()</span></code></a> method
for searching
classes in a predictable order that supports cooperative multiple inheritance.
Accordingly, <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> is undefined for implicit lookups using statements or
operators such as <code class="docutils literal notranslate"><span class="pre">super()[name]</span></code>.</p>
<p>Also note that, aside from the zero argument form, <a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a> is not
limited to use inside methods.  The two argument form specifies the
arguments exactly and makes the appropriate references.  The zero
argument form only works inside a class definition, as the compiler fills
in the necessary details to correctly retrieve the class being defined,
as well as accessing the current instance for ordinary methods.</p>
<p>For practical suggestions on how to design cooperative classes using
<a class="reference internal" href="#super" title="super"><code class="xref py py-func docutils literal notranslate"><span class="pre">super()</span></code></a>, see <a class="reference external" href="https://rhettinger.wordpress.com/2011/05/26/super-considered-super/">guide to using super()</a>.</p>
</dd></dl>

<dl class="py class" id="func-tuple">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tuple</span></span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">tuple</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Rather than being a function, <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> is actually an immutable
sequence type, as documented in <a class="reference internal" href="stdtypes.html#typesseq-tuple"><span class="std std-ref">Tuples</span></a> and <a class="reference internal" href="stdtypes.html#typesseq"><span class="std std-ref">Sequence Types — list, tuple, range</span></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="type">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#type" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bases</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dict</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span></dt>
<dd><p id="index-12">With one argument, return the type of an <em>object</em>.  The return value is a
type object and generally the same object as returned by
<a class="reference internal" href="stdtypes.html#instance.__class__" title="instance.__class__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">object.__class__</span></code></a>.</p>
<p>The <a class="reference internal" href="#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> built-in function is recommended for testing the type
of an object, because it takes subclasses into account.</p>
<p>With three arguments, return a new type object.  This is essentially a
dynamic form of the <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> statement. The <em>name</em> string is
the class name and becomes the <a class="reference internal" href="stdtypes.html#definition.__name__" title="definition.__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> attribute.
The <em>bases</em> tuple contains the base classes and becomes the
<a class="reference internal" href="stdtypes.html#class.__bases__" title="class.__bases__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__bases__</span></code></a> attribute; if empty, <a class="reference internal" href="#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>, the
ultimate base of all classes, is added.  The <em>dict</em> dictionary contains
attribute and method definitions for the class body; it may be copied
or wrapped before becoming the <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute.
The following two statements create identical <a class="reference internal" href="#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> objects:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">X</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">a</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">X</span> <span class="o">=</span> <span class="nb">type</span><span class="p">(</span><span class="s1">&#39;X&#39;</span><span class="p">,</span> <span class="p">(),</span> <span class="nb">dict</span><span class="p">(</span><span class="n">a</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>
</pre></div>
</div>
<p>See also <a class="reference internal" href="stdtypes.html#bltin-type-objects"><span class="std std-ref">Type Objects</span></a>.</p>
<p>Keyword arguments provided to the three argument form are passed to the
appropriate metaclass machinery (usually <a class="reference internal" href="../reference/datamodel.html#object.__init_subclass__" title="object.__init_subclass__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__init_subclass__()</span></code></a>)
in the same way that keywords in a class
definition (besides <em>metaclass</em>) would.</p>
<p>See also <a class="reference internal" href="../reference/datamodel.html#class-customization"><span class="std std-ref">Customizing class creation</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Subclasses of <a class="reference internal" href="#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a> which don’t override <code class="docutils literal notranslate"><span class="pre">type.__new__</span></code> may no
longer use the one-argument form to get the type of an object.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="vars">
<span class="sig-name descname"><span class="pre">vars</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#vars" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">vars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Return the <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute for a module, class, instance,
or any other object with a <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute.</p>
<p>Objects such as modules and instances have an updateable <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a>
attribute; however, other objects may have write restrictions on their
<a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attributes (for example, classes use a
<a class="reference internal" href="types.html#types.MappingProxyType" title="types.MappingProxyType"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.MappingProxyType</span></code></a> to prevent direct dictionary updates).</p>
<p>Without an argument, <a class="reference internal" href="#vars" title="vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">vars()</span></code></a> acts like <a class="reference internal" href="#locals" title="locals"><code class="xref py py-func docutils literal notranslate"><span class="pre">locals()</span></code></a>.  Note, the
locals dictionary is only useful for reads since updates to the locals
dictionary are ignored.</p>
<p>A <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> exception is raised if an object is specified but
it doesn’t have a <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a> attribute (for example, if
its class defines the <a class="reference internal" href="../reference/datamodel.html#object.__slots__" title="object.__slots__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__slots__</span></code></a> attribute).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zip">
<span class="sig-name descname"><span class="pre">zip</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">iterables</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zip" title="Link to this definition">¶</a></dt>
<dd><p>Iterate over several iterables in parallel, producing tuples with an item
from each one.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">([</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">],</span> <span class="p">[</span><span class="s1">&#39;sugar&#39;</span><span class="p">,</span> <span class="s1">&#39;spice&#39;</span><span class="p">,</span> <span class="s1">&#39;everything nice&#39;</span><span class="p">]):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">item</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">(1, &#39;sugar&#39;)</span>
<span class="go">(2, &#39;spice&#39;)</span>
<span class="go">(3, &#39;everything nice&#39;)</span>
</pre></div>
</div>
<p>More formally: <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> returns an iterator of tuples, where the <em>i</em>-th
tuple contains the <em>i</em>-th element from each of the argument iterables.</p>
<p>Another way to think of <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> is that it turns rows into columns, and
columns into rows.  This is similar to <a class="reference external" href="https://en.wikipedia.org/wiki/Transpose">transposing a matrix</a>.</p>
<p><a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> is lazy: The elements won’t be processed until the iterable is
iterated on, e.g. by a <code class="xref std std-keyword docutils literal notranslate"><span class="pre">for</span></code> loop or by wrapping in a
<a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>.</p>
<p>One thing to consider is that the iterables passed to <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> could have
different lengths; sometimes by design, and sometimes because of a bug in
the code that prepared these iterables.  Python offers three different
approaches to dealing with this issue:</p>
<ul>
<li><p>By default, <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> stops when the shortest iterable is exhausted.
It will ignore the remaining items in the longer iterables, cutting off
the result to the length of the shortest iterable:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">),</span> <span class="p">[</span><span class="s1">&#39;fee&#39;</span><span class="p">,</span> <span class="s1">&#39;fi&#39;</span><span class="p">,</span> <span class="s1">&#39;fo&#39;</span><span class="p">,</span> <span class="s1">&#39;fum&#39;</span><span class="p">]))</span>
<span class="go">[(0, &#39;fee&#39;), (1, &#39;fi&#39;), (2, &#39;fo&#39;)]</span>
</pre></div>
</div>
</li>
<li><p><a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> is often used in cases where the iterables are assumed to be
of equal length.  In such cases, it’s recommended to use the <code class="docutils literal notranslate"><span class="pre">strict=True</span></code>
option. Its output is the same as regular <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">zip</span><span class="p">((</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">),</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span> <span class="n">strict</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="go">[(&#39;a&#39;, 1), (&#39;b&#39;, 2), (&#39;c&#39;, 3)]</span>
</pre></div>
</div>
<p>Unlike the default behavior, it raises a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if one iterable
is exhausted before the others:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">),</span> <span class="p">[</span><span class="s1">&#39;fee&#39;</span><span class="p">,</span> <span class="s1">&#39;fi&#39;</span><span class="p">,</span> <span class="s1">&#39;fo&#39;</span><span class="p">,</span> <span class="s1">&#39;fum&#39;</span><span class="p">],</span> <span class="n">strict</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>  
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">item</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">(0, &#39;fee&#39;)</span>
<span class="go">(1, &#39;fi&#39;)</span>
<span class="go">(2, &#39;fo&#39;)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">zip() argument 2 is longer than argument 1</span>
</pre></div>
</div>
<p>Without the <code class="docutils literal notranslate"><span class="pre">strict=True</span></code> argument, any bug that results in iterables of
different lengths will be silenced, possibly manifesting as a hard-to-find
bug in another part of the program.</p>
</li>
<li><p>Shorter iterables can be padded with a constant value to make all the
iterables have the same length.  This is done by
<a class="reference internal" href="itertools.html#itertools.zip_longest" title="itertools.zip_longest"><code class="xref py py-func docutils literal notranslate"><span class="pre">itertools.zip_longest()</span></code></a>.</p></li>
</ul>
<p>Edge cases: With a single iterable argument, <a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> returns an
iterator of 1-tuples.  With no arguments, it returns an empty iterator.</p>
<p>Tips and tricks:</p>
<ul>
<li><p>The left-to-right evaluation order of the iterables is guaranteed. This
makes possible an idiom for clustering a data series into n-length groups
using <code class="docutils literal notranslate"><span class="pre">zip(*[iter(s)]*n,</span> <span class="pre">strict=True)</span></code>.  This repeats the <em>same</em> iterator
<code class="docutils literal notranslate"><span class="pre">n</span></code> times so that each output tuple has the result of <code class="docutils literal notranslate"><span class="pre">n</span></code> calls to the
iterator. This has the effect of dividing the input into n-length chunks.</p></li>
<li><p><a class="reference internal" href="#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> in conjunction with the <code class="docutils literal notranslate"><span class="pre">*</span></code> operator can be used to unzip a
list:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">y</span> <span class="o">=</span> <span class="p">[</span><span class="mi">4</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">6</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">))</span>
<span class="go">[(1, 4), (2, 5), (3, 6)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x2</span><span class="p">,</span> <span class="n">y2</span> <span class="o">=</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="nb">zip</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">==</span> <span class="nb">list</span><span class="p">(</span><span class="n">x2</span><span class="p">)</span> <span class="ow">and</span> <span class="n">y</span> <span class="o">==</span> <span class="nb">list</span><span class="p">(</span><span class="n">y2</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
</li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="docutils literal notranslate"><span class="pre">strict</span></code> argument.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="import__">
<span class="sig-name descname"><span class="pre">__import__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fromlist</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#import__" title="Link to this definition">¶</a></dt>
<dd><div class="admonition note" id="index-13">
<p class="admonition-title">Note</p>
<p>This is an advanced function that is not needed in everyday Python
programming, unlike <a class="reference internal" href="importlib.html#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a>.</p>
</div>
<p>This function is invoked by the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement.  It can be
replaced (by importing the <a class="reference internal" href="builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a> module and assigning to
<code class="docutils literal notranslate"><span class="pre">builtins.__import__</span></code>) in order to change semantics of the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code> statement, but doing so is <strong>strongly</strong> discouraged as it
is usually simpler to use import hooks (see <span class="target" id="index-14"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a>) to attain the same
goals and does not cause issues with code which assumes the default import
implementation is in use.  Direct use of <a class="reference internal" href="#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> is also
discouraged in favor of <a class="reference internal" href="importlib.html#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a>.</p>
<p>The function imports the module <em>name</em>, potentially using the given <em>globals</em>
and <em>locals</em> to determine how to interpret the name in a package context.
The <em>fromlist</em> gives the names of objects or submodules that should be
imported from the module given by <em>name</em>.  The standard implementation does
not use its <em>locals</em> argument at all and uses its <em>globals</em> only to
determine the package context of the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement.</p>
<p><em>level</em> specifies whether to use absolute or relative imports. <code class="docutils literal notranslate"><span class="pre">0</span></code> (the
default) means only perform absolute imports.  Positive values for
<em>level</em> indicate the number of parent directories to search relative to the
directory of the module calling <a class="reference internal" href="#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> (see <span class="target" id="index-15"></span><a class="pep reference external" href="https://peps.python.org/pep-0328/"><strong>PEP 328</strong></a> for the
details).</p>
<p>When the <em>name</em> variable is of the form <code class="docutils literal notranslate"><span class="pre">package.module</span></code>, normally, the
top-level package (the name up till the first dot) is returned, <em>not</em> the
module named by <em>name</em>.  However, when a non-empty <em>fromlist</em> argument is
given, the module named by <em>name</em> is returned.</p>
<p>For example, the statement <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">spam</span></code> results in bytecode resembling the
following code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">spam</span> <span class="o">=</span> <span class="nb">__import__</span><span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="nb">globals</span><span class="p">(),</span> <span class="nb">locals</span><span class="p">(),</span> <span class="p">[],</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>The statement <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">spam.ham</span></code> results in this call:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">spam</span> <span class="o">=</span> <span class="nb">__import__</span><span class="p">(</span><span class="s1">&#39;spam.ham&#39;</span><span class="p">,</span> <span class="nb">globals</span><span class="p">(),</span> <span class="nb">locals</span><span class="p">(),</span> <span class="p">[],</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>Note how <a class="reference internal" href="#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> returns the toplevel module here because this is
the object that is bound to a name by the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement.</p>
<p>On the other hand, the statement <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">spam.ham</span> <span class="pre">import</span> <span class="pre">eggs,</span> <span class="pre">sausage</span> <span class="pre">as</span>
<span class="pre">saus</span></code> results in</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">_temp</span> <span class="o">=</span> <span class="nb">__import__</span><span class="p">(</span><span class="s1">&#39;spam.ham&#39;</span><span class="p">,</span> <span class="nb">globals</span><span class="p">(),</span> <span class="nb">locals</span><span class="p">(),</span> <span class="p">[</span><span class="s1">&#39;eggs&#39;</span><span class="p">,</span> <span class="s1">&#39;sausage&#39;</span><span class="p">],</span> <span class="mi">0</span><span class="p">)</span>
<span class="n">eggs</span> <span class="o">=</span> <span class="n">_temp</span><span class="o">.</span><span class="n">eggs</span>
<span class="n">saus</span> <span class="o">=</span> <span class="n">_temp</span><span class="o">.</span><span class="n">sausage</span>
</pre></div>
</div>
<p>Here, the <code class="docutils literal notranslate"><span class="pre">spam.ham</span></code> module is returned from <a class="reference internal" href="#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a>.  From this
object, the names to import are retrieved and assigned to their respective
names.</p>
<p>If you simply want to import a module (potentially within a package) by name,
use <a class="reference internal" href="importlib.html#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Negative values for <em>level</em> are no longer supported (which also changes
the default value to 0).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>When the command line options <a class="reference internal" href="../using/cmdline.html#cmdoption-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">-E</span></code></a> or <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> are being used,
the environment variable <span class="target" id="index-16"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONCASEOK"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONCASEOK</span></code></a> is now ignored.</p>
</div>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id2" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>Note that the parser only accepts the Unix-style end of line convention.
If you are reading the code from a file, make sure to use newline conversion
mode to convert Windows or Mac-style newlines.</p>
</aside>
</aside>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="intro.html"
                          title="previous chapter">Introduction</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="constants.html"
                          title="next chapter">Built-in Constants</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/functions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="constants.html" title="Built-in Constants"
             >next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Built-in Functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>