<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">PayUMoney Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='provider_creation_warning']" position="after">
                <div class="alert alert-danger"
                     role="alert"
                     invisible="code != 'payumoney'">
                    This provider is deprecated.
                    Consider disabling it and moving to <strong>Razorpay</strong>.
                </div>
            </xpath>
            <group name="provider_credentials" position="inside">
                <group invisible="code != 'payumoney'">
                    <field name="payumoney_merchant_key" required="code == 'payumoney' and state != 'disabled'"/>
                    <field name="payumoney_merchant_salt" required="code == 'payumoney' and state != 'disabled'" password="True"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
