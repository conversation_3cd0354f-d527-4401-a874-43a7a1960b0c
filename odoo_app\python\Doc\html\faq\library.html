<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Library and Extension FAQ" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/faq/library.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Contents: Library and Extension FAQ- General Library Questions- How do I find a module or application to perform task X?, Where is the math.py (socket.py, regex.py, etc.) source file?, How do I mak..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Contents: Library and Extension FAQ- General Library Questions- How do I find a module or application to perform task X?, Where is the math.py (socket.py, regex.py, etc.) source file?, How do I mak..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Library and Extension FAQ &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Extending/Embedding FAQ" href="extending.html" />
    <link rel="prev" title="Design and History FAQ" href="design.html" />
    <link rel="canonical" href="https://docs.python.org/3/faq/library.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Library and Extension FAQ</a><ul>
<li><a class="reference internal" href="#general-library-questions">General Library Questions</a></li>
<li><a class="reference internal" href="#common-tasks">Common tasks</a></li>
<li><a class="reference internal" href="#threads">Threads</a></li>
<li><a class="reference internal" href="#input-and-output">Input and Output</a></li>
<li><a class="reference internal" href="#network-internet-programming">Network/Internet Programming</a></li>
<li><a class="reference internal" href="#databases">Databases</a></li>
<li><a class="reference internal" href="#mathematics-and-numerics">Mathematics and Numerics</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="design.html"
                          title="previous chapter">Design and History FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="extending.html"
                          title="next chapter">Extending/Embedding FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/library.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="extending.html" title="Extending/Embedding FAQ"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="design.html" title="Design and History FAQ"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Library and Extension FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="library-and-extension-faq">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">Library and Extension FAQ</a><a class="headerlink" href="#library-and-extension-faq" title="Link to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#library-and-extension-faq" id="id1">Library and Extension FAQ</a></p>
<ul>
<li><p><a class="reference internal" href="#general-library-questions" id="id2">General Library Questions</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-find-a-module-or-application-to-perform-task-x" id="id3">How do I find a module or application to perform task X?</a></p></li>
<li><p><a class="reference internal" href="#where-is-the-math-py-socket-py-regex-py-etc-source-file" id="id4">Where is the math.py (socket.py, regex.py, etc.) source file?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-make-a-python-script-executable-on-unix" id="id5">How do I make a Python script executable on Unix?</a></p></li>
<li><p><a class="reference internal" href="#is-there-a-curses-termcap-package-for-python" id="id6">Is there a curses/termcap package for Python?</a></p></li>
<li><p><a class="reference internal" href="#is-there-an-equivalent-to-c-s-onexit-in-python" id="id7">Is there an equivalent to C’s onexit() in Python?</a></p></li>
<li><p><a class="reference internal" href="#why-don-t-my-signal-handlers-work" id="id8">Why don’t my signal handlers work?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#common-tasks" id="id9">Common tasks</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-test-a-python-program-or-component" id="id10">How do I test a Python program or component?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-create-documentation-from-doc-strings" id="id11">How do I create documentation from doc strings?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-get-a-single-keypress-at-a-time" id="id12">How do I get a single keypress at a time?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#threads" id="id13">Threads</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-program-using-threads" id="id14">How do I program using threads?</a></p></li>
<li><p><a class="reference internal" href="#none-of-my-threads-seem-to-run-why" id="id15">None of my threads seem to run: why?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-parcel-out-work-among-a-bunch-of-worker-threads" id="id16">How do I parcel out work among a bunch of worker threads?</a></p></li>
<li><p><a class="reference internal" href="#what-kinds-of-global-value-mutation-are-thread-safe" id="id17">What kinds of global value mutation are thread-safe?</a></p></li>
<li><p><a class="reference internal" href="#can-t-we-get-rid-of-the-global-interpreter-lock" id="id18">Can’t we get rid of the Global Interpreter Lock?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#input-and-output" id="id19">Input and Output</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-delete-a-file-and-other-file-questions" id="id20">How do I delete a file? (And other file questions…)</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-copy-a-file" id="id21">How do I copy a file?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-read-or-write-binary-data" id="id22">How do I read (or write) binary data?</a></p></li>
<li><p><a class="reference internal" href="#i-can-t-seem-to-use-os-read-on-a-pipe-created-with-os-popen-why" id="id23">I can’t seem to use os.read() on a pipe created with os.popen(); why?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-access-the-serial-rs232-port" id="id24">How do I access the serial (RS232) port?</a></p></li>
<li><p><a class="reference internal" href="#why-doesn-t-closing-sys-stdout-stdin-stderr-really-close-it" id="id25">Why doesn’t closing sys.stdout (stdin, stderr) really close it?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#network-internet-programming" id="id26">Network/Internet Programming</a></p>
<ul>
<li><p><a class="reference internal" href="#what-www-tools-are-there-for-python" id="id27">What WWW tools are there for Python?</a></p></li>
<li><p><a class="reference internal" href="#how-can-i-mimic-cgi-form-submission-method-post" id="id28">How can I mimic CGI form submission (METHOD=POST)?</a></p></li>
<li><p><a class="reference internal" href="#what-module-should-i-use-to-help-with-generating-html" id="id29">What module should I use to help with generating HTML?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-send-mail-from-a-python-script" id="id30">How do I send mail from a Python script?</a></p></li>
<li><p><a class="reference internal" href="#how-do-i-avoid-blocking-in-the-connect-method-of-a-socket" id="id31">How do I avoid blocking in the connect() method of a socket?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#databases" id="id32">Databases</a></p>
<ul>
<li><p><a class="reference internal" href="#are-there-any-interfaces-to-database-packages-in-python" id="id33">Are there any interfaces to database packages in Python?</a></p></li>
<li><p><a class="reference internal" href="#how-do-you-implement-persistent-objects-in-python" id="id34">How do you implement persistent objects in Python?</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#mathematics-and-numerics" id="id35">Mathematics and Numerics</a></p>
<ul>
<li><p><a class="reference internal" href="#how-do-i-generate-random-numbers-in-python" id="id36">How do I generate random numbers in Python?</a></p></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
<section id="general-library-questions">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">General Library Questions</a><a class="headerlink" href="#general-library-questions" title="Link to this heading">¶</a></h2>
<section id="how-do-i-find-a-module-or-application-to-perform-task-x">
<h3><a class="toc-backref" href="#id3" role="doc-backlink">How do I find a module or application to perform task X?</a><a class="headerlink" href="#how-do-i-find-a-module-or-application-to-perform-task-x" title="Link to this heading">¶</a></h3>
<p>Check <a class="reference internal" href="../library/index.html#library-index"><span class="std std-ref">the Library Reference</span></a> to see if there’s a relevant
standard library module.  (Eventually you’ll learn what’s in the standard
library and will be able to skip this step.)</p>
<p>For third-party packages, search the <a class="reference external" href="https://pypi.org">Python Package Index</a> or try <a class="reference external" href="https://www.google.com">Google</a> or
another web search engine.  Searching for “Python” plus a keyword or two for
your topic of interest will usually find something helpful.</p>
</section>
<section id="where-is-the-math-py-socket-py-regex-py-etc-source-file">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Where is the math.py (socket.py, regex.py, etc.) source file?</a><a class="headerlink" href="#where-is-the-math-py-socket-py-regex-py-etc-source-file" title="Link to this heading">¶</a></h3>
<p>If you can’t find a source file for a module it may be a built-in or
dynamically loaded module implemented in C, C++ or other compiled language.
In this case you may not have the source file or it may be something like
<code class="file docutils literal notranslate"><span class="pre">mathmodule.c</span></code>, somewhere in a C source directory (not on the Python Path).</p>
<p>There are (at least) three kinds of modules in Python:</p>
<ol class="arabic">
<li><p>modules written in Python (.py);</p></li>
<li><p>modules written in C and dynamically loaded (.dll, .pyd, .so, .sl, etc);</p></li>
<li><p>modules written in C and linked with the interpreter; to get a list of these,
type:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span>
<span class="nb">print</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">builtin_module_names</span><span class="p">)</span>
</pre></div>
</div>
</li>
</ol>
</section>
<section id="how-do-i-make-a-python-script-executable-on-unix">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">How do I make a Python script executable on Unix?</a><a class="headerlink" href="#how-do-i-make-a-python-script-executable-on-unix" title="Link to this heading">¶</a></h3>
<p>You need to do two things: the script file’s mode must be executable and the
first line must begin with <code class="docutils literal notranslate"><span class="pre">#!</span></code> followed by the path of the Python
interpreter.</p>
<p>The first is done by executing <code class="docutils literal notranslate"><span class="pre">chmod</span> <span class="pre">+x</span> <span class="pre">scriptfile</span></code> or perhaps <code class="docutils literal notranslate"><span class="pre">chmod</span> <span class="pre">755</span>
<span class="pre">scriptfile</span></code>.</p>
<p>The second can be done in a number of ways.  The most straightforward way is to
write</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="ch">#!/usr/local/bin/python</span>
</pre></div>
</div>
<p>as the very first line of your file, using the pathname for where the Python
interpreter is installed on your platform.</p>
<p>If you would like the script to be independent of where the Python interpreter
lives, you can use the <strong class="program">env</strong> program.  Almost all Unix variants support
the following, assuming the Python interpreter is in a directory on the user’s
<span class="target" id="index-0"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="ch">#!/usr/bin/env python</span>
</pre></div>
</div>
<p><em>Don’t</em> do this for CGI scripts.  The <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code> variable for CGI scripts is
often very minimal, so you need to use the actual absolute pathname of the
interpreter.</p>
<p>Occasionally, a user’s environment is so full that the <strong class="program">/usr/bin/env</strong>
program fails; or there’s no env program at all.  In that case, you can try the
following hack (due to Alex Rezinsky):</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="ch">#! /bin/sh</span>
<span class="s2">&quot;&quot;&quot;:&quot;</span>
<span class="nb">exec</span><span class="w"> </span>python<span class="w"> </span><span class="nv">$0</span><span class="w"> </span><span class="si">${</span><span class="nv">1</span><span class="p">+</span><span class="s2">&quot;</span><span class="nv">$@</span><span class="s2">&quot;</span><span class="si">}</span>
<span class="s2">&quot;&quot;&quot;</span>
</pre></div>
</div>
<p>The minor disadvantage is that this defines the script’s __doc__ string.
However, you can fix that by adding</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="vm">__doc__</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;...Whatever...&quot;&quot;&quot;</span>
</pre></div>
</div>
</section>
<section id="is-there-a-curses-termcap-package-for-python">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Is there a curses/termcap package for Python?</a><a class="headerlink" href="#is-there-a-curses-termcap-package-for-python" title="Link to this heading">¶</a></h3>
<p>For Unix variants: The standard Python source distribution comes with a curses
module in the <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Modules">Modules</a> subdirectory, though it’s not compiled by default.
(Note that this is not available in the Windows distribution – there is no
curses module for Windows.)</p>
<p>The <a class="reference internal" href="../library/curses.html#module-curses" title="curses: An interface to the curses library, providing portable terminal handling. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">curses</span></code></a> module supports basic curses features as well as many additional
functions from ncurses and SYSV curses such as colour, alternative character set
support, pads, and mouse support. This means the module isn’t compatible with
operating systems that only have BSD curses, but there don’t seem to be any
currently maintained OSes that fall into this category.</p>
</section>
<section id="is-there-an-equivalent-to-c-s-onexit-in-python">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Is there an equivalent to C’s onexit() in Python?</a><a class="headerlink" href="#is-there-an-equivalent-to-c-s-onexit-in-python" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/atexit.html#module-atexit" title="atexit: Register and execute cleanup functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code></a> module provides a register function that is similar to C’s
<code class="xref c c-func docutils literal notranslate"><span class="pre">onexit()</span></code>.</p>
</section>
<section id="why-don-t-my-signal-handlers-work">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Why don’t my signal handlers work?</a><a class="headerlink" href="#why-don-t-my-signal-handlers-work" title="Link to this heading">¶</a></h3>
<p>The most common problem is that the signal handler is declared with the wrong
argument list.  It is called as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">handler</span><span class="p">(</span><span class="n">signum</span><span class="p">,</span> <span class="n">frame</span><span class="p">)</span>
</pre></div>
</div>
<p>so it should be declared with two parameters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">handler</span><span class="p">(</span><span class="n">signum</span><span class="p">,</span> <span class="n">frame</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
</section>
</section>
<section id="common-tasks">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Common tasks</a><a class="headerlink" href="#common-tasks" title="Link to this heading">¶</a></h2>
<section id="how-do-i-test-a-python-program-or-component">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">How do I test a Python program or component?</a><a class="headerlink" href="#how-do-i-test-a-python-program-or-component" title="Link to this heading">¶</a></h3>
<p>Python comes with two testing frameworks.  The <a class="reference internal" href="../library/doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a> module finds
examples in the docstrings for a module and runs them, comparing the output with
the expected output given in the docstring.</p>
<p>The <a class="reference internal" href="../library/unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> module is a fancier testing framework modelled on Java and
Smalltalk testing frameworks.</p>
<p>To make testing easier, you should use good modular design in your program.
Your program should have almost all functionality
encapsulated in either functions or class methods – and this sometimes has the
surprising and delightful effect of making the program run faster (because local
variable accesses are faster than global accesses).  Furthermore the program
should avoid depending on mutating global variables, since this makes testing
much more difficult to do.</p>
<p>The “global main logic” of your program may be as simple as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">main_logic</span><span class="p">()</span>
</pre></div>
</div>
<p>at the bottom of the main module of your program.</p>
<p>Once your program is organized as a tractable collection of function and class
behaviours, you should write test functions that exercise the behaviours.  A
test suite that automates a sequence of tests can be associated with each module.
This sounds like a lot of work, but since Python is so terse and flexible it’s
surprisingly easy.  You can make coding much more pleasant and fun by writing
your test functions in parallel with the “production code”, since this makes it
easy to find bugs and even design flaws earlier.</p>
<p>“Support modules” that are not intended to be the main module of a program may
include a self-test of the module.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">self_test</span><span class="p">()</span>
</pre></div>
</div>
<p>Even programs that interact with complex external interfaces may be tested when
the external interfaces are unavailable by using “fake” interfaces implemented
in Python.</p>
</section>
<section id="how-do-i-create-documentation-from-doc-strings">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">How do I create documentation from doc strings?</a><a class="headerlink" href="#how-do-i-create-documentation-from-doc-strings" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/pydoc.html#module-pydoc" title="pydoc: Documentation generator and online help system."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code></a> module can create HTML from the doc strings in your Python
source code.  An alternative for creating API documentation purely from
docstrings is <a class="reference external" href="https://epydoc.sourceforge.net/">epydoc</a>.  <a class="reference external" href="https://www.sphinx-doc.org">Sphinx</a> can also include docstring content.</p>
</section>
<section id="how-do-i-get-a-single-keypress-at-a-time">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">How do I get a single keypress at a time?</a><a class="headerlink" href="#how-do-i-get-a-single-keypress-at-a-time" title="Link to this heading">¶</a></h3>
<p>For Unix variants there are several solutions.  It’s straightforward to do this
using curses, but curses is a fairly large module to learn.</p>
</section>
</section>
<section id="threads">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Threads</a><a class="headerlink" href="#threads" title="Link to this heading">¶</a></h2>
<section id="how-do-i-program-using-threads">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">How do I program using threads?</a><a class="headerlink" href="#how-do-i-program-using-threads" title="Link to this heading">¶</a></h3>
<p>Be sure to use the <a class="reference internal" href="../library/threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module and not the <a class="reference internal" href="../library/_thread.html#module-_thread" title="_thread: Low-level threading API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">_thread</span></code></a> module.
The <a class="reference internal" href="../library/threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module builds convenient abstractions on top of the
low-level primitives provided by the <a class="reference internal" href="../library/_thread.html#module-_thread" title="_thread: Low-level threading API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">_thread</span></code></a> module.</p>
</section>
<section id="none-of-my-threads-seem-to-run-why">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">None of my threads seem to run: why?</a><a class="headerlink" href="#none-of-my-threads-seem-to-run-why" title="Link to this heading">¶</a></h3>
<p>As soon as the main thread exits, all threads are killed.  Your main thread is
running too quickly, giving the threads no time to do any work.</p>
<p>A simple fix is to add a sleep to the end of the program that’s long enough for
all the threads to finish:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">threading</span><span class="o">,</span> <span class="nn">time</span>

<span class="k">def</span> <span class="nf">thread_task</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">n</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">i</span><span class="p">)</span>

<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">):</span>
    <span class="n">T</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">thread_task</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">),</span> <span class="n">i</span><span class="p">))</span>
    <span class="n">T</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>  <span class="c1"># &lt;---------------------------!</span>
</pre></div>
</div>
<p>But now (on many platforms) the threads don’t run in parallel, but appear to run
sequentially, one at a time!  The reason is that the OS thread scheduler doesn’t
start a new thread until the previous thread is blocked.</p>
<p>A simple fix is to add a tiny sleep to the start of the run function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">thread_task</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">n</span><span class="p">):</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.001</span><span class="p">)</span>  <span class="c1"># &lt;--------------------!</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">i</span><span class="p">)</span>

<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">):</span>
    <span class="n">T</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">thread_task</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">),</span> <span class="n">i</span><span class="p">))</span>
    <span class="n">T</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
</pre></div>
</div>
<p>Instead of trying to guess a good delay value for <a class="reference internal" href="../library/time.html#time.sleep" title="time.sleep"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.sleep()</span></code></a>,
it’s better to use some kind of semaphore mechanism.  One idea is to use the
<a class="reference internal" href="../library/queue.html#module-queue" title="queue: A synchronized queue class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code></a> module to create a queue object, let each thread append a token to
the queue when it finishes, and let the main thread read as many tokens from the
queue as there are threads.</p>
</section>
<section id="how-do-i-parcel-out-work-among-a-bunch-of-worker-threads">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">How do I parcel out work among a bunch of worker threads?</a><a class="headerlink" href="#how-do-i-parcel-out-work-among-a-bunch-of-worker-threads" title="Link to this heading">¶</a></h3>
<p>The easiest way is to use the <a class="reference internal" href="../library/concurrent.futures.html#module-concurrent.futures" title="concurrent.futures: Execute computations concurrently using threads or processes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent.futures</span></code></a> module,
especially the <a class="reference internal" href="../library/concurrent.futures.html#concurrent.futures.ThreadPoolExecutor" title="concurrent.futures.ThreadPoolExecutor"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ThreadPoolExecutor</span></code></a> class.</p>
<p>Or, if you want fine control over the dispatching algorithm, you can write
your own logic manually.  Use the <a class="reference internal" href="../library/queue.html#module-queue" title="queue: A synchronized queue class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">queue</span></code></a> module to create a queue
containing a list of jobs.  The <a class="reference internal" href="../library/queue.html#queue.Queue" title="queue.Queue"><code class="xref py py-class docutils literal notranslate"><span class="pre">Queue</span></code></a> class maintains a
list of objects and has a <code class="docutils literal notranslate"><span class="pre">.put(obj)</span></code> method that adds items to the queue and
a <code class="docutils literal notranslate"><span class="pre">.get()</span></code> method to return them.  The class will take care of the locking
necessary to ensure that each job is handed out exactly once.</p>
<p>Here’s a trivial example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">threading</span><span class="o">,</span> <span class="nn">queue</span><span class="o">,</span> <span class="nn">time</span>

<span class="c1"># The worker thread gets jobs off the queue.  When the queue is empty, it</span>
<span class="c1"># assumes there will be no more work and exits.</span>
<span class="c1"># (Realistically workers will run until terminated.)</span>
<span class="k">def</span> <span class="nf">worker</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Running worker&#39;</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">arg</span> <span class="o">=</span> <span class="n">q</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">block</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
        <span class="k">except</span> <span class="n">queue</span><span class="o">.</span><span class="n">Empty</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Worker&#39;</span><span class="p">,</span> <span class="n">threading</span><span class="o">.</span><span class="n">current_thread</span><span class="p">(),</span> <span class="n">end</span><span class="o">=</span><span class="s1">&#39; &#39;</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;queue empty&#39;</span><span class="p">)</span>
            <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Worker&#39;</span><span class="p">,</span> <span class="n">threading</span><span class="o">.</span><span class="n">current_thread</span><span class="p">(),</span> <span class="n">end</span><span class="o">=</span><span class="s1">&#39; &#39;</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;running with argument&#39;</span><span class="p">,</span> <span class="n">arg</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

<span class="c1"># Create queue</span>
<span class="n">q</span> <span class="o">=</span> <span class="n">queue</span><span class="o">.</span><span class="n">Queue</span><span class="p">()</span>

<span class="c1"># Start a pool of 5 workers</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
    <span class="n">t</span> <span class="o">=</span> <span class="n">threading</span><span class="o">.</span><span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">worker</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s1">&#39;worker </span><span class="si">%i</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">))</span>
    <span class="n">t</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

<span class="c1"># Begin adding work to the queue</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">50</span><span class="p">):</span>
    <span class="n">q</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="n">i</span><span class="p">)</span>

<span class="c1"># Give threads time to run</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Main thread sleeping&#39;</span><span class="p">)</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
</pre></div>
</div>
<p>When run, this will produce the following output:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Running worker
Running worker
Running worker
Running worker
Running worker
Main thread sleeping
Worker &lt;Thread(worker 1, started 130283832797456)&gt; running with argument 0
Worker &lt;Thread(worker 2, started 130283824404752)&gt; running with argument 1
Worker &lt;Thread(worker 3, started 130283816012048)&gt; running with argument 2
Worker &lt;Thread(worker 4, started 130283807619344)&gt; running with argument 3
Worker &lt;Thread(worker 5, started 130283799226640)&gt; running with argument 4
Worker &lt;Thread(worker 1, started 130283832797456)&gt; running with argument 5
...
</pre></div>
</div>
<p>Consult the module’s documentation for more details; the <a class="reference internal" href="../library/queue.html#queue.Queue" title="queue.Queue"><code class="xref py py-class docutils literal notranslate"><span class="pre">Queue</span></code></a>
class provides a featureful interface.</p>
</section>
<section id="what-kinds-of-global-value-mutation-are-thread-safe">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">What kinds of global value mutation are thread-safe?</a><a class="headerlink" href="#what-kinds-of-global-value-mutation-are-thread-safe" title="Link to this heading">¶</a></h3>
<p>A <a class="reference internal" href="../glossary.html#term-global-interpreter-lock"><span class="xref std std-term">global interpreter lock</span></a> (GIL) is used internally to ensure that only one
thread runs in the Python VM at a time.  In general, Python offers to switch
among threads only between bytecode instructions; how frequently it switches can
be set via <a class="reference internal" href="../library/sys.html#sys.setswitchinterval" title="sys.setswitchinterval"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.setswitchinterval()</span></code></a>.  Each bytecode instruction and
therefore all the C implementation code reached from each instruction is
therefore atomic from the point of view of a Python program.</p>
<p>In theory, this means an exact accounting requires an exact understanding of the
PVM bytecode implementation.  In practice, it means that operations on shared
variables of built-in data types (ints, lists, dicts, etc) that “look atomic”
really are.</p>
<p>For example, the following operations are all atomic (L, L1, L2 are lists, D,
D1, D2 are dicts, x, y are objects, i, j are ints):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">L</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="n">L1</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">L2</span><span class="p">)</span>
<span class="n">x</span> <span class="o">=</span> <span class="n">L</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
<span class="n">x</span> <span class="o">=</span> <span class="n">L</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
<span class="n">L1</span><span class="p">[</span><span class="n">i</span><span class="p">:</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="n">L2</span>
<span class="n">L</span><span class="o">.</span><span class="n">sort</span><span class="p">()</span>
<span class="n">x</span> <span class="o">=</span> <span class="n">y</span>
<span class="n">x</span><span class="o">.</span><span class="n">field</span> <span class="o">=</span> <span class="n">y</span>
<span class="n">D</span><span class="p">[</span><span class="n">x</span><span class="p">]</span> <span class="o">=</span> <span class="n">y</span>
<span class="n">D1</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">D2</span><span class="p">)</span>
<span class="n">D</span><span class="o">.</span><span class="n">keys</span><span class="p">()</span>
</pre></div>
</div>
<p>These aren’t:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">i</span> <span class="o">=</span> <span class="n">i</span><span class="o">+</span><span class="mi">1</span>
<span class="n">L</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">L</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">])</span>
<span class="n">L</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">L</span><span class="p">[</span><span class="n">j</span><span class="p">]</span>
<span class="n">D</span><span class="p">[</span><span class="n">x</span><span class="p">]</span> <span class="o">=</span> <span class="n">D</span><span class="p">[</span><span class="n">x</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>
</pre></div>
</div>
<p>Operations that replace other objects may invoke those other objects’
<a class="reference internal" href="../reference/datamodel.html#object.__del__" title="object.__del__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code></a> method when their reference count reaches zero, and that can
affect things.  This is especially true for the mass updates to dictionaries and
lists.  When in doubt, use a mutex!</p>
</section>
<section id="can-t-we-get-rid-of-the-global-interpreter-lock">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">Can’t we get rid of the Global Interpreter Lock?</a><a class="headerlink" href="#can-t-we-get-rid-of-the-global-interpreter-lock" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../glossary.html#term-global-interpreter-lock"><span class="xref std std-term">global interpreter lock</span></a> (GIL) is often seen as a hindrance to Python’s
deployment on high-end multiprocessor server machines, because a multi-threaded
Python program effectively only uses one CPU, due to the insistence that
(almost) all Python code can only run while the GIL is held.</p>
<p>Back in the days of Python 1.5, Greg Stein actually implemented a comprehensive
patch set (the “free threading” patches) that removed the GIL and replaced it
with fine-grained locking.  Adam Olsen recently did a similar experiment
in his <a class="reference external" href="https://code.google.com/archive/p/python-safethread">python-safethread</a>
project.  Unfortunately, both experiments exhibited a sharp drop in single-thread
performance (at least 30% slower), due to the amount of fine-grained locking
necessary to compensate for the removal of the GIL.</p>
<p>This doesn’t mean that you can’t make good use of Python on multi-CPU machines!
You just have to be creative with dividing the work up between multiple
<em>processes</em> rather than multiple <em>threads</em>.  The
<a class="reference internal" href="../library/concurrent.futures.html#concurrent.futures.ProcessPoolExecutor" title="concurrent.futures.ProcessPoolExecutor"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProcessPoolExecutor</span></code></a> class in the new
<a class="reference internal" href="../library/concurrent.futures.html#module-concurrent.futures" title="concurrent.futures: Execute computations concurrently using threads or processes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent.futures</span></code></a> module provides an easy way of doing so; the
<a class="reference internal" href="../library/multiprocessing.html#module-multiprocessing" title="multiprocessing: Process-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code></a> module provides a lower-level API in case you want
more control over dispatching of tasks.</p>
<p>Judicious use of C extensions will also help; if you use a C extension to
perform a time-consuming task, the extension can release the GIL while the
thread of execution is in the C code and allow other threads to get some work
done.  Some standard library modules such as <a class="reference internal" href="../library/zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> and <a class="reference internal" href="../library/hashlib.html#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a>
already do this.</p>
<p>It has been suggested that the GIL should be a per-interpreter-state lock rather
than truly global; interpreters then wouldn’t be able to share objects.
Unfortunately, this isn’t likely to happen either.  It would be a tremendous
amount of work, because many object implementations currently have global state.
For example, small integers and short strings are cached; these caches would
have to be moved to the interpreter state.  Other object types have their own
free list; these free lists would have to be moved to the interpreter state.
And so on.</p>
<p>And I doubt that it can even be done in finite time, because the same problem
exists for 3rd party extensions.  It is likely that 3rd party extensions are
being written at a faster rate than you can convert them to store all their
global state in the interpreter state.</p>
<p>And finally, once you have multiple interpreters not sharing any state, what
have you gained over running each interpreter in a separate process?</p>
</section>
</section>
<section id="input-and-output">
<h2><a class="toc-backref" href="#id19" role="doc-backlink">Input and Output</a><a class="headerlink" href="#input-and-output" title="Link to this heading">¶</a></h2>
<section id="how-do-i-delete-a-file-and-other-file-questions">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">How do I delete a file? (And other file questions…)</a><a class="headerlink" href="#how-do-i-delete-a-file-and-other-file-questions" title="Link to this heading">¶</a></h3>
<p>Use <code class="docutils literal notranslate"><span class="pre">os.remove(filename)</span></code> or <code class="docutils literal notranslate"><span class="pre">os.unlink(filename)</span></code>; for documentation, see
the <a class="reference internal" href="../library/os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module.  The two functions are identical; <a class="reference internal" href="../library/os.html#os.unlink" title="os.unlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">unlink()</span></code></a> is simply
the name of the Unix system call for this function.</p>
<p>To remove a directory, use <a class="reference internal" href="../library/os.html#os.rmdir" title="os.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rmdir()</span></code></a>; use <a class="reference internal" href="../library/os.html#os.mkdir" title="os.mkdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.mkdir()</span></code></a> to create one.
<code class="docutils literal notranslate"><span class="pre">os.makedirs(path)</span></code> will create any intermediate directories in <code class="docutils literal notranslate"><span class="pre">path</span></code> that
don’t exist. <code class="docutils literal notranslate"><span class="pre">os.removedirs(path)</span></code> will remove intermediate directories as
long as they’re empty; if you want to delete an entire directory tree and its
contents, use <a class="reference internal" href="../library/shutil.html#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.rmtree()</span></code></a>.</p>
<p>To rename a file, use <code class="docutils literal notranslate"><span class="pre">os.rename(old_path,</span> <span class="pre">new_path)</span></code>.</p>
<p>To truncate a file, open it using <code class="docutils literal notranslate"><span class="pre">f</span> <span class="pre">=</span> <span class="pre">open(filename,</span> <span class="pre">&quot;rb+&quot;)</span></code>, and use
<code class="docutils literal notranslate"><span class="pre">f.truncate(offset)</span></code>; offset defaults to the current seek position.  There’s
also <code class="docutils literal notranslate"><span class="pre">os.ftruncate(fd,</span> <span class="pre">offset)</span></code> for files opened with <a class="reference internal" href="../library/os.html#os.open" title="os.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.open()</span></code></a>, where
<em>fd</em> is the file descriptor (a small integer).</p>
<p>The <a class="reference internal" href="../library/shutil.html#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> module also contains a number of functions to work on files
including <a class="reference internal" href="../library/shutil.html#shutil.copyfile" title="shutil.copyfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">copyfile()</span></code></a>, <a class="reference internal" href="../library/shutil.html#shutil.copytree" title="shutil.copytree"><code class="xref py py-func docutils literal notranslate"><span class="pre">copytree()</span></code></a>, and
<a class="reference internal" href="../library/shutil.html#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmtree()</span></code></a>.</p>
</section>
<section id="how-do-i-copy-a-file">
<h3><a class="toc-backref" href="#id21" role="doc-backlink">How do I copy a file?</a><a class="headerlink" href="#how-do-i-copy-a-file" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/shutil.html#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> module contains a <a class="reference internal" href="../library/shutil.html#shutil.copyfile" title="shutil.copyfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">copyfile()</span></code></a> function.
Note that on Windows NTFS volumes, it does not copy
<a class="reference external" href="https://en.wikipedia.org/wiki/NTFS#Alternate_data_stream_(ADS)">alternate data streams</a>
nor <a class="reference external" href="https://en.wikipedia.org/wiki/Resource_fork">resource forks</a>
on macOS HFS+ volumes, though both are now rarely used.
It also doesn’t copy file permissions and metadata, though using
<a class="reference internal" href="../library/shutil.html#shutil.copy2" title="shutil.copy2"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.copy2()</span></code></a> instead will preserve most (though not all) of it.</p>
</section>
<section id="how-do-i-read-or-write-binary-data">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">How do I read (or write) binary data?</a><a class="headerlink" href="#how-do-i-read-or-write-binary-data" title="Link to this heading">¶</a></h3>
<p>To read or write complex binary data formats, it’s best to use the <a class="reference internal" href="../library/struct.html#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a>
module.  It allows you to take a string containing binary data (usually numbers)
and convert it to Python objects; and vice versa.</p>
<p>For example, the following code reads two 2-byte integers and one 4-byte integer
in big-endian format from a file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">struct</span>

<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">s</span> <span class="o">=</span> <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">8</span><span class="p">)</span>
    <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">z</span> <span class="o">=</span> <span class="n">struct</span><span class="o">.</span><span class="n">unpack</span><span class="p">(</span><span class="s2">&quot;&gt;hhl&quot;</span><span class="p">,</span> <span class="n">s</span><span class="p">)</span>
</pre></div>
</div>
<p>The ‘&gt;’ in the format string forces big-endian data; the letter ‘h’ reads one
“short integer” (2 bytes), and ‘l’ reads one “long integer” (4 bytes) from the
string.</p>
<p>For data that is more regular (e.g. a homogeneous list of ints or floats),
you can also use the <a class="reference internal" href="../library/array.html#module-array" title="array: Space efficient arrays of uniformly typed numeric values."><code class="xref py py-mod docutils literal notranslate"><span class="pre">array</span></code></a> module.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>To read and write binary data, it is mandatory to open the file in
binary mode (here, passing <code class="docutils literal notranslate"><span class="pre">&quot;rb&quot;</span></code> to <a class="reference internal" href="../library/functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>).  If you use
<code class="docutils literal notranslate"><span class="pre">&quot;r&quot;</span></code> instead (the default), the file will be open in text mode
and <code class="docutils literal notranslate"><span class="pre">f.read()</span></code> will return <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> objects rather than
<a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects.</p>
</div>
</section>
<section id="i-can-t-seem-to-use-os-read-on-a-pipe-created-with-os-popen-why">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">I can’t seem to use os.read() on a pipe created with os.popen(); why?</a><a class="headerlink" href="#i-can-t-seem-to-use-os-read-on-a-pipe-created-with-os-popen-why" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="../library/os.html#os.read" title="os.read"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.read()</span></code></a> is a low-level function which takes a file descriptor, a small
integer representing the opened file.  <a class="reference internal" href="../library/os.html#os.popen" title="os.popen"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen()</span></code></a> creates a high-level
file object, the same type returned by the built-in <a class="reference internal" href="../library/functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function.
Thus, to read <em>n</em> bytes from a pipe <em>p</em> created with <a class="reference internal" href="../library/os.html#os.popen" title="os.popen"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen()</span></code></a>, you need to
use <code class="docutils literal notranslate"><span class="pre">p.read(n)</span></code>.</p>
</section>
<section id="how-do-i-access-the-serial-rs232-port">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">How do I access the serial (RS232) port?</a><a class="headerlink" href="#how-do-i-access-the-serial-rs232-port" title="Link to this heading">¶</a></h3>
<p>For Win32, OSX, Linux, BSD, Jython, IronPython:</p>
<blockquote>
<div><p><a class="reference external" href="https://pypi.org/project/pyserial/">https://pypi.org/project/pyserial/</a></p>
</div></blockquote>
<p>For Unix, see a Usenet post by Mitch Chapman:</p>
<blockquote>
<div><p><a class="reference external" href="https://groups.google.com/groups?selm=34A04430.CF9&#64;ohioee.com">https://groups.google.com/groups?selm=34A04430.CF9&#64;ohioee.com</a></p>
</div></blockquote>
</section>
<section id="why-doesn-t-closing-sys-stdout-stdin-stderr-really-close-it">
<h3><a class="toc-backref" href="#id25" role="doc-backlink">Why doesn’t closing sys.stdout (stdin, stderr) really close it?</a><a class="headerlink" href="#why-doesn-t-closing-sys-stdout-stdin-stderr-really-close-it" title="Link to this heading">¶</a></h3>
<p>Python <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file objects</span></a> are a high-level layer of
abstraction on low-level C file descriptors.</p>
<p>For most file objects you create in Python via the built-in <a class="reference internal" href="../library/functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>
function, <code class="docutils literal notranslate"><span class="pre">f.close()</span></code> marks the Python file object as being closed from
Python’s point of view, and also arranges to close the underlying C file
descriptor.  This also happens automatically in <code class="docutils literal notranslate"><span class="pre">f</span></code>’s destructor, when
<code class="docutils literal notranslate"><span class="pre">f</span></code> becomes garbage.</p>
<p>But stdin, stdout and stderr are treated specially by Python, because of the
special status also given to them by C.  Running <code class="docutils literal notranslate"><span class="pre">sys.stdout.close()</span></code> marks
the Python-level file object as being closed, but does <em>not</em> close the
associated C file descriptor.</p>
<p>To close the underlying C file descriptor for one of these three, you should
first be sure that’s what you really want to do (e.g., you may confuse
extension modules trying to do I/O).  If it is, use <a class="reference internal" href="../library/os.html#os.close" title="os.close"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.close()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">os</span><span class="o">.</span><span class="n">close</span><span class="p">(</span><span class="n">stdin</span><span class="o">.</span><span class="n">fileno</span><span class="p">())</span>
<span class="n">os</span><span class="o">.</span><span class="n">close</span><span class="p">(</span><span class="n">stdout</span><span class="o">.</span><span class="n">fileno</span><span class="p">())</span>
<span class="n">os</span><span class="o">.</span><span class="n">close</span><span class="p">(</span><span class="n">stderr</span><span class="o">.</span><span class="n">fileno</span><span class="p">())</span>
</pre></div>
</div>
<p>Or you can use the numeric constants 0, 1 and 2, respectively.</p>
</section>
</section>
<section id="network-internet-programming">
<h2><a class="toc-backref" href="#id26" role="doc-backlink">Network/Internet Programming</a><a class="headerlink" href="#network-internet-programming" title="Link to this heading">¶</a></h2>
<section id="what-www-tools-are-there-for-python">
<h3><a class="toc-backref" href="#id27" role="doc-backlink">What WWW tools are there for Python?</a><a class="headerlink" href="#what-www-tools-are-there-for-python" title="Link to this heading">¶</a></h3>
<p>See the chapters titled <a class="reference internal" href="../library/internet.html#internet"><span class="std std-ref">Internet Protocols and Support</span></a> and <a class="reference internal" href="../library/netdata.html#netdata"><span class="std std-ref">Internet Data Handling</span></a> in the Library
Reference Manual.  Python has many modules that will help you build server-side
and client-side web systems.</p>
<p>A summary of available frameworks is maintained by Paul Boddie at
<a class="reference external" href="https://wiki.python.org/moin/WebProgramming">https://wiki.python.org/moin/WebProgramming</a>.</p>
<p>Cameron Laird maintains a useful set of pages about Python web technologies at
<a class="reference external" href="https://web.archive.org/web/20210224183619/http://phaseit.net/claird/comp.lang.python/web_python">https://web.archive.org/web/20210224183619/http://phaseit.net/claird/comp.lang.python/web_python</a>.</p>
</section>
<section id="how-can-i-mimic-cgi-form-submission-method-post">
<h3><a class="toc-backref" href="#id28" role="doc-backlink">How can I mimic CGI form submission (METHOD=POST)?</a><a class="headerlink" href="#how-can-i-mimic-cgi-form-submission-method-post" title="Link to this heading">¶</a></h3>
<p>I would like to retrieve web pages that are the result of POSTing a form. Is
there existing code that would let me do this easily?</p>
<p>Yes. Here’s a simple example that uses <a class="reference internal" href="../library/urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="ch">#!/usr/local/bin/python</span>

<span class="kn">import</span> <span class="nn">urllib.request</span>

<span class="c1"># build the query string</span>
<span class="n">qs</span> <span class="o">=</span> <span class="s2">&quot;First=Josephine&amp;MI=Q&amp;Last=Public&quot;</span>

<span class="c1"># connect and send the server a path</span>
<span class="n">req</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;http://www.some-server.out-there&#39;</span>
                             <span class="s1">&#39;/cgi-bin/some-cgi-script&#39;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="n">qs</span><span class="p">)</span>
<span class="k">with</span> <span class="n">req</span><span class="p">:</span>
    <span class="n">msg</span><span class="p">,</span> <span class="n">hdrs</span> <span class="o">=</span> <span class="n">req</span><span class="o">.</span><span class="n">read</span><span class="p">(),</span> <span class="n">req</span><span class="o">.</span><span class="n">info</span><span class="p">()</span>
</pre></div>
</div>
<p>Note that in general for percent-encoded POST operations, query strings must be
quoted using <a class="reference internal" href="../library/urllib.parse.html#urllib.parse.urlencode" title="urllib.parse.urlencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.parse.urlencode()</span></code></a>.  For example, to send
<code class="docutils literal notranslate"><span class="pre">name=Guy</span> <span class="pre">Steele,</span> <span class="pre">Jr.</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.parse</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlencode</span><span class="p">({</span><span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;Guy Steele, Jr.&#39;</span><span class="p">})</span>
<span class="go">&#39;name=Guy+Steele%2C+Jr.&#39;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../howto/urllib2.html#urllib-howto"><span class="std std-ref">HOWTO Fetch Internet Resources Using The urllib Package</span></a> for extensive examples.</p>
</div>
</section>
<section id="what-module-should-i-use-to-help-with-generating-html">
<h3><a class="toc-backref" href="#id29" role="doc-backlink">What module should I use to help with generating HTML?</a><a class="headerlink" href="#what-module-should-i-use-to-help-with-generating-html" title="Link to this heading">¶</a></h3>
<p>You can find a collection of useful links on the <a class="reference external" href="https://wiki.python.org/moin/WebProgramming">Web Programming wiki page</a>.</p>
</section>
<section id="how-do-i-send-mail-from-a-python-script">
<h3><a class="toc-backref" href="#id30" role="doc-backlink">How do I send mail from a Python script?</a><a class="headerlink" href="#how-do-i-send-mail-from-a-python-script" title="Link to this heading">¶</a></h3>
<p>Use the standard library module <a class="reference internal" href="../library/smtplib.html#module-smtplib" title="smtplib: SMTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code></a>.</p>
<p>Here’s a very simple interactive mail sender that uses it.  This method will
work on any host that supports an SMTP listener.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span><span class="o">,</span> <span class="nn">smtplib</span>

<span class="n">fromaddr</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;From: &quot;</span><span class="p">)</span>
<span class="n">toaddrs</span>  <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;To: &quot;</span><span class="p">)</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="s1">&#39;,&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Enter message, end with ^D:&quot;</span><span class="p">)</span>
<span class="n">msg</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span>
<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">line</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">readline</span><span class="p">()</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">line</span><span class="p">:</span>
        <span class="k">break</span>
    <span class="n">msg</span> <span class="o">+=</span> <span class="n">line</span>

<span class="c1"># The actual mail send</span>
<span class="n">server</span> <span class="o">=</span> <span class="n">smtplib</span><span class="o">.</span><span class="n">SMTP</span><span class="p">(</span><span class="s1">&#39;localhost&#39;</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">sendmail</span><span class="p">(</span><span class="n">fromaddr</span><span class="p">,</span> <span class="n">toaddrs</span><span class="p">,</span> <span class="n">msg</span><span class="p">)</span>
<span class="n">server</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>
</pre></div>
</div>
<p>A Unix-only alternative uses sendmail.  The location of the sendmail program
varies between systems; sometimes it is <code class="docutils literal notranslate"><span class="pre">/usr/lib/sendmail</span></code>, sometimes
<code class="docutils literal notranslate"><span class="pre">/usr/sbin/sendmail</span></code>.  The sendmail manual page will help you out.  Here’s
some sample code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span>

<span class="n">SENDMAIL</span> <span class="o">=</span> <span class="s2">&quot;/usr/sbin/sendmail&quot;</span>  <span class="c1"># sendmail location</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">popen</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> -t -i&quot;</span> <span class="o">%</span> <span class="n">SENDMAIL</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span>
<span class="n">p</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;To: <EMAIL></span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">p</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Subject: test</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">p</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>  <span class="c1"># blank line separating headers from body</span>
<span class="n">p</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Some text</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">p</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;some more text</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="n">sts</span> <span class="o">=</span> <span class="n">p</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="k">if</span> <span class="n">sts</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Sendmail exit status&quot;</span><span class="p">,</span> <span class="n">sts</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="how-do-i-avoid-blocking-in-the-connect-method-of-a-socket">
<h3><a class="toc-backref" href="#id31" role="doc-backlink">How do I avoid blocking in the connect() method of a socket?</a><a class="headerlink" href="#how-do-i-avoid-blocking-in-the-connect-method-of-a-socket" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/select.html#module-select" title="select: Wait for I/O completion on multiple streams."><code class="xref py py-mod docutils literal notranslate"><span class="pre">select</span></code></a> module is commonly used to help with asynchronous I/O on
sockets.</p>
<p>To prevent the TCP connect from blocking, you can set the socket to non-blocking
mode.  Then when you do the <a class="reference internal" href="../library/socket.html#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a>,
you will either connect immediately
(unlikely) or get an exception that contains the error number as <code class="docutils literal notranslate"><span class="pre">.errno</span></code>.
<code class="docutils literal notranslate"><span class="pre">errno.EINPROGRESS</span></code> indicates that the connection is in progress, but hasn’t
finished yet.  Different OSes will return different values, so you’re going to
have to check what’s returned on your system.</p>
<p>You can use the <a class="reference internal" href="../library/socket.html#socket.socket.connect_ex" title="socket.socket.connect_ex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect_ex()</span></code></a> method
to avoid creating an exception.
It will just return the errno value.
To poll, you can call <a class="reference internal" href="../library/socket.html#socket.socket.connect_ex" title="socket.socket.connect_ex"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect_ex()</span></code></a> again later
– <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">errno.EISCONN</span></code> indicate that you’re connected – or you can pass this
socket to <a class="reference internal" href="../library/select.html#select.select" title="select.select"><code class="xref py py-meth docutils literal notranslate"><span class="pre">select.select()</span></code></a> to check if it’s writable.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="../library/asyncio.html#module-asyncio" title="asyncio: Asynchronous I/O."><code class="xref py py-mod docutils literal notranslate"><span class="pre">asyncio</span></code></a> module provides a general purpose single-threaded and
concurrent asynchronous library, which can be used for writing non-blocking
network code.
The third-party <a class="reference external" href="https://twisted.org/">Twisted</a> library is
a popular and feature-rich alternative.</p>
</div>
</section>
</section>
<section id="databases">
<h2><a class="toc-backref" href="#id32" role="doc-backlink">Databases</a><a class="headerlink" href="#databases" title="Link to this heading">¶</a></h2>
<section id="are-there-any-interfaces-to-database-packages-in-python">
<h3><a class="toc-backref" href="#id33" role="doc-backlink">Are there any interfaces to database packages in Python?</a><a class="headerlink" href="#are-there-any-interfaces-to-database-packages-in-python" title="Link to this heading">¶</a></h3>
<p>Yes.</p>
<p>Interfaces to disk-based hashes such as <a class="reference internal" href="../library/dbm.html#module-dbm.ndbm" title="dbm.ndbm: The New Database Manager (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">DBM</span></code></a> and <a class="reference internal" href="../library/dbm.html#module-dbm.gnu" title="dbm.gnu: GNU database manager (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">GDBM</span></code></a> are also included with standard Python.  There is also the
<a class="reference internal" href="../library/sqlite3.html#module-sqlite3" title="sqlite3: A DB-API 2.0 implementation using SQLite 3.x."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code></a> module, which provides a lightweight disk-based relational
database.</p>
<p>Support for most relational databases is available.  See the
<a class="reference external" href="https://wiki.python.org/moin/DatabaseProgramming">DatabaseProgramming wiki page</a> for details.</p>
</section>
<section id="how-do-you-implement-persistent-objects-in-python">
<h3><a class="toc-backref" href="#id34" role="doc-backlink">How do you implement persistent objects in Python?</a><a class="headerlink" href="#how-do-you-implement-persistent-objects-in-python" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> library module solves this in a very general way (though you
still can’t store things like open files, sockets or windows), and the
<a class="reference internal" href="../library/shelve.html#module-shelve" title="shelve: Python object persistence."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shelve</span></code></a> library module uses pickle and (g)dbm to create persistent
mappings containing arbitrary Python objects.</p>
</section>
</section>
<section id="mathematics-and-numerics">
<h2><a class="toc-backref" href="#id35" role="doc-backlink">Mathematics and Numerics</a><a class="headerlink" href="#mathematics-and-numerics" title="Link to this heading">¶</a></h2>
<section id="how-do-i-generate-random-numbers-in-python">
<h3><a class="toc-backref" href="#id36" role="doc-backlink">How do I generate random numbers in Python?</a><a class="headerlink" href="#how-do-i-generate-random-numbers-in-python" title="Link to this heading">¶</a></h3>
<p>The standard module <a class="reference internal" href="../library/random.html#module-random" title="random: Generate pseudo-random numbers with various common distributions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code></a> implements a random number generator.  Usage
is simple:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">random</span>
<span class="n">random</span><span class="o">.</span><span class="n">random</span><span class="p">()</span>
</pre></div>
</div>
<p>This returns a random floating point number in the range [0, 1).</p>
<p>There are also many other specialized generators in this module, such as:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">randrange(a,</span> <span class="pre">b)</span></code> chooses an integer in the range [a, b).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">uniform(a,</span> <span class="pre">b)</span></code> chooses a floating point number in the range [a, b).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">normalvariate(mean,</span> <span class="pre">sdev)</span></code> samples the normal (Gaussian) distribution.</p></li>
</ul>
<p>Some higher-level functions operate on sequences directly, such as:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">choice(S)</span></code> chooses a random element from a given sequence.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">shuffle(L)</span></code> shuffles a list in-place, i.e. permutes it randomly.</p></li>
</ul>
<p>There’s also a <code class="docutils literal notranslate"><span class="pre">Random</span></code> class you can instantiate to create independent
multiple random number generators.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Library and Extension FAQ</a><ul>
<li><a class="reference internal" href="#general-library-questions">General Library Questions</a></li>
<li><a class="reference internal" href="#common-tasks">Common tasks</a></li>
<li><a class="reference internal" href="#threads">Threads</a></li>
<li><a class="reference internal" href="#input-and-output">Input and Output</a></li>
<li><a class="reference internal" href="#network-internet-programming">Network/Internet Programming</a></li>
<li><a class="reference internal" href="#databases">Databases</a></li>
<li><a class="reference internal" href="#mathematics-and-numerics">Mathematics and Numerics</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="design.html"
                          title="previous chapter">Design and History FAQ</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="extending.html"
                          title="next chapter">Extending/Embedding FAQ</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/faq/library.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="extending.html" title="Extending/Embedding FAQ"
             >next</a> |</li>
        <li class="right" >
          <a href="design.html" title="Design and History FAQ"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python Frequently Asked Questions</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Library and Extension FAQ</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>