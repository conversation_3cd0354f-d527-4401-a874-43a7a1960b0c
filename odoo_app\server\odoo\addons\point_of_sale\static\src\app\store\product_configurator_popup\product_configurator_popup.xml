<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.RadioProductAttribute">
        <div class="configurator_radio" t-ref="root">
            <div class="d-flex flex-wrap gap-3">
                <t t-foreach="values" t-as="value" t-key="value.id">
                    <div class="attribute-name-cell form-check">
                        <input class="form-check-input radio-check" type="radio" t-model="state.attribute_value_ids" t-att-name="attribute.id"
                                t-attf-id="{{ attribute.id }}_{{ value.id }}" t-att-value="value.id"/>
                        <span t-esc="value.name"/>
                        <div t-if="value.price_extra" class="price-extra-cell d-inline-block ms-2">
                            <span class="price_extra px-2 py-1 rounded-pill text-bg-info">
                                <t t-esc="getFormatPriceExtra(value.price_extra)"/>
                            </span>
                        </div>
                    </div>
                    <div t-if="value.id == state.attribute_value_ids and value.is_custom" class="custom-value-cell">
                        <input class="custom_value form-control form-control-lg mt-2" type="text" t-model="state.custom_value"/>
                    </div>
                </t>
            </div>
        </div>
    </t>

    <t t-name="point_of_sale.PillsProductAttribute">
        <div class="configurator_radio" t-ref="root">
            <div class="d-flex flex-wrap gap-2">
                <t t-foreach="values" t-as="value" t-key="value.id">
                    <div class="attribute-name-cell">
                        <input class="form-check-input d-none" type="radio" t-model="state.attribute_value_ids" t-att-name="attribute.id"
                            t-attf-id="{{ attribute.id }}_{{ value.id }}" t-att-value="value.id"/>
                        <label
                            t-attf-class="btn d-flex {{ value.id == state.attribute_value_ids ? 'btn-primary' : 'btn-secondary' }}"
                            t-att-name="value.name"
                            t-attf-for="{{ attribute.id }}_{{ value.id }}">
                            <span t-esc="value.name"/>
                            <div t-if="value.price_extra" class="price-extra-cell d-inline-block ms-2">
                                <span class="price_extra px-2 py-1 rounded-pill text-bg-info">
                                    <t t-esc="getFormatPriceExtra(value.price_extra)"/>
                                </span>
                            </div>
                        </label>
                    </div>
                    <div t-if="value.id == state.attribute_value_ids and value.is_custom" class="custom-value-cell">
                        <input class="custom_value form-control form-control-lg mt-2" type="text" t-model="state.custom_value"/>
                    </div>
                </t>
            </div>
        </div>
    </t>

    <t t-name="point_of_sale.SelectProductAttribute">
        <div>
            <t t-set="is_custom" t-value="false"/>

            <select class="configurator_select form-select form-select-md" t-model="state.attribute_value_ids">
                <option t-foreach="values" t-as="value" t-key="value.id" t-att-value="value.id">
                    <t t-set="is_custom" t-value="is_custom || (value.is_custom and value.id == state.attribute_value_ids)"/>
                    <t t-esc="value.name"/>
                    <t t-if="value.price_extra">
                        <t t-esc="getFormatPriceExtra(value.price_extra)"/>
                    </t>
                </option>
            </select>

            <input class="custom_value form-control form-control-lg mt-2" t-if="is_custom" type="text" t-model="state.custom_value"/>
        </div>
    </t>

    <t t-name="point_of_sale.ColorProductAttribute">
        <div>
            <t t-set="is_custom" t-value="false"/>

            <ul class="color_attribute_list d-flex gap-3">
                <li t-foreach="values" t-as="value" t-key="value.id" class="color_attribute_list_item">
                    <t t-set="is_custom" t-value="is_custom || (value.is_custom and value.id == state.attribute_value_ids)"/>
                    <t t-set="img_style" t-value="value.image ? 'background:url(/web/image/product.template.attribute.value/' + value.id + '/image); background-size:cover;' : ''"/>
                    <t t-set="color_style" t-value="value.is_custom ? '' : 'background-color: ' + value.html_color" />
                    <span class="d-flex flex-row justify-content-center align-items-center">
                        <label t-attf-class="configurator_color rounded-circle border {{ value.id == state.attribute_value_ids ? 'active border-3 border-primary' : 'border-3 border-secondary' }}"
                            t-attf-style="{{ img_style or color_style }}" t-att-data-color="value.name">
                            <input class="m-2 opacity-0" type="radio" t-model="state.attribute_value_ids" t-att-value="value.id" t-att-name="attribute.id"/>
                        </label>
                        <div t-if="value.price_extra" class="price-extra-cell d-inline-block ms-2">
                            <span class="price_extra px-2 py-1 rounded-pill text-bg-info">
                                <t t-esc="getFormatPriceExtra(value.price_extra)"/>
                            </span>
                        </div>
                    </span>
                </li>
            </ul>

            <input class="custom_value form-control form-control-lg mt-2" t-if="is_custom" type="text" t-model="state.custom_value"/>
        </div>
    </t>

    <t t-name="point_of_sale.MultiProductAttribute">
        <div class="d-flex gap-2 flex-wrap">
            <div t-foreach="values" t-as="value" t-key="value.id">
                <input
                    class="form-check-input d-none"
                    type="checkbox"
                    t-attf-id="multi-{{value.id}}"
                    t-attf-name="multi-{{value.id}}"
                    t-model="state.attribute_value_ids[value.id]"/>
                <label
                    t-attf-class="form-check-label btn d-flex {{ state.attribute_value_ids[value.id] === true ? 'btn-primary' : 'btn-secondary' }}"
                    t-attf-name="multi-{{value.id}}"
                    t-attf-for="multi-{{value.id}}">
                    <span t-esc="value.name"/>
                    <div t-if="value.price_extra" class="price-extra-cell d-inline-block ms-2">
                        <span class="price_extra px-2 py-1 rounded-pill text-bg-info">
                            <t t-esc="getFormatPriceExtra(value.price_extra)"/>
                        </span>
                    </div>
                </label>
            </div>
        </div>
    </t>

    <t t-name="point_of_sale.ProductConfiguratorPopup">
        <t t-if="!ui.isSmall">
            <div class="popup popup-text popup-med product-configurator-popup">
                <div class="modal-header drag-handle">
                    <h4 class="modal-title"><t t-esc="props.product.display_name" /></h4>
                </div>

                <main class="body modal-body product_configurator_attributes m-0 text-start">
                    <div t-foreach="props.attributes" t-as="attribute" t-key="attribute.id" class="attribute mb-4">
                        <div class="attribute_name mb-2 fw-bolder d-flex w-100 align-items-center gap-2">
                            <span t-esc="attribute.name" />
                            <hr class="hr flex-grow-1" />
                        </div>
                        <RadioProductAttribute t-if="attribute.display_type === 'radio'" attribute="attribute"/>
                        <PillsProductAttribute t-elif="attribute.display_type === 'pills'" attribute="attribute"/>
                        <SelectProductAttribute t-elif="attribute.display_type === 'select'" attribute="attribute"/>
                        <ColorProductAttribute t-elif="attribute.display_type === 'color'" attribute="attribute"/>
                        <MultiProductAttribute t-elif="attribute.display_type === 'multi'" attribute="attribute"/>
                    </div>
                </main>

                <footer class="footer footer-flex modal-footer">
                    <div class="button highlight confirm btn btn-lg btn-primary" t-on-click="confirm">
                        Add
                    </div>
                    <div class="button cancel btn btn-lg btn-secondary" t-on-click="cancel">
                        Cancel
                    </div>
                </footer>
            </div>
        </t>
        <t t-else="">
            <div class="popup mobile popup-text popup-med product-configurator-popup">
                <div class="modal-header">
                    <h4 class="modal-title"><t t-esc="props.product.display_name" /></h4>
                    <span><t t-esc="unitPrice" ></t></span>
                </div>
                <main class="body modal-body mobile product_configurator_attributes d-flex flex-column text-start">
                    <div class="product-img d-flex justify-content-center h-50 p-2" >
                        <img class="h-100" t-att-src="imageUrl" t-att-alt="props.product.display_name" />
                    </div>
                    <div t-foreach="props.attributes" t-as="attribute" t-key="attribute.id" class="attribute mb-3">
                        <div class="attribute_name mb-2 fw-bolder" t-esc="attribute.name"/>
                        <RadioProductAttribute t-if="attribute.display_type === 'radio' or attribute.display_type === 'pills'" attribute="attribute"/>
                        <SelectProductAttribute t-elif="attribute.display_type === 'select'" attribute="attribute"/>
                        <ColorProductAttribute t-elif="attribute.display_type === 'color'" attribute="attribute"/>
                        <MultiProductAttribute t-elif="attribute.display_type === 'multi'" attribute="attribute"/>
                    </div>
                </main>

                <footer class="footer footer-flex mobile modal-footer">
                    <div class="quantity-selector d-flex align-items-center justify-content-center w-100 py-2 fw-bolder">
                        <button class="btn btn-lg btn-secondary" t-on-click="removeOneQuantity">
                            <i class="fa fa-minus" aria-hidden="true"></i>
                        </button>
                        <span class="quantity mx-3"><t t-esc="state.quantity"></t></span>
                        <button class="btn btn-lg btn-secondary" t-on-click="addOneQuantity">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="button highlight confirm btn btn-lg btn-primary" t-on-click="confirm">
                        Add
                    </div>
                    <div class="button cancel btn btn-lg btn-secondary" t-on-click="cancel">
                        Cancel
                    </div>
                </footer>
            </div>
        </t>
    </t>
</templates>
