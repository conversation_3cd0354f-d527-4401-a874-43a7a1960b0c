<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="calendar_event_rule_my" model="ir.rule">
            <field name="name">Own events</field>
            <field ref="model_calendar_event" name="model_id"/>
            <field name="domain_force">[('partner_ids', 'in', user.partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>

        <record id="calendar_event_rule_employee" model="ir.rule">
            <field ref="model_calendar_event" name="model_id"/>
            <field name="name">All Calendar Event for employees</field>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field eval="[(4,ref('base.group_user'))]" name="groups"/>
        </record>

        <record id="calendar_attendee_rule_my" model="ir.rule">
            <field name="name">Own attendees</field>
            <field ref="model_calendar_attendee" name="model_id"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>

        <record id="calendar_event_rule_private" model="ir.rule">
            <field ref="model_calendar_event" name="model_id"/>
            <field name="name">Private events</field>
            <field name="domain_force">['|', ('privacy', '!=', 'private'), '&amp;', ('privacy', '=', 'private'), '|', ('user_id', '=', user.id), ('partner_ids', 'in', user.partner_id.id)]</field>
            <field name="perm_read" eval="False"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

    </data>
</odoo>
