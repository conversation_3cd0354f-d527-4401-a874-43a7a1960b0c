<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Numeric and Mathematical Modules" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/numeric.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The modules described in this chapter provide numeric and math-related functions and data types. The numbers module defines an abstract hierarchy of numeric types. The math and cmath modules contai..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The modules described in this chapter provide numeric and math-related functions and data types. The numbers module defines an abstract hierarchy of numeric types. The math and cmath modules contai..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Numeric and Mathematical Modules &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="numbers — Numeric abstract base classes" href="numbers.html" />
    <link rel="prev" title="graphlib — Functionality to operate with graph-like structures" href="graphlib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/numeric.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="graphlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="numbers.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/numeric.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="numbers.html" title="numbers — Numeric abstract base classes"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="graphlib.html" title="graphlib — Functionality to operate with graph-like structures"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Numeric and Mathematical Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="numeric-and-mathematical-modules">
<span id="numeric"></span><h1>Numeric and Mathematical Modules<a class="headerlink" href="#numeric-and-mathematical-modules" title="Link to this heading">¶</a></h1>
<p>The modules described in this chapter provide numeric and math-related functions
and data types. The <a class="reference internal" href="numbers.html#module-numbers" title="numbers: Numeric abstract base classes (Complex, Real, Integral, etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code></a> module defines an abstract hierarchy of
numeric types. The <a class="reference internal" href="math.html#module-math" title="math: Mathematical functions (sin() etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code></a> and <a class="reference internal" href="cmath.html#module-cmath" title="cmath: Mathematical functions for complex numbers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code></a> modules contain various
mathematical functions for floating-point and complex numbers. The <a class="reference internal" href="decimal.html#module-decimal" title="decimal: Implementation of the General Decimal Arithmetic  Specification."><code class="xref py py-mod docutils literal notranslate"><span class="pre">decimal</span></code></a>
module supports exact representations of decimal numbers, using arbitrary precision
arithmetic.</p>
<p>The following modules are documented in this chapter:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="numbers.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="numbers.html#the-numeric-tower">The numeric tower</a></li>
<li class="toctree-l2"><a class="reference internal" href="numbers.html#notes-for-type-implementors">Notes for type implementors</a><ul>
<li class="toctree-l3"><a class="reference internal" href="numbers.html#adding-more-numeric-abcs">Adding More Numeric ABCs</a></li>
<li class="toctree-l3"><a class="reference internal" href="numbers.html#implementing-the-arithmetic-operations">Implementing the arithmetic operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="math.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="math.html#number-theoretic-and-representation-functions">Number-theoretic and representation functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#power-and-logarithmic-functions">Power and logarithmic functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#trigonometric-functions">Trigonometric functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#angular-conversion">Angular conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#hyperbolic-functions">Hyperbolic functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#special-functions">Special functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="math.html#constants">Constants</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="cmath.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code> — Mathematical functions for complex numbers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#conversions-to-and-from-polar-coordinates">Conversions to and from polar coordinates</a></li>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#power-and-logarithmic-functions">Power and logarithmic functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#trigonometric-functions">Trigonometric functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#hyperbolic-functions">Hyperbolic functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#classification-functions">Classification functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="cmath.html#constants">Constants</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="decimal.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">decimal</span></code> — Decimal fixed point and floating point arithmetic</a><ul>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#quick-start-tutorial">Quick-start Tutorial</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#decimal-objects">Decimal objects</a><ul>
<li class="toctree-l3"><a class="reference internal" href="decimal.html#logical-operands">Logical operands</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#context-objects">Context objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#constants">Constants</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#rounding-modes">Rounding modes</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#signals">Signals</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#floating-point-notes">Floating Point Notes</a><ul>
<li class="toctree-l3"><a class="reference internal" href="decimal.html#mitigating-round-off-error-with-increased-precision">Mitigating round-off error with increased precision</a></li>
<li class="toctree-l3"><a class="reference internal" href="decimal.html#special-values">Special values</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#working-with-threads">Working with threads</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#recipes">Recipes</a></li>
<li class="toctree-l2"><a class="reference internal" href="decimal.html#decimal-faq">Decimal FAQ</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="fractions.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">fractions</span></code> — Rational numbers</a></li>
<li class="toctree-l1"><a class="reference internal" href="random.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code> — Generate pseudo-random numbers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="random.html#bookkeeping-functions">Bookkeeping functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#functions-for-bytes">Functions for bytes</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#functions-for-integers">Functions for integers</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#functions-for-sequences">Functions for sequences</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#discrete-distributions">Discrete distributions</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#real-valued-distributions">Real-valued distributions</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#alternative-generator">Alternative Generator</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#notes-on-reproducibility">Notes on Reproducibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#examples">Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="random.html#recipes">Recipes</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="statistics.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">statistics</span></code> — Mathematical statistics functions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#averages-and-measures-of-central-location">Averages and measures of central location</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#measures-of-spread">Measures of spread</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#statistics-for-relations-between-two-inputs">Statistics for relations between two inputs</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#function-details">Function details</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#exceptions">Exceptions</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#normaldist-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">NormalDist</span></code> objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="statistics.html#examples-and-recipes">Examples and Recipes</a><ul>
<li class="toctree-l3"><a class="reference internal" href="statistics.html#classic-probability-problems">Classic probability problems</a></li>
<li class="toctree-l3"><a class="reference internal" href="statistics.html#monte-carlo-inputs-for-simulations">Monte Carlo inputs for simulations</a></li>
<li class="toctree-l3"><a class="reference internal" href="statistics.html#approximating-binomial-distributions">Approximating binomial distributions</a></li>
<li class="toctree-l3"><a class="reference internal" href="statistics.html#naive-bayesian-classifier">Naive bayesian classifier</a></li>
<li class="toctree-l3"><a class="reference internal" href="statistics.html#kernel-density-estimation">Kernel density estimation</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="graphlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="numbers.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/numeric.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="numbers.html" title="numbers — Numeric abstract base classes"
             >next</a> |</li>
        <li class="right" >
          <a href="graphlib.html" title="graphlib — Functionality to operate with graph-like structures"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Numeric and Mathematical Modules</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>