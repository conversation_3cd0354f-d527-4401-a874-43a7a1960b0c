$o-so-font-size-base: 1rem !default;
$o-so-4k-multiplier: 2.3 !default;

$o-so-font-size-4k: $o-so-font-size-base * $o-so-4k-multiplier !default;

// Mirror enterprise grayscale
$o-white: #FFF !default;
$o-black: #000 !default;

$o-gray-100: #F9FAFB !default;
$o-gray-200: #eeeeee !default;
$o-gray-300: #d8dadd !default;
$o-gray-400: #9a9ca5 !default;
$o-gray-500: #7c7f89 !default;
$o-gray-600: #5f636f !default;
$o-gray-700: #374151 !default;
$o-gray-800: #1F2937 !default;
$o-gray-900: #111827 !default;


// Buttons
// Map of customized values for each button. If a button's design is defined
// here, the relative values will take priority over default BS ones.
// Notice: each map's entry is passed directly to the Bootstrap mixin, meaning
// that all states must be defined, there can't be omissions.
$o-btns-bs-override: () !default;
$o-btns-bs-override: map-merge((
    "light": (
        background: $o-white,
        border: $o-gray-300,
        color: $o-gray-700,

        hover-background: $o-white,
        hover-border: var(--companyColor),
        hover-color: $o-gray-900,

        active-background:  $o-white,
        active-border: var(--companyColor),
        active-color: $o-gray-900,
    ),
    "secondary": (
        background: $o-gray-200,
        border: $o-gray-200,
        color: $o-gray-700,

        hover-background: $o-gray-300,
        hover-border: $o-gray-300,
        hover-color: $o-gray-800,

        active-background: var(--secondary-active-background),
        active-border: var(--secondary-active-border),
        active-color: $o-gray-900,
    ),
), $o-btns-bs-override);


// Mixins
@mixin o-so-landscape() {
    @media (orientation: landscape) {
        @content;
    }
}

@mixin o-so-portrait() {
    @media (orientation: portrait) {
        @content;
    }
}
