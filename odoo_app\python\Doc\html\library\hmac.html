<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="hmac — Keyed-Hashing for Message Authentication" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/hmac.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/hmac.py This module implements the HMAC algorithm as described by RFC 2104. An HMAC object has the following methods: A hash object has the following attributes: This module also p..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/hmac.py This module implements the HMAC algorithm as described by RFC 2104. An HMAC object has the following methods: A hash object has the following attributes: This module also p..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>hmac — Keyed-Hashing for Message Authentication &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="secrets — Generate secure random numbers for managing secrets" href="secrets.html" />
    <link rel="prev" title="hashlib — Secure hashes and message digests" href="hashlib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/hmac.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="hashlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="secrets.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/hmac.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="secrets.html" title="secrets — Generate secure random numbers for managing secrets"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="hashlib.html" title="hashlib — Secure hashes and message digests"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" accesskey="U">Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-hmac">
<span id="hmac-keyed-hashing-for-message-authentication"></span><h1><a class="reference internal" href="#module-hmac" title="hmac: Keyed-Hashing for Message Authentication (HMAC) implementation"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code></a> — Keyed-Hashing for Message Authentication<a class="headerlink" href="#module-hmac" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/hmac.py">Lib/hmac.py</a></p>
<hr class="docutils" />
<p>This module implements the HMAC algorithm as described by <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2104.html"><strong>RFC 2104</strong></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="hmac.new">
<span class="sig-prename descclassname"><span class="pre">hmac.</span></span><span class="sig-name descname"><span class="pre">new</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">digestmod</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hmac.new" title="Link to this definition">¶</a></dt>
<dd><p>Return a new hmac object.  <em>key</em> is a bytes or bytearray object giving the
secret key.  If <em>msg</em> is present, the method call <code class="docutils literal notranslate"><span class="pre">update(msg)</span></code> is made.
<em>digestmod</em> is the digest name, digest constructor or module for the HMAC
object to use.  It may be any name suitable to <a class="reference internal" href="hashlib.html#hashlib.new" title="hashlib.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">hashlib.new()</span></code></a>.
Despite its argument position, it is required.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Parameter <em>key</em> can be a bytes or bytearray object.
Parameter <em>msg</em> can be of any type supported by <a class="reference internal" href="hashlib.html#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a>.
Parameter <em>digestmod</em> can be the name of a hash algorithm.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>digestmod</em> argument is now required.  Pass it as a keyword
argument to avoid awkwardness when you do not have an initial <em>msg</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="hmac.digest">
<span class="sig-prename descclassname"><span class="pre">hmac.</span></span><span class="sig-name descname"><span class="pre">digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">digest</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hmac.digest" title="Link to this definition">¶</a></dt>
<dd><p>Return digest of <em>msg</em> for given secret <em>key</em> and <em>digest</em>. The
function is equivalent to <code class="docutils literal notranslate"><span class="pre">HMAC(key,</span> <span class="pre">msg,</span> <span class="pre">digest).digest()</span></code>, but
uses an optimized C or inline implementation, which is faster for messages
that fit into memory. The parameters <em>key</em>, <em>msg</em>, and <em>digest</em> have
the same meaning as in <a class="reference internal" href="#hmac.new" title="hmac.new"><code class="xref py py-func docutils literal notranslate"><span class="pre">new()</span></code></a>.</p>
<p>CPython implementation detail, the optimized C implementation is only used
when <em>digest</em> is a string and name of a digest algorithm, which is
supported by OpenSSL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<p>An HMAC object has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="hmac.HMAC.update">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hmac.HMAC.update" title="Link to this definition">¶</a></dt>
<dd><p>Update the hmac object with <em>msg</em>.  Repeated calls are equivalent to a
single call with the concatenation of all the arguments:
<code class="docutils literal notranslate"><span class="pre">m.update(a);</span> <span class="pre">m.update(b)</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">m.update(a</span> <span class="pre">+</span> <span class="pre">b)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Parameter <em>msg</em> can be of any type supported by <a class="reference internal" href="hashlib.html#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hmac.HMAC.digest">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">digest</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hmac.HMAC.digest" title="Link to this definition">¶</a></dt>
<dd><p>Return the digest of the bytes passed to the <a class="reference internal" href="#hmac.HMAC.update" title="hmac.HMAC.update"><code class="xref py py-meth docutils literal notranslate"><span class="pre">update()</span></code></a> method so far.
This bytes object will be the same length as the <em>digest_size</em> of the digest
given to the constructor.  It may contain non-ASCII bytes, including NUL
bytes.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>When comparing the output of <a class="reference internal" href="#hmac.digest" title="hmac.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> to an externally supplied
digest during a verification routine, it is recommended to use the
<a class="reference internal" href="#hmac.compare_digest" title="hmac.compare_digest"><code class="xref py py-func docutils literal notranslate"><span class="pre">compare_digest()</span></code></a> function instead of the <code class="docutils literal notranslate"><span class="pre">==</span></code> operator
to reduce the vulnerability to timing attacks.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hmac.HMAC.hexdigest">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">hexdigest</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hmac.HMAC.hexdigest" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#hmac.digest" title="hmac.digest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">digest()</span></code></a> except the digest is returned as a string twice the
length containing only hexadecimal digits.  This may be used to exchange the
value safely in email or other non-binary environments.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>When comparing the output of <a class="reference internal" href="#hmac.HMAC.hexdigest" title="hmac.HMAC.hexdigest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">hexdigest()</span></code></a> to an externally supplied
digest during a verification routine, it is recommended to use the
<a class="reference internal" href="#hmac.compare_digest" title="hmac.compare_digest"><code class="xref py py-func docutils literal notranslate"><span class="pre">compare_digest()</span></code></a> function instead of the <code class="docutils literal notranslate"><span class="pre">==</span></code> operator
to reduce the vulnerability to timing attacks.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="hmac.HMAC.copy">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#hmac.HMAC.copy" title="Link to this definition">¶</a></dt>
<dd><p>Return a copy (“clone”) of the hmac object.  This can be used to efficiently
compute the digests of strings that share a common initial substring.</p>
</dd></dl>

<p>A hash object has the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="hmac.HMAC.digest_size">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">digest_size</span></span><a class="headerlink" href="#hmac.HMAC.digest_size" title="Link to this definition">¶</a></dt>
<dd><p>The size of the resulting HMAC digest in bytes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="hmac.HMAC.block_size">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">block_size</span></span><a class="headerlink" href="#hmac.HMAC.block_size" title="Link to this definition">¶</a></dt>
<dd><p>The internal block size of the hash algorithm in bytes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="hmac.HMAC.name">
<span class="sig-prename descclassname"><span class="pre">HMAC.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#hmac.HMAC.name" title="Link to this definition">¶</a></dt>
<dd><p>The canonical name of this HMAC, always lowercase, e.g. <code class="docutils literal notranslate"><span class="pre">hmac-md5</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Removed the undocumented attributes <code class="docutils literal notranslate"><span class="pre">HMAC.digest_cons</span></code>, <code class="docutils literal notranslate"><span class="pre">HMAC.inner</span></code>,
and <code class="docutils literal notranslate"><span class="pre">HMAC.outer</span></code>.</p>
</div>
<p>This module also provides the following helper function:</p>
<dl class="py function">
<dt class="sig sig-object py" id="hmac.compare_digest">
<span class="sig-prename descclassname"><span class="pre">hmac.</span></span><span class="sig-name descname"><span class="pre">compare_digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#hmac.compare_digest" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">==</span> <span class="pre">b</span></code>.  This function uses an approach designed to prevent
timing analysis by avoiding content-based short circuiting behaviour,
making it appropriate for cryptography.  <em>a</em> and <em>b</em> must both be of the
same type: either <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> (ASCII only, as e.g. returned by
<a class="reference internal" href="#hmac.HMAC.hexdigest" title="hmac.HMAC.hexdigest"><code class="xref py py-meth docutils literal notranslate"><span class="pre">HMAC.hexdigest()</span></code></a>), or a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <em>a</em> and <em>b</em> are of different lengths, or if an error occurs,
a timing attack could theoretically reveal information about the
types and lengths of <em>a</em> and <em>b</em>—but not their values.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The function uses OpenSSL’s <code class="docutils literal notranslate"><span class="pre">CRYPTO_memcmp()</span></code> internally when
available.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="hashlib.html#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a></dt><dd><p>The Python module providing secure hash functions.</p>
</dd>
</dl>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="hashlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code> — Secure hashes and message digests</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="secrets.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/hmac.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="secrets.html" title="secrets — Generate secure random numbers for managing secrets"
             >next</a> |</li>
        <li class="right" >
          <a href="hashlib.html" title="hashlib — Secure hashes and message digests"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" >Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>