<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pickletools — Tools for pickle developers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pickletools.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pickletools.py This module contains various constants relating to the intimate details of the pickle module, some lengthy comments about the implementation, and a few useful functi..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pickletools.py This module contains various constants relating to the intimate details of the pickle module, some lengthy comments about the implementation, and a few useful functi..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pickletools — Tools for pickle developers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="MS Windows Specific Services" href="windows.html" />
    <link rel="prev" title="dis — Disassembler for Python bytecode" href="dis.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pickletools.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code> — Tools for pickle developers</a><ul>
<li><a class="reference internal" href="#command-line-usage">Command line usage</a><ul>
<li><a class="reference internal" href="#command-line-options">Command line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#programmatic-interface">Programmatic Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dis.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dis</span></code> — Disassembler for Python bytecode</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="windows.html"
                          title="next chapter">MS Windows Specific Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pickletools.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="windows.html" title="MS Windows Specific Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="dis.html" title="dis — Disassembler for Python bytecode"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" accesskey="U">Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code> — Tools for pickle developers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pickletools">
<span id="pickletools-tools-for-pickle-developers"></span><h1><a class="reference internal" href="#module-pickletools" title="pickletools: Contains extensive comments about the pickle protocols and pickle-machine opcodes, as well as some useful functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code></a> — Tools for pickle developers<a class="headerlink" href="#module-pickletools" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pickletools.py">Lib/pickletools.py</a></p>
<hr class="docutils" />
<p>This module contains various constants relating to the intimate details of the
<a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> module, some lengthy comments about the implementation, and a
few useful functions for analyzing pickled data.  The contents of this module
are useful for Python core developers who are working on the <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a>;
ordinary users of the <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> module probably won’t find the
<a class="reference internal" href="#module-pickletools" title="pickletools: Contains extensive comments about the pickle protocols and pickle-machine opcodes, as well as some useful functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code></a> module relevant.</p>
<section id="command-line-usage">
<span id="pickletools-cli"></span><h2>Command line usage<a class="headerlink" href="#command-line-usage" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>When invoked from the command line, <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">pickletools</span></code> will
disassemble the contents of one or more pickle files.  Note that if
you want to see the Python object stored in the pickle rather than the
details of pickle format, you may want to use <code class="docutils literal notranslate"><span class="pre">-m</span> <span class="pre">pickle</span></code> instead.
However, when the pickle file that you want to examine comes from an
untrusted source, <code class="docutils literal notranslate"><span class="pre">-m</span> <span class="pre">pickletools</span></code> is a safer option because it does
not execute pickle bytecode.</p>
<p>For example, with a tuple <code class="docutils literal notranslate"><span class="pre">(1,</span> <span class="pre">2)</span></code> pickled in file <code class="docutils literal notranslate"><span class="pre">x.pickle</span></code>:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>pickle<span class="w"> </span>x.pickle
<span class="gp gp-VirtualEnv">(1, 2)</span>

<span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>pickletools<span class="w"> </span>x.pickle
<span class="go">    0: \x80 PROTO      3</span>
<span class="go">    2: K    BININT1    1</span>
<span class="go">    4: K    BININT1    2</span>
<span class="go">    6: \x86 TUPLE2</span>
<span class="go">    7: q    BINPUT     0</span>
<span class="go">    9: .    STOP</span>
<span class="go">highest protocol among opcodes = 2</span>
</pre></div>
</div>
<section id="command-line-options">
<h3>Command line options<a class="headerlink" href="#command-line-options" title="Link to this heading">¶</a></h3>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-pickletools-a">
<span id="cmdoption-pickletools-annotate"></span><span class="sig-name descname"><span class="pre">-a</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--annotate</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-pickletools-a" title="Link to this definition">¶</a></dt>
<dd><p>Annotate each line with a short opcode description.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-pickletools-o">
<span id="cmdoption-pickletools-output"></span><span class="sig-name descname"><span class="pre">-o</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--output</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;file&gt;</span></span><a class="headerlink" href="#cmdoption-pickletools-o" title="Link to this definition">¶</a></dt>
<dd><p>Name of a file where the output should be written.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-pickletools-l">
<span id="cmdoption-pickletools-indentlevel"></span><span class="sig-name descname"><span class="pre">-l</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--indentlevel</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;num&gt;</span></span><a class="headerlink" href="#cmdoption-pickletools-l" title="Link to this definition">¶</a></dt>
<dd><p>The number of blanks by which to indent a new MARK level.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-pickletools-m">
<span id="cmdoption-pickletools-memo"></span><span class="sig-name descname"><span class="pre">-m</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--memo</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-pickletools-m" title="Link to this definition">¶</a></dt>
<dd><p>When multiple objects are disassembled, preserve memo between
disassemblies.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-pickletools-p">
<span id="cmdoption-pickletools-preamble"></span><span class="sig-name descname"><span class="pre">-p</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--preamble</span></span><span class="sig-prename descclassname"><span class="pre">=&lt;preamble&gt;</span></span><a class="headerlink" href="#cmdoption-pickletools-p" title="Link to this definition">¶</a></dt>
<dd><p>When more than one pickle file are specified, print given preamble
before each disassembly.</p>
</dd></dl>

</section>
</section>
<section id="programmatic-interface">
<h2>Programmatic Interface<a class="headerlink" href="#programmatic-interface" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="pickletools.dis">
<span class="sig-prename descclassname"><span class="pre">pickletools.</span></span><span class="sig-name descname"><span class="pre">dis</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pickle</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">out</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">memo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indentlevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">annotate</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pickletools.dis" title="Link to this definition">¶</a></dt>
<dd><p>Outputs a symbolic disassembly of the pickle to the file-like
object <em>out</em>, defaulting to <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>.  <em>pickle</em> can be a
string or a file-like object.  <em>memo</em> can be a Python dictionary
that will be used as the pickle’s memo; it can be used to perform
disassemblies across multiple pickles created by the same
pickler. Successive levels, indicated by <code class="docutils literal notranslate"><span class="pre">MARK</span></code> opcodes in the
stream, are indented by <em>indentlevel</em> spaces.  If a nonzero value
is given to <em>annotate</em>, each opcode in the output is annotated with
a short description.  The value of <em>annotate</em> is used as a hint for
the column where annotation should start.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>annotate</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pickletools.genops">
<span class="sig-prename descclassname"><span class="pre">pickletools.</span></span><span class="sig-name descname"><span class="pre">genops</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pickle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pickletools.genops" title="Link to this definition">¶</a></dt>
<dd><p>Provides an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> over all of the opcodes in a pickle, returning a
sequence of <code class="docutils literal notranslate"><span class="pre">(opcode,</span> <span class="pre">arg,</span> <span class="pre">pos)</span></code> triples.  <em>opcode</em> is an instance of an
<code class="xref py py-class docutils literal notranslate"><span class="pre">OpcodeInfo</span></code> class; <em>arg</em> is the decoded value, as a Python object, of
the opcode’s argument; <em>pos</em> is the position at which this opcode is located.
<em>pickle</em> can be a string or a file-like object.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pickletools.optimize">
<span class="sig-prename descclassname"><span class="pre">pickletools.</span></span><span class="sig-name descname"><span class="pre">optimize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">picklestring</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pickletools.optimize" title="Link to this definition">¶</a></dt>
<dd><p>Returns a new equivalent pickle string after eliminating unused <code class="docutils literal notranslate"><span class="pre">PUT</span></code>
opcodes. The optimized pickle is shorter, takes less transmission time,
requires less storage space, and unpickles more efficiently.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code> — Tools for pickle developers</a><ul>
<li><a class="reference internal" href="#command-line-usage">Command line usage</a><ul>
<li><a class="reference internal" href="#command-line-options">Command line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#programmatic-interface">Programmatic Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dis.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dis</span></code> — Disassembler for Python bytecode</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="windows.html"
                          title="next chapter">MS Windows Specific Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pickletools.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="windows.html" title="MS Windows Specific Services"
             >next</a> |</li>
        <li class="right" >
          <a href="dis.html" title="dis — Disassembler for Python bytecode"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" >Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickletools</span></code> — Tools for pickle developers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>