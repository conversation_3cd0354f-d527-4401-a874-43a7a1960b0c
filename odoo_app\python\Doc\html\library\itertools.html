<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="itertools — Functions creating iterators for efficient looping" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/itertools.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module implements a number of iterator building blocks inspired by constructs from APL, Haskell, and SML. Each has been recast in a form suitable for Python. The module standardizes a core set..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module implements a number of iterator building blocks inspired by constructs from APL, Haskell, and SML. Each has been recast in a form suitable for Python. The module standardizes a core set..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>itertools — Functions creating iterators for efficient looping &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="functools — Higher-order functions and operations on callable objects" href="functools.html" />
    <link rel="prev" title="Functional Programming Modules" href="functional.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/itertools.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">itertools</span></code> — Functions creating iterators for efficient looping</a><ul>
<li><a class="reference internal" href="#itertool-functions">Itertool Functions</a></li>
<li><a class="reference internal" href="#itertools-recipes">Itertools Recipes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="functional.html"
                          title="previous chapter">Functional Programming Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functools.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">functools</span></code> — Higher-order functions and operations on callable objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/itertools.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functools.html" title="functools — Higher-order functions and operations on callable objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="functional.html" title="Functional Programming Modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="functional.html" accesskey="U">Functional Programming Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">itertools</span></code> — Functions creating iterators for efficient looping</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-itertools">
<span id="itertools-functions-creating-iterators-for-efficient-looping"></span><h1><a class="reference internal" href="#module-itertools" title="itertools: Functions creating iterators for efficient looping."><code class="xref py py-mod docutils literal notranslate"><span class="pre">itertools</span></code></a> — Functions creating iterators for efficient looping<a class="headerlink" href="#module-itertools" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module implements a number of <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> building blocks inspired
by constructs from APL, Haskell, and SML.  Each has been recast in a form
suitable for Python.</p>
<p>The module standardizes a core set of fast, memory efficient tools that are
useful by themselves or in combination.  Together, they form an “iterator
algebra” making it possible to construct specialized tools succinctly and
efficiently in pure Python.</p>
<p>For instance, SML provides a tabulation tool: <code class="docutils literal notranslate"><span class="pre">tabulate(f)</span></code> which produces a
sequence <code class="docutils literal notranslate"><span class="pre">f(0),</span> <span class="pre">f(1),</span> <span class="pre">...</span></code>.  The same effect can be achieved in Python
by combining <a class="reference internal" href="functions.html#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a> and <a class="reference internal" href="#itertools.count" title="itertools.count"><code class="xref py py-func docutils literal notranslate"><span class="pre">count()</span></code></a> to form <code class="docutils literal notranslate"><span class="pre">map(f,</span> <span class="pre">count())</span></code>.</p>
<p>These tools and their built-in counterparts also work well with the high-speed
functions in the <a class="reference internal" href="operator.html#module-operator" title="operator: Functions corresponding to the standard operators."><code class="xref py py-mod docutils literal notranslate"><span class="pre">operator</span></code></a> module.  For example, the multiplication
operator can be mapped across two vectors to form an efficient dot-product:
<code class="docutils literal notranslate"><span class="pre">sum(starmap(operator.mul,</span> <span class="pre">zip(vec1,</span> <span class="pre">vec2,</span> <span class="pre">strict=True)))</span></code>.</p>
<p><strong>Infinite iterators:</strong></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Iterator</p></th>
<th class="head"><p>Arguments</p></th>
<th class="head"><p>Results</p></th>
<th class="head"><p>Example</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.count" title="itertools.count"><code class="xref py py-func docutils literal notranslate"><span class="pre">count()</span></code></a></p></td>
<td><p>[start[, step]]</p></td>
<td><p>start, start+step, start+2*step, …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">count(10)</span> <span class="pre">→</span> <span class="pre">10</span> <span class="pre">11</span> <span class="pre">12</span> <span class="pre">13</span> <span class="pre">14</span> <span class="pre">...</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.cycle" title="itertools.cycle"><code class="xref py py-func docutils literal notranslate"><span class="pre">cycle()</span></code></a></p></td>
<td><p>p</p></td>
<td><p>p0, p1, … plast, p0, p1, …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">cycle('ABCD')</span> <span class="pre">→</span> <span class="pre">A</span> <span class="pre">B</span> <span class="pre">C</span> <span class="pre">D</span> <span class="pre">A</span> <span class="pre">B</span> <span class="pre">C</span> <span class="pre">D</span> <span class="pre">...</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.repeat" title="itertools.repeat"><code class="xref py py-func docutils literal notranslate"><span class="pre">repeat()</span></code></a></p></td>
<td><p>elem [,n]</p></td>
<td><p>elem, elem, elem, … endlessly or up to n times</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">repeat(10,</span> <span class="pre">3)</span> <span class="pre">→</span> <span class="pre">10</span> <span class="pre">10</span> <span class="pre">10</span></code></p></td>
</tr>
</tbody>
</table>
<p><strong>Iterators terminating on the shortest input sequence:</strong></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Iterator</p></th>
<th class="head"><p>Arguments</p></th>
<th class="head"><p>Results</p></th>
<th class="head"><p>Example</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.accumulate" title="itertools.accumulate"><code class="xref py py-func docutils literal notranslate"><span class="pre">accumulate()</span></code></a></p></td>
<td><p>p [,func]</p></td>
<td><p>p0, p0+p1, p0+p1+p2, …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">accumulate([1,2,3,4,5])</span> <span class="pre">→</span> <span class="pre">1</span> <span class="pre">3</span> <span class="pre">6</span> <span class="pre">10</span> <span class="pre">15</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.batched" title="itertools.batched"><code class="xref py py-func docutils literal notranslate"><span class="pre">batched()</span></code></a></p></td>
<td><p>p, n</p></td>
<td><p>(p0, p1, …, p_n-1), …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">batched('ABCDEFG',</span> <span class="pre">n=3)</span> <span class="pre">→</span> <span class="pre">ABC</span> <span class="pre">DEF</span> <span class="pre">G</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.chain" title="itertools.chain"><code class="xref py py-func docutils literal notranslate"><span class="pre">chain()</span></code></a></p></td>
<td><p>p, q, …</p></td>
<td><p>p0, p1, … plast, q0, q1, …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">chain('ABC',</span> <span class="pre">'DEF')</span> <span class="pre">→</span> <span class="pre">A</span> <span class="pre">B</span> <span class="pre">C</span> <span class="pre">D</span> <span class="pre">E</span> <span class="pre">F</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.chain.from_iterable" title="itertools.chain.from_iterable"><code class="xref py py-func docutils literal notranslate"><span class="pre">chain.from_iterable()</span></code></a></p></td>
<td><p>iterable</p></td>
<td><p>p0, p1, … plast, q0, q1, …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">chain.from_iterable(['ABC',</span> <span class="pre">'DEF'])</span> <span class="pre">→</span> <span class="pre">A</span> <span class="pre">B</span> <span class="pre">C</span> <span class="pre">D</span> <span class="pre">E</span> <span class="pre">F</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.compress" title="itertools.compress"><code class="xref py py-func docutils literal notranslate"><span class="pre">compress()</span></code></a></p></td>
<td><p>data, selectors</p></td>
<td><p>(d[0] if s[0]), (d[1] if s[1]), …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">compress('ABCDEF',</span> <span class="pre">[1,0,1,0,1,1])</span> <span class="pre">→</span> <span class="pre">A</span> <span class="pre">C</span> <span class="pre">E</span> <span class="pre">F</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.dropwhile" title="itertools.dropwhile"><code class="xref py py-func docutils literal notranslate"><span class="pre">dropwhile()</span></code></a></p></td>
<td><p>predicate, seq</p></td>
<td><p>seq[n], seq[n+1], starting when predicate fails</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">dropwhile(lambda</span> <span class="pre">x:</span> <span class="pre">x&lt;5,</span> <span class="pre">[1,4,6,4,1])</span> <span class="pre">→</span> <span class="pre">6</span> <span class="pre">4</span> <span class="pre">1</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.filterfalse" title="itertools.filterfalse"><code class="xref py py-func docutils literal notranslate"><span class="pre">filterfalse()</span></code></a></p></td>
<td><p>predicate, seq</p></td>
<td><p>elements of seq where predicate(elem) fails</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">filterfalse(lambda</span> <span class="pre">x:</span> <span class="pre">x%2,</span> <span class="pre">range(10))</span> <span class="pre">→</span> <span class="pre">0</span> <span class="pre">2</span> <span class="pre">4</span> <span class="pre">6</span> <span class="pre">8</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.groupby" title="itertools.groupby"><code class="xref py py-func docutils literal notranslate"><span class="pre">groupby()</span></code></a></p></td>
<td><p>iterable[, key]</p></td>
<td><p>sub-iterators grouped by value of key(v)</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.islice" title="itertools.islice"><code class="xref py py-func docutils literal notranslate"><span class="pre">islice()</span></code></a></p></td>
<td><p>seq, [start,] stop [, step]</p></td>
<td><p>elements from seq[start:stop:step]</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">islice('ABCDEFG',</span> <span class="pre">2,</span> <span class="pre">None)</span> <span class="pre">→</span> <span class="pre">C</span> <span class="pre">D</span> <span class="pre">E</span> <span class="pre">F</span> <span class="pre">G</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.pairwise" title="itertools.pairwise"><code class="xref py py-func docutils literal notranslate"><span class="pre">pairwise()</span></code></a></p></td>
<td><p>iterable</p></td>
<td><p>(p[0], p[1]), (p[1], p[2])</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">pairwise('ABCDEFG')</span> <span class="pre">→</span> <span class="pre">AB</span> <span class="pre">BC</span> <span class="pre">CD</span> <span class="pre">DE</span> <span class="pre">EF</span> <span class="pre">FG</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.starmap" title="itertools.starmap"><code class="xref py py-func docutils literal notranslate"><span class="pre">starmap()</span></code></a></p></td>
<td><p>func, seq</p></td>
<td><p>func(*seq[0]), func(*seq[1]), …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">starmap(pow,</span> <span class="pre">[(2,5),</span> <span class="pre">(3,2),</span> <span class="pre">(10,3)])</span> <span class="pre">→</span> <span class="pre">32</span> <span class="pre">9</span> <span class="pre">1000</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.takewhile" title="itertools.takewhile"><code class="xref py py-func docutils literal notranslate"><span class="pre">takewhile()</span></code></a></p></td>
<td><p>predicate, seq</p></td>
<td><p>seq[0], seq[1], until predicate fails</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">takewhile(lambda</span> <span class="pre">x:</span> <span class="pre">x&lt;5,</span> <span class="pre">[1,4,6,4,1])</span> <span class="pre">→</span> <span class="pre">1</span> <span class="pre">4</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.tee" title="itertools.tee"><code class="xref py py-func docutils literal notranslate"><span class="pre">tee()</span></code></a></p></td>
<td><p>it, n</p></td>
<td><p>it1, it2, … itn  splits one iterator into n</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.zip_longest" title="itertools.zip_longest"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip_longest()</span></code></a></p></td>
<td><p>p, q, …</p></td>
<td><p>(p[0], q[0]), (p[1], q[1]), …</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">zip_longest('ABCD',</span> <span class="pre">'xy',</span> <span class="pre">fillvalue='-')</span> <span class="pre">→</span> <span class="pre">Ax</span> <span class="pre">By</span> <span class="pre">C-</span> <span class="pre">D-</span></code></p></td>
</tr>
</tbody>
</table>
<p><strong>Combinatoric iterators:</strong></p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Iterator</p></th>
<th class="head"><p>Arguments</p></th>
<th class="head"><p>Results</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.product" title="itertools.product"><code class="xref py py-func docutils literal notranslate"><span class="pre">product()</span></code></a></p></td>
<td><p>p, q, … [repeat=1]</p></td>
<td><p>cartesian product, equivalent to a nested for-loop</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.permutations" title="itertools.permutations"><code class="xref py py-func docutils literal notranslate"><span class="pre">permutations()</span></code></a></p></td>
<td><p>p[, r]</p></td>
<td><p>r-length tuples, all possible orderings, no repeated elements</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#itertools.combinations" title="itertools.combinations"><code class="xref py py-func docutils literal notranslate"><span class="pre">combinations()</span></code></a></p></td>
<td><p>p, r</p></td>
<td><p>r-length tuples, in sorted order, no repeated elements</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#itertools.combinations_with_replacement" title="itertools.combinations_with_replacement"><code class="xref py py-func docutils literal notranslate"><span class="pre">combinations_with_replacement()</span></code></a></p></td>
<td><p>p, r</p></td>
<td><p>r-length tuples, in sorted order, with repeated elements</p></td>
</tr>
</tbody>
</table>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Examples</p></th>
<th class="head"><p>Results</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">product('ABCD',</span> <span class="pre">repeat=2)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">AA</span> <span class="pre">AB</span> <span class="pre">AC</span> <span class="pre">AD</span> <span class="pre">BA</span> <span class="pre">BB</span> <span class="pre">BC</span> <span class="pre">BD</span> <span class="pre">CA</span> <span class="pre">CB</span> <span class="pre">CC</span> <span class="pre">CD</span> <span class="pre">DA</span> <span class="pre">DB</span> <span class="pre">DC</span> <span class="pre">DD</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">permutations('ABCD',</span> <span class="pre">2)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">AB</span> <span class="pre">AC</span> <span class="pre">AD</span> <span class="pre">BA</span> <span class="pre">BC</span> <span class="pre">BD</span> <span class="pre">CA</span> <span class="pre">CB</span> <span class="pre">CD</span> <span class="pre">DA</span> <span class="pre">DB</span> <span class="pre">DC</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">combinations('ABCD',</span> <span class="pre">2)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">AB</span> <span class="pre">AC</span> <span class="pre">AD</span> <span class="pre">BC</span> <span class="pre">BD</span> <span class="pre">CD</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">combinations_with_replacement('ABCD', 2)</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">AA</span> <span class="pre">AB</span> <span class="pre">AC</span> <span class="pre">AD</span> <span class="pre">BB</span> <span class="pre">BC</span> <span class="pre">BD</span> <span class="pre">CC</span> <span class="pre">CD</span> <span class="pre">DD</span></code></p></td>
</tr>
</tbody>
</table>
<section id="itertool-functions">
<span id="itertools-functions"></span><h2>Itertool Functions<a class="headerlink" href="#itertool-functions" title="Link to this heading">¶</a></h2>
<p>The following module functions all construct and return iterators. Some provide
streams of infinite length, so they should only be accessed by functions or
loops that truncate the stream.</p>
<dl class="py function">
<dt class="sig sig-object py" id="itertools.accumulate">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">accumulate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial=None</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#itertools.accumulate" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns accumulated sums, or accumulated
results of other binary functions (specified via the optional
<em>func</em> argument).</p>
<p>If <em>func</em> is supplied, it should be a function
of two arguments. Elements of the input <em>iterable</em> may be any type
that can be accepted as arguments to <em>func</em>. (For example, with
the default operation of addition, elements may be any addable
type including <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a> or
<a class="reference internal" href="fractions.html#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a>.)</p>
<p>Usually, the number of elements output matches the input iterable.
However, if the keyword argument <em>initial</em> is provided, the
accumulation leads off with the <em>initial</em> value so that the output
has one more element than the input iterable.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">accumulate</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">func</span><span class="o">=</span><span class="n">operator</span><span class="o">.</span><span class="n">add</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">initial</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s1">&#39;Return running totals&#39;</span>
    <span class="c1"># accumulate([1,2,3,4,5]) → 1 3 6 10 15</span>
    <span class="c1"># accumulate([1,2,3,4,5], initial=100) → 100 101 103 106 110 115</span>
    <span class="c1"># accumulate([1,2,3,4,5], operator.mul) → 1 2 6 24 120</span>
    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">total</span> <span class="o">=</span> <span class="n">initial</span>
    <span class="k">if</span> <span class="n">initial</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">total</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
            <span class="k">return</span>
    <span class="k">yield</span> <span class="n">total</span>
    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">it</span><span class="p">:</span>
        <span class="n">total</span> <span class="o">=</span> <span class="n">func</span><span class="p">(</span><span class="n">total</span><span class="p">,</span> <span class="n">element</span><span class="p">)</span>
        <span class="k">yield</span> <span class="n">total</span>
</pre></div>
</div>
<p>There are a number of uses for the <em>func</em> argument.  It can be set to
<a class="reference internal" href="functions.html#min" title="min"><code class="xref py py-func docutils literal notranslate"><span class="pre">min()</span></code></a> for a running minimum, <a class="reference internal" href="functions.html#max" title="max"><code class="xref py py-func docutils literal notranslate"><span class="pre">max()</span></code></a> for a running maximum, or
<a class="reference internal" href="operator.html#operator.mul" title="operator.mul"><code class="xref py py-func docutils literal notranslate"><span class="pre">operator.mul()</span></code></a> for a running product.  Amortization tables can be
built by accumulating interest and applying payments:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">=</span> <span class="p">[</span><span class="mi">3</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">6</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">9</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">7</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">8</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">accumulate</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">operator</span><span class="o">.</span><span class="n">mul</span><span class="p">))</span>     <span class="c1"># running product</span>
<span class="go">[3, 12, 72, 144, 144, 1296, 0, 0, 0, 0]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">accumulate</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="nb">max</span><span class="p">))</span>              <span class="c1"># running maximum</span>
<span class="go">[3, 4, 6, 6, 6, 9, 9, 9, 9, 9]</span>

<span class="go"># Amortize a 5% loan of 1000 with 10 annual payments of 90</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">account_update</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">bal</span><span class="p">,</span> <span class="n">pmt</span><span class="p">:</span> <span class="nb">round</span><span class="p">(</span><span class="n">bal</span> <span class="o">*</span> <span class="mf">1.05</span><span class="p">)</span> <span class="o">+</span> <span class="n">pmt</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">accumulate</span><span class="p">(</span><span class="n">repeat</span><span class="p">(</span><span class="o">-</span><span class="mi">90</span><span class="p">,</span> <span class="mi">10</span><span class="p">),</span> <span class="n">account_update</span><span class="p">,</span> <span class="n">initial</span><span class="o">=</span><span class="mi">1_000</span><span class="p">))</span>
<span class="go">[1000, 960, 918, 874, 828, 779, 728, 674, 618, 559, 497]</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="functools.html#functools.reduce" title="functools.reduce"><code class="xref py py-func docutils literal notranslate"><span class="pre">functools.reduce()</span></code></a> for a similar function that returns only the
final accumulated value.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the optional <em>func</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the optional <em>initial</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.batched">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">batched</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.batched" title="Link to this definition">¶</a></dt>
<dd><p>Batch data from the <em>iterable</em> into tuples of length <em>n</em>. The last
batch may be shorter than <em>n</em>.</p>
<p>Loops over the input iterable and accumulates data into tuples up to
size <em>n</em>.  The input is consumed lazily, just enough to fill a batch.
The result is yielded as soon as the batch is full or when the input
iterable is exhausted:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">flattened_data</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;roses&#39;</span><span class="p">,</span> <span class="s1">&#39;red&#39;</span><span class="p">,</span> <span class="s1">&#39;violets&#39;</span><span class="p">,</span> <span class="s1">&#39;blue&#39;</span><span class="p">,</span> <span class="s1">&#39;sugar&#39;</span><span class="p">,</span> <span class="s1">&#39;sweet&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unflattened</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">batched</span><span class="p">(</span><span class="n">flattened_data</span><span class="p">,</span> <span class="mi">2</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">unflattened</span>
<span class="go">[(&#39;roses&#39;, &#39;red&#39;), (&#39;violets&#39;, &#39;blue&#39;), (&#39;sugar&#39;, &#39;sweet&#39;)]</span>

<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">batch</span> <span class="ow">in</span> <span class="n">batched</span><span class="p">(</span><span class="s1">&#39;ABCDEFG&#39;</span><span class="p">,</span> <span class="mi">3</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">batch</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">(&#39;A&#39;, &#39;B&#39;, &#39;C&#39;)</span>
<span class="go">(&#39;D&#39;, &#39;E&#39;, &#39;F&#39;)</span>
<span class="go">(&#39;G&#39;,)</span>
</pre></div>
</div>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">batched</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">):</span>
    <span class="c1"># batched(&#39;ABCDEFG&#39;, 3) → ABC DEF G</span>
    <span class="k">if</span> <span class="n">n</span> <span class="o">&lt;</span> <span class="mi">1</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;n must be at least one&#39;</span><span class="p">)</span>
    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="k">while</span> <span class="n">batch</span> <span class="o">:=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">it</span><span class="p">,</span> <span class="n">n</span><span class="p">)):</span>
        <span class="k">yield</span> <span class="n">batch</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.chain">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">chain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">iterables</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.chain" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns elements from the first iterable until it is
exhausted, then proceeds to the next iterable, until all of the iterables are
exhausted.  Used for treating consecutive sequences as a single sequence.
Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">chain</span><span class="p">(</span><span class="o">*</span><span class="n">iterables</span><span class="p">):</span>
    <span class="c1"># chain(&#39;ABC&#39;, &#39;DEF&#39;) → A B C D E F</span>
    <span class="k">for</span> <span class="n">it</span> <span class="ow">in</span> <span class="n">iterables</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">it</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">element</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="itertools.chain.from_iterable">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">chain.</span></span><span class="sig-name descname"><span class="pre">from_iterable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.chain.from_iterable" title="Link to this definition">¶</a></dt>
<dd><p>Alternate constructor for <a class="reference internal" href="#itertools.chain" title="itertools.chain"><code class="xref py py-func docutils literal notranslate"><span class="pre">chain()</span></code></a>.  Gets chained inputs from a
single iterable argument that is evaluated lazily.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">from_iterable</span><span class="p">(</span><span class="n">iterables</span><span class="p">):</span>
    <span class="c1"># chain.from_iterable([&#39;ABC&#39;, &#39;DEF&#39;]) → A B C D E F</span>
    <span class="k">for</span> <span class="n">it</span> <span class="ow">in</span> <span class="n">iterables</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">it</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">element</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.combinations">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">combinations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">r</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.combinations" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>r</em> length subsequences of elements from the input <em>iterable</em>.</p>
<p>The combination tuples are emitted in lexicographic ordering according to
the order of the input <em>iterable</em>. So, if the input <em>iterable</em> is sorted,
the output tuples will be produced in sorted order.</p>
<p>Elements are treated as unique based on their position, not on their
value.  So if the input elements are unique, there will be no repeated
values in each combination.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">combinations</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="p">):</span>
    <span class="c1"># combinations(&#39;ABCD&#39;, 2) → AB AC AD BC BD CD</span>
    <span class="c1"># combinations(range(4), 3) → 012 013 023 123</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">r</span> <span class="o">&gt;</span> <span class="n">n</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="n">indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">r</span><span class="p">))</span>
    <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">r</span><span class="p">)):</span>
            <span class="k">if</span> <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">!=</span> <span class="n">i</span> <span class="o">+</span> <span class="n">n</span> <span class="o">-</span> <span class="n">r</span><span class="p">:</span>
                <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">,</span> <span class="n">r</span><span class="p">):</span>
            <span class="n">indices</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="n">indices</span><span class="p">[</span><span class="n">j</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>
        <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
</pre></div>
</div>
<p>The code for <a class="reference internal" href="#itertools.combinations" title="itertools.combinations"><code class="xref py py-func docutils literal notranslate"><span class="pre">combinations()</span></code></a> can be also expressed as a subsequence
of <a class="reference internal" href="#itertools.permutations" title="itertools.permutations"><code class="xref py py-func docutils literal notranslate"><span class="pre">permutations()</span></code></a> after filtering entries where the elements are not
in sorted order (according to their position in the input pool):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">combinations</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="p">):</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">indices</span> <span class="ow">in</span> <span class="n">permutations</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">),</span> <span class="n">r</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">indices</span><span class="p">)</span> <span class="o">==</span> <span class="nb">list</span><span class="p">(</span><span class="n">indices</span><span class="p">):</span>
            <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
</pre></div>
</div>
<p>The number of items returned is <code class="docutils literal notranslate"><span class="pre">n!</span> <span class="pre">/</span> <span class="pre">r!</span> <span class="pre">/</span> <span class="pre">(n-r)!</span></code> when <code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">&lt;=</span> <span class="pre">r</span> <span class="pre">&lt;=</span> <span class="pre">n</span></code>
or zero when <code class="docutils literal notranslate"><span class="pre">r</span> <span class="pre">&gt;</span> <span class="pre">n</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.combinations_with_replacement">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">combinations_with_replacement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">r</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.combinations_with_replacement" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>r</em> length subsequences of elements from the input <em>iterable</em>
allowing individual elements to be repeated more than once.</p>
<p>The combination tuples are emitted in lexicographic ordering according to
the order of the input <em>iterable</em>. So, if the input <em>iterable</em> is sorted,
the output tuples will be produced in sorted order.</p>
<p>Elements are treated as unique based on their position, not on their
value.  So if the input elements are unique, the generated combinations
will also be unique.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">combinations_with_replacement</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="p">):</span>
    <span class="c1"># combinations_with_replacement(&#39;ABC&#39;, 2) → AA AB AC BB BC CC</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">n</span> <span class="ow">and</span> <span class="n">r</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="n">indices</span> <span class="o">=</span> <span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">*</span> <span class="n">r</span>
    <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">r</span><span class="p">)):</span>
            <span class="k">if</span> <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">!=</span> <span class="n">n</span> <span class="o">-</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span>
        <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">:]</span> <span class="o">=</span> <span class="p">[</span><span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span><span class="p">]</span> <span class="o">*</span> <span class="p">(</span><span class="n">r</span> <span class="o">-</span> <span class="n">i</span><span class="p">)</span>
        <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
</pre></div>
</div>
<p>The code for <a class="reference internal" href="#itertools.combinations_with_replacement" title="itertools.combinations_with_replacement"><code class="xref py py-func docutils literal notranslate"><span class="pre">combinations_with_replacement()</span></code></a> can be also expressed as
a subsequence of <a class="reference internal" href="#itertools.product" title="itertools.product"><code class="xref py py-func docutils literal notranslate"><span class="pre">product()</span></code></a> after filtering entries where the elements
are not in sorted order (according to their position in the input pool):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">combinations_with_replacement</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="p">):</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">indices</span> <span class="ow">in</span> <span class="n">product</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">),</span> <span class="n">repeat</span><span class="o">=</span><span class="n">r</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">indices</span><span class="p">)</span> <span class="o">==</span> <span class="nb">list</span><span class="p">(</span><span class="n">indices</span><span class="p">):</span>
            <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
</pre></div>
</div>
<p>The number of items returned is <code class="docutils literal notranslate"><span class="pre">(n+r-1)!</span> <span class="pre">/</span> <span class="pre">r!</span> <span class="pre">/</span> <span class="pre">(n-1)!</span></code> when <code class="docutils literal notranslate"><span class="pre">n</span> <span class="pre">&gt;</span> <span class="pre">0</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.compress">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">compress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">selectors</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.compress" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that filters elements from <em>data</em> returning only those that
have a corresponding element in <em>selectors</em> that evaluates to <code class="docutils literal notranslate"><span class="pre">True</span></code>.
Stops when either the <em>data</em> or <em>selectors</em> iterables has been exhausted.
Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">compress</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">selectors</span><span class="p">):</span>
    <span class="c1"># compress(&#39;ABCDEF&#39;, [1,0,1,0,1,1]) → A C E F</span>
    <span class="k">return</span> <span class="p">(</span><span class="n">d</span> <span class="k">for</span> <span class="n">d</span><span class="p">,</span> <span class="n">s</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">selectors</span><span class="p">)</span> <span class="k">if</span> <span class="n">s</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.count">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">step</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.count" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns evenly spaced values starting with number <em>start</em>. Often
used as an argument to <a class="reference internal" href="functions.html#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a> to generate consecutive data points.
Also, used with <a class="reference internal" href="functions.html#zip" title="zip"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip()</span></code></a> to add sequence numbers.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">count</span><span class="p">(</span><span class="n">start</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">step</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
    <span class="c1"># count(10) → 10 11 12 13 14 ...</span>
    <span class="c1"># count(2.5, 0.5) → 2.5 3.0 3.5 ...</span>
    <span class="n">n</span> <span class="o">=</span> <span class="n">start</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">n</span>
        <span class="n">n</span> <span class="o">+=</span> <span class="n">step</span>
</pre></div>
</div>
<p>When counting with floating point numbers, better accuracy can sometimes be
achieved by substituting multiplicative code such as: <code class="docutils literal notranslate"><span class="pre">(start</span> <span class="pre">+</span> <span class="pre">step</span> <span class="pre">*</span> <span class="pre">i</span>
<span class="pre">for</span> <span class="pre">i</span> <span class="pre">in</span> <span class="pre">count())</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added <em>step</em> argument and allowed non-integer arguments.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.cycle">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">cycle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.cycle" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator returning elements from the iterable and saving a copy of each.
When the iterable is exhausted, return elements from the saved copy.  Repeats
indefinitely.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">cycle</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># cycle(&#39;ABCD&#39;) → A B C D A B C D A B C D ...</span>
    <span class="n">saved</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">element</span>
        <span class="n">saved</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">element</span><span class="p">)</span>
    <span class="k">while</span> <span class="n">saved</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">saved</span><span class="p">:</span>
              <span class="k">yield</span> <span class="n">element</span>
</pre></div>
</div>
<p>Note, this member of the toolkit may require significant auxiliary storage
(depending on the length of the iterable).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.dropwhile">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">dropwhile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">predicate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.dropwhile" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that drops elements from the iterable as long as the predicate
is true; afterwards, returns every element.  Note, the iterator does not produce
<em>any</em> output until the predicate first becomes false, so it may have a lengthy
start-up time.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">dropwhile</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># dropwhile(lambda x: x&lt;5, [1,4,6,4,1]) → 6 4 1</span>
    <span class="n">iterable</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">predicate</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
            <span class="k">yield</span> <span class="n">x</span>
            <span class="k">break</span>
    <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">x</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.filterfalse">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">filterfalse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">predicate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.filterfalse" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that filters elements from iterable returning only those for
which the predicate is false. If <em>predicate</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, return the items
that are false. Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">filterfalse</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># filterfalse(lambda x: x%2, range(10)) → 0 2 4 6 8</span>
    <span class="k">if</span> <span class="n">predicate</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">predicate</span> <span class="o">=</span> <span class="nb">bool</span>
    <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">predicate</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
            <span class="k">yield</span> <span class="n">x</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.groupby">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">groupby</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.groupby" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns consecutive keys and groups from the <em>iterable</em>.
The <em>key</em> is a function computing a key value for each element.  If not
specified or is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <em>key</em> defaults to an identity function and returns
the element unchanged.  Generally, the iterable needs to already be sorted on
the same key function.</p>
<p>The operation of <a class="reference internal" href="#itertools.groupby" title="itertools.groupby"><code class="xref py py-func docutils literal notranslate"><span class="pre">groupby()</span></code></a> is similar to the <code class="docutils literal notranslate"><span class="pre">uniq</span></code> filter in Unix.  It
generates a break or new group every time the value of the key function changes
(which is why it is usually necessary to have sorted the data using the same key
function).  That behavior differs from SQL’s GROUP BY which aggregates common
elements regardless of their input order.</p>
<p>The returned group is itself an iterator that shares the underlying iterable
with <a class="reference internal" href="#itertools.groupby" title="itertools.groupby"><code class="xref py py-func docutils literal notranslate"><span class="pre">groupby()</span></code></a>.  Because the source is shared, when the <a class="reference internal" href="#itertools.groupby" title="itertools.groupby"><code class="xref py py-func docutils literal notranslate"><span class="pre">groupby()</span></code></a>
object is advanced, the previous group is no longer visible.  So, if that data
is needed later, it should be stored as a list:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">groups</span> <span class="o">=</span> <span class="p">[]</span>
<span class="n">uniquekeys</span> <span class="o">=</span> <span class="p">[]</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="n">keyfunc</span><span class="p">)</span>
<span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">g</span> <span class="ow">in</span> <span class="n">groupby</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">keyfunc</span><span class="p">):</span>
    <span class="n">groups</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">g</span><span class="p">))</span>      <span class="c1"># Store group iterator as a list</span>
    <span class="n">uniquekeys</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">k</span><span class="p">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#itertools.groupby" title="itertools.groupby"><code class="xref py py-func docutils literal notranslate"><span class="pre">groupby()</span></code></a> is roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">groupby</span><span class="p">:</span>
    <span class="c1"># [k for k, g in groupby(&#39;AAAABBBCCDAABBB&#39;)] → A B C D A B</span>
    <span class="c1"># [list(g) for k, g in groupby(&#39;AAAABBBCCD&#39;)] → AAAA BBB CC D</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">key</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">key</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">keyfunc</span> <span class="o">=</span> <span class="n">key</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">tgtkey</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>

    <span class="k">def</span> <span class="fm">__iter__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="k">def</span> <span class="fm">__next__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">id</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
        <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">tgtkey</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">it</span><span class="p">)</span>    <span class="c1"># Exit on StopIteration</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">keyfunc</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">tgtkey</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span>
        <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">currkey</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_grouper</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tgtkey</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">id</span><span class="p">))</span>

    <span class="k">def</span> <span class="nf">_grouper</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tgtkey</span><span class="p">,</span> <span class="nb">id</span><span class="p">):</span>
        <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">id</span> <span class="ow">is</span> <span class="nb">id</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span> <span class="o">==</span> <span class="n">tgtkey</span><span class="p">:</span>
            <span class="k">yield</span> <span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">it</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
                <span class="k">return</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">currkey</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">keyfunc</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">currvalue</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.islice">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">islice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.islice" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">islice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stop</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">step</span></span></em><span class="optional">]</span><span class="sig-paren">)</span></dt>
<dd><p>Make an iterator that returns selected elements from the iterable. If <em>start</em> is
non-zero, then elements from the iterable are skipped until start is reached.
Afterward, elements are returned consecutively unless <em>step</em> is set higher than
one which results in items being skipped.  If <em>stop</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then iteration
continues until the iterator is exhausted, if at all; otherwise, it stops at the
specified position.</p>
<p>If <em>start</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then iteration starts at zero. If <em>step</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
then the step defaults to one.</p>
<p>Unlike regular slicing, <a class="reference internal" href="#itertools.islice" title="itertools.islice"><code class="xref py py-func docutils literal notranslate"><span class="pre">islice()</span></code></a> does not support negative values for
<em>start</em>, <em>stop</em>, or <em>step</em>.  Can be used to extract related fields from
data where the internal structure has been flattened (for example, a
multi-line report may list a name field on every third line).</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">islice</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span>
    <span class="c1"># islice(&#39;ABCDEFG&#39;, 2) → A B</span>
    <span class="c1"># islice(&#39;ABCDEFG&#39;, 2, 4) → C D</span>
    <span class="c1"># islice(&#39;ABCDEFG&#39;, 2, None) → C D E F G</span>
    <span class="c1"># islice(&#39;ABCDEFG&#39;, 0, None, 2) → A C E G</span>
    <span class="n">s</span> <span class="o">=</span> <span class="nb">slice</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">)</span>
    <span class="n">start</span><span class="p">,</span> <span class="n">stop</span><span class="p">,</span> <span class="n">step</span> <span class="o">=</span> <span class="n">s</span><span class="o">.</span><span class="n">start</span> <span class="ow">or</span> <span class="mi">0</span><span class="p">,</span> <span class="n">s</span><span class="o">.</span><span class="n">stop</span> <span class="ow">or</span> <span class="n">sys</span><span class="o">.</span><span class="n">maxsize</span><span class="p">,</span> <span class="n">s</span><span class="o">.</span><span class="n">step</span> <span class="ow">or</span> <span class="mi">1</span>
    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">start</span><span class="p">,</span> <span class="n">stop</span><span class="p">,</span> <span class="n">step</span><span class="p">))</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">nexti</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
        <span class="c1"># Consume *iterable* up to the *start* position.</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">start</span><span class="p">),</span> <span class="n">iterable</span><span class="p">):</span>
            <span class="k">pass</span>
        <span class="k">return</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">i</span> <span class="o">==</span> <span class="n">nexti</span><span class="p">:</span>
                <span class="k">yield</span> <span class="n">element</span>
                <span class="n">nexti</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
        <span class="c1"># Consume to *stop*.</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">i</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="n">stop</span><span class="p">),</span> <span class="n">iterable</span><span class="p">):</span>
            <span class="k">pass</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.pairwise">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">pairwise</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.pairwise" title="Link to this definition">¶</a></dt>
<dd><p>Return successive overlapping pairs taken from the input <em>iterable</em>.</p>
<p>The number of 2-tuples in the output iterator will be one fewer than the
number of inputs.  It will be empty if the input iterable has fewer than
two values.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">pairwise</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># pairwise(&#39;ABCDEFG&#39;) → AB BC CD DE EF FG</span>
    <span class="n">iterator</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">a</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">iterator</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="n">iterator</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">a</span><span class="p">,</span> <span class="n">b</span>
        <span class="n">a</span> <span class="o">=</span> <span class="n">b</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.permutations">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">permutations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">r</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.permutations" title="Link to this definition">¶</a></dt>
<dd><p>Return successive <em>r</em> length permutations of elements in the <em>iterable</em>.</p>
<p>If <em>r</em> is not specified or is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then <em>r</em> defaults to the length
of the <em>iterable</em> and all possible full-length permutations
are generated.</p>
<p>The permutation tuples are emitted in lexicographic order according to
the order of the input <em>iterable</em>. So, if the input <em>iterable</em> is sorted,
the output tuples will be produced in sorted order.</p>
<p>Elements are treated as unique based on their position, not on their
value.  So if the input elements are unique, there will be no repeated
values within a permutation.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">permutations</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="c1"># permutations(&#39;ABCD&#39;, 2) → AB AC AD BA BC BD CA CB CD DA DB DC</span>
    <span class="c1"># permutations(range(3)) → 012 021 102 120 201 210</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="n">r</span> <span class="o">=</span> <span class="n">n</span> <span class="k">if</span> <span class="n">r</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">r</span>
    <span class="k">if</span> <span class="n">r</span> <span class="o">&gt;</span> <span class="n">n</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="n">indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">))</span>
    <span class="n">cycles</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">n</span><span class="o">-</span><span class="n">r</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">))</span>
    <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">[:</span><span class="n">r</span><span class="p">])</span>
    <span class="k">while</span> <span class="n">n</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">r</span><span class="p">)):</span>
            <span class="n">cycles</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">-=</span> <span class="mi">1</span>
            <span class="k">if</span> <span class="n">cycles</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">:]</span> <span class="o">=</span> <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">:]</span> <span class="o">+</span> <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">:</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">]</span>
                <span class="n">cycles</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">n</span> <span class="o">-</span> <span class="n">i</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">j</span> <span class="o">=</span> <span class="n">cycles</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
                <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">],</span> <span class="n">indices</span><span class="p">[</span><span class="o">-</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="n">indices</span><span class="p">[</span><span class="o">-</span><span class="n">j</span><span class="p">],</span> <span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
                <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">[:</span><span class="n">r</span><span class="p">])</span>
                <span class="k">break</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span>
</pre></div>
</div>
<p>The code for <a class="reference internal" href="#itertools.permutations" title="itertools.permutations"><code class="xref py py-func docutils literal notranslate"><span class="pre">permutations()</span></code></a> can be also expressed as a subsequence of
<a class="reference internal" href="#itertools.product" title="itertools.product"><code class="xref py py-func docutils literal notranslate"><span class="pre">product()</span></code></a>, filtered to exclude entries with repeated elements (those
from the same position in the input pool):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">permutations</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">r</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="n">pool</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span>
    <span class="n">r</span> <span class="o">=</span> <span class="n">n</span> <span class="k">if</span> <span class="n">r</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">r</span>
    <span class="k">for</span> <span class="n">indices</span> <span class="ow">in</span> <span class="n">product</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">),</span> <span class="n">repeat</span><span class="o">=</span><span class="n">r</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">indices</span><span class="p">))</span> <span class="o">==</span> <span class="n">r</span><span class="p">:</span>
            <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">)</span>
</pre></div>
</div>
<p>The number of items returned is <code class="docutils literal notranslate"><span class="pre">n!</span> <span class="pre">/</span> <span class="pre">(n-r)!</span></code> when <code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">&lt;=</span> <span class="pre">r</span> <span class="pre">&lt;=</span> <span class="pre">n</span></code>
or zero when <code class="docutils literal notranslate"><span class="pre">r</span> <span class="pre">&gt;</span> <span class="pre">n</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.product">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">product</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">iterables</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">repeat</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.product" title="Link to this definition">¶</a></dt>
<dd><p>Cartesian product of input iterables.</p>
<p>Roughly equivalent to nested for-loops in a generator expression. For example,
<code class="docutils literal notranslate"><span class="pre">product(A,</span> <span class="pre">B)</span></code> returns the same as <code class="docutils literal notranslate"><span class="pre">((x,y)</span> <span class="pre">for</span> <span class="pre">x</span> <span class="pre">in</span> <span class="pre">A</span> <span class="pre">for</span> <span class="pre">y</span> <span class="pre">in</span> <span class="pre">B)</span></code>.</p>
<p>The nested loops cycle like an odometer with the rightmost element advancing
on every iteration.  This pattern creates a lexicographic ordering so that if
the input’s iterables are sorted, the product tuples are emitted in sorted
order.</p>
<p>To compute the product of an iterable with itself, specify the number of
repetitions with the optional <em>repeat</em> keyword argument.  For example,
<code class="docutils literal notranslate"><span class="pre">product(A,</span> <span class="pre">repeat=4)</span></code> means the same as <code class="docutils literal notranslate"><span class="pre">product(A,</span> <span class="pre">A,</span> <span class="pre">A,</span> <span class="pre">A)</span></code>.</p>
<p>This function is roughly equivalent to the following code, except that the
actual implementation does not build up intermediate results in memory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">product</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="n">repeat</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
    <span class="c1"># product(&#39;ABCD&#39;, &#39;xy&#39;) → Ax Ay Bx By Cx Cy Dx Dy</span>
    <span class="c1"># product(range(2), repeat=3) → 000 001 010 011 100 101 110 111</span>
    <span class="n">pools</span> <span class="o">=</span> <span class="p">[</span><span class="nb">tuple</span><span class="p">(</span><span class="n">pool</span><span class="p">)</span> <span class="k">for</span> <span class="n">pool</span> <span class="ow">in</span> <span class="n">args</span><span class="p">]</span> <span class="o">*</span> <span class="n">repeat</span>
    <span class="n">result</span> <span class="o">=</span> <span class="p">[[]]</span>
    <span class="k">for</span> <span class="n">pool</span> <span class="ow">in</span> <span class="n">pools</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="p">[</span><span class="n">x</span><span class="o">+</span><span class="p">[</span><span class="n">y</span><span class="p">]</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">result</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="n">pool</span><span class="p">]</span>
    <span class="k">for</span> <span class="n">prod</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
        <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">prod</span><span class="p">)</span>
</pre></div>
</div>
<p>Before <a class="reference internal" href="#itertools.product" title="itertools.product"><code class="xref py py-func docutils literal notranslate"><span class="pre">product()</span></code></a> runs, it completely consumes the input iterables,
keeping pools of values in memory to generate the products.  Accordingly,
it is only useful with finite inputs.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.repeat">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">repeat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">times</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#itertools.repeat" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns <em>object</em> over and over again. Runs indefinitely
unless the <em>times</em> argument is specified.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">repeat</span><span class="p">(</span><span class="nb">object</span><span class="p">,</span> <span class="n">times</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="c1"># repeat(10, 3) → 10 10 10</span>
    <span class="k">if</span> <span class="n">times</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">yield</span> <span class="nb">object</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">times</span><span class="p">):</span>
            <span class="k">yield</span> <span class="nb">object</span>
</pre></div>
</div>
<p>A common use for <em>repeat</em> is to supply a stream of constant values to <em>map</em>
or <em>zip</em>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="nb">pow</span><span class="p">,</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">),</span> <span class="n">repeat</span><span class="p">(</span><span class="mi">2</span><span class="p">)))</span>
<span class="go">[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.starmap">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">starmap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.starmap" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that computes the function using arguments obtained from
the iterable.  Used instead of <a class="reference internal" href="functions.html#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a> when argument parameters are already
grouped in tuples from a single iterable (when the data has been
“pre-zipped”).</p>
<p>The difference between <a class="reference internal" href="functions.html#map" title="map"><code class="xref py py-func docutils literal notranslate"><span class="pre">map()</span></code></a> and <a class="reference internal" href="#itertools.starmap" title="itertools.starmap"><code class="xref py py-func docutils literal notranslate"><span class="pre">starmap()</span></code></a> parallels the
distinction between <code class="docutils literal notranslate"><span class="pre">function(a,b)</span></code> and <code class="docutils literal notranslate"><span class="pre">function(*c)</span></code>. Roughly
equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">starmap</span><span class="p">(</span><span class="n">function</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># starmap(pow, [(2,5), (3,2), (10,3)]) → 32 9 1000</span>
    <span class="k">for</span> <span class="n">args</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">function</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.takewhile">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">takewhile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">predicate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.takewhile" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that returns elements from the iterable as long as the
predicate is true.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">takewhile</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="c1"># takewhile(lambda x: x&lt;5, [1,4,6,4,1]) → 1 4</span>
    <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">predicate</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
            <span class="k">yield</span> <span class="n">x</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">break</span>
</pre></div>
</div>
<p>Note, the element that first fails the predicate condition is
consumed from the input iterator and there is no way to access it.
This could be an issue if an application wants to further consume the
input iterator after takewhile has been run to exhaustion.  To work
around this problem, consider using <a class="reference external" href="https://more-itertools.readthedocs.io/en/stable/api.html#more_itertools.before_and_after">more-iterools before_and_after()</a>
instead.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.tee">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">tee</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">2</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.tee" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>n</em> independent iterators from a single iterable.</p>
<p>The following Python code helps explain what <em>tee</em> does (although the actual
implementation is more complex and uses only a single underlying
<abbr title="first-in, first-out">FIFO</abbr> queue):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">tee</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="o">=</span><span class="mi">2</span><span class="p">):</span>
    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">deques</span> <span class="o">=</span> <span class="p">[</span><span class="n">collections</span><span class="o">.</span><span class="n">deque</span><span class="p">()</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">)]</span>
    <span class="k">def</span> <span class="nf">gen</span><span class="p">(</span><span class="n">mydeque</span><span class="p">):</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">mydeque</span><span class="p">:</span>             <span class="c1"># when the local deque is empty</span>
                <span class="k">try</span><span class="p">:</span>
                    <span class="n">newval</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>   <span class="c1"># fetch a new value and</span>
                <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
                    <span class="k">return</span>
                <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">deques</span><span class="p">:</span>        <span class="c1"># load it to all the deques</span>
                    <span class="n">d</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">newval</span><span class="p">)</span>
            <span class="k">yield</span> <span class="n">mydeque</span><span class="o">.</span><span class="n">popleft</span><span class="p">()</span>
    <span class="k">return</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">gen</span><span class="p">(</span><span class="n">d</span><span class="p">)</span> <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">deques</span><span class="p">)</span>
</pre></div>
</div>
<p>Once a <a class="reference internal" href="#itertools.tee" title="itertools.tee"><code class="xref py py-func docutils literal notranslate"><span class="pre">tee()</span></code></a> has been created, the original <em>iterable</em> should not be
used anywhere else; otherwise, the <em>iterable</em> could get advanced without
the tee objects being informed.</p>
<p><code class="docutils literal notranslate"><span class="pre">tee</span></code> iterators are not threadsafe. A <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> may be
raised when simultaneously using iterators returned by the same <a class="reference internal" href="#itertools.tee" title="itertools.tee"><code class="xref py py-func docutils literal notranslate"><span class="pre">tee()</span></code></a>
call, even if the original <em>iterable</em> is threadsafe.</p>
<p>This itertool may require significant auxiliary storage (depending on how
much temporary data needs to be stored). In general, if one iterator uses
most or all of the data before another iterator starts, it is faster to use
<a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-func docutils literal notranslate"><span class="pre">list()</span></code></a> instead of <a class="reference internal" href="#itertools.tee" title="itertools.tee"><code class="xref py py-func docutils literal notranslate"><span class="pre">tee()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="itertools.zip_longest">
<span class="sig-prename descclassname"><span class="pre">itertools.</span></span><span class="sig-name descname"><span class="pre">zip_longest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">iterables</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fillvalue</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#itertools.zip_longest" title="Link to this definition">¶</a></dt>
<dd><p>Make an iterator that aggregates elements from each of the iterables. If the
iterables are of uneven length, missing values are filled-in with <em>fillvalue</em>.
Iteration continues until the longest iterable is exhausted.  Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">zip_longest</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="n">fillvalue</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="c1"># zip_longest(&#39;ABCD&#39;, &#39;xy&#39;, fillvalue=&#39;-&#39;) → Ax By C- D-</span>
    <span class="n">iterators</span> <span class="o">=</span> <span class="p">[</span><span class="nb">iter</span><span class="p">(</span><span class="n">it</span><span class="p">)</span> <span class="k">for</span> <span class="n">it</span> <span class="ow">in</span> <span class="n">args</span><span class="p">]</span>
    <span class="n">num_active</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">iterators</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">num_active</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">values</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">it</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">iterators</span><span class="p">):</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">value</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
                <span class="n">num_active</span> <span class="o">-=</span> <span class="mi">1</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">num_active</span><span class="p">:</span>
                    <span class="k">return</span>
                <span class="n">iterators</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">repeat</span><span class="p">(</span><span class="n">fillvalue</span><span class="p">)</span>
                <span class="n">value</span> <span class="o">=</span> <span class="n">fillvalue</span>
            <span class="n">values</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
        <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">values</span><span class="p">)</span>
</pre></div>
</div>
<p>If one of the iterables is potentially infinite, then the <a class="reference internal" href="#itertools.zip_longest" title="itertools.zip_longest"><code class="xref py py-func docutils literal notranslate"><span class="pre">zip_longest()</span></code></a>
function should be wrapped with something that limits the number of calls
(for example <a class="reference internal" href="#itertools.islice" title="itertools.islice"><code class="xref py py-func docutils literal notranslate"><span class="pre">islice()</span></code></a> or <a class="reference internal" href="#itertools.takewhile" title="itertools.takewhile"><code class="xref py py-func docutils literal notranslate"><span class="pre">takewhile()</span></code></a>).  If not specified,
<em>fillvalue</em> defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

</section>
<section id="itertools-recipes">
<span id="id1"></span><h2>Itertools Recipes<a class="headerlink" href="#itertools-recipes" title="Link to this heading">¶</a></h2>
<p>This section shows recipes for creating an extended toolset using the existing
itertools as building blocks.</p>
<p>The primary purpose of the itertools recipes is educational.  The recipes show
various ways of thinking about individual tools — for example, that
<code class="docutils literal notranslate"><span class="pre">chain.from_iterable</span></code> is related to the concept of flattening.  The recipes
also give ideas about ways that the tools can be combined — for example, how
<code class="docutils literal notranslate"><span class="pre">starmap()</span></code> and <code class="docutils literal notranslate"><span class="pre">repeat()</span></code> can work together.  The recipes also show patterns
for using itertools with the <a class="reference internal" href="operator.html#module-operator" title="operator: Functions corresponding to the standard operators."><code class="xref py py-mod docutils literal notranslate"><span class="pre">operator</span></code></a> and <a class="reference internal" href="collections.html#module-collections" title="collections: Container datatypes"><code class="xref py py-mod docutils literal notranslate"><span class="pre">collections</span></code></a> modules as
well as with the built-in itertools such as <code class="docutils literal notranslate"><span class="pre">map()</span></code>, <code class="docutils literal notranslate"><span class="pre">filter()</span></code>,
<code class="docutils literal notranslate"><span class="pre">reversed()</span></code>, and <code class="docutils literal notranslate"><span class="pre">enumerate()</span></code>.</p>
<p>A secondary purpose of the recipes is to serve as an incubator.  The
<code class="docutils literal notranslate"><span class="pre">accumulate()</span></code>, <code class="docutils literal notranslate"><span class="pre">compress()</span></code>, and <code class="docutils literal notranslate"><span class="pre">pairwise()</span></code> itertools started out as
recipes.  Currently, the <code class="docutils literal notranslate"><span class="pre">sliding_window()</span></code>, <code class="docutils literal notranslate"><span class="pre">iter_index()</span></code>, and <code class="docutils literal notranslate"><span class="pre">sieve()</span></code>
recipes are being tested to see whether they prove their worth.</p>
<p>Substantially all of these recipes and many, many others can be installed from
the <a class="reference external" href="https://pypi.org/project/more-itertools/">more-itertools project</a> found
on the Python Package Index:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pip</span> <span class="n">install</span> <span class="n">more</span><span class="o">-</span><span class="n">itertools</span>
</pre></div>
</div>
<p>Many of the recipes offer the same high performance as the underlying toolset.
Superior memory performance is kept by processing elements one at a time rather
than bringing the whole iterable into memory all at once. Code volume is kept
small by linking the tools together in a <a class="reference external" href="https://www.cs.kent.ac.uk/people/staff/dat/miranda/whyfp90.pdf">functional style</a>.  High speed
is retained by preferring “vectorized” building blocks over the use of for-loops
and <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generators</span></a> which incur interpreter overhead.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">collections</span>
<span class="kn">import</span> <span class="nn">functools</span>
<span class="kn">import</span> <span class="nn">math</span>
<span class="kn">import</span> <span class="nn">operator</span>
<span class="kn">import</span> <span class="nn">random</span>

<span class="k">def</span> <span class="nf">take</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="s2">&quot;Return first n items of the iterable as a list.&quot;</span>
    <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">prepend</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="s2">&quot;Prepend a single value in front of an iterable.&quot;</span>
    <span class="c1"># prepend(1, [2, 3, 4]) → 1 2 3 4</span>
    <span class="k">return</span> <span class="n">chain</span><span class="p">([</span><span class="n">value</span><span class="p">],</span> <span class="n">iterable</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">tabulate</span><span class="p">(</span><span class="n">function</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
    <span class="s2">&quot;Return function(0), function(1), ...&quot;</span>
    <span class="k">return</span> <span class="nb">map</span><span class="p">(</span><span class="n">function</span><span class="p">,</span> <span class="n">count</span><span class="p">(</span><span class="n">start</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">repeatfunc</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">times</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Repeat calls to func with specified arguments.</span>

<span class="sd">    Example:  repeatfunc(random.random)</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">times</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">starmap</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="n">args</span><span class="p">))</span>
    <span class="k">return</span> <span class="n">starmap</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="n">args</span><span class="p">,</span> <span class="n">times</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">flatten</span><span class="p">(</span><span class="n">list_of_lists</span><span class="p">):</span>
    <span class="s2">&quot;Flatten one level of nesting.&quot;</span>
    <span class="k">return</span> <span class="n">chain</span><span class="o">.</span><span class="n">from_iterable</span><span class="p">(</span><span class="n">list_of_lists</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">ncycles</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">):</span>
    <span class="s2">&quot;Returns the sequence elements n times.&quot;</span>
    <span class="k">return</span> <span class="n">chain</span><span class="o">.</span><span class="n">from_iterable</span><span class="p">(</span><span class="n">repeat</span><span class="p">(</span><span class="nb">tuple</span><span class="p">(</span><span class="n">iterable</span><span class="p">),</span> <span class="n">n</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">tail</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
    <span class="s2">&quot;Return an iterator over the last n items.&quot;</span>
    <span class="c1"># tail(3, &#39;ABCDEFG&#39;) → E F G</span>
    <span class="k">return</span> <span class="nb">iter</span><span class="p">(</span><span class="n">collections</span><span class="o">.</span><span class="n">deque</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">maxlen</span><span class="o">=</span><span class="n">n</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">consume</span><span class="p">(</span><span class="n">iterator</span><span class="p">,</span> <span class="n">n</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Advance the iterator n-steps ahead. If n is None, consume entirely.&quot;</span>
    <span class="c1"># Use functions that consume iterators at C speed.</span>
    <span class="k">if</span> <span class="n">n</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="c1"># feed the entire iterator into a zero-length deque</span>
        <span class="n">collections</span><span class="o">.</span><span class="n">deque</span><span class="p">(</span><span class="n">iterator</span><span class="p">,</span> <span class="n">maxlen</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># advance to the empty slice starting at position n</span>
        <span class="nb">next</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">iterator</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="n">n</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">nth</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Returns the nth item or a default value.&quot;</span>
    <span class="k">return</span> <span class="nb">next</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="kc">None</span><span class="p">),</span> <span class="n">default</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">quantify</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">predicate</span><span class="o">=</span><span class="nb">bool</span><span class="p">):</span>
    <span class="s2">&quot;Given a predicate that returns True or False, count the True results.&quot;</span>
    <span class="k">return</span> <span class="nb">sum</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">first_true</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">predicate</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Returns the first true value or the *default* if there is no true value.&quot;</span>
    <span class="c1"># first_true([a,b,c], x) → a or b or c or x</span>
    <span class="c1"># first_true([a,b], x, f) → a if f(a) else b if f(b) else x</span>
    <span class="k">return</span> <span class="nb">next</span><span class="p">(</span><span class="nb">filter</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">),</span> <span class="n">default</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">all_equal</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Returns True if all the elements are equal to each other.&quot;</span>
    <span class="c1"># all_equal(&#39;4٤໔４৪&#39;, key=int) → True</span>
    <span class="k">return</span> <span class="nb">len</span><span class="p">(</span><span class="n">take</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="n">groupby</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="p">)))</span> <span class="o">&lt;=</span> <span class="mi">1</span>

<span class="k">def</span> <span class="nf">unique_justseen</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;List unique elements, preserving order. Remember only the element just seen.&quot;</span>
    <span class="c1"># unique_justseen(&#39;AAAABBBCCDAABBB&#39;) → A B C D A B</span>
    <span class="c1"># unique_justseen(&#39;ABBcCAD&#39;, str.casefold) → A B c A D</span>
    <span class="k">if</span> <span class="n">key</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">map</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">itemgetter</span><span class="p">(</span><span class="mi">0</span><span class="p">),</span> <span class="n">groupby</span><span class="p">(</span><span class="n">iterable</span><span class="p">))</span>
    <span class="k">return</span> <span class="nb">map</span><span class="p">(</span><span class="nb">next</span><span class="p">,</span> <span class="nb">map</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">itemgetter</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="n">groupby</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="p">)))</span>

<span class="k">def</span> <span class="nf">unique_everseen</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;List unique elements, preserving order. Remember all elements ever seen.&quot;</span>
    <span class="c1"># unique_everseen(&#39;AAAABBBCCDAABBB&#39;) → A B C D</span>
    <span class="c1"># unique_everseen(&#39;ABBcCAD&#39;, str.casefold) → A B c D</span>
    <span class="n">seen</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">key</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">filterfalse</span><span class="p">(</span><span class="n">seen</span><span class="o">.</span><span class="fm">__contains__</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
            <span class="n">seen</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">element</span><span class="p">)</span>
            <span class="k">yield</span> <span class="n">element</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="n">iterable</span><span class="p">:</span>
            <span class="n">k</span> <span class="o">=</span> <span class="n">key</span><span class="p">(</span><span class="n">element</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">k</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">seen</span><span class="p">:</span>
                <span class="n">seen</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">k</span><span class="p">)</span>
                <span class="k">yield</span> <span class="n">element</span>

<span class="k">def</span> <span class="nf">sliding_window</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">):</span>
    <span class="s2">&quot;Collect data into overlapping fixed-length chunks or blocks.&quot;</span>
    <span class="c1"># sliding_window(&#39;ABCDEFG&#39;, 4) → ABCD BCDE CDEF DEFG</span>
    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="n">window</span> <span class="o">=</span> <span class="n">collections</span><span class="o">.</span><span class="n">deque</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">it</span><span class="p">,</span> <span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">maxlen</span><span class="o">=</span><span class="n">n</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">it</span><span class="p">:</span>
        <span class="n">window</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
        <span class="k">yield</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">window</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">grouper</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="o">*</span><span class="p">,</span> <span class="n">incomplete</span><span class="o">=</span><span class="s1">&#39;fill&#39;</span><span class="p">,</span> <span class="n">fillvalue</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Collect data into non-overlapping fixed-length chunks or blocks.&quot;</span>
    <span class="c1"># grouper(&#39;ABCDEFG&#39;, 3, fillvalue=&#39;x&#39;) → ABC DEF Gxx</span>
    <span class="c1"># grouper(&#39;ABCDEFG&#39;, 3, incomplete=&#39;strict&#39;) → ABC DEF ValueError</span>
    <span class="c1"># grouper(&#39;ABCDEFG&#39;, 3, incomplete=&#39;ignore&#39;) → ABC DEF</span>
    <span class="n">iterators</span> <span class="o">=</span> <span class="p">[</span><span class="nb">iter</span><span class="p">(</span><span class="n">iterable</span><span class="p">)]</span> <span class="o">*</span> <span class="n">n</span>
    <span class="k">match</span> <span class="n">incomplete</span><span class="p">:</span>
        <span class="k">case</span> <span class="s1">&#39;fill&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">zip_longest</span><span class="p">(</span><span class="o">*</span><span class="n">iterators</span><span class="p">,</span> <span class="n">fillvalue</span><span class="o">=</span><span class="n">fillvalue</span><span class="p">)</span>
        <span class="k">case</span> <span class="s1">&#39;strict&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">iterators</span><span class="p">,</span> <span class="n">strict</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">case</span> <span class="s1">&#39;ignore&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">iterators</span><span class="p">)</span>
        <span class="k">case</span><span class="w"> </span><span class="k">_</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Expected fill, strict, or ignore&#39;</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">roundrobin</span><span class="p">(</span><span class="o">*</span><span class="n">iterables</span><span class="p">):</span>
    <span class="s2">&quot;Visit input iterables in a cycle until each is exhausted.&quot;</span>
    <span class="c1"># roundrobin(&#39;ABC&#39;, &#39;D&#39;, &#39;EF&#39;) → A D E B F C</span>
    <span class="c1"># Algorithm credited to George Sakkis</span>
    <span class="n">iterators</span> <span class="o">=</span> <span class="nb">map</span><span class="p">(</span><span class="nb">iter</span><span class="p">,</span> <span class="n">iterables</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">num_active</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">iterables</span><span class="p">),</span> <span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">):</span>
        <span class="n">iterators</span> <span class="o">=</span> <span class="n">cycle</span><span class="p">(</span><span class="n">islice</span><span class="p">(</span><span class="n">iterators</span><span class="p">,</span> <span class="n">num_active</span><span class="p">))</span>
        <span class="k">yield from</span> <span class="nb">map</span><span class="p">(</span><span class="nb">next</span><span class="p">,</span> <span class="n">iterators</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">partition</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">iterable</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Partition entries into false entries and true entries.</span>

<span class="sd">    If *predicate* is slow, consider wrapping it with functools.lru_cache().</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># partition(is_odd, range(10)) → 0 2 4 6 8   and  1 3 5 7 9</span>
    <span class="n">t1</span><span class="p">,</span> <span class="n">t2</span> <span class="o">=</span> <span class="n">tee</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">filterfalse</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">t1</span><span class="p">),</span> <span class="nb">filter</span><span class="p">(</span><span class="n">predicate</span><span class="p">,</span> <span class="n">t2</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">subslices</span><span class="p">(</span><span class="n">seq</span><span class="p">):</span>
    <span class="s2">&quot;Return all contiguous non-empty subslices of a sequence.&quot;</span>
    <span class="c1"># subslices(&#39;ABCD&#39;) → A AB ABC ABCD B BC BCD C CD D</span>
    <span class="n">slices</span> <span class="o">=</span> <span class="n">starmap</span><span class="p">(</span><span class="nb">slice</span><span class="p">,</span> <span class="n">combinations</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">seq</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span><span class="p">),</span> <span class="mi">2</span><span class="p">))</span>
    <span class="k">return</span> <span class="nb">map</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">getitem</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="n">seq</span><span class="p">),</span> <span class="n">slices</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">iter_index</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">stop</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="s2">&quot;Return indices where a value occurs in a sequence or iterable.&quot;</span>
    <span class="c1"># iter_index(&#39;AABCADEAF&#39;, &#39;A&#39;) → 0 1 4 7</span>
    <span class="n">seq_index</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="s1">&#39;index&#39;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">seq_index</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="c1"># Path for general iterables</span>
        <span class="n">it</span> <span class="o">=</span> <span class="n">islice</span><span class="p">(</span><span class="n">iterable</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">stop</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">it</span><span class="p">,</span> <span class="n">start</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">element</span> <span class="ow">is</span> <span class="n">value</span> <span class="ow">or</span> <span class="n">element</span> <span class="o">==</span> <span class="n">value</span><span class="p">:</span>
                <span class="k">yield</span> <span class="n">i</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Path for sequences with an index() method</span>
        <span class="n">stop</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span> <span class="k">if</span> <span class="n">stop</span> <span class="ow">is</span> <span class="kc">None</span> <span class="k">else</span> <span class="n">stop</span>
        <span class="n">i</span> <span class="o">=</span> <span class="n">start</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
                <span class="k">yield</span> <span class="p">(</span><span class="n">i</span> <span class="o">:=</span> <span class="n">seq_index</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">stop</span><span class="p">))</span>
                <span class="n">i</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
            <span class="k">pass</span>

<span class="k">def</span> <span class="nf">iter_except</span><span class="p">(</span><span class="n">func</span><span class="p">,</span> <span class="n">exception</span><span class="p">,</span> <span class="n">first</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot; Call a function repeatedly until an exception is raised.</span>

<span class="sd">    Converts a call-until-exception interface to an iterator interface.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># iter_except(d.popitem, KeyError) → non-blocking dictionary iterator</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">first</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">first</span><span class="p">()</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">func</span><span class="p">()</span>
    <span class="k">except</span> <span class="n">exception</span><span class="p">:</span>
        <span class="k">pass</span>
</pre></div>
</div>
<p>The following recipes have a more mathematical flavor:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">powerset</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="s2">&quot;powerset([1,2,3]) → () (1,) (2,) (3,) (1,2) (1,3) (2,3) (1,2,3)&quot;</span>
    <span class="n">s</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">chain</span><span class="o">.</span><span class="n">from_iterable</span><span class="p">(</span><span class="n">combinations</span><span class="p">(</span><span class="n">s</span><span class="p">,</span> <span class="n">r</span><span class="p">)</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">s</span><span class="p">)</span><span class="o">+</span><span class="mi">1</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">sum_of_squares</span><span class="p">(</span><span class="n">iterable</span><span class="p">):</span>
    <span class="s2">&quot;Add up the squares of the input values.&quot;</span>
    <span class="c1"># sum_of_squares([10, 20, 30]) → 1400</span>
    <span class="k">return</span> <span class="n">math</span><span class="o">.</span><span class="n">sumprod</span><span class="p">(</span><span class="o">*</span><span class="n">tee</span><span class="p">(</span><span class="n">iterable</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">reshape</span><span class="p">(</span><span class="n">matrix</span><span class="p">,</span> <span class="n">cols</span><span class="p">):</span>
    <span class="s2">&quot;Reshape a 2-D matrix to have a given number of columns.&quot;</span>
    <span class="c1"># reshape([(0, 1), (2, 3), (4, 5)], 3) →  (0, 1, 2), (3, 4, 5)</span>
    <span class="k">return</span> <span class="n">batched</span><span class="p">(</span><span class="n">chain</span><span class="o">.</span><span class="n">from_iterable</span><span class="p">(</span><span class="n">matrix</span><span class="p">),</span> <span class="n">cols</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">transpose</span><span class="p">(</span><span class="n">matrix</span><span class="p">):</span>
    <span class="s2">&quot;Swap the rows and columns of a 2-D matrix.&quot;</span>
    <span class="c1"># transpose([(1, 2, 3), (11, 22, 33)]) → (1, 11) (2, 22) (3, 33)</span>
    <span class="k">return</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">matrix</span><span class="p">,</span> <span class="n">strict</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">matmul</span><span class="p">(</span><span class="n">m1</span><span class="p">,</span> <span class="n">m2</span><span class="p">):</span>
    <span class="s2">&quot;Multiply two matrices.&quot;</span>
    <span class="c1"># matmul([(7, 5), (3, 5)], [(2, 5), (7, 9)]) → (49, 80), (41, 60)</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">m2</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
    <span class="k">return</span> <span class="n">batched</span><span class="p">(</span><span class="n">starmap</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">sumprod</span><span class="p">,</span> <span class="n">product</span><span class="p">(</span><span class="n">m1</span><span class="p">,</span> <span class="n">transpose</span><span class="p">(</span><span class="n">m2</span><span class="p">))),</span> <span class="n">n</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">convolve</span><span class="p">(</span><span class="n">signal</span><span class="p">,</span> <span class="n">kernel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Discrete linear convolution of two iterables.</span>
<span class="sd">    Equivalent to polynomial multiplication.</span>

<span class="sd">    Convolutions are mathematically commutative; however, the inputs are</span>
<span class="sd">    evaluated differently.  The signal is consumed lazily and can be</span>
<span class="sd">    infinite. The kernel is fully consumed before the calculations begin.</span>

<span class="sd">    Article:  https://betterexplained.com/articles/intuitive-convolution/</span>
<span class="sd">    Video:    https://www.youtube.com/watch?v=KuXjwB4LzSA</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># convolve([1, -1, -20], [1, -3]) → 1 -4 -17 60</span>
    <span class="c1"># convolve(data, [0.25, 0.25, 0.25, 0.25]) → Moving average (blur)</span>
    <span class="c1"># convolve(data, [1/2, 0, -1/2]) → 1st derivative estimate</span>
    <span class="c1"># convolve(data, [1, -2, 1]) → 2nd derivative estimate</span>
    <span class="n">kernel</span> <span class="o">=</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">kernel</span><span class="p">)[::</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">kernel</span><span class="p">)</span>
    <span class="n">padded_signal</span> <span class="o">=</span> <span class="n">chain</span><span class="p">(</span><span class="n">repeat</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">signal</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">))</span>
    <span class="n">windowed_signal</span> <span class="o">=</span> <span class="n">sliding_window</span><span class="p">(</span><span class="n">padded_signal</span><span class="p">,</span> <span class="n">n</span><span class="p">)</span>
    <span class="k">return</span> <span class="nb">map</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">sumprod</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="n">kernel</span><span class="p">),</span> <span class="n">windowed_signal</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">polynomial_from_roots</span><span class="p">(</span><span class="n">roots</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Compute a polynomial&#39;s coefficients from its roots.</span>

<span class="sd">       (x - 5) (x + 4) (x - 3)  expands to:   x³ -4x² -17x + 60</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># polynomial_from_roots([5, -4, 3]) → [1, -4, -17, 60]</span>
    <span class="n">factors</span> <span class="o">=</span> <span class="nb">zip</span><span class="p">(</span><span class="n">repeat</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span> <span class="nb">map</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">neg</span><span class="p">,</span> <span class="n">roots</span><span class="p">))</span>
    <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">functools</span><span class="o">.</span><span class="n">reduce</span><span class="p">(</span><span class="n">convolve</span><span class="p">,</span> <span class="n">factors</span><span class="p">,</span> <span class="p">[</span><span class="mi">1</span><span class="p">]))</span>

<span class="k">def</span> <span class="nf">polynomial_eval</span><span class="p">(</span><span class="n">coefficients</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Evaluate a polynomial at a specific value.</span>

<span class="sd">    Computes with better numeric stability than Horner&#39;s method.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Evaluate x³ -4x² -17x + 60 at x = 5</span>
    <span class="c1"># polynomial_eval([1, -4, -17, 60], x=5) → 0</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">coefficients</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">n</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">type</span><span class="p">(</span><span class="n">x</span><span class="p">)(</span><span class="mi">0</span><span class="p">)</span>
    <span class="n">powers</span> <span class="o">=</span> <span class="nb">map</span><span class="p">(</span><span class="nb">pow</span><span class="p">,</span> <span class="n">repeat</span><span class="p">(</span><span class="n">x</span><span class="p">),</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">n</span><span class="p">)))</span>
    <span class="k">return</span> <span class="n">math</span><span class="o">.</span><span class="n">sumprod</span><span class="p">(</span><span class="n">coefficients</span><span class="p">,</span> <span class="n">powers</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">polynomial_derivative</span><span class="p">(</span><span class="n">coefficients</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Compute the first derivative of a polynomial.</span>

<span class="sd">       f(x)  =  x³ -4x² -17x + 60</span>
<span class="sd">       f&#39;(x) = 3x² -8x  -17</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># polynomial_derivative([1, -4, -17, 60]) → [3, -8, -17]</span>
    <span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">coefficients</span><span class="p">)</span>
    <span class="n">powers</span> <span class="o">=</span> <span class="nb">reversed</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">n</span><span class="p">))</span>
    <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">mul</span><span class="p">,</span> <span class="n">coefficients</span><span class="p">,</span> <span class="n">powers</span><span class="p">))</span>

<span class="k">def</span> <span class="nf">sieve</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
    <span class="s2">&quot;Primes less than n.&quot;</span>
    <span class="c1"># sieve(30) → 2 3 5 7 11 13 17 19 23 29</span>
    <span class="k">if</span> <span class="n">n</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
        <span class="k">yield</span> <span class="mi">2</span>
    <span class="n">start</span> <span class="o">=</span> <span class="mi">3</span>
    <span class="n">data</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">((</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">))</span> <span class="o">*</span> <span class="p">(</span><span class="n">n</span> <span class="o">//</span> <span class="mi">2</span><span class="p">)</span>
    <span class="n">limit</span> <span class="o">=</span> <span class="n">math</span><span class="o">.</span><span class="n">isqrt</span><span class="p">(</span><span class="n">n</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
    <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">iter_index</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">limit</span><span class="p">):</span>
        <span class="k">yield from</span> <span class="n">iter_index</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">p</span><span class="o">*</span><span class="n">p</span><span class="p">)</span>
        <span class="n">data</span><span class="p">[</span><span class="n">p</span><span class="o">*</span><span class="n">p</span> <span class="p">:</span> <span class="n">n</span> <span class="p">:</span> <span class="n">p</span><span class="o">+</span><span class="n">p</span><span class="p">]</span> <span class="o">=</span> <span class="nb">bytes</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">p</span><span class="o">*</span><span class="n">p</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="n">p</span><span class="o">+</span><span class="n">p</span><span class="p">)))</span>
        <span class="n">start</span> <span class="o">=</span> <span class="n">p</span><span class="o">*</span><span class="n">p</span>
    <span class="k">yield from</span> <span class="n">iter_index</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">start</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">factor</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
    <span class="s2">&quot;Prime factors of n.&quot;</span>
    <span class="c1"># factor(99) → 3 3 11</span>
    <span class="c1"># factor(1_000_000_000_000_007) → 47 59 360620266859</span>
    <span class="c1"># factor(1_000_000_000_000_403) → 1000000000000403</span>
    <span class="k">for</span> <span class="n">prime</span> <span class="ow">in</span> <span class="n">sieve</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">isqrt</span><span class="p">(</span><span class="n">n</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
        <span class="k">while</span> <span class="ow">not</span> <span class="n">n</span> <span class="o">%</span> <span class="n">prime</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">prime</span>
            <span class="n">n</span> <span class="o">//=</span> <span class="n">prime</span>
            <span class="k">if</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">return</span>
    <span class="k">if</span> <span class="n">n</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
        <span class="k">yield</span> <span class="n">n</span>

<span class="k">def</span> <span class="nf">totient</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
    <span class="s2">&quot;Count of natural numbers up to n that are coprime to n.&quot;</span>
    <span class="c1"># https://mathworld.wolfram.com/TotientFunction.html</span>
    <span class="c1"># totient(12) → 4 because len([1, 5, 7, 11]) == 4</span>
    <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">unique_justseen</span><span class="p">(</span><span class="n">factor</span><span class="p">(</span><span class="n">n</span><span class="p">)):</span>
        <span class="n">n</span> <span class="o">-=</span> <span class="n">n</span> <span class="o">//</span> <span class="n">p</span>
    <span class="k">return</span> <span class="n">n</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">itertools</span></code> — Functions creating iterators for efficient looping</a><ul>
<li><a class="reference internal" href="#itertool-functions">Itertool Functions</a></li>
<li><a class="reference internal" href="#itertools-recipes">Itertools Recipes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="functional.html"
                          title="previous chapter">Functional Programming Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functools.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">functools</span></code> — Higher-order functions and operations on callable objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/itertools.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functools.html" title="functools — Higher-order functions and operations on callable objects"
             >next</a> |</li>
        <li class="right" >
          <a href="functional.html" title="Functional Programming Modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="functional.html" >Functional Programming Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">itertools</span></code> — Functions creating iterators for efficient looping</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>