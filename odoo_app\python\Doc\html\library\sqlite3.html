<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sqlite3 — DB-API 2.0 interface for SQLite databases" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sqlite3.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/sqlite3/ SQLite is a C library that provides a lightweight disk-based database that doesn’t require a separate server process and allows accessing the database using a nonstandard ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/sqlite3/ SQLite is a C library that provides a lightweight disk-based database that doesn’t require a separate server process and allows accessing the database using a nonstandard ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sqlite3 — DB-API 2.0 interface for SQLite databases &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Data Compression and Archiving" href="archiving.html" />
    <link rel="prev" title="dbm — Interfaces to Unix “databases”" href="dbm.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sqlite3.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> — DB-API 2.0 interface for SQLite databases</a><ul>
<li><a class="reference internal" href="#tutorial">Tutorial</a></li>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#module-functions">Module functions</a></li>
<li><a class="reference internal" href="#module-constants">Module constants</a></li>
<li><a class="reference internal" href="#connection-objects">Connection objects</a></li>
<li><a class="reference internal" href="#cursor-objects">Cursor objects</a></li>
<li><a class="reference internal" href="#row-objects">Row objects</a></li>
<li><a class="reference internal" href="#blob-objects">Blob objects</a></li>
<li><a class="reference internal" href="#prepareprotocol-objects">PrepareProtocol objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#sqlite-and-python-types">SQLite and Python types</a></li>
<li><a class="reference internal" href="#default-adapters-and-converters-deprecated">Default adapters and converters (deprecated)</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-line interface</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-guides">How-to guides</a><ul>
<li><a class="reference internal" href="#how-to-use-placeholders-to-bind-values-in-sql-queries">How to use placeholders to bind values in SQL queries</a></li>
<li><a class="reference internal" href="#how-to-adapt-custom-python-types-to-sqlite-values">How to adapt custom Python types to SQLite values</a><ul>
<li><a class="reference internal" href="#how-to-write-adaptable-objects">How to write adaptable objects</a></li>
<li><a class="reference internal" href="#how-to-register-adapter-callables">How to register adapter callables</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-convert-sqlite-values-to-custom-python-types">How to convert SQLite values to custom Python types</a></li>
<li><a class="reference internal" href="#adapter-and-converter-recipes">Adapter and converter recipes</a></li>
<li><a class="reference internal" href="#how-to-use-connection-shortcut-methods">How to use connection shortcut methods</a></li>
<li><a class="reference internal" href="#how-to-use-the-connection-context-manager">How to use the connection context manager</a></li>
<li><a class="reference internal" href="#how-to-work-with-sqlite-uris">How to work with SQLite URIs</a></li>
<li><a class="reference internal" href="#how-to-create-and-use-row-factories">How to create and use row factories</a></li>
<li><a class="reference internal" href="#how-to-handle-non-utf-8-text-encodings">How to handle non-UTF-8 text encodings</a></li>
</ul>
</li>
<li><a class="reference internal" href="#explanation">Explanation</a><ul>
<li><a class="reference internal" href="#transaction-control">Transaction control</a><ul>
<li><a class="reference internal" href="#transaction-control-via-the-autocommit-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">autocommit</span></code> attribute</a></li>
<li><a class="reference internal" href="#transaction-control-via-the-isolation-level-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">isolation_level</span></code> attribute</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dbm.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm</span></code> — Interfaces to Unix “databases”</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="archiving.html"
                          title="next chapter">Data Compression and Archiving</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sqlite3.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="archiving.html" title="Data Compression and Archiving"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="dbm.html" title="dbm — Interfaces to Unix “databases”"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="persistence.html" accesskey="U">Data Persistence</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> — DB-API 2.0 interface for SQLite databases</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sqlite3">
<span id="sqlite3-db-api-2-0-interface-for-sqlite-databases"></span><h1><a class="reference internal" href="#module-sqlite3" title="sqlite3: A DB-API 2.0 implementation using SQLite 3.x."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code></a> — DB-API 2.0 interface for SQLite databases<a class="headerlink" href="#module-sqlite3" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sqlite3/">Lib/sqlite3/</a></p>
<p id="sqlite3-intro">SQLite is a C library that provides a lightweight disk-based database that
doesn’t require a separate server process and allows accessing the database
using a nonstandard variant of the SQL query language. Some applications can use
SQLite for internal data storage.  It’s also possible to prototype an
application using SQLite and then port the code to a larger database such as
PostgreSQL or Oracle.</p>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module was written by Gerhard Häring.  It provides an SQL interface
compliant with the DB-API 2.0 specification described by <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>, and
requires SQLite 3.7.15 or newer.</p>
<p>This document includes four main sections:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#sqlite3-tutorial"><span class="std std-ref">Tutorial</span></a> teaches how to use the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module.</p></li>
<li><p><a class="reference internal" href="#sqlite3-reference"><span class="std std-ref">Reference</span></a> describes the classes and functions this module
defines.</p></li>
<li><p><a class="reference internal" href="#sqlite3-howtos"><span class="std std-ref">How-to guides</span></a> details how to handle specific tasks.</p></li>
<li><p><a class="reference internal" href="#sqlite3-explanation"><span class="std std-ref">Explanation</span></a> provides in-depth background on
transaction control.</p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.sqlite.org">https://www.sqlite.org</a></dt><dd><p>The SQLite web page; the documentation describes the syntax and the
available data types for the supported SQL dialect.</p>
</dd>
<dt><a class="reference external" href="https://www.w3schools.com/sql/">https://www.w3schools.com/sql/</a></dt><dd><p>Tutorial, reference and examples for learning SQL syntax.</p>
</dd>
<dt><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a> - Database API Specification 2.0</dt><dd><p>PEP written by Marc-André Lemburg.</p>
</dd>
</dl>
</div>
<section id="tutorial">
<span id="sqlite3-tutorial"></span><h2>Tutorial<a class="headerlink" href="#tutorial" title="Link to this heading">¶</a></h2>
<p>In this tutorial, you will create a database of Monty Python movies
using basic <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> functionality.
It assumes a fundamental understanding of database concepts,
including <a class="reference external" href="https://en.wikipedia.org/wiki/Cursor_(databases)">cursors</a> and <a class="reference external" href="https://en.wikipedia.org/wiki/Database_transaction">transactions</a>.</p>
<p>First, we need to create a new database and open
a database connection to allow <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> to work with it.
Call <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">sqlite3.connect()</span></code></a> to create a connection to
the database <code class="file docutils literal notranslate"><span class="pre">tutorial.db</span></code> in the current working directory,
implicitly creating it if it does not exist:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sqlite3</span>
<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;tutorial.db&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The returned <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> object <code class="docutils literal notranslate"><span class="pre">con</span></code>
represents the connection to the on-disk database.</p>
<p>In order to execute SQL statements and fetch results from SQL queries,
we will need to use a database cursor.
Call <a class="reference internal" href="#sqlite3.Connection.cursor" title="sqlite3.Connection.cursor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">con.cursor()</span></code></a> to create the <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">cursor</span><span class="p">()</span>
</pre></div>
</div>
<p>Now that we’ve got a database connection and a cursor,
we can create a database table <code class="docutils literal notranslate"><span class="pre">movie</span></code> with columns for title,
release year, and review score.
For simplicity, we can just use column names in the table declaration –
thanks to the <a class="reference external" href="https://www.sqlite.org/flextypegood.html">flexible typing</a> feature of SQLite,
specifying the data types is optional.
Execute the <code class="docutils literal notranslate"><span class="pre">CREATE</span> <span class="pre">TABLE</span></code> statement
by calling <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cur.execute(...)</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE movie(title, year, score)&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>We can verify that the new table has been created by querying
the <code class="docutils literal notranslate"><span class="pre">sqlite_master</span></code> table built-in to SQLite,
which should now contain an entry for the <code class="docutils literal notranslate"><span class="pre">movie</span></code> table definition
(see <a class="reference external" href="https://www.sqlite.org/schematab.html">The Schema Table</a> for details).
Execute that query by calling <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cur.execute(...)</span></code></a>,
assign the result to <code class="docutils literal notranslate"><span class="pre">res</span></code>,
and call <a class="reference internal" href="#sqlite3.Cursor.fetchone" title="sqlite3.Cursor.fetchone"><code class="xref py py-meth docutils literal notranslate"><span class="pre">res.fetchone()</span></code></a> to fetch the resulting row:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT name FROM sqlite_master&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">res</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span>
<span class="go">(&#39;movie&#39;,)</span>
</pre></div>
</div>
<p>We can see that the table has been created,
as the query returns a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> containing the table’s name.
If we query <code class="docutils literal notranslate"><span class="pre">sqlite_master</span></code> for a non-existent table <code class="docutils literal notranslate"><span class="pre">spam</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">res.fetchone()</span></code> will return <code class="docutils literal notranslate"><span class="pre">None</span></code>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT name FROM sqlite_master WHERE name=&#39;spam&#39;&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">res</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span> <span class="ow">is</span> <span class="kc">None</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Now, add two rows of data supplied as SQL literals
by executing an <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> statement,
once again by calling <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cur.execute(...)</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;&quot;&quot;</span>
<span class="s2">    INSERT INTO movie VALUES</span>
<span class="s2">        (&#39;Monty Python and the Holy Grail&#39;, 1975, 8.2),</span>
<span class="s2">        (&#39;And Now for Something Completely Different&#39;, 1971, 7.5)</span>
<span class="s2">&quot;&quot;&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> statement implicitly opens a transaction,
which needs to be committed before changes are saved in the database
(see <a class="reference internal" href="#sqlite3-controlling-transactions"><span class="std std-ref">Transaction control</span></a> for details).
Call <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">con.commit()</span></code></a> on the connection object
to commit the transaction:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
</pre></div>
</div>
<p>We can verify that the data was inserted correctly
by executing a <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> query.
Use the now-familiar <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cur.execute(...)</span></code></a> to
assign the result to <code class="docutils literal notranslate"><span class="pre">res</span></code>,
and call <a class="reference internal" href="#sqlite3.Cursor.fetchall" title="sqlite3.Cursor.fetchall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">res.fetchall()</span></code></a> to return all resulting rows:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT score FROM movie&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">res</span><span class="o">.</span><span class="n">fetchall</span><span class="p">()</span>
<span class="go">[(8.2,), (7.5,)]</span>
</pre></div>
</div>
<p>The result is a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> of two <code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code>s, one per row,
each containing that row’s <code class="docutils literal notranslate"><span class="pre">score</span></code> value.</p>
<p>Now, insert three more rows by calling
<a class="reference internal" href="#sqlite3.Cursor.executemany" title="sqlite3.Cursor.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">cur.executemany(...)</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="s2">&quot;Monty Python Live at the Hollywood Bowl&quot;</span><span class="p">,</span> <span class="mi">1982</span><span class="p">,</span> <span class="mf">7.9</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;Monty Python&#39;s The Meaning of Life&quot;</span><span class="p">,</span> <span class="mi">1983</span><span class="p">,</span> <span class="mf">7.5</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;Monty Python&#39;s Life of Brian&quot;</span><span class="p">,</span> <span class="mi">1979</span><span class="p">,</span> <span class="mf">8.0</span><span class="p">),</span>
<span class="p">]</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO movie VALUES(?, ?, ?)&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>  <span class="c1"># Remember to commit the transaction after executing INSERT.</span>
</pre></div>
</div>
<p>Notice that <code class="docutils literal notranslate"><span class="pre">?</span></code> placeholders are used to bind <code class="docutils literal notranslate"><span class="pre">data</span></code> to the query.
Always use placeholders instead of <a class="reference internal" href="../tutorial/inputoutput.html#tut-formatting"><span class="std std-ref">string formatting</span></a>
to bind Python values to SQL statements,
to avoid <a class="reference external" href="https://en.wikipedia.org/wiki/SQL_injection">SQL injection attacks</a>
(see <a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">How to use placeholders to bind values in SQL queries</span></a> for more details).</p>
<p>We can verify that the new rows were inserted
by executing a <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> query,
this time iterating over the results of the query:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT year, title FROM movie ORDER BY year&quot;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
<span class="go">(1971, &#39;And Now for Something Completely Different&#39;)</span>
<span class="go">(1975, &#39;Monty Python and the Holy Grail&#39;)</span>
<span class="go">(1979, &quot;Monty Python&#39;s Life of Brian&quot;)</span>
<span class="go">(1982, &#39;Monty Python Live at the Hollywood Bowl&#39;)</span>
<span class="go">(1983, &quot;Monty Python&#39;s The Meaning of Life&quot;)</span>
</pre></div>
</div>
<p>Each row is a two-item <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> of <code class="docutils literal notranslate"><span class="pre">(year,</span> <span class="pre">title)</span></code>,
matching the columns selected in the query.</p>
<p>Finally, verify that the database has been written to disk
by calling <a class="reference internal" href="#sqlite3.Connection.close" title="sqlite3.Connection.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">con.close()</span></code></a>
to close the existing connection, opening a new one,
creating a new cursor, then querying the database:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">new_con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;tutorial.db&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">new_cur</span> <span class="o">=</span> <span class="n">new_con</span><span class="o">.</span><span class="n">cursor</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">new_cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT title, year FROM movie ORDER BY score DESC&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">title</span><span class="p">,</span> <span class="n">year</span> <span class="o">=</span> <span class="n">res</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;The highest scoring Monty Python movie is </span><span class="si">{</span><span class="n">title</span><span class="si">!r}</span><span class="s1">, released in </span><span class="si">{</span><span class="n">year</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="go">The highest scoring Monty Python movie is &#39;Monty Python and the Holy Grail&#39;, released in 1975</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">new_con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>You’ve now created an SQLite database using the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module,
inserted data and retrieved values from it in multiple ways.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><a class="reference internal" href="#sqlite3-howtos"><span class="std std-ref">How-to guides</span></a> for further reading:</p>
<ul>
<li><p><a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">How to use placeholders to bind values in SQL queries</span></a></p></li>
<li><p><a class="reference internal" href="#sqlite3-adapters"><span class="std std-ref">How to adapt custom Python types to SQLite values</span></a></p></li>
<li><p><a class="reference internal" href="#sqlite3-converters"><span class="std std-ref">How to convert SQLite values to custom Python types</span></a></p></li>
<li><p><a class="reference internal" href="#sqlite3-connection-context-manager"><span class="std std-ref">How to use the connection context manager</span></a></p></li>
<li><p><a class="reference internal" href="#sqlite3-howto-row-factory"><span class="std std-ref">How to create and use row factories</span></a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#sqlite3-explanation"><span class="std std-ref">Explanation</span></a> for in-depth background on transaction control.</p></li>
</ul>
</div>
</section>
<section id="reference">
<span id="sqlite3-reference"></span><h2>Reference<a class="headerlink" href="#reference" title="Link to this heading">¶</a></h2>
<section id="module-functions">
<span id="sqlite3-module-functions"></span><span id="sqlite3-module-contents"></span><h3>Module functions<a class="headerlink" href="#module-functions" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="sqlite3.connect">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">connect</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">database</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">5.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">detect_types</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">isolation_level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'DEFERRED'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check_same_thread</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">sqlite3.Connection</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cached_statements</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">128</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autocommit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">sqlite3.LEGACY_TRANSACTION_CONTROL</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.connect" title="Link to this definition">¶</a></dt>
<dd><p>Open a connection to an SQLite database.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>database</strong> (<a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>) – The path to the database file to be opened.
You can pass <code class="docutils literal notranslate"><span class="pre">&quot;:memory:&quot;</span></code> to create an <a class="reference external" href="https://sqlite.org/inmemorydb.html">SQLite database existing only
in memory</a>, and open a connection
to it.</p></li>
<li><p><strong>timeout</strong> (<a class="reference internal" href="functions.html#float" title="float"><em>float</em></a>) – How many seconds the connection should wait before raising
an <a class="reference internal" href="#sqlite3.OperationalError" title="sqlite3.OperationalError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OperationalError</span></code></a> when a table is locked.
If another connection opens a transaction to modify a table,
that table will be locked until the transaction is committed.
Default five seconds.</p></li>
<li><p><strong>detect_types</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – Control whether and how data types not
<a class="reference internal" href="#sqlite3-types"><span class="std std-ref">natively supported by SQLite</span></a>
are looked up to be converted to Python types,
using the converters registered with <a class="reference internal" href="#sqlite3.register_converter" title="sqlite3.register_converter"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_converter()</span></code></a>.
Set it to any combination (using <code class="docutils literal notranslate"><span class="pre">|</span></code>, bitwise or) of
<a class="reference internal" href="#sqlite3.PARSE_DECLTYPES" title="sqlite3.PARSE_DECLTYPES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_DECLTYPES</span></code></a> and <a class="reference internal" href="#sqlite3.PARSE_COLNAMES" title="sqlite3.PARSE_COLNAMES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_COLNAMES</span></code></a>
to enable this.
Column names takes precedence over declared types if both flags are set.
Types cannot be detected for generated fields (for example <code class="docutils literal notranslate"><span class="pre">max(data)</span></code>),
even when the <em>detect_types</em> parameter is set; <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> will be
returned instead.
By default (<code class="docutils literal notranslate"><span class="pre">0</span></code>), type detection is disabled.</p></li>
<li><p><strong>isolation_level</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em> | </em><em>None</em>) – Control legacy transaction handling behaviour.
See <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.isolation_level</span></code></a> and
<a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">Transaction control via the isolation_level attribute</span></a> for more information.
Can be <code class="docutils literal notranslate"><span class="pre">&quot;DEFERRED&quot;</span></code> (default), <code class="docutils literal notranslate"><span class="pre">&quot;EXCLUSIVE&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;IMMEDIATE&quot;</span></code>;
or <code class="docutils literal notranslate"><span class="pre">None</span></code> to disable opening transactions implicitly.
Has no effect unless <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.autocommit</span></code></a> is set to
<a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-const docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a> (the default).</p></li>
<li><p><strong>check_same_thread</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – If <code class="docutils literal notranslate"><span class="pre">True</span></code> (default), <a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProgrammingError</span></code></a> will be raised
if the database connection is used by a thread
other than the one that created it.
If <code class="docutils literal notranslate"><span class="pre">False</span></code>, the connection may be accessed in multiple threads;
write operations may need to be serialized by the user
to avoid data corruption.
See <a class="reference internal" href="#sqlite3.threadsafety" title="sqlite3.threadsafety"><code class="xref py py-attr docutils literal notranslate"><span class="pre">threadsafety</span></code></a> for more information.</p></li>
<li><p><strong>factory</strong> (<a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><em>Connection</em></a>) – A custom subclass of <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> to create the connection with,
if not the default <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> class.</p></li>
<li><p><strong>cached_statements</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The number of statements that <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>
should internally cache for this connection, to avoid parsing overhead.
By default, 128 statements.</p></li>
<li><p><strong>uri</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – If set to <code class="docutils literal notranslate"><span class="pre">True</span></code>, <em>database</em> is interpreted as a
<abbr title="Uniform Resource Identifier">URI</abbr> with a file path
and an optional query string.
The scheme part <em>must</em> be <code class="docutils literal notranslate"><span class="pre">&quot;file:&quot;</span></code>,
and the path can be relative or absolute.
The query string allows passing parameters to SQLite,
enabling various <a class="reference internal" href="#sqlite3-uri-tricks"><span class="std std-ref">How to work with SQLite URIs</span></a>.</p></li>
<li><p><strong>autocommit</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Control <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a> transaction handling behaviour.
See <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.autocommit</span></code></a> and
<a class="reference internal" href="#sqlite3-transaction-control-autocommit"><span class="std std-ref">Transaction control via the autocommit attribute</span></a> for more information.
<em>autocommit</em> currently defaults to
<a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-const docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>.
The default will change to <code class="docutils literal notranslate"><span class="pre">False</span></code> in a future Python release.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><em>Connection</em></a></p>
</dd>
</dl>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sqlite3.connect</span></code> with argument <code class="docutils literal notranslate"><span class="pre">database</span></code>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sqlite3.connect/handle</span></code> with argument <code class="docutils literal notranslate"><span class="pre">connection_handle</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>uri</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>database</em> can now also be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>, not only a string.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="docutils literal notranslate"><span class="pre">sqlite3.connect/handle</span></code> auditing event.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>autocommit</em> parameter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sqlite3.complete_statement">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">complete_statement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">statement</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.complete_statement" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the string <em>statement</em> appears to contain
one or more complete SQL statements.
No syntactic verification or parsing of any kind is performed,
other than checking that there are no unclosed string literals
and the statement is terminated by a semicolon.</p>
<p>For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">sqlite3</span><span class="o">.</span><span class="n">complete_statement</span><span class="p">(</span><span class="s2">&quot;SELECT foo FROM bar;&quot;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sqlite3</span><span class="o">.</span><span class="n">complete_statement</span><span class="p">(</span><span class="s2">&quot;SELECT foo&quot;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>This function may be useful during command-line input
to determine if the entered text seems to form a complete SQL statement,
or if additional input is needed before calling <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a>.</p>
<p>See <code class="xref py py-func docutils literal notranslate"><span class="pre">runsource()</span></code> in <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sqlite3/__main__.py">Lib/sqlite3/__main__.py</a>
for real-world use.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sqlite3.enable_callback_tracebacks">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">enable_callback_tracebacks</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.enable_callback_tracebacks" title="Link to this definition">¶</a></dt>
<dd><p>Enable or disable callback tracebacks.
By default you will not get any tracebacks in user-defined functions,
aggregates, converters, authorizer callbacks etc. If you want to debug them,
you can call this function with <em>flag</em> set to <code class="docutils literal notranslate"><span class="pre">True</span></code>. Afterwards, you
will get tracebacks from callbacks on <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>. Use <code class="docutils literal notranslate"><span class="pre">False</span></code>
to disable the feature again.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Errors in user-defined function callbacks are logged as unraisable exceptions.
Use an <a class="reference internal" href="sys.html#sys.unraisablehook" title="sys.unraisablehook"><code class="xref py py-func docutils literal notranslate"><span class="pre">unraisable</span> <span class="pre">hook</span> <span class="pre">handler</span></code></a> for
introspection of the failed callback.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sqlite3.register_adapter">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">register_adapter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">adapter</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.register_adapter" title="Link to this definition">¶</a></dt>
<dd><p>Register an <em>adapter</em> <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> to adapt the Python type <em>type</em>
into an SQLite type.
The adapter is called with a Python object of type <em>type</em> as its sole
argument, and must return a value of a
<a class="reference internal" href="#sqlite3-types"><span class="std std-ref">type that SQLite natively understands</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sqlite3.register_converter">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">register_converter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">typename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">converter</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.register_converter" title="Link to this definition">¶</a></dt>
<dd><p>Register the <em>converter</em> <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> to convert SQLite objects of type
<em>typename</em> into a Python object of a specific type.
The converter is invoked for all SQLite values of type <em>typename</em>;
it is passed a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object and should return an object of the
desired Python type.
Consult the parameter <em>detect_types</em> of
<a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a> for information regarding how type detection works.</p>
<p>Note: <em>typename</em> and the name of the type in your query are matched
case-insensitively.</p>
</dd></dl>

</section>
<section id="module-constants">
<span id="sqlite3-module-constants"></span><h3>Module constants<a class="headerlink" href="#module-constants" title="Link to this heading">¶</a></h3>
<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.LEGACY_TRANSACTION_CONTROL">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></span><a class="headerlink" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="Link to this definition">¶</a></dt>
<dd><p>Set <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> to this constant to select
old style (pre-Python 3.12) transaction control behaviour.
See <a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">Transaction control via the isolation_level attribute</span></a> for more information.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.PARSE_COLNAMES">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">PARSE_COLNAMES</span></span><a class="headerlink" href="#sqlite3.PARSE_COLNAMES" title="Link to this definition">¶</a></dt>
<dd><p>Pass this flag value to the <em>detect_types</em> parameter of
<a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a> to look up a converter function by
using the type name, parsed from the query column name,
as the converter dictionary key.
The type name must be wrapped in square brackets (<code class="docutils literal notranslate"><span class="pre">[]</span></code>).</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">SELECT</span><span class="w"> </span><span class="n">p</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="ss">&quot;p [point]&quot;</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="n">test</span><span class="p">;</span><span class="w">  </span><span class="o">!</span><span class="w"> </span><span class="n">will</span><span class="w"> </span><span class="n">look</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">converter</span><span class="w"> </span><span class="ss">&quot;point&quot;</span>
</pre></div>
</div>
<p>This flag may be combined with <a class="reference internal" href="#sqlite3.PARSE_DECLTYPES" title="sqlite3.PARSE_DECLTYPES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_DECLTYPES</span></code></a> using the <code class="docutils literal notranslate"><span class="pre">|</span></code>
(bitwise or) operator.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.PARSE_DECLTYPES">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">PARSE_DECLTYPES</span></span><a class="headerlink" href="#sqlite3.PARSE_DECLTYPES" title="Link to this definition">¶</a></dt>
<dd><p>Pass this flag value to the <em>detect_types</em> parameter of
<a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a> to look up a converter function using
the declared types for each column.
The types are declared when the database table is created.
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> will look up a converter function using the first word of the
declared type as the converter dictionary key.
For example:</p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">test</span><span class="p">(</span>
<span class="w">   </span><span class="n">i</span><span class="w"> </span><span class="nb">integer</span><span class="w"> </span><span class="k">primary</span><span class="w"> </span><span class="k">key</span><span class="p">,</span><span class="w">  </span><span class="o">!</span><span class="w"> </span><span class="n">will</span><span class="w"> </span><span class="n">look</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="n">converter</span><span class="w"> </span><span class="n">named</span><span class="w"> </span><span class="ss">&quot;integer&quot;</span>
<span class="w">   </span><span class="n">p</span><span class="w"> </span><span class="n">point</span><span class="p">,</span><span class="w">                </span><span class="o">!</span><span class="w"> </span><span class="n">will</span><span class="w"> </span><span class="n">look</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="n">converter</span><span class="w"> </span><span class="n">named</span><span class="w"> </span><span class="ss">&quot;point&quot;</span>
<span class="w">   </span><span class="n">n</span><span class="w"> </span><span class="nb">number</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span><span class="w">            </span><span class="o">!</span><span class="w"> </span><span class="n">will</span><span class="w"> </span><span class="n">look</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="n">converter</span><span class="w"> </span><span class="n">named</span><span class="w"> </span><span class="ss">&quot;number&quot;</span>
<span class="w"> </span><span class="p">)</span>
</pre></div>
</div>
<p>This flag may be combined with <a class="reference internal" href="#sqlite3.PARSE_COLNAMES" title="sqlite3.PARSE_COLNAMES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_COLNAMES</span></code></a> using the <code class="docutils literal notranslate"><span class="pre">|</span></code>
(bitwise or) operator.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.SQLITE_OK">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_OK</span></span><a class="headerlink" href="#sqlite3.SQLITE_OK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DENY">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DENY</span></span><a class="headerlink" href="#sqlite3.SQLITE_DENY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_IGNORE">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_IGNORE</span></span><a class="headerlink" href="#sqlite3.SQLITE_IGNORE" title="Link to this definition">¶</a></dt>
<dd><p>Flags that should be returned by the <em>authorizer_callback</em> <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a>
passed to <a class="reference internal" href="#sqlite3.Connection.set_authorizer" title="sqlite3.Connection.set_authorizer"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.set_authorizer()</span></code></a>, to indicate whether:</p>
<ul class="simple">
<li><p>Access is allowed (<code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_OK</span></code>),</p></li>
<li><p>The SQL statement should be aborted with an error (<code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_DENY</span></code>)</p></li>
<li><p>The column should be treated as a <code class="docutils literal notranslate"><span class="pre">NULL</span></code> value (<code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_IGNORE</span></code>)</p></li>
</ul>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.apilevel">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">apilevel</span></span><a class="headerlink" href="#sqlite3.apilevel" title="Link to this definition">¶</a></dt>
<dd><p>String constant stating the supported DB-API level. Required by the DB-API.
Hard-coded to <code class="docutils literal notranslate"><span class="pre">&quot;2.0&quot;</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.paramstyle">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">paramstyle</span></span><a class="headerlink" href="#sqlite3.paramstyle" title="Link to this definition">¶</a></dt>
<dd><p>String constant stating the type of parameter marker formatting expected by
the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module. Required by the DB-API. Hard-coded to
<code class="docutils literal notranslate"><span class="pre">&quot;qmark&quot;</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">named</span></code> DB-API parameter style is also supported.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.sqlite_version">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">sqlite_version</span></span><a class="headerlink" href="#sqlite3.sqlite_version" title="Link to this definition">¶</a></dt>
<dd><p>Version number of the runtime SQLite library as a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">string</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.sqlite_version_info">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">sqlite_version_info</span></span><a class="headerlink" href="#sqlite3.sqlite_version_info" title="Link to this definition">¶</a></dt>
<dd><p>Version number of the runtime SQLite library as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> of
<a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">integers</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.threadsafety">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">threadsafety</span></span><a class="headerlink" href="#sqlite3.threadsafety" title="Link to this definition">¶</a></dt>
<dd><p>Integer constant required by the DB-API 2.0, stating the level of thread
safety the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module supports. This attribute is set based on
the default <a class="reference external" href="https://sqlite.org/threadsafe.html">threading mode</a> the
underlying SQLite library is compiled with. The SQLite threading modes are:</p>
<ol class="arabic simple">
<li><p><strong>Single-thread</strong>: In this mode, all mutexes are disabled and SQLite is
unsafe to use in more than a single thread at once.</p></li>
<li><p><strong>Multi-thread</strong>: In this mode, SQLite can be safely used by multiple
threads provided that no single database connection is used
simultaneously in two or more threads.</p></li>
<li><p><strong>Serialized</strong>: In serialized mode, SQLite can be safely used by
multiple threads with no restriction.</p></li>
</ol>
<p>The mappings from SQLite threading modes to DB-API 2.0 threadsafety levels
are as follows:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>SQLite threading
mode</p></th>
<th class="head"><p><a class="reference external" href="https://peps.python.org/pep-0249/#threadsafety">threadsafety</a></p></th>
<th class="head"><p><a class="reference external" href="https://sqlite.org/compile.html#threadsafe">SQLITE_THREADSAFE</a></p></th>
<th class="head"><p>DB-API 2.0 meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>single-thread</p></td>
<td><p>0</p></td>
<td><p>0</p></td>
<td><p>Threads may not share the
module</p></td>
</tr>
<tr class="row-odd"><td><p>multi-thread</p></td>
<td><p>1</p></td>
<td><p>2</p></td>
<td><p>Threads may share the module,
but not connections</p></td>
</tr>
<tr class="row-even"><td><p>serialized</p></td>
<td><p>3</p></td>
<td><p>1</p></td>
<td><p>Threads may share the module,
connections and cursors</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Set <em>threadsafety</em> dynamically instead of hard-coding it to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.version">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#sqlite3.version" title="Link to this definition">¶</a></dt>
<dd><p>Version number of this module as a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">string</span></code></a>.
This is not the version of the SQLite library.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>This constant used to reflect the version number of the <code class="docutils literal notranslate"><span class="pre">pysqlite</span></code>
package, a third-party library which used to upstream changes to
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>. Today, it carries no meaning or practical value.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sqlite3.version_info">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">version_info</span></span><a class="headerlink" href="#sqlite3.version_info" title="Link to this definition">¶</a></dt>
<dd><p>Version number of this module as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> of <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">integers</span></code></a>.
This is not the version of the SQLite library.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>This constant used to reflect the version number of the <code class="docutils literal notranslate"><span class="pre">pysqlite</span></code>
package, a third-party library which used to upstream changes to
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>. Today, it carries no meaning or practical value.</p>
</div>
</dd></dl>

<dl class="py data" id="sqlite3-dbconfig-constants">
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_DEFENSIVE">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_DEFENSIVE</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_DEFENSIVE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_DQS_DDL">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_DQS_DDL</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_DQS_DDL" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_DQS_DML">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_DQS_DML</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_DQS_DML" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_FKEY">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_FKEY</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_FKEY" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_QPSG">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_QPSG</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_QPSG" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_TRIGGER">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_TRIGGER</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_TRIGGER" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_ENABLE_VIEW">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_ENABLE_VIEW</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_ENABLE_VIEW" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_LEGACY_ALTER_TABLE">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_LEGACY_ALTER_TABLE</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_LEGACY_ALTER_TABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_LEGACY_FILE_FORMAT">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_LEGACY_FILE_FORMAT</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_LEGACY_FILE_FORMAT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_RESET_DATABASE">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_RESET_DATABASE</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_RESET_DATABASE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_TRIGGER_EQP">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_TRIGGER_EQP</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_TRIGGER_EQP" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_TRUSTED_SCHEMA">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_TRUSTED_SCHEMA</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_TRUSTED_SCHEMA" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sqlite3.SQLITE_DBCONFIG_WRITABLE_SCHEMA">
<span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">SQLITE_DBCONFIG_WRITABLE_SCHEMA</span></span><a class="headerlink" href="#sqlite3.SQLITE_DBCONFIG_WRITABLE_SCHEMA" title="Link to this definition">¶</a></dt>
<dd><p>These constants are used for the <a class="reference internal" href="#sqlite3.Connection.setconfig" title="sqlite3.Connection.setconfig"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.setconfig()</span></code></a>
and <a class="reference internal" href="#sqlite3.Connection.getconfig" title="sqlite3.Connection.getconfig"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getconfig()</span></code></a> methods.</p>
<p>The availability of these constants varies depending on the version of SQLite
Python was compiled with.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.sqlite.org/c3ref/c_dbconfig_defensive.html">https://www.sqlite.org/c3ref/c_dbconfig_defensive.html</a></dt><dd><p>SQLite docs: Database Connection Configuration Options</p>
</dd>
</dl>
</div>
</dd></dl>

</section>
<section id="connection-objects">
<span id="sqlite3-connection-objects"></span><h3>Connection objects<a class="headerlink" href="#connection-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="sqlite3.Connection">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Connection</span></span><a class="headerlink" href="#sqlite3.Connection" title="Link to this definition">¶</a></dt>
<dd><p>Each open SQLite database is represented by a <code class="docutils literal notranslate"><span class="pre">Connection</span></code> object,
which is created using <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">sqlite3.connect()</span></code></a>.
Their main purpose is creating <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> objects,
and <a class="reference internal" href="#sqlite3-controlling-transactions"><span class="std std-ref">Transaction control</span></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><a class="reference internal" href="#sqlite3-connection-shortcuts"><span class="std std-ref">How to use connection shortcut methods</span></a></p></li>
<li><p><a class="reference internal" href="#sqlite3-connection-context-manager"><span class="std std-ref">How to use the connection context manager</span></a></p></li>
</ul>
</div>
<p>An SQLite database connection has the following attributes and methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.cursor">
<span class="sig-name descname"><span class="pre">cursor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">Cursor</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.cursor" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object.
The cursor method accepts a single optional parameter <em>factory</em>. If
supplied, this must be a <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> returning
an instance of <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> or its subclasses.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.blobopen">
<span class="sig-name descname"><span class="pre">blobopen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">table</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">row</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">readonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'main'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.blobopen" title="Link to this definition">¶</a></dt>
<dd><p>Open a <a class="reference internal" href="#sqlite3.Blob" title="sqlite3.Blob"><code class="xref py py-class docutils literal notranslate"><span class="pre">Blob</span></code></a> handle to an existing
<abbr title="Binary Large OBject">BLOB</abbr>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>table</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the table where the blob is located.</p></li>
<li><p><strong>column</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the column where the blob is located.</p></li>
<li><p><strong>row</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the row where the blob is located.</p></li>
<li><p><strong>readonly</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Set to <code class="docutils literal notranslate"><span class="pre">True</span></code> if the blob should be opened without write
permissions.
Defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p></li>
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the database where the blob is located.
Defaults to <code class="docutils literal notranslate"><span class="pre">&quot;main&quot;</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.OperationalError" title="sqlite3.OperationalError"><strong>OperationalError</strong></a> – When trying to open a blob in a <code class="docutils literal notranslate"><span class="pre">WITHOUT</span> <span class="pre">ROWID</span></code> table.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="#sqlite3.Blob" title="sqlite3.Blob">Blob</a></p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The blob size cannot be changed using the <a class="reference internal" href="#sqlite3.Blob" title="sqlite3.Blob"><code class="xref py py-class docutils literal notranslate"><span class="pre">Blob</span></code></a> class.
Use the SQL function <code class="docutils literal notranslate"><span class="pre">zeroblob</span></code> to create a blob with a fixed size.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.commit">
<span class="sig-name descname"><span class="pre">commit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.commit" title="Link to this definition">¶</a></dt>
<dd><p>Commit any pending transaction to the database.
If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, or there is no open transaction,
this method does nothing.
If <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, a new transaction is implicitly
opened if a pending transaction was committed by this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.rollback">
<span class="sig-name descname"><span class="pre">rollback</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.rollback" title="Link to this definition">¶</a></dt>
<dd><p>Roll back to the start of any pending transaction.
If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, or there is no open transaction,
this method does nothing.
If <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, a new transaction is implicitly
opened if a pending transaction was rolled back by this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the database connection.
If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>,
any pending transaction is implicitly rolled back.
If <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code> is <code class="docutils literal notranslate"><span class="pre">True</span></code> or <a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>,
no implicit transaction control is executed.
Make sure to <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">commit()</span></code></a> before closing
to avoid losing pending changes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.execute">
<span class="sig-name descname"><span class="pre">execute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parameters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.execute" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object and call
<a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> on it with the given <em>sql</em> and <em>parameters</em>.
Return the new cursor object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.executemany">
<span class="sig-name descname"><span class="pre">executemany</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parameters</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.executemany" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object and call
<a class="reference internal" href="#sqlite3.Cursor.executemany" title="sqlite3.Cursor.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executemany()</span></code></a> on it with the given <em>sql</em> and <em>parameters</em>.
Return the new cursor object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.executescript">
<span class="sig-name descname"><span class="pre">executescript</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql_script</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.executescript" title="Link to this definition">¶</a></dt>
<dd><p>Create a new <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object and call
<a class="reference internal" href="#sqlite3.Cursor.executescript" title="sqlite3.Cursor.executescript"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executescript()</span></code></a> on it with the given <em>sql_script</em>.
Return the new cursor object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.create_function">
<span class="sig-name descname"><span class="pre">create_function</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">narg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deterministic</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.create_function" title="Link to this definition">¶</a></dt>
<dd><p>Create or remove a user-defined SQL function.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the SQL function.</p></li>
<li><p><strong>narg</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The number of arguments the SQL function can accept.
If <code class="docutils literal notranslate"><span class="pre">-1</span></code>, it may take any number of arguments.</p></li>
<li><p><strong>func</strong> (<a class="reference internal" href="../glossary.html#term-callback"><span class="xref std std-term">callback</span></a> | None) – A <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> that is called when the SQL function is invoked.
The callable must return <a class="reference internal" href="#sqlite3-types"><span class="std std-ref">a type natively supported by SQLite</span></a>.
Set to <code class="docutils literal notranslate"><span class="pre">None</span></code> to remove an existing SQL function.</p></li>
<li><p><strong>deterministic</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – If <code class="docutils literal notranslate"><span class="pre">True</span></code>, the created SQL function is marked as
<a class="reference external" href="https://sqlite.org/deterministic.html">deterministic</a>,
which allows SQLite to perform additional optimizations.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.NotSupportedError" title="sqlite3.NotSupportedError"><strong>NotSupportedError</strong></a> – If <em>deterministic</em> is used with SQLite versions older than 3.8.3.</p>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>deterministic</em> parameter.</p>
</div>
<p>Example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">hashlib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">md5sum</span><span class="p">(</span><span class="n">t</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">hashlib</span><span class="o">.</span><span class="n">md5</span><span class="p">(</span><span class="n">t</span><span class="p">)</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">create_function</span><span class="p">(</span><span class="s2">&quot;md5&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">md5sum</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT md5(?)&quot;</span><span class="p">,</span> <span class="p">(</span><span class="sa">b</span><span class="s2">&quot;foo&quot;</span><span class="p">,)):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
<span class="go">(&#39;acbd18db4cc2f85cedef654fccc4a4d8&#39;,)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.create_aggregate">
<span class="sig-name descname"><span class="pre">create_aggregate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n_arg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregate_class</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.create_aggregate" title="Link to this definition">¶</a></dt>
<dd><p>Create or remove a user-defined SQL aggregate function.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the SQL aggregate function.</p></li>
<li><p><strong>n_arg</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The number of arguments the SQL aggregate function can accept.
If <code class="docutils literal notranslate"><span class="pre">-1</span></code>, it may take any number of arguments.</p></li>
<li><p><strong>aggregate_class</strong> (<a class="reference internal" href="../glossary.html#term-class"><span class="xref std std-term">class</span></a> | None) – <p>A class must implement the following methods:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">step()</span></code>: Add a row to the aggregate.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">finalize()</span></code>: Return the final result of the aggregate as
<a class="reference internal" href="#sqlite3-types"><span class="std std-ref">a type natively supported by SQLite</span></a>.</p></li>
</ul>
<p>The number of arguments that the <code class="docutils literal notranslate"><span class="pre">step()</span></code> method must accept
is controlled by <em>n_arg</em>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">None</span></code> to remove an existing SQL aggregate function.</p>
</p></li>
</ul>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MySum</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">count</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span> <span class="nf">step</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">count</span> <span class="o">+=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="nf">finalize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">count</span>

<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">create_aggregate</span><span class="p">(</span><span class="s2">&quot;mysum&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">MySum</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(i)&quot;</span><span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(i) VALUES(1)&quot;</span><span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(i) VALUES(2)&quot;</span><span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT mysum(i) FROM test&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()[</span><span class="mi">0</span><span class="p">])</span>

<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.create_window_function">
<span class="sig-name descname"><span class="pre">create_window_function</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">num_params</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">aggregate_class</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.create_window_function" title="Link to this definition">¶</a></dt>
<dd><p>Create or remove a user-defined aggregate window function.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the SQL aggregate window function to create or remove.</p></li>
<li><p><strong>num_params</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The number of arguments the SQL aggregate window function can accept.
If <code class="docutils literal notranslate"><span class="pre">-1</span></code>, it may take any number of arguments.</p></li>
<li><p><strong>aggregate_class</strong> (<a class="reference internal" href="../glossary.html#term-class"><span class="xref std std-term">class</span></a> | None) – <p>A class that must implement the following methods:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">step()</span></code>: Add a row to the current window.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">value()</span></code>: Return the current value of the aggregate.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">inverse()</span></code>: Remove a row from the current window.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">finalize()</span></code>: Return the final result of the aggregate as
<a class="reference internal" href="#sqlite3-types"><span class="std std-ref">a type natively supported by SQLite</span></a>.</p></li>
</ul>
<p>The number of arguments that the <code class="docutils literal notranslate"><span class="pre">step()</span></code> and <code class="docutils literal notranslate"><span class="pre">value()</span></code> methods
must accept is controlled by <em>num_params</em>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">None</span></code> to remove an existing SQL aggregate window function.</p>
</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.NotSupportedError" title="sqlite3.NotSupportedError"><strong>NotSupportedError</strong></a> – If used with a version of SQLite older than 3.25.0,
which does not support aggregate window functions.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Example taken from https://www.sqlite.org/windowfunctions.html#udfwinfunc</span>
<span class="k">class</span> <span class="nc">WindowSumInt</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">count</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span> <span class="nf">step</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Add a row to the current window.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">count</span> <span class="o">+=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="nf">value</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Return the current value of the aggregate.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">count</span>

    <span class="k">def</span> <span class="nf">inverse</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Remove a row from the current window.&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">count</span> <span class="o">-=</span> <span class="n">value</span>

    <span class="k">def</span> <span class="nf">finalize</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Return the final value of the aggregate.</span>

<span class="sd">        Any clean-up actions should be placed here.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">count</span>


<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(x, y)&quot;</span><span class="p">)</span>
<span class="n">values</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="s2">&quot;a&quot;</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;b&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;c&quot;</span><span class="p">,</span> <span class="mi">3</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;d&quot;</span><span class="p">,</span> <span class="mi">8</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;e&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
<span class="p">]</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test VALUES(?, ?)&quot;</span><span class="p">,</span> <span class="n">values</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">create_window_function</span><span class="p">(</span><span class="s2">&quot;sumint&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="n">WindowSumInt</span><span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;&quot;&quot;</span>
<span class="s2">    SELECT x, sumint(y) OVER (</span>
<span class="s2">        ORDER BY x ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING</span>
<span class="s2">    ) AS sum_y</span>
<span class="s2">    FROM test ORDER BY x</span>
<span class="s2">&quot;&quot;&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cur</span><span class="o">.</span><span class="n">fetchall</span><span class="p">())</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.create_collation">
<span class="sig-name descname"><span class="pre">create_collation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">callable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.create_collation" title="Link to this definition">¶</a></dt>
<dd><p>Create a collation named <em>name</em> using the collating function <em>callable</em>.
<em>callable</em> is passed two <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">string</span></code></a> arguments,
and it should return an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">integer</span></code></a>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the first is ordered higher than the second</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">-1</span></code> if the first is ordered lower than the second</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">0</span></code> if they are ordered equal</p></li>
</ul>
<p>The following example shows a reverse sorting collation:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">collate_reverse</span><span class="p">(</span><span class="n">string1</span><span class="p">,</span> <span class="n">string2</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">string1</span> <span class="o">==</span> <span class="n">string2</span><span class="p">:</span>
        <span class="k">return</span> <span class="mi">0</span>
    <span class="k">elif</span> <span class="n">string1</span> <span class="o">&lt;</span> <span class="n">string2</span><span class="p">:</span>
        <span class="k">return</span> <span class="mi">1</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">return</span> <span class="o">-</span><span class="mi">1</span>

<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">create_collation</span><span class="p">(</span><span class="s2">&quot;reverse&quot;</span><span class="p">,</span> <span class="n">collate_reverse</span><span class="p">)</span>

<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(x)&quot;</span><span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(x) VALUES(?)&quot;</span><span class="p">,</span> <span class="p">[(</span><span class="s2">&quot;a&quot;</span><span class="p">,),</span> <span class="p">(</span><span class="s2">&quot;b&quot;</span><span class="p">,)])</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT x FROM test ORDER BY x COLLATE reverse&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">cur</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>Remove a collation function by setting <em>callable</em> to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The collation name can contain any Unicode character.  Earlier, only
ASCII characters were allowed.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.interrupt">
<span class="sig-name descname"><span class="pre">interrupt</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.interrupt" title="Link to this definition">¶</a></dt>
<dd><p>Call this method from a different thread to abort any queries that might
be executing on the connection.
Aborted queries will raise an <a class="reference internal" href="#sqlite3.OperationalError" title="sqlite3.OperationalError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OperationalError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.set_authorizer">
<span class="sig-name descname"><span class="pre">set_authorizer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">authorizer_callback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.set_authorizer" title="Link to this definition">¶</a></dt>
<dd><p>Register <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> <em>authorizer_callback</em> to be invoked
for each attempt to access a column of a table in the database.
The callback should return one of <a class="reference internal" href="#sqlite3.SQLITE_OK" title="sqlite3.SQLITE_OK"><code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_OK</span></code></a>,
<a class="reference internal" href="#sqlite3.SQLITE_DENY" title="sqlite3.SQLITE_DENY"><code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_DENY</span></code></a>, or <a class="reference internal" href="#sqlite3.SQLITE_IGNORE" title="sqlite3.SQLITE_IGNORE"><code class="xref py py-const docutils literal notranslate"><span class="pre">SQLITE_IGNORE</span></code></a>
to signal how access to the column should be handled
by the underlying SQLite library.</p>
<p>The first argument to the callback signifies what kind of operation is to be
authorized. The second and third argument will be arguments or <code class="docutils literal notranslate"><span class="pre">None</span></code>
depending on the first argument. The 4th argument is the name of the database
(“main”, “temp”, etc.) if applicable. The 5th argument is the name of the
inner-most trigger or view that is responsible for the access attempt or
<code class="docutils literal notranslate"><span class="pre">None</span></code> if this access attempt is directly from input SQL code.</p>
<p>Please consult the SQLite documentation about the possible values for the first
argument and the meaning of the second and third argument depending on the first
one. All necessary constants are available in the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module.</p>
<p>Passing <code class="docutils literal notranslate"><span class="pre">None</span></code> as <em>authorizer_callback</em> will disable the authorizer.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added support for disabling the authorizer using <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.set_progress_handler">
<span class="sig-name descname"><span class="pre">set_progress_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">progress_handler</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.set_progress_handler" title="Link to this definition">¶</a></dt>
<dd><p>Register <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> <em>progress_handler</em> to be invoked for every <em>n</em>
instructions of the SQLite virtual machine. This is useful if you want to
get called from SQLite during long-running operations, for example to update
a GUI.</p>
<p>If you want to clear any previously installed progress handler, call the
method with <code class="docutils literal notranslate"><span class="pre">None</span></code> for <em>progress_handler</em>.</p>
<p>Returning a non-zero value from the handler function will terminate the
currently executing query and cause it to raise a <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>
exception.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.set_trace_callback">
<span class="sig-name descname"><span class="pre">set_trace_callback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">trace_callback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.set_trace_callback" title="Link to this definition">¶</a></dt>
<dd><p>Register <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> <em>trace_callback</em> to be invoked
for each SQL statement that is actually executed by the SQLite backend.</p>
<p>The only argument passed to the callback is the statement (as
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>) that is being executed. The return value of the callback is
ignored. Note that the backend does not only run statements passed to the
<a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Cursor.execute()</span></code></a> methods.  Other sources include the
<a class="reference internal" href="#sqlite3-controlling-transactions"><span class="std std-ref">transaction management</span></a> of the
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module and the execution of triggers defined in the current
database.</p>
<p>Passing <code class="docutils literal notranslate"><span class="pre">None</span></code> as <em>trace_callback</em> will disable the trace callback.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Exceptions raised in the trace callback are not propagated. As a
development and debugging aid, use
<a class="reference internal" href="#sqlite3.enable_callback_tracebacks" title="sqlite3.enable_callback_tracebacks"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enable_callback_tracebacks()</span></code></a> to enable printing
tracebacks from exceptions raised in the trace callback.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.enable_load_extension">
<span class="sig-name descname"><span class="pre">enable_load_extension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.enable_load_extension" title="Link to this definition">¶</a></dt>
<dd><p>Enable the SQLite engine to load SQLite extensions from shared libraries
if <em>enabled</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>;
else, disallow loading SQLite extensions.
SQLite extensions can define new functions,
aggregates or whole new virtual table implementations.  One well-known
extension is the fulltext-search extension distributed with SQLite.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module is not built with loadable extension support by
default, because some platforms (notably macOS) have SQLite
libraries which are compiled without this feature.
To get loadable extension support,
you must pass the <a class="reference internal" href="../using/configure.html#cmdoption-enable-loadable-sqlite-extensions"><code class="xref std std-option docutils literal notranslate"><span class="pre">--enable-loadable-sqlite-extensions</span></code></a> option
to <strong class="program">configure</strong>.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sqlite3.enable_load_extension</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">connection</span></code>, <code class="docutils literal notranslate"><span class="pre">enabled</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="docutils literal notranslate"><span class="pre">sqlite3.enable_load_extension</span></code> auditing event.</p>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">con</span><span class="o">.</span><span class="n">enable_load_extension</span><span class="p">(</span><span class="kc">True</span><span class="p">)</span>

<span class="c1"># Load the fulltext search extension</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;select load_extension(&#39;./fts3.so&#39;)&quot;</span><span class="p">)</span>

<span class="c1"># alternatively you can load the extension using an API call:</span>
<span class="c1"># con.load_extension(&quot;./fts3.so&quot;)</span>

<span class="c1"># disable extension loading again</span>
<span class="n">con</span><span class="o">.</span><span class="n">enable_load_extension</span><span class="p">(</span><span class="kc">False</span><span class="p">)</span>

<span class="c1"># example from SQLite wiki</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE VIRTUAL TABLE recipe USING fts3(name, ingredients)&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">executescript</span><span class="p">(</span><span class="s2">&quot;&quot;&quot;</span>
<span class="s2">    INSERT INTO recipe (name, ingredients) VALUES(&#39;broccoli stew&#39;, &#39;broccoli peppers cheese tomatoes&#39;);</span>
<span class="s2">    INSERT INTO recipe (name, ingredients) VALUES(&#39;pumpkin stew&#39;, &#39;pumpkin onions garlic celery&#39;);</span>
<span class="s2">    INSERT INTO recipe (name, ingredients) VALUES(&#39;broccoli pie&#39;, &#39;broccoli cheese onions flour&#39;);</span>
<span class="s2">    INSERT INTO recipe (name, ingredients) VALUES(&#39;pumpkin pie&#39;, &#39;pumpkin sugar flour butter&#39;);</span>
<span class="s2">    &quot;&quot;&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT rowid, name, ingredients FROM recipe WHERE name MATCH &#39;pie&#39;&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.load_extension">
<span class="sig-name descname"><span class="pre">load_extension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entrypoint</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.load_extension" title="Link to this definition">¶</a></dt>
<dd><p>Load an SQLite extension from a shared library.
Enable extension loading with <a class="reference internal" href="#sqlite3.Connection.enable_load_extension" title="sqlite3.Connection.enable_load_extension"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enable_load_extension()</span></code></a> before
calling this method.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>path</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The path to the SQLite extension.</p></li>
<li><p><strong>entrypoint</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em> | </em><em>None</em>) – Entry point name.
If <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default),
SQLite will come up with an entry point name of its own;
see the SQLite docs <a class="reference external" href="https://www.sqlite.org/loadext.html#loading_an_extension">Loading an Extension</a> for details.</p></li>
</ul>
</dd>
</dl>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sqlite3.load_extension</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">connection</span></code>, <code class="docutils literal notranslate"><span class="pre">path</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <code class="docutils literal notranslate"><span class="pre">sqlite3.load_extension</span></code> auditing event.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>entrypoint</em> parameter.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.iterdump">
<span class="sig-name descname"><span class="pre">iterdump</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.iterdump" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> to dump the database as SQL source code.
Useful when saving an in-memory database for later restoration.
Similar to the <code class="docutils literal notranslate"><span class="pre">.dump</span></code> command in the <strong class="program">sqlite3</strong> shell.</p>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Convert file example.db to SQL dump file dump.sql</span>
<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s1">&#39;example.db&#39;</span><span class="p">)</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;dump.sql&#39;</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">con</span><span class="o">.</span><span class="n">iterdump</span><span class="p">():</span>
        <span class="n">f</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%s</span><span class="se">\n</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">line</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#sqlite3-howto-encoding"><span class="std std-ref">How to handle non-UTF-8 text encodings</span></a></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.backup">
<span class="sig-name descname"><span class="pre">backup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pages</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">progress</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'main'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sleep</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.250</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.backup" title="Link to this definition">¶</a></dt>
<dd><p>Create a backup of an SQLite database.</p>
<p>Works even if the database is being accessed by other clients
or concurrently by the same connection.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>target</strong> (<a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><em>Connection</em></a>) – The database connection to save the backup to.</p></li>
<li><p><strong>pages</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The number of pages to copy at a time.
If equal to or less than <code class="docutils literal notranslate"><span class="pre">0</span></code>,
the entire database is copied in a single step.
Defaults to <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p></li>
<li><p><strong>progress</strong> (<a class="reference internal" href="../glossary.html#term-callback"><span class="xref std std-term">callback</span></a> | None) – If set to a <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a>,
it is invoked with three integer arguments for every backup iteration:
the <em>status</em> of the last iteration,
the <em>remaining</em> number of pages still to be copied,
and the <em>total</em> number of pages.
Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The name of the database to back up.
Either <code class="docutils literal notranslate"><span class="pre">&quot;main&quot;</span></code> (the default) for the main database,
<code class="docutils literal notranslate"><span class="pre">&quot;temp&quot;</span></code> for the temporary database,
or the name of a custom database as attached using the
<code class="docutils literal notranslate"><span class="pre">ATTACH</span> <span class="pre">DATABASE</span></code> SQL statement.</p></li>
<li><p><strong>sleep</strong> (<a class="reference internal" href="functions.html#float" title="float"><em>float</em></a>) – The number of seconds to sleep between successive attempts
to back up remaining pages.</p></li>
</ul>
</dd>
</dl>
<p>Example 1, copy an existing database into another:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">progress</span><span class="p">(</span><span class="n">status</span><span class="p">,</span> <span class="n">remaining</span><span class="p">,</span> <span class="n">total</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;Copied </span><span class="si">{</span><span class="n">total</span><span class="o">-</span><span class="n">remaining</span><span class="si">}</span><span class="s1"> of </span><span class="si">{</span><span class="n">total</span><span class="si">}</span><span class="s1"> pages...&#39;</span><span class="p">)</span>

<span class="n">src</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s1">&#39;example.db&#39;</span><span class="p">)</span>
<span class="n">dst</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s1">&#39;backup.db&#39;</span><span class="p">)</span>
<span class="k">with</span> <span class="n">dst</span><span class="p">:</span>
    <span class="n">src</span><span class="o">.</span><span class="n">backup</span><span class="p">(</span><span class="n">dst</span><span class="p">,</span> <span class="n">pages</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">progress</span><span class="o">=</span><span class="n">progress</span><span class="p">)</span>
<span class="n">dst</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">src</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>Example 2, copy an existing database into a transient copy:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">src</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s1">&#39;example.db&#39;</span><span class="p">)</span>
<span class="n">dst</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s1">&#39;:memory:&#39;</span><span class="p">)</span>
<span class="n">src</span><span class="o">.</span><span class="n">backup</span><span class="p">(</span><span class="n">dst</span><span class="p">)</span>
<span class="n">dst</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">src</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#sqlite3-howto-encoding"><span class="std std-ref">How to handle non-UTF-8 text encodings</span></a></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.getlimit">
<span class="sig-name descname"><span class="pre">getlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.getlimit" title="Link to this definition">¶</a></dt>
<dd><p>Get a connection runtime limit.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>category</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The <a class="reference external" href="https://www.sqlite.org/c3ref/c_limit_attached.html">SQLite limit category</a> to be queried.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="functions.html#int" title="int">int</a></p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><strong>ProgrammingError</strong></a> – If <em>category</em> is not recognised by the underlying SQLite library.</p>
</dd>
</dl>
<p>Example, query the maximum length of an SQL statement
for <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> <code class="docutils literal notranslate"><span class="pre">con</span></code> (the default is 1000000000):</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">getlimit</span><span class="p">(</span><span class="n">sqlite3</span><span class="o">.</span><span class="n">SQLITE_LIMIT_SQL_LENGTH</span><span class="p">)</span>
<span class="go">1000000000</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.setlimit">
<span class="sig-name descname"><span class="pre">setlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">category</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.setlimit" title="Link to this definition">¶</a></dt>
<dd><p>Set a connection runtime limit.
Attempts to increase a limit above its hard upper bound are silently
truncated to the hard upper bound. Regardless of whether or not the limit
was changed, the prior value of the limit is returned.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>category</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The <a class="reference external" href="https://www.sqlite.org/c3ref/c_limit_attached.html">SQLite limit category</a> to be set.</p></li>
<li><p><strong>limit</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The value of the new limit.
If negative, the current limit is unchanged.</p></li>
</ul>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="functions.html#int" title="int">int</a></p>
</dd>
<dt class="field-odd">Raises<span class="colon">:</span></dt>
<dd class="field-odd"><p><a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><strong>ProgrammingError</strong></a> – If <em>category</em> is not recognised by the underlying SQLite library.</p>
</dd>
</dl>
<p>Example, limit the number of attached databases to 1
for <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> <code class="docutils literal notranslate"><span class="pre">con</span></code> (the default limit is 10):</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">setlimit</span><span class="p">(</span><span class="n">sqlite3</span><span class="o">.</span><span class="n">SQLITE_LIMIT_ATTACHED</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">getlimit</span><span class="p">(</span><span class="n">sqlite3</span><span class="o">.</span><span class="n">SQLITE_LIMIT_ATTACHED</span><span class="p">)</span>
<span class="go">1</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.getconfig">
<span class="sig-name descname"><span class="pre">getconfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">op</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.getconfig" title="Link to this definition">¶</a></dt>
<dd><p>Query a boolean connection configuration option.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>op</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – A <a class="reference internal" href="#sqlite3-dbconfig-constants"><span class="std std-ref">SQLITE_DBCONFIG code</span></a>.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="functions.html#bool" title="bool">bool</a></p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.setconfig">
<span class="sig-name descname"><span class="pre">setconfig</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">op</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">enable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.setconfig" title="Link to this definition">¶</a></dt>
<dd><p>Set a boolean connection configuration option.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>op</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – A <a class="reference internal" href="#sqlite3-dbconfig-constants"><span class="std std-ref">SQLITE_DBCONFIG code</span></a>.</p></li>
<li><p><strong>enable</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – <code class="docutils literal notranslate"><span class="pre">True</span></code> if the configuration option should be enabled (default);
<code class="docutils literal notranslate"><span class="pre">False</span></code> if it should be disabled.</p></li>
</ul>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.serialize">
<span class="sig-name descname"><span class="pre">serialize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'main'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.serialize" title="Link to this definition">¶</a></dt>
<dd><p>Serialize a database into a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.  For an
ordinary on-disk database file, the serialization is just a copy of the
disk file.  For an in-memory database or a “temp” database, the
serialization is the same sequence of bytes which would be written to
disk if that database were backed up to disk.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The database name to be serialized.
Defaults to <code class="docutils literal notranslate"><span class="pre">&quot;main&quot;</span></code>.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="stdtypes.html#bytes" title="bytes">bytes</a></p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This method is only available if the underlying SQLite library has the
serialize API.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Connection.deserialize">
<span class="sig-name descname"><span class="pre">deserialize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'main'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Connection.deserialize" title="Link to this definition">¶</a></dt>
<dd><p>Deserialize a <a class="reference internal" href="#sqlite3.Connection.serialize" title="sqlite3.Connection.serialize"><code class="xref py py-meth docutils literal notranslate"><span class="pre">serialized</span></code></a> database into a
<a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a>.
This method causes the database connection to disconnect from database
<em>name</em>, and reopen <em>name</em> as an in-memory database based on the
serialization contained in <em>data</em>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>data</strong> (<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><em>bytes</em></a>) – A serialized database.</p></li>
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – The database name to deserialize into.
Defaults to <code class="docutils literal notranslate"><span class="pre">&quot;main&quot;</span></code>.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><a class="reference internal" href="#sqlite3.OperationalError" title="sqlite3.OperationalError"><strong>OperationalError</strong></a> – If the database connection is currently involved in a read
transaction or a backup operation.</p></li>
<li><p><a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><strong>DatabaseError</strong></a> – If <em>data</em> does not contain a valid SQLite database.</p></li>
<li><p><a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><strong>OverflowError</strong></a> – If <a class="reference internal" href="functions.html#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len(data)</span></code></a> is larger than <code class="docutils literal notranslate"><span class="pre">2**63</span> <span class="pre">-</span> <span class="pre">1</span></code>.</p></li>
</ul>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This method is only available if the underlying SQLite library has the
deserialize API.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.autocommit">
<span class="sig-name descname"><span class="pre">autocommit</span></span><a class="headerlink" href="#sqlite3.Connection.autocommit" title="Link to this definition">¶</a></dt>
<dd><p>This attribute controls <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>-compliant transaction behaviour.
<code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code> has three allowed values:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">False</span></code>: Select <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>-compliant transaction behaviour,
implying that <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> ensures a transaction is always open.
Use <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">commit()</span></code></a> and <a class="reference internal" href="#sqlite3.Connection.rollback" title="sqlite3.Connection.rollback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rollback()</span></code></a> to close transactions.</p>
<p>This is the recommended value of <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code>.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">True</span></code>: Use SQLite’s <a class="reference external" href="https://www.sqlite.org/lang_transaction.html#implicit_versus_explicit_transactions">autocommit mode</a>.
<a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">commit()</span></code></a> and <a class="reference internal" href="#sqlite3.Connection.rollback" title="sqlite3.Connection.rollback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rollback()</span></code></a> have no effect in this mode.</p></li>
<li><p><a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>:
Pre-Python 3.12 (non-<span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>-compliant) transaction control.
See <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a> for more details.</p>
<p>This is currently the default value of <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code>.</p>
</li>
</ul>
<p>Changing <code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code> to <code class="docutils literal notranslate"><span class="pre">False</span></code> will open a new transaction,
and changing it to <code class="docutils literal notranslate"><span class="pre">True</span></code> will commit any pending transaction.</p>
<p>See <a class="reference internal" href="#sqlite3-transaction-control-autocommit"><span class="std std-ref">Transaction control via the autocommit attribute</span></a> for more details.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a> attribute has no effect unless
<a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.in_transaction">
<span class="sig-name descname"><span class="pre">in_transaction</span></span><a class="headerlink" href="#sqlite3.Connection.in_transaction" title="Link to this definition">¶</a></dt>
<dd><p>This read-only attribute corresponds to the low-level SQLite
<a class="reference external" href="https://www.sqlite.org/lang_transaction.html#implicit_versus_explicit_transactions">autocommit mode</a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">True</span></code> if a transaction is active (there are uncommitted changes),
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.isolation_level">
<span class="sig-name descname"><span class="pre">isolation_level</span></span><a class="headerlink" href="#sqlite3.Connection.isolation_level" title="Link to this definition">¶</a></dt>
<dd><p>Controls the <a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">legacy transaction handling mode</span></a> of <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>.
If set to <code class="docutils literal notranslate"><span class="pre">None</span></code>, transactions are never implicitly opened.
If set to one of <code class="docutils literal notranslate"><span class="pre">&quot;DEFERRED&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;IMMEDIATE&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;EXCLUSIVE&quot;</span></code>,
corresponding to the underlying <a class="reference external" href="https://www.sqlite.org/lang_transaction.html#deferred_immediate_and_exclusive_transactions">SQLite transaction behaviour</a>,
<a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">implicit transaction management</span></a> is performed.</p>
<p>If not overridden by the <em>isolation_level</em> parameter of <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a>,
the default is <code class="docutils literal notranslate"><span class="pre">&quot;&quot;</span></code>, which is an alias for <code class="docutils literal notranslate"><span class="pre">&quot;DEFERRED&quot;</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> to control transaction handling is
recommended over using <code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code>.
<code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code> has no effect unless <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is
set to <a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a> (the default).</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.row_factory">
<span class="sig-name descname"><span class="pre">row_factory</span></span><a class="headerlink" href="#sqlite3.Connection.row_factory" title="Link to this definition">¶</a></dt>
<dd><p>The initial <a class="reference internal" href="#sqlite3.Cursor.row_factory" title="sqlite3.Cursor.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code></a>
for <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> objects created from this connection.
Assigning to this attribute does not affect the <code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code>
of existing cursors belonging to this connection, only new ones.
Is <code class="docutils literal notranslate"><span class="pre">None</span></code> by default,
meaning each row is returned as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.</p>
<p>See <a class="reference internal" href="#sqlite3-howto-row-factory"><span class="std std-ref">How to create and use row factories</span></a> for more details.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.text_factory">
<span class="sig-name descname"><span class="pre">text_factory</span></span><a class="headerlink" href="#sqlite3.Connection.text_factory" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> that accepts a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> parameter
and returns a text representation of it.
The callable is invoked for SQLite values with the <code class="docutils literal notranslate"><span class="pre">TEXT</span></code> data type.
By default, this attribute is set to <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.</p>
<p>See <a class="reference internal" href="#sqlite3-howto-encoding"><span class="std std-ref">How to handle non-UTF-8 text encodings</span></a> for more details.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Connection.total_changes">
<span class="sig-name descname"><span class="pre">total_changes</span></span><a class="headerlink" href="#sqlite3.Connection.total_changes" title="Link to this definition">¶</a></dt>
<dd><p>Return the total number of database rows that have been modified, inserted, or
deleted since the database connection was opened.</p>
</dd></dl>

</dd></dl>

</section>
<section id="cursor-objects">
<span id="sqlite3-cursor-objects"></span><h3>Cursor objects<a class="headerlink" href="#cursor-objects" title="Link to this heading">¶</a></h3>
<blockquote>
<div><p>A <code class="docutils literal notranslate"><span class="pre">Cursor</span></code> object represents a <a class="reference external" href="https://en.wikipedia.org/wiki/Cursor_(databases)">database cursor</a>
which is used to execute SQL statements,
and manage the context of a fetch operation.
Cursors are created using <a class="reference internal" href="#sqlite3.Connection.cursor" title="sqlite3.Connection.cursor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.cursor()</span></code></a>,
or by using any of the <a class="reference internal" href="#sqlite3-connection-shortcuts"><span class="std std-ref">connection shortcut methods</span></a>.</p>
<p>Cursor objects are <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterators</span></a>,
meaning that if you <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> a <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> query,
you can simply iterate over the cursor to fetch the resulting rows:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT t FROM data&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
</pre></div>
</div>
</div></blockquote>
<dl class="py class">
<dt class="sig sig-object py" id="sqlite3.Cursor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Cursor</span></span><a class="headerlink" href="#sqlite3.Cursor" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> instance has the following attributes and methods.</p>
<dl class="py method" id="index-7">
<span id="index-6"></span><dt class="sig sig-object py" id="sqlite3.Cursor.execute">
<span class="sig-name descname"><span class="pre">execute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parameters</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.execute" title="Link to this definition">¶</a></dt>
<dd><p>Execute a single SQL statement,
optionally binding Python values using
<a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">placeholders</span></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sql</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – A single SQL statement.</p></li>
<li><p><strong>parameters</strong> (<a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> | <a class="reference internal" href="../glossary.html#term-sequence"><span class="xref std std-term">sequence</span></a>) – Python values to bind to placeholders in <em>sql</em>.
A <code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code> if named placeholders are used.
A <span class="xref std std-term">sequence</span> if unnamed placeholders are used.
See <a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">How to use placeholders to bind values in SQL queries</span></a>.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><strong>ProgrammingError</strong></a> – If <em>sql</em> contains more than one SQL statement.</p>
</dd>
</dl>
<p>If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is
<a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>,
<a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>,
<em>sql</em> is an <code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, or <code class="docutils literal notranslate"><span class="pre">REPLACE</span></code> statement,
and there is no open transaction,
a transaction is implicitly opened before executing <em>sql</em>.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span><a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> is emitted if
<a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">named placeholders</span></a> are used
and <em>parameters</em> is a sequence instead of a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.
Starting with Python 3.14, <a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProgrammingError</span></code></a> will
be raised instead.</p>
</div>
<p>Use <a class="reference internal" href="#sqlite3.Cursor.executescript" title="sqlite3.Cursor.executescript"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executescript()</span></code></a> to execute multiple SQL statements.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.executemany">
<span class="sig-name descname"><span class="pre">executemany</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parameters</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.executemany" title="Link to this definition">¶</a></dt>
<dd><p>For every item in <em>parameters</em>,
repeatedly execute the <a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">parameterized</span></a>
<abbr title="Data Manipulation Language">DML</abbr> SQL statement <em>sql</em>.</p>
<p>Uses the same implicit transaction handling as <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sql</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a>) – A single SQL DML statement.</p></li>
<li><p><strong>parameters</strong> (<a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a>) – An <span class="xref std std-term">iterable</span> of parameters to bind with
the placeholders in <em>sql</em>.
See <a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">How to use placeholders to bind values in SQL queries</span></a>.</p></li>
</ul>
</dd>
<dt class="field-even">Raises<span class="colon">:</span></dt>
<dd class="field-even"><p><a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><strong>ProgrammingError</strong></a> – If <em>sql</em> contains more than one SQL statement,
or is not a DML statement.</p>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">rows</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="s2">&quot;row1&quot;</span><span class="p">,),</span>
    <span class="p">(</span><span class="s2">&quot;row2&quot;</span><span class="p">,),</span>
<span class="p">]</span>
<span class="c1"># cur is an sqlite3.Cursor object</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO data VALUES(?)&quot;</span><span class="p">,</span> <span class="n">rows</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Any resulting rows are discarded,
including DML statements with <a class="reference external" href="https://www.sqlite.org/lang_returning.html">RETURNING clauses</a>.</p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span><a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> is emitted if
<a class="reference internal" href="#sqlite3-placeholders"><span class="std std-ref">named placeholders</span></a> are used
and the items in <em>parameters</em> are sequences
instead of <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>s.
Starting with Python 3.14, <a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProgrammingError</span></code></a> will
be raised instead.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.executescript">
<span class="sig-name descname"><span class="pre">executescript</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sql_script</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.executescript" title="Link to this definition">¶</a></dt>
<dd><p>Execute the SQL statements in <em>sql_script</em>.
If the <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is
<a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>
and there is a pending transaction,
an implicit <code class="docutils literal notranslate"><span class="pre">COMMIT</span></code> statement is executed first.
No other implicit transaction control is performed;
any transaction control must be added to <em>sql_script</em>.</p>
<p><em>sql_script</em> must be a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">string</span></code></a>.</p>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># cur is an sqlite3.Cursor object</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executescript</span><span class="p">(</span><span class="s2">&quot;&quot;&quot;</span>
<span class="s2">    BEGIN;</span>
<span class="s2">    CREATE TABLE person(firstname, lastname, age);</span>
<span class="s2">    CREATE TABLE book(title, author, published);</span>
<span class="s2">    CREATE TABLE publisher(name, address);</span>
<span class="s2">    COMMIT;</span>
<span class="s2">&quot;&quot;&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.fetchone">
<span class="sig-name descname"><span class="pre">fetchone</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.fetchone" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#sqlite3.Cursor.row_factory" title="sqlite3.Cursor.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code></a> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
return the next row query result set as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.
Else, pass it to the row factory and return its result.
Return <code class="docutils literal notranslate"><span class="pre">None</span></code> if no more data is available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.fetchmany">
<span class="sig-name descname"><span class="pre">fetchmany</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">cursor.arraysize</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.fetchmany" title="Link to this definition">¶</a></dt>
<dd><p>Return the next set of rows of a query result as a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>.
Return an empty list if no more rows are available.</p>
<p>The number of rows to fetch per call is specified by the <em>size</em> parameter.
If <em>size</em> is not given, <a class="reference internal" href="#sqlite3.Cursor.arraysize" title="sqlite3.Cursor.arraysize"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arraysize</span></code></a> determines the number of rows
to be fetched.
If fewer than <em>size</em> rows are available,
as many rows as are available are returned.</p>
<p>Note there are performance considerations involved with the <em>size</em> parameter.
For optimal performance, it is usually best to use the arraysize attribute.
If the <em>size</em> parameter is used, then it is best for it to retain the same
value from one <a class="reference internal" href="#sqlite3.Cursor.fetchmany" title="sqlite3.Cursor.fetchmany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fetchmany()</span></code></a> call to the next.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.fetchall">
<span class="sig-name descname"><span class="pre">fetchall</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.fetchall" title="Link to this definition">¶</a></dt>
<dd><p>Return all (remaining) rows of a query result as a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>.
Return an empty list if no rows are available.
Note that the <a class="reference internal" href="#sqlite3.Cursor.arraysize" title="sqlite3.Cursor.arraysize"><code class="xref py py-attr docutils literal notranslate"><span class="pre">arraysize</span></code></a> attribute can affect the performance of
this operation.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the cursor now (rather than whenever <code class="docutils literal notranslate"><span class="pre">__del__</span></code> is called).</p>
<p>The cursor will be unusable from this point forward; a <a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProgrammingError</span></code></a>
exception will be raised if any operation is attempted with the cursor.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.setinputsizes">
<span class="sig-name descname"><span class="pre">setinputsizes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sizes</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.setinputsizes" title="Link to this definition">¶</a></dt>
<dd><p>Required by the DB-API. Does nothing in <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Cursor.setoutputsize">
<span class="sig-name descname"><span class="pre">setoutputsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">column</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Cursor.setoutputsize" title="Link to this definition">¶</a></dt>
<dd><p>Required by the DB-API. Does nothing in <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.arraysize">
<span class="sig-name descname"><span class="pre">arraysize</span></span><a class="headerlink" href="#sqlite3.Cursor.arraysize" title="Link to this definition">¶</a></dt>
<dd><p>Read/write attribute that controls the number of rows returned by <a class="reference internal" href="#sqlite3.Cursor.fetchmany" title="sqlite3.Cursor.fetchmany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fetchmany()</span></code></a>.
The default value is 1 which means a single row would be fetched per call.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.connection">
<span class="sig-name descname"><span class="pre">connection</span></span><a class="headerlink" href="#sqlite3.Cursor.connection" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute that provides the SQLite database <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a>
belonging to the cursor.  A <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object created by
calling <a class="reference internal" href="#sqlite3.Connection.cursor" title="sqlite3.Connection.cursor"><code class="xref py py-meth docutils literal notranslate"><span class="pre">con.cursor()</span></code></a> will have a
<a class="reference internal" href="#sqlite3.Cursor.connection" title="sqlite3.Cursor.connection"><code class="xref py py-attr docutils literal notranslate"><span class="pre">connection</span></code></a> attribute that refers to <em>con</em>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">cursor</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cur</span><span class="o">.</span><span class="n">connection</span> <span class="o">==</span> <span class="n">con</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.description">
<span class="sig-name descname"><span class="pre">description</span></span><a class="headerlink" href="#sqlite3.Cursor.description" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute that provides the column names of the last query. To
remain compatible with the Python DB API, it returns a 7-tuple for each
column where the last six items of each tuple are <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>It is set for <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> statements without any matching rows as well.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.lastrowid">
<span class="sig-name descname"><span class="pre">lastrowid</span></span><a class="headerlink" href="#sqlite3.Cursor.lastrowid" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute that provides the row id of the last inserted row. It
is only updated after successful <code class="docutils literal notranslate"><span class="pre">INSERT</span></code> or <code class="docutils literal notranslate"><span class="pre">REPLACE</span></code> statements
using the <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> method.  For other statements, after
<a class="reference internal" href="#sqlite3.Cursor.executemany" title="sqlite3.Cursor.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executemany()</span></code></a> or <a class="reference internal" href="#sqlite3.Cursor.executescript" title="sqlite3.Cursor.executescript"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executescript()</span></code></a>, or if the insertion failed,
the value of <code class="docutils literal notranslate"><span class="pre">lastrowid</span></code> is left unchanged.  The initial value of
<code class="docutils literal notranslate"><span class="pre">lastrowid</span></code> is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Inserts into <code class="docutils literal notranslate"><span class="pre">WITHOUT</span> <span class="pre">ROWID</span></code> tables are not recorded.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added support for the <code class="docutils literal notranslate"><span class="pre">REPLACE</span></code> statement.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.rowcount">
<span class="sig-name descname"><span class="pre">rowcount</span></span><a class="headerlink" href="#sqlite3.Cursor.rowcount" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute that provides the number of modified rows for
<code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, and <code class="docutils literal notranslate"><span class="pre">REPLACE</span></code> statements;
is <code class="docutils literal notranslate"><span class="pre">-1</span></code> for other statements,
including <abbr title="Common Table Expression">CTE</abbr> queries.
It is only updated by the <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> and <a class="reference internal" href="#sqlite3.Cursor.executemany" title="sqlite3.Cursor.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executemany()</span></code></a> methods,
after the statement has run to completion.
This means that any resulting rows must be fetched in order for
<code class="xref py py-attr docutils literal notranslate"><span class="pre">rowcount</span></code> to be updated.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Cursor.row_factory">
<span class="sig-name descname"><span class="pre">row_factory</span></span><a class="headerlink" href="#sqlite3.Cursor.row_factory" title="Link to this definition">¶</a></dt>
<dd><p>Control how a row fetched from this <code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code> is represented.
If <code class="docutils literal notranslate"><span class="pre">None</span></code>, a row is represented as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.
Can be set to the included <a class="reference internal" href="#sqlite3.Row" title="sqlite3.Row"><code class="xref py py-class docutils literal notranslate"><span class="pre">sqlite3.Row</span></code></a>;
or a <a class="reference internal" href="../glossary.html#term-callable"><span class="xref std std-term">callable</span></a> that accepts two arguments,
a <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> object and the <code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code> of row values,
and returns a custom object representing an SQLite row.</p>
<p>Defaults to what <a class="reference internal" href="#sqlite3.Connection.row_factory" title="sqlite3.Connection.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.row_factory</span></code></a> was set to
when the <code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code> was created.
Assigning to this attribute does not affect
<a class="reference internal" href="#sqlite3.Connection.row_factory" title="sqlite3.Connection.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.row_factory</span></code></a> of the parent connection.</p>
<p>See <a class="reference internal" href="#sqlite3-howto-row-factory"><span class="std std-ref">How to create and use row factories</span></a> for more details.</p>
</dd></dl>

</dd></dl>

</section>
<section id="row-objects">
<span id="sqlite3-row-objects"></span><span id="sqlite3-columns-by-name"></span><h3>Row objects<a class="headerlink" href="#row-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="sqlite3.Row">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Row</span></span><a class="headerlink" href="#sqlite3.Row" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">Row</span></code> instance serves as a highly optimized
<a class="reference internal" href="#sqlite3.Connection.row_factory" title="sqlite3.Connection.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code></a> for <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> objects.
It supports iteration, equality testing, <a class="reference internal" href="functions.html#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len()</span></code></a>,
and <a class="reference internal" href="../glossary.html#term-mapping"><span class="xref std std-term">mapping</span></a> access by column name and index.</p>
<p>Two <code class="xref py py-class docutils literal notranslate"><span class="pre">Row</span></code> objects compare equal
if they have identical column names and values.</p>
<p>See <a class="reference internal" href="#sqlite3-howto-row-factory"><span class="std std-ref">How to create and use row factories</span></a> for more details.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Row.keys">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Row.keys" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> of column names as <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">strings</span></code></a>.
Immediately after a query,
it is the first member of each tuple in <a class="reference internal" href="#sqlite3.Cursor.description" title="sqlite3.Cursor.description"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Cursor.description</span></code></a>.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added support of slicing.</p>
</div>
</dd></dl>

</section>
<section id="blob-objects">
<span id="sqlite3-blob-objects"></span><h3>Blob objects<a class="headerlink" href="#blob-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="sqlite3.Blob">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Blob</span></span><a class="headerlink" href="#sqlite3.Blob" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<p>A <a class="reference internal" href="#sqlite3.Blob" title="sqlite3.Blob"><code class="xref py py-class docutils literal notranslate"><span class="pre">Blob</span></code></a> instance is a <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a>
that can read and write data in an SQLite <abbr title="Binary Large OBject">BLOB</abbr>.
Call <a class="reference internal" href="functions.html#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len(blob)</span></code></a> to get the size (number of bytes) of the blob.
Use indices and <a class="reference internal" href="../glossary.html#term-slice"><span class="xref std std-term">slices</span></a> for direct access to the blob data.</p>
<p>Use the <a class="reference internal" href="#sqlite3.Blob" title="sqlite3.Blob"><code class="xref py py-class docutils literal notranslate"><span class="pre">Blob</span></code></a> as a <a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> to ensure that the blob
handle is closed after use.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(blob_col blob)&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(blob_col) VALUES(zeroblob(13))&quot;</span><span class="p">)</span>

<span class="c1"># Write to our blob, using two write operations:</span>
<span class="k">with</span> <span class="n">con</span><span class="o">.</span><span class="n">blobopen</span><span class="p">(</span><span class="s2">&quot;test&quot;</span><span class="p">,</span> <span class="s2">&quot;blob_col&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span> <span class="k">as</span> <span class="n">blob</span><span class="p">:</span>
    <span class="n">blob</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;hello, &quot;</span><span class="p">)</span>
    <span class="n">blob</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;world.&quot;</span><span class="p">)</span>
    <span class="c1"># Modify the first and last bytes of our blob</span>
    <span class="n">blob</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="nb">ord</span><span class="p">(</span><span class="s2">&quot;H&quot;</span><span class="p">)</span>
    <span class="n">blob</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="nb">ord</span><span class="p">(</span><span class="s2">&quot;!&quot;</span><span class="p">)</span>

<span class="c1"># Read the contents of our blob</span>
<span class="k">with</span> <span class="n">con</span><span class="o">.</span><span class="n">blobopen</span><span class="p">(</span><span class="s2">&quot;test&quot;</span><span class="p">,</span> <span class="s2">&quot;blob_col&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span> <span class="k">as</span> <span class="n">blob</span><span class="p">:</span>
    <span class="n">greeting</span> <span class="o">=</span> <span class="n">blob</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="n">greeting</span><span class="p">)</span>  <span class="c1"># outputs &quot;b&#39;Hello, world!&#39;&quot;</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Blob.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Blob.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the blob.</p>
<p>The blob will be unusable from this point onward.  An
<a class="reference internal" href="#sqlite3.Error" title="sqlite3.Error"><code class="xref py py-class docutils literal notranslate"><span class="pre">Error</span></code></a> (or subclass) exception will be raised if any
further operation is attempted with the blob.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Blob.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">length</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Blob.read" title="Link to this definition">¶</a></dt>
<dd><p>Read <em>length</em> bytes of data from the blob at the current offset position.
If the end of the blob is reached, the data up to
<abbr title="End of File">EOF</abbr> will be returned.  When <em>length</em> is not
specified, or is negative, <a class="reference internal" href="#sqlite3.Blob.read" title="sqlite3.Blob.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> will read until the end of
the blob.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Blob.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Blob.write" title="Link to this definition">¶</a></dt>
<dd><p>Write <em>data</em> to the blob at the current offset.  This function cannot
change the blob length.  Writing beyond the end of the blob will raise
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Blob.tell">
<span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Blob.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return the current access position of the blob.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sqlite3.Blob.seek">
<span class="sig-name descname"><span class="pre">seek</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">offset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">os.SEEK_SET</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sqlite3.Blob.seek" title="Link to this definition">¶</a></dt>
<dd><p>Set the current access position of the blob to <em>offset</em>.  The <em>origin</em>
argument defaults to <a class="reference internal" href="os.html#os.SEEK_SET" title="os.SEEK_SET"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.SEEK_SET</span></code></a> (absolute blob positioning).
Other values for <em>origin</em> are <a class="reference internal" href="os.html#os.SEEK_CUR" title="os.SEEK_CUR"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.SEEK_CUR</span></code></a> (seek relative to the
current position) and <a class="reference internal" href="os.html#os.SEEK_END" title="os.SEEK_END"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.SEEK_END</span></code></a> (seek relative to the blob’s
end).</p>
</dd></dl>

</dd></dl>

</section>
<section id="prepareprotocol-objects">
<h3>PrepareProtocol objects<a class="headerlink" href="#prepareprotocol-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="sqlite3.PrepareProtocol">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">PrepareProtocol</span></span><a class="headerlink" href="#sqlite3.PrepareProtocol" title="Link to this definition">¶</a></dt>
<dd><p>The PrepareProtocol type’s single purpose is to act as a <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0246/"><strong>PEP 246</strong></a> style
adaption protocol for objects that can <a class="reference internal" href="#sqlite3-conform"><span class="std std-ref">adapt themselves</span></a> to <a class="reference internal" href="#sqlite3-types"><span class="std std-ref">native SQLite types</span></a>.</p>
</dd></dl>

</section>
<section id="exceptions">
<span id="sqlite3-exceptions"></span><h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<p>The exception hierarchy is defined by the DB-API 2.0 (<span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>).</p>
<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.Warning">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Warning</span></span><a class="headerlink" href="#sqlite3.Warning" title="Link to this definition">¶</a></dt>
<dd><p>This exception is not currently raised by the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module,
but may be raised by applications using <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>,
for example if a user-defined function truncates data while inserting.
<code class="docutils literal notranslate"><span class="pre">Warning</span></code> is a subclass of <a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#sqlite3.Error" title="Link to this definition">¶</a></dt>
<dd><p>The base class of the other exceptions in this module.
Use this to catch all errors with one single <a class="reference internal" href="../reference/compound_stmts.html#except"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">except</span></code></a> statement.
<code class="docutils literal notranslate"><span class="pre">Error</span></code> is a subclass of <a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Exception</span></code></a>.</p>
<p>If the exception originated from within the SQLite library,
the following two attributes are added to the exception:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Error.sqlite_errorcode">
<span class="sig-name descname"><span class="pre">sqlite_errorcode</span></span><a class="headerlink" href="#sqlite3.Error.sqlite_errorcode" title="Link to this definition">¶</a></dt>
<dd><p>The numeric error code from the
<a class="reference external" href="https://sqlite.org/rescode.html">SQLite API</a></p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sqlite3.Error.sqlite_errorname">
<span class="sig-name descname"><span class="pre">sqlite_errorname</span></span><a class="headerlink" href="#sqlite3.Error.sqlite_errorname" title="Link to this definition">¶</a></dt>
<dd><p>The symbolic name of the numeric error code
from the <a class="reference external" href="https://sqlite.org/rescode.html">SQLite API</a></p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.InterfaceError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">InterfaceError</span></span><a class="headerlink" href="#sqlite3.InterfaceError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised for misuse of the low-level SQLite C API.
In other words, if this exception is raised, it probably indicates a bug in the
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module.
<code class="docutils literal notranslate"><span class="pre">InterfaceError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.Error" title="sqlite3.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.DatabaseError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">DatabaseError</span></span><a class="headerlink" href="#sqlite3.DatabaseError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised for errors that are related to the database.
This serves as the base exception for several types of database errors.
It is only raised implicitly through the specialised subclasses.
<code class="docutils literal notranslate"><span class="pre">DatabaseError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.Error" title="sqlite3.Error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Error</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.DataError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">DataError</span></span><a class="headerlink" href="#sqlite3.DataError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised for errors caused by problems with the processed data,
like numeric values out of range, and strings which are too long.
<code class="docutils literal notranslate"><span class="pre">DataError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.OperationalError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">OperationalError</span></span><a class="headerlink" href="#sqlite3.OperationalError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised for errors that are related to the database’s operation,
and not necessarily under the control of the programmer.
For example, the database path is not found,
or a transaction could not be processed.
<code class="docutils literal notranslate"><span class="pre">OperationalError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.IntegrityError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">IntegrityError</span></span><a class="headerlink" href="#sqlite3.IntegrityError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when the relational integrity of the database is affected,
e.g. a foreign key check fails.  It is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.InternalError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">InternalError</span></span><a class="headerlink" href="#sqlite3.InternalError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when SQLite encounters an internal error.
If this is raised, it may indicate that there is a problem with the runtime
SQLite library.
<code class="docutils literal notranslate"><span class="pre">InternalError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.ProgrammingError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">ProgrammingError</span></span><a class="headerlink" href="#sqlite3.ProgrammingError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised for <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> API programming errors,
for example supplying the wrong number of bindings to a query,
or trying to operate on a closed <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a>.
<code class="docutils literal notranslate"><span class="pre">ProgrammingError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="sqlite3.NotSupportedError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sqlite3.</span></span><span class="sig-name descname"><span class="pre">NotSupportedError</span></span><a class="headerlink" href="#sqlite3.NotSupportedError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised in case a method or database API is not supported by the
underlying SQLite library. For example, setting <em>deterministic</em> to
<code class="docutils literal notranslate"><span class="pre">True</span></code> in <a class="reference internal" href="#sqlite3.Connection.create_function" title="sqlite3.Connection.create_function"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_function()</span></code></a>, if the underlying SQLite library
does not support deterministic functions.
<code class="docutils literal notranslate"><span class="pre">NotSupportedError</span></code> is a subclass of <a class="reference internal" href="#sqlite3.DatabaseError" title="sqlite3.DatabaseError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DatabaseError</span></code></a>.</p>
</dd></dl>

</section>
<section id="sqlite-and-python-types">
<span id="sqlite3-types"></span><h3>SQLite and Python types<a class="headerlink" href="#sqlite-and-python-types" title="Link to this heading">¶</a></h3>
<p>SQLite natively supports the following types: <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <code class="docutils literal notranslate"><span class="pre">INTEGER</span></code>,
<code class="docutils literal notranslate"><span class="pre">REAL</span></code>, <code class="docutils literal notranslate"><span class="pre">TEXT</span></code>, <code class="docutils literal notranslate"><span class="pre">BLOB</span></code>.</p>
<p>The following Python types can thus be sent to SQLite without any problem:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python type</p></th>
<th class="head"><p>SQLite type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">BLOB</span></code></p></td>
</tr>
</tbody>
</table>
<p>This is how SQLite types are converted to Python types by default:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>SQLite type</p></th>
<th class="head"><p>Python type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">NULL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">INTEGER</span></code></p></td>
<td><p><a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">REAL</span></code></p></td>
<td><p><a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">TEXT</span></code></p></td>
<td><p>depends on <a class="reference internal" href="#sqlite3.Connection.text_factory" title="sqlite3.Connection.text_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">text_factory</span></code></a>,
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> by default</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">BLOB</span></code></p></td>
<td><p><a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a></p></td>
</tr>
</tbody>
</table>
<p>The type system of the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module is extensible in two ways: you can
store additional Python types in an SQLite database via
<a class="reference internal" href="#sqlite3-adapters"><span class="std std-ref">object adapters</span></a>,
and you can let the <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module convert SQLite types to
Python types via <a class="reference internal" href="#sqlite3-converters"><span class="std std-ref">converters</span></a>.</p>
</section>
<section id="default-adapters-and-converters-deprecated">
<span id="sqlite3-default-converters"></span><h3>Default adapters and converters (deprecated)<a class="headerlink" href="#default-adapters-and-converters-deprecated" title="Link to this heading">¶</a></h3>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The default adapters and converters are deprecated as of Python 3.12.
Instead, use the <a class="reference internal" href="#sqlite3-adapter-converter-recipes"><span class="std std-ref">Adapter and converter recipes</span></a>
and tailor them to your needs.</p>
</div>
<p>The deprecated default adapters and converters consist of:</p>
<ul class="simple">
<li><p>An adapter for <a class="reference internal" href="datetime.html#datetime.date" title="datetime.date"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.date</span></code></a> objects to <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">strings</span></code></a> in
<a class="reference external" href="https://en.wikipedia.org/wiki/ISO_8601">ISO 8601</a> format.</p></li>
<li><p>An adapter for <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> objects to strings in
ISO 8601 format.</p></li>
<li><p>A converter for <a class="reference internal" href="#sqlite3-converters"><span class="std std-ref">declared</span></a> “date” types to
<a class="reference internal" href="datetime.html#datetime.date" title="datetime.date"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.date</span></code></a> objects.</p></li>
<li><p>A converter for declared “timestamp” types to
<a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> objects.
Fractional parts will be truncated to 6 digits (microsecond precision).</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The default “timestamp” converter ignores UTC offsets in the database and
always returns a naive <a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a> object. To preserve UTC
offsets in timestamps, either leave converters disabled, or register an
offset-aware converter with <a class="reference internal" href="#sqlite3.register_converter" title="sqlite3.register_converter"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_converter()</span></code></a>.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</section>
<section id="command-line-interface">
<span id="sqlite3-cli"></span><h3>Command-line interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h3>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module can be invoked as a script,
using the interpreter’s <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> switch,
in order to provide a simple SQLite shell.
The argument signature is as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">sqlite3</span> <span class="p">[</span><span class="o">-</span><span class="n">h</span><span class="p">]</span> <span class="p">[</span><span class="o">-</span><span class="n">v</span><span class="p">]</span> <span class="p">[</span><span class="n">filename</span><span class="p">]</span> <span class="p">[</span><span class="n">sql</span><span class="p">]</span>
</pre></div>
</div>
<p>Type <code class="docutils literal notranslate"><span class="pre">.quit</span></code> or CTRL-D to exit the shell.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-python-m-sqlite3-h-v-filename-sql-h">
<span id="cmdoption-python-m-sqlite3-h-v-filename-sql-help"></span><span class="sig-name descname"><span class="pre">-h</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--help</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-python-m-sqlite3-h-v-filename-sql-h" title="Link to this definition">¶</a></dt>
<dd><p>Print CLI help.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-python-m-sqlite3-h-v-filename-sql-v">
<span id="cmdoption-python-m-sqlite3-h-v-filename-sql-version"></span><span class="sig-name descname"><span class="pre">-v</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--version</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-python-m-sqlite3-h-v-filename-sql-v" title="Link to this definition">¶</a></dt>
<dd><p>Print underlying SQLite library version.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</section>
</section>
<section id="how-to-guides">
<span id="sqlite3-howtos"></span><h2>How-to guides<a class="headerlink" href="#how-to-guides" title="Link to this heading">¶</a></h2>
<section id="how-to-use-placeholders-to-bind-values-in-sql-queries">
<span id="sqlite3-placeholders"></span><h3>How to use placeholders to bind values in SQL queries<a class="headerlink" href="#how-to-use-placeholders-to-bind-values-in-sql-queries" title="Link to this heading">¶</a></h3>
<p>SQL operations usually need to use values from Python variables. However,
beware of using Python’s string operations to assemble queries, as they
are vulnerable to <a class="reference external" href="https://en.wikipedia.org/wiki/SQL_injection">SQL injection attacks</a>. For example, an attacker can simply
close the single quote and inject <code class="docutils literal notranslate"><span class="pre">OR</span> <span class="pre">TRUE</span></code> to select all rows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># Never do this -- insecure!</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">symbol</span> <span class="o">=</span> <span class="nb">input</span><span class="p">()</span>
<span class="go">&#39; OR TRUE; --</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sql</span> <span class="o">=</span> <span class="s2">&quot;SELECT * FROM stocks WHERE symbol = &#39;</span><span class="si">%s</span><span class="s2">&#39;&quot;</span> <span class="o">%</span> <span class="n">symbol</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">sql</span><span class="p">)</span>
<span class="go">SELECT * FROM stocks WHERE symbol = &#39;&#39; OR TRUE; --&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="n">sql</span><span class="p">)</span>
</pre></div>
</div>
<p>Instead, use the DB-API’s parameter substitution. To insert a variable into a
query string, use a placeholder in the string, and substitute the actual values
into the query by providing them as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> of values to the second
argument of the cursor’s <a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> method.</p>
<p>An SQL statement may use one of two kinds of placeholders:
question marks (qmark style) or named placeholders (named style).
For the qmark style, <em>parameters</em> must be a
<a class="reference internal" href="../glossary.html#term-sequence"><span class="xref std std-term">sequence</span></a> whose length must match the number of placeholders,
or a <a class="reference internal" href="#sqlite3.ProgrammingError" title="sqlite3.ProgrammingError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ProgrammingError</span></code></a> is raised.
For the named style, <em>parameters</em> must be
an instance of a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> (or a subclass),
which must contain keys for all named parameters;
any extra items are ignored.
Here’s an example of both styles:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE lang(name, first_appeared)&quot;</span><span class="p">)</span>

<span class="c1"># This is the named style used with executemany():</span>
<span class="n">data</span> <span class="o">=</span> <span class="p">(</span>
    <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;C&quot;</span><span class="p">,</span> <span class="s2">&quot;year&quot;</span><span class="p">:</span> <span class="mi">1972</span><span class="p">},</span>
    <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Fortran&quot;</span><span class="p">,</span> <span class="s2">&quot;year&quot;</span><span class="p">:</span> <span class="mi">1957</span><span class="p">},</span>
    <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Python&quot;</span><span class="p">,</span> <span class="s2">&quot;year&quot;</span><span class="p">:</span> <span class="mi">1991</span><span class="p">},</span>
    <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Go&quot;</span><span class="p">,</span> <span class="s2">&quot;year&quot;</span><span class="p">:</span> <span class="mi">2009</span><span class="p">},</span>
<span class="p">)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO lang VALUES(:name, :year)&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

<span class="c1"># This is the qmark style used in a SELECT query:</span>
<span class="n">params</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1972</span><span class="p">,)</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT * FROM lang WHERE first_appeared = ?&quot;</span><span class="p">,</span> <span class="n">params</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cur</span><span class="o">.</span><span class="n">fetchall</span><span class="p">())</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a> numeric placeholders are <em>not</em> supported.
If used, they will be interpreted as named placeholders.</p>
</div>
</section>
<section id="how-to-adapt-custom-python-types-to-sqlite-values">
<span id="sqlite3-adapters"></span><h3>How to adapt custom Python types to SQLite values<a class="headerlink" href="#how-to-adapt-custom-python-types-to-sqlite-values" title="Link to this heading">¶</a></h3>
<p>SQLite supports only a limited set of data types natively.
To store custom Python types in SQLite databases, <em>adapt</em> them to one of the
<a class="reference internal" href="#sqlite3-types"><span class="std std-ref">Python types SQLite natively understands</span></a>.</p>
<p>There are two ways to adapt Python objects to SQLite types:
letting your object adapt itself, or using an <em>adapter callable</em>.
The latter will take precedence above the former.
For a library that exports a custom type,
it may make sense to enable that type to adapt itself.
As an application developer, it may make more sense to take direct control by
registering custom adapter functions.</p>
<section id="how-to-write-adaptable-objects">
<span id="sqlite3-conform"></span><h4>How to write adaptable objects<a class="headerlink" href="#how-to-write-adaptable-objects" title="Link to this heading">¶</a></h4>
<p>Suppose we have a <code class="xref py py-class docutils literal notranslate"><span class="pre">Point</span></code> class that represents a pair of coordinates,
<code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code>, in a Cartesian coordinate system.
The coordinate pair will be stored as a text string in the database,
using a semicolon to separate the coordinates.
This can be implemented by adding a <code class="docutils literal notranslate"><span class="pre">__conform__(self,</span> <span class="pre">protocol)</span></code>
method which returns the adapted value.
The object passed to <em>protocol</em> will be of type <a class="reference internal" href="#sqlite3.PrepareProtocol" title="sqlite3.PrepareProtocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrepareProtocol</span></code></a>.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span>

    <span class="k">def</span> <span class="nf">__conform__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">protocol</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">protocol</span> <span class="ow">is</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">PrepareProtocol</span><span class="p">:</span>
            <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="si">}</span><span class="s2">;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="si">}</span><span class="s2">&quot;</span>

<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">cursor</span><span class="p">()</span>

<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT ?&quot;</span><span class="p">,</span> <span class="p">(</span><span class="n">Point</span><span class="p">(</span><span class="mf">4.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.2</span><span class="p">),))</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()[</span><span class="mi">0</span><span class="p">])</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="how-to-register-adapter-callables">
<h4>How to register adapter callables<a class="headerlink" href="#how-to-register-adapter-callables" title="Link to this heading">¶</a></h4>
<p>The other possibility is to create a function that converts the Python object
to an SQLite-compatible type.
This function can then be registered using <a class="reference internal" href="#sqlite3.register_adapter" title="sqlite3.register_adapter"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_adapter()</span></code></a>.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span>

<span class="k">def</span> <span class="nf">adapt_point</span><span class="p">(</span><span class="n">point</span><span class="p">):</span>
    <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">point</span><span class="o">.</span><span class="n">x</span><span class="si">}</span><span class="s2">;</span><span class="si">{</span><span class="n">point</span><span class="o">.</span><span class="n">y</span><span class="si">}</span><span class="s2">&quot;</span>

<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_adapter</span><span class="p">(</span><span class="n">Point</span><span class="p">,</span> <span class="n">adapt_point</span><span class="p">)</span>

<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">cursor</span><span class="p">()</span>

<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT ?&quot;</span><span class="p">,</span> <span class="p">(</span><span class="n">Point</span><span class="p">(</span><span class="mf">1.0</span><span class="p">,</span> <span class="mf">2.5</span><span class="p">),))</span>
<span class="nb">print</span><span class="p">(</span><span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()[</span><span class="mi">0</span><span class="p">])</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="how-to-convert-sqlite-values-to-custom-python-types">
<span id="sqlite3-converters"></span><h3>How to convert SQLite values to custom Python types<a class="headerlink" href="#how-to-convert-sqlite-values-to-custom-python-types" title="Link to this heading">¶</a></h3>
<p>Writing an adapter lets you convert <em>from</em> custom Python types <em>to</em> SQLite
values.
To be able to convert <em>from</em> SQLite values <em>to</em> custom Python types,
we use <em>converters</em>.</p>
<p>Let’s go back to the <code class="xref py py-class docutils literal notranslate"><span class="pre">Point</span></code> class. We stored the x and y coordinates
separated via semicolons as strings in SQLite.</p>
<p>First, we’ll define a converter function that accepts the string as a parameter
and constructs a <code class="xref py py-class docutils literal notranslate"><span class="pre">Point</span></code> object from it.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Converter functions are <strong>always</strong> passed a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object,
no matter the underlying SQLite data type.</p>
</div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">convert_point</span><span class="p">(</span><span class="n">s</span><span class="p">):</span>
    <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="nb">map</span><span class="p">(</span><span class="nb">float</span><span class="p">,</span> <span class="n">s</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;;&quot;</span><span class="p">))</span>
    <span class="k">return</span> <span class="n">Point</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
</pre></div>
</div>
<p>We now need to tell <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> when it should convert a given SQLite value.
This is done when connecting to a database, using the <em>detect_types</em> parameter
of <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a>. There are three options:</p>
<ul class="simple">
<li><p>Implicit: set <em>detect_types</em> to <a class="reference internal" href="#sqlite3.PARSE_DECLTYPES" title="sqlite3.PARSE_DECLTYPES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_DECLTYPES</span></code></a></p></li>
<li><p>Explicit: set <em>detect_types</em> to <a class="reference internal" href="#sqlite3.PARSE_COLNAMES" title="sqlite3.PARSE_COLNAMES"><code class="xref py py-const docutils literal notranslate"><span class="pre">PARSE_COLNAMES</span></code></a></p></li>
<li><p>Both: set <em>detect_types</em> to
<code class="docutils literal notranslate"><span class="pre">sqlite3.PARSE_DECLTYPES</span> <span class="pre">|</span> <span class="pre">sqlite3.PARSE_COLNAMES</span></code>.
Column names take precedence over declared types.</p></li>
</ul>
<p>The following example illustrates the implicit and explicit approaches:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">Point</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;Point(</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="si">}</span><span class="s2">, </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="si">}</span><span class="s2">)&quot;</span>

<span class="k">def</span> <span class="nf">adapt_point</span><span class="p">(</span><span class="n">point</span><span class="p">):</span>
    <span class="k">return</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">point</span><span class="o">.</span><span class="n">x</span><span class="si">}</span><span class="s2">;</span><span class="si">{</span><span class="n">point</span><span class="o">.</span><span class="n">y</span><span class="si">}</span><span class="s2">&quot;</span>

<span class="k">def</span> <span class="nf">convert_point</span><span class="p">(</span><span class="n">s</span><span class="p">):</span>
    <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="nb">float</span><span class="p">,</span> <span class="n">s</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="sa">b</span><span class="s2">&quot;;&quot;</span><span class="p">)))</span>
    <span class="k">return</span> <span class="n">Point</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>

<span class="c1"># Register the adapter and converter</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_adapter</span><span class="p">(</span><span class="n">Point</span><span class="p">,</span> <span class="n">adapt_point</span><span class="p">)</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_converter</span><span class="p">(</span><span class="s2">&quot;point&quot;</span><span class="p">,</span> <span class="n">convert_point</span><span class="p">)</span>

<span class="c1"># 1) Parse using declared types</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Point</span><span class="p">(</span><span class="mf">4.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">3.2</span><span class="p">)</span>
<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">,</span> <span class="n">detect_types</span><span class="o">=</span><span class="n">sqlite3</span><span class="o">.</span><span class="n">PARSE_DECLTYPES</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(p point)&quot;</span><span class="p">)</span>

<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(p) VALUES(?)&quot;</span><span class="p">,</span> <span class="p">(</span><span class="n">p</span><span class="p">,))</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT p FROM test&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;with declared types:&quot;</span><span class="p">,</span> <span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()[</span><span class="mi">0</span><span class="p">])</span>
<span class="n">cur</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="c1"># 2) Parse using column names</span>
<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">,</span> <span class="n">detect_types</span><span class="o">=</span><span class="n">sqlite3</span><span class="o">.</span><span class="n">PARSE_COLNAMES</span><span class="p">)</span>
<span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE test(p)&quot;</span><span class="p">)</span>

<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO test(p) VALUES(?)&quot;</span><span class="p">,</span> <span class="p">(</span><span class="n">p</span><span class="p">,))</span>
<span class="n">cur</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s1">&#39;SELECT p AS &quot;p [point]&quot; FROM test&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;with column names:&quot;</span><span class="p">,</span> <span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()[</span><span class="mi">0</span><span class="p">])</span>
<span class="n">cur</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="adapter-and-converter-recipes">
<span id="sqlite3-adapter-converter-recipes"></span><h3>Adapter and converter recipes<a class="headerlink" href="#adapter-and-converter-recipes" title="Link to this heading">¶</a></h3>
<p>This section shows recipes for common adapters and converters.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">datetime</span>
<span class="kn">import</span> <span class="nn">sqlite3</span>

<span class="k">def</span> <span class="nf">adapt_date_iso</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Adapt datetime.date to ISO 8601 date.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">val</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">adapt_datetime_iso</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Adapt datetime.datetime to timezone-naive ISO 8601 date.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">val</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">adapt_datetime_epoch</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Adapt datetime.datetime to Unix timestamp.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="nb">int</span><span class="p">(</span><span class="n">val</span><span class="o">.</span><span class="n">timestamp</span><span class="p">())</span>

<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_adapter</span><span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">date</span><span class="p">,</span> <span class="n">adapt_date_iso</span><span class="p">)</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_adapter</span><span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="p">,</span> <span class="n">adapt_datetime_iso</span><span class="p">)</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_adapter</span><span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="p">,</span> <span class="n">adapt_datetime_epoch</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">convert_date</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Convert ISO 8601 date to datetime.date object.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">datetime</span><span class="o">.</span><span class="n">date</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">val</span><span class="o">.</span><span class="n">decode</span><span class="p">())</span>

<span class="k">def</span> <span class="nf">convert_datetime</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Convert ISO 8601 datetime to datetime.datetime object.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">val</span><span class="o">.</span><span class="n">decode</span><span class="p">())</span>

<span class="k">def</span> <span class="nf">convert_timestamp</span><span class="p">(</span><span class="n">val</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Convert Unix epoch timestamp to datetime.datetime object.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">datetime</span><span class="o">.</span><span class="n">datetime</span><span class="o">.</span><span class="n">fromtimestamp</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">val</span><span class="p">))</span>

<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_converter</span><span class="p">(</span><span class="s2">&quot;date&quot;</span><span class="p">,</span> <span class="n">convert_date</span><span class="p">)</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_converter</span><span class="p">(</span><span class="s2">&quot;datetime&quot;</span><span class="p">,</span> <span class="n">convert_datetime</span><span class="p">)</span>
<span class="n">sqlite3</span><span class="o">.</span><span class="n">register_converter</span><span class="p">(</span><span class="s2">&quot;timestamp&quot;</span><span class="p">,</span> <span class="n">convert_timestamp</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="how-to-use-connection-shortcut-methods">
<span id="sqlite3-connection-shortcuts"></span><h3>How to use connection shortcut methods<a class="headerlink" href="#how-to-use-connection-shortcut-methods" title="Link to this heading">¶</a></h3>
<p>Using the <a class="reference internal" href="#sqlite3.Connection.execute" title="sqlite3.Connection.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a>,
<a class="reference internal" href="#sqlite3.Connection.executemany" title="sqlite3.Connection.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executemany()</span></code></a>, and <a class="reference internal" href="#sqlite3.Connection.executescript" title="sqlite3.Connection.executescript"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executescript()</span></code></a>
methods of the <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> class, your code can
be written more concisely because you don’t have to create the (often
superfluous) <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> objects explicitly. Instead, the <a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a>
objects are created implicitly and these shortcut methods return the cursor
objects. This way, you can execute a <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> statement and iterate over it
directly using only a single call on the <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> object.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create and fill the table.</span>
<span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE lang(name, first_appeared)&quot;</span><span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="p">[</span>
    <span class="p">(</span><span class="s2">&quot;C++&quot;</span><span class="p">,</span> <span class="mi">1985</span><span class="p">),</span>
    <span class="p">(</span><span class="s2">&quot;Objective-C&quot;</span><span class="p">,</span> <span class="mi">1984</span><span class="p">),</span>
<span class="p">]</span>
<span class="n">con</span><span class="o">.</span><span class="n">executemany</span><span class="p">(</span><span class="s2">&quot;INSERT INTO lang(name, first_appeared) VALUES(?, ?)&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

<span class="c1"># Print the table contents</span>
<span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT name, first_appeared FROM lang&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;I just deleted&quot;</span><span class="p">,</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;DELETE FROM lang&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">rowcount</span><span class="p">,</span> <span class="s2">&quot;rows&quot;</span><span class="p">)</span>

<span class="c1"># close() is not a shortcut method and it&#39;s not called automatically;</span>
<span class="c1"># the connection object should be closed manually</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="how-to-use-the-connection-context-manager">
<span id="sqlite3-connection-context-manager"></span><h3>How to use the connection context manager<a class="headerlink" href="#how-to-use-the-connection-context-manager" title="Link to this heading">¶</a></h3>
<p>A <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> object can be used as a context manager that
automatically commits or rolls back open transactions when leaving the body of
the context manager.
If the body of the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement finishes without exceptions,
the transaction is committed.
If this commit fails,
or if the body of the <code class="docutils literal notranslate"><span class="pre">with</span></code> statement raises an uncaught exception,
the transaction is rolled back.
If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <code class="docutils literal notranslate"><span class="pre">False</span></code>,
a new transaction is implicitly opened after committing or rolling back.</p>
<p>If there is no open transaction upon leaving the body of the <code class="docutils literal notranslate"><span class="pre">with</span></code> statement,
or if <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>,
the context manager does nothing.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The context manager neither implicitly opens a new transaction
nor closes the connection. If you need a closing context manager, consider
using <a class="reference internal" href="contextlib.html#contextlib.closing" title="contextlib.closing"><code class="xref py py-meth docutils literal notranslate"><span class="pre">contextlib.closing()</span></code></a>.</p>
</div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE lang(id INTEGER PRIMARY KEY, name VARCHAR UNIQUE)&quot;</span><span class="p">)</span>

<span class="c1"># Successful, con.commit() is called automatically afterwards</span>
<span class="k">with</span> <span class="n">con</span><span class="p">:</span>
    <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO lang(name) VALUES(?)&quot;</span><span class="p">,</span> <span class="p">(</span><span class="s2">&quot;Python&quot;</span><span class="p">,))</span>

<span class="c1"># con.rollback() is called after the with block finishes with an exception,</span>
<span class="c1"># the exception is still raised and must be caught</span>
<span class="k">try</span><span class="p">:</span>
    <span class="k">with</span> <span class="n">con</span><span class="p">:</span>
        <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO lang(name) VALUES(?)&quot;</span><span class="p">,</span> <span class="p">(</span><span class="s2">&quot;Python&quot;</span><span class="p">,))</span>
<span class="k">except</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">IntegrityError</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;couldn&#39;t add Python twice&quot;</span><span class="p">)</span>

<span class="c1"># Connection object used as context manager only commits or rollbacks transactions,</span>
<span class="c1"># so the connection object should be closed manually</span>
<span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="how-to-work-with-sqlite-uris">
<span id="sqlite3-uri-tricks"></span><h3>How to work with SQLite URIs<a class="headerlink" href="#how-to-work-with-sqlite-uris" title="Link to this heading">¶</a></h3>
<p>Some useful URI tricks include:</p>
<ul class="simple">
<li><p>Open a database in read-only mode:</p></li>
</ul>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;file:tutorial.db?mode=ro&quot;</span><span class="p">,</span> <span class="n">uri</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE readonly(data)&quot;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="gr">OperationalError</span>: <span class="n">attempt to write a readonly database</span>
</pre></div>
</div>
<ul class="simple">
<li><p>Do not implicitly create a new database file if it does not already exist;
will raise <a class="reference internal" href="#sqlite3.OperationalError" title="sqlite3.OperationalError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OperationalError</span></code></a> if unable to create a new file:</p></li>
</ul>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;file:nosuchdb.db?mode=rw&quot;</span><span class="p">,</span> <span class="n">uri</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="gr">OperationalError</span>: <span class="n">unable to open database file</span>
</pre></div>
</div>
<ul class="simple">
<li><p>Create a shared named in-memory database:</p></li>
</ul>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">db</span> <span class="o">=</span> <span class="s2">&quot;file:mem1?mode=memory&amp;cache=shared&quot;</span>
<span class="n">con1</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="n">db</span><span class="p">,</span> <span class="n">uri</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="n">con2</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="n">db</span><span class="p">,</span> <span class="n">uri</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="k">with</span> <span class="n">con1</span><span class="p">:</span>
    <span class="n">con1</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;CREATE TABLE shared(data)&quot;</span><span class="p">)</span>
    <span class="n">con1</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;INSERT INTO shared VALUES(28)&quot;</span><span class="p">)</span>
<span class="n">res</span> <span class="o">=</span> <span class="n">con2</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT data FROM shared&quot;</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">res</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span> <span class="o">==</span> <span class="p">(</span><span class="mi">28</span><span class="p">,)</span>

<span class="n">con1</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">con2</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>More information about this feature, including a list of parameters,
can be found in the <a class="reference external" href="https://www.sqlite.org/uri.html">SQLite URI documentation</a>.</p>
</section>
<section id="how-to-create-and-use-row-factories">
<span id="sqlite3-howto-row-factory"></span><h3>How to create and use row factories<a class="headerlink" href="#how-to-create-and-use-row-factories" title="Link to this heading">¶</a></h3>
<p>By default, <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> represents each row as a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>.
If a <code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code> does not suit your needs,
you can use the <a class="reference internal" href="#sqlite3.Row" title="sqlite3.Row"><code class="xref py py-class docutils literal notranslate"><span class="pre">sqlite3.Row</span></code></a> class
or a custom <a class="reference internal" href="#sqlite3.Cursor.row_factory" title="sqlite3.Cursor.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code></a>.</p>
<p>While <code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code> exists as an attribute both on the
<a class="reference internal" href="#sqlite3.Cursor" title="sqlite3.Cursor"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cursor</span></code></a> and the <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a>,
it is recommended to set <a class="reference internal" href="#sqlite3.Connection.row_factory" title="sqlite3.Connection.row_factory"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection.row_factory</span></code></a>,
so all cursors created from the connection will use the same row factory.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Row</span></code> provides indexed and case-insensitive named access to columns,
with minimal memory overhead and performance impact over a <code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code>.
To use <code class="xref py py-class docutils literal notranslate"><span class="pre">Row</span></code> as a row factory,
assign it to the <code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code> attribute:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">row_factory</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">Row</span>
</pre></div>
</div>
<p>Queries now return <code class="xref py py-class docutils literal notranslate"><span class="pre">Row</span></code> objects:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">res</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT &#39;Earth&#39; AS name, 6378 AS radius&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span> <span class="o">=</span> <span class="n">res</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="o">.</span><span class="n">keys</span><span class="p">()</span>
<span class="go">[&#39;name&#39;, &#39;radius&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>         <span class="c1"># Access by index.</span>
<span class="go">&#39;Earth&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span>    <span class="c1"># Access by name.</span>
<span class="go">&#39;Earth&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="p">[</span><span class="s2">&quot;RADIUS&quot;</span><span class="p">]</span>  <span class="c1"># Column names are case-insensitive.</span>
<span class="go">6378</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">FROM</span></code> clause can be omitted in the <code class="docutils literal notranslate"><span class="pre">SELECT</span></code> statement, as in the
above example. In such cases, SQLite returns a single row with columns
defined by expressions, e.g. literals, with the given aliases
<code class="docutils literal notranslate"><span class="pre">expr</span> <span class="pre">AS</span> <span class="pre">alias</span></code>.</p>
</div>
<p>You can create a custom <a class="reference internal" href="#sqlite3.Cursor.row_factory" title="sqlite3.Cursor.row_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">row_factory</span></code></a>
that returns each row as a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>, with column names mapped to values:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">dict_factory</span><span class="p">(</span><span class="n">cursor</span><span class="p">,</span> <span class="n">row</span><span class="p">):</span>
    <span class="n">fields</span> <span class="o">=</span> <span class="p">[</span><span class="n">column</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">for</span> <span class="n">column</span> <span class="ow">in</span> <span class="n">cursor</span><span class="o">.</span><span class="n">description</span><span class="p">]</span>
    <span class="k">return</span> <span class="p">{</span><span class="n">key</span><span class="p">:</span> <span class="n">value</span> <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">fields</span><span class="p">,</span> <span class="n">row</span><span class="p">)}</span>
</pre></div>
</div>
<p>Using it, queries now return a <code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code> instead of a <code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">row_factory</span> <span class="o">=</span> <span class="n">dict_factory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">row</span> <span class="ow">in</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT 1 AS a, 2 AS b&quot;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
<span class="go">{&#39;a&#39;: 1, &#39;b&#39;: 2}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>The following row factory returns a <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">collections</span> <span class="kn">import</span> <span class="n">namedtuple</span>

<span class="k">def</span> <span class="nf">namedtuple_factory</span><span class="p">(</span><span class="n">cursor</span><span class="p">,</span> <span class="n">row</span><span class="p">):</span>
    <span class="n">fields</span> <span class="o">=</span> <span class="p">[</span><span class="n">column</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">for</span> <span class="n">column</span> <span class="ow">in</span> <span class="n">cursor</span><span class="o">.</span><span class="n">description</span><span class="p">]</span>
    <span class="bp">cls</span> <span class="o">=</span> <span class="n">namedtuple</span><span class="p">(</span><span class="s2">&quot;Row&quot;</span><span class="p">,</span> <span class="n">fields</span><span class="p">)</span>
    <span class="k">return</span> <span class="bp">cls</span><span class="o">.</span><span class="n">_make</span><span class="p">(</span><span class="n">row</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple_factory()</span></code> can be used as follows:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">con</span> <span class="o">=</span> <span class="n">sqlite3</span><span class="o">.</span><span class="n">connect</span><span class="p">(</span><span class="s2">&quot;:memory:&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">row_factory</span> <span class="o">=</span> <span class="n">namedtuple_factory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cur</span> <span class="o">=</span> <span class="n">con</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="s2">&quot;SELECT 1 AS a, 2 AS b&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span> <span class="o">=</span> <span class="n">cur</span><span class="o">.</span><span class="n">fetchone</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span>
<span class="go">Row(a=1, b=2)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>  <span class="c1"># Indexed access.</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">row</span><span class="o">.</span><span class="n">b</span>   <span class="c1"># Attribute access.</span>
<span class="go">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">con</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>With some adjustments, the above recipe can be adapted to use a
<a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-class docutils literal notranslate"><span class="pre">dataclass</span></code></a>, or any other custom class,
instead of a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a>.</p>
</section>
<section id="how-to-handle-non-utf-8-text-encodings">
<span id="sqlite3-howto-encoding"></span><h3>How to handle non-UTF-8 text encodings<a class="headerlink" href="#how-to-handle-non-utf-8-text-encodings" title="Link to this heading">¶</a></h3>
<p>By default, <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> uses <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> to adapt SQLite values
with the <code class="docutils literal notranslate"><span class="pre">TEXT</span></code> data type.
This works well for UTF-8 encoded text, but it might fail for other encodings
and invalid UTF-8.
You can use a custom <a class="reference internal" href="#sqlite3.Connection.text_factory" title="sqlite3.Connection.text_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">text_factory</span></code></a> to handle such cases.</p>
<p>Because of SQLite’s <a class="reference external" href="https://www.sqlite.org/flextypegood.html">flexible typing</a>, it is not uncommon to encounter table
columns with the <code class="docutils literal notranslate"><span class="pre">TEXT</span></code> data type containing non-UTF-8 encodings,
or even arbitrary data.
To demonstrate, let’s assume we have a database with ISO-8859-2 (Latin-2)
encoded text, for example a table of Czech-English dictionary entries.
Assuming we now have a <a class="reference internal" href="#sqlite3.Connection" title="sqlite3.Connection"><code class="xref py py-class docutils literal notranslate"><span class="pre">Connection</span></code></a> instance <code class="xref py py-data docutils literal notranslate"><span class="pre">con</span></code>
connected to this database,
we can decode the Latin-2 encoded text using this <a class="reference internal" href="#sqlite3.Connection.text_factory" title="sqlite3.Connection.text_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">text_factory</span></code></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span><span class="o">.</span><span class="n">text_factory</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">data</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s2">&quot;latin2&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>For invalid UTF-8 or arbitrary data in stored in <code class="docutils literal notranslate"><span class="pre">TEXT</span></code> table columns,
you can use the following technique, borrowed from the <a class="reference internal" href="../howto/unicode.html#unicode-howto"><span class="std std-ref">Unicode HOWTO</span></a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">con</span><span class="o">.</span><span class="n">text_factory</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">data</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">errors</span><span class="o">=</span><span class="s2">&quot;surrogateescape&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> module API does not support strings
containing surrogates.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="../howto/unicode.html#unicode-howto"><span class="std std-ref">Unicode HOWTO</span></a></p>
</div>
</section>
</section>
<section id="explanation">
<span id="sqlite3-explanation"></span><h2>Explanation<a class="headerlink" href="#explanation" title="Link to this heading">¶</a></h2>
<section id="transaction-control">
<span id="sqlite3-controlling-transactions"></span><span id="sqlite3-transaction-control"></span><h3>Transaction control<a class="headerlink" href="#transaction-control" title="Link to this heading">¶</a></h3>
<p><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> offers multiple methods of controlling whether,
when and how database transactions are opened and closed.
<a class="reference internal" href="#sqlite3-transaction-control-autocommit"><span class="std std-ref">Transaction control via the autocommit attribute</span></a> is recommended,
while <a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">Transaction control via the isolation_level attribute</span></a>
retains the pre-Python 3.12 behaviour.</p>
<section id="transaction-control-via-the-autocommit-attribute">
<span id="sqlite3-transaction-control-autocommit"></span><h4>Transaction control via the <code class="docutils literal notranslate"><span class="pre">autocommit</span></code> attribute<a class="headerlink" href="#transaction-control-via-the-autocommit-attribute" title="Link to this heading">¶</a></h4>
<p>The recommended way of controlling transaction behaviour is through
the <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.autocommit</span></code></a> attribute,
which should preferably be set using the <em>autocommit</em> parameter
of <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a>.</p>
<p>It is suggested to set <em>autocommit</em> to <code class="docutils literal notranslate"><span class="pre">False</span></code>,
which implies <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>-compliant transaction control.
This means:</p>
<ul class="simple">
<li><p><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> ensures that a transaction is always open,
so <a class="reference internal" href="#sqlite3.connect" title="sqlite3.connect"><code class="xref py py-func docutils literal notranslate"><span class="pre">connect()</span></code></a>, <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.commit()</span></code></a>, and <a class="reference internal" href="#sqlite3.Connection.rollback" title="sqlite3.Connection.rollback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.rollback()</span></code></a>
will implicitly open a new transaction
(immediately after closing the pending one, for the latter two).
<code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> uses <code class="docutils literal notranslate"><span class="pre">BEGIN</span> <span class="pre">DEFERRED</span></code> statements when opening transactions.</p></li>
<li><p>Transactions should be committed explicitly using <code class="xref py py-meth docutils literal notranslate"><span class="pre">commit()</span></code>.</p></li>
<li><p>Transactions should be rolled back explicitly using <code class="xref py py-meth docutils literal notranslate"><span class="pre">rollback()</span></code>.</p></li>
<li><p>An implicit rollback is performed if the database is
<a class="reference internal" href="#sqlite3.Connection.close" title="sqlite3.Connection.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a>-ed with pending changes.</p></li>
</ul>
<p>Set <em>autocommit</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> to enable SQLite’s <a class="reference external" href="https://www.sqlite.org/lang_transaction.html#implicit_versus_explicit_transactions">autocommit mode</a>.
In this mode, <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.commit()</span></code></a> and <a class="reference internal" href="#sqlite3.Connection.rollback" title="sqlite3.Connection.rollback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Connection.rollback()</span></code></a>
have no effect.
Note that SQLite’s autocommit mode is distinct from
the <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0249/"><strong>PEP 249</strong></a>-compliant <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.autocommit</span></code></a> attribute;
use <a class="reference internal" href="#sqlite3.Connection.in_transaction" title="sqlite3.Connection.in_transaction"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.in_transaction</span></code></a> to query
the low-level SQLite autocommit mode.</p>
<p>Set <em>autocommit</em> to <a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a>
to leave transaction control behaviour to the
<a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.isolation_level</span></code></a> attribute.
See <a class="reference internal" href="#sqlite3-transaction-control-isolation-level"><span class="std std-ref">Transaction control via the isolation_level attribute</span></a> for more information.</p>
</section>
<section id="transaction-control-via-the-isolation-level-attribute">
<span id="sqlite3-transaction-control-isolation-level"></span><h4>Transaction control via the <code class="docutils literal notranslate"><span class="pre">isolation_level</span></code> attribute<a class="headerlink" href="#transaction-control-via-the-isolation-level-attribute" title="Link to this heading">¶</a></h4>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The recommended way of controlling transactions is via the
<a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> attribute.
See <a class="reference internal" href="#sqlite3-transaction-control-autocommit"><span class="std std-ref">Transaction control via the autocommit attribute</span></a>.</p>
</div>
<p>If <a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.autocommit</span></code></a> is set to
<a class="reference internal" href="#sqlite3.LEGACY_TRANSACTION_CONTROL" title="sqlite3.LEGACY_TRANSACTION_CONTROL"><code class="xref py py-data docutils literal notranslate"><span class="pre">LEGACY_TRANSACTION_CONTROL</span></code></a> (the default),
transaction behaviour is controlled using
the <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Connection.isolation_level</span></code></a> attribute.
Otherwise, <code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code> has no effect.</p>
<p>If the connection attribute <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a>
is not <code class="docutils literal notranslate"><span class="pre">None</span></code>,
new transactions are implicitly opened before
<a class="reference internal" href="#sqlite3.Cursor.execute" title="sqlite3.Cursor.execute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">execute()</span></code></a> and <a class="reference internal" href="#sqlite3.Cursor.executemany" title="sqlite3.Cursor.executemany"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executemany()</span></code></a> executes
<code class="docutils literal notranslate"><span class="pre">INSERT</span></code>, <code class="docutils literal notranslate"><span class="pre">UPDATE</span></code>, <code class="docutils literal notranslate"><span class="pre">DELETE</span></code>, or <code class="docutils literal notranslate"><span class="pre">REPLACE</span></code> statements;
for other statements, no implicit transaction handling is performed.
Use the <a class="reference internal" href="#sqlite3.Connection.commit" title="sqlite3.Connection.commit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">commit()</span></code></a> and <a class="reference internal" href="#sqlite3.Connection.rollback" title="sqlite3.Connection.rollback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">rollback()</span></code></a> methods
to respectively commit and roll back pending transactions.
You can choose the underlying <a class="reference external" href="https://www.sqlite.org/lang_transaction.html#deferred_immediate_and_exclusive_transactions">SQLite transaction behaviour</a> —
that is, whether and what type of <code class="docutils literal notranslate"><span class="pre">BEGIN</span></code> statements <code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code>
implicitly executes –
via the <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a> attribute.</p>
<p>If <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">None</span></code>,
no transactions are implicitly opened at all.
This leaves the underlying SQLite library in <a class="reference external" href="https://www.sqlite.org/lang_transaction.html#implicit_versus_explicit_transactions">autocommit mode</a>,
but also allows the user to perform their own transaction handling
using explicit SQL statements.
The underlying SQLite library autocommit mode can be queried using the
<a class="reference internal" href="#sqlite3.Connection.in_transaction" title="sqlite3.Connection.in_transaction"><code class="xref py py-attr docutils literal notranslate"><span class="pre">in_transaction</span></code></a> attribute.</p>
<p>The <a class="reference internal" href="#sqlite3.Cursor.executescript" title="sqlite3.Cursor.executescript"><code class="xref py py-meth docutils literal notranslate"><span class="pre">executescript()</span></code></a> method implicitly commits
any pending transaction before execution of the given SQL script,
regardless of the value of <a class="reference internal" href="#sqlite3.Connection.isolation_level" title="sqlite3.Connection.isolation_level"><code class="xref py py-attr docutils literal notranslate"><span class="pre">isolation_level</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> used to implicitly commit an open transaction before DDL
statements.  This is no longer the case.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The recommended way of controlling transactions is now via the
<a class="reference internal" href="#sqlite3.Connection.autocommit" title="sqlite3.Connection.autocommit"><code class="xref py py-attr docutils literal notranslate"><span class="pre">autocommit</span></code></a> attribute.</p>
</div>
</section>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> — DB-API 2.0 interface for SQLite databases</a><ul>
<li><a class="reference internal" href="#tutorial">Tutorial</a></li>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#module-functions">Module functions</a></li>
<li><a class="reference internal" href="#module-constants">Module constants</a></li>
<li><a class="reference internal" href="#connection-objects">Connection objects</a></li>
<li><a class="reference internal" href="#cursor-objects">Cursor objects</a></li>
<li><a class="reference internal" href="#row-objects">Row objects</a></li>
<li><a class="reference internal" href="#blob-objects">Blob objects</a></li>
<li><a class="reference internal" href="#prepareprotocol-objects">PrepareProtocol objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#sqlite-and-python-types">SQLite and Python types</a></li>
<li><a class="reference internal" href="#default-adapters-and-converters-deprecated">Default adapters and converters (deprecated)</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-line interface</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-guides">How-to guides</a><ul>
<li><a class="reference internal" href="#how-to-use-placeholders-to-bind-values-in-sql-queries">How to use placeholders to bind values in SQL queries</a></li>
<li><a class="reference internal" href="#how-to-adapt-custom-python-types-to-sqlite-values">How to adapt custom Python types to SQLite values</a><ul>
<li><a class="reference internal" href="#how-to-write-adaptable-objects">How to write adaptable objects</a></li>
<li><a class="reference internal" href="#how-to-register-adapter-callables">How to register adapter callables</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to-convert-sqlite-values-to-custom-python-types">How to convert SQLite values to custom Python types</a></li>
<li><a class="reference internal" href="#adapter-and-converter-recipes">Adapter and converter recipes</a></li>
<li><a class="reference internal" href="#how-to-use-connection-shortcut-methods">How to use connection shortcut methods</a></li>
<li><a class="reference internal" href="#how-to-use-the-connection-context-manager">How to use the connection context manager</a></li>
<li><a class="reference internal" href="#how-to-work-with-sqlite-uris">How to work with SQLite URIs</a></li>
<li><a class="reference internal" href="#how-to-create-and-use-row-factories">How to create and use row factories</a></li>
<li><a class="reference internal" href="#how-to-handle-non-utf-8-text-encodings">How to handle non-UTF-8 text encodings</a></li>
</ul>
</li>
<li><a class="reference internal" href="#explanation">Explanation</a><ul>
<li><a class="reference internal" href="#transaction-control">Transaction control</a><ul>
<li><a class="reference internal" href="#transaction-control-via-the-autocommit-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">autocommit</span></code> attribute</a></li>
<li><a class="reference internal" href="#transaction-control-via-the-isolation-level-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">isolation_level</span></code> attribute</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="dbm.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm</span></code> — Interfaces to Unix “databases”</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="archiving.html"
                          title="next chapter">Data Compression and Archiving</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sqlite3.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="archiving.html" title="Data Compression and Archiving"
             >next</a> |</li>
        <li class="right" >
          <a href="dbm.html" title="dbm — Interfaces to Unix “databases”"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="persistence.html" >Data Persistence</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> — DB-API 2.0 interface for SQLite databases</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>