<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pprint — Data pretty printer" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pprint.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pprint.py The pprint module provides a capability to “pretty-print” arbitrary Python data structures in a form which can be used as input to the interpreter. If the formatted struc..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pprint.py The pprint module provides a capability to “pretty-print” arbitrary Python data structures in a form which can be used as input to the interpreter. If the formatted struc..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pprint — Data pretty printer &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="reprlib — Alternate repr() implementation" href="reprlib.html" />
    <link rel="prev" title="copy — Shallow and deep copy operations" href="copy.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pprint.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#prettyprinter-objects">PrettyPrinter Objects</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="copy.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">copy</span></code> — Shallow and deep copy operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="reprlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pprint.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="reprlib.html" title="reprlib — Alternate repr() implementation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="copy.html" title="copy — Shallow and deep copy operations"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pprint">
<span id="pprint-data-pretty-printer"></span><h1><a class="reference internal" href="#module-pprint" title="pprint: Data pretty printer."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code></a> — Data pretty printer<a class="headerlink" href="#module-pprint" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pprint.py">Lib/pprint.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-pprint" title="pprint: Data pretty printer."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code></a> module provides a capability to “pretty-print” arbitrary
Python data structures in a form which can be used as input to the interpreter.
If the formatted structures include objects which are not fundamental Python
types, the representation may not be loadable.  This may be the case if objects
such as files, sockets or classes are included, as well as many other
objects which are not representable as Python literals.</p>
<p>The formatted representation keeps objects on a single line if it can, and
breaks them onto multiple lines if they don’t fit within the allowed width.
Construct <a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> objects explicitly if you need to adjust the
width constraint.</p>
<p>Dictionaries are sorted by key before the display is computed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added support for pretty-printing <a class="reference internal" href="types.html#types.SimpleNamespace" title="types.SimpleNamespace"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.SimpleNamespace</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added support for pretty-printing <a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-class docutils literal notranslate"><span class="pre">dataclasses.dataclass</span></code></a>.</p>
</div>
<section id="functions">
<span id="pprint-functions"></span><h2>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="pprint.pp">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">pp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_dicts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.pp" title="Link to this definition">¶</a></dt>
<dd><p>Prints the formatted representation of <em>object</em> followed by a newline.
If <em>sort_dicts</em> is false (the default), dictionaries will be displayed with
their keys in insertion order, otherwise the dict keys will be sorted.
<em>args</em> and <em>kwargs</em> will be passed to <a class="reference internal" href="#pprint.pprint" title="pprint.pprint"><code class="xref py py-func docutils literal notranslate"><span class="pre">pprint()</span></code></a> as formatting
parameters.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pprint</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stuff</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="s1">&#39;eggs&#39;</span><span class="p">,</span> <span class="s1">&#39;lumberjack&#39;</span><span class="p">,</span> <span class="s1">&#39;knights&#39;</span><span class="p">,</span> <span class="s1">&#39;ni&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stuff</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">stuff</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pp</span><span class="p">(</span><span class="n">stuff</span><span class="p">)</span>
<span class="go">[&lt;Recursion on list with id=...&gt;,</span>
<span class="go"> &#39;spam&#39;,</span>
<span class="go"> &#39;eggs&#39;,</span>
<span class="go"> &#39;lumberjack&#39;,</span>
<span class="go"> &#39;knights&#39;,</span>
<span class="go"> &#39;ni&#39;]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pprint.pprint">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">pprint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">80</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compact</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_dicts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">underscore_numbers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.pprint" title="Link to this definition">¶</a></dt>
<dd><p>Prints the formatted representation of <em>object</em> on <em>stream</em>, followed by a
newline.  If <em>stream</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> is used. This may be used
in the interactive interpreter instead of the <a class="reference internal" href="functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> function for
inspecting values (you can even reassign <code class="docutils literal notranslate"><span class="pre">print</span> <span class="pre">=</span> <span class="pre">pprint.pprint</span></code> for use
within a scope).</p>
<p>The configuration parameters <em>stream</em>, <em>indent</em>, <em>width</em>, <em>depth</em>,
<em>compact</em>, <em>sort_dicts</em> and <em>underscore_numbers</em> are passed to the
<a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> constructor and their meanings are as
described in its documentation below.</p>
<p>Note that <em>sort_dicts</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> by default and you might want to use
<a class="reference internal" href="#pprint.pp" title="pprint.pp"><code class="xref py py-func docutils literal notranslate"><span class="pre">pp()</span></code></a> instead where it is <code class="docutils literal notranslate"><span class="pre">False</span></code> by default.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pprint.pformat">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">pformat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">80</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compact</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_dicts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">underscore_numbers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.pformat" title="Link to this definition">¶</a></dt>
<dd><p>Return the formatted representation of <em>object</em> as a string.  <em>indent</em>,
<em>width</em>, <em>depth</em>, <em>compact</em>, <em>sort_dicts</em> and <em>underscore_numbers</em> are
passed to the <a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> constructor as formatting parameters
and their meanings are as described in its documentation below.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pprint.isreadable">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">isreadable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.isreadable" title="Link to this definition">¶</a></dt>
<dd><p id="index-0">Determine if the formatted representation of <em>object</em> is “readable”, or can be
used to reconstruct the value using <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>.  This always returns <code class="docutils literal notranslate"><span class="pre">False</span></code>
for recursive objects.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">isreadable</span><span class="p">(</span><span class="n">stuff</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pprint.isrecursive">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">isrecursive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.isrecursive" title="Link to this definition">¶</a></dt>
<dd><p>Determine if <em>object</em> requires a recursive representation.  This function is
subject to the same limitations as noted in <a class="reference internal" href="#pprint.saferepr" title="pprint.saferepr"><code class="xref py py-func docutils literal notranslate"><span class="pre">saferepr()</span></code></a> below and may raise an
<a class="reference internal" href="exceptions.html#RecursionError" title="RecursionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RecursionError</span></code></a> if it fails to detect a recursive object.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pprint.saferepr">
<span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">saferepr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.saferepr" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representation of <em>object</em>, protected against recursion in
some common data structures, namely instances of <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>, <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>
and <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> or subclasses whose <code class="docutils literal notranslate"><span class="pre">__repr__</span></code> has not been overridden.  If the
representation of object exposes a recursive entry, the recursive reference
will be represented as <code class="docutils literal notranslate"><span class="pre">&lt;Recursion</span> <span class="pre">on</span> <span class="pre">typename</span> <span class="pre">with</span> <span class="pre">id=number&gt;</span></code>.  The
representation is not otherwise formatted.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">saferepr</span><span class="p">(</span><span class="n">stuff</span><span class="p">)</span>
<span class="go">&quot;[&lt;Recursion on list with id=...&gt;, &#39;spam&#39;, &#39;eggs&#39;, &#39;lumberjack&#39;, &#39;knights&#39;, &#39;ni&#39;]&quot;</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="prettyprinter-objects">
<span id="id1"></span><h2>PrettyPrinter Objects<a class="headerlink" href="#prettyprinter-objects" title="Link to this heading">¶</a></h2>
<p>This module defines one class:</p>
<dl class="py class" id="index-1">
<dt class="sig sig-object py" id="pprint.PrettyPrinter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pprint.</span></span><span class="sig-name descname"><span class="pre">PrettyPrinter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">80</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stream</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compact</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_dicts</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">underscore_numbers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter" title="Link to this definition">¶</a></dt>
<dd><p>Construct a <a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> instance.  This constructor understands
several keyword parameters.</p>
<p><em>stream</em> (default <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code>) is a <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a> to
which the output will be written by calling its <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> method.
If both <em>stream</em> and <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code> are <code class="docutils literal notranslate"><span class="pre">None</span></code>, then
<a class="reference internal" href="#pprint.PrettyPrinter.pprint" title="pprint.PrettyPrinter.pprint"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pprint()</span></code></a> silently returns.</p>
<p>Other values configure the manner in which nesting of complex data
structures is displayed.</p>
<p><em>indent</em> (default 1) specifies the amount of indentation added for
each nesting level.</p>
<p><em>depth</em> controls the number of nesting levels which may be printed; if
the data structure being printed is too deep, the next contained level
is replaced by <code class="docutils literal notranslate"><span class="pre">...</span></code>.  By default, there is no constraint on the
depth of the objects being formatted.</p>
<p><em>width</em> (default 80) specifies the desired maximum number of characters per
line in the output. If a structure cannot be formatted within the width
constraint, a best effort will be made.</p>
<p><em>compact</em> impacts the way that long sequences (lists, tuples, sets, etc)
are formatted. If <em>compact</em> is false (the default) then each item of a
sequence will be formatted on a separate line.  If <em>compact</em> is true, as
many items as will fit within the <em>width</em> will be formatted on each output
line.</p>
<p>If <em>sort_dicts</em> is true (the default), dictionaries will be formatted with
their keys sorted, otherwise they will display in insertion order.</p>
<p>If <em>underscore_numbers</em> is true, integers will be formatted with the
<code class="docutils literal notranslate"><span class="pre">_</span></code> character for a thousands separator, otherwise underscores are not
displayed (the default).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>compact</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>sort_dicts</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <em>underscore_numbers</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>No longer attempts to write to <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code> if it is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pprint</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stuff</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="s1">&#39;eggs&#39;</span><span class="p">,</span> <span class="s1">&#39;lumberjack&#39;</span><span class="p">,</span> <span class="s1">&#39;knights&#39;</span><span class="p">,</span> <span class="s1">&#39;ni&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stuff</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">stuff</span><span class="p">[:])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span> <span class="o">=</span> <span class="n">pprint</span><span class="o">.</span><span class="n">PrettyPrinter</span><span class="p">(</span><span class="n">indent</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span><span class="o">.</span><span class="n">pprint</span><span class="p">(</span><span class="n">stuff</span><span class="p">)</span>
<span class="go">[   [&#39;spam&#39;, &#39;eggs&#39;, &#39;lumberjack&#39;, &#39;knights&#39;, &#39;ni&#39;],</span>
<span class="go">    &#39;spam&#39;,</span>
<span class="go">    &#39;eggs&#39;,</span>
<span class="go">    &#39;lumberjack&#39;,</span>
<span class="go">    &#39;knights&#39;,</span>
<span class="go">    &#39;ni&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span> <span class="o">=</span> <span class="n">pprint</span><span class="o">.</span><span class="n">PrettyPrinter</span><span class="p">(</span><span class="n">width</span><span class="o">=</span><span class="mi">41</span><span class="p">,</span> <span class="n">compact</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span><span class="o">.</span><span class="n">pprint</span><span class="p">(</span><span class="n">stuff</span><span class="p">)</span>
<span class="go">[[&#39;spam&#39;, &#39;eggs&#39;, &#39;lumberjack&#39;,</span>
<span class="go">  &#39;knights&#39;, &#39;ni&#39;],</span>
<span class="go"> &#39;spam&#39;, &#39;eggs&#39;, &#39;lumberjack&#39;, &#39;knights&#39;,</span>
<span class="go"> &#39;ni&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tup</span> <span class="o">=</span> <span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;eggs&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;lumberjack&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;knights&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;ni&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;dead&#39;</span><span class="p">,</span>
<span class="gp">... </span><span class="p">(</span><span class="s1">&#39;parrot&#39;</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;fresh fruit&#39;</span><span class="p">,))))))))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span> <span class="o">=</span> <span class="n">pprint</span><span class="o">.</span><span class="n">PrettyPrinter</span><span class="p">(</span><span class="n">depth</span><span class="o">=</span><span class="mi">6</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pp</span><span class="o">.</span><span class="n">pprint</span><span class="p">(</span><span class="n">tup</span><span class="p">)</span>
<span class="go">(&#39;spam&#39;, (&#39;eggs&#39;, (&#39;lumberjack&#39;, (&#39;knights&#39;, (&#39;ni&#39;, (&#39;dead&#39;, (...)))))))</span>
</pre></div>
</div>
</div>
</dd></dl>

<p><a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="pprint.PrettyPrinter.pformat">
<span class="sig-prename descclassname"><span class="pre">PrettyPrinter.</span></span><span class="sig-name descname"><span class="pre">pformat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter.pformat" title="Link to this definition">¶</a></dt>
<dd><p>Return the formatted representation of <em>object</em>.  This takes into account the
options passed to the <a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> constructor.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pprint.PrettyPrinter.pprint">
<span class="sig-prename descclassname"><span class="pre">PrettyPrinter.</span></span><span class="sig-name descname"><span class="pre">pprint</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter.pprint" title="Link to this definition">¶</a></dt>
<dd><p>Print the formatted representation of <em>object</em> on the configured stream,
followed by a newline.</p>
</dd></dl>

<p>The following methods provide the implementations for the corresponding
functions of the same names.  Using these methods on an instance is slightly
more efficient since new <a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> objects don’t need to be
created.</p>
<dl class="py method">
<dt class="sig sig-object py" id="pprint.PrettyPrinter.isreadable">
<span class="sig-prename descclassname"><span class="pre">PrettyPrinter.</span></span><span class="sig-name descname"><span class="pre">isreadable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter.isreadable" title="Link to this definition">¶</a></dt>
<dd><p id="index-2">Determine if the formatted representation of the object is “readable,” or can be
used to reconstruct the value using <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a>.  Note that this returns
<code class="docutils literal notranslate"><span class="pre">False</span></code> for recursive objects.  If the <em>depth</em> parameter of the
<a class="reference internal" href="#pprint.PrettyPrinter" title="pprint.PrettyPrinter"><code class="xref py py-class docutils literal notranslate"><span class="pre">PrettyPrinter</span></code></a> is set and the object is deeper than allowed, this
returns <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pprint.PrettyPrinter.isrecursive">
<span class="sig-prename descclassname"><span class="pre">PrettyPrinter.</span></span><span class="sig-name descname"><span class="pre">isrecursive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter.isrecursive" title="Link to this definition">¶</a></dt>
<dd><p>Determine if the object requires a recursive representation.</p>
</dd></dl>

<p>This method is provided as a hook to allow subclasses to modify the way objects
are converted to strings.  The default implementation uses the internals of the
<a class="reference internal" href="#pprint.saferepr" title="pprint.saferepr"><code class="xref py py-func docutils literal notranslate"><span class="pre">saferepr()</span></code></a> implementation.</p>
<dl class="py method">
<dt class="sig sig-object py" id="pprint.PrettyPrinter.format">
<span class="sig-prename descclassname"><span class="pre">PrettyPrinter.</span></span><span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxlevels</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pprint.PrettyPrinter.format" title="Link to this definition">¶</a></dt>
<dd><p>Returns three values: the formatted version of <em>object</em> as a string, a flag
indicating whether the result is readable, and a flag indicating whether
recursion was detected.  The first argument is the object to be presented.  The
second is a dictionary which contains the <a class="reference internal" href="functions.html#id" title="id"><code class="xref py py-func docutils literal notranslate"><span class="pre">id()</span></code></a> of objects that are part of
the current presentation context (direct and indirect containers for <em>object</em>
that are affecting the presentation) as the keys; if an object needs to be
presented which is already represented in <em>context</em>, the third return value
should be <code class="docutils literal notranslate"><span class="pre">True</span></code>.  Recursive calls to the <a class="reference internal" href="#pprint.PrettyPrinter.format" title="pprint.PrettyPrinter.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> method should add
additional entries for containers to this dictionary.  The third argument,
<em>maxlevels</em>, gives the requested limit to recursion; this will be <code class="docutils literal notranslate"><span class="pre">0</span></code> if there
is no requested limit.  This argument should be passed unmodified to recursive
calls. The fourth argument, <em>level</em>, gives the current level; recursive calls
should be passed a value less than that of the current call.</p>
</dd></dl>

</section>
<section id="example">
<span id="pprint-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>To demonstrate several uses of the <a class="reference internal" href="#pprint.pp" title="pprint.pp"><code class="xref py py-func docutils literal notranslate"><span class="pre">pp()</span></code></a> function and its parameters,
let’s fetch information about a project from <a class="reference external" href="https://pypi.org">PyPI</a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pprint</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">urllib.request</span> <span class="kn">import</span> <span class="n">urlopen</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;https://pypi.org/pypi/sampleproject/json&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">resp</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">project_info</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">resp</span><span class="p">)[</span><span class="s1">&#39;info&#39;</span><span class="p">]</span>
</pre></div>
</div>
<p>In its basic form, <a class="reference internal" href="#pprint.pp" title="pprint.pp"><code class="xref py py-func docutils literal notranslate"><span class="pre">pp()</span></code></a> shows the whole object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pp</span><span class="p">(</span><span class="n">project_info</span><span class="p">)</span>
<span class="go">{&#39;author&#39;: &#39;The Python Packaging Authority&#39;,</span>
<span class="go"> &#39;author_email&#39;: &#39;<EMAIL>&#39;,</span>
<span class="go"> &#39;bugtrack_url&#39;: None,</span>
<span class="go"> &#39;classifiers&#39;: [&#39;Development Status :: 3 - Alpha&#39;,</span>
<span class="go">                 &#39;Intended Audience :: Developers&#39;,</span>
<span class="go">                 &#39;License :: OSI Approved :: MIT License&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 2&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 2.6&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 2.7&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 3&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 3.2&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 3.3&#39;,</span>
<span class="go">                 &#39;Programming Language :: Python :: 3.4&#39;,</span>
<span class="go">                 &#39;Topic :: Software Development :: Build Tools&#39;],</span>
<span class="go"> &#39;description&#39;: &#39;A sample Python project\n&#39;</span>
<span class="go">                &#39;=======================\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;This is the description file for the project.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;The file should use UTF-8 encoding and be written using &#39;</span>
<span class="go">                &#39;ReStructured Text. It\n&#39;</span>
<span class="go">                &#39;will be used to generate the project webpage on PyPI, and &#39;</span>
<span class="go">                &#39;should be written for\n&#39;</span>
<span class="go">                &#39;that purpose.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;Typical contents for this file would include an overview of &#39;</span>
<span class="go">                &#39;the project, basic\n&#39;</span>
<span class="go">                &#39;usage examples, etc. Generally, including the project &#39;</span>
<span class="go">                &#39;changelog in here is not\n&#39;</span>
<span class="go">                &#39;a good idea, although a simple &quot;What\&#39;s New&quot; section for the &#39;</span>
<span class="go">                &#39;most recent version\n&#39;</span>
<span class="go">                &#39;may be appropriate.&#39;,</span>
<span class="go"> &#39;description_content_type&#39;: None,</span>
<span class="go"> &#39;docs_url&#39;: None,</span>
<span class="go"> &#39;download_url&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;downloads&#39;: {&#39;last_day&#39;: -1, &#39;last_month&#39;: -1, &#39;last_week&#39;: -1},</span>
<span class="go"> &#39;home_page&#39;: &#39;https://github.com/pypa/sampleproject&#39;,</span>
<span class="go"> &#39;keywords&#39;: &#39;sample setuptools development&#39;,</span>
<span class="go"> &#39;license&#39;: &#39;MIT&#39;,</span>
<span class="go"> &#39;maintainer&#39;: None,</span>
<span class="go"> &#39;maintainer_email&#39;: None,</span>
<span class="go"> &#39;name&#39;: &#39;sampleproject&#39;,</span>
<span class="go"> &#39;package_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;platform&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;project_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;project_urls&#39;: {&#39;Download&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go">                  &#39;Homepage&#39;: &#39;https://github.com/pypa/sampleproject&#39;},</span>
<span class="go"> &#39;release_url&#39;: &#39;https://pypi.org/project/sampleproject/1.2.0/&#39;,</span>
<span class="go"> &#39;requires_dist&#39;: None,</span>
<span class="go"> &#39;requires_python&#39;: None,</span>
<span class="go"> &#39;summary&#39;: &#39;A sample Python project&#39;,</span>
<span class="go"> &#39;version&#39;: &#39;1.2.0&#39;}</span>
</pre></div>
</div>
<p>The result can be limited to a certain <em>depth</em> (ellipsis is used for deeper
contents):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pp</span><span class="p">(</span><span class="n">project_info</span><span class="p">,</span> <span class="n">depth</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="go">{&#39;author&#39;: &#39;The Python Packaging Authority&#39;,</span>
<span class="go"> &#39;author_email&#39;: &#39;<EMAIL>&#39;,</span>
<span class="go"> &#39;bugtrack_url&#39;: None,</span>
<span class="go"> &#39;classifiers&#39;: [...],</span>
<span class="go"> &#39;description&#39;: &#39;A sample Python project\n&#39;</span>
<span class="go">                &#39;=======================\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;This is the description file for the project.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;The file should use UTF-8 encoding and be written using &#39;</span>
<span class="go">                &#39;ReStructured Text. It\n&#39;</span>
<span class="go">                &#39;will be used to generate the project webpage on PyPI, and &#39;</span>
<span class="go">                &#39;should be written for\n&#39;</span>
<span class="go">                &#39;that purpose.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;Typical contents for this file would include an overview of &#39;</span>
<span class="go">                &#39;the project, basic\n&#39;</span>
<span class="go">                &#39;usage examples, etc. Generally, including the project &#39;</span>
<span class="go">                &#39;changelog in here is not\n&#39;</span>
<span class="go">                &#39;a good idea, although a simple &quot;What\&#39;s New&quot; section for the &#39;</span>
<span class="go">                &#39;most recent version\n&#39;</span>
<span class="go">                &#39;may be appropriate.&#39;,</span>
<span class="go"> &#39;description_content_type&#39;: None,</span>
<span class="go"> &#39;docs_url&#39;: None,</span>
<span class="go"> &#39;download_url&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;downloads&#39;: {...},</span>
<span class="go"> &#39;home_page&#39;: &#39;https://github.com/pypa/sampleproject&#39;,</span>
<span class="go"> &#39;keywords&#39;: &#39;sample setuptools development&#39;,</span>
<span class="go"> &#39;license&#39;: &#39;MIT&#39;,</span>
<span class="go"> &#39;maintainer&#39;: None,</span>
<span class="go"> &#39;maintainer_email&#39;: None,</span>
<span class="go"> &#39;name&#39;: &#39;sampleproject&#39;,</span>
<span class="go"> &#39;package_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;platform&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;project_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;project_urls&#39;: {...},</span>
<span class="go"> &#39;release_url&#39;: &#39;https://pypi.org/project/sampleproject/1.2.0/&#39;,</span>
<span class="go"> &#39;requires_dist&#39;: None,</span>
<span class="go"> &#39;requires_python&#39;: None,</span>
<span class="go"> &#39;summary&#39;: &#39;A sample Python project&#39;,</span>
<span class="go"> &#39;version&#39;: &#39;1.2.0&#39;}</span>
</pre></div>
</div>
<p>Additionally, maximum character <em>width</em> can be suggested. If a long object
cannot be split, the specified width will be exceeded:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pprint</span><span class="o">.</span><span class="n">pp</span><span class="p">(</span><span class="n">project_info</span><span class="p">,</span> <span class="n">depth</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">60</span><span class="p">)</span>
<span class="go">{&#39;author&#39;: &#39;The Python Packaging Authority&#39;,</span>
<span class="go"> &#39;author_email&#39;: &#39;<EMAIL>&#39;,</span>
<span class="go"> &#39;bugtrack_url&#39;: None,</span>
<span class="go"> &#39;classifiers&#39;: [...],</span>
<span class="go"> &#39;description&#39;: &#39;A sample Python project\n&#39;</span>
<span class="go">                &#39;=======================\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;This is the description file for the &#39;</span>
<span class="go">                &#39;project.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;The file should use UTF-8 encoding and be &#39;</span>
<span class="go">                &#39;written using ReStructured Text. It\n&#39;</span>
<span class="go">                &#39;will be used to generate the project &#39;</span>
<span class="go">                &#39;webpage on PyPI, and should be written &#39;</span>
<span class="go">                &#39;for\n&#39;</span>
<span class="go">                &#39;that purpose.\n&#39;</span>
<span class="go">                &#39;\n&#39;</span>
<span class="go">                &#39;Typical contents for this file would &#39;</span>
<span class="go">                &#39;include an overview of the project, &#39;</span>
<span class="go">                &#39;basic\n&#39;</span>
<span class="go">                &#39;usage examples, etc. Generally, including &#39;</span>
<span class="go">                &#39;the project changelog in here is not\n&#39;</span>
<span class="go">                &#39;a good idea, although a simple &quot;What\&#39;s &#39;</span>
<span class="go">                &#39;New&quot; section for the most recent version\n&#39;</span>
<span class="go">                &#39;may be appropriate.&#39;,</span>
<span class="go"> &#39;description_content_type&#39;: None,</span>
<span class="go"> &#39;docs_url&#39;: None,</span>
<span class="go"> &#39;download_url&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;downloads&#39;: {...},</span>
<span class="go"> &#39;home_page&#39;: &#39;https://github.com/pypa/sampleproject&#39;,</span>
<span class="go"> &#39;keywords&#39;: &#39;sample setuptools development&#39;,</span>
<span class="go"> &#39;license&#39;: &#39;MIT&#39;,</span>
<span class="go"> &#39;maintainer&#39;: None,</span>
<span class="go"> &#39;maintainer_email&#39;: None,</span>
<span class="go"> &#39;name&#39;: &#39;sampleproject&#39;,</span>
<span class="go"> &#39;package_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;platform&#39;: &#39;UNKNOWN&#39;,</span>
<span class="go"> &#39;project_url&#39;: &#39;https://pypi.org/project/sampleproject/&#39;,</span>
<span class="go"> &#39;project_urls&#39;: {...},</span>
<span class="go"> &#39;release_url&#39;: &#39;https://pypi.org/project/sampleproject/1.2.0/&#39;,</span>
<span class="go"> &#39;requires_dist&#39;: None,</span>
<span class="go"> &#39;requires_python&#39;: None,</span>
<span class="go"> &#39;summary&#39;: &#39;A sample Python project&#39;,</span>
<span class="go"> &#39;version&#39;: &#39;1.2.0&#39;}</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#prettyprinter-objects">PrettyPrinter Objects</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="copy.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">copy</span></code> — Shallow and deep copy operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="reprlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pprint.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="reprlib.html" title="reprlib — Alternate repr() implementation"
             >next</a> |</li>
        <li class="right" >
          <a href="copy.html" title="copy — Shallow and deep copy operations"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>