<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Graphical User Interfaces with Tk" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tk.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Tk/Tcl has long been an integral part of Python. It provides a robust and platform independent windowing toolkit, that is available to Python programmers using the tkinter package, and its extensio..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Tk/Tcl has long been an integral part of Python. It provides a robust and platform independent windowing toolkit, that is available to Python programmers using the tkinter package, and its extensio..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Graphical User Interfaces with Tk &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tkinter — Python interface to Tcl/Tk" href="tkinter.html" />
    <link rel="prev" title="shlex — Simple lexical analysis" href="shlex.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tk.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="shlex.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tk.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.html" title="tkinter — Python interface to Tcl/Tk"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="shlex.html" title="shlex — Simple lexical analysis"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Graphical User Interfaces with Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="graphical-user-interfaces-with-tk">
<span id="tkinter"></span><h1>Graphical User Interfaces with Tk<a class="headerlink" href="#graphical-user-interfaces-with-tk" title="Link to this heading">¶</a></h1>
<p id="index-0">Tk/Tcl has long been an integral part of Python.  It provides a robust and
platform independent windowing toolkit, that is available to Python programmers
using the <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> package, and its extension, the <a class="reference internal" href="tkinter.tix.html#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> and
the <a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> modules.</p>
<p>The <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> package is a thin object-oriented layer on top of Tcl/Tk. To
use <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>, you don’t need to write Tcl code, but you will need to
consult the Tk documentation, and occasionally the Tcl documentation.
<a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> is a set of wrappers that implement the Tk widgets as Python
classes.</p>
<p><a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>’s chief virtues are that it is fast, and that it usually comes
bundled with Python. Although its standard documentation is weak, good
material is available, which includes: references, tutorials, a book and
others. <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> is also famous for having an outdated look and feel,
which has been vastly improved in Tk 8.5. Nevertheless, there are many other
GUI libraries that you could be interested in. The Python wiki lists several
alternative <a class="reference external" href="https://wiki.python.org/moin/GuiProgramming">GUI frameworks and tools</a>.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="tkinter.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#architecture">Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#tkinter-modules">Tkinter Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#tkinter-life-preserver">Tkinter Life Preserver</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#a-hello-world-program">A Hello World Program</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#important-tk-concepts">Important Tk Concepts</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#understanding-how-tkinter-wraps-tcl-tk">Understanding How Tkinter Wraps Tcl/Tk</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#how-do-i-what-option-does">How do I…? What option does…?</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#navigating-the-tcl-tk-reference-manual">Navigating the Tcl/Tk Reference Manual</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#threading-model">Threading model</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#handy-reference">Handy Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#setting-options">Setting Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#the-packer">The Packer</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#packer-options">Packer Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#coupling-widget-variables">Coupling Widget Variables</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#the-window-manager">The Window Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#tk-option-data-types">Tk Option Data Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#bindings-and-events">Bindings and Events</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#the-index-parameter">The index Parameter</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.html#images">Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.html#file-handlers">File Handlers</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.colorchooser.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code> — Color choosing dialog</a></li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.font.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code> — Tkinter font wrapper</a></li>
<li class="toctree-l1"><a class="reference internal" href="dialog.html">Tkinter Dialogs</a><ul>
<li class="toctree-l2"><a class="reference internal" href="dialog.html#module-tkinter.simpledialog"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.simpledialog</span></code> — Standard Tkinter input dialogs</a></li>
<li class="toctree-l2"><a class="reference internal" href="dialog.html#module-tkinter.filedialog"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.filedialog</span></code> — File selection dialogs</a><ul>
<li class="toctree-l3"><a class="reference internal" href="dialog.html#native-load-save-dialogs">Native Load/Save Dialogs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="dialog.html#module-tkinter.commondialog"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.commondialog</span></code> — Dialog window templates</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.messagebox.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code> — Tkinter message prompts</a></li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.scrolledtext.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.scrolledtext</span></code> — Scrolled Text Widget</a></li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.dnd.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.dnd</span></code> — Drag and drop support</a></li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.ttk.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#using-ttk">Using Ttk</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#ttk-widgets">Ttk Widgets</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#widget">Widget</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#standard-options">Standard Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#scrollable-widget-options">Scrollable Widget Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#label-options">Label Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#compatibility-options">Compatibility Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#widget-states">Widget States</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-widget">ttk.Widget</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#combobox">Combobox</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#options">Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#virtual-events">Virtual events</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-combobox">ttk.Combobox</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#spinbox">Spinbox</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id1">Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id2">Virtual events</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-spinbox">ttk.Spinbox</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#notebook">Notebook</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id3">Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#tab-options">Tab Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#tab-identifiers">Tab Identifiers</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id4">Virtual Events</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-notebook">ttk.Notebook</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#progressbar">Progressbar</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id5">Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-progressbar">ttk.Progressbar</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#separator">Separator</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id6">Options</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#sizegrip">Sizegrip</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#platform-specific-notes">Platform-specific notes</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#bugs">Bugs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#treeview">Treeview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id7">Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#item-options">Item Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#tag-options">Tag Options</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#column-identifiers">Column Identifiers</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#id8">Virtual Events</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#ttk-treeview">ttk.Treeview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.ttk.html#ttk-styling">Ttk Styling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.ttk.html#layouts">Layouts</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="tkinter.tix.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tkinter.tix.html#using-tix">Using Tix</a></li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.tix.html#tix-widgets">Tix Widgets</a><ul>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#basic-widgets">Basic Widgets</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#file-selectors">File Selectors</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#hierarchical-listbox">Hierarchical ListBox</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#tabular-listbox">Tabular ListBox</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#manager-widgets">Manager Widgets</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#image-types">Image Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#miscellaneous-widgets">Miscellaneous Widgets</a></li>
<li class="toctree-l3"><a class="reference internal" href="tkinter.tix.html#form-geometry-manager">Form Geometry Manager</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="tkinter.tix.html#tix-commands">Tix Commands</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="idle.html">IDLE</a><ul>
<li class="toctree-l2"><a class="reference internal" href="idle.html#menus">Menus</a><ul>
<li class="toctree-l3"><a class="reference internal" href="idle.html#file-menu-shell-and-editor">File menu (Shell and Editor)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#edit-menu-shell-and-editor">Edit menu (Shell and Editor)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#format-menu-editor-window-only">Format menu (Editor window only)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#run-menu-editor-window-only">Run menu (Editor window only)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#shell-menu-shell-window-only">Shell menu (Shell window only)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#debug-menu-shell-window-only">Debug menu (Shell window only)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#options-menu-shell-and-editor">Options menu (Shell and Editor)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#window-menu-shell-and-editor">Window menu (Shell and Editor)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#help-menu-shell-and-editor">Help menu (Shell and Editor)</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#context-menus">Context menus</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="idle.html#editing-and-navigation">Editing and Navigation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="idle.html#editor-windows">Editor windows</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#key-bindings">Key bindings</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#automatic-indentation">Automatic indentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#search-and-replace">Search and Replace</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#completions">Completions</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#calltips">Calltips</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#code-context">Code Context</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#shell-window">Shell window</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#text-colors">Text colors</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="idle.html#startup-and-code-execution">Startup and Code Execution</a><ul>
<li class="toctree-l3"><a class="reference internal" href="idle.html#command-line-usage">Command line usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#startup-failure">Startup failure</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#running-user-code">Running user code</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#user-output-in-shell">User output in Shell</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#developing-tkinter-applications">Developing tkinter applications</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#running-without-a-subprocess">Running without a subprocess</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="idle.html#help-and-preferences">Help and Preferences</a><ul>
<li class="toctree-l3"><a class="reference internal" href="idle.html#help-sources">Help sources</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#setting-preferences">Setting preferences</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#idle-on-macos">IDLE on macOS</a></li>
<li class="toctree-l3"><a class="reference internal" href="idle.html#extensions">Extensions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="idle.html#module-idlelib">idlelib</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="shlex.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code> — Simple lexical analysis</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tk.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.html" title="tkinter — Python interface to Tcl/Tk"
             >next</a> |</li>
        <li class="right" >
          <a href="shlex.html" title="shlex — Simple lexical analysis"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Graphical User Interfaces with Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>