# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_stripe
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Complete the Stripe onboarding for company %s."
msgstr "会社 %s用にStripeオンボーディングを完了して下さい。"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Do not have access to fetch token from Stripe"
msgstr "Stripeからのフェッチトークンにアクセス権がありません"

#. module: pos_stripe
#: model_terms:ir.ui.view,arch_db:pos_stripe.pos_payment_method_view_form_inherit_pos_stripe
msgid ""
"Don't forget to complete Stripe connect before using this payment method."
msgstr "この決済方法を使用する前にStripe接続を完了して下さい。"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Failed to discover: %s"
msgstr "以下の発見に失敗しました:%s"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Failed to load resource: net::ERR_INTERNET_DISCONNECTED."
msgstr "リソースをロードするのに失敗しました::ERR_INTERNET_DISCONNECTED."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "No available Stripe readers."
msgstr "利用可能なStripeリーダーがありません"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Payment canceled because not reader connected"
msgstr "リーダーが接続されていないため"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS支払い方法"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_session
msgid "Point of Sale Session"
msgstr "POSセッション"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Reader disconnected"
msgstr "リーダーが切断されました"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe"
msgstr "Stripe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Stripe Error"
msgstr "Stripeエラー"

#. module: pos_stripe
#: model:ir.model.fields,field_description:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "Stripe Serial Number"
msgstr "Stripeシリアル番号"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe payment provider for company %s is missing"
msgstr "会社 %s 用のStripe決済プロバイダーがありません。"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/app/payment_stripe.js:0
#, python-format
msgid "Stripe readers %s not listed in your account"
msgstr "Stripeリーダー%sがアカウントに表示されていません"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "端末%sはすでに決済方法%sで使用されています。"

#. module: pos_stripe
#: model:ir.model.fields,help:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "[Serial number of the stripe terminal], for example: WSC513105011295"
msgstr "[Stripe端末のシリアル番号]、例: WSC513105011295"
