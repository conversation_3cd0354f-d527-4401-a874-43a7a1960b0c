# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "Nº de reuniones"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(date_start)s a las %(time_start)s hasta\n"
" %(date_end)s a las %(time_end)s (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s (del %(start)s al %(end)s) (%(timezone)s)"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has accepted the invitation"
msgstr "%s ha aceptado la invitación"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has declined the invitation"
msgstr "%s ha rechazado la invitación"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br><br>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hola <t t-out=\"object.common_name or ''\">Ready Mat</t><br><br>,\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                Se ha actualizado la fecha de su cita con <t t-out=\"customer.name or ''\">Jesse Brown</t>.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Se actualizó su cita.\n"
"            </t>\n"
"            La cita <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Programar una demo</strong> ahora está programada para el\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 en un horario de (11:00:00 a 11:30:00) (Europa/Bruselas)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            Se ha actualizado la fecha de su cita con <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>.\n"
"            La cita <strong t-out=\"object.event_id.appointment_type_id.name or ''\"></strong> ahora está programada para el\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 en un horario de (11:00:00 a 11:30:00) (Europa/Bruselas)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Se ha actualizado la fecha de la reunión.\n"
"            La reunión <strong t-out=\"object.event_id.name or ''\">Seguimiento para propuesta de proyecto</strong> creada por <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> ahora está programada para el\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 en un horario de (11:00:00 a 11:30:00) (Europa/Bruselas)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Aceptar</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Rechazar</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Ver</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Martes</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>Mayo de 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europa/Bruselas</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Detalles del evento</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Ubicación: <t t-out=\"object.event_id.location or ''\">Bruselas</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">Ver mapa</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Cuándo: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Cada 1 semana, por 3 eventos</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duración: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Asistentes\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Usted</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Cómo unirse:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Unirse con Conversaciones de Odoo</t>\n"
"                        <t t-else=\"\"> Unirse a través de</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Descripción del evento:\n"
"                    <t t-out=\"object.event_id.description\">Reunión interna para hablar de los nuevos precios de los productos y servicios.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Gracias,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"\n"
"    <p>\n"
"        Hola <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br><br>\n"
"\n"
"        <t t-if=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>  le ha invitado a la reunión <strong t-out=\"object.event_id.name or ''\">Seguimiento para propuesta de proyecto</strong>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"             Se ha programado su reunión <strong t-out=\"object.event_id.name or ''\">Seguimiento para propuesta de proyecto</strong>.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Aceptar</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Rechazar</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">Ver</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Martes</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">Mayo de 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europa/Bruselas</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Detalles del evento</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Ubicación: <t t-out=\"object.event_id.location or ''\">Bruselas</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">Ver mapa</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Cuándo: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Cada 1 semanas, por 3 eventos</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duración: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Asistentes\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Usted</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Cómo unirse:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Unirse con Conversaciones de Odoo</t>\n"
"                        <t t-else=\"\"> Unirse a través de</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Descripción del evento:\n"
"                    <t t-out=\"object.event_id.description\">Reunión interna para hablar de los nuevos precios de los productos y servicios.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Gracias,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Tuesday</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">May 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00 AM</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europe/Brussels</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                How to Join:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Join with Odoo Discuss</t>\n"
"                                <t t-else=\"\"> Join at</t><br>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">View Map</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object and object.appointment_type_id\"></t>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.partner_id\"></t>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"></t>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"                    <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \">Martes</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\">4</t>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\">Mayo de 2021</t>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\">11:00</t>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"> Europa/Bruselas</t>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"></td>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Detalles del evento</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Descripción:\n"
"                            <t t-out=\"object.description\">Reunión interna para hablar sobre los nuevos precios de los productos y servicios.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>\n"
"                                Cómo unirse:\n"
"                                <t t-if=\"object.get_base_url() in object.videocall_location\"> Unirse con Conversaciones de Odoo</t>\n"
"                                <t t-else=\"\"> Unirse a través de</t><br>\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Ubicación: <t t-out=\"object.location or ''\">Bruselas</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">Ver mapa</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>Cuándo: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Cada 1 semana, por 3 eventos</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duración:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\">0H30</t>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr>\n"
"        <p placeholder=\"Enter your message here\"><br></p>\n"
"\n"
"    </div>\n"
"    <t t-if=\"object.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br><br>\n"
"        This is a reminder for the below event:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        How to Join:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Join with Odoo Discuss</t>\n"
"                        <t t-else=\"\"> Join at</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"></t>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"></t>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"></t>\n"
"    <p>\n"
"        Hola, <t t-out=\"object.common_name or ''\">Gemini Furniture</t><br><br>\n"
"        Este es un recordatorio para el siguiente evento:\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Aceptar</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Rechazar</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Ver</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\" style=\"min-width: 130px;\">\n"
"            <div style=\"border-top-start-radius: 3px; border-top-end-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"EEEE\", lang_code=object.env.lang) or \"\"'>Martes</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='d', lang_code=object.env.lang) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out='format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=\"MMMM y\", lang_code=object.env.lang) or \"\"'>Mayo de 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-end-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-start-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out='format_time(time=object.event_id.start, tz=object.mail_tz, time_format=\"short\", lang_code=object.env.lang) or \"\"'>11:00</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europa/Bruselas</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"></td>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Detalles del evento</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Ubicación: <t t-out=\"object.event_id.location or ''\">Bruselas</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">Ver mapa</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>Cuándo: <t t-out=\"object.recurrence_id.get_recurrence_name()\">Cada 1 semanas, por 3 eventos</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duración: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Asistentes\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">Usted</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>\n"
"                        Cómo unirse:\n"
"                        <t t-if=\"object.get_base_url() in object.event_id.videocall_location\"> Unirse con Conversaciones de Odoo</t>\n"
"                        <t t-else=\"\"> Unirse en</t><br>\n"
"                        <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">www.mycompany.com/calendar/join_videocall/xyz</a>\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Descripción del evento:\n"
"                    <t t-out=\"object.event_id.description\">Reunión interna para hablar de los nuevos precios de los productos y servicios.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br>\n"
"    Gracias,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-plus\"/> <span>Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/> <span>Reunión de Odoo</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span class=\"fa fa-plus\"/><span> Odoo meeting</span>"
msgstr "<span class=\"fa fa-plus\"/><span> Reunión de Odoo</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"fa fa-times\"/><span> Clear meeting</span>"
msgstr "<span class=\"fa fa-times\"/><span> Eliminar reunión</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Repeat on</span>"
msgstr ""
"<span class=\"fw-bold text-nowrap\" invisible=\"rrule_type_ui not in "
"['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != "
"'weekly')\">Repetir el</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Google Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">Google Calendar</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "<span class=\"me-1 o_form_label\">Outlook Calendar</span>"
msgstr "<span class=\"me-1 o_form_label\">Calendario de Outlook</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "<span class=\"oi oi-arrow-right\"/><span> Join video call</span>"
msgstr "<span class=\"oi oi-arrow-right\"/><span> Unirse a videollamada</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span invisible=\"allday\" style=\"white-space: pre;\"> or </span>"
msgstr "<span invisible=\"allday\" style=\"white-space: pre;\"> o </span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> Attendees</span>"
msgstr "<span> Asistentes</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span> horas</span>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Guarde</strong> esta página y regrese para configurar la función."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid ""
"<strong>The following attendees have invalid email addresses and won't "
"receive any email notifications:</strong>"
msgstr ""
"<strong>Los siguientes asistentes tienen direcciones de correo electrónico "
"no válidas y no recibirán ninguna notificación por correo "
"electrónico:</strong>"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "Un usuario no puede tener el mismo contacto dos veces."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Accept"
msgstr "Aceptar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__accepted_count
msgid "Accepted Count"
msgstr "Número de invitaciones aceptadas"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "Acción"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Las acciones pueden activar comportamientos específicos, como abrir la vista"
" de calendario o marcar automáticamente como hecha al subir un documento"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "Activo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "Actividad"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Mixin de actividad"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de actividad"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Asistente para planificar actividades"

#. module: calendar
#: model:onboarding.onboarding.step,button_text:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Add"
msgstr "Añadir"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add attendees..."
msgstr "Añada asistentes..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Add description"
msgstr "Añadir una descripción"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Add title"
msgstr "Añadir título"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "Mensaje adicional"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr ""
"Mensaje adicional que se enviará con la notificación para el recordatorio"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_menu_patch.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "Todo el día"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "All Day, %(day)s"
msgstr "Todo el día, %(day)s"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
#, python-format
msgid "All events"
msgstr "Todos los eventos"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "Archivado"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "¿Está seguro que desea eliminar ese registro?"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "Asistente"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "Asistentes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendees_count
msgid "Attendees Count"
msgstr "Número de asistentes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_status
msgid "Attending?"
msgstr "¿Asistirá?"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "Disponible"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "Disponible/Ocupado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__awaiting_count
msgid "Awaiting Count"
msgstr "Número de invitaciones pendientes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "Evento base"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
#, python-format
msgid "Busy"
msgstr "Ocupado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "Por día"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_event_menu
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "Calendario"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "Alerta de calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Información del calendario de asistentes"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__record
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "Evento de calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "Filtros de calendario"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "Invitación de calendario"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "Reunión de calendario"

#. module: calendar
#: model:onboarding.onboarding,name:calendar.onboarding_onboarding_calendar
msgid "Calendar Onboarding"
msgstr "Incorporación del calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_popover_delete_wizard
msgid "Calendar Popover Delete Wizard"
msgstr "Asistente para eliminar ventana emergente del calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_provider_config
msgid "Calendar Provider Configuration Wizard"
msgstr "Asistente de configuración de proveedor de calendario"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Calendar Settings"
msgstr "Ajustes del calendario"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date Updated"
msgstr "Calendario: fecha actualizada"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
msgid "Calendar: Event Reminder"
msgstr "Calendario: recordatorio de evento"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "Calendario: actualización de evento"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "Calendario: invitación a reunión"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "Calendario: recordatorio"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"Compruebe si el organizador está solo en el evento, es decir, si el organizador es el único que no ha rechazado\n"
"        el evento (solo si el organizador no es el único asistente)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "Comprobado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__external_calendar_provider
msgid "Choose an external calendar to configure"
msgstr "Elija un calendario externo para configurar"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"Elija qué hacer con otros eventos recurrentes. No se permite actualizar "
"todos los eventos cuando se modifican las fechas o la hora."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client ID"
msgstr "ID de cliente"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Client Secret"
msgstr "Secreto de cliente"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "Color"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "Nombre común"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "Configuración"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
#: code:addons/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.xml:0
#, python-format
msgid "Connect"
msgstr "Conectar"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.js:0
#: model:ir.actions.act_window,name:calendar.action_view_start_calendar_sync
#, python-format
msgid "Connect your Calendar"
msgstr "Conecte su calendario"

#. module: calendar
#: model:onboarding.onboarding.step,title:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Connect your calendar"
msgstr "Conecte su calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Contact Attendees"
msgstr "Contactar a los asistentes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "Cuenta"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "Creado el"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__current_attendee
msgid "Current Attendee"
msgstr "Asistente actual"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__custom
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__custom
msgid "Custom"
msgstr "Personalizado"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__daily
msgid "Daily"
msgstr "Diariamente"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "Fecha"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "Día del mes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "Día"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "Día del mes"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "Día del mes"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "Días"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Decline"
msgstr "Rechazar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__declined_count
msgid "Declined Count"
msgstr "Número de invitaciones rechazadas"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__delete
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Delete Event"
msgstr "Eliminar evento"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__all
msgid "Delete all the events"
msgstr "Eliminar todos los eventos"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__next
msgid "Delete this and following events"
msgstr "Eliminar este y los eventos siguientes"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_popover_delete_wizard__delete__one
msgid "Delete this event"
msgstr "Eliminar este evento"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Describe your meeting"
msgstr "Describa su reunión"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "Descripción"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Details"
msgstr "Detalles"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__videocall_source__discuss
msgid "Discuss"
msgstr "Conversaciones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_channel_id
msgid "Discuss Channel"
msgstr "Canal de conversaciones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "Mostrar descripción"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "ID del documento"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "Modelo del documento"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "Nombre del modelo del documento"

#. module: calendar
#: model:onboarding.onboarding.step,done_text:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Done!"
msgstr "¡Hecho!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "Fecha de inicio"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "Duración"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "Duración en minutos"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "CORREO ELECTRÓNICO"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.xml:0
#, python-format
msgid "Edit Recurrent event"
msgstr "Editar evento recurrente"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "Editar evento recurrente"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "Correo electrónico"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "Correo electrónico - 3 Horas"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "Correo electrónico - 6 Horas"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "Empleado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "Fecha límite"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "Tipo de finalización"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "Fecha límite"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "Alerta del evento"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Gestor de alertas del calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "Tipo de reunión"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Regla de recurrencia de evento"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "Hora del evento"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days"
msgstr "Cada %(interval)s días"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days for %(count)s events"
msgstr "Cada %(interval)s días para %(count)s eventos"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Days until %(until)s"
msgstr "Cada %(interval)s días hasta %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s"
msgstr "Cada %(interval)s meses el día %(day)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s for %(count)s events"
msgstr "Cada %(interval)s meses el día %(day)s para %(count)s repeticiones"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months day %(day)s until %(until)s"
msgstr "Cada %(interval)s meses el día %(day)s hasta %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Months on the %(position)s %(weekday)s"
msgstr "Cada %(interval)s meses el %(position)s %(weekday)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s for %(count)s "
"events"
msgstr ""
"Cada %(interval)s meses el %(position)s %(weekday)s para %(count)s "
"repeticiones"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid ""
"Every %(interval)s Months on the %(position)s %(weekday)s until %(until)s"
msgstr "Cada %(interval)s meses el %(position)s %(weekday)s hasta %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s"
msgstr "Cada %(interval)s semanas en %(days)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s for %(count)s events"
msgstr "Cada %(interval)s semanas en %(days)s para %(count)s repeticiones"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Weeks on %(days)s until %(until)s"
msgstr "Cada %(interval)s semanas el %(days)s hasta %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years"
msgstr "Cada %(interval)s años"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years for %(count)s events"
msgstr "Cada %(interval)s años para %(count)s eventos"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(interval)s Years until %(until)s"
msgstr "Cada %(interval)s años hasta %(until)s"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: %(feedback)s"
msgstr "Retroalimentación: %(feedback)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "Primero"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "Primero debe especificar la fecha de la invitación."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "Seguir la recurrencia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "Para siempre"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "Cuarto"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "Libre"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "Vie"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "Viernes"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__google
msgid "Google"
msgstr "Google"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Google Calendar"
msgstr "Google Calendar"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Google Calendar icon"
msgstr "Icono de Google Calendar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_id
msgid "Google Client_id"
msgstr "Client_id de Google"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_client_secret
msgid "Google Client_key"
msgstr "Client_key de Google"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__cal_sync_paused
msgid "Google Synchronization Paused"
msgstr "Sincronización con Google pausada"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "Agrupar por"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP "

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "Horas"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__id
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "ID"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Si el campo activo es False, le permitirá ocultar la notificación de aviso "
"del evento sin eliminarla."

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"Si el horario aparece como \"ocupado\", puede que otras personas puedan ver la información         completa del evento o solo se mostrará el texto \"ocupado\", esto dependerá de su privacidad. Utilice esta opción para indicarle a otros         que no está disponible durante ese periodo de tiempo. \n"
" Si el evento se muestra como \"disponible\", los demás usuarios pueden saber         que está disponible durante ese periodo de tiempo."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "Intervalo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__invalid_email_partner_ids
msgid "Invalid Email Partner"
msgstr "Correo electrónico de contacto no válido"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "Invitación"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
#: model:ir.model.fields,field_description:calendar.field_calendar_event__access_token
msgid "Invitation Token"
msgstr "Token de invitación"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "Detalles de la invitación"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_invitation
msgid "Invitation email to new attendees"
msgstr "Correo electrónico de invitación para nuevos asistentes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "Invitación para"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "Invitación a {{ object.event_id.name }}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "Invitaciones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "Es un evento destacado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "El organizador está solo"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Join Video Call"
msgstr "Unirse a videollamada"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "Última"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_popover_delete_wizard__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "Última notificación marcada como leída desde el calendario principal"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type_ui
msgid "Let the event automatically repeat at that interval"
msgstr "Permite que el evento se repita automáticamente en ese intervalo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "Ubicación"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "Logotipo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "Zona horaria del correo"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#, python-format
msgid "Maybe"
msgstr "Tal vez"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "Yo"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "Reunión"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"
msgstr ""
"La reunión '%(name)s' comienza: '%(start_datetime)s' y finaliza: "
"'%(end_datetime)s'"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "Asunto de la reunión"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "Tipos de reunión"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "URL de la reunión"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "Reunión vinculada"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "Reuniones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Microsoft Outlook icon"
msgstr "Icono de Microsoft Outlook"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "Minutos"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_name
msgid "Model Description"
msgstr "Descripción del modelo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "Lun"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "Lunes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "Mes por"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__monthly
msgid "Monthly"
msgstr "Mensualmente"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "Meses"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/calendar_form/calendar_quick_create.xml:0
#, python-format
msgid "More Options"
msgstr "Más opciones"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "Mis reuniones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "Nombre"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
msgid "Needs Action"
msgstr "Necesita acción"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.xml:0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#, python-format
msgid "No"
msgstr "No"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "No, no asistiré."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "Todavía no hay retroalimentación"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "No se encontraron reuniones. ¡Creemos una!"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "Notificación"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "Notificación - 1 día"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "Notificación - 1 hora"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "Notificación - 15 minutos"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "Notificación - 2 horas"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "Notificación - 30 minutos"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr ""
"Notificaciones enviadas a todos los asistentes como recordatorio de la "
"reunión."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Number of Repetitions"
msgstr "Número de repeticiones"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "Número de repeticiones"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "OK"
msgstr "Aceptar"

#. module: calendar
#: model:ir.model,name:calendar.model_onboarding_onboarding
msgid "Onboarding"
msgstr "Incorporación"

#. module: calendar
#: model:onboarding.onboarding.step,step_image_alt:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "Onboarding Calendar Synchronization"
msgstr "Incorporación de sincronización del calendario"

#. module: calendar
#: model:ir.model,name:calendar.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Paso de incorporación"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Online Meeting"
msgstr "Reunión en línea"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "Solo usuarios internos"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "Solo usuarios internos"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "Abrir calendario"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "Opción"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "Organizador"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_provider_config__external_calendar_provider__microsoft
msgid "Outlook"
msgstr "Outlook"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "Calendario de Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "ID de cliente de Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "Secreto de cliente de Outlook"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_provider_config__microsoft_outlook_sync_paused
msgid "Outlook Synchronization Paused"
msgstr "Sincronización de Outlook pausada"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "Participante"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "Datos relacionados al contacto del usuario"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "Gente que podrá ver este evento."

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "Teléfono"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "Privacidad"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "Privado"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "Público"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
msgid "Read More"
msgstr "Leer más"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "Recurrencia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "Regla de recurrencia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "Finalización de recurrencia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "Actualización de recurrencia"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "Recurrente"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "Regla recurrente"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "Recordar antes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "Recordatorios"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type_ui
msgid "Repeat"
msgstr "Repetir"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat On"
msgstr "Repetir en"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "Repetir hasta"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Repeat every"
msgstr "Repite cada"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "Repetir cada (días/semana/mes/año)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "Repetir x veces"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/activity/activity_patch.xml:0
#, python-format
msgid "Reschedule"
msgstr "Replanificar"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "Responsable"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "Rrule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "Tipo Rrule"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "Sáb"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "Sábado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "Planificado por"

#. module: calendar
#. odoo-python
#: code:addons/calendar/wizard/mail_activity_schedule.py:0
#, python-format
msgid ""
"Scheduling an activity using the calendar is not possible on more than one "
"record."
msgstr ""
"No es posible programar una actividad con el calendario en más de un "
"registro."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "Buscar reuniones"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "Segundo"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "Seleccione asistentes..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "Enviar correo electrónico a los asistentes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "Enviar invitaciones"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "Enviar correo electrónico"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_reminder
msgid "Sent to all attendees if a reminder is set"
msgstr "Enviado a todos los asistentes si se estableció un recordatorio"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_changedate
msgid "Sent to all attendees if the schedule change"
msgstr "Enviado a todos los asistentes si el programa cambia"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.calendar_settings_action
#: model:ir.ui.menu,name:calendar.menu_calendar_settings
msgid "Settings"
msgstr "Ajustes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__should_show_status
msgid "Should Show Status"
msgstr "Debe mostrar el estado"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "Mostrar como"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Snooze"
msgstr "Posponer"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "Iniciar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr ""
"Fecha de inicio del evento, sin tiempo para eventos que duran un día "
"completo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "Estado"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "Estado:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "Detener"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "Fecha de fin del evento, sin horas para eventos de día completo"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "Asunto"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_popover_delete_view
msgid "Submit"
msgstr "Enviar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "Dom"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "Domingo"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "Sincronice su calendario con Google Calendar"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.calendar_provider_config_view_form
#: model_terms:ir.ui.view,arch_db:calendar.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "Sincronice su calendario con Outlook"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists!"
msgstr "¡El nombre de la etiqueta ya existe!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "Etiquetas"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr ""
"Plantilla utilizada para visualizar el contenido del correo de recordatorio."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "Tentativo"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tentative_count
msgid "Tentative Count"
msgstr "Número tentativo"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr "El"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"El calendario se comparte entre los empleados y se integra por completo con\n"
"                otras aplicaciones como las asistencias del empleado las oportunidades\n"
"                de la empresa."

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "El día debe ser entre 1 y 31."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr ""
"La fecha y hora de finalización no pueden ser anteriores a la fecha y hora "
"de inicio."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "La fecha de finalización no puede ser anterior a la fecha de inicio."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "El intervalo no puede ser negativo. "

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "El número de repeticiones no puede ser negativo."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "No hay asistentes para estos eventos"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "Tercero"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
#, python-format
msgid "This and following events"
msgstr "Este evento y los siguientes"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/ask_recurrence_update_policy_dialog.js:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
#, python-format
msgid "This event"
msgstr "Este evento"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "Jue"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "Jueves"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr ""
"Zona horaria utilizada para mostrar la hora en la plantilla de correo "
"electrónico"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "Reunión de hoy"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__trigger_id
msgid "Trigger"
msgstr "Activador"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "Mar"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "Martes"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "Tipo"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Unable to save the recurrence with \"This Event\""
msgstr "No se puede guardar la recurrencia con \"Este evento\""

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Uncertain"
msgstr "Incierto"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "Unidad"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "Hasta"

#. module: calendar
#: model:mail.template,description:calendar.calendar_template_meeting_update
msgid "Used to manually notifiy attendees"
msgstr "Se utiliza para notificar manualmente a los asistentes"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "User"
msgstr "Usuario"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_can_edit
msgid "User Can Edit"
msgstr "El usuario puede editar"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_source
msgid "Videocall Source"
msgstr "Origen de la videollamada"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form_quick_create
msgid "Videocall URL"
msgstr "URL de la videollamada"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#, python-format
msgid "View"
msgstr "Vista"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "Mié"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "Miércoles"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "Día de la semana"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "Semanas"

#. module: calendar
#: model:onboarding.onboarding.step,description:calendar.onboarding_onboarding_step_setup_calendar_integration
msgid "With Outlook or Google"
msgstr "Con Outlook o Google"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type_ui__yearly
msgid "Yearly"
msgstr "Anualmente"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "Años"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "Sí, asistiré"

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "You can't update a recurrence without base event."
msgstr "No puede actualizar una recurrencia sin evento base."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "No puede duplicar un asistente de calendario."

#. module: calendar
#. odoo-python
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "You have to choose at least one day in the week"
msgstr "Debe elegir al menos un día de la semana"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.xml:0
#, python-format
msgid "You're alone in this meeting"
msgstr "No hay nadie más en esta reunión"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "accepted"
msgstr "aceptada"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "attendees"
msgstr "asistentes"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "awaiting"
msgstr "pendiente"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "declined"
msgstr "rechazada"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "p. ej. Comida de negocios"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g: 12/31/2023"
msgstr "p. ej. 31/12/2023"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "maybe,"
msgstr "tal vez, "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "no,"
msgstr "no,"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee.xml:0
#, python-format
msgid "props.placeholder"
msgstr "props.placeholder"

#. module: calendar
#. odoo-javascript
#: code:addons/calendar/static/src/views/fields/many2many_attendee_expandable.xml:0
#, python-format
msgid "uncertain"
msgstr "incierto"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "yes,"
msgstr "sí,"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - Recordatorio"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: Fecha actualizada"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: Actualización de evento"
