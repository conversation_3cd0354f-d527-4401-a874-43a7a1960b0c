<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="point_of_sale.ProductScreen">
        <div class="product-screen d-flex h-100 bg-100" t-att-class="{ 'd-none': !props.isShown }">
            <div t-if="!ui.isSmall || pos.mobile_pane === 'left'"
                t-att-class="{'flex-grow-1': ui.isSmall}"
                class="leftpane d-flex flex-column border-end bg-200" >
                <OrderWidget lines="currentOrder.orderlines" t-slot-scope="scope"
                    total="env.utils.formatCurrency(currentOrder.get_total_with_tax())"
                    tax="!env.utils.floatIsZero(currentOrder.get_total_tax()) and env.utils.formatCurrency(currentOrder.get_total_tax()) or ''">
                    <t t-set="line" t-value="scope.line" />
                    <Orderline line="line.getDisplayData()"
                        t-on-click="() => this.selectLine(line)"
                        class="{ ...line.getDisplayClasses(), 'selected' : line.selected }">
                        <t t-set-slot="product-name">
                            <i  t-if="line.get_product().isTracked()"
                                t-on-click.stop="() => line.editPackLotLines()" role="img"
                                t-attf-class="{{ line.has_valid_product_lot() ? 'text-success' : 'text-danger'}} fa fa-list line-lot-icon ms-1"
                                t-attf-title="{{ line.has_valid_product_lot() ? 'Valid product lot' : 'Invalid product lot'}}" />
                        </t>
                        <ul t-if="line.pack_lot_lines">
                            <li t-foreach="line.get_lot_lines()" t-as="lot" t-key="lot.cid">
                                SN <t t-esc="lot.lot_name"/>
                            </li>
                        </ul>
                    </Orderline>
                    <t t-set-slot="details" />
                </OrderWidget>
                <div class="pads border-top">
                    <div class="control-buttons d-flex flex-wrap border-bottom overflow-hidden bg-300"
                        t-if="!ui.isSmall">
                        <t t-foreach="controlButtons" t-as="cb" t-key="cb.name">
                            <t t-component="cb.component" t-key="cb.name"/>
                        </t>
                    </div>
                    <div class="subpads d-flex">
                        <ActionpadWidget
                            partner="partner"
                            actionName="constructor.numpadActionName"
                            actionType="'payment'"
                            onClickMore.bind="displayAllControlPopup" />
                        <Numpad buttons="getNumpadButtons()" onClick.bind="onNumpadClick" class="'w-100'"/>
                    </div>
                </div>
            </div>
            <div class="rightpane overflow-auto d-flex flex-grow-1 flex-column bg-300 w-60" t-if="!ui.isSmall || pos.mobile_pane === 'right'">
                <ProductsWidget />
                <t t-if="ui.isSmall">
                    <div class="product-reminder d-flex justify-content-center align-items-center py-1 text-bg-warning bg-opacity-50 fw-bolder" t-if="currentOrder.get_selected_orderline() and currentOrder.hasJustAddedProduct" t-key="animationKey" >
                        <span><t t-esc="selectedOrderlineQuantity"/> <t t-esc="selectedOrderlineDisplayName"/> <t t-esc="selectedOrderlineTotal"/></span>
                    </div>
                    <div class="switchpane d-flex h-12">
                        <button class="btn-switchpane pay-button btn w-50 rounded-0 fw-bolder" t-attf-class="{{ primaryPayButton ? 'btn-primary' : 'btn-secondary' }}" t-on-click="() => currentOrder.pay()">
                            <span class="fs-1 d-block">Pay</span>
                            <span><t t-esc="total" /></span>
                        </button>
                        <button class="btn-switchpane btn w-50 btn-secondary rounded-0 fw-bolder review-button" t-on-click="switchPane">
                            <span class="fs-1 d-block">Review</span>
                            <span><t t-esc="items"/> items</span>
                        </button>
                    </div>
                </t>
            </div>
        </div>
    </t>

</templates>
