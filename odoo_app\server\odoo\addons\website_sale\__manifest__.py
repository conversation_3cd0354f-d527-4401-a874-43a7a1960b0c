# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'eCommerce',
    'category': 'Website/Website',
    'sequence': 50,
    'summary': 'Sell your products online',
    'website': 'https://www.odoo.com/app/ecommerce',
    'version': '1.1',
    'depends': ['website', 'sale', 'website_payment', 'website_mail', 'portal_rating', 'digest', 'delivery'],
    'data': [
        'security/ir.model.access.csv',
        'security/website_sale.xml',

        'data/data.xml',
        'data/mail_template_data.xml',
        'data/product_snippet_template_data.xml',
        'data/digest_data.xml',
        'data/ir_cron_data.xml',

        'report/sale_report_views.xml',

        'views/account_move_views.xml',
        'views/crm_team_views.xml',
        'views/digest_views.xml',
        'views/product_attribute_views.xml',
        'views/product_document_views.xml',
        'views/product_tag_views.xml',
        'views/product_views.xml',
        'views/sale_order_views.xml',
        'views/templates.xml',
        'views/snippets/snippets.xml',
        'views/snippets/s_add_to_cart.xml',
        'views/snippets/s_dynamic_snippet_products.xml',
        'views/snippets/s_popup.xml',
        'views/res_config_settings_views.xml',
        'views/website_sale_visitor_views.xml',
        'views/website_base_unit_views.xml',
        'views/product_product_add.xml',
        'views/website_views.xml',
        'views/website_pages_views.xml',
        'views/website_sale_delivery_templates.xml',
        'views/website_sale_menus.xml',
        'views/website_sale_delivery_views.xml',
        'views/variant_templates.xml',
    ],
    'demo': [
        'data/demo.xml',
    ],
    'installable': True,
    'application': True,
    'post_init_hook': '_post_init_hook',
    'uninstall_hook': 'uninstall_hook',
    'assets': {
        'web.assets_frontend': [
            'website_sale/static/src/js/tours/tour_utils.js',
            'website_sale/static/src/scss/website_sale.scss',
            'website_sale/static/src/scss/website_mail.scss',
            'website_sale/static/src/scss/website_sale_frontend.scss',
            'website_sale/static/src/scss/website_sale_delivery.scss',
            'website/static/lib/multirange/multirange_custom.scss',
            'sale/static/src/scss/sale_portal.scss',
            'website_sale/static/src/js/payment_button.js',
            'website_sale/static/src/js/payment_form.js',
            'website_sale/static/src/js/sale_variant_mixin.js',
            'website_sale/static/src/js/terms_and_conditions_checkbox.js',
            'website_sale/static/src/js/variant_mixin.js',
            'website_sale/static/src/js/website_sale.js',
            'website_sale/static/src/xml/website_sale.xml',
            'website_sale/static/src/js/website_sale_offcanvas.js',
            'website_sale/static/src/js/website_sale_price_range_option.js',
            'website_sale/static/src/js/website_sale_utils.js',
            'website_sale/static/src/xml/website_sale_utils.xml',
            'website_sale/static/src/js/website_sale_recently_viewed.js',
            'website_sale/static/src/js/website_sale_tracking.js',
            'website/static/lib/multirange/multirange_custom.js',
            'website/static/lib/multirange/multirange_instance.js',
            'website_sale/static/src/js/website_sale_category_link.js',
            'website_sale/static/src/xml/website_sale_image_viewer.xml',
            'website_sale/static/src/js/components/website_sale_image_viewer.js',
            'website_sale/static/src/xml/website_sale_reorder_modal.xml',
            'website_sale/static/src/js/website_sale_reorder.js',
            'website_sale/static/src/js/website_sale_delivery.js',
            'website_sale/static/src/scss/product_configurator.scss',
            'website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.js',
            'website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml',
            'website_sale/static/src/js/notification/cart_notification/cart_notification.js',
            'website_sale/static/src/js/notification/cart_notification/cart_notification.xml',
            'website_sale/static/src/js/notification/warning_notification/warning_notification.js',
            'website_sale/static/src/js/notification/warning_notification/warning_notification.xml',
            'website_sale/static/src/js/notification/notification_service.js',
        ],
        'web._assets_primary_variables': [
            'website_sale/static/src/scss/primary_variables.scss',
        ],
        'web.assets_backend': [
            'website_sale/static/src/js/tours/tour_utils.js',
            'website_sale/static/src/js/website_sale_video_field_preview.js',
            'website_sale/static/src/scss/website_sale_backend.scss',
            'website_sale/static/src/js/tours/website_sale_shop.js',
            'website_sale/static/src/xml/website_sale.xml',
        ],
        'website.assets_wysiwyg': [
            'website_sale/static/src/scss/website_sale.editor.scss',
            'website_sale/static/src/snippets/s_dynamic_snippet_products/options.js',
            'website_sale/static/src/snippets/s_add_to_cart/options.js',
            'website_sale/static/src/js/website_sale.editor.js',
            'website_sale/static/src/js/website_sale_form_editor.js',
        ],
        'website.assets_editor': [
            'website_sale/static/src/js/systray_items/*.js',
            'website_sale/static/src/xml/website_sale_utils.xml',
        ],
        'website.backend_assets_all_wysiwyg': [
            'website_sale/static/src/js/components/wysiwyg_adapter/wysiwyg_adapter.js',
        ],
        'web.assets_tests': [
            'website_sale/static/tests/**/*',
        ],
    },
    'license': 'LGPL-3',
}
