<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="imghdr — Determine the type of an image" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/imghdr.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/imghdr.py The imghdr module determines the type of image contained in a file or byte stream. The imghdr module defines the following function: The following image types are recogni..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/imghdr.py The imghdr module determines the type of image contained in a file or byte stream. The imghdr module defines the following function: The following image types are recogni..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>imghdr — Determine the type of an image &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="mailcap — Mailcap file handling" href="mailcap.html" />
    <link rel="prev" title="crypt — Function to check Unix passwords" href="crypt.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/imghdr.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="crypt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">crypt</span></code> — Function to check Unix passwords</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mailcap.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code> — Mailcap file handling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/imghdr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mailcap.html" title="mailcap — Mailcap file handling"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="crypt.html" title="crypt — Function to check Unix passwords"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code> — Determine the type of an image</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-imghdr">
<span id="imghdr-determine-the-type-of-an-image"></span><h1><a class="reference internal" href="#module-imghdr" title="imghdr: Determine the type of image contained in a file or byte stream. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code></a> — Determine the type of an image<a class="headerlink" href="#module-imghdr" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/imghdr.py">Lib/imghdr.py</a></p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-imghdr" title="imghdr: Determine the type of image contained in a file or byte stream. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code></a> module is deprecated
(see <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#imghdr"><strong>PEP 594</strong></a> for details and alternatives).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-imghdr" title="imghdr: Determine the type of image contained in a file or byte stream. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code></a> module determines the type of image contained in a file or
byte stream.</p>
<p>The <a class="reference internal" href="#module-imghdr" title="imghdr: Determine the type of image contained in a file or byte stream. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code></a> module defines the following function:</p>
<dl class="py function">
<dt class="sig sig-object py" id="imghdr.what">
<span class="sig-prename descclassname"><span class="pre">imghdr.</span></span><span class="sig-name descname"><span class="pre">what</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">h</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imghdr.what" title="Link to this definition">¶</a></dt>
<dd><p>Test the image data contained in the file named <em>file</em> and return a
string describing the image type.  If <em>h</em> is provided, the <em>file</em>
argument is ignored and <em>h</em> is assumed to contain the byte stream to test.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<p>The following image types are recognized, as listed below with the return value
from <a class="reference internal" href="#imghdr.what" title="imghdr.what"><code class="xref py py-func docutils literal notranslate"><span class="pre">what()</span></code></a>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value</p></th>
<th class="head"><p>Image format</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'rgb'</span></code></p></td>
<td><p>SGI ImgLib Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'gif'</span></code></p></td>
<td><p>GIF 87a and 89a Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'pbm'</span></code></p></td>
<td><p>Portable Bitmap Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'pgm'</span></code></p></td>
<td><p>Portable Graymap Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'ppm'</span></code></p></td>
<td><p>Portable Pixmap Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'tiff'</span></code></p></td>
<td><p>TIFF Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'rast'</span></code></p></td>
<td><p>Sun Raster Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'xbm'</span></code></p></td>
<td><p>X Bitmap Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'jpeg'</span></code></p></td>
<td><p>JPEG data in JFIF or Exif formats</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'bmp'</span></code></p></td>
<td><p>BMP files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'png'</span></code></p></td>
<td><p>Portable Network Graphics</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'webp'</span></code></p></td>
<td><p>WebP files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'exr'</span></code></p></td>
<td><p>OpenEXR Files</p></td>
</tr>
</tbody>
</table>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>The <em>exr</em> and <em>webp</em> formats were added.</p>
</div>
<p>You can extend the list of file types <a class="reference internal" href="#module-imghdr" title="imghdr: Determine the type of image contained in a file or byte stream. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code></a> can recognize by appending
to this variable:</p>
<dl class="py data">
<dt class="sig sig-object py" id="imghdr.tests">
<span class="sig-prename descclassname"><span class="pre">imghdr.</span></span><span class="sig-name descname"><span class="pre">tests</span></span><a class="headerlink" href="#imghdr.tests" title="Link to this definition">¶</a></dt>
<dd><p>A list of functions performing the individual tests.  Each function takes two
arguments: the byte-stream and an open file-like object. When <a class="reference internal" href="#imghdr.what" title="imghdr.what"><code class="xref py py-func docutils literal notranslate"><span class="pre">what()</span></code></a> is
called with a byte-stream, the file-like object will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>The test function should return a string describing the image type if the test
succeeded, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if it failed.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">imghdr</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">imghdr</span><span class="o">.</span><span class="n">what</span><span class="p">(</span><span class="s1">&#39;bass.gif&#39;</span><span class="p">)</span>
<span class="go">&#39;gif&#39;</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="crypt.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">crypt</span></code> — Function to check Unix passwords</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mailcap.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code> — Mailcap file handling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/imghdr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mailcap.html" title="mailcap — Mailcap file handling"
             >next</a> |</li>
        <li class="right" >
          <a href="crypt.html" title="crypt — Function to check Unix passwords"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code> — Determine the type of an image</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>