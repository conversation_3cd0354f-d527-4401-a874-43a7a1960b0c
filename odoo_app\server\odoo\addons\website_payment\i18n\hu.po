# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# Tamás Dombos, 2023
# krnkris, 2023
# Szabolcs Rádi, 2023
# <PERSON><PERSON>é<PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Ország...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Nem található megfelelő fizetési lehetőség.</strong><br/>\n"
"                                Lépjen kapcsolatba a weboldal adminisztrátorával, ha úgy gondolja, hogy ez egy hiba."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr ""

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_activate_stripe
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr "Stripe aktiválása"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Adjon hozzá egy leírást"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "Összeg"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Amount ("
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások módosítása"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr "Ország"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Country\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "Ország megadása kötelező."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Tisztelt"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr "Meghatározások"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donate"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr "Adományozás"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr "Adományozás gomb"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "Érvénytelen e-mail cím"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "Mail cím megadása kötelező."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "Behozatal"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr "Maximum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Name"
msgstr "Név"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Name\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "Név megadása kötelező."

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "Nincs"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr ""

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Payment Details"
msgstr "Fizetési művelet részletei"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr "Fizetési szolgáltató"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Fizetési tranzakció"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr ""

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr ""

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "Fizetések"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "Válasszon ki, vagy adjon meg egy összeget"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "Címzett email címe"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr "Shop - Fizetés"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "Néhány információ hiányzik a fizetés végrehajtásához."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr "Lépés"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay, etc. as well as recurring charges."
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr ""

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "Nincs fizetendő tétel"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr ""

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "Honlap"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Write us a comment"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Your comment"
msgstr ""

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr ""
