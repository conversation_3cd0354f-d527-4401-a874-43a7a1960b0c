<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="winsound — Sound-playing interface for Windows" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/winsound.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The winsound module provides access to the basic sound-playing machinery provided by Windows platforms. It includes functions and several constants." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The winsound module provides access to the basic sound-playing machinery provided by Windows platforms. It includes functions and several constants." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>winsound — Sound-playing interface for Windows &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Unix Specific Services" href="unix.html" />
    <link rel="prev" title="winreg — Windows registry access" href="winreg.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/winsound.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="winreg.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unix.html"
                          title="next chapter">Unix Specific Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/winsound.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unix.html" title="Unix Specific Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="winreg.html" title="winreg — Windows registry access"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" accesskey="U">MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code> — Sound-playing interface for Windows</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-winsound">
<span id="winsound-sound-playing-interface-for-windows"></span><h1><a class="reference internal" href="#module-winsound" title="winsound: Access to the sound-playing machinery for Windows. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code></a> — Sound-playing interface for Windows<a class="headerlink" href="#module-winsound" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-winsound" title="winsound: Access to the sound-playing machinery for Windows. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code></a> module provides access to the basic sound-playing machinery
provided by Windows platforms.  It includes functions and several constants.</p>
<dl class="py function">
<dt class="sig sig-object py" id="winsound.Beep">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">Beep</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frequency</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">duration</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winsound.Beep" title="Link to this definition">¶</a></dt>
<dd><p>Beep the PC’s speaker. The <em>frequency</em> parameter specifies frequency, in hertz,
of the sound, and must be in the range 37 through 32,767. The <em>duration</em>
parameter specifies the number of milliseconds the sound should last.  If the
system is not able to beep the speaker, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winsound.PlaySound">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">PlaySound</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sound</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winsound.PlaySound" title="Link to this definition">¶</a></dt>
<dd><p>Call the underlying <code class="xref c c-func docutils literal notranslate"><span class="pre">PlaySound()</span></code> function from the Platform API.  The
<em>sound</em> parameter may be a filename, a system sound alias, audio data as a
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  Its
interpretation depends on the value of <em>flags</em>, which can be a bitwise ORed
combination of the constants described below. If the <em>sound</em> parameter is
<code class="docutils literal notranslate"><span class="pre">None</span></code>, any currently playing waveform sound is stopped. If the system
indicates an error, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="winsound.MessageBeep">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MessageBeep</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">MB_OK</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#winsound.MessageBeep" title="Link to this definition">¶</a></dt>
<dd><p>Call the underlying <code class="xref c c-func docutils literal notranslate"><span class="pre">MessageBeep()</span></code> function from the Platform API.  This
plays a sound as specified in the registry.  The <em>type</em> argument specifies which
sound to play; possible values are <code class="docutils literal notranslate"><span class="pre">-1</span></code>, <code class="docutils literal notranslate"><span class="pre">MB_ICONASTERISK</span></code>,
<code class="docutils literal notranslate"><span class="pre">MB_ICONEXCLAMATION</span></code>, <code class="docutils literal notranslate"><span class="pre">MB_ICONHAND</span></code>, <code class="docutils literal notranslate"><span class="pre">MB_ICONQUESTION</span></code>, and <code class="docutils literal notranslate"><span class="pre">MB_OK</span></code>, all
described below.  The value <code class="docutils literal notranslate"><span class="pre">-1</span></code> produces a “simple beep”; this is the final
fallback if a sound cannot be played otherwise.  If the system indicates an
error, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_FILENAME">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_FILENAME</span></span><a class="headerlink" href="#winsound.SND_FILENAME" title="Link to this definition">¶</a></dt>
<dd><p>The <em>sound</em> parameter is the name of a WAV file. Do not use with
<a class="reference internal" href="#winsound.SND_ALIAS" title="winsound.SND_ALIAS"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_ALIAS</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_ALIAS">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_ALIAS</span></span><a class="headerlink" href="#winsound.SND_ALIAS" title="Link to this definition">¶</a></dt>
<dd><p>The <em>sound</em> parameter is a sound association name from the registry.  If the
registry contains no such name, play the system default sound unless
<a class="reference internal" href="#winsound.SND_NODEFAULT" title="winsound.SND_NODEFAULT"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_NODEFAULT</span></code></a> is also specified. If no default sound is registered,
raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>. Do not use with <a class="reference internal" href="#winsound.SND_FILENAME" title="winsound.SND_FILENAME"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_FILENAME</span></code></a>.</p>
<p>All Win32 systems support at least the following; most systems support many
more:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><a class="reference internal" href="#winsound.PlaySound" title="winsound.PlaySound"><code class="xref py py-func docutils literal notranslate"><span class="pre">PlaySound()</span></code></a> <em>name</em></p></th>
<th class="head"><p>Corresponding Control Panel Sound name</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'SystemAsterisk'</span></code></p></td>
<td><p>Asterisk</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'SystemExclamation'</span></code></p></td>
<td><p>Exclamation</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'SystemExit'</span></code></p></td>
<td><p>Exit Windows</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'SystemHand'</span></code></p></td>
<td><p>Critical Stop</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'SystemQuestion'</span></code></p></td>
<td><p>Question</p></td>
</tr>
</tbody>
</table>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">winsound</span>
<span class="c1"># Play Windows exit sound.</span>
<span class="n">winsound</span><span class="o">.</span><span class="n">PlaySound</span><span class="p">(</span><span class="s2">&quot;SystemExit&quot;</span><span class="p">,</span> <span class="n">winsound</span><span class="o">.</span><span class="n">SND_ALIAS</span><span class="p">)</span>

<span class="c1"># Probably play Windows default sound, if any is registered (because</span>
<span class="c1"># &quot;*&quot; probably isn&#39;t the registered name of any sound).</span>
<span class="n">winsound</span><span class="o">.</span><span class="n">PlaySound</span><span class="p">(</span><span class="s2">&quot;*&quot;</span><span class="p">,</span> <span class="n">winsound</span><span class="o">.</span><span class="n">SND_ALIAS</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_LOOP">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_LOOP</span></span><a class="headerlink" href="#winsound.SND_LOOP" title="Link to this definition">¶</a></dt>
<dd><p>Play the sound repeatedly.  The <a class="reference internal" href="#winsound.SND_ASYNC" title="winsound.SND_ASYNC"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_ASYNC</span></code></a> flag must also be used to
avoid blocking.  Cannot be used with <a class="reference internal" href="#winsound.SND_MEMORY" title="winsound.SND_MEMORY"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_MEMORY</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_MEMORY">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_MEMORY</span></span><a class="headerlink" href="#winsound.SND_MEMORY" title="Link to this definition">¶</a></dt>
<dd><p>The <em>sound</em> parameter to <a class="reference internal" href="#winsound.PlaySound" title="winsound.PlaySound"><code class="xref py py-func docutils literal notranslate"><span class="pre">PlaySound()</span></code></a> is a memory image of a WAV file, as a
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This module does not support playing from a memory image asynchronously, so a
combination of this flag and <a class="reference internal" href="#winsound.SND_ASYNC" title="winsound.SND_ASYNC"><code class="xref py py-const docutils literal notranslate"><span class="pre">SND_ASYNC</span></code></a> will raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_PURGE">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_PURGE</span></span><a class="headerlink" href="#winsound.SND_PURGE" title="Link to this definition">¶</a></dt>
<dd><p>Stop playing all instances of the specified sound.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This flag is not supported on modern Windows platforms.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_ASYNC">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_ASYNC</span></span><a class="headerlink" href="#winsound.SND_ASYNC" title="Link to this definition">¶</a></dt>
<dd><p>Return immediately, allowing sounds to play asynchronously.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_NODEFAULT">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_NODEFAULT</span></span><a class="headerlink" href="#winsound.SND_NODEFAULT" title="Link to this definition">¶</a></dt>
<dd><p>If the specified sound cannot be found, do not play the system default sound.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_NOSTOP">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_NOSTOP</span></span><a class="headerlink" href="#winsound.SND_NOSTOP" title="Link to this definition">¶</a></dt>
<dd><p>Do not interrupt sounds currently playing.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.SND_NOWAIT">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">SND_NOWAIT</span></span><a class="headerlink" href="#winsound.SND_NOWAIT" title="Link to this definition">¶</a></dt>
<dd><p>Return immediately if the sound driver is busy.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This flag is not supported on modern Windows platforms.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.MB_ICONASTERISK">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MB_ICONASTERISK</span></span><a class="headerlink" href="#winsound.MB_ICONASTERISK" title="Link to this definition">¶</a></dt>
<dd><p>Play the <code class="docutils literal notranslate"><span class="pre">SystemDefault</span></code> sound.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.MB_ICONEXCLAMATION">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MB_ICONEXCLAMATION</span></span><a class="headerlink" href="#winsound.MB_ICONEXCLAMATION" title="Link to this definition">¶</a></dt>
<dd><p>Play the <code class="docutils literal notranslate"><span class="pre">SystemExclamation</span></code> sound.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.MB_ICONHAND">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MB_ICONHAND</span></span><a class="headerlink" href="#winsound.MB_ICONHAND" title="Link to this definition">¶</a></dt>
<dd><p>Play the <code class="docutils literal notranslate"><span class="pre">SystemHand</span></code> sound.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.MB_ICONQUESTION">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MB_ICONQUESTION</span></span><a class="headerlink" href="#winsound.MB_ICONQUESTION" title="Link to this definition">¶</a></dt>
<dd><p>Play the <code class="docutils literal notranslate"><span class="pre">SystemQuestion</span></code> sound.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="winsound.MB_OK">
<span class="sig-prename descclassname"><span class="pre">winsound.</span></span><span class="sig-name descname"><span class="pre">MB_OK</span></span><a class="headerlink" href="#winsound.MB_OK" title="Link to this definition">¶</a></dt>
<dd><p>Play the <code class="docutils literal notranslate"><span class="pre">SystemDefault</span></code> sound.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="winreg.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unix.html"
                          title="next chapter">Unix Specific Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/winsound.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unix.html" title="Unix Specific Services"
             >next</a> |</li>
        <li class="right" >
          <a href="winreg.html" title="winreg — Windows registry access"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" >MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">winsound</span></code> — Sound-playing interface for Windows</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>