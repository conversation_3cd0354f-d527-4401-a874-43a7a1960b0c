<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="mimetypes — Map filenames to MIME types" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/mimetypes.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/mimetypes.py The mimetypes module converts between a filename or URL and the MIME type associated with the filename extension. Conversions are provided from filename to MIME type a..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/mimetypes.py The mimetypes module converts between a filename or URL and the MIME type associated with the filename extension. Conversions are provided from filename to MIME type a..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>mimetypes — Map filenames to MIME types &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="base64 — Base16, Base32, Base64, Base85 Data Encodings" href="base64.html" />
    <link rel="prev" title="mailbox — Manipulate mailboxes in various formats" href="mailbox.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/mimetypes.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a><ul>
<li><a class="reference internal" href="#mimetypes-objects">MimeTypes Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mailbox.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="base64.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">base64</span></code> — Base16, Base32, Base64, Base85 Data Encodings</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mimetypes.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="base64.html" title="base64 — Base16, Base32, Base64, Base85 Data Encodings"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="mailbox.html" title="mailbox — Manipulate mailboxes in various formats"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" accesskey="U">Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-mimetypes">
<span id="mimetypes-map-filenames-to-mime-types"></span><h1><a class="reference internal" href="#module-mimetypes" title="mimetypes: Mapping of filename extensions to MIME types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code></a> — Map filenames to MIME types<a class="headerlink" href="#module-mimetypes" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/mimetypes.py">Lib/mimetypes.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-mimetypes" title="mimetypes: Mapping of filename extensions to MIME types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code></a> module converts between a filename or URL and the MIME type
associated with the filename extension.  Conversions are provided from filename
to MIME type and from MIME type to filename extension; encodings are not
supported for the latter conversion.</p>
<p>The module provides one class and a number of convenience functions. The
functions are the normal interface to this module, but some applications may be
interested in the class as well.</p>
<p>The functions described below provide the primary interface for this module.  If
the module has not been initialized, they will call <a class="reference internal" href="#mimetypes.init" title="mimetypes.init"><code class="xref py py-func docutils literal notranslate"><span class="pre">init()</span></code></a> if they rely on
the information <a class="reference internal" href="#mimetypes.init" title="mimetypes.init"><code class="xref py py-func docutils literal notranslate"><span class="pre">init()</span></code></a> sets up.</p>
<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.guess_type">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">guess_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.guess_type" title="Link to this definition">¶</a></dt>
<dd><p id="index-1">Guess the type of a file based on its filename, path or URL, given by <em>url</em>.
URL can be a string or a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
<p>The return value is a tuple <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">encoding)</span></code> where <em>type</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> if the
type can’t be guessed (missing or unknown suffix) or a string of the form
<code class="docutils literal notranslate"><span class="pre">'type/subtype'</span></code>, usable for a MIME <em class="mailheader">content-type</em> header.</p>
<p><em>encoding</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> for no encoding or the name of the program used to encode
(e.g. <strong class="program">compress</strong> or <strong class="program">gzip</strong>). The encoding is suitable for use
as a <em class="mailheader">Content-Encoding</em> header, <strong>not</strong> as a
<em class="mailheader">Content-Transfer-Encoding</em> header. The mappings are table driven.
Encoding suffixes are case sensitive; type suffixes are first tried case
sensitively, then case insensitively.</p>
<p>The optional <em>strict</em> argument is a flag specifying whether the list of known MIME types
is limited to only the official types <a class="reference external" href="https://www.iana.org/assignments/media-types/media-types.xhtml">registered with IANA</a>.
When <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default), only the IANA types are supported; when
<em>strict</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, some additional non-standard but commonly used MIME types
are also recognized.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added support for url being a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.guess_all_extensions">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">guess_all_extensions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.guess_all_extensions" title="Link to this definition">¶</a></dt>
<dd><p>Guess the extensions for a file based on its MIME type, given by <em>type</em>. The
return value is a list of strings giving all possible filename extensions,
including the leading dot (<code class="docutils literal notranslate"><span class="pre">'.'</span></code>).  The extensions are not guaranteed to have
been associated with any particular data stream, but would be mapped to the MIME
type <em>type</em> by <a class="reference internal" href="#mimetypes.guess_type" title="mimetypes.guess_type"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_type()</span></code></a>.</p>
<p>The optional <em>strict</em> argument has the same meaning as with the <a class="reference internal" href="#mimetypes.guess_type" title="mimetypes.guess_type"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_type()</span></code></a> function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.guess_extension">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">guess_extension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.guess_extension" title="Link to this definition">¶</a></dt>
<dd><p>Guess the extension for a file based on its MIME type, given by <em>type</em>. The
return value is a string giving a filename extension, including the leading dot
(<code class="docutils literal notranslate"><span class="pre">'.'</span></code>).  The extension is not guaranteed to have been associated with any
particular data stream, but would be mapped to the MIME type <em>type</em> by
<a class="reference internal" href="#mimetypes.guess_type" title="mimetypes.guess_type"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_type()</span></code></a>.  If no extension can be guessed for <em>type</em>, <code class="docutils literal notranslate"><span class="pre">None</span></code> is
returned.</p>
<p>The optional <em>strict</em> argument has the same meaning as with the <a class="reference internal" href="#mimetypes.guess_type" title="mimetypes.guess_type"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_type()</span></code></a> function.</p>
</dd></dl>

<p>Some additional functions and data items are available for controlling the
behavior of the module.</p>
<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.init">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">files</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.init" title="Link to this definition">¶</a></dt>
<dd><p>Initialize the internal data structures.  If given, <em>files</em> must be a sequence
of file names which should be used to augment the default type map.  If omitted,
the file names to use are taken from <a class="reference internal" href="#mimetypes.knownfiles" title="mimetypes.knownfiles"><code class="xref py py-const docutils literal notranslate"><span class="pre">knownfiles</span></code></a>; on Windows, the
current registry settings are loaded.  Each file named in <em>files</em> or
<a class="reference internal" href="#mimetypes.knownfiles" title="mimetypes.knownfiles"><code class="xref py py-const docutils literal notranslate"><span class="pre">knownfiles</span></code></a> takes precedence over those named before it.  Calling
<a class="reference internal" href="#mimetypes.init" title="mimetypes.init"><code class="xref py py-func docutils literal notranslate"><span class="pre">init()</span></code></a> repeatedly is allowed.</p>
<p>Specifying an empty list for <em>files</em> will prevent the system defaults from
being applied: only the well-known values will be present from a built-in list.</p>
<p>If <em>files</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> the internal data structure is completely rebuilt to its
initial default value. This is a stable operation and will produce the same results
when called multiple times.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Previously, Windows registry settings were ignored.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.read_mime_types">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">read_mime_types</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.read_mime_types" title="Link to this definition">¶</a></dt>
<dd><p>Load the type map given in the file <em>filename</em>, if it exists.  The type map is
returned as a dictionary mapping filename extensions, including the leading dot
(<code class="docutils literal notranslate"><span class="pre">'.'</span></code>), to strings of the form <code class="docutils literal notranslate"><span class="pre">'type/subtype'</span></code>.  If the file <em>filename</em>
does not exist or cannot be read, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="mimetypes.add_type">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">add_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ext</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.add_type" title="Link to this definition">¶</a></dt>
<dd><p>Add a mapping from the MIME type <em>type</em> to the extension <em>ext</em>. When the
extension is already known, the new type will replace the old one. When the type
is already known the extension will be added to the list of known extensions.</p>
<p>When <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default), the mapping will be added to the
official MIME types, otherwise to the non-standard ones.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.inited">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">inited</span></span><a class="headerlink" href="#mimetypes.inited" title="Link to this definition">¶</a></dt>
<dd><p>Flag indicating whether or not the global data structures have been initialized.
This is set to <code class="docutils literal notranslate"><span class="pre">True</span></code> by <a class="reference internal" href="#mimetypes.init" title="mimetypes.init"><code class="xref py py-func docutils literal notranslate"><span class="pre">init()</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.knownfiles">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">knownfiles</span></span><a class="headerlink" href="#mimetypes.knownfiles" title="Link to this definition">¶</a></dt>
<dd><p id="index-2">List of type map file names commonly installed.  These files are typically named
<code class="file docutils literal notranslate"><span class="pre">mime.types</span></code> and are installed in different locations by different
packages.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.suffix_map">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">suffix_map</span></span><a class="headerlink" href="#mimetypes.suffix_map" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping suffixes to suffixes.  This is used to allow recognition of
encoded files for which the encoding and the type are indicated by the same
extension.  For example, the <code class="file docutils literal notranslate"><span class="pre">.tgz</span></code> extension is mapped to <code class="file docutils literal notranslate"><span class="pre">.tar.gz</span></code>
to allow the encoding and type to be recognized separately.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.encodings_map">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">encodings_map</span></span><a class="headerlink" href="#mimetypes.encodings_map" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping filename extensions to encoding types.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.types_map">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">types_map</span></span><a class="headerlink" href="#mimetypes.types_map" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping filename extensions to MIME types.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="mimetypes.common_types">
<span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">common_types</span></span><a class="headerlink" href="#mimetypes.common_types" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping filename extensions to non-standard, but commonly found MIME
types.</p>
</dd></dl>

<p>An example usage of the module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">mimetypes</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mimetypes</span><span class="o">.</span><span class="n">init</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mimetypes</span><span class="o">.</span><span class="n">knownfiles</span>
<span class="go">[&#39;/etc/mime.types&#39;, &#39;/etc/httpd/mime.types&#39;, ... ]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mimetypes</span><span class="o">.</span><span class="n">suffix_map</span><span class="p">[</span><span class="s1">&#39;.tgz&#39;</span><span class="p">]</span>
<span class="go">&#39;.tar.gz&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mimetypes</span><span class="o">.</span><span class="n">encodings_map</span><span class="p">[</span><span class="s1">&#39;.gz&#39;</span><span class="p">]</span>
<span class="go">&#39;gzip&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mimetypes</span><span class="o">.</span><span class="n">types_map</span><span class="p">[</span><span class="s1">&#39;.tgz&#39;</span><span class="p">]</span>
<span class="go">&#39;application/x-tar-gz&#39;</span>
</pre></div>
</div>
<section id="mimetypes-objects">
<span id="id1"></span><h2>MimeTypes Objects<a class="headerlink" href="#mimetypes-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#mimetypes.MimeTypes" title="mimetypes.MimeTypes"><code class="xref py py-class docutils literal notranslate"><span class="pre">MimeTypes</span></code></a> class may be useful for applications which may want more
than one MIME-type database; it provides an interface similar to the one of the
<a class="reference internal" href="#module-mimetypes" title="mimetypes: Mapping of filename extensions to MIME types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code></a> module.</p>
<dl class="py class">
<dt class="sig sig-object py" id="mimetypes.MimeTypes">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">mimetypes.</span></span><span class="sig-name descname"><span class="pre">MimeTypes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filenames</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes" title="Link to this definition">¶</a></dt>
<dd><p>This class represents a MIME-types database.  By default, it provides access to
the same database as the rest of this module. The initial database is a copy of
that provided by the module, and may be extended by loading additional
<code class="file docutils literal notranslate"><span class="pre">mime.types</span></code>-style files into the database using the <a class="reference internal" href="#mimetypes.MimeTypes.read" title="mimetypes.MimeTypes.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> or
<a class="reference internal" href="#mimetypes.MimeTypes.readfp" title="mimetypes.MimeTypes.readfp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readfp()</span></code></a> methods.  The mapping dictionaries may also be cleared before
loading additional data if the default data is not desired.</p>
<p>The optional <em>filenames</em> parameter can be used to cause additional files to be
loaded “on top” of the default database.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.suffix_map">
<span class="sig-name descname"><span class="pre">suffix_map</span></span><a class="headerlink" href="#mimetypes.MimeTypes.suffix_map" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping suffixes to suffixes.  This is used to allow recognition of
encoded files for which the encoding and the type are indicated by the same
extension.  For example, the <code class="file docutils literal notranslate"><span class="pre">.tgz</span></code> extension is mapped to <code class="file docutils literal notranslate"><span class="pre">.tar.gz</span></code>
to allow the encoding and type to be recognized separately.  This is initially a
copy of the global <a class="reference internal" href="#mimetypes.suffix_map" title="mimetypes.suffix_map"><code class="xref py py-data docutils literal notranslate"><span class="pre">suffix_map</span></code></a> defined in the module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.encodings_map">
<span class="sig-name descname"><span class="pre">encodings_map</span></span><a class="headerlink" href="#mimetypes.MimeTypes.encodings_map" title="Link to this definition">¶</a></dt>
<dd><p>Dictionary mapping filename extensions to encoding types.  This is initially a
copy of the global <a class="reference internal" href="#mimetypes.encodings_map" title="mimetypes.encodings_map"><code class="xref py py-data docutils literal notranslate"><span class="pre">encodings_map</span></code></a> defined in the module.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.types_map">
<span class="sig-name descname"><span class="pre">types_map</span></span><a class="headerlink" href="#mimetypes.MimeTypes.types_map" title="Link to this definition">¶</a></dt>
<dd><p>Tuple containing two dictionaries, mapping filename extensions to MIME types:
the first dictionary is for the non-standards types and the second one is for
the standard types. They are initialized by <a class="reference internal" href="#mimetypes.common_types" title="mimetypes.common_types"><code class="xref py py-data docutils literal notranslate"><span class="pre">common_types</span></code></a> and
<a class="reference internal" href="#mimetypes.types_map" title="mimetypes.types_map"><code class="xref py py-data docutils literal notranslate"><span class="pre">types_map</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.types_map_inv">
<span class="sig-name descname"><span class="pre">types_map_inv</span></span><a class="headerlink" href="#mimetypes.MimeTypes.types_map_inv" title="Link to this definition">¶</a></dt>
<dd><p>Tuple containing two dictionaries, mapping MIME types to a list of filename
extensions: the first dictionary is for the non-standards types and the
second one is for the standard types. They are initialized by
<a class="reference internal" href="#mimetypes.common_types" title="mimetypes.common_types"><code class="xref py py-data docutils literal notranslate"><span class="pre">common_types</span></code></a> and <a class="reference internal" href="#mimetypes.types_map" title="mimetypes.types_map"><code class="xref py py-data docutils literal notranslate"><span class="pre">types_map</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.guess_extension">
<span class="sig-name descname"><span class="pre">guess_extension</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.guess_extension" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#mimetypes.guess_extension" title="mimetypes.guess_extension"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_extension()</span></code></a> function, using the tables stored as part
of the object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.guess_type">
<span class="sig-name descname"><span class="pre">guess_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.guess_type" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#mimetypes.guess_type" title="mimetypes.guess_type"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_type()</span></code></a> function, using the tables stored as part of
the object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.guess_all_extensions">
<span class="sig-name descname"><span class="pre">guess_all_extensions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.guess_all_extensions" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#mimetypes.guess_all_extensions" title="mimetypes.guess_all_extensions"><code class="xref py py-func docutils literal notranslate"><span class="pre">guess_all_extensions()</span></code></a> function, using the tables stored
as part of the object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.read">
<span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.read" title="Link to this definition">¶</a></dt>
<dd><p>Load MIME information from a file named <em>filename</em>.  This uses <a class="reference internal" href="#mimetypes.MimeTypes.readfp" title="mimetypes.MimeTypes.readfp"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readfp()</span></code></a> to
parse the file.</p>
<p>If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, information will be added to list of standard types,
else to the list of non-standard types.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.readfp">
<span class="sig-name descname"><span class="pre">readfp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.readfp" title="Link to this definition">¶</a></dt>
<dd><p>Load MIME type information from an open file <em>fp</em>.  The file must have the format of
the standard <code class="file docutils literal notranslate"><span class="pre">mime.types</span></code> files.</p>
<p>If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, information will be added to the list of standard
types, else to the list of non-standard types.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="mimetypes.MimeTypes.read_windows_registry">
<span class="sig-name descname"><span class="pre">read_windows_registry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mimetypes.MimeTypes.read_windows_registry" title="Link to this definition">¶</a></dt>
<dd><p>Load MIME type information from the Windows registry.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<p>If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, information will be added to the list of standard
types, else to the list of non-standard types.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a><ul>
<li><a class="reference internal" href="#mimetypes-objects">MimeTypes Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="mailbox.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="base64.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">base64</span></code> — Base16, Base32, Base64, Base85 Data Encodings</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mimetypes.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="base64.html" title="base64 — Base16, Base32, Base64, Base85 Data Encodings"
             >next</a> |</li>
        <li class="right" >
          <a href="mailbox.html" title="mailbox — Manipulate mailboxes in various formats"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code> — Map filenames to MIME types</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>