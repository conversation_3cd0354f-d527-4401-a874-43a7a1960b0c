<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="importlib — The implementation of import" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/importlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/importlib/__init__.py Introduction: The purpose of the importlib package is three-fold. One is to provide the implementation of the import statement (and thus, by extension, the__i..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/importlib/__init__.py Introduction: The purpose of the importlib package is three-fold. One is to provide the implementation of the import statement (and thus, by extension, the__i..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>importlib — The implementation of import &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="importlib.resources – Package resource reading, opening and access" href="importlib.resources.html" />
    <link rel="prev" title="runpy — Locating and executing Python modules" href="runpy.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/importlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#module-importlib.abc"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.abc</span></code> – Abstract base classes related to import</a></li>
<li><a class="reference internal" href="#module-importlib.machinery"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.machinery</span></code> – Importers and path hooks</a></li>
<li><a class="reference internal" href="#module-importlib.util"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.util</span></code> – Utility code for importers</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#importing-programmatically">Importing programmatically</a></li>
<li><a class="reference internal" href="#checking-if-a-module-can-be-imported">Checking if a module can be imported</a></li>
<li><a class="reference internal" href="#importing-a-source-file-directly">Importing a source file directly</a></li>
<li><a class="reference internal" href="#implementing-lazy-imports">Implementing lazy imports</a></li>
<li><a class="reference internal" href="#setting-up-an-importer">Setting up an importer</a></li>
<li><a class="reference internal" href="#approximating-importlib-import-module">Approximating <code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="runpy.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">runpy</span></code> — Locating and executing Python modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.resources.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.resources.html" title="importlib.resources – Package resource reading, opening and access"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="runpy.html" title="runpy — Locating and executing Python modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-importlib">
<span id="importlib-the-implementation-of-import"></span><h1><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code><a class="headerlink" href="#module-importlib" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/__init__.py">Lib/importlib/__init__.py</a></p>
<hr class="docutils" />
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Link to this heading">¶</a></h2>
<p>The purpose of the <a class="reference internal" href="#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> package is three-fold.</p>
<p>One is to provide the
implementation of the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement (and thus, by extension, the
<a class="reference internal" href="functions.html#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> function) in Python source code. This provides an
implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code> which is portable to any Python
interpreter. This also provides an implementation which is easier to
comprehend than one implemented in a programming language other than Python.</p>
<p>Two, the components to implement <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> are exposed in this
package, making it easier for users to create their own custom objects (known
generically as an <a class="reference internal" href="../glossary.html#term-importer"><span class="xref std std-term">importer</span></a>) to participate in the import process.</p>
<p>Three, the package contains modules exposing additional functionality for
managing aspects of Python packages:</p>
<ul class="simple">
<li><p><a class="reference internal" href="importlib.metadata.html#module-importlib.metadata" title="importlib.metadata: Accessing package metadata"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code></a> presents access to metadata from third-party
distributions.</p></li>
<li><p><a class="reference internal" href="importlib.resources.html#module-importlib.resources" title="importlib.resources: Package resource reading, opening, and access"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code></a> provides routines for accessing non-code
“resources” from Python packages.</p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="../reference/simple_stmts.html#import"><span class="std std-ref">The import statement</span></a></dt><dd><p>The language reference for the <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement.</p>
</dd>
<dt><a class="reference external" href="https://www.python.org/doc/essays/packages/">Packages specification</a></dt><dd><p>Original specification of packages. Some semantics have changed since
the writing of this document (e.g. redirecting based on <code class="docutils literal notranslate"><span class="pre">None</span></code>
in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>).</p>
</dd>
<dt>The <a class="reference internal" href="#importlib.__import__" title="importlib.__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> function</dt><dd><p>The <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement is syntactic sugar for this function.</p>
</dd>
<dt><a class="reference internal" href="sys_path_init.html#sys-path-init"><span class="std std-ref">The initialization of the sys.path module search path</span></a></dt><dd><p>The initialization of <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
</dd>
<dt><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0235/"><strong>PEP 235</strong></a></dt><dd><p>Import on Case-Insensitive Platforms</p>
</dd>
<dt><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0263/"><strong>PEP 263</strong></a></dt><dd><p>Defining Python Source Code Encodings</p>
</dd>
<dt><span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a></dt><dd><p>New Import Hooks</p>
</dd>
<dt><span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0328/"><strong>PEP 328</strong></a></dt><dd><p>Imports: Multi-Line and Absolute/Relative</p>
</dd>
<dt><span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0366/"><strong>PEP 366</strong></a></dt><dd><p>Main module explicit relative imports</p>
</dd>
<dt><span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0420/"><strong>PEP 420</strong></a></dt><dd><p>Implicit namespace packages</p>
</dd>
<dt><span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0451/"><strong>PEP 451</strong></a></dt><dd><p>A ModuleSpec Type for the Import System</p>
</dd>
<dt><span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0488/"><strong>PEP 488</strong></a></dt><dd><p>Elimination of PYO files</p>
</dd>
<dt><span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a></dt><dd><p>Multi-phase extension module initialization</p>
</dd>
<dt><span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0552/"><strong>PEP 552</strong></a></dt><dd><p>Deterministic pycs</p>
</dd>
<dt><span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-3120/"><strong>PEP 3120</strong></a></dt><dd><p>Using UTF-8 as the Default Source Encoding</p>
</dd>
<dt><span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a></dt><dd><p>PYC Repository Directories</p>
</dd>
</dl>
</div>
</section>
<section id="functions">
<h2>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="importlib.__import__">
<span class="sig-prename descclassname"><span class="pre">importlib.</span></span><span class="sig-name descname"><span class="pre">__import__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fromlist</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.__import__" title="Link to this definition">¶</a></dt>
<dd><p>An implementation of the built-in <a class="reference internal" href="functions.html#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a> function.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Programmatic importing of modules should use <a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">import_module()</span></code></a>
instead of this function.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.import_module">
<span class="sig-prename descclassname"><span class="pre">importlib.</span></span><span class="sig-name descname"><span class="pre">import_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">package</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.import_module" title="Link to this definition">¶</a></dt>
<dd><p>Import a module. The <em>name</em> argument specifies what module to
import in absolute or relative terms
(e.g. either <code class="docutils literal notranslate"><span class="pre">pkg.mod</span></code> or <code class="docutils literal notranslate"><span class="pre">..mod</span></code>). If the name is
specified in relative terms, then the <em>package</em> argument must be set to
the name of the package which is to act as the anchor for resolving the
package name (e.g. <code class="docutils literal notranslate"><span class="pre">import_module('..mod',</span> <span class="pre">'pkg.subpkg')</span></code> will import
<code class="docutils literal notranslate"><span class="pre">pkg.mod</span></code>).</p>
<p>The <a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">import_module()</span></code></a> function acts as a simplifying wrapper around
<a class="reference internal" href="#importlib.__import__" title="importlib.__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.__import__()</span></code></a>. This means all semantics of the function are
derived from <a class="reference internal" href="#importlib.__import__" title="importlib.__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.__import__()</span></code></a>. The most important difference
between these two functions is that <a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">import_module()</span></code></a> returns the
specified package or module (e.g. <code class="docutils literal notranslate"><span class="pre">pkg.mod</span></code>), while <a class="reference internal" href="functions.html#import__" title="__import__"><code class="xref py py-func docutils literal notranslate"><span class="pre">__import__()</span></code></a>
returns the top-level package or module (e.g. <code class="docutils literal notranslate"><span class="pre">pkg</span></code>).</p>
<p>If you are dynamically importing a module that was created since the
interpreter began execution (e.g., created a Python source file), you may
need to call <a class="reference internal" href="#importlib.invalidate_caches" title="importlib.invalidate_caches"><code class="xref py py-func docutils literal notranslate"><span class="pre">invalidate_caches()</span></code></a> in order for the new module to be
noticed by the import system.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Parent packages are automatically imported.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.invalidate_caches">
<span class="sig-prename descclassname"><span class="pre">importlib.</span></span><span class="sig-name descname"><span class="pre">invalidate_caches</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.invalidate_caches" title="Link to this definition">¶</a></dt>
<dd><p>Invalidate the internal caches of finders stored at
<a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a>. If a finder implements <code class="docutils literal notranslate"><span class="pre">invalidate_caches()</span></code> then it
will be called to perform the invalidation.  This function should be called
if any modules are created/installed while your program is running to
guarantee all finders will notice the new module’s existence.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Namespace packages created/installed in a different <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>
location after the same namespace was already imported are noticed.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.reload">
<span class="sig-prename descclassname"><span class="pre">importlib.</span></span><span class="sig-name descname"><span class="pre">reload</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.reload" title="Link to this definition">¶</a></dt>
<dd><p>Reload a previously imported <em>module</em>.  The argument must be a module object,
so it must have been successfully imported before.  This is useful if you
have edited the module source file using an external editor and want to try
out the new version without leaving the Python interpreter.  The return value
is the module object (which can be different if re-importing causes a
different object to be placed in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>).</p>
<p>When <a class="reference internal" href="#importlib.reload" title="importlib.reload"><code class="xref py py-func docutils literal notranslate"><span class="pre">reload()</span></code></a> is executed:</p>
<ul class="simple">
<li><p>Python module’s code is recompiled and the module-level code re-executed,
defining a new set of objects which are bound to names in the module’s
dictionary by reusing the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> which originally loaded the
module.  The <code class="docutils literal notranslate"><span class="pre">init</span></code> function of extension modules is not called a second
time.</p></li>
<li><p>As with all other objects in Python the old objects are only reclaimed
after their reference counts drop to zero.</p></li>
<li><p>The names in the module namespace are updated to point to any new or
changed objects.</p></li>
<li><p>Other references to the old objects (such as names external to the module) are
not rebound to refer to the new objects and must be updated in each namespace
where they occur if that is desired.</p></li>
</ul>
<p>There are a number of other caveats:</p>
<p>When a module is reloaded, its dictionary (containing the module’s global
variables) is retained.  Redefinitions of names will override the old
definitions, so this is generally not a problem.  If the new version of a
module does not define a name that was defined by the old version, the old
definition remains.  This feature can be used to the module’s advantage if it
maintains a global table or cache of objects — with a <a class="reference internal" href="../reference/compound_stmts.html#try"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">try</span></code></a>
statement it can test for the table’s presence and skip its initialization if
desired:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="n">cache</span>
<span class="k">except</span> <span class="ne">NameError</span><span class="p">:</span>
    <span class="n">cache</span> <span class="o">=</span> <span class="p">{}</span>
</pre></div>
</div>
<p>It is generally not very useful to reload built-in or dynamically loaded
modules.  Reloading <a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a>, <a class="reference internal" href="__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a>, <a class="reference internal" href="builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a> and other
key modules is not recommended.  In many cases extension modules are not
designed to be initialized more than once, and may fail in arbitrary ways
when reloaded.</p>
<p>If a module imports objects from another module using <a class="reference internal" href="../reference/simple_stmts.html#from"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">from</span></code></a> …
<a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> …, calling <a class="reference internal" href="#importlib.reload" title="importlib.reload"><code class="xref py py-func docutils literal notranslate"><span class="pre">reload()</span></code></a> for the other module does not
redefine the objects imported from it — one way around this is to
re-execute the <code class="xref std std-keyword docutils literal notranslate"><span class="pre">from</span></code> statement, another is to use <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code>
and qualified names (<em>module.name</em>) instead.</p>
<p>If a module instantiates instances of a class, reloading the module that
defines the class does not affect the method definitions of the instances —
they continue to use the old class definition.  The same is true for derived
classes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="exceptions.html#ModuleNotFoundError" title="ModuleNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ModuleNotFoundError</span></code></a> is raised when the module being reloaded lacks
a <a class="reference internal" href="#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleSpec</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="module-importlib.abc">
<span id="importlib-abc-abstract-base-classes-related-to-import"></span><h2><a class="reference internal" href="#module-importlib.abc" title="importlib.abc: Abstract base classes related to import"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.abc</span></code></a> – Abstract base classes related to import<a class="headerlink" href="#module-importlib.abc" title="Link to this heading">¶</a></h2>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/abc.py">Lib/importlib/abc.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-importlib.abc" title="importlib.abc: Abstract base classes related to import"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.abc</span></code></a> module contains all of the core abstract base classes
used by <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a>. Some subclasses of the core abstract base classes
are also provided to help in implementing the core ABCs.</p>
<p>ABC hierarchy:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">object</span>
 <span class="o">+--</span> <span class="n">MetaPathFinder</span>
 <span class="o">+--</span> <span class="n">PathEntryFinder</span>
 <span class="o">+--</span> <span class="n">Loader</span>
      <span class="o">+--</span> <span class="n">ResourceLoader</span> <span class="o">--------+</span>
      <span class="o">+--</span> <span class="n">InspectLoader</span>          <span class="o">|</span>
           <span class="o">+--</span> <span class="n">ExecutionLoader</span> <span class="o">--+</span>
                                 <span class="o">+--</span> <span class="n">FileLoader</span>
                                 <span class="o">+--</span> <span class="n">SourceLoader</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.MetaPathFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">MetaPathFinder</span></span><a class="headerlink" href="#importlib.abc.MetaPathFinder" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class representing a <a class="reference internal" href="../glossary.html#term-meta-path-finder"><span class="xref std std-term">meta path finder</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>No longer a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Finder</span></code>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.MetaPathFinder.find_spec">
<span class="sig-name descname"><span class="pre">find_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.MetaPathFinder.find_spec" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method for finding a <a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">spec</span></a> for
the specified module.  If this is a top-level import, <em>path</em> will
be <code class="docutils literal notranslate"><span class="pre">None</span></code>.  Otherwise, this is a search for a subpackage or
module and <em>path</em> will be the value of <a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a> from the
parent package. If a spec cannot be found, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.
When passed in, <code class="docutils literal notranslate"><span class="pre">target</span></code> is a module object that the finder may
use to make a more educated guess about what spec to return.
<a class="reference internal" href="#importlib.util.spec_from_loader" title="importlib.util.spec_from_loader"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.spec_from_loader()</span></code></a> may be useful for implementing
concrete <code class="docutils literal notranslate"><span class="pre">MetaPathFinders</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.MetaPathFinder.invalidate_caches">
<span class="sig-name descname"><span class="pre">invalidate_caches</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.MetaPathFinder.invalidate_caches" title="Link to this definition">¶</a></dt>
<dd><p>An optional method which, when called, should invalidate any internal
cache used by the finder. Used by <a class="reference internal" href="#importlib.invalidate_caches" title="importlib.invalidate_caches"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.invalidate_caches()</span></code></a>
when invalidating the caches of all finders on <a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code> when called instead of <a class="reference internal" href="constants.html#NotImplemented" title="NotImplemented"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotImplemented</span></code></a>.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.PathEntryFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">PathEntryFinder</span></span><a class="headerlink" href="#importlib.abc.PathEntryFinder" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class representing a <a class="reference internal" href="../glossary.html#term-path-entry-finder"><span class="xref std std-term">path entry finder</span></a>.  Though
it bears some similarities to <a class="reference internal" href="#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">MetaPathFinder</span></code></a>, <code class="docutils literal notranslate"><span class="pre">PathEntryFinder</span></code>
is meant for use only within the path-based import subsystem provided
by <a class="reference internal" href="#importlib.machinery.PathFinder" title="importlib.machinery.PathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.PathFinder</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>No longer a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Finder</span></code>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.PathEntryFinder.find_spec">
<span class="sig-name descname"><span class="pre">find_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.PathEntryFinder.find_spec" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method for finding a <a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">spec</span></a> for
the specified module.  The finder will search for the module only
within the <a class="reference internal" href="../glossary.html#term-path-entry"><span class="xref std std-term">path entry</span></a> to which it is assigned.  If a spec
cannot be found, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.  When passed in, <code class="docutils literal notranslate"><span class="pre">target</span></code>
is a module object that the finder may use to make a more educated
guess about what spec to return. <a class="reference internal" href="#importlib.util.spec_from_loader" title="importlib.util.spec_from_loader"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.spec_from_loader()</span></code></a>
may be useful for implementing concrete <code class="docutils literal notranslate"><span class="pre">PathEntryFinders</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.PathEntryFinder.invalidate_caches">
<span class="sig-name descname"><span class="pre">invalidate_caches</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.PathEntryFinder.invalidate_caches" title="Link to this definition">¶</a></dt>
<dd><p>An optional method which, when called, should invalidate any internal
cache used by the finder. Used by
<a class="reference internal" href="#importlib.machinery.PathFinder.invalidate_caches" title="importlib.machinery.PathFinder.invalidate_caches"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.machinery.PathFinder.invalidate_caches()</span></code></a>
when invalidating the caches of all cached finders.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.Loader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">Loader</span></span><a class="headerlink" href="#importlib.abc.Loader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for a <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a>.
See <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> for the exact definition for a loader.</p>
<p>Loaders that wish to support resource reading should implement a
<code class="xref py py-meth docutils literal notranslate"><span class="pre">get_resource_reader()</span></code> method as specified by
<a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.ResourceReader</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Introduced the optional <code class="xref py py-meth docutils literal notranslate"><span class="pre">get_resource_reader()</span></code> method.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Loader.create_module">
<span class="sig-name descname"><span class="pre">create_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">spec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Loader.create_module" title="Link to this definition">¶</a></dt>
<dd><p>A method that returns the module object to use when
importing a module.  This method may return <code class="docutils literal notranslate"><span class="pre">None</span></code>,
indicating that default module creation semantics should take place.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>This method is no longer optional when
<a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> is defined.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Loader.exec_module">
<span class="sig-name descname"><span class="pre">exec_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Loader.exec_module" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method that executes the module in its own namespace
when a module is imported or reloaded.  The module should already
be initialized when <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> is called.  When this method exists,
<a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code></a> must be defined.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code></a> must also be defined.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Loader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Loader.load_module" title="Link to this definition">¶</a></dt>
<dd><p>A legacy method for loading a module.  If the module cannot be
loaded, <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is raised, otherwise the loaded module is
returned.</p>
<p>If the requested module already exists in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>, that
module should be used and reloaded.
Otherwise the loader should create a new module and insert it into
<a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a> before any loading begins, to prevent recursion
from the import.  If the loader inserted a module and the load fails, it
must be removed by the loader from <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>; modules already
in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a> before the loader began execution should be left
alone.</p>
<p>The loader should set several attributes on the module
(note that some of these attributes can change when a module is
reloaded):</p>
<ul class="simple">
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a></dt><dd><p>The module’s fully qualified name.
It is <code class="docutils literal notranslate"><span class="pre">'__main__'</span></code> for an executed module.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a></dt><dd><p>The location the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> used to load the module.
For example, for modules loaded from a .py file this is the filename.
It is not set on all modules (e.g. built-in modules).</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#cached__" title="__cached__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cached__</span></code></a></dt><dd><p>The filename of a compiled version of the module’s code.
It is not set on all modules (e.g. built-in modules).</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a></dt><dd><p>The list of locations where the package’s submodules will be found.
Most of the time this is a single directory.
The import system passes this attribute to <code class="docutils literal notranslate"><span class="pre">__import__()</span></code> and to finders
in the same way as <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> but just for the package.
It is not set on non-package modules so it can be used
as an indicator that the module is a package.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#package__" title="__package__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__package__</span></code></a></dt><dd><p>The fully qualified name of the package the module is in (or the
empty string for a top-level module).
If the module is a package then this is the same as <a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a>.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference internal" href="../reference/import.html#loader__" title="__loader__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__loader__</span></code></a></dt><dd><p>The <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> used to load the module.</p>
</dd>
</dl>
</li>
</ul>
<p>When <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> is available then backwards-compatible
functionality is provided.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raise <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> when called instead of
<a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.  Functionality provided when
<a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> is available.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.4: </span>The recommended API for loading a module is <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a>
(and <a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code></a>).  Loaders should implement it instead of
<a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load_module()</span></code></a>.  The import machinery takes care of all the
other responsibilities of <a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load_module()</span></code></a> when
<a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> is implemented.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.ResourceLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">ResourceLoader</span></span><a class="headerlink" href="#importlib.abc.ResourceLoader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for a <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> which implements the optional
<span class="target" id="index-13"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> protocol for loading arbitrary resources from the storage
back-end.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>This ABC is deprecated in favour of supporting resource loading
through <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.ResourceReader</span></code></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ResourceLoader.get_data">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ResourceLoader.get_data" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method to return the bytes for the data located at <em>path</em>.
Loaders that have a file-like storage back-end
that allows storing arbitrary data
can implement this abstract method to give direct access
to the data stored. <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is to be raised if the <em>path</em> cannot
be found. The <em>path</em> is expected to be constructed using a module’s
<a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a> attribute or an item from a package’s <a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">InspectLoader</span></span><a class="headerlink" href="#importlib.abc.InspectLoader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for a <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> which implements the optional
<span class="target" id="index-14"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> protocol for loaders that inspect modules.</p>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.get_code">
<span class="sig-name descname"><span class="pre">get_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.get_code" title="Link to this definition">¶</a></dt>
<dd><p>Return the code object for a module, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the module does not
have a code object (as would be the case, for example, for a built-in
module).  Raise an <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> if loader cannot find the
requested module.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>While the method has a default implementation, it is suggested that
it be overridden if possible for performance.</p>
</div>
<div class="versionchanged" id="index-15">
<p><span class="versionmodified changed">Changed in version 3.4: </span>No longer abstract and a concrete implementation is provided.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.get_source">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.get_source" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method to return the source of a module. It is returned as
a text string using <a class="reference internal" href="../glossary.html#term-universal-newlines"><span class="xref std std-term">universal newlines</span></a>, translating all
recognized line separators into <code class="docutils literal notranslate"><span class="pre">'\n'</span></code> characters.  Returns <code class="docutils literal notranslate"><span class="pre">None</span></code>
if no source is available (e.g. a built-in module). Raises
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> if the loader cannot find the module specified.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raises <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.is_package">
<span class="sig-name descname"><span class="pre">is_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.is_package" title="Link to this definition">¶</a></dt>
<dd><p>An optional method to return a true value if the module is a package, a
false value otherwise. <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is raised if the
<a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> cannot find the module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raises <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.source_to_code">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">source_to_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'&lt;string&gt;'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.source_to_code" title="Link to this definition">¶</a></dt>
<dd><p>Create a code object from Python source.</p>
<p>The <em>data</em> argument can be whatever the <a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a> function
supports (i.e. string or bytes). The <em>path</em> argument should be
the “path” to where the source code originated from, which can be an
abstract concept (e.g. location in a zip file).</p>
<p>With the subsequent code object one can execute it in a module by
running <code class="docutils literal notranslate"><span class="pre">exec(code,</span> <span class="pre">module.__dict__)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Made the method static.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.exec_module">
<span class="sig-name descname"><span class="pre">exec_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.exec_module" title="Link to this definition">¶</a></dt>
<dd><p>Implementation of <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.exec_module()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.InspectLoader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.InspectLoader.load_module" title="Link to this definition">¶</a></dt>
<dd><p>Implementation of <a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.load_module()</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.4: </span>use <a class="reference internal" href="#importlib.abc.InspectLoader.exec_module" title="importlib.abc.InspectLoader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> instead.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.ExecutionLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">ExecutionLoader</span></span><a class="headerlink" href="#importlib.abc.ExecutionLoader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class which inherits from <a class="reference internal" href="#importlib.abc.InspectLoader" title="importlib.abc.InspectLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">InspectLoader</span></code></a> that,
when implemented, helps a module to be executed as a script. The ABC
represents an optional <span class="target" id="index-16"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> protocol.</p>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ExecutionLoader.get_filename">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_filename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ExecutionLoader.get_filename" title="Link to this definition">¶</a></dt>
<dd><p>An abstract method that is to return the value of <a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a> for
the specified module. If no path is available, <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is
raised.</p>
<p>If source code is available, then the method should return the path to
the source file, regardless of whether a bytecode was used to load the
module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raises <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.FileLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">FileLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.FileLoader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class which inherits from <a class="reference internal" href="#importlib.abc.ResourceLoader" title="importlib.abc.ResourceLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">ResourceLoader</span></code></a> and
<a class="reference internal" href="#importlib.abc.ExecutionLoader" title="importlib.abc.ExecutionLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionLoader</span></code></a>, providing concrete implementations of
<a class="reference internal" href="#importlib.abc.ResourceLoader.get_data" title="importlib.abc.ResourceLoader.get_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ResourceLoader.get_data()</span></code></a> and <a class="reference internal" href="#importlib.abc.ExecutionLoader.get_filename" title="importlib.abc.ExecutionLoader.get_filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ExecutionLoader.get_filename()</span></code></a>.</p>
<p>The <em>fullname</em> argument is a fully resolved name of the module the loader is
to handle. The <em>path</em> argument is the path to the file for the module.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.abc.FileLoader.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.abc.FileLoader.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module the loader can handle.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.abc.FileLoader.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#importlib.abc.FileLoader.path" title="Link to this definition">¶</a></dt>
<dd><p>Path to the file of the module.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.FileLoader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.FileLoader.load_module" title="Link to this definition">¶</a></dt>
<dd><p>Calls super’s <code class="docutils literal notranslate"><span class="pre">load_module()</span></code>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.4: </span>Use <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.exec_module()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.FileLoader.get_filename">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_filename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.FileLoader.get_filename" title="Link to this definition">¶</a></dt>
<dd><p>Returns <a class="reference internal" href="#importlib.abc.FileLoader.path" title="importlib.abc.FileLoader.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.FileLoader.get_data">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">get_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.FileLoader.get_data" title="Link to this definition">¶</a></dt>
<dd><p>Reads <em>path</em> as a binary file and returns the bytes from it.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">SourceLoader</span></span><a class="headerlink" href="#importlib.abc.SourceLoader" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for implementing source (and optionally bytecode)
file loading. The class inherits from both <a class="reference internal" href="#importlib.abc.ResourceLoader" title="importlib.abc.ResourceLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">ResourceLoader</span></code></a> and
<a class="reference internal" href="#importlib.abc.ExecutionLoader" title="importlib.abc.ExecutionLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExecutionLoader</span></code></a>, requiring the implementation of:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#importlib.abc.ResourceLoader.get_data" title="importlib.abc.ResourceLoader.get_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ResourceLoader.get_data()</span></code></a></p></li>
<li><dl class="simple">
<dt><a class="reference internal" href="#importlib.abc.ExecutionLoader.get_filename" title="importlib.abc.ExecutionLoader.get_filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ExecutionLoader.get_filename()</span></code></a></dt><dd><p>Should only return the path to the source file; sourceless
loading is not supported.</p>
</dd>
</dl>
</li>
</ul>
<p>The abstract methods defined by this class are to add optional bytecode
file support. Not implementing these optional methods (or causing them to
raise <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>) causes the loader to
only work with source code. Implementing the methods allows the loader to
work with source <em>and</em> bytecode files; it does not allow for <em>sourceless</em>
loading where only bytecode is provided.  Bytecode files are an
optimization to speed up loading by removing the parsing step of Python’s
compiler, and so no bytecode-specific API is exposed.</p>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.path_stats">
<span class="sig-name descname"><span class="pre">path_stats</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.path_stats" title="Link to this definition">¶</a></dt>
<dd><p>Optional abstract method which returns a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> containing
metadata about the specified path.  Supported dictionary keys are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">'mtime'</span></code> (mandatory): an integer or floating-point number
representing the modification time of the source code;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'size'</span></code> (optional): the size in bytes of the source code.</p></li>
</ul>
<p>Any other keys in the dictionary are ignored, to allow for future
extensions. If the path cannot be handled, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.path_mtime">
<span class="sig-name descname"><span class="pre">path_mtime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.path_mtime" title="Link to this definition">¶</a></dt>
<dd><p>Optional abstract method which returns the modification time for the
specified path.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.3: </span>This method is deprecated in favour of <a class="reference internal" href="#importlib.abc.SourceLoader.path_stats" title="importlib.abc.SourceLoader.path_stats"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_stats()</span></code></a>.  You don’t
have to implement it, but it is still available for compatibility
purposes. Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if the path cannot be handled.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> instead of <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.set_data">
<span class="sig-name descname"><span class="pre">set_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.set_data" title="Link to this definition">¶</a></dt>
<dd><p>Optional abstract method which writes the specified bytes to a file
path. Any intermediate directories which do not exist are to be created
automatically.</p>
<p>When writing to the path fails because the path is read-only
(<a class="reference internal" href="errno.html#errno.EACCES" title="errno.EACCES"><code class="xref py py-const docutils literal notranslate"><span class="pre">errno.EACCES</span></code></a>/<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a>), do not propagate the
exception.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>No longer raises <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> when called.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.get_code">
<span class="sig-name descname"><span class="pre">get_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.get_code" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.InspectLoader.get_code" title="importlib.abc.InspectLoader.get_code"><code class="xref py py-meth docutils literal notranslate"><span class="pre">InspectLoader.get_code()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.exec_module">
<span class="sig-name descname"><span class="pre">exec_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.exec_module" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.exec_module()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.load_module" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.load_module()</span></code></a>.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.4: </span>Use <a class="reference internal" href="#importlib.abc.SourceLoader.exec_module" title="importlib.abc.SourceLoader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.get_source">
<span class="sig-name descname"><span class="pre">get_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.get_source" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.InspectLoader.get_source" title="importlib.abc.InspectLoader.get_source"><code class="xref py py-meth docutils literal notranslate"><span class="pre">InspectLoader.get_source()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.SourceLoader.is_package">
<span class="sig-name descname"><span class="pre">is_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.SourceLoader.is_package" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.InspectLoader.is_package" title="importlib.abc.InspectLoader.is_package"><code class="xref py py-meth docutils literal notranslate"><span class="pre">InspectLoader.is_package()</span></code></a>. A module
is determined to be a package if its file path (as provided by
<a class="reference internal" href="#importlib.abc.ExecutionLoader.get_filename" title="importlib.abc.ExecutionLoader.get_filename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ExecutionLoader.get_filename()</span></code></a>) is a file named
<code class="docutils literal notranslate"><span class="pre">__init__</span></code> when the file extension is removed <strong>and</strong> the module name
itself does not end in <code class="docutils literal notranslate"><span class="pre">__init__</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.ResourceReader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">ResourceReader</span></span><a class="headerlink" href="#importlib.abc.ResourceReader" title="Link to this definition">¶</a></dt>
<dd><p><em>Superseded by TraversableResources</em></p>
<p>An <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a> to provide the ability to read
<em>resources</em>.</p>
<p>From the perspective of this ABC, a <em>resource</em> is a binary
artifact that is shipped within a package. Typically this is
something like a data file that lives next to the <code class="docutils literal notranslate"><span class="pre">__init__.py</span></code>
file of the package. The purpose of this class is to help abstract
out the accessing of such data files so that it does not matter if
the package and its data file(s) are stored in a e.g. zip file
versus on the file system.</p>
<p>For any of methods of this class, a <em>resource</em> argument is
expected to be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> which represents
conceptually just a file name. This means that no subdirectory
paths should be included in the <em>resource</em> argument. This is
because the location of the package the reader is for, acts as the
“directory”. Hence the metaphor for directories and file
names is packages and resources, respectively. This is also why
instances of this class are expected to directly correlate to
a specific package (instead of potentially representing multiple
packages or a module).</p>
<p>Loaders that wish to support resource reading are expected to
provide a method called <code class="docutils literal notranslate"><span class="pre">get_resource_reader(fullname)</span></code> which
returns an object implementing this ABC’s interface. If the module
specified by fullname is not a package, this method should return
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>. An object compatible with this ABC should only be
returned when the specified module is a package.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.TraversableResources" title="importlib.resources.abc.TraversableResources"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.TraversableResources</span></code></a> instead.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ResourceReader.open_resource">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">open_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ResourceReader.open_resource" title="Link to this definition">¶</a></dt>
<dd><p>Returns an opened, <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a> for binary reading
of the <em>resource</em>.</p>
<p>If the resource cannot be found, <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> is
raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ResourceReader.resource_path">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">resource_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ResourceReader.resource_path" title="Link to this definition">¶</a></dt>
<dd><p>Returns the file system path to the <em>resource</em>.</p>
<p>If the resource does not concretely exist on the file system,
raise <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ResourceReader.is_resource">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ResourceReader.is_resource" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the named <em>name</em> is considered a resource.
<a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> is raised if <em>name</em> does not exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.ResourceReader.contents">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">contents</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.ResourceReader.contents" title="Link to this definition">¶</a></dt>
<dd><p>Returns an <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a> of strings over the contents of
the package. Do note that it is not required that all names
returned by the iterator be actual resources, e.g. it is
acceptable to return names for which <a class="reference internal" href="#importlib.abc.ResourceReader.is_resource" title="importlib.abc.ResourceReader.is_resource"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_resource()</span></code></a> would
be false.</p>
<p>Allowing non-resource names to be returned is to allow for
situations where how a package and its resources are stored
are known a priori and the non-resource names would be useful.
For instance, returning subdirectory names is allowed so that
when it is known that the package and resources are stored on
the file system then those subdirectory names can be used
directly.</p>
<p>The abstract method returns an iterable of no items.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.Traversable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">Traversable</span></span><a class="headerlink" href="#importlib.abc.Traversable" title="Link to this definition">¶</a></dt>
<dd><p>An object with a subset of <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> methods suitable for
traversing directories and opening files.</p>
<p>For a representation of the object on the file-system, use
<a class="reference internal" href="importlib.resources.html#importlib.resources.as_file" title="importlib.resources.as_file"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.resources.as_file()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.Traversable</span></code></a> instead.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.abc.Traversable.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.abc.Traversable.name" title="Link to this definition">¶</a></dt>
<dd><p>Abstract. The base name of this object without any parent references.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.iterdir">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">iterdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.iterdir" title="Link to this definition">¶</a></dt>
<dd><p>Yield <code class="docutils literal notranslate"><span class="pre">Traversable</span></code> objects in <code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.is_dir">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.is_dir" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <code class="docutils literal notranslate"><span class="pre">self</span></code> is a directory.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.is_file">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_file</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.is_file" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <code class="docutils literal notranslate"><span class="pre">self</span></code> is a file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.joinpath">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">joinpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">child</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.joinpath" title="Link to this definition">¶</a></dt>
<dd><p>Return Traversable child in <code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.__truediv__">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">__truediv__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">child</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.__truediv__" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">Traversable</span></code> child in <code class="docutils literal notranslate"><span class="pre">self</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.open">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.open" title="Link to this definition">¶</a></dt>
<dd><p><em>mode</em> may be ‘r’ or ‘rb’ to open as text or binary. Return a handle
suitable for reading (same as <a class="reference internal" href="pathlib.html#pathlib.Path.open" title="pathlib.Path.open"><code class="xref py py-attr docutils literal notranslate"><span class="pre">pathlib.Path.open</span></code></a>).</p>
<p>When opening as text, accepts encoding parameters such as those
accepted by <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-attr docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.read_bytes">
<span class="sig-name descname"><span class="pre">read_bytes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.read_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Read contents of <code class="docutils literal notranslate"><span class="pre">self</span></code> as bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.Traversable.read_text">
<span class="sig-name descname"><span class="pre">read_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.Traversable.read_text" title="Link to this definition">¶</a></dt>
<dd><p>Read contents of <code class="docutils literal notranslate"><span class="pre">self</span></code> as text.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.abc.TraversableResources">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.abc.</span></span><span class="sig-name descname"><span class="pre">TraversableResources</span></span><a class="headerlink" href="#importlib.abc.TraversableResources" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for resource readers capable of serving
the <a class="reference internal" href="importlib.resources.html#importlib.resources.files" title="importlib.resources.files"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.resources.files()</span></code></a> interface. Subclasses
<a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.ResourceReader</span></code></a> and provides
concrete implementations of the <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.ResourceReader</span></code></a>’s
abstract methods. Therefore, any loader supplying
<a class="reference internal" href="#importlib.abc.TraversableResources" title="importlib.abc.TraversableResources"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.TraversableResources</span></code></a> also supplies ResourceReader.</p>
<p>Loaders that wish to support resource reading are expected to
implement this interface.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.TraversableResources" title="importlib.resources.abc.TraversableResources"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.TraversableResources</span></code></a> instead.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.abc.TraversableResources.files">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">files</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.abc.TraversableResources.files" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.Traversable</span></code></a> object for the loaded
package.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-importlib.machinery">
<span id="importlib-machinery-importers-and-path-hooks"></span><h2><a class="reference internal" href="#module-importlib.machinery" title="importlib.machinery: Importers and path hooks"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.machinery</span></code></a> – Importers and path hooks<a class="headerlink" href="#module-importlib.machinery" title="Link to this heading">¶</a></h2>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/machinery.py">Lib/importlib/machinery.py</a></p>
<hr class="docutils" />
<p>This module contains the various objects that help <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a>
find and load modules.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.SOURCE_SUFFIXES">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">SOURCE_SUFFIXES</span></span><a class="headerlink" href="#importlib.machinery.SOURCE_SUFFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of strings representing the recognized file suffixes for source
modules.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.DEBUG_BYTECODE_SUFFIXES">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">DEBUG_BYTECODE_SUFFIXES</span></span><a class="headerlink" href="#importlib.machinery.DEBUG_BYTECODE_SUFFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of strings representing the file suffixes for non-optimized bytecode
modules.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.5: </span>Use <a class="reference internal" href="#importlib.machinery.BYTECODE_SUFFIXES" title="importlib.machinery.BYTECODE_SUFFIXES"><code class="xref py py-attr docutils literal notranslate"><span class="pre">BYTECODE_SUFFIXES</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.OPTIMIZED_BYTECODE_SUFFIXES">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">OPTIMIZED_BYTECODE_SUFFIXES</span></span><a class="headerlink" href="#importlib.machinery.OPTIMIZED_BYTECODE_SUFFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of strings representing the file suffixes for optimized bytecode
modules.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.5: </span>Use <a class="reference internal" href="#importlib.machinery.BYTECODE_SUFFIXES" title="importlib.machinery.BYTECODE_SUFFIXES"><code class="xref py py-attr docutils literal notranslate"><span class="pre">BYTECODE_SUFFIXES</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.BYTECODE_SUFFIXES">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">BYTECODE_SUFFIXES</span></span><a class="headerlink" href="#importlib.machinery.BYTECODE_SUFFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of strings representing the recognized file suffixes for bytecode
modules (including the leading dot).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The value is no longer dependent on <code class="docutils literal notranslate"><span class="pre">__debug__</span></code>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.EXTENSION_SUFFIXES">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">EXTENSION_SUFFIXES</span></span><a class="headerlink" href="#importlib.machinery.EXTENSION_SUFFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of strings representing the recognized file suffixes for
extension modules.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.machinery.all_suffixes">
<span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">all_suffixes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.all_suffixes" title="Link to this definition">¶</a></dt>
<dd><p>Returns a combined list of strings representing all file suffixes for
modules recognized by the standard import machinery. This is a
helper for code which simply needs to know if a filesystem path
potentially refers to a module without needing any details on the kind
of module (for example, <a class="reference internal" href="inspect.html#inspect.getmodulename" title="inspect.getmodulename"><code class="xref py py-func docutils literal notranslate"><span class="pre">inspect.getmodulename()</span></code></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.BuiltinImporter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">BuiltinImporter</span></span><a class="headerlink" href="#importlib.machinery.BuiltinImporter" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="../glossary.html#term-importer"><span class="xref std std-term">importer</span></a> for built-in modules. All known built-in modules are
listed in <a class="reference internal" href="sys.html#sys.builtin_module_names" title="sys.builtin_module_names"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.builtin_module_names</span></code></a>. This class implements the
<a class="reference internal" href="#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a> and
<a class="reference internal" href="#importlib.abc.InspectLoader" title="importlib.abc.InspectLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.InspectLoader</span></code></a> ABCs.</p>
<p>Only class methods are defined by this class to alleviate the need for
instantiation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>As part of <span class="target" id="index-17"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>, the builtin importer now implements
<code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.create_module()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">Loader.exec_module()</span></code></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.FrozenImporter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">FrozenImporter</span></span><a class="headerlink" href="#importlib.machinery.FrozenImporter" title="Link to this definition">¶</a></dt>
<dd><p>An <a class="reference internal" href="../glossary.html#term-importer"><span class="xref std std-term">importer</span></a> for frozen modules. This class implements the
<a class="reference internal" href="#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a> and
<a class="reference internal" href="#importlib.abc.InspectLoader" title="importlib.abc.InspectLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.InspectLoader</span></code></a> ABCs.</p>
<p>Only class methods are defined by this class to alleviate the need for
instantiation.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Gained <code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code>
methods.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.WindowsRegistryFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">WindowsRegistryFinder</span></span><a class="headerlink" href="#importlib.machinery.WindowsRegistryFinder" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">Finder</span></a> for modules declared in the Windows registry.  This class
implements the <a class="reference internal" href="#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a> ABC.</p>
<p>Only class methods are defined by this class to alleviate the need for
instantiation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> configuration instead. Future versions of Python may
not enable this finder by default.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.PathFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">PathFinder</span></span><a class="headerlink" href="#importlib.machinery.PathFinder" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">Finder</span></a> for <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> and package <code class="docutils literal notranslate"><span class="pre">__path__</span></code> attributes.
This class implements the <a class="reference internal" href="#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a> ABC.</p>
<p>Only class methods are defined by this class to alleviate the need for
instantiation.</p>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.PathFinder.find_spec">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">find_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.PathFinder.find_spec" title="Link to this definition">¶</a></dt>
<dd><p>Class method that attempts to find a <a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">spec</span></a>
for the module specified by <em>fullname</em> on <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> or, if
defined, on <em>path</em>. For each path entry that is searched,
<a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> is checked. If a non-false object
is found then it is used as the <a class="reference internal" href="../glossary.html#term-path-entry-finder"><span class="xref std std-term">path entry finder</span></a> to look
for the module being searched for. If no entry is found in
<a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a>, then <a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> is
searched for a finder for the path entry and, if found, is stored
in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> along with being queried about
the module. If no finder is ever found then <code class="docutils literal notranslate"><span class="pre">None</span></code> is both
stored in the cache and returned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>If the current working directory – represented by an empty string –
is no longer valid then <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned but no value is cached
in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.PathFinder.invalidate_caches">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">invalidate_caches</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.PathFinder.invalidate_caches" title="Link to this definition">¶</a></dt>
<dd><p>Calls <a class="reference internal" href="#importlib.abc.PathEntryFinder.invalidate_caches" title="importlib.abc.PathEntryFinder.invalidate_caches"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.PathEntryFinder.invalidate_caches()</span></code></a> on all
finders stored in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> that define the method.
Otherwise entries in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> set to <code class="docutils literal notranslate"><span class="pre">None</span></code> are
deleted.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Entries of <code class="docutils literal notranslate"><span class="pre">None</span></code> in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> are deleted.</p>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Calls objects in <a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> with the current working
directory for <code class="docutils literal notranslate"><span class="pre">''</span></code> (i.e. the empty string).</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.FileFinder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">FileFinder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">loader_details</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.FileFinder" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#importlib.abc.PathEntryFinder" title="importlib.abc.PathEntryFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.PathEntryFinder</span></code></a> which
caches results from the file system.</p>
<p>The <em>path</em> argument is the directory for which the finder is in charge of
searching.</p>
<p>The <em>loader_details</em> argument is a variable number of 2-item tuples each
containing a loader and a sequence of file suffixes the loader recognizes.
The loaders are expected to be callables which accept two arguments of
the module’s name and the path to the file found.</p>
<p>The finder will cache the directory contents as necessary, making stat calls
for each module search to verify the cache is not outdated. Because cache
staleness relies upon the granularity of the operating system’s state
information of the file system, there is a potential race condition of
searching for a module, creating a new file, and then searching for the
module the new file represents. If the operations happen fast enough to fit
within the granularity of stat calls, then the module search will fail. To
prevent this from happening, when you create a module dynamically, make sure
to call <a class="reference internal" href="#importlib.invalidate_caches" title="importlib.invalidate_caches"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.invalidate_caches()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.FileFinder.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#importlib.machinery.FileFinder.path" title="Link to this definition">¶</a></dt>
<dd><p>The path the finder will search in.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.FileFinder.find_spec">
<span class="sig-name descname"><span class="pre">find_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.FileFinder.find_spec" title="Link to this definition">¶</a></dt>
<dd><p>Attempt to find the spec to handle <em>fullname</em> within <a class="reference internal" href="#importlib.machinery.FileFinder.path" title="importlib.machinery.FileFinder.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.FileFinder.invalidate_caches">
<span class="sig-name descname"><span class="pre">invalidate_caches</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.FileFinder.invalidate_caches" title="Link to this definition">¶</a></dt>
<dd><p>Clear out the internal cache.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.FileFinder.path_hook">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">path_hook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">loader_details</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.FileFinder.path_hook" title="Link to this definition">¶</a></dt>
<dd><p>A class method which returns a closure for use on <a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a>.
An instance of <a class="reference internal" href="#importlib.machinery.FileFinder" title="importlib.machinery.FileFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileFinder</span></code></a> is returned by the closure using the
path argument given to the closure directly and <em>loader_details</em>
indirectly.</p>
<p>If the argument to the closure is not an existing directory,
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is raised.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">SourceFileLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourceFileLoader" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#importlib.abc.SourceLoader" title="importlib.abc.SourceLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.SourceLoader</span></code></a> by
subclassing <a class="reference internal" href="#importlib.abc.FileLoader" title="importlib.abc.FileLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.FileLoader</span></code></a> and providing some concrete
implementations of other methods.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module that this loader will handle.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.path" title="Link to this definition">¶</a></dt>
<dd><p>The path to the source file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.is_package">
<span class="sig-name descname"><span class="pre">is_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.is_package" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <a class="reference internal" href="#importlib.machinery.SourceFileLoader.path" title="importlib.machinery.SourceFileLoader.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a> appears to be for a package.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.path_stats">
<span class="sig-name descname"><span class="pre">path_stats</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.path_stats" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.SourceLoader.path_stats" title="importlib.abc.SourceLoader.path_stats"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.SourceLoader.path_stats()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.set_data">
<span class="sig-name descname"><span class="pre">set_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.set_data" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.SourceLoader.set_data" title="importlib.abc.SourceLoader.set_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.SourceLoader.set_data()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourceFileLoader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourceFileLoader.load_module" title="Link to this definition">¶</a></dt>
<dd><p>Concrete implementation of <a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.Loader.load_module()</span></code></a> where
specifying the name of the module to load is optional.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.Loader.exec_module()</span></code></a> instead.</p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">SourcelessFileLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#importlib.abc.FileLoader" title="importlib.abc.FileLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.FileLoader</span></code></a> which can
import bytecode files (i.e. no source code files exist).</p>
<p>Please note that direct use of bytecode files (and thus not source code
files) inhibits your modules from being usable by all Python
implementations or new versions of Python which change the bytecode
format.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module the loader will handle.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.path" title="Link to this definition">¶</a></dt>
<dd><p>The path to the bytecode file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.is_package">
<span class="sig-name descname"><span class="pre">is_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.is_package" title="Link to this definition">¶</a></dt>
<dd><p>Determines if the module is a package based on <a class="reference internal" href="#importlib.machinery.SourcelessFileLoader.path" title="importlib.machinery.SourcelessFileLoader.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.get_code">
<span class="sig-name descname"><span class="pre">get_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.get_code" title="Link to this definition">¶</a></dt>
<dd><p>Returns the code object for <a class="reference internal" href="#importlib.machinery.SourcelessFileLoader.name" title="importlib.machinery.SourcelessFileLoader.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a> created from <a class="reference internal" href="#importlib.machinery.SourcelessFileLoader.path" title="importlib.machinery.SourcelessFileLoader.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.get_source">
<span class="sig-name descname"><span class="pre">get_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.get_source" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code> as bytecode files have no source when this loader is
used.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.SourcelessFileLoader.load_module">
<span class="sig-name descname"><span class="pre">load_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.SourcelessFileLoader.load_module" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>Concrete implementation of <a class="reference internal" href="#importlib.abc.Loader.load_module" title="importlib.abc.Loader.load_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.Loader.load_module()</span></code></a> where
specifying the name of the module to load is optional.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>Use <a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.Loader.exec_module()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">ExtensionFileLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#importlib.abc.ExecutionLoader" title="importlib.abc.ExecutionLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.ExecutionLoader</span></code></a> for
extension modules.</p>
<p>The <em>fullname</em> argument specifies the name of the module the loader is to
support. The <em>path</em> argument is the path to the extension module’s file.</p>
<p>Note that, by default, importing an extension module will fail
in subinterpreters if it doesn’t implement multi-phase init
(see <span class="target" id="index-18"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>), even if it would otherwise import successfully.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Multi-phase init is now required for use in subinterpreters.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.name" title="Link to this definition">¶</a></dt>
<dd><p>Name of the module the loader supports.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.path">
<span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.path" title="Link to this definition">¶</a></dt>
<dd><p>Path to the extension module.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.create_module">
<span class="sig-name descname"><span class="pre">create_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">spec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.create_module" title="Link to this definition">¶</a></dt>
<dd><p>Creates the module object from the given specification in accordance
with <span class="target" id="index-19"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.exec_module">
<span class="sig-name descname"><span class="pre">exec_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.exec_module" title="Link to this definition">¶</a></dt>
<dd><p>Initializes the given module object in accordance with <span class="target" id="index-20"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.is_package">
<span class="sig-name descname"><span class="pre">is_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.is_package" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the file path points to a package’s <code class="docutils literal notranslate"><span class="pre">__init__</span></code>
module based on <a class="reference internal" href="#importlib.machinery.EXTENSION_SUFFIXES" title="importlib.machinery.EXTENSION_SUFFIXES"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EXTENSION_SUFFIXES</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.get_code">
<span class="sig-name descname"><span class="pre">get_code</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.get_code" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code> as extension modules lack a code object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.get_source">
<span class="sig-name descname"><span class="pre">get_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.get_source" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code> as extension modules do not have source code.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.machinery.ExtensionFileLoader.get_filename">
<span class="sig-name descname"><span class="pre">get_filename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ExtensionFileLoader.get_filename" title="Link to this definition">¶</a></dt>
<dd><p>Returns <a class="reference internal" href="#importlib.machinery.ExtensionFileLoader.path" title="importlib.machinery.ExtensionFileLoader.path"><code class="xref py py-attr docutils literal notranslate"><span class="pre">path</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.NamespaceLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">NamespaceLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path_finder</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.NamespaceLoader" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of <a class="reference internal" href="#importlib.abc.InspectLoader" title="importlib.abc.InspectLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.InspectLoader</span></code></a> for
namespace packages.  This is an alias for a private class and is only made
public for introspecting the <code class="docutils literal notranslate"><span class="pre">__loader__</span></code> attribute on namespace
packages:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">importlib.machinery</span> <span class="kn">import</span> <span class="n">NamespaceLoader</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">my_namespace</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">my_namespace</span><span class="o">.</span><span class="n">__loader__</span><span class="p">,</span> <span class="n">NamespaceLoader</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">importlib.abc</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">my_namespace</span><span class="o">.</span><span class="n">__loader__</span><span class="p">,</span> <span class="n">importlib</span><span class="o">.</span><span class="n">abc</span><span class="o">.</span><span class="n">Loader</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.machinery.</span></span><span class="sig-name descname"><span class="pre">ModuleSpec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader_state</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_package</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.machinery.ModuleSpec" title="Link to this definition">¶</a></dt>
<dd><p>A specification for a module’s import-system-related state.  This is
typically exposed as the module’s <a class="reference internal" href="../reference/import.html#spec__" title="__spec__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__</span></code></a> attribute.  In the
descriptions below, the names in parentheses give the corresponding
attribute available directly on the module object,
e.g. <code class="docutils literal notranslate"><span class="pre">module.__spec__.origin</span> <span class="pre">==</span> <span class="pre">module.__file__</span></code>.  Note, however, that
while the <em>values</em> are usually equivalent, they can differ since there is
no synchronization between the two objects.  For example, it is possible to update
the module’s <a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a> at runtime and this will not be automatically
reflected in the module’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__.origin</span></code>, and vice versa.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.name" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a>)</p>
<p>The module’s fully qualified name.
The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> should always set this attribute to a non-empty string.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.loader">
<span class="sig-name descname"><span class="pre">loader</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.loader" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#loader__" title="__loader__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__loader__</span></code></a>)</p>
<p>The <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> used to load the module.
The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> should always set this attribute.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.origin">
<span class="sig-name descname"><span class="pre">origin</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.origin" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a>)</p>
<p>The location the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> should use to load the module.
For example, for modules loaded from a .py file this is the filename.
The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> should always set this attribute to a meaningful value
for the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> to use.  In the uncommon case that there is not one
(like for namespace packages), it should be set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.submodule_search_locations">
<span class="sig-name descname"><span class="pre">submodule_search_locations</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.submodule_search_locations" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a>)</p>
<p>The list of locations where the package’s submodules will be found.
Most of the time this is a single directory.
The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> should set this attribute to a list, even an empty one, to indicate
to the import system that the module is a package.  It should be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for
non-package modules.  It is set automatically later to a special object for
namespace packages.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.loader_state">
<span class="sig-name descname"><span class="pre">loader_state</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.loader_state" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> may set this attribute to an object containing additional,
module-specific data to use when loading the module.  Otherwise it should be
set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.cached">
<span class="sig-name descname"><span class="pre">cached</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.cached" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#cached__" title="__cached__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cached__</span></code></a>)</p>
<p>The filename of a compiled version of the module’s code.
The <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> should always set this attribute but it may be <code class="docutils literal notranslate"><span class="pre">None</span></code>
for modules that do not need compiled code stored.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.parent" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>(<a class="reference internal" href="../reference/import.html#package__" title="__package__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__package__</span></code></a>)</p>
<p>(Read-only) The fully qualified name of the package the module is in (or the
empty string for a top-level module).
If the module is a package then this is the same as <a class="reference internal" href="#importlib.machinery.ModuleSpec.name" title="importlib.machinery.ModuleSpec.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.machinery.ModuleSpec.has_location">
<span class="sig-name descname"><span class="pre">has_location</span></span><a class="headerlink" href="#importlib.machinery.ModuleSpec.has_location" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">True</span></code> if the spec’s <a class="reference internal" href="#importlib.machinery.ModuleSpec.origin" title="importlib.machinery.ModuleSpec.origin"><code class="xref py py-attr docutils literal notranslate"><span class="pre">origin</span></code></a> refers to a loadable location,</dt><dd><p><code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.  This value impacts how <a class="reference internal" href="#importlib.machinery.ModuleSpec.origin" title="importlib.machinery.ModuleSpec.origin"><code class="xref py py-attr docutils literal notranslate"><span class="pre">origin</span></code></a> is interpreted
and how the module’s <a class="reference internal" href="../reference/import.html#file__" title="__file__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__file__</span></code></a> is populated.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="module-importlib.util">
<span id="importlib-util-utility-code-for-importers"></span><h2><a class="reference internal" href="#module-importlib.util" title="importlib.util: Utility code for importers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.util</span></code></a> – Utility code for importers<a class="headerlink" href="#module-importlib.util" title="Link to this heading">¶</a></h2>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/util.py">Lib/importlib/util.py</a></p>
<hr class="docutils" />
<p>This module contains the various objects that help in the construction of
an <a class="reference internal" href="../glossary.html#term-importer"><span class="xref std std-term">importer</span></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.util.MAGIC_NUMBER">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">MAGIC_NUMBER</span></span><a class="headerlink" href="#importlib.util.MAGIC_NUMBER" title="Link to this definition">¶</a></dt>
<dd><p>The bytes which represent the bytecode version number. If you need help with
loading/writing bytecode then consider <a class="reference internal" href="#importlib.abc.SourceLoader" title="importlib.abc.SourceLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.SourceLoader</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.cache_from_source">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">cache_from_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug_override</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optimization</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.cache_from_source" title="Link to this definition">¶</a></dt>
<dd><p>Return the <span class="target" id="index-21"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a>/<span class="target" id="index-22"></span><a class="pep reference external" href="https://peps.python.org/pep-0488/"><strong>PEP 488</strong></a> path to the byte-compiled file associated
with the source <em>path</em>.  For example, if <em>path</em> is <code class="docutils literal notranslate"><span class="pre">/foo/bar/baz.py</span></code> the return
value would be <code class="docutils literal notranslate"><span class="pre">/foo/bar/__pycache__/baz.cpython-32.pyc</span></code> for Python 3.2.
The <code class="docutils literal notranslate"><span class="pre">cpython-32</span></code> string comes from the current magic tag (see
<code class="xref py py-func docutils literal notranslate"><span class="pre">get_tag()</span></code>; if <code class="xref py py-attr docutils literal notranslate"><span class="pre">sys.implementation.cache_tag</span></code> is not defined then
<a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> will be raised).</p>
<p>The <em>optimization</em> parameter is used to specify the optimization level of the
bytecode file. An empty string represents no optimization, so
<code class="docutils literal notranslate"><span class="pre">/foo/bar/baz.py</span></code> with an <em>optimization</em> of <code class="docutils literal notranslate"><span class="pre">''</span></code> will result in a
bytecode path of <code class="docutils literal notranslate"><span class="pre">/foo/bar/__pycache__/baz.cpython-32.pyc</span></code>. <code class="docutils literal notranslate"><span class="pre">None</span></code> causes
the interpreter’s optimization level to be used. Any other value’s string
representation is used, so <code class="docutils literal notranslate"><span class="pre">/foo/bar/baz.py</span></code> with an <em>optimization</em> of
<code class="docutils literal notranslate"><span class="pre">2</span></code> will lead to the bytecode path of
<code class="docutils literal notranslate"><span class="pre">/foo/bar/__pycache__/baz.cpython-32.opt-2.pyc</span></code>. The string representation
of <em>optimization</em> can only be alphanumeric, else <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p>The <em>debug_override</em> parameter is deprecated and can be used to override
the system’s value for <code class="docutils literal notranslate"><span class="pre">__debug__</span></code>. A <code class="docutils literal notranslate"><span class="pre">True</span></code> value is the equivalent of
setting <em>optimization</em> to the empty string. A <code class="docutils literal notranslate"><span class="pre">False</span></code> value is the same as
setting <em>optimization</em> to <code class="docutils literal notranslate"><span class="pre">1</span></code>. If both <em>debug_override</em> an <em>optimization</em>
are not <code class="docutils literal notranslate"><span class="pre">None</span></code> then <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>optimization</em> parameter was added and the <em>debug_override</em> parameter
was deprecated.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.source_from_cache">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">source_from_cache</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.source_from_cache" title="Link to this definition">¶</a></dt>
<dd><p>Given the <em>path</em> to a <span class="target" id="index-23"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a> file name, return the associated source code
file path.  For example, if <em>path</em> is
<code class="docutils literal notranslate"><span class="pre">/foo/bar/__pycache__/baz.cpython-32.pyc</span></code> the returned path would be
<code class="docutils literal notranslate"><span class="pre">/foo/bar/baz.py</span></code>.  <em>path</em> need not exist, however if it does not conform
to <span class="target" id="index-24"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a> or <span class="target" id="index-25"></span><a class="pep reference external" href="https://peps.python.org/pep-0488/"><strong>PEP 488</strong></a> format, a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised. If
<code class="xref py py-attr docutils literal notranslate"><span class="pre">sys.implementation.cache_tag</span></code> is not defined,
<a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> is raised.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.decode_source">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">decode_source</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source_bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.decode_source" title="Link to this definition">¶</a></dt>
<dd><p>Decode the given bytes representing source code and return it as a string
with universal newlines (as required by
<a class="reference internal" href="#importlib.abc.InspectLoader.get_source" title="importlib.abc.InspectLoader.get_source"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.abc.InspectLoader.get_source()</span></code></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.resolve_name">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">resolve_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">package</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.resolve_name" title="Link to this definition">¶</a></dt>
<dd><p>Resolve a relative module name to an absolute one.</p>
<p>If  <strong>name</strong> has no leading dots, then <strong>name</strong> is simply returned. This
allows for usage such as
<code class="docutils literal notranslate"><span class="pre">importlib.util.resolve_name('sys',</span> <span class="pre">__spec__.parent)</span></code> without doing a
check to see if the <strong>package</strong> argument is needed.</p>
<p><a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is raised if <strong>name</strong> is a relative module name but
<strong>package</strong> is a false value (e.g. <code class="docutils literal notranslate"><span class="pre">None</span></code> or the empty string).
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> is also raised if a relative name would escape its
containing package (e.g. requesting <code class="docutils literal notranslate"><span class="pre">..bacon</span></code> from within the <code class="docutils literal notranslate"><span class="pre">spam</span></code>
package).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>To improve consistency with import statements, raise
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> instead of <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> for invalid relative
import attempts.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.find_spec">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">find_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">package</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.find_spec" title="Link to this definition">¶</a></dt>
<dd><p>Find the <a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">spec</span></a> for a module, optionally relative to
the specified <strong>package</strong> name. If the module is in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>,
then <code class="docutils literal notranslate"><span class="pre">sys.modules[name].__spec__</span></code> is returned (unless the spec would be
<code class="docutils literal notranslate"><span class="pre">None</span></code> or is not set, in which case <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised).
Otherwise a search using <a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a> is done. <code class="docutils literal notranslate"><span class="pre">None</span></code> is
returned if no spec is found.</p>
<p>If <strong>name</strong> is for a submodule (contains a dot), the parent module is
automatically imported.</p>
<p><strong>name</strong> and <strong>package</strong> work the same as for <code class="xref py py-func docutils literal notranslate"><span class="pre">import_module()</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Raises <a class="reference internal" href="exceptions.html#ModuleNotFoundError" title="ModuleNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ModuleNotFoundError</span></code></a> instead of <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> if
<strong>package</strong> is in fact not a package (i.e. lacks a <a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a>
attribute).</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.module_from_spec">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">module_from_spec</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">spec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.module_from_spec" title="Link to this definition">¶</a></dt>
<dd><p>Create a new module based on <strong>spec</strong> and
<a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">spec.loader.create_module</span></code></a>.</p>
<p>If <a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">spec.loader.create_module</span></code></a>
does not return <code class="docutils literal notranslate"><span class="pre">None</span></code>, then any pre-existing attributes will not be reset.
Also, no <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> will be raised if triggered while accessing
<strong>spec</strong> or setting an attribute on the module.</p>
<p>This function is preferred over using <a class="reference internal" href="types.html#types.ModuleType" title="types.ModuleType"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.ModuleType</span></code></a> to create a
new module as <strong>spec</strong> is used to set as many import-controlled attributes on
the module as possible.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.spec_from_loader">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">spec_from_loader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_package</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.spec_from_loader" title="Link to this definition">¶</a></dt>
<dd><p>A factory function for creating a <a class="reference internal" href="#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleSpec</span></code></a>
instance based on a loader.  The parameters have the same meaning as they do
for ModuleSpec.  The function uses available <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> APIs, such as
<code class="xref py py-meth docutils literal notranslate"><span class="pre">InspectLoader.is_package()</span></code>, to fill in any missing
information on the spec.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.spec_from_file_location">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">spec_from_file_location</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">location</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">submodule_search_locations</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.spec_from_file_location" title="Link to this definition">¶</a></dt>
<dd><p>A factory function for creating a <a class="reference internal" href="#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleSpec</span></code></a>
instance based on the path to a file.  Missing information will be filled in
on the spec by making use of loader APIs and by the implication that the
module will be file-based.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util.source_hash">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">source_hash</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source_bytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.source_hash" title="Link to this definition">¶</a></dt>
<dd><p>Return the hash of <em>source_bytes</em> as bytes. A hash-based <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file embeds
the <a class="reference internal" href="#importlib.util.source_hash" title="importlib.util.source_hash"><code class="xref py py-func docutils literal notranslate"><span class="pre">source_hash()</span></code></a> of the corresponding source file’s contents in its
header.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="importlib.util._incompatible_extension_module_restrictions">
<span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">_incompatible_extension_module_restrictions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">disable_check</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util._incompatible_extension_module_restrictions" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that can temporarily skip the compatibility check
for extension modules.  By default the check is enabled and will fail
when a single-phase init module is imported in a subinterpreter.
It will also fail for a multi-phase init module that doesn’t
explicitly support a per-interpreter GIL, when imported
in an interpreter with its own GIL.</p>
<p>Note that this function is meant to accommodate an unusual case;
one which is likely to eventually go away.  There’s is a pretty good
chance this is not what you were looking for.</p>
<p>You can get the same effect as this function by implementing the
basic interface of multi-phase init (<span class="target" id="index-26"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>) and lying about
support for multiple interpreters (or per-interpreter GIL).</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Using this function to disable the check can lead to
unexpected behavior and even crashes.  It should only be used during
extension module development.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.util.LazyLoader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.util.</span></span><span class="sig-name descname"><span class="pre">LazyLoader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">loader</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.LazyLoader" title="Link to this definition">¶</a></dt>
<dd><p>A class which postpones the execution of the loader of a module until the
module has an attribute accessed.</p>
<p>This class <strong>only</strong> works with loaders that define
<a class="reference internal" href="#importlib.abc.Loader.exec_module" title="importlib.abc.Loader.exec_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exec_module()</span></code></a> as control over what module type
is used for the module is required. For those same reasons, the loader’s
<a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code></a> method must return <code class="docutils literal notranslate"><span class="pre">None</span></code> or a
type for which its <code class="docutils literal notranslate"><span class="pre">__class__</span></code> attribute can be mutated along with not
using <a class="reference internal" href="../glossary.html#term-__slots__"><span class="xref std std-term">slots</span></a>. Finally, modules which substitute the object
placed into <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a> will not work as there is no way to properly
replace the module references throughout the interpreter safely;
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if such a substitution is detected.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For projects where startup time is critical, this class allows for
potentially minimizing the cost of loading a module if it is never used.
For projects where startup time is not essential then use of this class is
<strong>heavily</strong> discouraged due to error messages created during loading being
postponed and thus occurring out of context.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Began calling <a class="reference internal" href="#importlib.abc.Loader.create_module" title="importlib.abc.Loader.create_module"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_module()</span></code></a>, removing the
compatibility warning for <a class="reference internal" href="#importlib.machinery.BuiltinImporter" title="importlib.machinery.BuiltinImporter"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.BuiltinImporter</span></code></a> and
<a class="reference internal" href="#importlib.machinery.ExtensionFileLoader" title="importlib.machinery.ExtensionFileLoader"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.ExtensionFileLoader</span></code></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.util.LazyLoader.factory">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">factory</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">loader</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.util.LazyLoader.factory" title="Link to this definition">¶</a></dt>
<dd><p>A class method which returns a callable that creates a lazy loader. This
is meant to be used in situations where the loader is passed by class
instead of by instance.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">suffixes</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">SOURCE_SUFFIXES</span>
<span class="n">loader</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">SourceFileLoader</span>
<span class="n">lazy_loader</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">LazyLoader</span><span class="o">.</span><span class="n">factory</span><span class="p">(</span><span class="n">loader</span><span class="p">)</span>
<span class="n">finder</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">FileFinder</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="p">(</span><span class="n">lazy_loader</span><span class="p">,</span> <span class="n">suffixes</span><span class="p">))</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="examples">
<span id="importlib-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<section id="importing-programmatically">
<h3>Importing programmatically<a class="headerlink" href="#importing-programmatically" title="Link to this heading">¶</a></h3>
<p>To programmatically import a module, use <a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib</span>

<span class="n">itertools</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">import_module</span><span class="p">(</span><span class="s1">&#39;itertools&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="checking-if-a-module-can-be-imported">
<h3>Checking if a module can be imported<a class="headerlink" href="#checking-if-a-module-can-be-imported" title="Link to this heading">¶</a></h3>
<p>If you need to find out if a module can be imported without actually doing the
import, then you should use <a class="reference internal" href="#importlib.util.find_spec" title="importlib.util.find_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.find_spec()</span></code></a>.</p>
<p>Note that if <code class="docutils literal notranslate"><span class="pre">name</span></code> is a submodule (contains a dot),
<a class="reference internal" href="#importlib.util.find_spec" title="importlib.util.find_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.find_spec()</span></code></a> will import the parent module.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib.util</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="c1"># For illustrative purposes.</span>
<span class="n">name</span> <span class="o">=</span> <span class="s1">&#39;itertools&#39;</span>

<span class="k">if</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">name</span><span class="si">!r}</span><span class="s2"> already in sys.modules&quot;</span><span class="p">)</span>
<span class="k">elif</span> <span class="p">(</span><span class="n">spec</span> <span class="o">:=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="n">name</span><span class="p">))</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
    <span class="c1"># If you chose to perform the actual import ...</span>
    <span class="n">module</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">module_from_spec</span><span class="p">(</span><span class="n">spec</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">module</span>
    <span class="n">spec</span><span class="o">.</span><span class="n">loader</span><span class="o">.</span><span class="n">exec_module</span><span class="p">(</span><span class="n">module</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">name</span><span class="si">!r}</span><span class="s2"> has been imported&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;can&#39;t find the </span><span class="si">{</span><span class="n">name</span><span class="si">!r}</span><span class="s2"> module&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="importing-a-source-file-directly">
<h3>Importing a source file directly<a class="headerlink" href="#importing-a-source-file-directly" title="Link to this heading">¶</a></h3>
<p>To import a Python source file directly, use the following recipe:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib.util</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="c1"># For illustrative purposes.</span>
<span class="kn">import</span> <span class="nn">tokenize</span>
<span class="n">file_path</span> <span class="o">=</span> <span class="n">tokenize</span><span class="o">.</span><span class="vm">__file__</span>
<span class="n">module_name</span> <span class="o">=</span> <span class="n">tokenize</span><span class="o">.</span><span class="vm">__name__</span>

<span class="n">spec</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">spec_from_file_location</span><span class="p">(</span><span class="n">module_name</span><span class="p">,</span> <span class="n">file_path</span><span class="p">)</span>
<span class="n">module</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">module_from_spec</span><span class="p">(</span><span class="n">spec</span><span class="p">)</span>
<span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">module_name</span><span class="p">]</span> <span class="o">=</span> <span class="n">module</span>
<span class="n">spec</span><span class="o">.</span><span class="n">loader</span><span class="o">.</span><span class="n">exec_module</span><span class="p">(</span><span class="n">module</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="implementing-lazy-imports">
<h3>Implementing lazy imports<a class="headerlink" href="#implementing-lazy-imports" title="Link to this heading">¶</a></h3>
<p>The example below shows how to implement lazy imports:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">importlib.util</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">sys</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">lazy_import</span><span class="p">(</span><span class="n">name</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">spec</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="n">name</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">loader</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">LazyLoader</span><span class="p">(</span><span class="n">spec</span><span class="o">.</span><span class="n">loader</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">spec</span><span class="o">.</span><span class="n">loader</span> <span class="o">=</span> <span class="n">loader</span>
<span class="gp">... </span>    <span class="n">module</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">module_from_spec</span><span class="p">(</span><span class="n">spec</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">module</span>
<span class="gp">... </span>    <span class="n">loader</span><span class="o">.</span><span class="n">exec_module</span><span class="p">(</span><span class="n">module</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">module</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">lazy_typing</span> <span class="o">=</span> <span class="n">lazy_import</span><span class="p">(</span><span class="s2">&quot;typing&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1">#lazy_typing is a real module object,</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1">#but it is not loaded in memory yet.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">lazy_typing</span><span class="o">.</span><span class="n">TYPE_CHECKING</span>
<span class="go">False</span>
</pre></div>
</div>
</section>
<section id="setting-up-an-importer">
<h3>Setting up an importer<a class="headerlink" href="#setting-up-an-importer" title="Link to this heading">¶</a></h3>
<p>For deep customizations of import, you typically want to implement an
<a class="reference internal" href="../glossary.html#term-importer"><span class="xref std std-term">importer</span></a>. This means managing both the <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> and <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a>
side of things. For finders there are two flavours to choose from depending on
your needs: a <a class="reference internal" href="../glossary.html#term-meta-path-finder"><span class="xref std std-term">meta path finder</span></a> or a <a class="reference internal" href="../glossary.html#term-path-entry-finder"><span class="xref std std-term">path entry finder</span></a>. The
former is what you would put on <a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a> while the latter is what
you create using a <a class="reference internal" href="../glossary.html#term-path-entry-hook"><span class="xref std std-term">path entry hook</span></a> on <a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> which works
with <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> entries to potentially create a finder. This example will
show you how to register your own importers so that import will use them (for
creating an importer for yourself, read the documentation for the appropriate
classes defined within this package):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib.machinery</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="c1"># For illustrative purposes only.</span>
<span class="n">SpamMetaPathFinder</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">PathFinder</span>
<span class="n">SpamPathEntryFinder</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">FileFinder</span>
<span class="n">loader_details</span> <span class="o">=</span> <span class="p">(</span><span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">SourceFileLoader</span><span class="p">,</span>
                  <span class="n">importlib</span><span class="o">.</span><span class="n">machinery</span><span class="o">.</span><span class="n">SOURCE_SUFFIXES</span><span class="p">)</span>

<span class="c1"># Setting up a meta path finder.</span>
<span class="c1"># Make sure to put the finder in the proper location in the list in terms of</span>
<span class="c1"># priority.</span>
<span class="n">sys</span><span class="o">.</span><span class="n">meta_path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SpamMetaPathFinder</span><span class="p">)</span>

<span class="c1"># Setting up a path entry finder.</span>
<span class="c1"># Make sure to put the path hook in the proper location in the list in terms</span>
<span class="c1"># of priority.</span>
<span class="n">sys</span><span class="o">.</span><span class="n">path_hooks</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SpamPathEntryFinder</span><span class="o">.</span><span class="n">path_hook</span><span class="p">(</span><span class="n">loader_details</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="approximating-importlib-import-module">
<h3>Approximating <a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a><a class="headerlink" href="#approximating-importlib-import-module" title="Link to this heading">¶</a></h3>
<p>Import itself is implemented in Python code, making it possible to
expose most of the import machinery through importlib. The following
helps illustrate the various APIs that importlib exposes by providing an
approximate implementation of
<a class="reference internal" href="#importlib.import_module" title="importlib.import_module"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">importlib.util</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="k">def</span> <span class="nf">import_module</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">package</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;An approximate implementation of import.&quot;&quot;&quot;</span>
    <span class="n">absolute_name</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">resolve_name</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">package</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">absolute_name</span><span class="p">]</span>
    <span class="k">except</span> <span class="ne">KeyError</span><span class="p">:</span>
        <span class="k">pass</span>

    <span class="n">path</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="k">if</span> <span class="s1">&#39;.&#39;</span> <span class="ow">in</span> <span class="n">absolute_name</span><span class="p">:</span>
        <span class="n">parent_name</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">child_name</span> <span class="o">=</span> <span class="n">absolute_name</span><span class="o">.</span><span class="n">rpartition</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span>
        <span class="n">parent_module</span> <span class="o">=</span> <span class="n">import_module</span><span class="p">(</span><span class="n">parent_name</span><span class="p">)</span>
        <span class="n">path</span> <span class="o">=</span> <span class="n">parent_module</span><span class="o">.</span><span class="n">__spec__</span><span class="o">.</span><span class="n">submodule_search_locations</span>
    <span class="k">for</span> <span class="n">finder</span> <span class="ow">in</span> <span class="n">sys</span><span class="o">.</span><span class="n">meta_path</span><span class="p">:</span>
        <span class="n">spec</span> <span class="o">=</span> <span class="n">finder</span><span class="o">.</span><span class="n">find_spec</span><span class="p">(</span><span class="n">absolute_name</span><span class="p">,</span> <span class="n">path</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">spec</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">break</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">msg</span> <span class="o">=</span> <span class="sa">f</span><span class="s1">&#39;No module named </span><span class="si">{</span><span class="n">absolute_name</span><span class="si">!r}</span><span class="s1">&#39;</span>
        <span class="k">raise</span> <span class="ne">ModuleNotFoundError</span><span class="p">(</span><span class="n">msg</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="n">absolute_name</span><span class="p">)</span>
    <span class="n">module</span> <span class="o">=</span> <span class="n">importlib</span><span class="o">.</span><span class="n">util</span><span class="o">.</span><span class="n">module_from_spec</span><span class="p">(</span><span class="n">spec</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">absolute_name</span><span class="p">]</span> <span class="o">=</span> <span class="n">module</span>
    <span class="n">spec</span><span class="o">.</span><span class="n">loader</span><span class="o">.</span><span class="n">exec_module</span><span class="p">(</span><span class="n">module</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">path</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="nb">setattr</span><span class="p">(</span><span class="n">parent_module</span><span class="p">,</span> <span class="n">child_name</span><span class="p">,</span> <span class="n">module</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">module</span>
</pre></div>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#module-importlib.abc"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.abc</span></code> – Abstract base classes related to import</a></li>
<li><a class="reference internal" href="#module-importlib.machinery"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.machinery</span></code> – Importers and path hooks</a></li>
<li><a class="reference internal" href="#module-importlib.util"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.util</span></code> – Utility code for importers</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#importing-programmatically">Importing programmatically</a></li>
<li><a class="reference internal" href="#checking-if-a-module-can-be-imported">Checking if a module can be imported</a></li>
<li><a class="reference internal" href="#importing-a-source-file-directly">Importing a source file directly</a></li>
<li><a class="reference internal" href="#implementing-lazy-imports">Implementing lazy imports</a></li>
<li><a class="reference internal" href="#setting-up-an-importer">Setting up an importer</a></li>
<li><a class="reference internal" href="#approximating-importlib-import-module">Approximating <code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.import_module()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="runpy.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">runpy</span></code> — Locating and executing Python modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.resources.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.resources.html" title="importlib.resources – Package resource reading, opening and access"
             >next</a> |</li>
        <li class="right" >
          <a href="runpy.html" title="runpy — Locating and executing Python modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code> — The implementation of <code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>