# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* privacy_lookup
# 
# Translators:
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Most<PERSON>a Bar<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__additional_note
msgid "Additional Note"
msgstr "یادداشت اضافه"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_email
msgid "Anonymized Email"
msgstr "ایمیل ناشناس"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_name
msgid "Anonymized Name"
msgstr "نام ناشناس"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_archive_all
msgid "Archive Selection"
msgstr "انتخاب بایگانی"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
#, python-format
msgid "Archived"
msgstr "بایگانی شده"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Can be archived"
msgstr "می‌تواند بایگانی شود"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__date
msgid "Date"
msgstr "تاریخ"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Delete"
msgstr "حذف"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_unlink_all
msgid "Delete Selection"
msgstr "حذف انتخاب"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Deleted"
msgstr "حذف شده"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model
msgid "Document Model"
msgstr "مدل سند"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__email
msgid "Email"
msgstr "پست الکترونیک"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__execution_details
msgid "Execution Details"
msgstr "جزئیات اجرا"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__records_description
msgid "Found Records"
msgstr "رکوردهای یافت شده"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__user_id
msgid "Handled By"
msgstr "توسط رسیدگی شده"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__has_active
msgid "Has Active"
msgstr "فعال است"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__id
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "ID"
msgstr "شناسه"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_active
msgid "Is Active"
msgstr "فعال است"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_unlinked
msgid "Is Unlinked"
msgstr "قطع ارتباط شده است"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_ids
msgid "Line"
msgstr "سطر"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_count
msgid "Line Count"
msgstr "تعداد خطوط"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__log_id
msgid "Log"
msgstr "ثبت"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Lookup"
msgstr "لوکاپ"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Model"
msgstr "مدل"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__name
msgid "Name"
msgstr "نام"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Open Record"
msgstr "باز کردن رکورد"

#. module: privacy_lookup
#: model:ir.ui.menu,name:privacy_lookup.privacy_menu
msgid "Privacy"
msgstr "حریم‌خصوصی"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_log
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_form
msgid "Privacy Log"
msgstr "ورود به حریم خصوصی"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_action
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_form_action
#: model:ir.ui.menu,name:privacy_lookup.pricacy_log_menu
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_list
msgid "Privacy Logs"
msgstr ""
"<!DOCTYPE html>\n"
"<html lang=\"fa\" dir=\"rtl\">\n"
"<head>\n"
"    <meta charset=\"UTF-8\">\n"
"    <title>سوابق حریم خصوصی</title>\n"
"</head>\n"
"<body>\n"
"    <h1>سوابق حریم خصوصی</h1>\n"
"</body>\n"
"</html>"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_partner
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_user
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
#, python-format
msgid "Privacy Lookup"
msgstr "جستجوی حریم خصوصی"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard_line
msgid "Privacy Lookup Line"
msgstr "خط بررسی حریم خصوصی"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard
msgid "Privacy Lookup Wizard"
msgstr "ویزارد جستجو حریم خصوصی"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard_line
msgid "Privacy Lookup Wizard Line"
msgstr "خط جادوگر جستجوی حریم خصوصی"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__resource_ref
msgid "Record"
msgstr "رکورد"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__records_description
msgid "Records Description"
msgstr "توصیف سوابق"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "References"
msgstr "مراجع"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model_id
msgid "Related Document Model"
msgstr "مدل مدرک مربوطه"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_id
msgid "Resource ID"
msgstr "شناسه منبع"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_name
msgid "Resource name"
msgstr "نام منابع"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Search References"
msgstr "جستجو مراجع"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "The record is already unlinked."
msgstr "رکورد قبلاً حذف شده است."

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/models/privacy_log.py:0
#, python-format
msgid "This email address is not valid (%s)"
msgstr "این آدرس ایمیل معتبر نیست (%s)"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid ""
"This operation is irreversible. Do you wish to proceed to the record "
"deletion?"
msgstr "این عملیات غیرقابل بازگشت است. آیا می‌خواهید به حذف رکورد ادامه دهید؟"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Unarchived"
msgstr "غیرفعال شد"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__wizard_id
msgid "Wizard"
msgstr "تَردست"
