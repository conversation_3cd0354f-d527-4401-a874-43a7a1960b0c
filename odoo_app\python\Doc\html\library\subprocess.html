<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="subprocess — Subprocess management" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/subprocess.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/subprocess.py The subprocess module allows you to spawn new processes, connect to their input/output/error pipes, and obtain their return codes. This module intends to replace seve..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/subprocess.py The subprocess module allows you to spawn new processes, connect to their input/output/error pipes, and obtain their return codes. This module intends to replace seve..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>subprocess — Subprocess management &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="sched — Event scheduler" href="sched.html" />
    <link rel="prev" title="concurrent.futures — Launching parallel tasks" href="concurrent.futures.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/subprocess.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a><ul>
<li><a class="reference internal" href="#using-the-subprocess-module">Using the <code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> Module</a><ul>
<li><a class="reference internal" href="#frequently-used-arguments">Frequently Used Arguments</a></li>
<li><a class="reference internal" href="#popen-constructor">Popen Constructor</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a></li>
<li><a class="reference internal" href="#popen-objects">Popen Objects</a></li>
<li><a class="reference internal" href="#windows-popen-helpers">Windows Popen Helpers</a><ul>
<li><a class="reference internal" href="#windows-constants">Windows Constants</a></li>
</ul>
</li>
<li><a class="reference internal" href="#older-high-level-api">Older high-level API</a></li>
<li><a class="reference internal" href="#replacing-older-functions-with-the-subprocess-module">Replacing Older Functions with the <code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> Module</a><ul>
<li><a class="reference internal" href="#replacing-bin-sh-shell-command-substitution">Replacing <strong class="program">/bin/sh</strong> shell command substitution</a></li>
<li><a class="reference internal" href="#replacing-shell-pipeline">Replacing shell pipeline</a></li>
<li><a class="reference internal" href="#replacing-os-system">Replacing <code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a></li>
<li><a class="reference internal" href="#replacing-the-os-spawn-family">Replacing the <code class="xref py py-func docutils literal notranslate"><span class="pre">os.spawn</span></code> family</a></li>
<li><a class="reference internal" href="#replacing-os-popen-os-popen2-os-popen3">Replacing <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen2()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen3()</span></code></a></li>
<li><a class="reference internal" href="#replacing-functions-from-the-popen2-module">Replacing functions from the <code class="xref py py-mod docutils literal notranslate"><span class="pre">popen2</span></code> module</a></li>
</ul>
</li>
<li><a class="reference internal" href="#legacy-shell-invocation-functions">Legacy Shell Invocation Functions</a></li>
<li><a class="reference internal" href="#notes">Notes</a><ul>
<li><a class="reference internal" href="#converting-an-argument-sequence-to-a-string-on-windows">Converting an argument sequence to a string on Windows</a></li>
<li><a class="reference internal" href="#disabling-use-of-vfork-or-posix-spawn">Disabling use of <code class="docutils literal notranslate"><span class="pre">vfork()</span></code> or <code class="docutils literal notranslate"><span class="pre">posix_spawn()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="concurrent.futures.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent.futures</span></code> — Launching parallel tasks</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sched.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/subprocess.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sched.html" title="sched — Event scheduler"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="concurrent.futures.html" title="concurrent.futures — Launching parallel tasks"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" accesskey="U">Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-subprocess">
<span id="subprocess-subprocess-management"></span><h1><a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> — Subprocess management<a class="headerlink" href="#module-subprocess" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/subprocess.py">Lib/subprocess.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module allows you to spawn new processes, connect to their
input/output/error pipes, and obtain their return codes.  This module intends to
replace several older modules and functions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">os</span><span class="o">.</span><span class="n">system</span>
<span class="n">os</span><span class="o">.</span><span class="n">spawn</span><span class="o">*</span>
</pre></div>
</div>
<p>Information about how the <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module can be used to replace these
modules and functions can be found in the following sections.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0324/"><strong>PEP 324</strong></a> – PEP proposing the subprocess module</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<section id="using-the-subprocess-module">
<h2>Using the <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> Module<a class="headerlink" href="#using-the-subprocess-module" title="Link to this heading">¶</a></h2>
<p>The recommended approach to invoking subprocesses is to use the <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>
function for all use cases it can handle. For more advanced use cases, the
underlying <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> interface can be used directly.</p>
<dl class="py function">
<dt class="sig sig-object py" id="subprocess.run">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">input</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capture_output</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">env</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">universal_newlines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">other_popen_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.run" title="Link to this definition">¶</a></dt>
<dd><p>Run the command described by <em>args</em>.  Wait for command to complete, then
return a <a class="reference internal" href="#subprocess.CompletedProcess" title="subprocess.CompletedProcess"><code class="xref py py-class docutils literal notranslate"><span class="pre">CompletedProcess</span></code></a> instance.</p>
<p>The arguments shown above are merely the most common ones, described below
in <a class="reference internal" href="#frequently-used-arguments"><span class="std std-ref">Frequently Used Arguments</span></a> (hence the use of keyword-only notation
in the abbreviated signature). The full function signature is largely the
same as that of the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> constructor - most of the arguments to
this function are passed through to that interface. (<em>timeout</em>,  <em>input</em>,
<em>check</em>, and <em>capture_output</em> are not.)</p>
<p>If <em>capture_output</em> is true, stdout and stderr will be captured.
When used, the internal <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> object is automatically created with
<em>stdout</em> and <em>stdin</em> both set to <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>.
The <em>stdout</em> and <em>stderr</em> arguments may not be supplied at the same time as <em>capture_output</em>.
If you wish to capture and combine both streams into one,
set <em>stdout</em> to <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>
and <em>stderr</em> to <a class="reference internal" href="#subprocess.STDOUT" title="subprocess.STDOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">STDOUT</span></code></a>,
instead of using <em>capture_output</em>.</p>
<p>A <em>timeout</em> may be specified in seconds, it is internally passed on to
<a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a>. If the timeout expires, the child process will be
killed and waited for. The <a class="reference internal" href="#subprocess.TimeoutExpired" title="subprocess.TimeoutExpired"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutExpired</span></code></a> exception will be
re-raised after the child process has terminated. The initial process
creation itself cannot be interrupted on many platform APIs so you are not
guaranteed to see a timeout exception until at least after however long
process creation takes.</p>
<p>The <em>input</em> argument is passed to <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a> and thus to the
subprocess’s stdin.  If used it must be a byte sequence, or a string if
<em>encoding</em> or <em>errors</em> is specified or <em>text</em> is true.  When
used, the internal <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> object is automatically created with
<em>stdin</em> set to <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>,
and the <em>stdin</em> argument may not be used as well.</p>
<p>If <em>check</em> is true, and the process exits with a non-zero exit code, a
<a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a> exception will be raised. Attributes of that
exception hold the arguments, the exit code, and stdout and stderr if they
were captured.</p>
<p>If <em>encoding</em> or <em>errors</em> are specified, or <em>text</em> is true,
file objects for stdin, stdout and stderr are opened in text mode using the
specified <em>encoding</em> and <em>errors</em> or the <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> default.
The <em>universal_newlines</em> argument is equivalent  to <em>text</em> and is provided
for backwards compatibility. By default, file objects are opened in binary mode.</p>
<p>If <em>env</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it must be a mapping that defines the environment
variables for the new process; these are used instead of the default
behavior of inheriting the current process’ environment. It is passed
directly to <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a>. This mapping can be str to str on any platform
or bytes to bytes on POSIX platforms much like <a class="reference internal" href="os.html#os.environ" title="os.environ"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.environ</span></code></a> or
<a class="reference internal" href="os.html#os.environb" title="os.environb"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.environb</span></code></a>.</p>
<p>Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">([</span><span class="s2">&quot;ls&quot;</span><span class="p">,</span> <span class="s2">&quot;-l&quot;</span><span class="p">])</span>  <span class="c1"># doesn&#39;t capture output</span>
<span class="go">CompletedProcess(args=[&#39;ls&#39;, &#39;-l&#39;], returncode=0)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="s2">&quot;exit 1&quot;</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">subprocess.CalledProcessError</span>: <span class="n">Command &#39;exit 1&#39; returned non-zero exit status 1</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">run</span><span class="p">([</span><span class="s2">&quot;ls&quot;</span><span class="p">,</span> <span class="s2">&quot;-l&quot;</span><span class="p">,</span> <span class="s2">&quot;/dev/null&quot;</span><span class="p">],</span> <span class="n">capture_output</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">CompletedProcess(args=[&#39;ls&#39;, &#39;-l&#39;, &#39;/dev/null&#39;], returncode=0,</span>
<span class="go">stdout=b&#39;crw-rw-rw- 1 root root 1, 3 Jan 23 16:23 /dev/null\n&#39;, stderr=b&#39;&#39;)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added <em>encoding</em> and <em>errors</em> parameters</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <em>text</em> parameter, as a more understandable alias of <em>universal_newlines</em>.
Added the <em>capture_output</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Changed Windows shell search order for <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>. The current
directory and <code class="docutils literal notranslate"><span class="pre">%PATH%</span></code> are replaced with <code class="docutils literal notranslate"><span class="pre">%COMSPEC%</span></code> and
<code class="docutils literal notranslate"><span class="pre">%SystemRoot%\System32\cmd.exe</span></code>. As a result, dropping a
malicious program named <code class="docutils literal notranslate"><span class="pre">cmd.exe</span></code> into a current directory no
longer works.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="subprocess.CompletedProcess">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CompletedProcess</span></span><a class="headerlink" href="#subprocess.CompletedProcess" title="Link to this definition">¶</a></dt>
<dd><p>The return value from <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>, representing a process that has finished.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CompletedProcess.args">
<span class="sig-name descname"><span class="pre">args</span></span><a class="headerlink" href="#subprocess.CompletedProcess.args" title="Link to this definition">¶</a></dt>
<dd><p>The arguments used to launch the process. This may be a list or a string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CompletedProcess.returncode">
<span class="sig-name descname"><span class="pre">returncode</span></span><a class="headerlink" href="#subprocess.CompletedProcess.returncode" title="Link to this definition">¶</a></dt>
<dd><p>Exit status of the child process. Typically, an exit status of 0 indicates
that it ran successfully.</p>
<p>A negative value <code class="docutils literal notranslate"><span class="pre">-N</span></code> indicates that the child was terminated by signal
<code class="docutils literal notranslate"><span class="pre">N</span></code> (POSIX only).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CompletedProcess.stdout">
<span class="sig-name descname"><span class="pre">stdout</span></span><a class="headerlink" href="#subprocess.CompletedProcess.stdout" title="Link to this definition">¶</a></dt>
<dd><p>Captured stdout from the child process. A bytes sequence, or a string if
<a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> was called with an encoding, errors, or text=True.
<code class="docutils literal notranslate"><span class="pre">None</span></code> if stdout was not captured.</p>
<p>If you ran the process with <code class="docutils literal notranslate"><span class="pre">stderr=subprocess.STDOUT</span></code>, stdout and
stderr will be combined in this attribute, and <a class="reference internal" href="#subprocess.CompletedProcess.stderr" title="subprocess.CompletedProcess.stderr"><code class="xref py py-attr docutils literal notranslate"><span class="pre">stderr</span></code></a> will be
<code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CompletedProcess.stderr">
<span class="sig-name descname"><span class="pre">stderr</span></span><a class="headerlink" href="#subprocess.CompletedProcess.stderr" title="Link to this definition">¶</a></dt>
<dd><p>Captured stderr from the child process. A bytes sequence, or a string if
<a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> was called with an encoding, errors, or text=True.
<code class="docutils literal notranslate"><span class="pre">None</span></code> if stderr was not captured.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.CompletedProcess.check_returncode">
<span class="sig-name descname"><span class="pre">check_returncode</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.CompletedProcess.check_returncode" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#subprocess.CompletedProcess.returncode" title="subprocess.CompletedProcess.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> is non-zero, raise a <a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a>.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.DEVNULL">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">DEVNULL</span></span><a class="headerlink" href="#subprocess.DEVNULL" title="Link to this definition">¶</a></dt>
<dd><p>Special value that can be used as the <em>stdin</em>, <em>stdout</em> or <em>stderr</em> argument
to <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> and indicates that the special file <a class="reference internal" href="os.html#os.devnull" title="os.devnull"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.devnull</span></code></a>
will be used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.PIPE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">PIPE</span></span><a class="headerlink" href="#subprocess.PIPE" title="Link to this definition">¶</a></dt>
<dd><p>Special value that can be used as the <em>stdin</em>, <em>stdout</em> or <em>stderr</em> argument
to <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> and indicates that a pipe to the standard stream should be
opened.  Most useful with <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STDOUT">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STDOUT</span></span><a class="headerlink" href="#subprocess.STDOUT" title="Link to this definition">¶</a></dt>
<dd><p>Special value that can be used as the <em>stderr</em> argument to <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> and
indicates that standard error should go into the same handle as standard
output.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="subprocess.SubprocessError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">SubprocessError</span></span><a class="headerlink" href="#subprocess.SubprocessError" title="Link to this definition">¶</a></dt>
<dd><p>Base class for all other exceptions from this module.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">TimeoutExpired</span></span><a class="headerlink" href="#subprocess.TimeoutExpired" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="#subprocess.SubprocessError" title="subprocess.SubprocessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SubprocessError</span></code></a>, raised when a timeout expires
while waiting for a child process.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired.cmd">
<span class="sig-name descname"><span class="pre">cmd</span></span><a class="headerlink" href="#subprocess.TimeoutExpired.cmd" title="Link to this definition">¶</a></dt>
<dd><p>Command that was used to spawn the child process.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired.timeout">
<span class="sig-name descname"><span class="pre">timeout</span></span><a class="headerlink" href="#subprocess.TimeoutExpired.timeout" title="Link to this definition">¶</a></dt>
<dd><p>Timeout in seconds.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired.output">
<span class="sig-name descname"><span class="pre">output</span></span><a class="headerlink" href="#subprocess.TimeoutExpired.output" title="Link to this definition">¶</a></dt>
<dd><p>Output of the child process if it was captured by <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> or
<a class="reference internal" href="#subprocess.check_output" title="subprocess.check_output"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_output()</span></code></a>.  Otherwise, <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This is always
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> when any output was captured regardless of the
<code class="docutils literal notranslate"><span class="pre">text=True</span></code> setting.  It may remain <code class="docutils literal notranslate"><span class="pre">None</span></code> instead of <code class="docutils literal notranslate"><span class="pre">b''</span></code>
when no output was observed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired.stdout">
<span class="sig-name descname"><span class="pre">stdout</span></span><a class="headerlink" href="#subprocess.TimeoutExpired.stdout" title="Link to this definition">¶</a></dt>
<dd><p>Alias for output, for symmetry with <a class="reference internal" href="#subprocess.TimeoutExpired.stderr" title="subprocess.TimeoutExpired.stderr"><code class="xref py py-attr docutils literal notranslate"><span class="pre">stderr</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.TimeoutExpired.stderr">
<span class="sig-name descname"><span class="pre">stderr</span></span><a class="headerlink" href="#subprocess.TimeoutExpired.stderr" title="Link to this definition">¶</a></dt>
<dd><p>Stderr output of the child process if it was captured by <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>.
Otherwise, <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This is always <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> when stderr output
was captured regardless of the <code class="docutils literal notranslate"><span class="pre">text=True</span></code> setting.  It may remain
<code class="docutils literal notranslate"><span class="pre">None</span></code> instead of <code class="docutils literal notranslate"><span class="pre">b''</span></code> when no stderr output was observed.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><em>stdout</em> and <em>stderr</em> attributes added</p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="subprocess.CalledProcessError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CalledProcessError</span></span><a class="headerlink" href="#subprocess.CalledProcessError" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="#subprocess.SubprocessError" title="subprocess.SubprocessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SubprocessError</span></code></a>, raised when a process run by
<a class="reference internal" href="#subprocess.check_call" title="subprocess.check_call"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_call()</span></code></a>, <a class="reference internal" href="#subprocess.check_output" title="subprocess.check_output"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_output()</span></code></a>, or <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> (with <code class="docutils literal notranslate"><span class="pre">check=True</span></code>)
returns a non-zero exit status.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CalledProcessError.returncode">
<span class="sig-name descname"><span class="pre">returncode</span></span><a class="headerlink" href="#subprocess.CalledProcessError.returncode" title="Link to this definition">¶</a></dt>
<dd><p>Exit status of the child process.  If the process exited due to a
signal, this will be the negative signal number.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CalledProcessError.cmd">
<span class="sig-name descname"><span class="pre">cmd</span></span><a class="headerlink" href="#subprocess.CalledProcessError.cmd" title="Link to this definition">¶</a></dt>
<dd><p>Command that was used to spawn the child process.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CalledProcessError.output">
<span class="sig-name descname"><span class="pre">output</span></span><a class="headerlink" href="#subprocess.CalledProcessError.output" title="Link to this definition">¶</a></dt>
<dd><p>Output of the child process if it was captured by <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> or
<a class="reference internal" href="#subprocess.check_output" title="subprocess.check_output"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_output()</span></code></a>.  Otherwise, <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CalledProcessError.stdout">
<span class="sig-name descname"><span class="pre">stdout</span></span><a class="headerlink" href="#subprocess.CalledProcessError.stdout" title="Link to this definition">¶</a></dt>
<dd><p>Alias for output, for symmetry with <a class="reference internal" href="#subprocess.CalledProcessError.stderr" title="subprocess.CalledProcessError.stderr"><code class="xref py py-attr docutils literal notranslate"><span class="pre">stderr</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.CalledProcessError.stderr">
<span class="sig-name descname"><span class="pre">stderr</span></span><a class="headerlink" href="#subprocess.CalledProcessError.stderr" title="Link to this definition">¶</a></dt>
<dd><p>Stderr output of the child process if it was captured by <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>.
Otherwise, <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><em>stdout</em> and <em>stderr</em> attributes added</p>
</div>
</dd></dl>

<section id="frequently-used-arguments">
<span id="id1"></span><h3>Frequently Used Arguments<a class="headerlink" href="#frequently-used-arguments" title="Link to this heading">¶</a></h3>
<p>To support a wide variety of use cases, the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> constructor (and
the convenience functions) accept a large number of optional arguments. For
most typical use cases, many of these arguments can be safely left at their
default values. The arguments that are most commonly needed are:</p>
<blockquote>
<div><p><em>args</em> is required for all calls and should be a string, or a sequence of
program arguments. Providing a sequence of arguments is generally
preferred, as it allows the module to take care of any required escaping
and quoting of arguments (e.g. to permit spaces in file names). If passing
a single string, either <em>shell</em> must be <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> (see below) or else
the string must simply name the program to be executed without specifying
any arguments.</p>
<p><em>stdin</em>, <em>stdout</em> and <em>stderr</em> specify the executed program’s standard input,
standard output and standard error file handles, respectively.  Valid values
are <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a>, an existing file descriptor (a
positive integer), and an existing <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> with a valid file
descriptor.  With the default settings of <code class="docutils literal notranslate"><span class="pre">None</span></code>, no redirection will
occur.  <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a> indicates that a new pipe to the child should be
created.  <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a> indicates that the special file <a class="reference internal" href="os.html#os.devnull" title="os.devnull"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.devnull</span></code></a>
will be used.  Additionally, <em>stderr</em> can be <a class="reference internal" href="#subprocess.STDOUT" title="subprocess.STDOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">STDOUT</span></code></a>, which indicates
that the stderr data from the child process should be captured into the same
file handle as for <em>stdout</em>.</p>
<p id="index-1">If <em>encoding</em> or <em>errors</em> are specified, or <em>text</em> (also known as
<em>universal_newlines</em>) is true,
the file objects <em>stdin</em>, <em>stdout</em> and <em>stderr</em> will be opened in text
mode using the <em>encoding</em> and <em>errors</em> specified in the call or the
defaults for <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>.</p>
<p>For <em>stdin</em>, line ending characters <code class="docutils literal notranslate"><span class="pre">'\n'</span></code> in the input will be converted
to the default line separator <a class="reference internal" href="os.html#os.linesep" title="os.linesep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.linesep</span></code></a>. For <em>stdout</em> and <em>stderr</em>,
all line endings in the output will be converted to <code class="docutils literal notranslate"><span class="pre">'\n'</span></code>.  For more
information see the documentation of the <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> class
when the <em>newline</em> argument to its constructor is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>If text mode is not used, <em>stdin</em>, <em>stdout</em> and <em>stderr</em> will be opened as
binary streams. No encoding or line ending conversion is performed.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <em>encoding</em> and <em>errors</em> parameters.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <em>text</em> parameter as an alias for <em>universal_newlines</em>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The newlines attribute of the file objects <a class="reference internal" href="#subprocess.Popen.stdin" title="subprocess.Popen.stdin"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Popen.stdin</span></code></a>,
<a class="reference internal" href="#subprocess.Popen.stdout" title="subprocess.Popen.stdout"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Popen.stdout</span></code></a> and <a class="reference internal" href="#subprocess.Popen.stderr" title="subprocess.Popen.stderr"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Popen.stderr</span></code></a> are not updated by
the <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a> method.</p>
</div>
<p>If <em>shell</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the specified command will be executed through
the shell.  This can be useful if you are using Python primarily for the
enhanced control flow it offers over most system shells and still want
convenient access to other shell features such as shell pipes, filename
wildcards, environment variable expansion, and expansion of <code class="docutils literal notranslate"><span class="pre">~</span></code> to a
user’s home directory.  However, note that Python itself offers
implementations of many shell-like features (in particular, <a class="reference internal" href="glob.html#module-glob" title="glob: Unix shell style pathname pattern expansion."><code class="xref py py-mod docutils literal notranslate"><span class="pre">glob</span></code></a>,
<a class="reference internal" href="fnmatch.html#module-fnmatch" title="fnmatch: Unix shell style filename pattern matching."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fnmatch</span></code></a>, <a class="reference internal" href="os.html#os.walk" title="os.walk"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.walk()</span></code></a>, <a class="reference internal" href="os.path.html#os.path.expandvars" title="os.path.expandvars"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.expandvars()</span></code></a>,
<a class="reference internal" href="os.path.html#os.path.expanduser" title="os.path.expanduser"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.expanduser()</span></code></a>, and <a class="reference internal" href="shutil.html#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>When <em>universal_newlines</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the class uses the encoding
<a class="reference internal" href="locale.html#locale.getpreferredencoding" title="locale.getpreferredencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">locale.getpreferredencoding(False)</span></code></a>
instead of <code class="docutils literal notranslate"><span class="pre">locale.getpreferredencoding()</span></code>.  See the
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> class for more information on this change.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Read the <a class="reference internal" href="#security-considerations">Security Considerations</a> section before using <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>.</p>
</div>
</div></blockquote>
<p>These options, along with all of the other options, are described in more
detail in the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> constructor documentation.</p>
</section>
<section id="popen-constructor">
<h3>Popen Constructor<a class="headerlink" href="#popen-constructor" title="Link to this heading">¶</a></h3>
<p>The underlying process creation and management in this module is handled by
the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> class. It offers a lot of flexibility so that developers
are able to handle the less common cases not covered by the convenience
functions.</p>
<dl class="py class">
<dt class="sig sig-object py" id="subprocess.Popen">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">Popen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bufsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">executable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preexec_fn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">close_fds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">env</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">universal_newlines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">startupinfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">creationflags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">restore_signals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_new_session</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pass_fds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra_groups</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">umask</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pipesize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">process_group</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen" title="Link to this definition">¶</a></dt>
<dd><p>Execute a child program in a new process.  On POSIX, the class uses
<a class="reference internal" href="os.html#os.execvpe" title="os.execvpe"><code class="xref py py-meth docutils literal notranslate"><span class="pre">os.execvpe()</span></code></a>-like behavior to execute the child program.  On Windows,
the class uses the Windows <code class="docutils literal notranslate"><span class="pre">CreateProcess()</span></code> function.  The arguments to
<a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> are as follows.</p>
<p><em>args</em> should be a sequence of program arguments or else a single string
or <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.
By default, the program to execute is the first item in <em>args</em> if <em>args</em> is
a sequence.  If <em>args</em> is a string, the interpretation is
platform-dependent and described below.  See the <em>shell</em> and <em>executable</em>
arguments for additional differences from the default behavior.  Unless
otherwise stated, it is recommended to pass <em>args</em> as a sequence.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>For maximum reliability, use a fully qualified path for the executable.
To search for an unqualified name on <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code>, use
<a class="reference internal" href="shutil.html#shutil.which" title="shutil.which"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shutil.which()</span></code></a>. On all platforms, passing <a class="reference internal" href="sys.html#sys.executable" title="sys.executable"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.executable</span></code></a>
is the recommended way to launch the current Python interpreter again,
and use the <code class="docutils literal notranslate"><span class="pre">-m</span></code> command-line format to launch an installed module.</p>
<p>Resolving the path of <em>executable</em> (or the first item of <em>args</em>) is
platform dependent. For POSIX, see <a class="reference internal" href="os.html#os.execvpe" title="os.execvpe"><code class="xref py py-meth docutils literal notranslate"><span class="pre">os.execvpe()</span></code></a>, and note that
when resolving or searching for the executable path, <em>cwd</em> overrides the
current working directory and <em>env</em> can override the <code class="docutils literal notranslate"><span class="pre">PATH</span></code>
environment variable. For Windows, see the documentation of the
<code class="docutils literal notranslate"><span class="pre">lpApplicationName</span></code> and <code class="docutils literal notranslate"><span class="pre">lpCommandLine</span></code> parameters of WinAPI
<code class="docutils literal notranslate"><span class="pre">CreateProcess</span></code>, and note that when resolving or searching for the
executable path with <code class="docutils literal notranslate"><span class="pre">shell=False</span></code>, <em>cwd</em> does not override the
current working directory and <em>env</em> cannot override the <code class="docutils literal notranslate"><span class="pre">PATH</span></code>
environment variable. Using a full path avoids all of these variations.</p>
</div>
<p>An example of passing some arguments to an external program
as a sequence is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;/usr/bin/git&quot;</span><span class="p">,</span> <span class="s2">&quot;commit&quot;</span><span class="p">,</span> <span class="s2">&quot;-m&quot;</span><span class="p">,</span> <span class="s2">&quot;Fixes a bug.&quot;</span><span class="p">])</span>
</pre></div>
</div>
<p>On POSIX, if <em>args</em> is a string, the string is interpreted as the name or
path of the program to execute.  However, this can only be done if not
passing arguments to the program.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It may not be obvious how to break a shell command into a sequence of arguments,
especially in complex cases. <a class="reference internal" href="shlex.html#shlex.split" title="shlex.split"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shlex.split()</span></code></a> can illustrate how to
determine the correct tokenization for <em>args</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">shlex</span><span class="o">,</span> <span class="nn">subprocess</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">command_line</span> <span class="o">=</span> <span class="nb">input</span><span class="p">()</span>
<span class="go">/bin/vikings -input eggs.txt -output &quot;spam spam.txt&quot; -cmd &quot;echo &#39;$MONEY&#39;&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">args</span> <span class="o">=</span> <span class="n">shlex</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">command_line</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
<span class="go">[&#39;/bin/vikings&#39;, &#39;-input&#39;, &#39;eggs.txt&#39;, &#39;-output&#39;, &#39;spam spam.txt&#39;, &#39;-cmd&#39;, &quot;echo &#39;$MONEY&#39;&quot;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">Popen</span><span class="p">(</span><span class="n">args</span><span class="p">)</span> <span class="c1"># Success!</span>
</pre></div>
</div>
<p>Note in particular that options (such as <em>-input</em>) and arguments (such
as <em>eggs.txt</em>) that are separated by whitespace in the shell go in separate
list elements, while arguments that need quoting or backslash escaping when
used in the shell (such as filenames containing spaces or the <em>echo</em> command
shown above) are single list elements.</p>
</div>
<p>On Windows, if <em>args</em> is a sequence, it will be converted to a string in a
manner described in <a class="reference internal" href="#converting-argument-sequence"><span class="std std-ref">Converting an argument sequence to a string on Windows</span></a>.  This is because
the underlying <code class="docutils literal notranslate"><span class="pre">CreateProcess()</span></code> operates on strings.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>args</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> if <em>shell</em> is
<code class="docutils literal notranslate"><span class="pre">False</span></code> and a sequence containing path-like objects on POSIX.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><em>args</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> if <em>shell</em> is
<code class="docutils literal notranslate"><span class="pre">False</span></code> and a sequence containing bytes and path-like objects
on Windows.</p>
</div>
<p>The <em>shell</em> argument (which defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>) specifies whether to use
the shell as the program to execute.  If <em>shell</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, it is
recommended to pass <em>args</em> as a string rather than as a sequence.</p>
<p>On POSIX with <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>, the shell defaults to <code class="file docutils literal notranslate"><span class="pre">/bin/sh</span></code>.  If
<em>args</em> is a string, the string specifies the command
to execute through the shell.  This means that the string must be
formatted exactly as it would be when typed at the shell prompt.  This
includes, for example, quoting or backslash escaping filenames with spaces in
them.  If <em>args</em> is a sequence, the first item specifies the command string, and
any additional items will be treated as additional arguments to the shell
itself.  That is to say, <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> does the equivalent of:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Popen</span><span class="p">([</span><span class="s1">&#39;/bin/sh&#39;</span><span class="p">,</span> <span class="s1">&#39;-c&#39;</span><span class="p">,</span> <span class="n">args</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">args</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="o">...</span><span class="p">])</span>
</pre></div>
</div>
<p>On Windows with <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>, the <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">COMSPEC</span></code> environment variable
specifies the default shell.  The only time you need to specify
<code class="docutils literal notranslate"><span class="pre">shell=True</span></code> on Windows is when the command you wish to execute is built
into the shell (e.g. <strong class="command">dir</strong> or <strong class="command">copy</strong>).  You do not need
<code class="docutils literal notranslate"><span class="pre">shell=True</span></code> to run a batch file or console-based executable.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Read the <a class="reference internal" href="#security-considerations">Security Considerations</a> section before using <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>.</p>
</div>
<p><em>bufsize</em> will be supplied as the corresponding argument to the
<a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function when creating the stdin/stdout/stderr pipe
file objects:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">0</span></code> means unbuffered (read and write are one
system call and can return short)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">1</span></code> means line buffered
(only usable if <code class="docutils literal notranslate"><span class="pre">text=True</span></code> or <code class="docutils literal notranslate"><span class="pre">universal_newlines=True</span></code>)</p></li>
<li><p>any other positive value means use a buffer of approximately that
size</p></li>
<li><p>negative bufsize (the default) means the system default of
io.DEFAULT_BUFFER_SIZE will be used.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3.1: </span><em>bufsize</em> now defaults to -1 to enable buffering by default to match the
behavior that most code expects.  In versions prior to Python 3.2.4 and
3.3.1 it incorrectly defaulted to <code class="docutils literal notranslate"><span class="pre">0</span></code> which was unbuffered
and allowed short reads.  This was unintentional and did not match the
behavior of Python 2 as most code expected.</p>
</div>
<p>The <em>executable</em> argument specifies a replacement program to execute.   It
is very seldom needed.  When <code class="docutils literal notranslate"><span class="pre">shell=False</span></code>, <em>executable</em> replaces the
program to execute specified by <em>args</em>.  However, the original <em>args</em> is
still passed to the program.  Most programs treat the program specified
by <em>args</em> as the command name, which can then be different from the program
actually executed.  On POSIX, the <em>args</em> name
becomes the display name for the executable in utilities such as
<strong class="program">ps</strong>.  If <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>, on POSIX the <em>executable</em> argument
specifies a replacement shell for the default <code class="file docutils literal notranslate"><span class="pre">/bin/sh</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>executable</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> on POSIX.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><em>executable</em> parameter accepts a bytes and <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>
on Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Changed Windows shell search order for <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>. The current
directory and <code class="docutils literal notranslate"><span class="pre">%PATH%</span></code> are replaced with <code class="docutils literal notranslate"><span class="pre">%COMSPEC%</span></code> and
<code class="docutils literal notranslate"><span class="pre">%SystemRoot%\System32\cmd.exe</span></code>. As a result, dropping a
malicious program named <code class="docutils literal notranslate"><span class="pre">cmd.exe</span></code> into a current directory no
longer works.</p>
</div>
<p><em>stdin</em>, <em>stdout</em> and <em>stderr</em> specify the executed program’s standard input,
standard output and standard error file handles, respectively.  Valid values
are <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a>, an existing file descriptor (a
positive integer), and an existing <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> with a valid file
descriptor.  With the default settings of <code class="docutils literal notranslate"><span class="pre">None</span></code>, no redirection will
occur.  <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a> indicates that a new pipe to the child should be
created.  <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a> indicates that the special file <a class="reference internal" href="os.html#os.devnull" title="os.devnull"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.devnull</span></code></a>
will be used.  Additionally, <em>stderr</em> can be <a class="reference internal" href="#subprocess.STDOUT" title="subprocess.STDOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">STDOUT</span></code></a>, which indicates
that the stderr data from the applications should be captured into the same
file handle as for <em>stdout</em>.</p>
<p>If <em>preexec_fn</em> is set to a callable object, this object will be called in the
child process just before the child is executed.
(POSIX only)</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <em>preexec_fn</em> parameter is NOT SAFE to use in the presence of threads
in your application.  The child process could deadlock before exec is
called.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If you need to modify the environment for the child use the <em>env</em>
parameter rather than doing it in a <em>preexec_fn</em>.
The <em>start_new_session</em> and <em>process_group</em> parameters should take the place of
code using <em>preexec_fn</em> to call <a class="reference internal" href="os.html#os.setsid" title="os.setsid"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.setsid()</span></code></a> or <a class="reference internal" href="os.html#os.setpgid" title="os.setpgid"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.setpgid()</span></code></a> in the child.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>preexec_fn</em> parameter is no longer supported in subinterpreters.
The use of the parameter in a subinterpreter raises
<a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>. The new restriction may affect applications that
are deployed in mod_wsgi, uWSGI, and other embedded environments.</p>
</div>
<p>If <em>close_fds</em> is true, all file descriptors except <code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">1</span></code> and
<code class="docutils literal notranslate"><span class="pre">2</span></code> will be closed before the child process is executed.  Otherwise
when <em>close_fds</em> is false, file descriptors obey their inheritable flag
as described in <a class="reference internal" href="os.html#fd-inheritance"><span class="std std-ref">Inheritance of File Descriptors</span></a>.</p>
<p>On Windows, if <em>close_fds</em> is true then no handles will be inherited by the
child process unless explicitly passed in the <code class="docutils literal notranslate"><span class="pre">handle_list</span></code> element of
<a class="reference internal" href="#subprocess.STARTUPINFO.lpAttributeList" title="subprocess.STARTUPINFO.lpAttributeList"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STARTUPINFO.lpAttributeList</span></code></a>, or by standard handle redirection.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The default for <em>close_fds</em> was changed from <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> to
what is described above.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>On Windows the default for <em>close_fds</em> was changed from <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> to
<a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> when redirecting the standard handles. It’s now possible to
set <em>close_fds</em> to <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> when redirecting the standard handles.</p>
</div>
<p><em>pass_fds</em> is an optional sequence of file descriptors to keep open
between the parent and child.  Providing any <em>pass_fds</em> forces
<em>close_fds</em> to be <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>.  (POSIX only)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>pass_fds</em> parameter was added.</p>
</div>
<p>If <em>cwd</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, the function changes the working directory to
<em>cwd</em> before executing the child.  <em>cwd</em> can be a string, bytes or
<a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like</span></a> object.  On POSIX, the function
looks for <em>executable</em> (or for the first item in <em>args</em>) relative to <em>cwd</em>
if the executable path is a relative path.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>cwd</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> on POSIX.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>cwd</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> on Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><em>cwd</em> parameter accepts a bytes object on Windows.</p>
</div>
<p>If <em>restore_signals</em> is true (the default) all signals that Python has set to
SIG_IGN are restored to SIG_DFL in the child process before the exec.
Currently this includes the SIGPIPE, SIGXFZ and SIGXFSZ signals.
(POSIX only)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>restore_signals</em> was added.</p>
</div>
<p>If <em>start_new_session</em> is true the <code class="docutils literal notranslate"><span class="pre">setsid()</span></code> system call will be made in the
child process prior to the execution of the subprocess.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>start_new_session</em> was added.</p>
</div>
<p>If <em>process_group</em> is a non-negative integer, the <code class="docutils literal notranslate"><span class="pre">setpgid(0,</span> <span class="pre">value)</span></code> system call will
be made in the child process prior to the execution of the subprocess.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><em>process_group</em> was added.</p>
</div>
<p>If <em>group</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, the setregid() system call will be made in the
child process prior to the execution of the subprocess. If the provided
value is a string, it will be looked up via <a class="reference internal" href="grp.html#grp.getgrnam" title="grp.getgrnam"><code class="xref py py-func docutils literal notranslate"><span class="pre">grp.getgrnam()</span></code></a> and
the value in <code class="docutils literal notranslate"><span class="pre">gr_gid</span></code> will be used. If the value is an integer, it
will be passed verbatim. (POSIX only)</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>If <em>extra_groups</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, the setgroups() system call will be
made in the child process prior to the execution of the subprocess.
Strings provided in <em>extra_groups</em> will be looked up via
<a class="reference internal" href="grp.html#grp.getgrnam" title="grp.getgrnam"><code class="xref py py-func docutils literal notranslate"><span class="pre">grp.getgrnam()</span></code></a> and the values in <code class="docutils literal notranslate"><span class="pre">gr_gid</span></code> will be used.
Integer values will be passed verbatim. (POSIX only)</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>If <em>user</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, the setreuid() system call will be made in the
child process prior to the execution of the subprocess. If the provided
value is a string, it will be looked up via <a class="reference internal" href="pwd.html#pwd.getpwnam" title="pwd.getpwnam"><code class="xref py py-func docutils literal notranslate"><span class="pre">pwd.getpwnam()</span></code></a> and
the value in <code class="docutils literal notranslate"><span class="pre">pw_uid</span></code> will be used. If the value is an integer, it will
be passed verbatim. (POSIX only)</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>If <em>umask</em> is not negative, the umask() system call will be made in the
child process prior to the execution of the subprocess.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: POSIX</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>If <em>env</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it must be a mapping that defines the environment
variables for the new process; these are used instead of the default
behavior of inheriting the current process’ environment. This mapping can be
str to str on any platform or bytes to bytes on POSIX platforms much like
<a class="reference internal" href="os.html#os.environ" title="os.environ"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.environ</span></code></a> or <a class="reference internal" href="os.html#os.environb" title="os.environb"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.environb</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If specified, <em>env</em> must provide any variables required for the program to
execute.  On Windows, in order to run a <a class="reference external" href="https://en.wikipedia.org/wiki/Side-by-Side_Assembly">side-by-side assembly</a> the
specified <em>env</em> <strong>must</strong> include a valid <span class="target" id="index-4"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SystemRoot</span></code>.</p>
</div>
<p>If <em>encoding</em> or <em>errors</em> are specified, or <em>text</em> is true, the file objects
<em>stdin</em>, <em>stdout</em> and <em>stderr</em> are opened in text mode with the specified
<em>encoding</em> and <em>errors</em>, as described above in <a class="reference internal" href="#frequently-used-arguments"><span class="std std-ref">Frequently Used Arguments</span></a>.
The <em>universal_newlines</em> argument is equivalent  to <em>text</em> and is provided
for backwards compatibility. By default, file objects are opened in binary mode.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span><em>encoding</em> and <em>errors</em> were added.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span><em>text</em> was added as a more readable alias for <em>universal_newlines</em>.</p>
</div>
<p>If given, <em>startupinfo</em> will be a <a class="reference internal" href="#subprocess.STARTUPINFO" title="subprocess.STARTUPINFO"><code class="xref py py-class docutils literal notranslate"><span class="pre">STARTUPINFO</span></code></a> object, which is
passed to the underlying <code class="docutils literal notranslate"><span class="pre">CreateProcess</span></code> function.</p>
<p>If given, <em>creationflags</em>, can be one or more of the following flags:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#subprocess.CREATE_NEW_CONSOLE" title="subprocess.CREATE_NEW_CONSOLE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_NEW_CONSOLE</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.CREATE_NEW_PROCESS_GROUP" title="subprocess.CREATE_NEW_PROCESS_GROUP"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_NEW_PROCESS_GROUP</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.ABOVE_NORMAL_PRIORITY_CLASS" title="subprocess.ABOVE_NORMAL_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">ABOVE_NORMAL_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.BELOW_NORMAL_PRIORITY_CLASS" title="subprocess.BELOW_NORMAL_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">BELOW_NORMAL_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.HIGH_PRIORITY_CLASS" title="subprocess.HIGH_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">HIGH_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.IDLE_PRIORITY_CLASS" title="subprocess.IDLE_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">IDLE_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.NORMAL_PRIORITY_CLASS" title="subprocess.NORMAL_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">NORMAL_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.REALTIME_PRIORITY_CLASS" title="subprocess.REALTIME_PRIORITY_CLASS"><code class="xref py py-data docutils literal notranslate"><span class="pre">REALTIME_PRIORITY_CLASS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.CREATE_NO_WINDOW" title="subprocess.CREATE_NO_WINDOW"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_NO_WINDOW</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.DETACHED_PROCESS" title="subprocess.DETACHED_PROCESS"><code class="xref py py-data docutils literal notranslate"><span class="pre">DETACHED_PROCESS</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.CREATE_DEFAULT_ERROR_MODE" title="subprocess.CREATE_DEFAULT_ERROR_MODE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_DEFAULT_ERROR_MODE</span></code></a></p></li>
<li><p><a class="reference internal" href="#subprocess.CREATE_BREAKAWAY_FROM_JOB" title="subprocess.CREATE_BREAKAWAY_FROM_JOB"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_BREAKAWAY_FROM_JOB</span></code></a></p></li>
</ul>
<p><em>pipesize</em> can be used to change the size of the pipe when
<a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a> is used for <em>stdin</em>, <em>stdout</em> or <em>stderr</em>. The size of the pipe
is only changed on platforms that support this (only Linux at this time of
writing). Other platforms will ignore this parameter.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <em>pipesize</em> parameter.</p>
</div>
<p>Popen objects are supported as context managers via the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement:
on exit, standard file descriptors are closed, and the process is waited for.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;ifconfig&quot;</span><span class="p">],</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span> <span class="k">as</span> <span class="n">proc</span><span class="p">:</span>
    <span class="n">log</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">proc</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</pre></div>
</div>
<p class="audit-hook"><p>Popen and the other functions in this module that use it raise an
<a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">subprocess.Popen</span></code> with arguments
<code class="docutils literal notranslate"><span class="pre">executable</span></code>, <code class="docutils literal notranslate"><span class="pre">args</span></code>, <code class="docutils literal notranslate"><span class="pre">cwd</span></code>, and <code class="docutils literal notranslate"><span class="pre">env</span></code>. The value for <code class="docutils literal notranslate"><span class="pre">args</span></code>
may be a single string or a list of strings, depending on platform.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added context manager support.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Popen destructor now emits a <a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a> warning if the child
process is still running.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Popen can use <a class="reference internal" href="os.html#os.posix_spawn" title="os.posix_spawn"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.posix_spawn()</span></code></a> in some cases for better
performance. On Windows Subsystem for Linux and QEMU User Emulation,
Popen constructor using <a class="reference internal" href="os.html#os.posix_spawn" title="os.posix_spawn"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.posix_spawn()</span></code></a> no longer raise an
exception on errors like missing program, but the child process fails
with a non-zero <a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="exceptions">
<h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<p>Exceptions raised in the child process, before the new program has started to
execute, will be re-raised in the parent.</p>
<p>The most common exception raised is <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.  This occurs, for example,
when trying to execute a non-existent file.  Applications should prepare for
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exceptions. Note that, when <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>
will be raised by the child only if the selected shell itself was not found.
To determine if the shell failed to find the requested application, it is
necessary to check the return code or output from the subprocess.</p>
<p>A <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised if <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> is called with invalid
arguments.</p>
<p><a class="reference internal" href="#subprocess.check_call" title="subprocess.check_call"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_call()</span></code></a> and <a class="reference internal" href="#subprocess.check_output" title="subprocess.check_output"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_output()</span></code></a> will raise
<a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a> if the called process returns a non-zero return
code.</p>
<p>All of the functions and methods that accept a <em>timeout</em> parameter, such as
<a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> and <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a> will raise <a class="reference internal" href="#subprocess.TimeoutExpired" title="subprocess.TimeoutExpired"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutExpired</span></code></a> if
the timeout expires before the process exits.</p>
<p>Exceptions defined in this module all inherit from <a class="reference internal" href="#subprocess.SubprocessError" title="subprocess.SubprocessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SubprocessError</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>The <a class="reference internal" href="#subprocess.SubprocessError" title="subprocess.SubprocessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SubprocessError</span></code></a> base class was added.</p>
</div>
</section>
</section>
<section id="security-considerations">
<span id="subprocess-security"></span><h2>Security Considerations<a class="headerlink" href="#security-considerations" title="Link to this heading">¶</a></h2>
<p>Unlike some other popen functions, this implementation will never
implicitly call a system shell.  This means that all characters,
including shell metacharacters, can safely be passed to child processes.
If the shell is invoked explicitly, via <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>, it is the application’s
responsibility to ensure that all whitespace and metacharacters are
quoted appropriately to avoid
<a class="reference external" href="https://en.wikipedia.org/wiki/Shell_injection#Shell_injection">shell injection</a>
vulnerabilities. On <a class="reference internal" href="shlex.html#shlex-quote-warning"><span class="std std-ref">some platforms</span></a>, it is possible
to use <a class="reference internal" href="shlex.html#shlex.quote" title="shlex.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">shlex.quote()</span></code></a> for this escaping.</p>
</section>
<section id="popen-objects">
<h2>Popen Objects<a class="headerlink" href="#popen-objects" title="Link to this heading">¶</a></h2>
<p>Instances of the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> class have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.poll">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">poll</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.poll" title="Link to this definition">¶</a></dt>
<dd><p>Check if child process has terminated.  Set and return
<a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute. Otherwise, returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.wait">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">wait</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.wait" title="Link to this definition">¶</a></dt>
<dd><p>Wait for child process to terminate.  Set and return
<a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute.</p>
<p>If the process does not terminate after <em>timeout</em> seconds, raise a
<a class="reference internal" href="#subprocess.TimeoutExpired" title="subprocess.TimeoutExpired"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutExpired</span></code></a> exception.  It is safe to catch this exception and
retry the wait.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This will deadlock when using <code class="docutils literal notranslate"><span class="pre">stdout=PIPE</span></code> or <code class="docutils literal notranslate"><span class="pre">stderr=PIPE</span></code>
and the child process generates enough output to a pipe such that
it blocks waiting for the OS pipe buffer to accept more data.
Use <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.communicate()</span></code></a> when using pipes to avoid that.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">timeout</span></code> parameter is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, then (on POSIX) the
function is implemented using a busy loop (non-blocking call and short
sleeps). Use the <a class="reference internal" href="asyncio.html#module-asyncio" title="asyncio: Asynchronous I/O."><code class="xref py py-mod docutils literal notranslate"><span class="pre">asyncio</span></code></a> module for an asynchronous wait: see
<a class="reference internal" href="asyncio-subprocess.html#asyncio.create_subprocess_exec" title="asyncio.create_subprocess_exec"><code class="xref py py-class docutils literal notranslate"><span class="pre">asyncio.create_subprocess_exec</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timeout</em> was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.communicate">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">communicate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">input</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.communicate" title="Link to this definition">¶</a></dt>
<dd><p>Interact with process: Send data to stdin.  Read data from stdout and stderr,
until end-of-file is reached.  Wait for process to terminate and set the
<a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute.  The optional <em>input</em> argument should be
data to be sent to the child process, or <code class="docutils literal notranslate"><span class="pre">None</span></code>, if no data should be sent
to the child.  If streams were opened in text mode, <em>input</em> must be a string.
Otherwise, it must be bytes.</p>
<p><a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">communicate()</span></code></a> returns a tuple <code class="docutils literal notranslate"><span class="pre">(stdout_data,</span> <span class="pre">stderr_data)</span></code>.
The data will be strings if streams were opened in text mode; otherwise,
bytes.</p>
<p>Note that if you want to send data to the process’s stdin, you need to create
the Popen object with <code class="docutils literal notranslate"><span class="pre">stdin=PIPE</span></code>.  Similarly, to get anything other than
<code class="docutils literal notranslate"><span class="pre">None</span></code> in the result tuple, you need to give <code class="docutils literal notranslate"><span class="pre">stdout=PIPE</span></code> and/or
<code class="docutils literal notranslate"><span class="pre">stderr=PIPE</span></code> too.</p>
<p>If the process does not terminate after <em>timeout</em> seconds, a
<a class="reference internal" href="#subprocess.TimeoutExpired" title="subprocess.TimeoutExpired"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TimeoutExpired</span></code></a> exception will be raised.  Catching this exception and
retrying communication will not lose any output.</p>
<p>The child process is not killed if the timeout expires, so in order to
cleanup properly a well-behaved application should kill the child process and
finish communication:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">proc</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">Popen</span><span class="p">(</span><span class="o">...</span><span class="p">)</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">outs</span><span class="p">,</span> <span class="n">errs</span> <span class="o">=</span> <span class="n">proc</span><span class="o">.</span><span class="n">communicate</span><span class="p">(</span><span class="n">timeout</span><span class="o">=</span><span class="mi">15</span><span class="p">)</span>
<span class="k">except</span> <span class="n">TimeoutExpired</span><span class="p">:</span>
    <span class="n">proc</span><span class="o">.</span><span class="n">kill</span><span class="p">()</span>
    <span class="n">outs</span><span class="p">,</span> <span class="n">errs</span> <span class="o">=</span> <span class="n">proc</span><span class="o">.</span><span class="n">communicate</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The data read is buffered in memory, so do not use this method if the data
size is large or unlimited.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timeout</em> was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.send_signal">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">send_signal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">signal</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.send_signal" title="Link to this definition">¶</a></dt>
<dd><p>Sends the signal <em>signal</em> to the child.</p>
<p>Do nothing if the process completed.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Windows, SIGTERM is an alias for <a class="reference internal" href="#subprocess.Popen.terminate" title="subprocess.Popen.terminate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">terminate()</span></code></a>. CTRL_C_EVENT and
CTRL_BREAK_EVENT can be sent to processes started with a <em>creationflags</em>
parameter which includes <code class="docutils literal notranslate"><span class="pre">CREATE_NEW_PROCESS_GROUP</span></code>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.terminate">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">terminate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.terminate" title="Link to this definition">¶</a></dt>
<dd><p>Stop the child. On POSIX OSs the method sends <a class="reference internal" href="signal.html#signal.SIGTERM" title="signal.SIGTERM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SIGTERM</span></code></a> to the
child. On Windows the Win32 API function <code class="xref c c-func docutils literal notranslate"><span class="pre">TerminateProcess()</span></code> is called
to stop the child.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="subprocess.Popen.kill">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">kill</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.Popen.kill" title="Link to this definition">¶</a></dt>
<dd><p>Kills the child. On POSIX OSs the function sends SIGKILL to the child.
On Windows <a class="reference internal" href="#subprocess.Popen.kill" title="subprocess.Popen.kill"><code class="xref py py-meth docutils literal notranslate"><span class="pre">kill()</span></code></a> is an alias for <a class="reference internal" href="#subprocess.Popen.terminate" title="subprocess.Popen.terminate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">terminate()</span></code></a>.</p>
</dd></dl>

<p>The following attributes are also set by the class for you to access.
Reassigning them to new values is unsupported:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.args">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">args</span></span><a class="headerlink" href="#subprocess.Popen.args" title="Link to this definition">¶</a></dt>
<dd><p>The <em>args</em> argument as it was passed to <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> – a
sequence of program arguments or else a single string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.stdin">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">stdin</span></span><a class="headerlink" href="#subprocess.Popen.stdin" title="Link to this definition">¶</a></dt>
<dd><p>If the <em>stdin</em> argument was <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is a writeable
stream object as returned by <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>. If the <em>encoding</em> or <em>errors</em>
arguments were specified or the <em>text</em> or <em>universal_newlines</em> argument
was <code class="docutils literal notranslate"><span class="pre">True</span></code>, the stream is a text stream, otherwise it is a byte stream.
If the <em>stdin</em> argument was not <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.stdout">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">stdout</span></span><a class="headerlink" href="#subprocess.Popen.stdout" title="Link to this definition">¶</a></dt>
<dd><p>If the <em>stdout</em> argument was <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is a readable
stream object as returned by <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>. Reading from the stream provides
output from the child process. If the <em>encoding</em> or <em>errors</em> arguments were
specified or the <em>text</em> or <em>universal_newlines</em> argument was <code class="docutils literal notranslate"><span class="pre">True</span></code>, the
stream is a text stream, otherwise it is a byte stream. If the <em>stdout</em>
argument was not <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.stderr">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">stderr</span></span><a class="headerlink" href="#subprocess.Popen.stderr" title="Link to this definition">¶</a></dt>
<dd><p>If the <em>stderr</em> argument was <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is a readable
stream object as returned by <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>. Reading from the stream provides
error output from the child process. If the <em>encoding</em> or <em>errors</em> arguments
were specified or the <em>text</em> or <em>universal_newlines</em> argument was <code class="docutils literal notranslate"><span class="pre">True</span></code>, the
stream is a text stream, otherwise it is a byte stream. If the <em>stderr</em> argument
was not <a class="reference internal" href="#subprocess.PIPE" title="subprocess.PIPE"><code class="xref py py-data docutils literal notranslate"><span class="pre">PIPE</span></code></a>, this attribute is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Use <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">communicate()</span></code></a> rather than <a class="reference internal" href="#subprocess.Popen.stdin" title="subprocess.Popen.stdin"><code class="xref py py-attr docutils literal notranslate"><span class="pre">.stdin.write</span></code></a>,
<a class="reference internal" href="#subprocess.Popen.stdout" title="subprocess.Popen.stdout"><code class="xref py py-attr docutils literal notranslate"><span class="pre">.stdout.read</span></code></a> or <a class="reference internal" href="#subprocess.Popen.stderr" title="subprocess.Popen.stderr"><code class="xref py py-attr docutils literal notranslate"><span class="pre">.stderr.read</span></code></a> to avoid
deadlocks due to any of the other OS pipe buffers filling up and blocking the
child process.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.pid">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">pid</span></span><a class="headerlink" href="#subprocess.Popen.pid" title="Link to this definition">¶</a></dt>
<dd><p>The process ID of the child process.</p>
<p>Note that if you set the <em>shell</em> argument to <code class="docutils literal notranslate"><span class="pre">True</span></code>, this is the process ID
of the spawned shell.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.Popen.returncode">
<span class="sig-prename descclassname"><span class="pre">Popen.</span></span><span class="sig-name descname"><span class="pre">returncode</span></span><a class="headerlink" href="#subprocess.Popen.returncode" title="Link to this definition">¶</a></dt>
<dd><p>The child return code. Initially <code class="docutils literal notranslate"><span class="pre">None</span></code>, <a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> is set by
a call to the <a class="reference internal" href="#subprocess.Popen.poll" title="subprocess.Popen.poll"><code class="xref py py-meth docutils literal notranslate"><span class="pre">poll()</span></code></a>, <a class="reference internal" href="#subprocess.Popen.wait" title="subprocess.Popen.wait"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wait()</span></code></a>, or <a class="reference internal" href="#subprocess.Popen.communicate" title="subprocess.Popen.communicate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">communicate()</span></code></a> methods
if they detect that the process has terminated.</p>
<p>A <code class="docutils literal notranslate"><span class="pre">None</span></code> value indicates that the process hadn’t yet terminated at the
time of the last method call.</p>
<p>A negative value <code class="docutils literal notranslate"><span class="pre">-N</span></code> indicates that the child was terminated by signal
<code class="docutils literal notranslate"><span class="pre">N</span></code> (POSIX only).</p>
</dd></dl>

</section>
<section id="windows-popen-helpers">
<h2>Windows Popen Helpers<a class="headerlink" href="#windows-popen-helpers" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#subprocess.STARTUPINFO" title="subprocess.STARTUPINFO"><code class="xref py py-class docutils literal notranslate"><span class="pre">STARTUPINFO</span></code></a> class and following constants are only available
on Windows.</p>
<dl class="py class">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STARTUPINFO</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dwFlags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hStdInput</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hStdOutput</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hStdError</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">wShowWindow</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lpAttributeList</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.STARTUPINFO" title="Link to this definition">¶</a></dt>
<dd><p>Partial support of the Windows
<a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms686331(v=vs.85).aspx">STARTUPINFO</a>
structure is used for <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> creation.  The following attributes can
be set by passing them as keyword-only arguments.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Keyword-only argument support was added.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.dwFlags">
<span class="sig-name descname"><span class="pre">dwFlags</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.dwFlags" title="Link to this definition">¶</a></dt>
<dd><p>A bit field that determines whether certain <a class="reference internal" href="#subprocess.STARTUPINFO" title="subprocess.STARTUPINFO"><code class="xref py py-class docutils literal notranslate"><span class="pre">STARTUPINFO</span></code></a>
attributes are used when the process creates a window.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">si</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">STARTUPINFO</span><span class="p">()</span>
<span class="n">si</span><span class="o">.</span><span class="n">dwFlags</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">STARTF_USESTDHANDLES</span> <span class="o">|</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">STARTF_USESHOWWINDOW</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.hStdInput">
<span class="sig-name descname"><span class="pre">hStdInput</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.hStdInput" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#subprocess.STARTUPINFO.dwFlags" title="subprocess.STARTUPINFO.dwFlags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dwFlags</span></code></a> specifies <a class="reference internal" href="#subprocess.STARTF_USESTDHANDLES" title="subprocess.STARTF_USESTDHANDLES"><code class="xref py py-data docutils literal notranslate"><span class="pre">STARTF_USESTDHANDLES</span></code></a>, this attribute
is the standard input handle for the process. If
<a class="reference internal" href="#subprocess.STARTF_USESTDHANDLES" title="subprocess.STARTF_USESTDHANDLES"><code class="xref py py-data docutils literal notranslate"><span class="pre">STARTF_USESTDHANDLES</span></code></a> is not specified, the default for standard
input is the keyboard buffer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.hStdOutput">
<span class="sig-name descname"><span class="pre">hStdOutput</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.hStdOutput" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#subprocess.STARTUPINFO.dwFlags" title="subprocess.STARTUPINFO.dwFlags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dwFlags</span></code></a> specifies <a class="reference internal" href="#subprocess.STARTF_USESTDHANDLES" title="subprocess.STARTF_USESTDHANDLES"><code class="xref py py-data docutils literal notranslate"><span class="pre">STARTF_USESTDHANDLES</span></code></a>, this attribute
is the standard output handle for the process. Otherwise, this attribute
is ignored and the default for standard output is the console window’s
buffer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.hStdError">
<span class="sig-name descname"><span class="pre">hStdError</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.hStdError" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#subprocess.STARTUPINFO.dwFlags" title="subprocess.STARTUPINFO.dwFlags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dwFlags</span></code></a> specifies <a class="reference internal" href="#subprocess.STARTF_USESTDHANDLES" title="subprocess.STARTF_USESTDHANDLES"><code class="xref py py-data docutils literal notranslate"><span class="pre">STARTF_USESTDHANDLES</span></code></a>, this attribute
is the standard error handle for the process. Otherwise, this attribute is
ignored and the default for standard error is the console window’s buffer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.wShowWindow">
<span class="sig-name descname"><span class="pre">wShowWindow</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.wShowWindow" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#subprocess.STARTUPINFO.dwFlags" title="subprocess.STARTUPINFO.dwFlags"><code class="xref py py-attr docutils literal notranslate"><span class="pre">dwFlags</span></code></a> specifies <a class="reference internal" href="#subprocess.STARTF_USESHOWWINDOW" title="subprocess.STARTF_USESHOWWINDOW"><code class="xref py py-data docutils literal notranslate"><span class="pre">STARTF_USESHOWWINDOW</span></code></a>, this attribute
can be any of the values that can be specified in the <code class="docutils literal notranslate"><span class="pre">nCmdShow</span></code>
parameter for the
<a class="reference external" href="https://msdn.microsoft.com/en-us/library/ms633548(v=vs.85).aspx">ShowWindow</a>
function, except for <code class="docutils literal notranslate"><span class="pre">SW_SHOWDEFAULT</span></code>. Otherwise, this attribute is
ignored.</p>
<p><a class="reference internal" href="#subprocess.SW_HIDE" title="subprocess.SW_HIDE"><code class="xref py py-data docutils literal notranslate"><span class="pre">SW_HIDE</span></code></a> is provided for this attribute. It is used when
<a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> is called with <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="subprocess.STARTUPINFO.lpAttributeList">
<span class="sig-name descname"><span class="pre">lpAttributeList</span></span><a class="headerlink" href="#subprocess.STARTUPINFO.lpAttributeList" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary of additional attributes for process creation as given in
<code class="docutils literal notranslate"><span class="pre">STARTUPINFOEX</span></code>, see
<a class="reference external" href="https://msdn.microsoft.com/en-us/library/windows/desktop/ms686880(v=vs.85).aspx">UpdateProcThreadAttribute</a>.</p>
<p>Supported attributes:</p>
<dl>
<dt><strong>handle_list</strong></dt><dd><p>Sequence of handles that will be inherited. <em>close_fds</em> must be true if
non-empty.</p>
<p>The handles must be temporarily made inheritable by
<a class="reference internal" href="os.html#os.set_handle_inheritable" title="os.set_handle_inheritable"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.set_handle_inheritable()</span></code></a> when passed to the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a>
constructor, else <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-class docutils literal notranslate"><span class="pre">OSError</span></code></a> will be raised with Windows error
<code class="docutils literal notranslate"><span class="pre">ERROR_INVALID_PARAMETER</span></code> (87).</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>In a multithreaded process, use caution to avoid leaking handles
that are marked inheritable when combining this feature with
concurrent calls to other process creation functions that inherit
all handles such as <a class="reference internal" href="os.html#os.system" title="os.system"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a>.  This also applies to
standard handle redirection, which temporarily creates inheritable
handles.</p>
</div>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

</dd></dl>

<section id="windows-constants">
<h3>Windows Constants<a class="headerlink" href="#windows-constants" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module exposes the following constants.</p>
<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STD_INPUT_HANDLE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STD_INPUT_HANDLE</span></span><a class="headerlink" href="#subprocess.STD_INPUT_HANDLE" title="Link to this definition">¶</a></dt>
<dd><p>The standard input device. Initially, this is the console input buffer,
<code class="docutils literal notranslate"><span class="pre">CONIN$</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STD_OUTPUT_HANDLE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STD_OUTPUT_HANDLE</span></span><a class="headerlink" href="#subprocess.STD_OUTPUT_HANDLE" title="Link to this definition">¶</a></dt>
<dd><p>The standard output device. Initially, this is the active console screen
buffer, <code class="docutils literal notranslate"><span class="pre">CONOUT$</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STD_ERROR_HANDLE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STD_ERROR_HANDLE</span></span><a class="headerlink" href="#subprocess.STD_ERROR_HANDLE" title="Link to this definition">¶</a></dt>
<dd><p>The standard error device. Initially, this is the active console screen
buffer, <code class="docutils literal notranslate"><span class="pre">CONOUT$</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.SW_HIDE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">SW_HIDE</span></span><a class="headerlink" href="#subprocess.SW_HIDE" title="Link to this definition">¶</a></dt>
<dd><p>Hides the window. Another window will be activated.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STARTF_USESTDHANDLES">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STARTF_USESTDHANDLES</span></span><a class="headerlink" href="#subprocess.STARTF_USESTDHANDLES" title="Link to this definition">¶</a></dt>
<dd><p>Specifies that the <a class="reference internal" href="#subprocess.STARTUPINFO.hStdInput" title="subprocess.STARTUPINFO.hStdInput"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STARTUPINFO.hStdInput</span></code></a>,
<a class="reference internal" href="#subprocess.STARTUPINFO.hStdOutput" title="subprocess.STARTUPINFO.hStdOutput"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STARTUPINFO.hStdOutput</span></code></a>, and <a class="reference internal" href="#subprocess.STARTUPINFO.hStdError" title="subprocess.STARTUPINFO.hStdError"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STARTUPINFO.hStdError</span></code></a> attributes
contain additional information.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.STARTF_USESHOWWINDOW">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">STARTF_USESHOWWINDOW</span></span><a class="headerlink" href="#subprocess.STARTF_USESHOWWINDOW" title="Link to this definition">¶</a></dt>
<dd><p>Specifies that the <a class="reference internal" href="#subprocess.STARTUPINFO.wShowWindow" title="subprocess.STARTUPINFO.wShowWindow"><code class="xref py py-attr docutils literal notranslate"><span class="pre">STARTUPINFO.wShowWindow</span></code></a> attribute contains
additional information.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.CREATE_NEW_CONSOLE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CREATE_NEW_CONSOLE</span></span><a class="headerlink" href="#subprocess.CREATE_NEW_CONSOLE" title="Link to this definition">¶</a></dt>
<dd><p>The new process has a new console, instead of inheriting its parent’s
console (the default).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.CREATE_NEW_PROCESS_GROUP">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CREATE_NEW_PROCESS_GROUP</span></span><a class="headerlink" href="#subprocess.CREATE_NEW_PROCESS_GROUP" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
group will be created. This flag is necessary for using <a class="reference internal" href="os.html#os.kill" title="os.kill"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.kill()</span></code></a>
on the subprocess.</p>
<p>This flag is ignored if <a class="reference internal" href="#subprocess.CREATE_NEW_CONSOLE" title="subprocess.CREATE_NEW_CONSOLE"><code class="xref py py-data docutils literal notranslate"><span class="pre">CREATE_NEW_CONSOLE</span></code></a> is specified.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.ABOVE_NORMAL_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">ABOVE_NORMAL_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.ABOVE_NORMAL_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have an above average priority.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.BELOW_NORMAL_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">BELOW_NORMAL_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.BELOW_NORMAL_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have a below average priority.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.HIGH_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">HIGH_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.HIGH_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have a high priority.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.IDLE_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">IDLE_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.IDLE_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have an idle (lowest) priority.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.NORMAL_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">NORMAL_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.NORMAL_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have an normal priority. (default)</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.REALTIME_PRIORITY_CLASS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">REALTIME_PRIORITY_CLASS</span></span><a class="headerlink" href="#subprocess.REALTIME_PRIORITY_CLASS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will have realtime priority.
You should almost never use REALTIME_PRIORITY_CLASS, because this interrupts
system threads that manage mouse input, keyboard input, and background disk
flushing. This class can be appropriate for applications that “talk” directly
to hardware or that perform brief tasks that should have limited interruptions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.CREATE_NO_WINDOW">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CREATE_NO_WINDOW</span></span><a class="headerlink" href="#subprocess.CREATE_NO_WINDOW" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will not create a window.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.DETACHED_PROCESS">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">DETACHED_PROCESS</span></span><a class="headerlink" href="#subprocess.DETACHED_PROCESS" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
will not inherit its parent’s console.
This value cannot be used with CREATE_NEW_CONSOLE.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.CREATE_DEFAULT_ERROR_MODE">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CREATE_DEFAULT_ERROR_MODE</span></span><a class="headerlink" href="#subprocess.CREATE_DEFAULT_ERROR_MODE" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
does not inherit the error mode of the calling process. Instead, the new
process gets the default error mode.
This feature is particularly useful for multithreaded shell applications
that run with hard errors disabled.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="subprocess.CREATE_BREAKAWAY_FROM_JOB">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">CREATE_BREAKAWAY_FROM_JOB</span></span><a class="headerlink" href="#subprocess.CREATE_BREAKAWAY_FROM_JOB" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> <code class="docutils literal notranslate"><span class="pre">creationflags</span></code> parameter to specify that a new process
is not associated with the job.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="older-high-level-api">
<span id="call-function-trio"></span><h2>Older high-level API<a class="headerlink" href="#older-high-level-api" title="Link to this heading">¶</a></h2>
<p>Prior to Python 3.5, these three functions comprised the high level API to
subprocess. You can now use <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> in many cases, but lots of existing code
calls these functions.</p>
<dl class="py function">
<dt class="sig sig-object py" id="subprocess.call">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">call</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">other_popen_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.call" title="Link to this definition">¶</a></dt>
<dd><p>Run the command described by <em>args</em>.  Wait for command to complete, then
return the <a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute.</p>
<p>Code needing to capture stdout or stderr should use <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> instead:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">run</span><span class="p">(</span><span class="o">...</span><span class="p">)</span><span class="o">.</span><span class="n">returncode</span>
</pre></div>
</div>
<p>To suppress stdout or stderr, supply a value of <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a>.</p>
<p>The arguments shown above are merely some common ones.
The full function signature is the
same as that of the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> constructor - this function passes all
supplied arguments other than <em>timeout</em> directly through to that interface.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Do not use <code class="docutils literal notranslate"><span class="pre">stdout=PIPE</span></code> or <code class="docutils literal notranslate"><span class="pre">stderr=PIPE</span></code> with this
function.  The child process will block if it generates enough
output to a pipe to fill up the OS pipe buffer as the pipes are
not being read from.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timeout</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Changed Windows shell search order for <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>. The current
directory and <code class="docutils literal notranslate"><span class="pre">%PATH%</span></code> are replaced with <code class="docutils literal notranslate"><span class="pre">%COMSPEC%</span></code> and
<code class="docutils literal notranslate"><span class="pre">%SystemRoot%\System32\cmd.exe</span></code>. As a result, dropping a
malicious program named <code class="docutils literal notranslate"><span class="pre">cmd.exe</span></code> into a current directory no
longer works.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="subprocess.check_call">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">check_call</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">other_popen_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.check_call" title="Link to this definition">¶</a></dt>
<dd><p>Run command with arguments.  Wait for command to complete. If the return
code was zero then return, otherwise raise <a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a>. The
<a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a> object will have the return code in the
<a class="reference internal" href="#subprocess.CalledProcessError.returncode" title="subprocess.CalledProcessError.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute.
If <a class="reference internal" href="#subprocess.check_call" title="subprocess.check_call"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_call()</span></code></a> was unable to start the process it will propagate the exception
that was raised.</p>
<p>Code needing to capture stdout or stderr should use <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> instead:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">run</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p>To suppress stdout or stderr, supply a value of <a class="reference internal" href="#subprocess.DEVNULL" title="subprocess.DEVNULL"><code class="xref py py-data docutils literal notranslate"><span class="pre">DEVNULL</span></code></a>.</p>
<p>The arguments shown above are merely some common ones.
The full function signature is the
same as that of the <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> constructor - this function passes all
supplied arguments other than <em>timeout</em> directly through to that interface.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Do not use <code class="docutils literal notranslate"><span class="pre">stdout=PIPE</span></code> or <code class="docutils literal notranslate"><span class="pre">stderr=PIPE</span></code> with this
function.  The child process will block if it generates enough
output to a pipe to fill up the OS pipe buffer as the pipes are
not being read from.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timeout</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Changed Windows shell search order for <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>. The current
directory and <code class="docutils literal notranslate"><span class="pre">%PATH%</span></code> are replaced with <code class="docutils literal notranslate"><span class="pre">%COMSPEC%</span></code> and
<code class="docutils literal notranslate"><span class="pre">%SystemRoot%\System32\cmd.exe</span></code>. As a result, dropping a
malicious program named <code class="docutils literal notranslate"><span class="pre">cmd.exe</span></code> into a current directory no
longer works.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="subprocess.check_output">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">check_output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shell</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">universal_newlines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">other_popen_kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.check_output" title="Link to this definition">¶</a></dt>
<dd><p>Run command with arguments and return its output.</p>
<p>If the return code was non-zero it raises a <a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a>. The
<a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a> object will have the return code in the
<a class="reference internal" href="#subprocess.CalledProcessError.returncode" title="subprocess.CalledProcessError.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a> attribute and any output in the
<a class="reference internal" href="#subprocess.CalledProcessError.output" title="subprocess.CalledProcessError.output"><code class="xref py py-attr docutils literal notranslate"><span class="pre">output</span></code></a> attribute.</p>
<p>This is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">run</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">check</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span><span class="o">.</span><span class="n">stdout</span>
</pre></div>
</div>
<p>The arguments shown above are merely some common ones.
The full function signature is largely the same as that of <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> -
most arguments are passed directly through to that interface.
One API deviation from <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> behavior exists: passing <code class="docutils literal notranslate"><span class="pre">input=None</span></code>
will behave the same as <code class="docutils literal notranslate"><span class="pre">input=b''</span></code> (or <code class="docutils literal notranslate"><span class="pre">input=''</span></code>, depending on other
arguments) rather than using the parent’s standard input file handle.</p>
<p>By default, this function will return the data as encoded bytes. The actual
encoding of the output data may depend on the command being invoked, so the
decoding to text will often need to be handled at the application level.</p>
<p>This behaviour may be overridden by setting <em>text</em>, <em>encoding</em>, <em>errors</em>,
or <em>universal_newlines</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> as described in
<a class="reference internal" href="#frequently-used-arguments"><span class="std std-ref">Frequently Used Arguments</span></a> and <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>.</p>
<p>To also capture standard error in the result, use
<code class="docutils literal notranslate"><span class="pre">stderr=subprocess.STDOUT</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">check_output</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;ls non_existent_file; exit 0&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">stderr</span><span class="o">=</span><span class="n">subprocess</span><span class="o">.</span><span class="n">STDOUT</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">&#39;ls: non_existent_file: No such file or directory\n&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>timeout</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Support for the <em>input</em> keyword argument was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>encoding</em> and <em>errors</em> were added.  See <a class="reference internal" href="#subprocess.run" title="subprocess.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a> for details.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span><em>text</em> was added as a more readable alias for <em>universal_newlines</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Changed Windows shell search order for <code class="docutils literal notranslate"><span class="pre">shell=True</span></code>. The current
directory and <code class="docutils literal notranslate"><span class="pre">%PATH%</span></code> are replaced with <code class="docutils literal notranslate"><span class="pre">%COMSPEC%</span></code> and
<code class="docutils literal notranslate"><span class="pre">%SystemRoot%\System32\cmd.exe</span></code>. As a result, dropping a
malicious program named <code class="docutils literal notranslate"><span class="pre">cmd.exe</span></code> into a current directory no
longer works.</p>
</div>
</dd></dl>

</section>
<section id="replacing-older-functions-with-the-subprocess-module">
<span id="subprocess-replacements"></span><h2>Replacing Older Functions with the <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> Module<a class="headerlink" href="#replacing-older-functions-with-the-subprocess-module" title="Link to this heading">¶</a></h2>
<p>In this section, “a becomes b” means that b can be used as a replacement for a.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>All “a” functions in this section fail (more or less) silently if the
executed program cannot be found; the “b” replacements raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>
instead.</p>
<p>In addition, the replacements using <a class="reference internal" href="#subprocess.check_output" title="subprocess.check_output"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_output()</span></code></a> will fail with a
<a class="reference internal" href="#subprocess.CalledProcessError" title="subprocess.CalledProcessError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CalledProcessError</span></code></a> if the requested operation produces a non-zero
return code. The output is still available as the
<a class="reference internal" href="#subprocess.CalledProcessError.output" title="subprocess.CalledProcessError.output"><code class="xref py py-attr docutils literal notranslate"><span class="pre">output</span></code></a> attribute of the raised exception.</p>
</div>
<p>In the following examples, we assume that the relevant functions have already
been imported from the <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module.</p>
<section id="replacing-bin-sh-shell-command-substitution">
<h3>Replacing <strong class="program">/bin/sh</strong> shell command substitution<a class="headerlink" href="#replacing-bin-sh-shell-command-substitution" title="Link to this heading">¶</a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">output</span><span class="o">=</span><span class="k">$(</span>mycmd<span class="w"> </span>myarg<span class="k">)</span>
</pre></div>
</div>
<p>becomes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">output</span> <span class="o">=</span> <span class="n">check_output</span><span class="p">([</span><span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">])</span>
</pre></div>
</div>
</section>
<section id="replacing-shell-pipeline">
<h3>Replacing shell pipeline<a class="headerlink" href="#replacing-shell-pipeline" title="Link to this heading">¶</a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">output</span><span class="o">=</span><span class="k">$(</span>dmesg<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>hda<span class="k">)</span>
</pre></div>
</div>
<p>becomes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">p1</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;dmesg&quot;</span><span class="p">],</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span>
<span class="n">p2</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;grep&quot;</span><span class="p">,</span> <span class="s2">&quot;hda&quot;</span><span class="p">],</span> <span class="n">stdin</span><span class="o">=</span><span class="n">p1</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span>
<span class="n">p1</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>  <span class="c1"># Allow p1 to receive a SIGPIPE if p2 exits.</span>
<span class="n">output</span> <span class="o">=</span> <span class="n">p2</span><span class="o">.</span><span class="n">communicate</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">p1.stdout.close()</span></code> call after starting the p2 is important in order for
p1 to receive a SIGPIPE if p2 exits before p1.</p>
<p>Alternatively, for trusted input, the shell’s own pipeline support may still
be used directly:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nv">output</span><span class="o">=</span><span class="k">$(</span>dmesg<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>hda<span class="k">)</span>
</pre></div>
</div>
<p>becomes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">output</span> <span class="o">=</span> <span class="n">check_output</span><span class="p">(</span><span class="s2">&quot;dmesg | grep hda&quot;</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="replacing-os-system">
<h3>Replacing <a class="reference internal" href="os.html#os.system" title="os.system"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a><a class="headerlink" href="#replacing-os-system" title="Link to this heading">¶</a></h3>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">sts</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">system</span><span class="p">(</span><span class="s2">&quot;mycmd&quot;</span> <span class="o">+</span> <span class="s2">&quot; myarg&quot;</span><span class="p">)</span>
<span class="c1"># becomes</span>
<span class="n">retcode</span> <span class="o">=</span> <span class="n">call</span><span class="p">(</span><span class="s2">&quot;mycmd&quot;</span> <span class="o">+</span> <span class="s2">&quot; myarg&quot;</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p>Notes:</p>
<ul class="simple">
<li><p>Calling the program through the shell is usually not required.</p></li>
<li><p>The <a class="reference internal" href="#subprocess.call" title="subprocess.call"><code class="xref py py-func docutils literal notranslate"><span class="pre">call()</span></code></a> return value is encoded differently to that of
<a class="reference internal" href="os.html#os.system" title="os.system"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a>.</p></li>
<li><p>The <a class="reference internal" href="os.html#os.system" title="os.system"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a> function ignores SIGINT and SIGQUIT signals while
the command is running, but the caller must do this separately when
using the <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module.</p></li>
</ul>
<p>A more realistic example would look like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="n">retcode</span> <span class="o">=</span> <span class="n">call</span><span class="p">(</span><span class="s2">&quot;mycmd&quot;</span> <span class="o">+</span> <span class="s2">&quot; myarg&quot;</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">retcode</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Child was terminated by signal&quot;</span><span class="p">,</span> <span class="o">-</span><span class="n">retcode</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Child returned&quot;</span><span class="p">,</span> <span class="n">retcode</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
<span class="k">except</span> <span class="ne">OSError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Execution failed:&quot;</span><span class="p">,</span> <span class="n">e</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="replacing-the-os-spawn-family">
<h3>Replacing the <a class="reference internal" href="os.html#os.spawnl" title="os.spawnl"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.spawn</span></code></a> family<a class="headerlink" href="#replacing-the-os-spawn-family" title="Link to this heading">¶</a></h3>
<p>P_NOWAIT example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">pid</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">spawnlp</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">P_NOWAIT</span><span class="p">,</span> <span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">pid</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">])</span><span class="o">.</span><span class="n">pid</span>
</pre></div>
</div>
<p>P_WAIT example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">retcode</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">spawnlp</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">P_WAIT</span><span class="p">,</span> <span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">retcode</span> <span class="o">=</span> <span class="n">call</span><span class="p">([</span><span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">])</span>
</pre></div>
</div>
<p>Vector example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">os</span><span class="o">.</span><span class="n">spawnvp</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">P_NOWAIT</span><span class="p">,</span> <span class="n">path</span><span class="p">,</span> <span class="n">args</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">Popen</span><span class="p">([</span><span class="n">path</span><span class="p">]</span> <span class="o">+</span> <span class="n">args</span><span class="p">[</span><span class="mi">1</span><span class="p">:])</span>
</pre></div>
</div>
<p>Environment example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">os</span><span class="o">.</span><span class="n">spawnlpe</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">P_NOWAIT</span><span class="p">,</span> <span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">,</span> <span class="n">env</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;/bin/mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">],</span> <span class="n">env</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;PATH&quot;</span><span class="p">:</span> <span class="s2">&quot;/usr/bin&quot;</span><span class="p">})</span>
</pre></div>
</div>
</section>
<section id="replacing-os-popen-os-popen2-os-popen3">
<h3>Replacing <a class="reference internal" href="os.html#os.popen" title="os.popen"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen()</span></code></a>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen2()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen3()</span></code><a class="headerlink" href="#replacing-os-popen-os-popen2-os-popen3" title="Link to this heading">¶</a></h3>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span> <span class="n">child_stdout</span><span class="p">)</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">popen2</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">mode</span><span class="p">,</span> <span class="n">bufsize</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">bufsize</span><span class="o">=</span><span class="n">bufsize</span><span class="p">,</span>
          <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">close_fds</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span> <span class="n">child_stdout</span><span class="p">)</span> <span class="o">=</span> <span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span>
 <span class="n">child_stdout</span><span class="p">,</span>
 <span class="n">child_stderr</span><span class="p">)</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">popen3</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">mode</span><span class="p">,</span> <span class="n">bufsize</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">bufsize</span><span class="o">=</span><span class="n">bufsize</span><span class="p">,</span>
          <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stderr</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">close_fds</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span>
 <span class="n">child_stdout</span><span class="p">,</span>
 <span class="n">child_stderr</span><span class="p">)</span> <span class="o">=</span> <span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span> <span class="n">child_stdout_and_stderr</span><span class="p">)</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">popen4</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">mode</span><span class="p">,</span> <span class="n">bufsize</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">bufsize</span><span class="o">=</span><span class="n">bufsize</span><span class="p">,</span>
          <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stderr</span><span class="o">=</span><span class="n">STDOUT</span><span class="p">,</span> <span class="n">close_fds</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">(</span><span class="n">child_stdin</span><span class="p">,</span> <span class="n">child_stdout_and_stderr</span><span class="p">)</span> <span class="o">=</span> <span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
</pre></div>
</div>
<p>Return code handling translates as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">pipe</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">rc</span> <span class="o">=</span> <span class="n">pipe</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="k">if</span> <span class="n">rc</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">rc</span> <span class="o">&gt;&gt;</span> <span class="mi">8</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;There were some errors&quot;</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">process</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">process</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="k">if</span> <span class="n">process</span><span class="o">.</span><span class="n">wait</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;There were some errors&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="replacing-functions-from-the-popen2-module">
<h3>Replacing functions from the <code class="xref py py-mod docutils literal notranslate"><span class="pre">popen2</span></code> module<a class="headerlink" href="#replacing-functions-from-the-popen2-module" title="Link to this heading">¶</a></h3>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If the cmd argument to popen2 functions is a string, the command is executed
through /bin/sh.  If it is a list, the command is directly executed.</p>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">child_stdout</span><span class="p">,</span> <span class="n">child_stdin</span><span class="p">)</span> <span class="o">=</span> <span class="n">popen2</span><span class="o">.</span><span class="n">popen2</span><span class="p">(</span><span class="s2">&quot;somestring&quot;</span><span class="p">,</span> <span class="n">bufsize</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="s2">&quot;somestring&quot;</span><span class="p">,</span> <span class="n">shell</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">bufsize</span><span class="o">=</span><span class="n">bufsize</span><span class="p">,</span>
          <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">close_fds</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">(</span><span class="n">child_stdout</span><span class="p">,</span> <span class="n">child_stdin</span><span class="p">)</span> <span class="o">=</span> <span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">)</span>
</pre></div>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">child_stdout</span><span class="p">,</span> <span class="n">child_stdin</span><span class="p">)</span> <span class="o">=</span> <span class="n">popen2</span><span class="o">.</span><span class="n">popen2</span><span class="p">([</span><span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">],</span> <span class="n">bufsize</span><span class="p">,</span> <span class="n">mode</span><span class="p">)</span>
<span class="o">==&gt;</span>
<span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;mycmd&quot;</span><span class="p">,</span> <span class="s2">&quot;myarg&quot;</span><span class="p">],</span> <span class="n">bufsize</span><span class="o">=</span><span class="n">bufsize</span><span class="p">,</span>
          <span class="n">stdin</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">close_fds</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="p">(</span><span class="n">child_stdout</span><span class="p">,</span> <span class="n">child_stdin</span><span class="p">)</span> <span class="o">=</span> <span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="n">p</span><span class="o">.</span><span class="n">stdin</span><span class="p">)</span>
</pre></div>
</div>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">popen2.Popen3</span></code> and <code class="xref py py-class docutils literal notranslate"><span class="pre">popen2.Popen4</span></code> basically work as
<a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">subprocess.Popen</span></code></a>, except that:</p>
<ul class="simple">
<li><p><a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> raises an exception if the execution fails.</p></li>
<li><p>The <em>capturestderr</em> argument is replaced with the <em>stderr</em> argument.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">stdin=PIPE</span></code> and <code class="docutils literal notranslate"><span class="pre">stdout=PIPE</span></code> must be specified.</p></li>
<li><p>popen2 closes all file descriptors by default, but you have to specify
<code class="docutils literal notranslate"><span class="pre">close_fds=True</span></code> with <a class="reference internal" href="#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Popen</span></code></a> to guarantee this behavior on
all platforms or past Python versions.</p></li>
</ul>
</section>
</section>
<section id="legacy-shell-invocation-functions">
<h2>Legacy Shell Invocation Functions<a class="headerlink" href="#legacy-shell-invocation-functions" title="Link to this heading">¶</a></h2>
<p>This module also provides the following legacy functions from the 2.x
<code class="docutils literal notranslate"><span class="pre">commands</span></code> module. These operations implicitly invoke the system shell and
none of the guarantees described above regarding security and exception
handling consistency are valid for these functions.</p>
<dl class="py function">
<dt class="sig sig-object py" id="subprocess.getstatusoutput">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">getstatusoutput</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.getstatusoutput" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">(exitcode,</span> <span class="pre">output)</span></code> of executing <em>cmd</em> in a shell.</p>
<p>Execute the string <em>cmd</em> in a shell with <code class="xref py py-meth docutils literal notranslate"><span class="pre">Popen.check_output()</span></code> and
return a 2-tuple <code class="docutils literal notranslate"><span class="pre">(exitcode,</span> <span class="pre">output)</span></code>.
<em>encoding</em> and <em>errors</em> are used to decode output;
see the notes on <a class="reference internal" href="#frequently-used-arguments"><span class="std std-ref">Frequently Used Arguments</span></a> for more details.</p>
<p>A trailing newline is stripped from the output.
The exit code for the command can be interpreted as the return code
of subprocess.  Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">getstatusoutput</span><span class="p">(</span><span class="s1">&#39;ls /bin/ls&#39;</span><span class="p">)</span>
<span class="go">(0, &#39;/bin/ls&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">getstatusoutput</span><span class="p">(</span><span class="s1">&#39;cat /bin/junk&#39;</span><span class="p">)</span>
<span class="go">(1, &#39;cat: /bin/junk: No such file or directory&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">getstatusoutput</span><span class="p">(</span><span class="s1">&#39;/bin/junk&#39;</span><span class="p">)</span>
<span class="go">(127, &#39;sh: /bin/junk: not found&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">getstatusoutput</span><span class="p">(</span><span class="s1">&#39;/bin/kill $$&#39;</span><span class="p">)</span>
<span class="go">(-15, &#39;&#39;)</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3.4: </span>Windows support was added.</p>
<p>The function now returns (exitcode, output) instead of (status, output)
as it did in Python 3.3.3 and earlier.  exitcode has the same value as
<a class="reference internal" href="#subprocess.Popen.returncode" title="subprocess.Popen.returncode"><code class="xref py py-attr docutils literal notranslate"><span class="pre">returncode</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <em>encoding</em> and <em>errors</em> parameters.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="subprocess.getoutput">
<span class="sig-prename descclassname"><span class="pre">subprocess.</span></span><span class="sig-name descname"><span class="pre">getoutput</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#subprocess.getoutput" title="Link to this definition">¶</a></dt>
<dd><p>Return output (stdout and stderr) of executing <em>cmd</em> in a shell.</p>
<p>Like <a class="reference internal" href="#subprocess.getstatusoutput" title="subprocess.getstatusoutput"><code class="xref py py-func docutils literal notranslate"><span class="pre">getstatusoutput()</span></code></a>, except the exit code is ignored and the return
value is a string containing the command’s output.  Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">subprocess</span><span class="o">.</span><span class="n">getoutput</span><span class="p">(</span><span class="s1">&#39;ls /bin/ls&#39;</span><span class="p">)</span>
<span class="go">&#39;/bin/ls&#39;</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3.4: </span>Windows support added</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <em>encoding</em> and <em>errors</em> parameters.</p>
</div>
</dd></dl>

</section>
<section id="notes">
<h2>Notes<a class="headerlink" href="#notes" title="Link to this heading">¶</a></h2>
<section id="converting-an-argument-sequence-to-a-string-on-windows">
<span id="converting-argument-sequence"></span><h3>Converting an argument sequence to a string on Windows<a class="headerlink" href="#converting-an-argument-sequence-to-a-string-on-windows" title="Link to this heading">¶</a></h3>
<p>On Windows, an <em>args</em> sequence is converted to a string that can be parsed
using the following rules (which correspond to the rules used by the MS C
runtime):</p>
<ol class="arabic simple">
<li><p>Arguments are delimited by white space, which is either a
space or a tab.</p></li>
<li><p>A string surrounded by double quotation marks is
interpreted as a single argument, regardless of white space
contained within.  A quoted string can be embedded in an
argument.</p></li>
<li><p>A double quotation mark preceded by a backslash is
interpreted as a literal double quotation mark.</p></li>
<li><p>Backslashes are interpreted literally, unless they
immediately precede a double quotation mark.</p></li>
<li><p>If backslashes immediately precede a double quotation mark,
every pair of backslashes is interpreted as a literal
backslash.  If the number of backslashes is odd, the last
backslash escapes the next double quotation mark as
described in rule 3.</p></li>
</ol>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="shlex.html#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a></dt><dd><p>Module which provides function to parse and escape command lines.</p>
</dd>
</dl>
</div>
</section>
<section id="disabling-use-of-vfork-or-posix-spawn">
<span id="disable-posix-spawn"></span><span id="disable-vfork"></span><h3>Disabling use of <code class="docutils literal notranslate"><span class="pre">vfork()</span></code> or <code class="docutils literal notranslate"><span class="pre">posix_spawn()</span></code><a class="headerlink" href="#disabling-use-of-vfork-or-posix-spawn" title="Link to this heading">¶</a></h3>
<p>On Linux, <a class="reference internal" href="#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> defaults to using the <code class="docutils literal notranslate"><span class="pre">vfork()</span></code> system call
internally when it is safe to do so rather than <code class="docutils literal notranslate"><span class="pre">fork()</span></code>. This greatly
improves performance.</p>
<p>If you ever encounter a presumed highly unusual situation where you need to
prevent <code class="docutils literal notranslate"><span class="pre">vfork()</span></code> from being used by Python, you can set the
<code class="xref py py-const docutils literal notranslate"><span class="pre">subprocess._USE_VFORK</span></code> attribute to a false value.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">subprocess</span><span class="o">.</span><span class="n">_USE_VFORK</span> <span class="o">=</span> <span class="kc">False</span>  <span class="c1"># See CPython issue gh-NNNNNN.</span>
</pre></div>
</div>
<p>Setting this has no impact on use of <code class="docutils literal notranslate"><span class="pre">posix_spawn()</span></code> which could use
<code class="docutils literal notranslate"><span class="pre">vfork()</span></code> internally within its libc implementation.  There is a similar
<code class="xref py py-const docutils literal notranslate"><span class="pre">subprocess._USE_POSIX_SPAWN</span></code> attribute if you need to prevent use of
that.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">subprocess</span><span class="o">.</span><span class="n">_USE_POSIX_SPAWN</span> <span class="o">=</span> <span class="kc">False</span>  <span class="c1"># See CPython issue gh-NNNNNN.</span>
</pre></div>
</div>
<p>It is safe to set these to false on any Python version. They will have no
effect on older versions when unsupported. Do not assume the attributes are
available to read. Despite their names, a true value does not indicate that the
corresponding function will be used, only that it may be.</p>
<p>Please file issues any time you have to use these private knobs with a way to
reproduce the issue you were seeing. Link to that issue from a comment in your
code.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span><code class="docutils literal notranslate"><span class="pre">_USE_POSIX_SPAWN</span></code></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span><code class="docutils literal notranslate"><span class="pre">_USE_VFORK</span></code></p>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a><ul>
<li><a class="reference internal" href="#using-the-subprocess-module">Using the <code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> Module</a><ul>
<li><a class="reference internal" href="#frequently-used-arguments">Frequently Used Arguments</a></li>
<li><a class="reference internal" href="#popen-constructor">Popen Constructor</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security-considerations">Security Considerations</a></li>
<li><a class="reference internal" href="#popen-objects">Popen Objects</a></li>
<li><a class="reference internal" href="#windows-popen-helpers">Windows Popen Helpers</a><ul>
<li><a class="reference internal" href="#windows-constants">Windows Constants</a></li>
</ul>
</li>
<li><a class="reference internal" href="#older-high-level-api">Older high-level API</a></li>
<li><a class="reference internal" href="#replacing-older-functions-with-the-subprocess-module">Replacing Older Functions with the <code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> Module</a><ul>
<li><a class="reference internal" href="#replacing-bin-sh-shell-command-substitution">Replacing <strong class="program">/bin/sh</strong> shell command substitution</a></li>
<li><a class="reference internal" href="#replacing-shell-pipeline">Replacing shell pipeline</a></li>
<li><a class="reference internal" href="#replacing-os-system">Replacing <code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a></li>
<li><a class="reference internal" href="#replacing-the-os-spawn-family">Replacing the <code class="xref py py-func docutils literal notranslate"><span class="pre">os.spawn</span></code> family</a></li>
<li><a class="reference internal" href="#replacing-os-popen-os-popen2-os-popen3">Replacing <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen2()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">os.popen3()</span></code></a></li>
<li><a class="reference internal" href="#replacing-functions-from-the-popen2-module">Replacing functions from the <code class="xref py py-mod docutils literal notranslate"><span class="pre">popen2</span></code> module</a></li>
</ul>
</li>
<li><a class="reference internal" href="#legacy-shell-invocation-functions">Legacy Shell Invocation Functions</a></li>
<li><a class="reference internal" href="#notes">Notes</a><ul>
<li><a class="reference internal" href="#converting-an-argument-sequence-to-a-string-on-windows">Converting an argument sequence to a string on Windows</a></li>
<li><a class="reference internal" href="#disabling-use-of-vfork-or-posix-spawn">Disabling use of <code class="docutils literal notranslate"><span class="pre">vfork()</span></code> or <code class="docutils literal notranslate"><span class="pre">posix_spawn()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="concurrent.futures.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent.futures</span></code> — Launching parallel tasks</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sched.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sched</span></code> — Event scheduler</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/subprocess.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sched.html" title="sched — Event scheduler"
             >next</a> |</li>
        <li class="right" >
          <a href="concurrent.futures.html" title="concurrent.futures — Launching parallel tasks"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" >Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code> — Subprocess management</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>