# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_viva_wallet
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2024-02-07 08:49+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
msgid "API Key"
msgstr "Κλειδί API"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid "Client ID"
msgstr "Client ID"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_secret
msgid "Client secret"
msgstr ""

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "It is essential to provide API key for the use of viva wallet"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid "Merchant ID"
msgstr "Αναγνωριστικό Εμπόρου"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Message from Viva Wallet: %s"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to cancel a Viva Wallet payment"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to get latest transaction status"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to get the payment status from Viva Wallet"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to send a Viva Wallet payment request"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_session
msgid "Point of Sale Session"
msgstr "Βάρδια Σταθμού Εργασίας"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Run transactions in the test environment."
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "Terminal ID"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Test mode"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later. %s"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later.%s)"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Unable to retrieve Viva Wallet Bearer Token: Please verify that the Client "
"ID and Client Secret are correct"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_bearer_token
msgid "Viva Wallet Bearer Token"
msgstr ""

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Viva Wallet Error"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_latest_response
msgid "Viva Wallet Latest Response"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_endpoint
msgid "Viva Wallet Webhook Endpoint"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_verification_key
msgid "Viva Wallet Webhook Verification Key"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Your transaction with Viva Wallet failed. Please try again later."
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "[Terminal ID of the Viva Wallet terminal], for example: 16002169"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a pos payment provider not registered."
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a terminal not registered in Odoo: %s"
msgstr ""
