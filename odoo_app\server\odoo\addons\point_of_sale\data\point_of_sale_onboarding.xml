<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Resource: pos.category -->
    <record id="pos_category_miscellaneous" model="pos.category">
        <field name="name">Misc</field>
        <field name="image_128" type="base64" file="point_of_sale/static/img/misc_category.png" />
    </record>
    <record id="pos_category_desks" model="pos.category">
        <field name="name">Desks</field>
        <field name="image_128" type="base64" file="point_of_sale/static/img/desk_category.png" />
    </record>
    <record id="pos_category_chairs" model="pos.category">
        <field name="name">Chairs</field>
        <field name="image_128" type="base64" file="point_of_sale/static/img/chair_category.png" />
    </record>

    <function model="pos.config" name="add_cash_payment_method" />

    <!-- Preparation Printer -->
    <record id="preparation_printer" model="pos.printer">
        <field name="name">Preparation Printer</field>
        <field name="proxy_ip">localhost</field>
        <field name="product_categories_ids" eval="[(6, 0, [ref('point_of_sale.pos_category_miscellaneous')])]" />
    </record>

    <!-- Products -->

    <!-- Old -->
    <record id="wall_shelf" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.98</field>
        <field name="name">Wall Shelf Unit</field>
        <field name="default_code">FURN_0009</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="barcode">2100002000003</field>
        <field name="taxes_id" eval="[(5,)]" />
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/wall_shelf_unit.png" />
    </record>
    <record id="small_shelf" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">2.83</field>
        <field name="name">Small Shelf</field>
        <field name="default_code">FURN_0008</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="taxes_id" eval="[(5,)]" />
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="to_weight">True</field>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/small_shelf.png" />
    </record>

    <record id="letter_tray" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">4.80</field>
        <field name="name">Letter Tray</field>
        <field name="default_code">FURN_0004</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/letter_tray.png" />
    </record>
    <record id="desk_organizer" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">5.10</field>
        <field name="name">Desk Organizer</field>
        <field name="default_code">FURN_0001</field>
        <field name="to_weight">True</field>
        <field name="barcode">2300001000008</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/desk_organizer.png" />
        <field name="taxes_id" eval="[(5,)]" /> <!-- no taxes -->
    </record>

    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
                    'xml_id': 'point_of_sale.desk_organizer_product_template',
                    'record': obj().env.ref('point_of_sale.desk_organizer').product_tmpl_id,
                    'noupdate': True,
                }]" />
    </function>

    <record id="size_attribute" model="product.attribute">
        <field name="name">Size</field>
        <field name="sequence">30</field>
        <field name="display_type">radio</field>
        <field name="create_variant">no_variant</field>
    </record>
    <record id="size_attribute_s" model="product.attribute.value">
        <field name="name">S</field>
        <field name="sequence">1</field>
        <field name="attribute_id" ref="size_attribute" />
    </record>
    <record id="size_attribute_m" model="product.attribute.value">
        <field name="name">M</field>
        <field name="sequence">2</field>
        <field name="attribute_id" ref="size_attribute" />
    </record>
    <record id="size_attribute_l" model="product.attribute.value">
        <field name="name">L</field>
        <field name="sequence">3</field>
        <field name="attribute_id" ref="size_attribute" />
    </record>
    <record id="desk_organizer_size" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="point_of_sale.desk_organizer_product_template" />
        <field name="attribute_id" ref="size_attribute" />
        <field name="value_ids"
            eval="[(6, 0, [ref('size_attribute_s'), ref('size_attribute_m'), ref('size_attribute_l')])]" />
    </record>

    <record id="fabric_attribute" model="product.attribute">
        <field name="name">Fabric</field>
        <field name="sequence">40</field>
        <field name="display_type">select</field>
        <field name="create_variant">no_variant</field>
    </record>
    <record id="fabric_attribute_plastic" model="product.attribute.value">
        <field name="name">Plastic</field>
        <field name="sequence">1</field>
        <field name="attribute_id" ref="fabric_attribute" />
    </record>
    <record id="fabric_attribute_leather" model="product.attribute.value">
        <field name="name">Leather</field>
        <field name="sequence">2</field>
        <field name="attribute_id" ref="fabric_attribute" />
    </record>
    <record id="fabric_attribute_custom" model="product.attribute.value">
        <field name="name">Custom</field>
        <field name="sequence">3</field>
        <field name="attribute_id" ref="fabric_attribute" />
        <field name="is_custom">True</field>
    </record>
    <record id="desk_organizer_fabric" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="point_of_sale.desk_organizer_product_template" />
        <field name="attribute_id" ref="fabric_attribute" />
        <field name="value_ids"
            eval="[(6, 0, [ref('fabric_attribute_plastic'), ref('fabric_attribute_leather'), ref('fabric_attribute_custom')])]" />
    </record>

    <record id="magnetic_board" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.98</field>
        <field name="name">Magnetic Board</field>
        <field name="default_code">FURN_0005</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="barcode">2301000000006</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/magnetic_board.png" />
    </record>
    <record id="monitor_stand" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">3.19</field>
        <field name="name">Monitor Stand</field>
        <field name="default_code">FURN_0006</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/monitor_stand.png" />
    </record>
    <record id="desk_pad" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.98</field>
        <field name="name">Desk Pad</field>
        <field name="default_code">FURN_0002</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/desk_pad.png" />
    </record>

    <record id="whiteboard" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.70</field>
        <field name="name">Whiteboard</field>
        <field name="to_weight">True</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/whiteboard.png" />
    </record>

    <record id="led_lamp" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">0.90</field>
        <field name="name">LED Lamp</field>
        <field name="default_code">FURN_0003</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/led_lamp.png" />
    </record>

    <record id="newspaper_rack" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.28</field>
        <field name="name">Newspaper Rack</field>
        <field name="default_code">FURN_0007</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="barcode">2100001000004</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/newspaper_stand.png" />
    </record>

    <record id="whiteboard_pen" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.20</field>
        <field name="name">Whiteboard Pen</field>
        <field name="weight">0.01</field>
        <field name="default_code">CONS_0001</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1" />
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/whiteboard_pen.png" />
    </record>

    <!--
        Those following products come from product demo data.
        As this file is not considered as demo data and its content is red and added to the db
        apart from demo data, we cannot override the product from the product demo data
        but we have to rewrite them here.
    -->

    <!-- Service products -->
    <record id="product_product_1" model="product.product">
        <field name="name">Virtual Interior Design</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">20.5</field>
        <field name="list_price">30.75</field>
        <field name="detailed_type">service</field>
        <field name="uom_id" ref="uom.product_uom_hour"/>
        <field name="uom_po_id" ref="uom.product_uom_hour"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
        <field name="available_in_pos" eval="True"/>
    </record>

    <record id="product_product_2" model="product.product">
        <field name="name">Virtual Home Staging</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">25.5</field>
        <field name="list_price">38.25</field>
        <field name="detailed_type">service</field>
        <field name="uom_id" ref="uom.product_uom_hour"/>
        <field name="uom_po_id" ref="uom.product_uom_hour"/>
        <field name="available_in_pos" eval="True"/>
          <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_delivery_01" model="product.product">
        <field name="name">Office Chair</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">55.0</field>
        <field name="list_price">70.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_7777</field>
        <field name="image_1920" type="base64" file="product/static/img/product_chair.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]"/>
    </record>

    <record id="product_delivery_02" model="product.product">
        <field name="name">Office Lamp</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">35.0</field>
        <field name="list_price">40.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_8888</field>
        <field name="image_1920" type="base64" file="product/static/img/product_lamp.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_order_01" model="product.product">
        <field name="name">Office Design Software</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">235.0</field>
        <field name="list_price">280.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_9999</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_43-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_3" model="product.product">
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
        <field name="name">Desk Combination</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="list_price">450.0</field>
        <field name="standard_price">300.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">Desk combination, black-brown: chair + desk + drawer.</field>
        <field name="default_code">FURN_7800</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_3-image.jpg"/>
    </record>

    <record id="product_attribute_1" model="product.attribute">
        <field name="name">Legs</field>
        <field name="sequence">10</field>
    </record>
    <record id="product_attribute_value_1" model="product.attribute.value">
        <field name="name">Steel</field>
        <field name="attribute_id" ref="product_attribute_1"/>
        <field name="sequence">1</field>
    </record>
    <record id="product_attribute_value_2" model="product.attribute.value">
        <field name="name">Aluminium</field>
        <field name="attribute_id" ref="product_attribute_1"/>
        <field name="sequence">2</field>
    </record>

    <record id="product_attribute_2" model="product.attribute">
        <field name="name">Color</field>
        <field name="sequence">20</field>
    </record>
    <record id="product_attribute_value_3" model="product.attribute.value">
        <field name="name">White</field>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="sequence">1</field>
    </record>
    <record id="product_attribute_value_4" model="product.attribute.value">
        <field name="name">Black</field>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="sequence">2</field>
    </record>

    <record id="product_attribute_3" model="product.attribute">
        <field name="name">Duration</field>
        <field name="sequence">30</field>
    </record>
    <record id="product_attribute_value_5" model="product.attribute.value">
        <field name="name">1 year</field>
        <field name="attribute_id" ref="product_attribute_3"/>
    </record>
    <record id="product_attribute_value_6" model="product.attribute.value">
        <field name="name">2 year</field>
        <field name="attribute_id" ref="product_attribute_3"/>
    </record>

    <record id="product_product_4_product_template" model="product.template">
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]"/>
        <field name="name">Customizable Desk</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">500.0</field>
        <field name="list_price">750.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">160x80cm, with large legs.</field>
    </record>

    <record id="product_4_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="product_product_4_product_template"/>
        <field name="attribute_id" ref="product_attribute_1"/>
        <field name="value_ids" eval="[(6, 0, [ref('point_of_sale.product_attribute_value_1'), ref('point_of_sale.product_attribute_value_2')])]"/>
    </record>
    <record id="product_4_attribute_2_product_template_attribute_line" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="product_product_4_product_template"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('point_of_sale.product_attribute_value_3'), ref('point_of_sale.product_attribute_value_4')])]"/>
    </record>

    <!--
    Handle automatically created product.template.attribute.value.
    Meaning that the combination between the "customizable desk" and the attribute value "black" will be materialized
    into a "product.template.attribute.value" with the ref "point_of_sale.product_4_attribute_1_value_1".
    This will allow setting fields like "price_extra" and "exclude_for"
     -->
    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
            'xml_id': 'point_of_sale.product_4_attribute_1_value_1',
            'record': obj().env.ref('point_of_sale.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[0],
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_4_attribute_1_value_2',
            'record': obj().env.ref('point_of_sale.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[1],
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_4_attribute_2_value_1',
            'record': obj().env.ref('point_of_sale.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[0],
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_4_attribute_2_value_2',
            'record': obj().env.ref('point_of_sale.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[1],
            'noupdate': True,
        },]"/>
    </function>

    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
            'xml_id': 'point_of_sale.product_product_4',
            'record': obj().env.ref('point_of_sale.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('point_of_sale.product_4_attribute_1_value_1') + obj().env.ref('point_of_sale.product_4_attribute_2_value_1')),
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_product_4b',
            'record': obj().env.ref('point_of_sale.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('point_of_sale.product_4_attribute_1_value_1') + obj().env.ref('point_of_sale.product_4_attribute_2_value_2')),
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_product_4c',
            'record': obj().env.ref('point_of_sale.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('point_of_sale.product_4_attribute_1_value_2') + obj().env.ref('point_of_sale.product_4_attribute_2_value_1')),
            'noupdate': True,
        },]"/>
    </function>

    <record id="product_product_4" model="product.product">
        <field name="default_code">FURN_0096</field>
        <field name="standard_price">500.0</field>
        <field name="weight">0.01</field>
        <field name="image_1920" type="base64" file="product/static/img/table02.jpg"/>
    </record>
    <record id="product_product_4b" model="product.product">
        <field name="default_code">FURN_0097</field>
        <field name="weight">0.01</field>
        <field name="standard_price">500.0</field>
        <field name="image_1920" type="base64" file="product/static/img/table04.jpg"/>
    </record>
    <record id="product_product_4c" model="product.product">
        <field name="default_code">FURN_0098</field>
        <field name="weight">0.01</field>
        <field name="standard_price">500.0</field>
        <field name="image_1920" type="base64" file="product/static/img/table03.jpg"/>
    </record>

    <record id="product_product_5" model="product.product">
        <field name="name">Corner Desk Right Sit</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">600.0</field>
        <field name="list_price">147.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM06</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_5-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]"/>
    </record>

    <record id="product_product_6" model="product.product">
        <field name="name">Large Cabinet</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">800.0</field>
        <field name="list_price">320.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM07</field>
        <field name='weight'>0.330</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_6-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
          <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_7" model="product.product">
        <field name="name">Storage Box</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">14.0</field>
        <field name="list_price">15.8</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM08</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_7-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_8" model="product.product">
        <field name="name">Large Desk</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">1299.0</field>
        <field name="list_price">1799.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM09</field>
        <field name='weight'>9.54</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_8-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]"/>
    </record>

    <record id="product_product_9" model="product.product">
        <field name="name">Pedal Bin</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">10.0</field>
        <field name="list_price">47.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM10</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_9-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_10" model="product.product">
        <field name="name">Cabinet with Doors</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">120.50</field>
        <field name="list_price">140</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">E-COM11</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_10-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_11_product_template" model="product.template">
        <field name="name">Conference Chair</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">28</field>
        <field name="list_price">33</field>
        <field name="detailed_type">consu</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="image_1920" type="base64" file="product/static/img/product_product_11-image.png"/>
    </record>

    <!-- the product template attribute lines have to be defined before creating the variants -->
    <record id="product_11_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
        <field name="product_tmpl_id" ref="product_product_11_product_template"/>
        <field name="attribute_id" ref="product_attribute_1"/>
        <field name="value_ids" eval="[(6,0,[ref('point_of_sale.product_attribute_value_1'), ref('point_of_sale.product_attribute_value_2')])]"/>
    </record>

    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
            'xml_id': 'point_of_sale.product_11_attribute_1_value_1',
            'record': obj().env.ref('point_of_sale.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[0],
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_11_attribute_1_value_2',
            'record': obj().env.ref('point_of_sale.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[1],
            'noupdate': True,
        }]"/>
    </function>

    <function model="ir.model.data" name="_update_xmlids">
        <value model="base" eval="[{
            'xml_id': 'point_of_sale.product_product_11',
            'record': obj().env.ref('point_of_sale.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('point_of_sale.product_11_attribute_1_value_1')),
            'noupdate': True,
        }, {
            'xml_id': 'point_of_sale.product_product_11b',
            'record': obj().env.ref('point_of_sale.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('point_of_sale.product_11_attribute_1_value_2')),
            'noupdate': True,
        },]"/>
    </function>

    <record id="product_product_11" model="product.product">
        <field name="default_code">E-COM12</field>
        <field name="weight">0.01</field>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]"/>
    </record>
    <record id="product_product_11b" model="product.product">
        <field name="default_code">E-COM13</field>
        <field name="weight">0.01</field>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]"/>
    </record>

    <record id="point_of_sale.product_4_attribute_1_value_2" model="product.template.attribute.value">
        <field name="price_extra">50.40</field>
    </record>

    <record id="point_of_sale.product_11_attribute_1_value_2" model="product.template.attribute.value">
        <field name="price_extra">6.40</field>
    </record>

    <record id="product_template_attribute_exclusion_1" model="product.template.attribute.exclusion">
        <field name="product_tmpl_id" ref="point_of_sale.product_product_4_product_template" />
        <field name="value_ids" eval="[(6, 0, [ref('point_of_sale.product_4_attribute_2_value_2')])]"/>
    </record>
    <record id="product_template_attribute_exclusion_2" model="product.template.attribute.exclusion">
        <field name="product_tmpl_id" ref="point_of_sale.product_product_11_product_template" />
        <field name="value_ids" eval="[(6, 0, [ref('point_of_sale.product_11_attribute_1_value_1')])]"/>
    </record>
    <record id="product_template_attribute_exclusion_3" model="product.template.attribute.exclusion">
        <field name="product_tmpl_id" ref="point_of_sale.product_product_11_product_template" />
        <field name="value_ids" eval="[(6, 0, [ref('point_of_sale.product_11_attribute_1_value_2')])]"/>
    </record>

    <!--
    The "Customizable Desk's Aluminium" attribute value will excude:
    - The "Customizable Desk's Black" attribute
    - The "Office Chair's Steel" attribute
     -->
    <record id="product_4_attribute_1_value_2" model="product.template.attribute.value">
        <field name="exclude_for" eval="[(6, 0, [ref('point_of_sale.product_template_attribute_exclusion_1'), ref('point_of_sale.product_template_attribute_exclusion_2')])]" />
    </record>
    <!--
    The "Customizable Desk's Steel" attribute value will excude:
    - The "Office Chair's Aluminium" attribute
    -->
    <record id="product_4_attribute_1_value_1" model="product.template.attribute.value">
        <field name="exclude_for" eval="[(6, 0, [ref('point_of_sale.product_template_attribute_exclusion_3')])]" />
    </record>

    <record id="product_product_12" model="product.product">
        <field name="name">Office Chair Black</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">180</field>
        <field name="list_price">120.50</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_0269</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_12-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]"/>
    </record>

    <record id="product_product_13" model="product.product">
        <field name="name">Corner Desk Left Sit</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">78.0</field>
        <field name="list_price">85.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_1118</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_13-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]"/>
    </record>

    <record id="product_product_16" model="product.product">
        <field name="name">Drawer Black</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">20.0</field>
        <field name="list_price">25.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_8900</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_16-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_20" model="product.product">
        <field name="name">Flipover</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">1700.0</field>
        <field name="list_price">1950.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_9001</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_20-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_22" model="product.product">
        <field name="name">Desk Stand with Screen</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">2010.0</field>
        <field name="list_price">2100.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_7888</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_22-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_24" model="product.product">
        <field name="name">Individual Workplace</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">876.0</field>
        <field name="list_price">885.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_0789</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_24-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_25" model="product.product">
        <field name="name">Acoustic Bloc Screens</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">287.0</field>
        <field name="list_price">295.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="default_code">FURN_6666</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_25-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="product_product_27" model="product.product">
        <field name="name">Drawer</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">100.0</field>
        <field name="list_price">110.50</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description">Drawer with two routing possiblities.</field>
        <field name="default_code">FURN_8855</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_27-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="consu_delivery_03" model="product.product">
        <field name="name">Four Person Desk</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">2500.0</field>
        <field name="list_price">2350.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">Four person modern office workstation</field>
        <field name="default_code">FURN_8220</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_d03-image.png"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]"/>
    </record>

    <record id="consu_delivery_02" model="product.product">
        <field name="name">Large Meeting Table</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">4500.0</field>
        <field name="list_price">4000.0</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">Conference room table</field>
        <field name="default_code">FURN_6741</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_46-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="consu_delivery_01" model="product.product">
        <field name="name">Three-Seat Sofa</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="standard_price">1000</field>
        <field name="list_price">1500</field>
        <field name="detailed_type">consu</field>
        <field name="weight">0.01</field>
        <field name="uom_id" ref="uom.product_uom_unit"/>
        <field name="uom_po_id" ref="uom.product_uom_unit"/>
        <field name="description_sale">Three Seater Sofa with Lounger in Steel Grey Colour</field>
        <field name="default_code">FURN_8999</field>
        <field name="image_1920" type="base64" file="product/static/img/product_product_d01-image.jpg"/>
        <field name="available_in_pos" eval="True"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>

    <record id="desk_organizer_combo_line" model="pos.combo.line">
        <field name="product_id" ref="desk_organizer"/>
        <field name="combo_price">0</field>
    </record>
    <record id="desk_pad_combo_line" model="pos.combo.line">
        <field name="product_id" ref="desk_pad"/>
        <field name="combo_price">0</field>
    </record>
    <record id="monitor_stand_combo_line" model="pos.combo.line">
        <field name="product_id" ref="monitor_stand"/>
        <field name="combo_price">2</field>
    </record>
    <record id="desk_accessories_combo" model="pos.combo">
        <field name="name">Desk Accessories Combo</field>
        <field name="combo_line_ids" eval="[(6, 0, [ref('desk_organizer_combo_line'), ref('desk_pad_combo_line'), ref('monitor_stand_combo_line')])]"/>
    </record>

    <record id="product_3_combo_line" model="pos.combo.line">
        <field name="product_id" ref="product_product_3"/>
        <field name="combo_price">0</field>
    </record>
    <record id="product_5_combo_line" model="pos.combo.line">
        <field name="product_id" ref="product_product_5"/>
        <field name="combo_price">0</field>
    </record>
    <record id="desks_combo" model="pos.combo">
        <field name="name">Desks Combo</field>
        <field name="combo_line_ids" eval="[(6, 0, [ref('product_3_combo_line'), ref('product_5_combo_line')])]"/>
    </record>

    <record id="product_11_combo_line" model="pos.combo.line">
        <field name="product_id" ref="product_product_11"/>
        <field name="combo_price">0</field>
    </record>
    <record id="product_11b_combo_line" model="pos.combo.line">
        <field name="product_id" ref="product_product_11b"/>
        <field name="combo_price">0</field>
    </record>
    <record id="product_12_combo_line" model="pos.combo.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="combo_price">0</field>
    </record>
    <record id="chairs_combo" model="pos.combo">
        <field name="name">Chairs Combo</field>
        <field name="combo_line_ids" eval="[(6, 0, [ref('product_11_combo_line'), ref('product_11b_combo_line'), ref('product_12_combo_line')])]"/>
    </record>

    <record id="office_combo" model="product.product">
      <field name="available_in_pos">True</field>
      <field name="list_price">160</field>
      <field name="name">Office combo</field>
      <field name="type">combo</field>
      <field name="categ_id" ref="product.product_category_1"/>
      <field name="uom_id" ref="uom.product_uom_unit"/>
      <field name="uom_po_id" ref="uom.product_uom_unit"/>
      <field name="image_1920" type="base64" file="point_of_sale/static/img/office_combo.jpg"/>
      <field name="combo_ids" eval="[(6, 0, [ref('desks_combo'), ref('chairs_combo'), ref('desk_accessories_combo')])]"/>
      <field name="taxes_id" eval="[(5,)]"/>  <!-- no taxes -->
      <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]"/>
    </record>
</odoo>
