# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>rr, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_userid
msgid "API User ID"
msgstr "API User ID"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_password
msgid "API User Password"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__code
msgid "Code"
msgstr "Cod"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Nu s-a putut stabili conexiunea cu API-ul."

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_hash_function
msgid "Hash function"
msgstr ""

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nu s-a găsit nicio tranzacție care să se potrivească cu referința %s."

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__code__ogone
#: model:payment.provider,name:payment_ogone.payment_provider_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_pspid
msgid "PSPID"
msgstr ""

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_provider
msgid "Payment Provider"
msgstr "Furnizor de plată"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "Tranzacție plată"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_in
msgid "SHA Key IN"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_out
msgid "SHA Key OUT"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha1
msgid "SHA1"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha256
msgid "SHA256"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha512
msgid "SHA512"
msgstr ""

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr ""

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "Comunicarea cu API a eșuat."

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Cod tehnic al acestui procesator de plată"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Tranzacția nu este legată de un token."

#. module: payment_ogone
#: model_terms:ir.ui.view,arch_db:payment_ogone.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Stripe</strong>."
msgstr ""

#. module: payment_ogone
#: model_terms:payment.provider,auth_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been authorized."
msgstr "Plata dvs. a fost autorizată."

#. module: payment_ogone
#: model_terms:payment.provider,cancel_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been cancelled."
msgstr "Plata dvs. a fost anulată."

#. module: payment_ogone
#: model_terms:payment.provider,pending_msg:payment_ogone.payment_provider_ogone
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Plata dvs. a fost procesată cu succes, dar așteaptă aprobarea."

#. module: payment_ogone
#: model_terms:payment.provider,done_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been successfully processed."
msgstr "Plata dvs. a fost procesată cu succes."
