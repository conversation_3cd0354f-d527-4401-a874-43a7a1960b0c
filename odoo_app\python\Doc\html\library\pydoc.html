<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pydoc — Documentation generator and online help system" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pydoc.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pydoc.py The pydoc module automatically generates documentation from Python modules. The documentation can be presented as pages of text on the console, served to a web browser, or..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pydoc.py The pydoc module automatically generates documentation from Python modules. The documentation can be presented as pages of text on the console, served to a web browser, or..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pydoc — Documentation generator and online help system &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Python Development Mode" href="devmode.html" />
    <link rel="prev" title="typing — Support for type hints" href="typing.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pydoc.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="typing.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="devmode.html"
                          title="next chapter">Python Development Mode</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pydoc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="devmode.html" title="Python Development Mode"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="typing.html" title="typing — Support for type hints"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" accesskey="U">Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> — Documentation generator and online help system</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pydoc">
<span id="pydoc-documentation-generator-and-online-help-system"></span><h1><a class="reference internal" href="#module-pydoc" title="pydoc: Documentation generator and online help system."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code></a> — Documentation generator and online help system<a class="headerlink" href="#module-pydoc" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pydoc.py">Lib/pydoc.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> module automatically generates documentation from Python
modules.  The documentation can be presented as pages of text on the console,
served to a web browser, or saved to HTML files.</p>
<p>For modules, classes, functions and methods, the displayed documentation is
derived from the docstring (i.e. the <code class="xref py py-attr docutils literal notranslate"><span class="pre">__doc__</span></code> attribute) of the object,
and recursively of its documentable members.  If there is no docstring,
<code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> tries to obtain a description from the block of comment lines just
above the definition of the class, function or method in the source file, or at
the top of the module (see <a class="reference internal" href="inspect.html#inspect.getcomments" title="inspect.getcomments"><code class="xref py py-func docutils literal notranslate"><span class="pre">inspect.getcomments()</span></code></a>).</p>
<p>The built-in function <a class="reference internal" href="functions.html#help" title="help"><code class="xref py py-func docutils literal notranslate"><span class="pre">help()</span></code></a> invokes the online help system in the
interactive interpreter, which uses <code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> to generate its documentation
as text on the console.  The same text documentation can also be viewed from
outside the Python interpreter by running <strong class="program">pydoc</strong> as a script at the
operating system’s command prompt. For example, running</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pydoc</span> <span class="n">sys</span>
</pre></div>
</div>
<p>at a shell prompt will display documentation on the <a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> module, in a
style similar to the manual pages shown by the Unix <strong class="program">man</strong> command.  The
argument to <strong class="program">pydoc</strong> can be the name of a function, module, or package,
or a dotted reference to a class, method, or function within a module or module
in a package.  If the argument to <strong class="program">pydoc</strong> looks like a path (that is,
it contains the path separator for your operating system, such as a slash in
Unix), and refers to an existing Python source file, then documentation is
produced for that file.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In order to find objects and their documentation, <code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> imports the
module(s) to be documented.  Therefore, any code on module level will be
executed on that occasion.  Use an <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__':</span></code> guard to
only execute code when a file is invoked as a script and not just imported.</p>
</div>
<p>When printing output to the console, <strong class="program">pydoc</strong> attempts to paginate the
output for easier reading.  If the <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PAGER</span></code> environment variable is set,
<strong class="program">pydoc</strong> will use its value as a pagination program.</p>
<p>Specifying a <code class="docutils literal notranslate"><span class="pre">-w</span></code> flag before the argument will cause HTML documentation
to be written out to a file in the current directory, instead of displaying text
on the console.</p>
<p>Specifying a <code class="docutils literal notranslate"><span class="pre">-k</span></code> flag before the argument will search the synopsis
lines of all available modules for the keyword given as the argument, again in a
manner similar to the Unix <strong class="program">man</strong> command.  The synopsis line of a
module is the first line of its documentation string.</p>
<p>You can also use <strong class="program">pydoc</strong> to start an HTTP server on the local machine
that will serve documentation to visiting web browsers.  <strong class="program">python -m pydoc -p 1234</strong>
will start a HTTP server on port 1234, allowing you to browse the
documentation at <code class="docutils literal notranslate"><span class="pre">http://localhost:1234/</span></code> in your preferred web browser.
Specifying <code class="docutils literal notranslate"><span class="pre">0</span></code> as the port number will select an arbitrary unused port.</p>
<p><strong class="program">python -m pydoc -n &lt;hostname&gt;</strong> will start the server listening at the given
hostname.  By default the hostname is ‘localhost’ but if you want the server to
be reached from other machines, you may want to change the host name that the
server responds to.  During development this is especially useful if you want
to run pydoc from within a container.</p>
<p><strong class="program">python -m pydoc -b</strong> will start the server and additionally open a web
browser to a module index page.  Each served page has a navigation bar at the
top where you can <em>Get</em> help on an individual item, <em>Search</em> all modules with a
keyword in their synopsis line, and go to the <em>Module index</em>, <em>Topics</em> and
<em>Keywords</em> pages.</p>
<p>When <strong class="program">pydoc</strong> generates documentation, it uses the current environment
and path to locate modules.  Thus, invoking <strong class="program">pydoc spam</strong>
documents precisely the version of the module you would get if you started the
Python interpreter and typed <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">spam</span></code>.</p>
<p>Module docs for core modules are assumed to reside in
<code class="docutils literal notranslate"><span class="pre">https://docs.python.org/X.Y/library/</span></code> where <code class="docutils literal notranslate"><span class="pre">X</span></code> and <code class="docutils literal notranslate"><span class="pre">Y</span></code> are the
major and minor version numbers of the Python interpreter.  This can
be overridden by setting the <code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDOCS</span></code> environment variable
to a different URL or to a local directory containing the Library
Reference Manual pages.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <code class="docutils literal notranslate"><span class="pre">-b</span></code> option.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <code class="docutils literal notranslate"><span class="pre">-g</span></code> command line option was removed.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> now uses <a class="reference internal" href="inspect.html#inspect.signature" title="inspect.signature"><code class="xref py py-func docutils literal notranslate"><span class="pre">inspect.signature()</span></code></a> rather than
<a class="reference internal" href="inspect.html#inspect.getfullargspec" title="inspect.getfullargspec"><code class="xref py py-func docutils literal notranslate"><span class="pre">inspect.getfullargspec()</span></code></a> to extract signature information from
callables.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <code class="docutils literal notranslate"><span class="pre">-n</span></code> option.</p>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="typing.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">typing</span></code> — Support for type hints</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="devmode.html"
                          title="next chapter">Python Development Mode</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pydoc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="devmode.html" title="Python Development Mode"
             >next</a> |</li>
        <li class="right" >
          <a href="typing.html" title="typing — Support for type hints"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" >Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pydoc</span></code> — Documentation generator and online help system</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>