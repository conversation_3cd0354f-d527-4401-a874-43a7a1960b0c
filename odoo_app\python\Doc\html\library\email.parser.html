<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="email.parser: Parsing email messages" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/email.parser.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/email/parser.py Message object structures can be created in one of two ways: they can be created from whole cloth by creating an EmailMessage object, adding headers using the dicti..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/email/parser.py Message object structures can be created in one of two ways: they can be created from whole cloth by creating an EmailMessage object, adding headers using the dicti..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>email.parser: Parsing email messages &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="email.generator: Generating MIME documents" href="email.generator.html" />
    <link rel="prev" title="email.message: Representing an email message" href="email.message.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/email.parser.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code>: Parsing email messages</a><ul>
<li><a class="reference internal" href="#feedparser-api">FeedParser API</a></li>
<li><a class="reference internal" href="#parser-api">Parser API</a></li>
<li><a class="reference internal" href="#additional-notes">Additional notes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.message.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message</span></code>: Representing an email message</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.generator.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.generator</span></code>: Generating MIME documents</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.parser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.generator.html" title="email.generator: Generating MIME documents"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="email.message.html" title="email.message: Representing an email message"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" accesskey="U"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code>: Parsing email messages</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-email.parser">
<span id="email-parser-parsing-email-messages"></span><h1><a class="reference internal" href="#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code></a>: Parsing email messages<a class="headerlink" href="#module-email.parser" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/email/parser.py">Lib/email/parser.py</a></p>
<hr class="docutils" />
<p>Message object structures can be created in one of two ways: they can be
created from whole cloth by creating an <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a>
object, adding headers using the dictionary interface, and adding payload(s)
using <a class="reference internal" href="email.message.html#email.message.EmailMessage.set_content" title="email.message.EmailMessage.set_content"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_content()</span></code></a> and related methods, or
they can be created by parsing a serialized representation of the email
message.</p>
<p>The <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a> package provides a standard parser that understands most email
document structures, including MIME documents.  You can pass the parser a
bytes, string or file object, and the parser will return to you the root
<a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> instance of the object structure.  For
simple, non-MIME messages the payload of this root object will likely be a
string containing the text of the message.  For MIME messages, the root object
will return <code class="docutils literal notranslate"><span class="pre">True</span></code> from its <a class="reference internal" href="email.message.html#email.message.EmailMessage.is_multipart" title="email.message.EmailMessage.is_multipart"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_multipart()</span></code></a>
method, and the subparts can be accessed via the payload manipulation methods,
such as <a class="reference internal" href="email.message.html#email.message.EmailMessage.get_body" title="email.message.EmailMessage.get_body"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_body()</span></code></a>,
<a class="reference internal" href="email.message.html#email.message.EmailMessage.iter_parts" title="email.message.EmailMessage.iter_parts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iter_parts()</span></code></a>, and
<a class="reference internal" href="email.message.html#email.message.EmailMessage.walk" title="email.message.EmailMessage.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">walk()</span></code></a>.</p>
<p>There are actually two parser interfaces available for use, the <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a>
API and the incremental <a class="reference internal" href="#email.parser.FeedParser" title="email.parser.FeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">FeedParser</span></code></a> API.  The <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a> API is
most useful if you have the entire text of the message in memory, or if the
entire message lives in a file on the file system.  <a class="reference internal" href="#email.parser.FeedParser" title="email.parser.FeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">FeedParser</span></code></a> is more
appropriate when you are reading the message from a stream which might block
waiting for more input (such as reading an email message from a socket).  The
<a class="reference internal" href="#email.parser.FeedParser" title="email.parser.FeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">FeedParser</span></code></a> can consume and parse the message incrementally, and only
returns the root object when you close the parser.</p>
<p>Note that the parser can be extended in limited ways, and of course you can
implement your own parser completely from scratch.  All of the logic that
connects the <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a> package’s bundled parser and the
<a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> class is embodied in the <a class="reference internal" href="email.policy.html#email.policy.Policy" title="email.policy.Policy"><code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code></a>
class, so a custom parser can create message object trees any way it finds
necessary by implementing custom versions of the appropriate <code class="xref py py-class docutils literal notranslate"><span class="pre">Policy</span></code>
methods.</p>
<section id="feedparser-api">
<h2>FeedParser API<a class="headerlink" href="#feedparser-api" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a>, imported from the <code class="xref py py-mod docutils literal notranslate"><span class="pre">email.feedparser</span></code> module,
provides an API that is conducive to incremental parsing of email messages,
such as would be necessary when reading the text of an email message from a
source that can block (such as a socket).  The <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a> can of
course be used to parse an email message fully contained in a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like
object</span></a>, string, or file, but the <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a> API may be more
convenient for such use cases.  The semantics and results of the two parser
APIs are identical.</p>
<p>The <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a>’s API is simple; you create an instance, feed it a
bunch of bytes until there’s no more to feed it, then close the parser to
retrieve the root message object.  The <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a> is extremely
accurate when parsing standards-compliant messages, and it does a very good job
of parsing non-compliant messages, providing information about how a message
was deemed broken.  It will populate a message object’s
<a class="reference internal" href="email.message.html#email.message.EmailMessage.defects" title="email.message.EmailMessage.defects"><code class="xref py py-attr docutils literal notranslate"><span class="pre">defects</span></code></a> attribute with a list of any
problems it found in a message.  See the <a class="reference internal" href="email.errors.html#module-email.errors" title="email.errors: The exception classes used by the email package."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.errors</span></code></a> module for the
list of defects that it can find.</p>
<p>Here is the API for the <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a>:</p>
<dl class="py class">
<dt class="sig sig-object py" id="email.parser.BytesFeedParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">BytesFeedParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesFeedParser" title="Link to this definition">¶</a></dt>
<dd><p>Create a <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a> instance.  Optional <em>_factory</em> is a
no-argument callable; if not specified use the
<a class="reference internal" href="email.policy.html#email.policy.Policy.message_factory" title="email.policy.Policy.message_factory"><code class="xref py py-attr docutils literal notranslate"><span class="pre">message_factory</span></code></a> from the <em>policy</em>.  Call
<em>_factory</em> whenever a new message object is needed.</p>
<p>If <em>policy</em> is specified use the rules it specifies to update the
representation of the message.  If <em>policy</em> is not set, use the
<a class="reference internal" href="email.policy.html#email.policy.Compat32" title="email.policy.Compat32"><code class="xref py py-class docutils literal notranslate"><span class="pre">compat32</span></code></a> policy, which maintains backward
compatibility with the Python 3.2 version of the email package and provides
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">Message</span></code></a> as the default factory.  All other policies
provide <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> as the default <em>_factory</em>. For
more information on what else <em>policy</em> controls, see the
<a class="reference internal" href="email.policy.html#module-email.policy" title="email.policy: Controlling the parsing and generating of messages"><code class="xref py py-mod docutils literal notranslate"><span class="pre">policy</span></code></a> documentation.</p>
<p>Note: <strong>The policy keyword should always be specified</strong>; The default will
change to <a class="reference internal" href="email.policy.html#email.policy.default" title="email.policy.default"><code class="xref py py-data docutils literal notranslate"><span class="pre">email.policy.default</span></code></a> in a future version of Python.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>policy</em> keyword.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>_factory</em> defaults to the policy <code class="docutils literal notranslate"><span class="pre">message_factory</span></code>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="email.parser.BytesFeedParser.feed">
<span class="sig-name descname"><span class="pre">feed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesFeedParser.feed" title="Link to this definition">¶</a></dt>
<dd><p>Feed the parser some more data.  <em>data</em> should be a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like
object</span></a> containing one or more lines.  The lines can be partial and the
parser will stitch such partial lines together properly.  The lines can
have any of the three common line endings: carriage return, newline, or
carriage return and newline (they can even be mixed).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.parser.BytesFeedParser.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesFeedParser.close" title="Link to this definition">¶</a></dt>
<dd><p>Complete the parsing of all previously fed data and return the root
message object.  It is undefined what happens if <a class="reference internal" href="#email.parser.BytesFeedParser.feed" title="email.parser.BytesFeedParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">feed()</span></code></a> is called
after this method has been called.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="email.parser.FeedParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">FeedParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.FeedParser" title="Link to this definition">¶</a></dt>
<dd><p>Works like <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a> except that the input to the
<a class="reference internal" href="#email.parser.BytesFeedParser.feed" title="email.parser.BytesFeedParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">feed()</span></code></a> method must be a string.  This is of limited
utility, since the only way for such a message to be valid is for it to
contain only ASCII text or, if <code class="xref py py-attr docutils literal notranslate"><span class="pre">utf8</span></code> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>, no binary attachments.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added the <em>policy</em> keyword.</p>
</div>
</dd></dl>

</section>
<section id="parser-api">
<h2>Parser API<a class="headerlink" href="#parser-api" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a> class, imported from the <a class="reference internal" href="#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code></a> module,
provides an API that can be used to parse a message when the complete contents
of the message are available in a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> or file.  The
<a class="reference internal" href="#module-email.parser" title="email.parser: Parse flat text email messages to produce a message object structure."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code></a> module also provides <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a> for parsing strings,
and header-only parsers, <a class="reference internal" href="#email.parser.BytesHeaderParser" title="email.parser.BytesHeaderParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesHeaderParser</span></code></a> and
<a class="reference internal" href="#email.parser.HeaderParser" title="email.parser.HeaderParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HeaderParser</span></code></a>, which can be used if you’re only interested in the
headers of the message.  <a class="reference internal" href="#email.parser.BytesHeaderParser" title="email.parser.BytesHeaderParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesHeaderParser</span></code></a> and <a class="reference internal" href="#email.parser.HeaderParser" title="email.parser.HeaderParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HeaderParser</span></code></a>
can be much faster in these situations, since they do not attempt to parse the
message body, instead setting the payload to the raw body.</p>
<dl class="py class">
<dt class="sig sig-object py" id="email.parser.BytesParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">BytesParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesParser" title="Link to this definition">¶</a></dt>
<dd><p>Create a <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a> instance.  The <em>_class</em> and <em>policy</em>
arguments have the same meaning and semantics as the <em>_factory</em>
and <em>policy</em> arguments of <a class="reference internal" href="#email.parser.BytesFeedParser" title="email.parser.BytesFeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesFeedParser</span></code></a>.</p>
<p>Note: <strong>The policy keyword should always be specified</strong>; The default will
change to <a class="reference internal" href="email.policy.html#email.policy.default" title="email.policy.default"><code class="xref py py-data docutils literal notranslate"><span class="pre">email.policy.default</span></code></a> in a future version of Python.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument that was deprecated in 2.4.  Added the
<em>policy</em> keyword.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>_class</em> defaults to the policy <code class="docutils literal notranslate"><span class="pre">message_factory</span></code>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="email.parser.BytesParser.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headersonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesParser.parse" title="Link to this definition">¶</a></dt>
<dd><p>Read all the data from the binary file-like object <em>fp</em>, parse the
resulting bytes, and return the message object.  <em>fp</em> must support
both the <a class="reference internal" href="io.html#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> and the <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code>
methods.</p>
<p>The bytes contained in <em>fp</em> must be formatted as a block of <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5322.html"><strong>RFC 5322</strong></a>
(or, if <code class="xref py py-attr docutils literal notranslate"><span class="pre">utf8</span></code> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6532.html"><strong>RFC 6532</strong></a>)
style headers and header continuation lines, optionally preceded by an
envelope header.  The header block is terminated either by the end of the
data or by a blank line.  Following the header block is the body of the
message (which may contain MIME-encoded subparts, including subparts
with a <em class="mailheader">Content-Transfer-Encoding</em> of <code class="docutils literal notranslate"><span class="pre">8bit</span></code>).</p>
<p>Optional <em>headersonly</em> is a flag specifying whether to stop parsing after
reading the headers or not.  The default is <code class="docutils literal notranslate"><span class="pre">False</span></code>, meaning it parses
the entire contents of the file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.parser.BytesParser.parsebytes">
<span class="sig-name descname"><span class="pre">parsebytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytes</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headersonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesParser.parsebytes" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#email.parser.BytesParser.parse" title="email.parser.BytesParser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> method, except it takes a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like
object</span></a> instead of a file-like object.  Calling this method on a
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is equivalent to wrapping <em>bytes</em> in a
<a class="reference internal" href="io.html#io.BytesIO" title="io.BytesIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesIO</span></code></a> instance first and calling <a class="reference internal" href="#email.parser.BytesParser.parse" title="email.parser.BytesParser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a>.</p>
<p>Optional <em>headersonly</em> is as with the <a class="reference internal" href="#email.parser.BytesParser.parse" title="email.parser.BytesParser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> method.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="email.parser.BytesHeaderParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">BytesHeaderParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.BytesHeaderParser" title="Link to this definition">¶</a></dt>
<dd><p>Exactly like <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a>, except that <em>headersonly</em>
defaults to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="email.parser.Parser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">Parser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.Parser" title="Link to this definition">¶</a></dt>
<dd><p>This class is parallel to <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a>, but handles string input.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument.  Added the <em>policy</em> keyword.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>_class</em> defaults to the policy <code class="docutils literal notranslate"><span class="pre">message_factory</span></code>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="email.parser.Parser.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headersonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.Parser.parse" title="Link to this definition">¶</a></dt>
<dd><p>Read all the data from the text-mode file-like object <em>fp</em>, parse the
resulting text, and return the root message object.  <em>fp</em> must support
both the <a class="reference internal" href="io.html#io.TextIOBase.readline" title="io.TextIOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> and the
<a class="reference internal" href="io.html#io.TextIOBase.read" title="io.TextIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> methods on file-like objects.</p>
<p>Other than the text mode requirement, this method operates like
<a class="reference internal" href="#email.parser.BytesParser.parse" title="email.parser.BytesParser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BytesParser.parse()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="email.parser.Parser.parsestr">
<span class="sig-name descname"><span class="pre">parsestr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headersonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.Parser.parsestr" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the <a class="reference internal" href="#email.parser.Parser.parse" title="email.parser.Parser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> method, except it takes a string object
instead of a file-like object.  Calling this method on a string is
equivalent to wrapping <em>text</em> in a <a class="reference internal" href="io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">StringIO</span></code></a> instance first
and calling <a class="reference internal" href="#email.parser.Parser.parse" title="email.parser.Parser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a>.</p>
<p>Optional <em>headersonly</em> is as with the <a class="reference internal" href="#email.parser.Parser.parse" title="email.parser.Parser.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> method.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="email.parser.HeaderParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">email.parser.</span></span><span class="sig-name descname"><span class="pre">HeaderParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.parser.HeaderParser" title="Link to this definition">¶</a></dt>
<dd><p>Exactly like <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a>, except that <em>headersonly</em>
defaults to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</dd></dl>

<p>Since creating a message object structure from a string or a file object is such
a common task, four functions are provided as a convenience.  They are available
in the top-level <a class="reference internal" href="email.html#module-email" title="email: Package supporting the parsing, manipulating, and generating email messages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code></a> package namespace.</p>
<dl class="py function">
<dt class="sig sig-object py" id="email.message_from_bytes">
<span class="sig-prename descclassname"><span class="pre">email.</span></span><span class="sig-name descname"><span class="pre">message_from_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.message_from_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Return a message object structure from a <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a>.  This is
equivalent to <code class="docutils literal notranslate"><span class="pre">BytesParser().parsebytes(s)</span></code>.  Optional <em>_class</em> and
<em>policy</em> are interpreted as with the <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a> class
constructor.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument.  Added the <em>policy</em> keyword.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.message_from_binary_file">
<span class="sig-prename descclassname"><span class="pre">email.</span></span><span class="sig-name descname"><span class="pre">message_from_binary_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.message_from_binary_file" title="Link to this definition">¶</a></dt>
<dd><p>Return a message object structure tree from an open binary <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file
object</span></a>.  This is equivalent to <code class="docutils literal notranslate"><span class="pre">BytesParser().parse(fp)</span></code>.  <em>_class</em> and
<em>policy</em> are interpreted as with the <a class="reference internal" href="#email.parser.BytesParser" title="email.parser.BytesParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">BytesParser</span></code></a> class
constructor.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument.  Added the <em>policy</em> keyword.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.message_from_string">
<span class="sig-prename descclassname"><span class="pre">email.</span></span><span class="sig-name descname"><span class="pre">message_from_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.message_from_string" title="Link to this definition">¶</a></dt>
<dd><p>Return a message object structure from a string.  This is equivalent to
<code class="docutils literal notranslate"><span class="pre">Parser().parsestr(s)</span></code>.  <em>_class</em> and <em>policy</em> are interpreted as
with the <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a> class constructor.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument.  Added the <em>policy</em> keyword.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="email.message_from_file">
<span class="sig-prename descclassname"><span class="pre">email.</span></span><span class="sig-name descname"><span class="pre">message_from_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">policy.compat32</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#email.message_from_file" title="Link to this definition">¶</a></dt>
<dd><p>Return a message object structure tree from an open <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>.
This is equivalent to <code class="docutils literal notranslate"><span class="pre">Parser().parse(fp)</span></code>.  <em>_class</em> and <em>policy</em> are
interpreted as with the <a class="reference internal" href="#email.parser.Parser" title="email.parser.Parser"><code class="xref py py-class docutils literal notranslate"><span class="pre">Parser</span></code></a> class constructor.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed the <em>strict</em> argument.  Added the <em>policy</em> keyword.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>_class</em> defaults to the policy <code class="docutils literal notranslate"><span class="pre">message_factory</span></code>.</p>
</div>
</dd></dl>

<p>Here’s an example of how you might use <a class="reference internal" href="#email.message_from_bytes" title="email.message_from_bytes"><code class="xref py py-func docutils literal notranslate"><span class="pre">message_from_bytes()</span></code></a> at an
interactive Python prompt:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">email</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">msg</span> <span class="o">=</span> <span class="n">email</span><span class="o">.</span><span class="n">message_from_bytes</span><span class="p">(</span><span class="n">myBytes</span><span class="p">)</span>  
</pre></div>
</div>
</section>
<section id="additional-notes">
<h2>Additional notes<a class="headerlink" href="#additional-notes" title="Link to this heading">¶</a></h2>
<p>Here are some notes on the parsing semantics:</p>
<ul class="simple">
<li><p>Most non-<em class="mimetype">multipart</em> type messages are parsed as a single message
object with a string payload.  These objects will return <code class="docutils literal notranslate"><span class="pre">False</span></code> for
<a class="reference internal" href="email.message.html#email.message.EmailMessage.is_multipart" title="email.message.EmailMessage.is_multipart"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_multipart()</span></code></a>, and
<a class="reference internal" href="email.message.html#email.message.EmailMessage.iter_parts" title="email.message.EmailMessage.iter_parts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iter_parts()</span></code></a> will yield an empty list.</p></li>
<li><p>All <em class="mimetype">multipart</em> type messages will be parsed as a container message
object with a list of sub-message objects for their payload.  The outer
container message will return <code class="docutils literal notranslate"><span class="pre">True</span></code> for
<a class="reference internal" href="email.message.html#email.message.EmailMessage.is_multipart" title="email.message.EmailMessage.is_multipart"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_multipart()</span></code></a>, and
<a class="reference internal" href="email.message.html#email.message.EmailMessage.iter_parts" title="email.message.EmailMessage.iter_parts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iter_parts()</span></code></a> will yield a list of subparts.</p></li>
<li><p>Most messages with a content type of <em class="mimetype">message/*</em> (such as
<em class="mimetype">message/delivery-status</em> and <em class="mimetype">message/rfc822</em>) will also
be parsed as container object containing a list payload of length 1.  Their
<a class="reference internal" href="email.message.html#email.message.EmailMessage.is_multipart" title="email.message.EmailMessage.is_multipart"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_multipart()</span></code></a> method will return <code class="docutils literal notranslate"><span class="pre">True</span></code>.
The single element yielded by <a class="reference internal" href="email.message.html#email.message.EmailMessage.iter_parts" title="email.message.EmailMessage.iter_parts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">iter_parts()</span></code></a>
will be a sub-message object.</p></li>
<li><p>Some non-standards-compliant messages may not be internally consistent about
their <em class="mimetype">multipart</em>-edness.  Such messages may have a
<em class="mailheader">Content-Type</em> header of type <em class="mimetype">multipart</em>, but their
<a class="reference internal" href="email.message.html#email.message.EmailMessage.is_multipart" title="email.message.EmailMessage.is_multipart"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_multipart()</span></code></a> method may return <code class="docutils literal notranslate"><span class="pre">False</span></code>.
If such messages were parsed with the <a class="reference internal" href="#email.parser.FeedParser" title="email.parser.FeedParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">FeedParser</span></code></a>,
they will have an instance of the
<code class="xref py py-class docutils literal notranslate"><span class="pre">MultipartInvariantViolationDefect</span></code> class in their
<em>defects</em> attribute list.  See <a class="reference internal" href="email.errors.html#module-email.errors" title="email.errors: The exception classes used by the email package."><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.errors</span></code></a> for details.</p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code>: Parsing email messages</a><ul>
<li><a class="reference internal" href="#feedparser-api">FeedParser API</a></li>
<li><a class="reference internal" href="#parser-api">Parser API</a></li>
<li><a class="reference internal" href="#additional-notes">Additional notes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.message.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.message</span></code>: Representing an email message</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="email.generator.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.generator</span></code>: Generating MIME documents</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/email.parser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="email.generator.html" title="email.generator: Generating MIME documents"
             >next</a> |</li>
        <li class="right" >
          <a href="email.message.html" title="email.message: Representing an email message"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
          <li class="nav-item nav-item-3"><a href="email.html" ><code class="xref py py-mod docutils literal notranslate"><span class="pre">email</span></code> — An email and MIME handling package</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.parser</span></code>: Parsing email messages</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>