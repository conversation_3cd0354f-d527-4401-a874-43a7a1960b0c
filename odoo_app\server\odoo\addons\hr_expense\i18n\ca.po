# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON><PERSON>12, 2023
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> Consulting <<EMAIL>>, 2023
# 7b9408628f00af852f513eb4f12c005b_f9c6891, 2023
# <PERSON><PERSON>, 2023
# martio<PERSON>o hola, 2023
# eriiikgt, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON> i Bochaca <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# jabiri7, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# marc<PERSON>, 2024
# Arnau Ros, 2024
# <PERSON>gaux, 2024
# Noemi Pla, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-<PERSON>-To: \n"
"POT-Creation-Date: 2025-05-07 20:35+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$100.00"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$120.00"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$500.00"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "$600.00"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "%(date_from)s - %(date_to)s"
msgstr "%(date_from)s - %(date_to)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""
"%(user)sconfirma que aquesta despesa no és un duplicat d'una despesa "
"similar."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "%s: It is not from your department"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "%s: It is your own expense"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "%s: Your are not a Manager or HR Officer"
msgstr ""

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'Despeses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "2023-08-11"
msgstr "2023-08-11"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<span class=\"d-inline-block\">\n"
"                                        <i class=\"text-muted\">Use this reference as a subject prefix when submitting by email.</i>\n"
"                                    </span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_payment_form_inherit_expense
msgid "<span class=\"o_stat_text\">Expense Report</span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "<span class=\"o_stat_text\">Journal Entry</span>"
msgstr "<span class=\"o_stat_text\">Assentament comptable</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Date:</span>"
msgstr "<span>Data:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Employee:</span>"
msgstr "<span>Empleat:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Manager:</span>"
msgstr "<span>Gerent:</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<span>Paid by:</span>"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Compte"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Accounting"
msgstr "Comptabilitat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr "Data comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Acció necessària"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitat d'excepció de decoració"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipus d'activitats"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "Pseudònim"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "Aprovador de tot"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Totes les notes de despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
msgid "All Reports"
msgstr "Tots els informes"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "All expenses in an expense report must have the same \"paid by\" criteria."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "All payment methods allowed"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "Import del deute"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/document_upload.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr "Un error va ocórrer durant la càrrega"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "un compte de despeses és esperat"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr "Ja existeix una despesa de la mateixa categoria, import i data."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr ""
"Un informe de despeses ha de contenir només línies de la mateixa empresa."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_account
msgid "Analytic Account"
msgstr "Compte analític"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribució analítica"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_distribution_search
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Cerca de Distribució Analítica"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Aplicabilitats del pla analític"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_precision
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__analytic_precision
msgid "Analytic Precision"
msgstr "Precisió analítica"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Apple App Store"
msgstr "App Store d'Apple"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "Data d'aprovació"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_state
msgid "Approval State"
msgstr ""

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Approve"
msgstr "Aprova"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Approve Report"
msgstr "aprovar informe"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__approve
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Aprovat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "Aprovat per"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "Aprovat el"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "Arxivat"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#, python-format
msgid "Attach Receipt"
msgstr "Adjunta rebut"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach a receipt - usually an image or a PDF file."
msgstr "Adjunta un rebut - normalment una imatge o un fitxer PDF."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_attachment
msgid "Attachment"
msgstr "Adjunt"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet_img
msgid "Attachment Name"
msgstr "Nom del document adjunt"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_ids
msgid "Attachments"
msgstr "Adjunts"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_ids
msgid "Attachments of expenses"
msgstr "Adjunts de despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleat bàsic"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid ""
"Both price-included and price-excluded taxes will behave as price-included "
"taxes for expenses."
msgstr ""
"Les taxes incloses en el preu i les taxes no incloses en el preu es "
"comportaran com a taxes incloses en el preu per a les notes de despeses."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Business Trip"
msgstr "Viatge de negocis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "Pot aprovar"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "pot reinicialitzar"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr "Pot ser una despesa"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Cancel"
msgstr "Cancel·la"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__cannot_approve_reason
msgid "Cannot Approve Reason"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__product_ids
msgid "Categories"
msgstr "Categories"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr "Categoria"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "Categoria:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "Categoria: no trobada"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_checksum
msgid "Checksum/SHA1"
msgstr "Suma verificació/SHA1"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_communication_product_template
msgid "Communication"
msgstr "Comunicació"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "Empresa"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Configuració"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversió entre les unitats de mesura només és possible si pertanyen a la"
" mateixa categoria. La conversió es basarà en les ràtios establertes."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_kanban_view
msgid "Cost:"
msgstr "Cost:"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#, python-format
msgid "Create Report"
msgstr "Crear informe"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "Crear un nou informe de despeses."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Create a report to submit one or more expenses to your manager."
msgstr "Creeu un informe per enviar una o més despeses al vostre gestor."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "Creeu despeses a partir dels correus electrònics entrants"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "Crea noves despeses per obtenir estadístiques."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__create_date
msgid "Created on"
msgstr "Creat el"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Credit Card"
msgstr "Targeta de crèdit"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_rate
msgid "Currency Rate"
msgstr "Taxa monetària"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "Data"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "Benvolgut/da"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Nom predeterminat dels alias per els despeses"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default Category"
msgstr "Categoria per defecte"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_product_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_product_id
msgid "Default Expense Category"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_journal_id
msgid "Default Expense Journal"
msgstr "Diari de despeses predeterminat"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default accounting journal for expenses paid by employees."
msgstr "Diari comptable per defecte per a les despeses pagades pels empleats."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Default expense categories for uploaded expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "Departament"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__name
msgid "Description"
msgstr "Descripció"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Did you try the mobile app?"
msgstr "Heu provat l'aplicació del mòbil?"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr "Digitalitza els teus rebuts amb OCR i Intel·ligència Artificial"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Compte analític de distribució"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"No guardeu més els bitllets de despeses a les butxaques. Només has de fer "
"una foto del teu rebut i deixa que Odoo el digitalitzi. L'OCR i la "
"Intel·ligència Artificial ompliran les dades automàticament."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_move_form_inherit_expense
msgid ""
"Do you really want to invoice your own company? Remove the \"Company Name\" "
"from the partner to fix the configuration. Cancel this invoice and start "
"again."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domini "

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "Fet"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/mixins/qrcode.js:0
#, python-format
msgid "Download our App"
msgstr "Descarregueu l'aplicació"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_reset
msgid "Draft"
msgstr "Esborrany"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Drag and drop files to create expenses"
msgstr "Arrossegueu i deixeu anar fitxers per a crear despeses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr "Duplicar despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "Empleat"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "Empleat (a reembolsar)"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Employee Expense Journal"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "Despeses dels empleats"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Enable users to choose default category for automatically generated "
"expenses."
msgstr ""

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Enter a name then choose a category and configure the amount of your "
"expense."
msgstr ""
"Introduïu un nom i trieu una categoria i configureu la quantitat de la "
"despesa."

#. module: hr_expense
#: model:account.journal,name:hr_expense.hr_expense_account_journal
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model:ir.model.fields.selection,name:hr_expense.selection__account_analytic_applicability__business_domain__expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Despesa"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "Aprovació de despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "Duplicat d'aprovació de despeses"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "Aprovador de despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Categories"
msgstr "Categories de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "Data de la despesa"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "Digitalització de despeses (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr " Diari de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Línies de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr "Les línies de despeses són editables per l'usuari actual"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "Gestor de despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "Assistent del motiu de la despesa"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "Informe de despeses"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_expense_sheet_img
msgid "Expense Report Image"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Resum de l'informe de despeses"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Informes de despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Anàlisi dels informes de despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Informes de despeses per aprovar"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_move__expense_sheet_id
#: model:ir.model.fields,field_description:hr_expense.field_account_payment__expense_sheet_id
msgid "Expense Sheet"
msgstr "Full de despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "Divisió de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__expense_split_line_ids
msgid "Expense Split Line"
msgstr "Línia dividida de despeses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_split_wizard
msgid "Expense Split Wizard"
msgstr "Auxiliar de desglossament de despeses"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "Validació de despeses Duplicat"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense categories can be reinvoiced to your customers."
msgstr ""
"Les categories de despeses es poden tornar a facturar als vostres clients."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Expense entry created from: %s"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Motiu de denegació de despeses"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Informe de despeses aprovat"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Informe de despeses pagats"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Informe de despeses rebutjat"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_reset
msgid "Expense report reset to Draft"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"Els informes de despeses reagrupen totes les despeses realitzades durant un "
"esdeveniment concret."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expense split"
msgstr "Divisió de despeses"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Expenses"
msgstr "Despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Anàlisi de despeses"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Informe de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Informes de despeses per aprovar"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "Despeses per data"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Expenses from which the report has been submitted to the approver and is "
"waiting for approval."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "Despeses del membre del teu equip"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses paid by employee that are approved but not paid yet."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses that need to be submitted to the approver."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr "Despeses a tramitar"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_base__filter_for_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__filter_for_expense
msgid "Filter For Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Flight Ticket"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "Antics empleats"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "Activitats futures"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Informació general"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generate Expenses"
msgstr "Genera les despeses"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_gift_product_template
msgid "Gifts"
msgstr "Regals"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_gift_product_template
msgid "Gifts to customers or vendors"
msgstr "Regals per a clients o proveïdors"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr ""
"Handle lines with different currencies\n"
" "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_travel_accommodation_product_template
msgid "Hotel, plane ticket, taxi, etc."
msgstr "Hotel, bitllet d'avió, taxi, etc."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_alias_domain_id
msgid "Hr Expense Alias Domain"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "En procés de pagament"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
msgid "Included taxes"
msgstr "Impostos inclosos"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "Correus d'entrada"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Internal Note"
msgstr "Nota interna"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
msgid "Internal Notes"
msgstr "Notes internes"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr "Adjunts no vàlids!"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "És editable per l'usuari actual"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "És seguidor"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_multiple_currency
msgid "Is currency_id different from the company_currency_id"
msgstr "És currency_id diferent de company_currency_id"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_cost
msgid "Is product with non zero cost selected"
msgstr ""
"Is product with non zero cost selected\n"
" "

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "It all begins here - let's go!"
msgstr "Tot comença aquí, anem!"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_journal_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "Diari"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_ids
msgid "Journal Entries"
msgstr "Assentaments"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_entry_delete
msgid "Journal Entry Deleted"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Apunt comptable"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Journal entries"
msgstr "Assentaments"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_entry_delete
msgid "Journal entry deleted"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_currency_rate
msgid "Label Currency Rate"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__hr_expense_use_mailgateway
msgid "Let your employees record expenses by email"
msgstr ""
"Deixa que els teus empleats registren les despeses per correu electrònic"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Let's check out where you can manage all your employees expenses"
msgstr ""
"Anem a comprovar on podeu gestionar totes les despeses dels vostres empleats"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Let's go back to your expenses."
msgstr "Tornem a les vostres despeses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Lunch with customer $12.32"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "Gestor"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Managers can approve the report here, then an accountant can post the "
"accounting entries."
msgstr ""
"Els gestors poden aprovar l'informe aquí, i després un comptable pot "
"publicar les entrades comptables."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can inspect all expenses from here."
msgstr "Els gerents poden inspeccionar totes les despeses des d'aquí."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_meal_product_template
msgid "Meals"
msgstr "Meals"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_mileage_product_template
msgid "Mileage"
msgstr "Kilometratge"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Mitchell Admin"
msgstr "Mitchell Admin"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "Les meves despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "Els meus informes"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "El meu equip"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Nom"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report, paid by %(paid_by)s"
msgstr "Nou informe de despesa, pagat per %(paid_by)s"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Reports"
msgstr "Informes de despeses nous"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr "No s'ha proporcionat cap fitxer adjunt"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "Encara no hi han dades!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense categories found. Let's create one!"
msgstr "No s'han trobat categories de despeses. Creem-ne un!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "No s'ha trobat cap informe de despeses. Creem-ne un!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "No s'han trobat informes de despeses. Creem-ne un!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "No work contact found for the employee %s, please configure one."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Not Refused"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Notes..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__nb_attachment
msgid "Number of Attachments"
msgstr "Nombre d'adjunts"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_expense
msgid "Number of Expenses"
msgstr "Nombre de despeses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__nb_account_move
msgid "Number of Journal Entries"
msgstr "Nombre d'assentaments"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/expense_dashboard.xml:0
#, python-format
msgid "Numbers computed from your personal expenses."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""
"Un cop hàgiu creat la vostra despesa, envieu-la al vostre gestor que la "
"validarà."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense Report</b> is ready, you can submit it to your manager "
"and wait for approval."
msgstr ""
"Una vegada que el vostre <b>Informe de Despeses</b> estigui llest, podeu "
"enviar-lo al vostre gestor i esperar a la seva aprovació."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"Només els oficials de recursos humans o l'empleat en qüestió poden restablir"
" a l'esborrany."

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid "Or"
msgstr "Or"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Or send your receipts at"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
msgid "Original Amount"
msgstr "Import original"

#. module: hr_expense
#: model:product.template,name:hr_expense.product_product_no_cost_product_template
msgid "Others"
msgstr "Altres"

#. module: hr_expense
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "Pagat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "Pagat per"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "Parcial"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "Payment Method"
msgstr "Forma de pagament"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "Estat de pagament"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
#, python-format
msgid "Payment created for: %s"
msgstr "Pagament creat per: %s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment method allowed for expenses paid by company."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Payment methods"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_company__company_expense_allowed_payment_method_line_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__company_expense_allowed_payment_method_line_ids
msgid "Payment methods available for expenses paid by company"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_payment
msgid "Payments"
msgstr "Pagaments"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_communication_product_template
msgid "Phone bills, postage, etc."
msgstr "Factures telefòniques, correus electrònics, etc."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid ""
"Please specify an expense journal in order to generate accounting entries."
msgstr ""

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Post Entries"
msgstr "Publicar entrades"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Validar publicacions"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "Publicat"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "Impulsat per"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "Preu:"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_id
msgid "Product"
msgstr "Producte"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_description
msgid "Product Description"
msgstr "Descripció del producte"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Product Has Cost"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Nom producte"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_product
msgid "Product Variant"
msgstr "Variant de producte"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "Variants de producte"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleat públic"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Quantity"
msgstr "Quantitat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__rating_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__rating_ids
msgid "Ratings"
msgstr "Valoracions"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Ready? You can save it manually or discard modifications from here. You "
"don't <em>need to save</em> - Odoo will save eveyrthing for you when you "
"navigate."
msgstr ""
"Preparats? Podeu desar-lo manualment o descartar les modificacions des "
"d'aquí. No cal que <em>deseu</em> - l'Odoo us desarà l'Eveyrthing quan "
"navegueu."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Raó"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Motiu per rebutjar la despesa"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason:"
msgstr "Raó:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Rebutja"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Rebutja la despesa"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__cancel
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "Rebutjada"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "Despeses denegades"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Register Payment"
msgstr "Registrar pagament"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "Reemborsar les despeses a la nòmina"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr "Reemborsar les despeses en nòmines"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "Reemborsament en el full de pagament"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_ir_actions_report
msgid "Report Action"
msgstr "Acció d'informe"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_currency_id
msgid "Report Company Currency"
msgstr "Informe de la moneda de l'empresa"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Informes"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Restablir com a esborrany"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: hr_expense
#: model_terms:product.template,description:hr_expense.expense_product_meal_product_template
msgid "Restaurants, business lunches, etc."
msgstr "Restaurants, dinars de negocis, etc."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de lliurament SMS"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_categories_tree_view
msgid "Sales Price"
msgstr "Preus de venda"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Scan"
msgstr "Escanejar"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/components/qrcode_action.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr "Escaneja aquest codi QR per obtenir l'aplicació Odoo:"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Seleccioneu l'usuari responsable d'aprovar \"Despeses\" d'aquest empleat.\n"
"Si està buit, l'aprovació la fa un administrador o aprovador (determinat a la configuració/usuaris)."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__selectable_payment_method_line_ids
msgid "Selectable Payment Method Line"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"Envieu un correu electrònic a aquest àlies de correu electrònic amb el rebut"
" adjunt per crear una despesa amb un sol clic. Si la primera paraula de "
"l'assumpte del correu conté la referència interna de la categoria o el nom "
"de la categoria, s'establirà automàticament la categoria corresponent. "
"Escriviu l'import de la despesa a l'assumpte del correu per definir-lo també"
" a la despesa."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "Envieu factures a OCR per generar despeses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Configuració"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your alias domain"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__sheet_ids
msgid "Sheet"
msgstr "Full"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_bank_statement_line__show_commercial_partner_warning
#: model:ir.model.fields,field_description:hr_expense.field_account_move__show_commercial_partner_warning
#: model:ir.model.fields,field_description:hr_expense.field_account_payment__show_commercial_partner_warning
msgid "Show Commercial Partner Warning"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Show missing work email employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""
"Fes fotos dels teus rebuts i deixa que Odoo<br> crear despeses "
"automàticament per a vostè."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "Especifiqueu si el producte es pot seleccionar en una despesa."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Split Expense"
msgstr "Divideix la despesa"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/wizard/hr_expense_split_wizard.py:0
#, python-format
msgid "Split Expenses"
msgstr "Despeses desglossades"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "Split Possible"
msgstr "Divideix possible"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__standard_price_update_warning
msgid "Standard Price Update Warning"
msgstr "Avís d'actualització de preu estàndard"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "Estat"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Sobrepassat: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Submit"
msgstr "Publicar"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#, python-format
msgid "Submit to Manager"
msgstr "Enviar al responsable"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__submitted
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__approval_state__submit
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "Enviat"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal"
msgstr "Subtotal"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Subtotal In Currency"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Subtotal in currency"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_ids
msgid "Tax"
msgstr "Impost"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Tax 15%"
msgstr "Impost 15%"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount"
msgstr "Quantitat d'impostos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__tax_amount_currency
msgid "Tax amount in Currency"
msgstr "Import de l'impost a la moneda"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount
msgid "Tax amount in company currency"
msgstr "Import fiscal en moneda de l'empresa"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_amount_currency
msgid "Tax amount in currency"
msgstr "Import fiscal en moneda"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_tax_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__tax_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_split
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Taxes"
msgstr "Impostos"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "Supervisor d'equip"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_res_company__expense_journal_id
#: model:ir.model.fields,help:hr_expense.field_res_config_settings__expense_journal_id
msgid ""
"The company's default journal used when an employee expense is created."
msgstr ""
"El diari per defecte de l'empresa utilitzat quan es crea una despesa "
"d'empleat."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr ""
"L'usuari actual no té cap empleat relacionat. Si us plau, creeu-ne un."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"La primera paraula de l'assumpte del correu electrònic no corresponia a cap "
"codi de categoria. Haureu d'establir la categoria manualment a la despesa."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"Les següents despeses aprovades tenen empleat, import i categoria similars a"
" algunes despeses d'aquest informe. Verifiqueu que aquest informe no conté "
"duplicats."

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "El diari s'ha de configurar en funció de la despesa publicada"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__employee_journal_id
msgid "The journal used when the expense is paid by employee."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__payment_method_line_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""
"La forma de pagament utilitzada quan la despesa és pagada per l'empresa."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "The status of all your current expenses is visible from here."
msgstr "L'estat de totes les despeses actuals és visible des d'aquí."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__split_possible
msgid "The sum of after split shut remain the same"
msgstr ""
"La suma de després de la separació d'apagades continua sent la mateixa"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid ""
"The work email of some employees is missing. Please add it on the employee "
"form"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/product_product.py:0
#, python-format
msgid ""
"There are unsubmitted expenses linked to this category. Updating the "
"category cost will change expense amounts. Make sure it is what you want to "
"do."
msgstr ""
"Hi ha despeses pendents d'enviar vinculades en aquesta categoria. "
"Actualitzar el cost de la categoria canviarà les quantitats de la despesa. "
"Assegura't que és això el que vols fer."

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "There you go - expense management in a nutshell!"
msgstr "Aquí va la gestió de les despeses en poques paraules!"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"This note will be shown to users when they select this expense product."
msgstr ""
"Aquesta nota es mostrarà als usuaris quan seleccionin aquest producte de "
"despeses."

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "Consell: feu fotos dels vostres rebuts amb l'aplicació remota"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "Reportar"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "To Submit"
msgstr "Per publicar"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Total"
msgstr "Total"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Import total"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_currency
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__total_amount_currency
msgid "Total In Currency"
msgstr "Total de monedas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Taxes"
msgstr "Total Impostos "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount_currency
msgid "Total Untaxed Amount In Currency"
msgstr "Import total no gravat en moneda"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount of the original Expense that we are splitting"
msgstr "Import total de la despesa original que estem dividint"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split_wizard__total_amount_currency_original
msgid "Total amount original"
msgstr "Import total original"

#. module: hr_expense
#: model:product.template,name:hr_expense.expense_product_travel_accommodation_product_template
msgid "Travel & Accommodation"
msgstr "Accommodation de viatges"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__price_unit
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Preu un."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Unitat de mesura"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__untaxed_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Untaxed Amount"
msgstr "Base imposable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "Categoria UoM"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/views/kanban.xml:0
#: code:addons/hr_expense/static/src/views/list.xml:0
#, python-format
msgid "Upload"
msgstr "Pujar"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Use the breadcrumbs to go back to the list of expenses."
msgstr "Utilitzeu les molles de pa per tornar a la llista de despeses."

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "User"
msgstr "Usuari"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "Validació de despeses duplicades"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Veure fitxers adjunts"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "Veure Despesa"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Visualitza l'informe"

#. module: hr_expense
#. odoo-javascript
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Wasting time recording your receipts? Let’s try a better way."
msgstr ""
"Perds temps registrant els teus rebuts? Anem a provar una millor manera."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Missatges del lloc web"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicacions del lloc web"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user"
"                                         using this product won't be able to"
" change the amount of the expense,                                         "
"only the quantity. Use a cost different than 0 for expense categories funded"
" by                                         the company at fixed cost like "
"allowances for mileage, per diem, accommodation"
"                                         or meal."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_tax
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__product_has_tax
msgid "Whether tax is defined on a selected product"
msgstr "Si els impostos es defineixen en un producte seleccionat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_split__wizard_id
msgid "Wizard"
msgstr "Assistent"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr "No teniu autorització per modificar aquest informe de despeses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr "No podeu crear un informe sense categoria."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr "Ara podeu enviar-lo al gestor des del següent enllaç."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""
"Només podeu generar un registre comptable per a les despeses aprovade(s)."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr "No podeu afegir despeses d'un altre empleat."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid ""
"You cannot approve:\n"
" %s"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "You cannot cancel an expense sheet linked to a journal entry"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr "No podeu suprimir una despesa publicada o aprovada."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "No podeu suprimir una despesa publicada o pagada."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/analytic.py:0
#, python-format
msgid "You cannot delete an analytic account that is used in an expense."
msgstr "No podeu eliminar un compte analític utilitzat a una despesa."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid ""
"You cannot delete only some entries linked to an expense report. All entries"
" must be deleted at the same time."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
#, python-format
msgid ""
"You cannot delete only some payments linked to an expense report. All "
"payments must be deleted at the same time."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/account_payment.py:0
#, python-format
msgid ""
"You cannot do this modification since the payment is linked to an expense "
"report."
msgstr ""
"No podeu fer aquesta modificació perquè el pagament està vinculat a un "
"informe de despeses."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid ""
"You cannot refuse:\n"
" %s"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different companies in the same report."
msgstr ""
"No pots incloure despeses de diferents companyies en el mateix informe."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""
"No podeu declarar despeses de diferents empleats en el mateix informe."

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report the expenses without amount!"
msgstr "No podeu informar de les despeses sense import!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr "No podeu informar dues vegades a la mateixa línia!"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense_sheet.py:0
#, python-format
msgid ""
"You don't have the rights to bypass the validation process of this expense "
"report."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You don't have the rights to bypass the validation process of this expense."
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You have no expense to report"
msgstr "No teniu cap despesa per informar"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You need to add a manual payment method on the journal (%s)"
msgstr "Heu d'afegir un mètode de pagament manual al diari (%s)"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"Heu de tenir almenys una categoria que es pugui gastar a la vostra base de "
"dades per continuar!"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "La vostra despesa s'ha registrat correctament."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "Per exemple: dinar"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "p.e. Dinar amb el client"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "p.e. Viatge a NY"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "e.g. domain.com"
msgstr "e.g. domini.com"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "ha estat denegada"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr "per ser reemborsat"

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to submit"
msgstr ""

#. module: hr_expense
#. odoo-python
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr "en validació"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr "utilitzeu OCR per omplir les dades d'una imatge de la factura"
