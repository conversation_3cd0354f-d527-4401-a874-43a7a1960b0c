# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__code
msgid "Code"
msgstr "程式碼"

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Invalid payment status."
msgstr "無效付款狀態。"

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_provider__code__payulatam
#: model:payment.provider,name:payment_payulatam.payment_provider_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_api_key
msgid "PayU Latam API Key"
msgstr "PayU Latam API 密鑰"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr "PayU Latam 帳戶識別碼"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr "PayU Latam 商戶識別碼"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "The ID solely used to identify the account with PayULatam"
msgstr "只用於向 PayULatam 識別該帳戶的識別碼"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_account_id
msgid ""
"The ID solely used to identify the country-dependent shop with PayULatam"
msgstr "只用於向 PayULatam 識別相關國家/地區商店的識別碼"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"

#. module: payment_payulatam
#: model_terms:ir.ui.view,arch_db:payment_payulatam.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Mercado Pago</strong>."
msgstr ""
"此服務商已被棄用。\n"
"                    請考慮將它設為停用，並轉用 <strong>Mercado Pago</strong>。"

#. module: payment_payulatam
#: model_terms:payment.provider,auth_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: payment_payulatam
#: model_terms:payment.provider,cancel_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: payment_payulatam
#: model_terms:payment.provider,pending_msg:payment_payulatam.payment_provider_payulatam
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "您的付款已成功處理，但正在等待批准。"

#. module: payment_payulatam
#: model_terms:payment.provider,done_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been successfully processed."
msgstr "你的付款已成功處理。"
