# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rasar<PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_userid
msgid "API User ID"
msgstr "ไอดีผู้ใช้ API"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_password
msgid "API User Password"
msgstr "รหัสผู้ใช้ API"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__code
msgid "Code"
msgstr "โค้ด"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "ไม่สามารถสร้างการเชื่อมต่อกับ API ได้"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_hash_function
msgid "Hash function"
msgstr "ฟังก์ชันแฮช"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "ไม่พบธุรกรรมที่ตรงกับการอ้างอิง %s"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__code__ogone
#: model:payment.provider,name:payment_ogone.payment_provider_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_provider
msgid "Payment Provider"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "ธุรกรรมสำหรับการชำระเงิน"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "ได้รับข้อมูลที่มีสถานะการชำระเงินไม่ถูกต้อง: %s"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHA รหัสขาเข้า"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHA รหัสขาออก"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha1
msgid "SHA1"
msgstr "SHA1"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha256
msgid "SHA256"
msgstr "SHA256"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha512
msgid "SHA512"
msgstr "SHA512"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr ""
"การจัดเก็บรายละเอียดการชำระเงินของคุณเป็นสิ่งจำเป็นสำหรับการใช้งานในอนาคต"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr "ไอดีใช้เพื่อระบุผู้ใช้ API ด้วย Ogone เท่านั้น"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr "ไอดีใช้เพื่อระบุบัญชีกับ Ogone เท่านั้น"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "การสื่อสารกับ API ล้มเหลว"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr "การชำระเงินถูกปฏิเสธ: %s"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "รหัสทางเทคนิคของผู้ให้บริการชำระเงินรายนี้"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "ธุรกรรมไม่ได้เชื่อมโยงกับโทเค็น"

#. module: payment_ogone
#: model_terms:ir.ui.view,arch_db:payment_ogone.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Stripe</strong>."
msgstr ""
"ผู้ให้บริการรายนี้เลิกใช้แล้ว\n"
"                    ให้พิจารณาปิดการใช้งานและเปลี่ยนไปใช้ <strong>Stripe</strong>"

#. module: payment_ogone
#: model_terms:payment.provider,auth_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been authorized."
msgstr "การชำระเงินของคุณได้รับการอนุมัติแล้ว"

#. module: payment_ogone
#: model_terms:payment.provider,cancel_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been cancelled."
msgstr "การชำระเงินของคุณถูกยกเลิก"

#. module: payment_ogone
#: model_terms:payment.provider,pending_msg:payment_ogone.payment_provider_ogone
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว แต่กำลังรอการอนุมัติ"

#. module: payment_ogone
#: model_terms:payment.provider,done_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been successfully processed."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว"
