# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_sms
# 
# Translators:
# <PERSON> <and<PERSON>.<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_project_stage__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the project reaches this stage."
msgstr ""
"Om inställt kommer ett SMS-textmeddelande automatiskt att skickas till "
"kunden när projektet når denna etapp."

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_task_type__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the task reaches this stage."
msgstr ""
"Om inställt kommer ett SMS-textmeddelande automatiskt att skickas till "
"kunden när uppgiften når denna etapp."

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project
msgid "Project"
msgstr "Projekt"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project_stage
msgid "Project Stage"
msgstr "Projeketapp"

#. module: project_sms
#: model:ir.model.fields,field_description:project_sms.field_project_project_stage__sms_template_id
#: model:ir.model.fields,field_description:project_sms.field_project_task_type__sms_template_id
msgid "SMS Template"
msgstr "SMS-mall"

#. module: project_sms
#: model:ir.actions.act_window,name:project_sms.project_project_act_window_sms_composer
#: model:ir.actions.act_window,name:project_sms.project_task_act_window_sms_composer
msgid "Send SMS Text Message"
msgstr "Skicka SMS"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task
msgid "Task"
msgstr "Aktivitet"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task_type
msgid "Task Stage"
msgstr "Aktivitetsetapper"
