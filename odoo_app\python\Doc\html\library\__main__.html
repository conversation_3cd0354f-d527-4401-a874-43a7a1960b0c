<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="__main__ — Top-level code environment" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/__main__.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="In Python, the special name__main__ is used for two important constructs: the name of the top-level environment of the program, which can be checked using the__name__ == '__main__' expression; and,..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="In Python, the special name__main__ is used for two important constructs: the name of the top-level environment of the program, which can be checked using the__name__ == '__main__' expression; and,..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>__main__ — Top-level code environment &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="warnings — Warning control" href="warnings.html" />
    <link rel="prev" title="builtins — Built-in objects" href="builtins.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/__main__.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a><ul>
<li><a class="reference internal" href="#name-main"><code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code></a><ul>
<li><a class="reference internal" href="#what-is-the-top-level-code-environment">What is the “top-level code environment”?</a></li>
<li><a class="reference internal" href="#idiomatic-usage">Idiomatic Usage</a></li>
<li><a class="reference internal" href="#packaging-considerations">Packaging Considerations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#main-py-in-python-packages"><code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> in Python Packages</a><ul>
<li><a class="reference internal" href="#id1">Idiomatic Usage</a></li>
</ul>
</li>
<li><a class="reference internal" href="#import-main"><code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">__main__</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="builtins.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code> — Built-in objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="warnings.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/__main__.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="warnings.html" title="warnings — Warning control"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="builtins.html" title="builtins — Built-in objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-__main__">
<span id="main-top-level-code-environment"></span><h1><a class="reference internal" href="#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> — Top-level code environment<a class="headerlink" href="#module-__main__" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>In Python, the special name <code class="docutils literal notranslate"><span class="pre">__main__</span></code> is used for two important constructs:</p>
<ol class="arabic simple">
<li><p>the name of the top-level environment of the program, which can be
checked using the <code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code> expression; and</p></li>
<li><p>the <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> file in Python packages.</p></li>
</ol>
<p>Both of these mechanisms are related to Python modules; how users interact with
them and how they interact with each other.  They are explained in detail
below.  If you’re new to Python modules, see the tutorial section
<a class="reference internal" href="../tutorial/modules.html#tut-modules"><span class="std std-ref">Modules</span></a> for an introduction.</p>
<section id="name-main">
<span id="name-equals-main"></span><h2><code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code><a class="headerlink" href="#name-main" title="Link to this heading">¶</a></h2>
<p>When a Python module or package is imported, <code class="docutils literal notranslate"><span class="pre">__name__</span></code> is set to the
module’s name.  Usually, this is the name of the Python file itself without the
<code class="docutils literal notranslate"><span class="pre">.py</span></code> extension:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">configparser</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">configparser</span><span class="o">.</span><span class="vm">__name__</span>
<span class="go">&#39;configparser&#39;</span>
</pre></div>
</div>
<p>If the file is part of a package, <code class="docutils literal notranslate"><span class="pre">__name__</span></code> will also include the parent
package’s path:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">concurrent.futures</span> <span class="kn">import</span> <span class="n">process</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">process</span><span class="o">.</span><span class="vm">__name__</span>
<span class="go">&#39;concurrent.futures.process&#39;</span>
</pre></div>
</div>
<p>However, if the module is executed in the top-level code environment,
its <code class="docutils literal notranslate"><span class="pre">__name__</span></code> is set to the string <code class="docutils literal notranslate"><span class="pre">'__main__'</span></code>.</p>
<section id="what-is-the-top-level-code-environment">
<h3>What is the “top-level code environment”?<a class="headerlink" href="#what-is-the-top-level-code-environment" title="Link to this heading">¶</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">__main__</span></code> is the name of the environment where top-level code is run.
“Top-level code” is the first user-specified Python module that starts running.
It’s “top-level” because it imports all other modules that the program needs.
Sometimes “top-level code” is called an <em>entry point</em> to the application.</p>
<p>The top-level code environment can be:</p>
<ul>
<li><p>the scope of an interactive prompt:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="vm">__name__</span>
<span class="go">&#39;__main__&#39;</span>
</pre></div>
</div>
</li>
<li><p>the Python module passed to the Python interpreter as a file argument:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>helloworld.py
<span class="go">Hello, world!</span>
</pre></div>
</div>
</li>
<li><p>the Python module or package passed to the Python interpreter with the
<a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> argument:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile
<span class="go">usage: tarfile.py [-h] [-v] (...)</span>
</pre></div>
</div>
</li>
<li><p>Python code read by the Python interpreter from standard input:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span><span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;import this&quot;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python
<span class="go">The Zen of Python, by Tim Peters</span>

<span class="go">Beautiful is better than ugly.</span>
<span class="go">Explicit is better than implicit.</span>
<span class="go">...</span>
</pre></div>
</div>
</li>
<li><p>Python code passed to the Python interpreter with the <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a> argument:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;import this&quot;</span>
<span class="go">The Zen of Python, by Tim Peters</span>

<span class="go">Beautiful is better than ugly.</span>
<span class="go">Explicit is better than implicit.</span>
<span class="go">...</span>
</pre></div>
</div>
</li>
</ul>
<p>In each of these situations, the top-level module’s <code class="docutils literal notranslate"><span class="pre">__name__</span></code> is set to
<code class="docutils literal notranslate"><span class="pre">'__main__'</span></code>.</p>
<p>As a result, a module can discover whether or not it is running in the
top-level environment by checking its own <code class="docutils literal notranslate"><span class="pre">__name__</span></code>, which allows a common
idiom for conditionally executing code when the module is not initialized from
an import statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="c1"># Execute when the module is not initialized from an import statement.</span>
    <span class="o">...</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>For a more detailed look at how <code class="docutils literal notranslate"><span class="pre">__name__</span></code> is set in all situations, see
the tutorial section <a class="reference internal" href="../tutorial/modules.html#tut-modules"><span class="std std-ref">Modules</span></a>.</p>
</div>
</section>
<section id="idiomatic-usage">
<h3>Idiomatic Usage<a class="headerlink" href="#idiomatic-usage" title="Link to this heading">¶</a></h3>
<p>Some modules contain code that is intended for script use only, like parsing
command-line arguments or fetching data from standard input.  If a module
like this was imported from a different module, for example to unit test
it, the script code would unintentionally execute as well.</p>
<p>This is where using the <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code> code block comes in
handy. Code within this block won’t run unless the module is executed in the
top-level environment.</p>
<p>Putting as few statements as possible in the block below <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span>
<span class="pre">'__main__'</span></code> can improve code clarity and correctness. Most often, a function
named <code class="docutils literal notranslate"><span class="pre">main</span></code> encapsulates the program’s primary behavior:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># echo.py</span>

<span class="kn">import</span> <span class="nn">shlex</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="k">def</span> <span class="nf">echo</span><span class="p">(</span><span class="n">phrase</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">   </span><span class="sd">&quot;&quot;&quot;A dummy wrapper around print.&quot;&quot;&quot;</span>
   <span class="c1"># for demonstration purposes, you can imagine that there is some</span>
   <span class="c1"># valuable and reusable logic inside this function</span>
   <span class="nb">print</span><span class="p">(</span><span class="n">phrase</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">main</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Echo the input arguments to standard output&quot;&quot;&quot;</span>
    <span class="n">phrase</span> <span class="o">=</span> <span class="n">shlex</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">)</span>
    <span class="n">echo</span><span class="p">(</span><span class="n">phrase</span><span class="p">)</span>
    <span class="k">return</span> <span class="mi">0</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>  <span class="c1"># next section explains the use of sys.exit</span>
</pre></div>
</div>
<p>Note that if the module didn’t encapsulate code inside the <code class="docutils literal notranslate"><span class="pre">main</span></code> function
but instead put it directly within the <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code> block,
the <code class="docutils literal notranslate"><span class="pre">phrase</span></code> variable would be global to the entire module.  This is
error-prone as other functions within the module could be unintentionally using
the global variable instead of a local name.  A <code class="docutils literal notranslate"><span class="pre">main</span></code> function solves this
problem.</p>
<p>Using a <code class="docutils literal notranslate"><span class="pre">main</span></code> function has the added benefit of the <code class="docutils literal notranslate"><span class="pre">echo</span></code> function itself
being isolated and importable elsewhere. When <code class="docutils literal notranslate"><span class="pre">echo.py</span></code> is imported, the
<code class="docutils literal notranslate"><span class="pre">echo</span></code> and <code class="docutils literal notranslate"><span class="pre">main</span></code> functions will be defined, but neither of them will be
called, because <code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">!=</span> <span class="pre">'__main__'</span></code>.</p>
</section>
<section id="packaging-considerations">
<h3>Packaging Considerations<a class="headerlink" href="#packaging-considerations" title="Link to this heading">¶</a></h3>
<p><code class="docutils literal notranslate"><span class="pre">main</span></code> functions are often used to create command-line tools by specifying
them as entry points for console scripts.  When this is done,
<a class="reference external" href="https://pip.pypa.io/">pip</a> inserts the function call into a template script,
where the return value of <code class="docutils literal notranslate"><span class="pre">main</span></code> is passed into <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>.
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>
</pre></div>
</div>
<p>Since the call to <code class="docutils literal notranslate"><span class="pre">main</span></code> is wrapped in <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>, the expectation is
that your function will return some value acceptable as an input to
<a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>; typically, an integer or <code class="docutils literal notranslate"><span class="pre">None</span></code> (which is implicitly
returned if your function does not have a return statement).</p>
<p>By proactively following this convention ourselves, our module will have the
same behavior when run directly (i.e. <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">echo.py</span></code>) as it will have if
we later package it as a console script entry-point in a pip-installable
package.</p>
<p>In particular, be careful about returning strings from your <code class="docutils literal notranslate"><span class="pre">main</span></code> function.
<a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a> will interpret a string argument as a failure message, so
your program will have an exit code of <code class="docutils literal notranslate"><span class="pre">1</span></code>, indicating failure, and the
string will be written to <a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>.  The <code class="docutils literal notranslate"><span class="pre">echo.py</span></code> example from
earlier exemplifies using the <code class="docutils literal notranslate"><span class="pre">sys.exit(main())</span></code> convention.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/">Python Packaging User Guide</a>
contains a collection of tutorials and references on how to distribute and
install Python packages with modern tools.</p>
</div>
</section>
</section>
<section id="main-py-in-python-packages">
<h2><code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> in Python Packages<a class="headerlink" href="#main-py-in-python-packages" title="Link to this heading">¶</a></h2>
<p>If you are not familiar with Python packages, see section <a class="reference internal" href="../tutorial/modules.html#tut-packages"><span class="std std-ref">Packages</span></a>
of the tutorial.  Most commonly, the <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> file is used to provide
a command-line interface for a package. Consider the following hypothetical
package, “bandclass”:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>bandclass
  ├── __init__.py
  ├── __main__.py
  └── student.py
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> will be executed when the package itself is invoked
directly from the command line using the <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> flag. For example:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>bandclass
</pre></div>
</div>
<p>This command will cause <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> to run. How you utilize this mechanism
will depend on the nature of the package you are writing, but in this
hypothetical case, it might make sense to allow the teacher to search for
students:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># bandclass/__main__.py</span>

<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">from</span> <span class="nn">.student</span> <span class="kn">import</span> <span class="n">search_students</span>

<span class="n">student_name</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">2</span> <span class="k">else</span> <span class="s1">&#39;&#39;</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;Found student: </span><span class="si">{</span><span class="n">search_students</span><span class="p">(</span><span class="n">student_name</span><span class="p">)</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">.student</span> <span class="pre">import</span> <span class="pre">search_students</span></code> is an example of a relative
import.  This import style can be used when referencing modules within a
package.  For more details, see <a class="reference internal" href="../tutorial/modules.html#intra-package-references"><span class="std std-ref">Intra-package References</span></a> in the
<a class="reference internal" href="../tutorial/modules.html#tut-modules"><span class="std std-ref">Modules</span></a> section of the tutorial.</p>
<section id="id1">
<h3>Idiomatic Usage<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<p>The content of <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> typically isn’t fenced with an
<code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code> block.  Instead, those files are kept
short and import functions to execute from other modules.  Those other modules can then be
easily unit-tested and are properly reusable.</p>
<p>If used, an <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code> block will still work as expected
for a <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> file within a package, because its <code class="docutils literal notranslate"><span class="pre">__name__</span></code>
attribute will include the package’s path if imported:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">asyncio.__main__</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">asyncio</span><span class="o">.</span><span class="n">__main__</span><span class="o">.</span><span class="vm">__name__</span>
<span class="go">&#39;asyncio.__main__&#39;</span>
</pre></div>
</div>
<p>This won’t work for <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> files in the root directory of a .zip file
though.  Hence, for consistency, minimal <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> like the <a class="reference internal" href="venv.html#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code></a>
one mentioned below are preferred.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>See <a class="reference internal" href="venv.html#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code></a> for an example of a package with a minimal <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code>
in the standard library. It doesn’t contain a <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code>
block. You can invoke it with <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">venv</span> <span class="pre">[directory]</span></code>.</p>
<p>See <a class="reference internal" href="runpy.html#module-runpy" title="runpy: Locate and run Python modules without importing them first."><code class="xref py py-mod docutils literal notranslate"><span class="pre">runpy</span></code></a> for more details on the <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> flag to the
interpreter executable.</p>
<p>See <a class="reference internal" href="zipapp.html#module-zipapp" title="zipapp: Manage executable Python zip archives"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipapp</span></code></a> for how to run applications packaged as <em>.zip</em> files. In
this case Python looks for a <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> file in the root directory of
the archive.</p>
</div>
</section>
</section>
<section id="import-main">
<h2><code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">__main__</span></code><a class="headerlink" href="#import-main" title="Link to this heading">¶</a></h2>
<p>Regardless of which module a Python program was started with, other modules
running within that same program can import the top-level environment’s scope
(<a class="reference internal" href="../glossary.html#term-namespace"><span class="xref std std-term">namespace</span></a>) by importing the <code class="docutils literal notranslate"><span class="pre">__main__</span></code> module.  This doesn’t import
a <code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> file but rather whichever module that received the special
name <code class="docutils literal notranslate"><span class="pre">'__main__'</span></code>.</p>
<p>Here is an example module that consumes the <code class="docutils literal notranslate"><span class="pre">__main__</span></code> namespace:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># namely.py</span>

<span class="kn">import</span> <span class="nn">__main__</span>

<span class="k">def</span> <span class="nf">did_user_define_their_name</span><span class="p">():</span>
    <span class="k">return</span> <span class="s1">&#39;my_name&#39;</span> <span class="ow">in</span> <span class="nb">dir</span><span class="p">(</span><span class="n">__main__</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">print_user_name</span><span class="p">():</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">did_user_define_their_name</span><span class="p">():</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Define the variable `my_name`!&#39;</span><span class="p">)</span>

    <span class="k">if</span> <span class="s1">&#39;__file__&#39;</span> <span class="ow">in</span> <span class="nb">dir</span><span class="p">(</span><span class="n">__main__</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">my_name</span><span class="p">,</span> <span class="s2">&quot;found in file&quot;</span><span class="p">,</span> <span class="n">__main__</span><span class="o">.</span><span class="vm">__file__</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">__main__</span><span class="o">.</span><span class="n">my_name</span><span class="p">)</span>
</pre></div>
</div>
<p>Example usage of this module could be as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># start.py</span>

<span class="kn">import</span> <span class="nn">sys</span>

<span class="kn">from</span> <span class="nn">namely</span> <span class="kn">import</span> <span class="n">print_user_name</span>

<span class="c1"># my_name = &quot;Dinsdale&quot;</span>

<span class="k">def</span> <span class="nf">main</span><span class="p">():</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">print_user_name</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">ve</span><span class="p">:</span>
        <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">ve</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="n">main</span><span class="p">())</span>
</pre></div>
</div>
<p>Now, if we started our program, the result would look like this:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>start.py
<span class="go">Define the variable `my_name`!</span>
</pre></div>
</div>
<p>The exit code of the program would be 1, indicating an error. Uncommenting the
line with <code class="docutils literal notranslate"><span class="pre">my_name</span> <span class="pre">=</span> <span class="pre">&quot;Dinsdale&quot;</span></code> fixes the program and now it exits with
status code 0, indicating success:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>start.py
<span class="go">Dinsdale found in file /path/to/start.py</span>
</pre></div>
</div>
<p>Note that importing <code class="docutils literal notranslate"><span class="pre">__main__</span></code> doesn’t cause any issues with unintentionally
running top-level code meant for script use which is put in the
<code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">&quot;__main__&quot;</span></code> block of the <code class="docutils literal notranslate"><span class="pre">start</span></code> module. Why does this work?</p>
<p>Python inserts an empty <code class="docutils literal notranslate"><span class="pre">__main__</span></code> module in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a> at
interpreter startup, and populates it by running top-level code. In our example
this is the <code class="docutils literal notranslate"><span class="pre">start</span></code> module which runs line by line and imports <code class="docutils literal notranslate"><span class="pre">namely</span></code>.
In turn, <code class="docutils literal notranslate"><span class="pre">namely</span></code> imports <code class="docutils literal notranslate"><span class="pre">__main__</span></code> (which is really <code class="docutils literal notranslate"><span class="pre">start</span></code>). That’s an
import cycle! Fortunately, since the partially populated <code class="docutils literal notranslate"><span class="pre">__main__</span></code>
module is present in <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>, Python passes that to <code class="docutils literal notranslate"><span class="pre">namely</span></code>.
See <a class="reference internal" href="../reference/import.html#import-dunder-main"><span class="std std-ref">Special considerations for __main__</span></a> in the
import system’s reference for details on how this works.</p>
<p>The Python REPL is another example of a “top-level environment”, so anything
defined in the REPL becomes part of the <code class="docutils literal notranslate"><span class="pre">__main__</span></code> scope:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">namely</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">namely</span><span class="o">.</span><span class="n">did_user_define_their_name</span><span class="p">()</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">namely</span><span class="o">.</span><span class="n">print_user_name</span><span class="p">()</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">Define the variable `my_name`!</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">my_name</span> <span class="o">=</span> <span class="s1">&#39;Jabberwocky&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">namely</span><span class="o">.</span><span class="n">did_user_define_their_name</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">namely</span><span class="o">.</span><span class="n">print_user_name</span><span class="p">()</span>
<span class="go">Jabberwocky</span>
</pre></div>
</div>
<p>Note that in this case the <code class="docutils literal notranslate"><span class="pre">__main__</span></code> scope doesn’t contain a <code class="docutils literal notranslate"><span class="pre">__file__</span></code>
attribute as it’s interactive.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">__main__</span></code> scope is used in the implementation of <a class="reference internal" href="pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> and
<a class="reference internal" href="rlcompleter.html#module-rlcompleter" title="rlcompleter: Python identifier completion, suitable for the GNU readline library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code></a>.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a><ul>
<li><a class="reference internal" href="#name-main"><code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code></a><ul>
<li><a class="reference internal" href="#what-is-the-top-level-code-environment">What is the “top-level code environment”?</a></li>
<li><a class="reference internal" href="#idiomatic-usage">Idiomatic Usage</a></li>
<li><a class="reference internal" href="#packaging-considerations">Packaging Considerations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#main-py-in-python-packages"><code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> in Python Packages</a><ul>
<li><a class="reference internal" href="#id1">Idiomatic Usage</a></li>
</ul>
</li>
<li><a class="reference internal" href="#import-main"><code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">__main__</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="builtins.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code> — Built-in objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="warnings.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/__main__.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="warnings.html" title="warnings — Warning control"
             >next</a> |</li>
        <li class="right" >
          <a href="builtins.html" title="builtins — Built-in objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>