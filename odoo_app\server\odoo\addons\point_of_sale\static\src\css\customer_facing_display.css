@keyframes item_in {
  0% {
    opacity: 0;
    margin-top: -30px;
  }
  50% {
    margin-top: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes item_in {
  0% {
    opacity: 0;
    margin-top: -30px;
  }
  50% {
    margin-top: 0;
  }
  100% {
    opacity: 1;
  }
}
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: geometricPrecision;
  font-smooth: always;
}
body .pos-customer_facing_display {
  background-color: #f6f6f6;
  font-size: 2vw;
  font-family: Futura, HelveticaNeue, Helvetica, Arial, "Lucida Grande", sans-serif;
  font-weight: 300;
  width: 100%;
  height: 100%;
  padding: 0;
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  -o-display: flex;
  display: flex;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  -o-flex-direction: row;
  flex-direction: row;
  overflow: hidden;
}
body .pos-customer_facing_display .pos-customer_products {
    overflow: auto;
    padding: 1%;
}
body .pos-customer_facing_display .pos-customer_products,
body .pos-customer_facing_display .pos-payment_info {
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  -o-display: flex;
  display: flex;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -moz-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.pos-customer_facing_display .pos_orderlines_list {
    -webkit-display: flex;
    -moz-display: flex;
    -ms-display: flex;
    -o-display: flex;
    display: flex;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    -ms-flex-direction: column;
    -o-flex-direction: column;
    flex-direction: column;
    height: 100%;
}

.pos-customer_facing_display .pos-order_top_bar {
    border-radius: 0.2rem;
    padding: 0.5rem 1rem 0.3rem 1.3rem;
    color: white;
    font-weight: bold;
    font-size: max(min(3.2vh, 3.2vw), 1rem);
    background-color: #343A40;
}

.pos-customer_facing_display .pos_orderlines_list table {
    text-align: center;
    vertical-align: middle;
    border-collapse: separate;
    border-spacing: 0 10px;
    width: 100%;
}

.pos-customer_facing_display .pos_orderlines_list table th {
    position: sticky;
    top: 0;
    padding: 0.5rem;
    color: #212529;
    font-weight: 590;
    font-size: max(min(2vh, 2vw), 1rem);
}

.pos-customer_facing_display .pos_orderlines_list table th.pos-product-th {
    text-align: left;
}

.pos-customer_facing_display .pos_orderlines_list table th.pos-product-th span {
    margin-left: 1rem;
}

.pos-customer_facing_display .pos_orderlines_list table td {
    word-wrap: break-word;
    max-width: min(13vw, 8rem);
    padding: 0 0.3vw;
    font-size: max(min(3vh, 3vw), 1rem);
    font-weight: bold;
}

.pos-customer_facing_display .pos_orderlines_list table td.product_img {
    padding: 1vh 1vh;
    max-width: unset;
}

.pos-customer_facing_display .pos_orderlines_list table td.product_img img {
    width: min(10vh, 10vw);
    height: min(10vh, 10vw);
    border-radius: 10%;
}

.pos-customer_facing_display .pos_orderlines_list table td.product_text_info {
    min-width: 15vw;
    max-width: 30vw;
}

.pos-customer_facing_display .pos_orderlines_list table .product_text_info ul {
    text-align: left;
    margin: 0;
    list-style-type: none;
    padding: 0;
}

.pos-customer_facing_display .pos_orderlines_list table td .product_details i.fa{
    margin-right: 0.5rem;
    color: gray;
}

.pos-customer_facing_display .pos_orderlines_list table td .product_details {
    font-size: max(min(2.5vh, 2.5vw), 0.8rem);
}

.pos-customer_facing_display .pos_orderlines_list table td.product_quantity > span + span {
    margin-left: .3rem;
    word-wrap: unset;
    white-space: nowrap;
    word-break: break-word;
}

.gray {
    color: gray;
}

.pos-customer_facing_display .order-empty {
    text-align: center;
    margin: auto;
}

.pos-customer_facing_display .order-empty .fa {
    font-size: min(max(8vw, 2.3rem), 4.3rem);
}

.pos-customer_facing_display .order-empty h1 {
    font-size: min(max(4vw, 1.3rem), 2.3rem);
}

body .pos-customer_facing_display .pos_orderlines {
  width: 100%;
  height: 100%;
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  -o-display: flex;
  display: flex;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
}
body .pos-customer_facing_display .pos_orderlines .pos_orderlines_list {
  overflow-y: scroll;
  scroll-snap-type: y mandatory;
  padding-right: 1.5vw;
  position: relative;
  height: 100%;
}
.pos-customer_facing_display .pos_orderlines .pos_orderlines_item td:first-child {
    border-left-style: solid;
    border-top-left-radius: 0.9vw;
    border-bottom-left-radius: 0.9vw;
}
.pos-customer_facing_display .pos_orderlines .pos_orderlines_item td:last-child {
    border-left-style: solid;
    border-top-right-radius: 0.9vw;
    border-bottom-right-radius: 0.9vw;
}
body .pos-customer_facing_display .pos_orderlines .pos_orderlines_item {
  padding: 1%;
  border-radius: 0.9vw;
}
body .pos-customer_facing_display .pos_orderlines_list.backend_product_screen .selected_orderline {
    scroll-snap-align: center;
    animation: item_in 1s ease;
}
body .pos-customer_facing_display .pos-thank-you-message {
    -webkit-display: flex;
    -moz-display: flex;
    -ms-display: flex;
    -o-display: flex;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: min(max(4vw, 1.3rem), 2.3rem);
    text-align: center;
}
body .pos-customer_facing_display .pos-payment_info {
  max-width: 30%;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  -o-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -moz-box-pack: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}
body .pos-customer_facing_display .pos-company_logo {
    background-image: url(/logo);
    background-size: contain;
    background-repeat: no-repeat;
}
body .pos-customer_facing_display .pos-company_logo[style*="url(http://placehold.it"] {
  background-color: #ccc;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-total,
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-paymentlines {
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  -o-flex-direction: row;
  flex-direction: row;
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  -o-display: flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -moz-box-pack: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
  -webkit-box-align: baseline;
  -webkit-align-items: baseline;
  -moz-box-align: baseline;
  -ms-flex-align: baseline;
  -ms-grid-row-align: baseline;
  align-items: baseline;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-total {
    font-size: min(max(3.7vw, 1rem), 2rem);
    font-weight: bold;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-amount-container {
    width: 100%;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-spaced-line-container {
    -webkit-display: flex;
    -moz-display: flex;
    -ms-display: flex;
    -o-display: flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    flex-grow: 1;
    flex-direction: row;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-amount-container.pos-spaced-line-container .name {
    min-width: unset;
    max-width: unset;
    width: fit-content;
    min-height: unset;
    max-height: unset;
    height: fit-content;
    flex: unset;
    margin-right: .3rem;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-spaced-line-container .spacer {
    flex-grow: 1;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-amount-container.pos-spaced-line-container .amount {
    word-break: break-all;
    white-space: break-spaces;
    word-wrap: break-word;
    text-align: left;
    min-width: fit-content;
    max-width: fit-content;
    width: unset;
    align-self: flex-end;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-total .pos-total_amount {
  font-size: min(max(4vw, 1.3rem), 2.3rem);
  font-weight: bold;
}
body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-paymentlines {
  margin-top: 2%;
  font-size: min(max(2vw, .9rem), 1.6rem);
  font-weight: normal;
  line-height: 1.3;
  color: #DEE2E6;
}
body .pos-customer_facing_display .pos-payment_info .pos-odoo_logo_container {
    background: #212529;
    opacity: .9;
    border-top: gray solid 1px;
    height: max(min(3.1vh, 2.1rem), 1.1rem);
    width: 100%;
    -webkit-display: flex;
    -moz-display: flex;
    -ms-display: flex;
    -o-display: flex;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}
body .pos-customer_facing_display .pos-payment_info .pos-odoo_logo_container .logo {
    height: max(min(min(2vh, 2vw), 2rem), .8rem);
    width: max(min(min(6vh, 6vw), 6rem), 2.6rem);
    margin-left: .3rem;
    background-image: url(/web/static/img/logo_inverse_white_206px.png);
    background-position: right top;
    background-size: contain;
    background-repeat: no-repeat;
}
body .pos-customer_facing_display .pos-payment_info .pos-odoo_logo_container span {
    font-size: max(min(2vw, 1rem), .8rem);
    color: rgba(255, 255, 255, 0.4);
}

body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-paymentlines::after {
    height: 2vw; /* this empty element gives space between the paymentlines and the Odoo logo footer only if there is enough space (depending on the amount of paymentlines) */
    content: '';
    display: block;
    width: 100%;
}

/* properties for default orientation (landscape): */
body .pos-customer_facing_display .pos-portrait-top {
    display: none;
}

body .pos-customer_facing_display .pos-no-order {
    display: block;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-color: #343A40;
}

body .pos-customer_facing_display .pos-no-order .pos-company_logo {
    display: block;
    background-position: center top;
    margin-top: 5vh;
    height: max(10vh, 3rem);
    width: auto;
}

body .pos-customer_facing_display .pos-payment_info .pos-company_logo {
    display: initial;
    background-position: center top;
    margin: 10%;
    -webkit-box-flex: 1 0 20%;
    -webkit-flex: 1 0 20%;
    -moz-box-flex: 1 0 20%;
    -ms-flex: 1 0 20%;
    flex: 1 0 20%;
}

body .pos-customer_facing_display .pos-payment_info {
    background-size: cover; /* background image displayed */
}

body .pos-customer_facing_display .pos-customer_products,
body .pos-customer_facing_display .pos-payment_info {
    height: 100%;
}
/* end of properties for default orientation (landscape) */

@media all and (orientation: portrait) {
  body .pos-customer_facing_display {
    font-size: 2vh;
    height: 100%;
    -webkit-flex-direction: column;
    -moz-flex-direction: column;
    -ms-flex-direction: column;
    -o-flex-direction: column;
    flex-direction: column;
  }
  body .pos-customer_facing_display .pos-portrait-top {
    -webkit-display: flex;
    -moz-display: flex;
    -ms-display: flex;
    -o-display: flex;
    display: flex;
    height: 15vh;
    padding: 1% 1% 0 1%;
  }
  body .pos-customer_facing_display .pos-portrait-top .pos-header {
    background-image: var(--pos-bg-img);
    background-size: cover;
    border-radius: 0.2rem 0.2rem 0 0;
    height: 100%;
    width: 100%;
    padding: 1% 30%;
    background-color: #343A40;
  }
  body .pos-customer_facing_display .pos-portrait-top .pos-company_logo {
    height: 100%;
    background-position: center;
  }
  body .pos-customer_facing_display .pos-customer_products {
    flex-grow: 1;
    overflow: auto;
    padding-top: 0;
    height: unset;
  }
  body .pos-customer_facing_display .pos_orderlines_list table th {
    font-size: min(5vw, 1rem);
  }
  body .pos-customer_facing_display .pos-order_top_bar {
    border-radius: 0 0 0.2rem 0.2rem;
    border-top: solid gray 1px;
  }
  body .pos-customer_facing_display .pos-customer_products .pos_orderlines {
    -webkit-box-flex: 1 0 auto;
    -webkit-flex: 1 0 auto;
    -moz-box-flex: 1 0 auto;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
  }
  body .pos-customer_facing_display .pos-customer_products .pos_orderlines .pos_orderlines_list {
    padding-right: 1.5vh;
    height: auto;
  }
  body .pos-customer_facing_display .pos-customer_products .pos_orderlines .pos_orderlines_list .pos_orderlines_item {
    box-shadow: 0 0.1vh 0.1vh #dddddd;
    margin-bottom: 1vh;
  }
  body .pos-customer_facing_display .pos-payment_info {
    background-size: 0 0; /* background image hidden */
    max-width: 100%;
    width: 100%;
    max-height: 40vh;
    height: unset;
    flex-grow: 0;
  }
  body .pos-customer_facing_display .pos-payment_info .pos-company_logo {
    display: none;
  }
  body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details {
    flex-grow: 1;
  }
  body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details .pos-paymentlines {
    margin-top: 2%;
    font-size: max(2.5vw, .7rem);
  }
}

body .pos-customer_facing_display .pos-payment_info .pos-payment_info_details {
    background: #212529;
    opacity: .9;
    padding: 1% 6%;
    flex-grow: 0;
    overflow: hidden;
}

body .pos-hidden {
  opacity: 0;
}

.pos-palette_01 .pos-payment_info {
  background: #343A40;
  color: #f6f6f6;
}
.pos-palette_01 .pos-customer_products {
  background: #f6f6f6;
  color: #585858;
}
.pos-palette_01.pos-customer_facing_display .pos_orderlines_list table th {
    background-color: #f6f6f6;
}
.pos-palette_01 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #aaaaaa;
}
@media all and (orientation: portrait) {
  .pos-palette_01:before {
    background: #3E3E3E;
  }
}

.pos-palette_02 .pos-payment_info {
  background: #364152;
  color: #e6e7e8;
}
.pos-palette_02 .pos-customer_products {
  background: #ecf2f6;
  color: #364152;
}
.pos-palette_02.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ecf2f6;
}
.pos-palette_02 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #364152;
}
@media all and (orientation: portrait) {
  .pos-palette_02:before {
    background: #364152;
  }
}

.pos-palette_03 .pos-payment_info {
  background: #1BA39C;
  color: #f6f6f6;
}
.pos-palette_03 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_03.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_03 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_03:before {
    background: #1BA39C;
  }
}

.pos-palette_04 .pos-payment_info {
  background: #0b7b6c;
  color: #f6f6f6;
}
.pos-palette_04 .pos-customer_products {
  background: #efeeec;
  color: #585858;
}
.pos-palette_04.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #efeeec;
}
.pos-palette_04 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a9a499;
}
@media all and (orientation: portrait) {
  .pos-palette_04:before {
    background: #0b7b6c;
  }
}

.pos-palette_05 .pos-payment_info {
  background: #E26868;
  color: #f6f6f6;
}
.pos-palette_05 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_05.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_05 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_05:before {
    background: #E26868;
  }
}

.pos-palette_06 .pos-payment_info {
  background: #9E373B;
  color: #f6f6f6;
}
.pos-palette_06 .pos-customer_products {
  background: #f6f6f6;
  color: #585858;
}
.pos-palette_06.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #f6f6f6;
}
.pos-palette_06 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #aaaaaa;
}
@media all and (orientation: portrait) {
  .pos-palette_06:before {
    background: #9E373B;
  }
}

.pos-palette_07 .pos-payment_info {
  background: #ce9934;
  color: white;
}
.pos-palette_07 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_07.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_07 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_07:before {
    background: #ce9934;
  }
}

.pos-palette_08 .pos-payment_info {
  background: #a48c77;
  color: #f6f6f6;
}
.pos-palette_08 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_08.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_08 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_08:before {
    background: #a48c77;
  }
}

.pos-palette_09 .pos-payment_info {
  background: linear-gradient(30deg, #014d43, #127e71);
  color: #f6f6f6;
}
.pos-palette_09 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_09.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_09 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_09:before {
    background: linear-gradient(30deg, #014d43, #127e71);
  }
}

.pos-palette_10 .pos-payment_info {
  background: linear-gradient(30deg, #e2316c, #ea4c89);
  color: white;
}
.pos-palette_10 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_10.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_10 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_10:before {
    background: linear-gradient(30deg, #e2316c, #ea4c89);
  }
}

.pos-palette_11 .pos-payment_info {
  background: linear-gradient(30deg, #362b3d, #5b4a63);
  color: white;
}
.pos-palette_11 .pos-customer_products {
  background: #ececec;
  color: #585858;
}
.pos-palette_11.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #ececec;
}
.pos-palette_11 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: white;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #a0a0a0;
}
@media all and (orientation: portrait) {
  .pos-palette_11:before {
    background: linear-gradient(30deg, #362b3d, #5b4a63);
  }
}

.pos-palette_12 .pos-payment_info {
  background: #434343;
  color: #e6e6e6;
}
.pos-palette_12 .pos-customer_products {
  background: #5b5b5b;
  color: #bdb9b9;
}
.pos-palette_12.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #5b5b5b;
}
.pos-palette_12 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: #f5f5f5;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #0f0f0f;
}
@media all and (orientation: portrait) {
  .pos-palette_12:before {
    background: #434343;
  }
}

.pos-palette_13 .pos-payment_info {
  background: linear-gradient(30deg, #1a1b1f, #3d3f45);
  color: white;
}
.pos-palette_13 .pos-customer_products {
  background: #a2a2ab;
  color: #f6f6f6;
}
.pos-palette_13.pos-customer_facing_display .pos_orderlines_list table th {
  background-color: #a2a2ab;
}
.pos-palette_13 .pos-customer_products .pos_orderlines_list .pos_orderlines_item {
  background-color: #f6f6f6;
  color: #3E3E3E;
  box-shadow: 0 0.1vh 0.1vh #55555f;
}
@media all and (orientation: portrait) {
  .pos-palette_13:before {
    background: linear-gradient(30deg, #1a1b1f, #3d3f45);
  }
}
