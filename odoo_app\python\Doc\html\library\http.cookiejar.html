<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="http.cookiejar — Cookie handling for HTTP clients" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/http.cookiejar.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/http/cookiejar.py The http.cookiejar module defines classes for automatic handling of HTTP cookies. It is useful for accessing web sites that require small pieces of data – cookies..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/http/cookiejar.py The http.cookiejar module defines classes for automatic handling of HTTP cookies. It is useful for accessing web sites that require small pieces of data – cookies..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>http.cookiejar — Cookie handling for HTTP clients &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xmlrpc — XMLRPC server and client modules" href="xmlrpc.html" />
    <link rel="prev" title="http.cookies — HTTP state management" href="http.cookies.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/http.cookiejar.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a><ul>
<li><a class="reference internal" href="#cookiejar-and-filecookiejar-objects">CookieJar and FileCookieJar Objects</a></li>
<li><a class="reference internal" href="#filecookiejar-subclasses-and-co-operation-with-web-browsers">FileCookieJar subclasses and co-operation with web browsers</a></li>
<li><a class="reference internal" href="#cookiepolicy-objects">CookiePolicy Objects</a></li>
<li><a class="reference internal" href="#defaultcookiepolicy-objects">DefaultCookiePolicy Objects</a></li>
<li><a class="reference internal" href="#cookie-objects">Cookie Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.cookies.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xmlrpc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc</span></code> — XMLRPC server and client modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.cookiejar.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xmlrpc.html" title="xmlrpc — XMLRPC server and client modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="http.cookies.html" title="http.cookies — HTTP state management"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-http.cookiejar">
<span id="http-cookiejar-cookie-handling-for-http-clients"></span><h1><a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> — Cookie handling for HTTP clients<a class="headerlink" href="#module-http.cookiejar" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/http/cookiejar.py">Lib/http/cookiejar.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> module defines classes for automatic handling of HTTP
cookies.  It is useful for accessing web sites that require small pieces of data
– <em class="dfn">cookies</em> – to be set on the client machine by an HTTP response from a
web server, and then returned to the server in later HTTP requests.</p>
<p>Both the regular Netscape cookie protocol and the protocol defined by
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> are handled.  RFC 2965 handling is switched off by default.
<span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookies are parsed as Netscape cookies and subsequently treated
either as Netscape or RFC 2965 cookies according to the ‘policy’ in effect.
Note that the great majority of cookies on the internet are Netscape cookies.
<a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> attempts to follow the de-facto Netscape cookie protocol (which
differs substantially from that set out in the original Netscape specification),
including taking note of the <code class="docutils literal notranslate"><span class="pre">max-age</span></code> and <code class="docutils literal notranslate"><span class="pre">port</span></code> cookie-attributes
introduced with RFC 2965.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The various named parameters found in <em class="mailheader">Set-Cookie</em> and
<em class="mailheader">Set-Cookie2</em> headers (eg. <code class="docutils literal notranslate"><span class="pre">domain</span></code> and <code class="docutils literal notranslate"><span class="pre">expires</span></code>) are
conventionally referred to as <em class="dfn">attributes</em>.  To distinguish them from
Python attributes, the documentation for this module uses the term
<em class="dfn">cookie-attribute</em> instead.</p>
</div>
<p>The module defines the following exception:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="http.cookiejar.LoadError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">LoadError</span></span><a class="headerlink" href="#http.cookiejar.LoadError" title="Link to this definition">¶</a></dt>
<dd><p>Instances of <a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> raise this exception on failure to load
cookies from a file.  <a class="reference internal" href="#http.cookiejar.LoadError" title="http.cookiejar.LoadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">LoadError</span></code></a> is a subclass of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="#http.cookiejar.LoadError" title="http.cookiejar.LoadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">LoadError</span></code></a> used to be a subtype of <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>, which is now an
alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<p>The following classes are provided:</p>
<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">CookieJar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar" title="Link to this definition">¶</a></dt>
<dd><p><em>policy</em> is an object implementing the <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> interface.</p>
<p>The <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> class stores HTTP cookies.  It extracts cookies from HTTP
requests, and returns them in HTTP responses. <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> instances
automatically expire contained cookies when necessary.  Subclasses are also
responsible for storing and retrieving cookies from a file or database.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">FileCookieJar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delayload</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.FileCookieJar" title="Link to this definition">¶</a></dt>
<dd><p><em>policy</em> is an object implementing the <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> interface.  For the
other arguments, see the documentation for the corresponding attributes.</p>
<p>A <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> which can load cookies from, and perhaps save cookies to, a
file on disk.  Cookies are <strong>NOT</strong> loaded from the named file until either the
<a class="reference internal" href="#http.cookiejar.FileCookieJar.load" title="http.cookiejar.FileCookieJar.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load()</span></code></a> or <a class="reference internal" href="#http.cookiejar.FileCookieJar.revert" title="http.cookiejar.FileCookieJar.revert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">revert()</span></code></a> method is called.  Subclasses of this class are
documented in section <a class="reference internal" href="#file-cookie-jar-classes"><span class="std std-ref">FileCookieJar subclasses and co-operation with web browsers</span></a>.</p>
<p>This should not be initialized directly – use its subclasses below instead.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The filename parameter supports a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">CookiePolicy</span></span><a class="headerlink" href="#http.cookiejar.CookiePolicy" title="Link to this definition">¶</a></dt>
<dd><p>This class is responsible for deciding whether each cookie should be accepted
from / returned to the server.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">DefaultCookiePolicy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">blocked_domains</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowed_domains</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">netscape</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rfc2965</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rfc2109_as_netscape</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hide_cookie2</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_rfc2965_unverifiable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_ns_unverifiable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_ns_domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DefaultCookiePolicy.DomainLiberal</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_ns_set_initial_dollar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_ns_set_path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">secure_protocols</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('https',</span> <span class="pre">'wss')</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy" title="Link to this definition">¶</a></dt>
<dd><p>Constructor arguments should be passed as keyword arguments only.
<em>blocked_domains</em> is a sequence of domain names that we never accept cookies
from, nor return cookies to. <em>allowed_domains</em> if not <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, this is a
sequence of the only domains for which we accept and return cookies.
<em>secure_protocols</em> is a sequence of protocols for which secure cookies can be
added to. By default <em>https</em> and <em>wss</em> (secure websocket) are considered
secure protocols. For all other arguments, see the documentation for
<a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> and <a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> objects.</p>
<p><a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> implements the standard accept / reject rules for
Netscape and <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> cookies.  By default, <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookies (ie. cookies
received in a <em class="mailheader">Set-Cookie</em> header with a version cookie-attribute of
1) are treated according to the RFC 2965 rules.  However, if RFC 2965 handling
is turned off or <a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy.rfc2109_as_netscape" title="http.cookiejar.DefaultCookiePolicy.rfc2109_as_netscape"><code class="xref py py-attr docutils literal notranslate"><span class="pre">rfc2109_as_netscape</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, RFC 2109 cookies are
‘downgraded’ by the <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> instance to Netscape cookies, by
setting the <code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code> attribute of the <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> instance to 0.
<a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> also provides some parameters to allow some
fine-tuning of policy.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.Cookie">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">Cookie</span></span><a class="headerlink" href="#http.cookiejar.Cookie" title="Link to this definition">¶</a></dt>
<dd><p>This class represents Netscape, <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> and <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> cookies.  It is not
expected that users of <a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> construct their own <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a>
instances.  Instead, if necessary, call <code class="xref py py-meth docutils literal notranslate"><span class="pre">make_cookies()</span></code> on a
<a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> instance.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a></dt><dd><p>URL opening with automatic cookie handling.</p>
</dd>
<dt>Module <a class="reference internal" href="http.cookies.html#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a></dt><dd><p>HTTP cookie classes, principally useful for server-side code.  The
<a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> and <a class="reference internal" href="http.cookies.html#module-http.cookies" title="http.cookies: Support for HTTP state management (cookies)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code></a> modules do not depend on each
other.</p>
</dd>
<dt><a class="reference external" href="https://curl.se/rfc/cookie_spec.html">https://curl.se/rfc/cookie_spec.html</a></dt><dd><p>The specification of the original Netscape cookie protocol.  Though this is
still the dominant protocol, the ‘Netscape cookie protocol’ implemented by all
the major browsers (and <a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a>) only bears a passing resemblance to
the one sketched out in <code class="docutils literal notranslate"><span class="pre">cookie_spec.html</span></code>.</p>
</dd>
<dt><span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> - HTTP State Management Mechanism</dt><dd><p>Obsoleted by <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a>. Uses <em class="mailheader">Set-Cookie</em> with version=1.</p>
</dd>
<dt><span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> - HTTP State Management Mechanism</dt><dd><p>The Netscape protocol with the bugs fixed.  Uses <em class="mailheader">Set-Cookie2</em> in
place of <em class="mailheader">Set-Cookie</em>.  Not widely used.</p>
</dd>
<dt><a class="reference external" href="http://kristol.org/cookie/errata.html">http://kristol.org/cookie/errata.html</a></dt><dd><p>Unfinished errata to <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a>.</p>
</dd>
</dl>
<p><span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2964.html"><strong>RFC 2964</strong></a> - Use of HTTP State Management</p>
</div>
<section id="cookiejar-and-filecookiejar-objects">
<span id="cookie-jar-objects"></span><h2>CookieJar and FileCookieJar Objects<a class="headerlink" href="#cookiejar-and-filecookiejar-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> objects support the <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> protocol for iterating over
contained <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> objects.</p>
<p><a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.add_cookie_header">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">add_cookie_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.add_cookie_header" title="Link to this definition">¶</a></dt>
<dd><p>Add correct <em class="mailheader">Cookie</em> header to <em>request</em>.</p>
<p>If policy allows (ie. the <code class="xref py py-attr docutils literal notranslate"><span class="pre">rfc2965</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">hide_cookie2</span></code> attributes of
the <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a>’s <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> instance are true and false
respectively), the <em class="mailheader">Cookie2</em> header is also added when appropriate.</p>
<p>The <em>request</em> object (usually a <a class="reference internal" href="urllib.request.html#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.request.Request</span></code></a> instance)
must support the methods <code class="xref py py-meth docutils literal notranslate"><span class="pre">get_full_url()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">has_header()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">get_header()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">header_items()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">add_unredirected_header()</span></code>
and the attributes <code class="xref py py-attr docutils literal notranslate"><span class="pre">host</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">unverifiable</span></code>
and <code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_req_host</span></code> as documented by <a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>request</em> object needs <code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_req_host</span></code> attribute. Dependency on a
deprecated method <code class="xref py py-meth docutils literal notranslate"><span class="pre">get_origin_req_host()</span></code> has been removed.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.extract_cookies">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">extract_cookies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">response</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.extract_cookies" title="Link to this definition">¶</a></dt>
<dd><p>Extract cookies from HTTP <em>response</em> and store them in the <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a>,
where allowed by policy.</p>
<p>The <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> will look for allowable <em class="mailheader">Set-Cookie</em> and
<em class="mailheader">Set-Cookie2</em> headers in the <em>response</em> argument, and store cookies
as appropriate (subject to the <a class="reference internal" href="#http.cookiejar.CookiePolicy.set_ok" title="http.cookiejar.CookiePolicy.set_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">CookiePolicy.set_ok()</span></code></a> method’s approval).</p>
<p>The <em>response</em> object (usually the result of a call to
<a class="reference internal" href="urllib.request.html#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-meth docutils literal notranslate"><span class="pre">urllib.request.urlopen()</span></code></a>, or similar) should support an <code class="xref py py-meth docutils literal notranslate"><span class="pre">info()</span></code>
method, which returns an <a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> instance.</p>
<p>The <em>request</em> object (usually a <a class="reference internal" href="urllib.request.html#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.request.Request</span></code></a> instance)
must support the method <code class="xref py py-meth docutils literal notranslate"><span class="pre">get_full_url()</span></code> and the attributes
<code class="xref py py-attr docutils literal notranslate"><span class="pre">host</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">unverifiable</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_req_host</span></code>,
as documented by <a class="reference internal" href="urllib.request.html#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a>.  The request is used to set
default values for cookie-attributes as well as for checking that the
cookie is allowed to be set.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>request</em> object needs <code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_req_host</span></code> attribute. Dependency on a
deprecated method <code class="xref py py-meth docutils literal notranslate"><span class="pre">get_origin_req_host()</span></code> has been removed.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.set_policy">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">set_policy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">policy</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.set_policy" title="Link to this definition">¶</a></dt>
<dd><p>Set the <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> instance to be used.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.make_cookies">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">make_cookies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">response</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.make_cookies" title="Link to this definition">¶</a></dt>
<dd><p>Return sequence of <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> objects extracted from <em>response</em> object.</p>
<p>See the documentation for <a class="reference internal" href="#http.cookiejar.CookieJar.extract_cookies" title="http.cookiejar.CookieJar.extract_cookies"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract_cookies()</span></code></a> for the interfaces required of
the <em>response</em> and <em>request</em> arguments.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.set_cookie_if_ok">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">set_cookie_if_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookie</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.set_cookie_if_ok" title="Link to this definition">¶</a></dt>
<dd><p>Set a <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> if policy says it’s OK to do so.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.set_cookie">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">set_cookie</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookie</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.set_cookie" title="Link to this definition">¶</a></dt>
<dd><p>Set a <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a>, without checking with policy to see whether or not it
should be set.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.clear">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.clear" title="Link to this definition">¶</a></dt>
<dd><p>Clear some cookies.</p>
<p>If invoked without arguments, clear all cookies.  If given a single argument,
only cookies belonging to that <em>domain</em> will be removed. If given two arguments,
cookies belonging to the specified <em>domain</em> and URL <em>path</em> are removed.  If
given three arguments, then the cookie with the specified <em>domain</em>, <em>path</em> and
<em>name</em> is removed.</p>
<p>Raises <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if no matching cookie exists.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookieJar.clear_session_cookies">
<span class="sig-prename descclassname"><span class="pre">CookieJar.</span></span><span class="sig-name descname"><span class="pre">clear_session_cookies</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookieJar.clear_session_cookies" title="Link to this definition">¶</a></dt>
<dd><p>Discard all session cookies.</p>
<p>Discards all contained cookies that have a true <code class="xref py py-attr docutils literal notranslate"><span class="pre">discard</span></code> attribute
(usually because they had either no <code class="docutils literal notranslate"><span class="pre">max-age</span></code> or <code class="docutils literal notranslate"><span class="pre">expires</span></code> cookie-attribute,
or an explicit <code class="docutils literal notranslate"><span class="pre">discard</span></code> cookie-attribute).  For interactive browsers, the end
of a session usually corresponds to closing the browser window.</p>
<p>Note that the <code class="xref py py-meth docutils literal notranslate"><span class="pre">save()</span></code> method won’t save session cookies anyway, unless you
ask otherwise by passing a true <em>ignore_discard</em> argument.</p>
</dd></dl>

<p><a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> implements the following additional methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar.save">
<span class="sig-prename descclassname"><span class="pre">FileCookieJar.</span></span><span class="sig-name descname"><span class="pre">save</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_discard</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_expires</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.FileCookieJar.save" title="Link to this definition">¶</a></dt>
<dd><p>Save cookies to a file.</p>
<p>This base class raises <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>.  Subclasses may leave this
method unimplemented.</p>
<p><em>filename</em> is the name of file in which to save cookies.  If <em>filename</em> is not
specified, <code class="xref py py-attr docutils literal notranslate"><span class="pre">self.filename</span></code> is used (whose default is the value passed to
the constructor, if any); if <code class="xref py py-attr docutils literal notranslate"><span class="pre">self.filename</span></code> is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>,
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
<p><em>ignore_discard</em>: save even cookies set to be discarded. <em>ignore_expires</em>: save
even cookies that have expired</p>
<p>The file is overwritten if it already exists, thus wiping all the cookies it
contains.  Saved cookies can be restored later using the <a class="reference internal" href="#http.cookiejar.FileCookieJar.load" title="http.cookiejar.FileCookieJar.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load()</span></code></a> or
<a class="reference internal" href="#http.cookiejar.FileCookieJar.revert" title="http.cookiejar.FileCookieJar.revert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">revert()</span></code></a> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar.load">
<span class="sig-prename descclassname"><span class="pre">FileCookieJar.</span></span><span class="sig-name descname"><span class="pre">load</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_discard</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_expires</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.FileCookieJar.load" title="Link to this definition">¶</a></dt>
<dd><p>Load cookies from a file.</p>
<p>Old cookies are kept unless overwritten by newly loaded ones.</p>
<p>Arguments are as for <a class="reference internal" href="#http.cookiejar.FileCookieJar.save" title="http.cookiejar.FileCookieJar.save"><code class="xref py py-meth docutils literal notranslate"><span class="pre">save()</span></code></a>.</p>
<p>The named file must be in the format understood by the class, or
<a class="reference internal" href="#http.cookiejar.LoadError" title="http.cookiejar.LoadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">LoadError</span></code></a> will be raised.  Also, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> may be raised, for
example if the file does not exist.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> used to be raised, it is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar.revert">
<span class="sig-prename descclassname"><span class="pre">FileCookieJar.</span></span><span class="sig-name descname"><span class="pre">revert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_discard</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_expires</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.FileCookieJar.revert" title="Link to this definition">¶</a></dt>
<dd><p>Clear all cookies and reload cookies from a saved file.</p>
<p><a class="reference internal" href="#http.cookiejar.FileCookieJar.revert" title="http.cookiejar.FileCookieJar.revert"><code class="xref py py-meth docutils literal notranslate"><span class="pre">revert()</span></code></a> can raise the same exceptions as <a class="reference internal" href="#http.cookiejar.FileCookieJar.load" title="http.cookiejar.FileCookieJar.load"><code class="xref py py-meth docutils literal notranslate"><span class="pre">load()</span></code></a>. If there is a
failure, the object’s state will not be altered.</p>
</dd></dl>

<p><a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> instances have the following public attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar.filename">
<span class="sig-prename descclassname"><span class="pre">FileCookieJar.</span></span><span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#http.cookiejar.FileCookieJar.filename" title="Link to this definition">¶</a></dt>
<dd><p>Filename of default file in which to keep cookies.  This attribute may be
assigned to.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.FileCookieJar.delayload">
<span class="sig-prename descclassname"><span class="pre">FileCookieJar.</span></span><span class="sig-name descname"><span class="pre">delayload</span></span><a class="headerlink" href="#http.cookiejar.FileCookieJar.delayload" title="Link to this definition">¶</a></dt>
<dd><p>If true, load cookies lazily from disk.  This attribute should not be assigned
to.  This is only a hint, since this only affects performance, not behaviour
(unless the cookies on disk are changing). A <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> object may
ignore it.  None of the <a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> classes included in the standard
library lazily loads cookies.</p>
</dd></dl>

</section>
<section id="filecookiejar-subclasses-and-co-operation-with-web-browsers">
<span id="file-cookie-jar-classes"></span><h2>FileCookieJar subclasses and co-operation with web browsers<a class="headerlink" href="#filecookiejar-subclasses-and-co-operation-with-web-browsers" title="Link to this heading">¶</a></h2>
<p>The following <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> subclasses are provided for reading and
writing.</p>
<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.MozillaCookieJar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">MozillaCookieJar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delayload</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.MozillaCookieJar" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> that can load from and save cookies to disk in the
Mozilla <code class="docutils literal notranslate"><span class="pre">cookies.txt</span></code> file format (which is also used by curl and the Lynx
and Netscape browsers).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This loses information about <span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> cookies, and also about newer or
non-standard cookie-attributes such as <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Back up your cookies before saving if you have cookies whose loss / corruption
would be inconvenient (there are some subtleties which may lead to slight
changes in the file over a load / save round-trip).</p>
</div>
<p>Also note that cookies saved while Mozilla is running will get clobbered by
Mozilla.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="http.cookiejar.LWPCookieJar">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">http.cookiejar.</span></span><span class="sig-name descname"><span class="pre">LWPCookieJar</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delayload</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">policy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.LWPCookieJar" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#http.cookiejar.FileCookieJar" title="http.cookiejar.FileCookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileCookieJar</span></code></a> that can load from and save cookies to disk in format
compatible with the libwww-perl library’s <code class="docutils literal notranslate"><span class="pre">Set-Cookie3</span></code> file format.  This is
convenient if you want to store cookies in a human-readable file.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The filename parameter supports a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

</section>
<section id="cookiepolicy-objects">
<span id="cookie-policy-objects"></span><h2>CookiePolicy Objects<a class="headerlink" href="#cookiepolicy-objects" title="Link to this heading">¶</a></h2>
<p>Objects implementing the <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> interface have the following
methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.set_ok">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">set_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookie</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookiePolicy.set_ok" title="Link to this definition">¶</a></dt>
<dd><p>Return boolean value indicating whether cookie should be accepted from server.</p>
<p><em>cookie</em> is a <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> instance.  <em>request</em> is an object
implementing the interface defined by the documentation for
<a class="reference internal" href="#http.cookiejar.CookieJar.extract_cookies" title="http.cookiejar.CookieJar.extract_cookies"><code class="xref py py-meth docutils literal notranslate"><span class="pre">CookieJar.extract_cookies()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.return_ok">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">return_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookie</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookiePolicy.return_ok" title="Link to this definition">¶</a></dt>
<dd><p>Return boolean value indicating whether cookie should be returned to server.</p>
<p><em>cookie</em> is a <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> instance.  <em>request</em> is an object
implementing the interface defined by the documentation for
<a class="reference internal" href="#http.cookiejar.CookieJar.add_cookie_header" title="http.cookiejar.CookieJar.add_cookie_header"><code class="xref py py-meth docutils literal notranslate"><span class="pre">CookieJar.add_cookie_header()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.domain_return_ok">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">domain_return_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookiePolicy.domain_return_ok" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">False</span></code> if cookies should not be returned, given cookie domain.</p>
<p>This method is an optimization.  It removes the need for checking every cookie
with a particular domain (which might involve reading many files).  Returning
true from <a class="reference internal" href="#http.cookiejar.CookiePolicy.domain_return_ok" title="http.cookiejar.CookiePolicy.domain_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">domain_return_ok()</span></code></a> and <a class="reference internal" href="#http.cookiejar.CookiePolicy.path_return_ok" title="http.cookiejar.CookiePolicy.path_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_return_ok()</span></code></a> leaves all the
work to <a class="reference internal" href="#http.cookiejar.CookiePolicy.return_ok" title="http.cookiejar.CookiePolicy.return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">return_ok()</span></code></a>.</p>
<p>If <a class="reference internal" href="#http.cookiejar.CookiePolicy.domain_return_ok" title="http.cookiejar.CookiePolicy.domain_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">domain_return_ok()</span></code></a> returns true for the cookie domain,
<a class="reference internal" href="#http.cookiejar.CookiePolicy.path_return_ok" title="http.cookiejar.CookiePolicy.path_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_return_ok()</span></code></a> is called for the cookie path.  Otherwise,
<a class="reference internal" href="#http.cookiejar.CookiePolicy.path_return_ok" title="http.cookiejar.CookiePolicy.path_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_return_ok()</span></code></a> and <a class="reference internal" href="#http.cookiejar.CookiePolicy.return_ok" title="http.cookiejar.CookiePolicy.return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">return_ok()</span></code></a> are never called for that cookie
domain.  If <a class="reference internal" href="#http.cookiejar.CookiePolicy.path_return_ok" title="http.cookiejar.CookiePolicy.path_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_return_ok()</span></code></a> returns true, <a class="reference internal" href="#http.cookiejar.CookiePolicy.return_ok" title="http.cookiejar.CookiePolicy.return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">return_ok()</span></code></a> is called
with the <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> object itself for a full check.  Otherwise,
<a class="reference internal" href="#http.cookiejar.CookiePolicy.return_ok" title="http.cookiejar.CookiePolicy.return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">return_ok()</span></code></a> is never called for that cookie path.</p>
<p>Note that <a class="reference internal" href="#http.cookiejar.CookiePolicy.domain_return_ok" title="http.cookiejar.CookiePolicy.domain_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">domain_return_ok()</span></code></a> is called for every <em>cookie</em> domain, not just
for the <em>request</em> domain.  For example, the function might be called with both
<code class="docutils literal notranslate"><span class="pre">&quot;.example.com&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;www.example.com&quot;</span></code> if the request domain is
<code class="docutils literal notranslate"><span class="pre">&quot;www.example.com&quot;</span></code>.  The same goes for <a class="reference internal" href="#http.cookiejar.CookiePolicy.path_return_ok" title="http.cookiejar.CookiePolicy.path_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">path_return_ok()</span></code></a>.</p>
<p>The <em>request</em> argument is as documented for <a class="reference internal" href="#http.cookiejar.CookiePolicy.return_ok" title="http.cookiejar.CookiePolicy.return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">return_ok()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.path_return_ok">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">path_return_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.CookiePolicy.path_return_ok" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">False</span></code> if cookies should not be returned, given cookie path.</p>
<p>See the documentation for <a class="reference internal" href="#http.cookiejar.CookiePolicy.domain_return_ok" title="http.cookiejar.CookiePolicy.domain_return_ok"><code class="xref py py-meth docutils literal notranslate"><span class="pre">domain_return_ok()</span></code></a>.</p>
</dd></dl>

<p>In addition to implementing the methods above, implementations of the
<a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> interface must also supply the following attributes,
indicating which protocols should be used, and how.  All of these attributes may
be assigned to.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.netscape">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">netscape</span></span><a class="headerlink" href="#http.cookiejar.CookiePolicy.netscape" title="Link to this definition">¶</a></dt>
<dd><p>Implement Netscape protocol.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.rfc2965">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">rfc2965</span></span><a class="headerlink" href="#http.cookiejar.CookiePolicy.rfc2965" title="Link to this definition">¶</a></dt>
<dd><p>Implement <span class="target" id="index-12"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> protocol.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.CookiePolicy.hide_cookie2">
<span class="sig-prename descclassname"><span class="pre">CookiePolicy.</span></span><span class="sig-name descname"><span class="pre">hide_cookie2</span></span><a class="headerlink" href="#http.cookiejar.CookiePolicy.hide_cookie2" title="Link to this definition">¶</a></dt>
<dd><p>Don’t add <em class="mailheader">Cookie2</em> header to requests (the presence of this header
indicates to the server that we understand <span class="target" id="index-13"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> cookies).</p>
</dd></dl>

<p>The most useful way to define a <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> class is by subclassing
from <a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> and overriding some or all of the methods
above.  <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> itself may be used as a ‘null policy’ to allow
setting and receiving any and all cookies (this is unlikely to be useful).</p>
</section>
<section id="defaultcookiepolicy-objects">
<span id="default-cookie-policy-objects"></span><h2>DefaultCookiePolicy Objects<a class="headerlink" href="#defaultcookiepolicy-objects" title="Link to this heading">¶</a></h2>
<p>Implements the standard rules for accepting and returning cookies.</p>
<p>Both <span class="target" id="index-14"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> and Netscape cookies are covered.  RFC 2965 handling is switched
off by default.</p>
<p>The easiest way to provide your own policy is to override this class and call
its methods in your overridden implementations before adding your own additional
checks:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">http.cookiejar</span>
<span class="k">class</span> <span class="nc">MyCookiePolicy</span><span class="p">(</span><span class="n">http</span><span class="o">.</span><span class="n">cookiejar</span><span class="o">.</span><span class="n">DefaultCookiePolicy</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">set_ok</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cookie</span><span class="p">,</span> <span class="n">request</span><span class="p">):</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">http</span><span class="o">.</span><span class="n">cookiejar</span><span class="o">.</span><span class="n">DefaultCookiePolicy</span><span class="o">.</span><span class="n">set_ok</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cookie</span><span class="p">,</span> <span class="n">request</span><span class="p">):</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">if</span> <span class="n">i_dont_want_to_store_this_cookie</span><span class="p">(</span><span class="n">cookie</span><span class="p">):</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">return</span> <span class="kc">True</span>
</pre></div>
</div>
<p>In addition to the features required to implement the <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a>
interface, this class allows you to block and allow domains from setting and
receiving cookies.  There are also some strictness switches that allow you to
tighten up the rather loose Netscape protocol rules a little bit (at the cost of
blocking some benign cookies).</p>
<p>A domain blocklist and allowlist is provided (both off by default). Only domains
not in the blocklist and present in the allowlist (if the allowlist is active)
participate in cookie setting and returning.  Use the <em>blocked_domains</em>
constructor argument, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">blocked_domains()</span></code> and
<code class="xref py py-meth docutils literal notranslate"><span class="pre">set_blocked_domains()</span></code> methods (and the corresponding argument and methods
for <em>allowed_domains</em>).  If you set an allowlist, you can turn it off again by
setting it to <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
<p>Domains in block or allow lists that do not start with a dot must equal the
cookie domain to be matched.  For example, <code class="docutils literal notranslate"><span class="pre">&quot;example.com&quot;</span></code> matches a blocklist
entry of <code class="docutils literal notranslate"><span class="pre">&quot;example.com&quot;</span></code>, but <code class="docutils literal notranslate"><span class="pre">&quot;www.example.com&quot;</span></code> does not.  Domains that do
start with a dot are matched by more specific domains too. For example, both
<code class="docutils literal notranslate"><span class="pre">&quot;www.example.com&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;www.coyote.example.com&quot;</span></code> match <code class="docutils literal notranslate"><span class="pre">&quot;.example.com&quot;</span></code>
(but <code class="docutils literal notranslate"><span class="pre">&quot;example.com&quot;</span></code> itself does not).  IP addresses are an exception, and
must match exactly.  For example, if blocked_domains contains <code class="docutils literal notranslate"><span class="pre">&quot;***********&quot;</span></code>
and <code class="docutils literal notranslate"><span class="pre">&quot;.168.1.2&quot;</span></code>, *********** is blocked, but *********** is not.</p>
<p><a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> implements the following additional methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.blocked_domains">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">blocked_domains</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.blocked_domains" title="Link to this definition">¶</a></dt>
<dd><p>Return the sequence of blocked domains (as a tuple).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.set_blocked_domains">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">set_blocked_domains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">blocked_domains</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.set_blocked_domains" title="Link to this definition">¶</a></dt>
<dd><p>Set the sequence of blocked domains.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.is_blocked">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">is_blocked</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.is_blocked" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>domain</em> is on the blocklist for setting or receiving
cookies.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.allowed_domains">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">allowed_domains</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.allowed_domains" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, or the sequence of allowed domains (as a tuple).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.set_allowed_domains">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">set_allowed_domains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">allowed_domains</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.set_allowed_domains" title="Link to this definition">¶</a></dt>
<dd><p>Set the sequence of allowed domains, or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.is_not_allowed">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">is_not_allowed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.is_not_allowed" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>domain</em> is not on the allowlist for setting or receiving
cookies.</p>
</dd></dl>

<p><a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a> instances have the following attributes, which are
all initialised from the constructor arguments of the same name, and which may
all be assigned to.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.rfc2109_as_netscape">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">rfc2109_as_netscape</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.rfc2109_as_netscape" title="Link to this definition">¶</a></dt>
<dd><p>If true, request that the <a class="reference internal" href="#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookieJar</span></code></a> instance downgrade <span class="target" id="index-15"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookies
(ie. cookies received in a <em class="mailheader">Set-Cookie</em> header with a version
cookie-attribute of 1) to Netscape cookies by setting the version attribute of
the <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> instance to 0.  The default value is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, in which
case RFC 2109 cookies are downgraded if and only if <span class="target" id="index-16"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> handling is turned
off.  Therefore, RFC 2109 cookies are downgraded by default.</p>
</dd></dl>

<p>General strictness switches:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_domain">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_domain</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_domain" title="Link to this definition">¶</a></dt>
<dd><p>Don’t allow sites to set two-component domains with country-code top-level
domains like <code class="docutils literal notranslate"><span class="pre">.co.uk</span></code>, <code class="docutils literal notranslate"><span class="pre">.gov.uk</span></code>, <code class="docutils literal notranslate"><span class="pre">.co.nz</span></code>.etc.  This is far from perfect
and isn’t guaranteed to work!</p>
</dd></dl>

<p><span class="target" id="index-17"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> protocol strictness switches:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_rfc2965_unverifiable">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_rfc2965_unverifiable</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_rfc2965_unverifiable" title="Link to this definition">¶</a></dt>
<dd><p>Follow <span class="target" id="index-18"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> rules on unverifiable transactions (usually, an unverifiable
transaction is one resulting from a redirect or a request for an image hosted on
another site).  If this is false, cookies are <em>never</em> blocked on the basis of
verifiability</p>
</dd></dl>

<p>Netscape protocol strictness switches:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_ns_unverifiable">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_ns_unverifiable</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_ns_unverifiable" title="Link to this definition">¶</a></dt>
<dd><p>Apply <span class="target" id="index-19"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> rules on unverifiable transactions even to Netscape cookies.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_ns_domain">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_ns_domain</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_ns_domain" title="Link to this definition">¶</a></dt>
<dd><p>Flags indicating how strict to be with domain-matching rules for Netscape
cookies.  See below for acceptable values.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_ns_set_initial_dollar">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_ns_set_initial_dollar</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_ns_set_initial_dollar" title="Link to this definition">¶</a></dt>
<dd><p>Ignore cookies in Set-Cookie: headers that have names starting with <code class="docutils literal notranslate"><span class="pre">'$'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.strict_ns_set_path">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">strict_ns_set_path</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.strict_ns_set_path" title="Link to this definition">¶</a></dt>
<dd><p>Don’t allow setting cookies whose path doesn’t path-match request URI.</p>
</dd></dl>

<p><code class="xref py py-attr docutils literal notranslate"><span class="pre">strict_ns_domain</span></code> is a collection of flags.  Its value is constructed by
or-ing together (for example, <code class="docutils literal notranslate"><span class="pre">DomainStrictNoDots|DomainStrictNonDomain</span></code> means
both flags are set).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.DomainStrictNoDots">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">DomainStrictNoDots</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.DomainStrictNoDots" title="Link to this definition">¶</a></dt>
<dd><p>When setting cookies, the ‘host prefix’ must not contain a dot (eg.
<code class="docutils literal notranslate"><span class="pre">www.foo.bar.com</span></code> can’t set a cookie for <code class="docutils literal notranslate"><span class="pre">.bar.com</span></code>, because <code class="docutils literal notranslate"><span class="pre">www.foo</span></code>
contains a dot).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.DomainStrictNonDomain">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">DomainStrictNonDomain</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.DomainStrictNonDomain" title="Link to this definition">¶</a></dt>
<dd><p>Cookies that did not explicitly specify a <code class="docutils literal notranslate"><span class="pre">domain</span></code> cookie-attribute can only
be returned to a domain equal to the domain that set the cookie (eg.
<code class="docutils literal notranslate"><span class="pre">spam.example.com</span></code> won’t be returned cookies from <code class="docutils literal notranslate"><span class="pre">example.com</span></code> that had no
<code class="docutils literal notranslate"><span class="pre">domain</span></code> cookie-attribute).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.DomainRFC2965Match">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">DomainRFC2965Match</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.DomainRFC2965Match" title="Link to this definition">¶</a></dt>
<dd><p>When setting cookies, require a full <span class="target" id="index-20"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> domain-match.</p>
</dd></dl>

<p>The following attributes are provided for convenience, and are the most useful
combinations of the above flags:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.DomainLiberal">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">DomainLiberal</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.DomainLiberal" title="Link to this definition">¶</a></dt>
<dd><p>Equivalent to 0 (ie. all of the above Netscape domain strictness flags switched
off).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.DefaultCookiePolicy.DomainStrict">
<span class="sig-prename descclassname"><span class="pre">DefaultCookiePolicy.</span></span><span class="sig-name descname"><span class="pre">DomainStrict</span></span><a class="headerlink" href="#http.cookiejar.DefaultCookiePolicy.DomainStrict" title="Link to this definition">¶</a></dt>
<dd><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">DomainStrictNoDots|DomainStrictNonDomain</span></code>.</p>
</dd></dl>

</section>
<section id="cookie-objects">
<h2>Cookie Objects<a class="headerlink" href="#cookie-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> instances have Python attributes roughly corresponding to the
standard cookie-attributes specified in the various cookie standards.  The
correspondence is not one-to-one, because there are complicated rules for
assigning default values, because the <code class="docutils literal notranslate"><span class="pre">max-age</span></code> and <code class="docutils literal notranslate"><span class="pre">expires</span></code>
cookie-attributes contain equivalent information, and because <span class="target" id="index-21"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookies
may be ‘downgraded’ by <a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> from version 1 to version 0 (Netscape)
cookies.</p>
<p>Assignment to these attributes should not be necessary other than in rare
circumstances in a <a class="reference internal" href="#http.cookiejar.CookiePolicy" title="http.cookiejar.CookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">CookiePolicy</span></code></a> method.  The class does not enforce
internal consistency, so you should know what you’re doing if you do that.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.version">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#http.cookiejar.Cookie.version" title="Link to this definition">¶</a></dt>
<dd><p>Integer or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.  Netscape cookies have <a class="reference internal" href="#http.cookiejar.Cookie.version" title="http.cookiejar.Cookie.version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code></a> 0. <span class="target" id="index-22"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> and
<span class="target" id="index-23"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookies have a <code class="docutils literal notranslate"><span class="pre">version</span></code> cookie-attribute of 1.  However, note that
<a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> may ‘downgrade’ RFC 2109 cookies to Netscape cookies, in which
case <a class="reference internal" href="#http.cookiejar.Cookie.version" title="http.cookiejar.Cookie.version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code></a> is 0.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.name">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#http.cookiejar.Cookie.name" title="Link to this definition">¶</a></dt>
<dd><p>Cookie name (a string).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.value">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#http.cookiejar.Cookie.value" title="Link to this definition">¶</a></dt>
<dd><p>Cookie value (a string), or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.port">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">port</span></span><a class="headerlink" href="#http.cookiejar.Cookie.port" title="Link to this definition">¶</a></dt>
<dd><p>String representing a port or a set of ports (eg. ‘80’, or ‘80,8080’), or
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.domain">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">domain</span></span><a class="headerlink" href="#http.cookiejar.Cookie.domain" title="Link to this definition">¶</a></dt>
<dd><p>Cookie domain (a string).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.path">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#http.cookiejar.Cookie.path" title="Link to this definition">¶</a></dt>
<dd><p>Cookie path (a string, eg. <code class="docutils literal notranslate"><span class="pre">'/acme/rocket_launchers'</span></code>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.secure">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">secure</span></span><a class="headerlink" href="#http.cookiejar.Cookie.secure" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if cookie should only be returned over a secure connection.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.expires">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">expires</span></span><a class="headerlink" href="#http.cookiejar.Cookie.expires" title="Link to this definition">¶</a></dt>
<dd><p>Integer expiry date in seconds since epoch, or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.  See also the
<a class="reference internal" href="#http.cookiejar.Cookie.is_expired" title="http.cookiejar.Cookie.is_expired"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_expired()</span></code></a> method.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.discard">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">discard</span></span><a class="headerlink" href="#http.cookiejar.Cookie.discard" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if this is a session cookie.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.comment">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">comment</span></span><a class="headerlink" href="#http.cookiejar.Cookie.comment" title="Link to this definition">¶</a></dt>
<dd><p>String comment from the server explaining the function of this cookie, or
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.comment_url">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">comment_url</span></span><a class="headerlink" href="#http.cookiejar.Cookie.comment_url" title="Link to this definition">¶</a></dt>
<dd><p>URL linking to a comment from the server explaining the function of this cookie,
or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.rfc2109">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">rfc2109</span></span><a class="headerlink" href="#http.cookiejar.Cookie.rfc2109" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if this cookie was received as an <span class="target" id="index-24"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2109.html"><strong>RFC 2109</strong></a> cookie (ie. the cookie
arrived in a <em class="mailheader">Set-Cookie</em> header, and the value of the Version
cookie-attribute in that header was 1).  This attribute is provided because
<a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a> may ‘downgrade’ RFC 2109 cookies to Netscape cookies, in
which case <a class="reference internal" href="#http.cookiejar.Cookie.version" title="http.cookiejar.Cookie.version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code></a> is 0.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.port_specified">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">port_specified</span></span><a class="headerlink" href="#http.cookiejar.Cookie.port_specified" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if a port or set of ports was explicitly specified by the server (in the
<em class="mailheader">Set-Cookie</em> / <em class="mailheader">Set-Cookie2</em> header).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.domain_specified">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">domain_specified</span></span><a class="headerlink" href="#http.cookiejar.Cookie.domain_specified" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if a domain was explicitly specified by the server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.domain_initial_dot">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">domain_initial_dot</span></span><a class="headerlink" href="#http.cookiejar.Cookie.domain_initial_dot" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the domain explicitly specified by the server began with a dot
(<code class="docutils literal notranslate"><span class="pre">'.'</span></code>).</p>
</dd></dl>

<p>Cookies may have additional non-standard cookie-attributes.  These may be
accessed using the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.has_nonstandard_attr">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">has_nonstandard_attr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.Cookie.has_nonstandard_attr" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if cookie has the named cookie-attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.get_nonstandard_attr">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">get_nonstandard_attr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.Cookie.get_nonstandard_attr" title="Link to this definition">¶</a></dt>
<dd><p>If cookie has the named cookie-attribute, return its value. Otherwise, return
<em>default</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.set_nonstandard_attr">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">set_nonstandard_attr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.Cookie.set_nonstandard_attr" title="Link to this definition">¶</a></dt>
<dd><p>Set the value of the named cookie-attribute.</p>
</dd></dl>

<p>The <a class="reference internal" href="#http.cookiejar.Cookie" title="http.cookiejar.Cookie"><code class="xref py py-class docutils literal notranslate"><span class="pre">Cookie</span></code></a> class also defines the following method:</p>
<dl class="py method">
<dt class="sig sig-object py" id="http.cookiejar.Cookie.is_expired">
<span class="sig-prename descclassname"><span class="pre">Cookie.</span></span><span class="sig-name descname"><span class="pre">is_expired</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">now</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#http.cookiejar.Cookie.is_expired" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if cookie has passed the time at which the server requested it should
expire.  If <em>now</em> is given (in seconds since the epoch), return whether the
cookie has expired at the specified time.</p>
</dd></dl>

</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>The first example shows the most common usage of <a class="reference internal" href="#module-http.cookiejar" title="http.cookiejar: Classes for automatic handling of HTTP cookies."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">http.cookiejar</span><span class="o">,</span> <span class="nn">urllib.request</span>
<span class="n">cj</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">cookiejar</span><span class="o">.</span><span class="n">CookieJar</span><span class="p">()</span>
<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">(</span><span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">HTTPCookieProcessor</span><span class="p">(</span><span class="n">cj</span><span class="p">))</span>
<span class="n">r</span> <span class="o">=</span> <span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;http://example.com/&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>This example illustrates how to open a URL using your Netscape, Mozilla, or Lynx
cookies (assumes Unix/Netscape convention for location of the cookies file):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span><span class="o">,</span> <span class="nn">http.cookiejar</span><span class="o">,</span> <span class="nn">urllib.request</span>
<span class="n">cj</span> <span class="o">=</span> <span class="n">http</span><span class="o">.</span><span class="n">cookiejar</span><span class="o">.</span><span class="n">MozillaCookieJar</span><span class="p">()</span>
<span class="n">cj</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">expanduser</span><span class="p">(</span><span class="s2">&quot;~&quot;</span><span class="p">),</span> <span class="s2">&quot;.netscape&quot;</span><span class="p">,</span> <span class="s2">&quot;cookies.txt&quot;</span><span class="p">))</span>
<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">(</span><span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">HTTPCookieProcessor</span><span class="p">(</span><span class="n">cj</span><span class="p">))</span>
<span class="n">r</span> <span class="o">=</span> <span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;http://example.com/&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The next example illustrates the use of <a class="reference internal" href="#http.cookiejar.DefaultCookiePolicy" title="http.cookiejar.DefaultCookiePolicy"><code class="xref py py-class docutils literal notranslate"><span class="pre">DefaultCookiePolicy</span></code></a>. Turn on
<span class="target" id="index-25"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a> cookies, be more strict about domains when setting and returning
Netscape cookies, and block some domains from setting cookies or having them
returned:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="kn">from</span> <span class="nn">http.cookiejar</span> <span class="kn">import</span> <span class="n">CookieJar</span><span class="p">,</span> <span class="n">DefaultCookiePolicy</span>
<span class="n">policy</span> <span class="o">=</span> <span class="n">DefaultCookiePolicy</span><span class="p">(</span>
    <span class="n">rfc2965</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">strict_ns_domain</span><span class="o">=</span><span class="n">Policy</span><span class="o">.</span><span class="n">DomainStrict</span><span class="p">,</span>
    <span class="n">blocked_domains</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;ads.net&quot;</span><span class="p">,</span> <span class="s2">&quot;.ads.net&quot;</span><span class="p">])</span>
<span class="n">cj</span> <span class="o">=</span> <span class="n">CookieJar</span><span class="p">(</span><span class="n">policy</span><span class="p">)</span>
<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">(</span><span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">HTTPCookieProcessor</span><span class="p">(</span><span class="n">cj</span><span class="p">))</span>
<span class="n">r</span> <span class="o">=</span> <span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;http://example.com/&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a><ul>
<li><a class="reference internal" href="#cookiejar-and-filecookiejar-objects">CookieJar and FileCookieJar Objects</a></li>
<li><a class="reference internal" href="#filecookiejar-subclasses-and-co-operation-with-web-browsers">FileCookieJar subclasses and co-operation with web browsers</a></li>
<li><a class="reference internal" href="#cookiepolicy-objects">CookiePolicy Objects</a></li>
<li><a class="reference internal" href="#defaultcookiepolicy-objects">DefaultCookiePolicy Objects</a></li>
<li><a class="reference internal" href="#cookie-objects">Cookie Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="http.cookies.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookies</span></code> — HTTP state management</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xmlrpc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xmlrpc</span></code> — XMLRPC server and client modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/http.cookiejar.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xmlrpc.html" title="xmlrpc — XMLRPC server and client modules"
             >next</a> |</li>
        <li class="right" >
          <a href="http.cookies.html" title="http.cookies — HTTP state management"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.cookiejar</span></code> — Cookie handling for HTTP clients</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>