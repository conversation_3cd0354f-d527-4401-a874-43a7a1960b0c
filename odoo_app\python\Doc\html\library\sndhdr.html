<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sndhdr — Determine type of sound file" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sndhdr.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/sndhdr.py The sndhdr provides utility functions which attempt to determine the type of sound data which is in a file. When these functions are able to determine what type of sound ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/sndhdr.py The sndhdr provides utility functions which attempt to determine the type of sound data which is in a file. When these functions are able to determine what type of sound ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sndhdr — Determine type of sound file &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="spwd — The shadow password database" href="spwd.html" />
    <link rel="prev" title="pipes — Interface to shell pipelines" href="pipes.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sndhdr.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pipes.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pipes</span></code> — Interface to shell pipelines</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="spwd.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">spwd</span></code> — The shadow password database</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sndhdr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="spwd.html" title="spwd — The shadow password database"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pipes.html" title="pipes — Interface to shell pipelines"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sndhdr</span></code> — Determine type of sound file</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sndhdr">
<span id="sndhdr-determine-type-of-sound-file"></span><h1><a class="reference internal" href="#module-sndhdr" title="sndhdr: Determine type of a sound file. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sndhdr</span></code></a> — Determine type of sound file<a class="headerlink" href="#module-sndhdr" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sndhdr.py">Lib/sndhdr.py</a></p>
<div class="deprecated-removed" id="index-0">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-sndhdr" title="sndhdr: Determine type of a sound file. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sndhdr</span></code></a> module is deprecated
(see <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#sndhdr"><strong>PEP 594</strong></a> for details and alternatives).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-sndhdr" title="sndhdr: Determine type of a sound file. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sndhdr</span></code></a> provides utility functions which attempt to determine the type
of sound data which is in a file.  When these functions are able to determine
what type of sound data is stored in a file, they return a
<a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a>, containing five attributes: (<code class="docutils literal notranslate"><span class="pre">filetype</span></code>,
<code class="docutils literal notranslate"><span class="pre">framerate</span></code>, <code class="docutils literal notranslate"><span class="pre">nchannels</span></code>, <code class="docutils literal notranslate"><span class="pre">nframes</span></code>, <code class="docutils literal notranslate"><span class="pre">sampwidth</span></code>). The value for <em>type</em>
indicates the data type and will be one of the strings <code class="docutils literal notranslate"><span class="pre">'aifc'</span></code>, <code class="docutils literal notranslate"><span class="pre">'aiff'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'au'</span></code>, <code class="docutils literal notranslate"><span class="pre">'hcom'</span></code>, <code class="docutils literal notranslate"><span class="pre">'sndr'</span></code>, <code class="docutils literal notranslate"><span class="pre">'sndt'</span></code>, <code class="docutils literal notranslate"><span class="pre">'voc'</span></code>, <code class="docutils literal notranslate"><span class="pre">'wav'</span></code>, <code class="docutils literal notranslate"><span class="pre">'8svx'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'sb'</span></code>, <code class="docutils literal notranslate"><span class="pre">'ub'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'ul'</span></code>.  The <em>sampling_rate</em> will be either the actual
value or <code class="docutils literal notranslate"><span class="pre">0</span></code> if unknown or difficult to decode.  Similarly, <em>channels</em> will be
either the number of channels or <code class="docutils literal notranslate"><span class="pre">0</span></code> if it cannot be determined or if the
value is difficult to decode.  The value for <em>frames</em> will be either the number
of frames or <code class="docutils literal notranslate"><span class="pre">-1</span></code>.  The last item in the tuple, <em>bits_per_sample</em>, will either
be the sample size in bits or <code class="docutils literal notranslate"><span class="pre">'A'</span></code> for A-LAW or <code class="docutils literal notranslate"><span class="pre">'U'</span></code> for u-LAW.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sndhdr.what">
<span class="sig-prename descclassname"><span class="pre">sndhdr.</span></span><span class="sig-name descname"><span class="pre">what</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sndhdr.what" title="Link to this definition">¶</a></dt>
<dd><p>Determines the type of sound data stored in the file <em>filename</em> using
<a class="reference internal" href="#sndhdr.whathdr" title="sndhdr.whathdr"><code class="xref py py-func docutils literal notranslate"><span class="pre">whathdr()</span></code></a>.  If it succeeds, returns a namedtuple as described above, otherwise
<code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Result changed from a tuple to a namedtuple.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sndhdr.whathdr">
<span class="sig-prename descclassname"><span class="pre">sndhdr.</span></span><span class="sig-name descname"><span class="pre">whathdr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sndhdr.whathdr" title="Link to this definition">¶</a></dt>
<dd><p>Determines the type of sound data stored in a file based on the file  header.
The name of the file is given by <em>filename</em>.  This function returns a namedtuple as
described above on success, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Result changed from a tuple to a namedtuple.</p>
</div>
</dd></dl>

<p>The following sound header types are recognized, as listed below with the return value
from <a class="reference internal" href="#sndhdr.whathdr" title="sndhdr.whathdr"><code class="xref py py-func docutils literal notranslate"><span class="pre">whathdr()</span></code></a>: and <a class="reference internal" href="#sndhdr.what" title="sndhdr.what"><code class="xref py py-func docutils literal notranslate"><span class="pre">what()</span></code></a>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value</p></th>
<th class="head"><p>Sound header format</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'aifc'</span></code></p></td>
<td><p>Compressed Audio Interchange Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'aiff'</span></code></p></td>
<td><p>Audio Interchange Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'au'</span></code></p></td>
<td><p>Au Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'hcom'</span></code></p></td>
<td><p>HCOM Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'sndt'</span></code></p></td>
<td><p>Sndtool Sound Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'voc'</span></code></p></td>
<td><p>Creative Labs Audio Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'wav'</span></code></p></td>
<td><p>Waveform Audio File Format Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'8svx'</span></code></p></td>
<td><p>8-Bit Sampled Voice Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'sb'</span></code></p></td>
<td><p>Signed Byte Audio Data Files</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'ub'</span></code></p></td>
<td><p>UB Files</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'ul'</span></code></p></td>
<td><p>uLAW Audio Files</p></td>
</tr>
</tbody>
</table>
<dl class="py data">
<dt class="sig sig-object py" id="sndhdr.tests">
<span class="sig-prename descclassname"><span class="pre">sndhdr.</span></span><span class="sig-name descname"><span class="pre">tests</span></span><a class="headerlink" href="#sndhdr.tests" title="Link to this definition">¶</a></dt>
<dd><p>A list of functions performing the individual tests.  Each function takes two
arguments: the byte-stream and an open file-like object. When <a class="reference internal" href="#sndhdr.what" title="sndhdr.what"><code class="xref py py-func docutils literal notranslate"><span class="pre">what()</span></code></a> is
called with a byte-stream, the file-like object will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>The test function should return a string describing the image type if the test
succeeded, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if it failed.</p>
</dd></dl>

<p>Example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">sndhdr</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">imghdr</span><span class="o">.</span><span class="n">what</span><span class="p">(</span><span class="s1">&#39;bass.wav&#39;</span><span class="p">)</span>
<span class="go">&#39;wav&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">imghdr</span><span class="o">.</span><span class="n">whathdr</span><span class="p">(</span><span class="s1">&#39;bass.wav&#39;</span><span class="p">)</span>
<span class="go">&#39;wav&#39;</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pipes.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pipes</span></code> — Interface to shell pipelines</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="spwd.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">spwd</span></code> — The shadow password database</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sndhdr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="spwd.html" title="spwd — The shadow password database"
             >next</a> |</li>
        <li class="right" >
          <a href="pipes.html" title="pipes — Interface to shell pipelines"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sndhdr</span></code> — Determine type of sound file</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>