# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oauth
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 08:26+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.providers
msgid "- or -"
msgstr "- alebo -"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Príručka"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "Prístup zamietnutý"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr "Povoliť používateľom prihlásiť sa cez Google"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr "Povoliť používateľom prihlásiť sa s účtom Google"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__enabled
msgid "Allowed"
msgstr "Povolené"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__auth_endpoint
msgid "Authorization URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__css_class
msgid "CSS class"
msgstr "CSS trieda"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_client_id
msgid "Client ID"
msgstr "ID klienta"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr "ID klienta:"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__data_endpoint
msgid "Data Endpoint"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentácia"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr "Autentifikácia Google"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__id
msgid "ID"
msgstr "ID"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_auth_oauth_provider__body
msgid "Link text in Login Dialog"
msgstr "Text odkazu v dialógovom okne prihlásenia"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_facebook
msgid "Log in with Facebook"
msgstr "Prihlásiť sa cez Facebook"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_google
msgid "Log in with Google"
msgstr "Prihlásiť sa cez Google"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_openerp
msgid "Log in with Odoo.com"
msgstr "Prihlásiť sa cez Odoo.com"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__body
msgid "Login button label"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_access_token
msgid "OAuth Access Token"
msgstr "OAuth prístupový token"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_provider_id
msgid "OAuth Provider"
msgstr "OAuth poskytovateľ"

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr "OAuth poskytovatelia"

#. module: auth_oauth
#: model:ir.model.constraint,message:auth_oauth.constraint_res_users_uniq_users_oauth_provider_oauth_uid
msgid "OAuth UID must be unique per provider"
msgstr "OAuth UID musí byť unikátne pre poskytovateľa"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_uid
msgid "OAuth User ID"
msgstr "OAuth ID používateľa"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "OAuth2 poskytovateľ"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr "Oauth "

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users__oauth_uid
msgid "Oauth Provider user_id"
msgstr "Oauth user_id poskytovateľa"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__name
msgid "Provider name"
msgstr "Názov poskytovateľa"

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr "Poskytovatelia"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__scope
msgid "Scope"
msgstr "Rozsah"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__server_uri_google
msgid "Server uri"
msgstr "Server uri"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Sign up is not allowed on this database."
msgstr "Registrácia nie je povolená v tejto databáze."

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "System Parameter"
msgstr "Systémový parameter"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "User"
msgstr "Užívateľ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""
"Nemáte prístup do tejto databázy, alebo vaša pozvánka vypršala. Prosím "
"požiadajte o pozvánku a uistite sa že použijete odkaz vo vašom pozývacom "
"emaile."

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr "arch"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr "napr. 1234-xyz.apps.googleusercontent.com"
