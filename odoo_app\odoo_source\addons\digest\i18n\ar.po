# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Check our Documentation"
msgstr "<i class=\"fa fa-arrow-right\"/> ألقِ نظرة على التوثيقات "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">تقرير مفتوح</span> "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">أودو</span> "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr "<span style=\"color: #8f8f8f;\">إلغاء الاشتراك</span> "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "تفعيل"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Activated"
msgstr "مفعل"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""
"قم بإدراج المستخدمين الجدد في قائمة مستلمي رسائل البريد الدورية بمقاييس "
"رئيسية "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "المجموعة المصرح لها "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "الحقول المتاحة"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr "اختر المقاييس التي تعنيك "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "الشركة "

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "تهيئة رسائل البريد التلخيصية"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "اتصل"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "المستخدمين المتصلين "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "العملة"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Custom"
msgstr "مُخصص"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "يوميًا"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate"
msgstr "تعطيل"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Deactivated"
msgstr "معطل"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "الموجز "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "البريد الإلكتروني الموجز "

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "رسائل البريد الإلكتروني الموجزة "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "اشتراكات الرسائل الموجزة "

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "نصائح الموجز "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Title"
msgstr "عنوان الموجز "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Documentation"
msgstr "التوثيق"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Email Address"
msgstr "عنوان البريد الإلكتروني"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "عام"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "التجميع حسب "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""
"ألديك أي أسئلة حول مستند ما؟ اضغط على صورة المستخدم المسؤول لبدء محادثة. إذا"
" كانت هناك نقطة خضراء بجانب الصورة الرمزية لذلك المستخدم، هذا يعني أنه متصل "
"الآن. "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "المُعرف"

#. module: digest
#. odoo-python
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr "الدورية التي تم تعيينها للموجز غير صالحة "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "مستخدم مشترك "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "موجز المؤشرات الرئيسية للأداء "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "نصيحة موجز المؤشرات الرئيسية للأداء "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "نصائح موجز المؤشرات الرئيسية للأداء "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "المؤشرات الرئيسية للأداء "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "قيمة المؤشرات الرئيسية للأداء لعدد الرسائل "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "قيمة المؤشرات الرئيسية للأداء لعدد المستخدمين المتصلين "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr "آخر 24 ساعة "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr "آخر 30 يوم"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr "آخر 7 أيام"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages Sent"
msgstr "الرسائل المرسلة"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "شهرياً"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "الاسم"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"يتم إضافة المستخدمين الجدد في قائمة مستلمي رسائل البريد الإلكتروني الموجزة "
"التالية تلقائياً. "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Mailing Date"
msgstr "تاريخ الإرسال التالي"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "أودو للهاتف المحمول "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "الوتيرة "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr " أتفضل أن تكون النظرة العامة شاملة أكثر؟ "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""
"اضغط على زر ALT في أي شاشة لإبراز الاختصارات لكل زر في الشاشة. يكون ذلك "
"مفيداً عند معالجة عدة مستندات في دفعة واحدة. "

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "المستلمين"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr ""
"تمكن من إدارة أعمالك من أي مكان باستخدام تطبيق <b>أودو للهاتف المحمول</b>. "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "إرسال الآن"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "أُرسل من قِبَل "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "الإحصائيات"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "الحالة"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr "التبديل للموجزات الأسبوعية "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "وصف النصيحة "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "نصيحة: الآلة الحاسبة في أودو "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "نصيحة: اضغط على الصورة الرمزية للدردشة مع مستخدم "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr " نصيحة: كيف تقوم بتنبيه المستخدمين في الملاحظات الداخلية؟ "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "نصيحة: المعرفة هي أساس القوة "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "نصيحة: قم بتسريع سير عملك عن طريق الاختصارات "

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "نصيحة: الآلة الحاسبة في أودو "

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "نصيحة: اضغط على الصورة الرمزية للدردشة مع مستخدم "

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "نصيحة: كيف تقوم بتنبيه المستخدمين في الملاحظات الداخلية؟ "

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "نصيحة: المعرفة هي أساس القوة "

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "نصيحة: قم بتسريع سير عملك عن طريق الاختصارات "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "Title"
msgstr "العنوان"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""
"اكتب \"@\" لتنبيه شخص في رسالة، أو \"#\" لربط أحدهم بقناة. جرب تنبيه "
"@OdooBot لاختبار هذه الخاصية. "

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr "يستخدم لعرض ملاحظة موجزة في قالب البريد الإلكتروني حسب الطلب "

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "المستخدمين الذين استلموا هذه النصيحة بالفعل "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Want to add your own KPIs?<br/>"
msgstr "أتود إضافة المؤشرات الرئيسية للأداء الخاصة بك؟<br/> "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr "أترغب في تخصيص رسالة البريد الإلكتروني هذه؟ "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""
"لقد لاحظنا أنك لم تكن متصلاً أبداً خلال الأيام القليلة الماضية. لقد قمنا "
"بتغيير تفضيلك تلقائياً إلى موجزات %(new_perioridicy_str)s. "

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""
"عندما تقوم بتحرير رقم ما، بإمكانك استخدام معادلات عن طريق كتابة رمز `=`. "
"يكون ذلك مفيداً عند احتساب الهوامش أو الخصومات في عروض الأسعار أو أوامر "
"البيع أو الفواتير. "

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"عند متابعة المستندات، استخدم أيقونة القلم لصقل المعلومات التي تود الحصول عليها. \n"
"قم بمتابعة مشروع / فريق مبيعات لتبقى على اطلاع بمهام المشروع / فرص الفريق. "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from:<br/>"
msgstr "ألغيت اشتراكك بنجاح من: <br/> "

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "موجزك الدوري من أودو "

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "مثال: موجزك الأسبوعي "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr "شهرياً "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr "ربع سنوي "

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr "أسبوعياً "
