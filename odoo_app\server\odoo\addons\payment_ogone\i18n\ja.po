# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Jun<PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_userid
msgid "API User ID"
msgstr "APIユーザID"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_password
msgid "API User Password"
msgstr "APIユーザパスワード"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__code
msgid "Code"
msgstr "コード"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "APIへの接続を確立できませんでした。"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_hash_function
msgid "Hash function"
msgstr "ハッシュ機能"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "参照に一致する取引が見つかりません%s。"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__code__ogone
#: model:payment.provider,name:payment_ogone.payment_provider_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_provider
msgid "Payment Provider"
msgstr "決済プロバイダー"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "決済トランザクション"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "無効な支払ステータスのデータを受信しました: %s"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHAキーIN"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_provider__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHAキーOUT"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha1
msgid "SHA1"
msgstr "SHA1"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha256
msgid "SHA256"
msgstr "SHA256"

#. module: payment_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_provider__ogone_hash_function__sha512
msgid "SHA512"
msgstr "SHA512"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr "支払いの詳細を保存することは、将来の使用のために必要です。"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr "OgoneでAPIユーザを識別するためにのみ使用されるID"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr "Ogoneのアカウントを識別するためにのみ使用されるID"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "APIとのやり取りに失敗しました。"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr "支払が却下されました: %s"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "この決済プロバイダーのテクニカルコード。"

#. module: payment_ogone
#. odoo-python
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "取引はトークンにリンクしていません。"

#. module: payment_ogone
#: model_terms:ir.ui.view,arch_db:payment_ogone.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Stripe</strong>."
msgstr ""
"このプロバイダーは非推奨です。\n"
"　　　　　無効にし、<strong>Stripe</strong>に移行することを検討して下さい。"

#. module: payment_ogone
#: model_terms:payment.provider,auth_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been authorized."
msgstr "お支払いは承認されました。"

#. module: payment_ogone
#: model_terms:payment.provider,cancel_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been cancelled."
msgstr "お支払いは取消されました。"

#. module: payment_ogone
#: model_terms:payment.provider,pending_msg:payment_ogone.payment_provider_ogone
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "お支払いは正常に処理されましたが、承認待ちです。"

#. module: payment_ogone
#: model_terms:payment.provider,done_msg:payment_ogone.payment_provider_ogone
msgid "Your payment has been successfully processed."
msgstr "お支払いは正常に処理されました。"
