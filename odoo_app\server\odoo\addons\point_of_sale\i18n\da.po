# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"This issue occurs because the quantity becomes zero after rounding during the conversion. To fix this, adjust the conversion factors or rounding method to ensure that even the smallest quantity in the original unit does not round down to zero in the target unit."
msgstr ""
"\n"
"Dette problem opstår, fordi mængden bliver nul efter afrunding under konverteringen. For at løse dette skal du justere konverteringsfaktorerne eller afrundingsmetoden for at sikre, at selv den mindste mængde i den oprindelige enhed ikke rundes ned til nul i målenheden."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid " - From \"%s\" to \"%s\""
msgstr " - Fra \"%s\" til \"%s\""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - closing"
msgstr "- lukning"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - opening"
msgstr "- åbning"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "KREDITNOTA"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "Brug en printer forbundet til IoT boksen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "% Disc"
msgstr "% Rabat"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (ikke anvendt)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "%(vatLabel)s: %(vatId)s"
msgstr "%(vatLabel)s: %(vatId)s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s POS betaling på %s i %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "%s customer(s) found for \"%s\"."
msgstr "%s kunde(r) fundet for \"%s\"."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "%s fiscal position(s) added to the configuration."
msgstr "%s bogførings gruppe(r) tilføjet til konfigurationen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order?"
msgstr ""
"%s har et samlet beløb på %s, er du sikker på du vil slette denne ordre?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "%s product(s) found for \"%s\"."
msgstr "%s produkt(er) fundet for \"%s\"."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s untaxed"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s with %s"
msgstr "%s med %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(GENDANNELSE FOR %(session)s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(ved åbning)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr "+ Ny butik"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "/pos/ticket and use the code below to request an invoice online"
msgstr "/pos/ticket og brug koden nedenfor til at anmode om en faktura online"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "0.00"
msgstr "0.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr "00014-001-0001"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10"
msgstr "10"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "100.00"
msgstr "100.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "1000"
msgstr "1000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000"
msgstr "10000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000.00"
msgstr "10000.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "123.45"
msgstr "123.45"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "1234567890"
msgstr "1234567890"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "2-03-2000 9:00 AM"
msgstr "2-03-2000 9:00 AM"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "45"
msgstr "45"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid ""
"5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr ""
"5-cifret alfanumerisk kode, der skal bruges af portalbrugeren til at anmode "
"om en faktura"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "5.00"
msgstr "5.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "50.00"
msgstr "50.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "567789"
msgstr "567789"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "95.00"
msgstr "95.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "987657"
msgstr "987657"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "99.99"
msgstr "99.99"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-"
"label=\"Indkøbskurv\" title=\"Indkøbskurv\"/> "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> Redigér"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>How to manage tax-included prices"
msgstr ""
"<i class=\"oi oi-fw oi-arrow-right\"/>Sådan håndterer du priser inkl. moms"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"<p>Dear %(client_name)s,<br/>Here is your electronic ticket for the "
"%(pos_name)s. </p>"
msgstr ""
"<p>Kære %(client_name)s,<br/>Her er din elektroniske billet for "
"%(pos_name)s. </p>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p>Klar til at tage et kik på <b>POS brugerfladen</b>? Lad os begynde vores "
"første session.</p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<span class=\"o_form_label\">Stregkoder</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Cash Register</span>"
msgstr "<span class=\"o_stat_text\">Kasseapparat</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Journal Items</span>"
msgstr "<span class=\"o_stat_text\">Journalartikler</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Spring forhåndsvisning over</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "<span invisible=\"is_total_cost_computed\">TBD</span>"
msgstr "<span invisible=\"is_total_cost_computed\">TBD</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Balance</span>"
msgstr "<span>Balance</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Closing</span>"
msgstr "<span>Lukning</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rapportering</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Visning</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; Betalingsterminaler</strong>\n"
"                                    for at installere en betalingsterminal og lave en fuldt integreret betalingsmetode."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>Rabatbeløb</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr "<strong>Beløber til:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Config names</strong>"
msgstr "<strong>Konfigurer navne</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>End of session note:</strong>"
msgstr "<strong>Slutnotat til session:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong>Antal rabatter</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>Åbning af notat fra sessionen:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "<strong>Source Invoice:</strong>"
msgstr "<strong>Kildefaktura:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Ved at klikke \"bekræft\", validerer du betalingen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "Et navn på kunden er påkrævet"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"En global unik identifikation for denne pos konfiguration, brugt til at "
"forhindre konflikter i klient-genererede data."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Et sekvens nummer der forøges hver gang, en bruger genoptager pos sessionen"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Et nummer, der øges med hver ordre"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"En session er en tidsperiode - typisk én dag - hvori du sælger via et Point "
"of Sale."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"En session er i øjeblikket åben for dette PoS. Visse idnstillinger kan kun "
"ændres efter sessionen er lukket."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Et unikt ekspeditionsnummer for ordren"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr ""
"En kort tekst, som vil bliver indsat som sidefod på den printede kvittering."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr ""
"En kort tekst, der vil blive indsat som sidehoved på den printede "
"kvittering."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:point_of_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Et opbevaringsvare er et produkt for hvilket du kan administrere lager. Inventar appliktionen skal være installeret.\n"
"En forbrugsvare er et produkt hvor lageret ikke er administreret.\n"
"En tjeneste er et ikke-materielt produkt du tilbyder."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"A valid product already exists for Point of Sale. Therefore, demonstration "
"products cannot be loaded."
msgstr ""
"Der findes allerede et gyldigt produkt til salgsstedet. Derfor kan "
"demonstrationsprodukter ikke indlæses."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "ALL POS"
msgstr "ALLE POS"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "BELØB"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr ""
"Acceptér drikkepenge fra kunder eller konvertér deres byttepenge til "
"drikkepenge"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a PayTM payment terminal"
msgstr "Accepter betalinger med en PayTM betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "Accepter betalinger med en Six betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr "Accepter betalinger med en Stripe betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Acceptér betalinger med en Vantiv betalingsterminal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Acceptér betalinger med en Adyen betalingsterminal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr "Adgangsadvarsel"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Konto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Kontant afrunding"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontoplansskabelon"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "Kontobevægelse"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""
"Kontobetalinger, der repræsenterer aggregerede og bankopdelte betalinger."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Regnskab"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Regnskabs journal som bruges til at oprette fakturaer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"Regnskabsjournal som anvendes til at bogføre journalbilag fra POS sessioner "
"og fakturabetalinger."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktiv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitetstype ikon"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#, python-format
msgid "Add"
msgstr "Tilføj"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#, python-format
msgid "Add Customer Note"
msgstr "Tilføj kundenote"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Add Tip"
msgstr "Tilføj drikkepenge"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Add a 5-digit code on the receipt to allow the user to request the invoice "
"for an order on the portal."
msgstr ""
"Tilføj en 5-cifret kode på kvitteringen for at give brugeren mulighed for at"
" anmode om fakturaen for en ordre på portalen."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid ""
"Add a QR code on the ticket, which the user can scan to request the invoice "
"linked to its order."
msgstr ""
"Tilføj en QR-kode på billetten, som brugeren kan scanne for at anmode om den"
" faktura, der er knyttet til sin ordre."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Add a closing note..."
msgstr "Tilføj en afsluttende bemærkning..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr "Tilføj en brugertilpasset besked til sidehoved og -fod"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Add a customer"
msgstr "Tilføj en kunde"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Tilføje en ny betalingsmetode i PoS"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr "Tilføj en ny resturant bestillings printer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.xml:0
#, python-format
msgid "Add a note..."
msgstr "Tilføj et notat..."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Add an opening note..."
msgstr "Tilføj en åbnings bemærkning..."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Add to order"
msgstr "Tilføj til ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr "Yderligere nødvendige oplysninger:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr "Yderligere nødvendige faktureringsoplysninger:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr "Yderligere nødvendige brugeroplysninger:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Address"
msgstr "Adresse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr "Tilføjer en knap for at indstille en global rabat"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrator"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr "Avanceret Kontant Kontrol"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Adyen betalingsterminal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "All active orders"
msgstr "Alle aktive ordrer"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Alle tilgængelige prislister skal være i den samme valuta som virksomheden "
"eller som Salgsjournalen, der anvendes i denne kasse, hvis du anvender "
"applikationen Regnskab."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Alle betalingsmetoder skal være i den samme valuta som Salgsjournalen eller "
"virksomhedsvalutaen, hvis det ikke er indstillet."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Alle salgslinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr "Tillad Send senere"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr "Tillad kasserere at angive en rabat pr. linje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr "Tillad adgang til hinandens aktive ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr "Tillad at logge og skifte mellem udvalgte medarbejdere"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr "Tilladt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""
"Giver mulighed for at vide, om alle de samlede omkostninger for "
"ordrelinjerne allerede er blevet beregnet"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr ""
"Giver mulighed for at vide, om de samlede omkostninger allerede er blevet "
"beregnet eller ej"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
#, python-format
msgid "Amount"
msgstr "Beløb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Autoriseret beløbsforskel"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "Beløb til afstemning"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Beløb i alt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"Der opstod en fejl, da sessionen blev forsøgt lukket.\n"
"Du vil blive omdirigeret til back-end, hvor du manuelt kan lukke sessionen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"Der forekom en fejl ved indlæsning af produkt priser. Forsikre at alle "
"prislister er tilgængelige i POS'en."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/main.js:0
#, python-format
msgid "An error occurred while loading the Point of Sale: \n"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "En intern identifikation af kassen."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr "En intern identifikation af printeren"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "En anden ekspedition er allerede åbnet for den kasse."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Arkiveret"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Er du sikker på at kunden vil betale"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Hurtigst muligt"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (faster)"
msgstr "Ved sessionens afslutning (hurtigere)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"Ved sessionens afslutning: Et pluk oprettes for hele sessionen når den afsluttes\n"
"I realtid: Hver ordre afsendt til serveren opretter dets eget pluk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Egenskaber"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr "Autoriseret difference"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr "Autovalider terminalbetaling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "Auto-genereret session for forladte ordre, ignoreret i begrænsninger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Automatisk udskrift af kvittering"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Åben pengeskuffen automatisk."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr "Validerer automatisk ordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr "Validerer automatisk ordrer betalt med en betalingsterminal."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr "Til rådighed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Tilgængelige PoS Produkt kategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr "Tilgængelige prislister"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "Tilgængelig på POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Gennemsnitspris"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr "Tilbage"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Back"
msgstr "Tilbage"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Backend"
msgstr "Backend"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_background_image_1920
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_background_image_1920
msgid "Background Image"
msgstr "Baggrundsbillede"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Badge ID"
msgstr "ID skilt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Balance"
msgstr "Balance"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "Bank"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "Bank Betalinger"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Kontoudtogslinje"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Barcode"
msgstr "Stregkode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Stregkode plan"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Stregkode regler"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Stregkode scanner"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "Stregkode scanner/kortlæser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Base"
msgstr "Basis"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Basis beløb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "Før Luk Difference"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr "Faktureringsadresse:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr "Regninger og kvitteringer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Boost dit salg med flere slags programmer: Kuponer, Kampagner, Gavekort, "
"Loyalitet. Der kan opstilles specifikke betingelser (produkter, kunder, "
"minimumskøbsbeløb, periode). Belønninger kan være rabatter (% eller beløb) "
"eller gratis produkter."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Buffer:"
msgstr "Buffer:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "Omgå browser printning og udprintning via hardware proxy'en."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "CANCELLED"
msgstr "ANNULLERET"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "CASH"
msgstr "KONTANT"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "BYTTEPENGE"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Can't change customer"
msgstr "Kan ikke skifte kunde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Annullér"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "Annuller betalingsanmodning"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Annulleret"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "Kan ikke ændre et tip"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "Kan ikke returnere byttepenge uden en kontant betalingsmetode"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "Kortholder Navn"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#: model:pos.payment.method,name:point_of_sale.payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Cash"
msgstr "Kontant"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "Kontant%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash In"
msgstr "Kontanter ind"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "Kontanter ind/ud"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Kontant journal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr "Kontant linjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Cash Move 1"
msgstr "Kontant bevægelse 1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Cash Opening"
msgstr "Kontant åbning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash Out"
msgstr "Kontanter ud"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr "Kontantafrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr "Kontantafrunding (PoS)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Kontantafrundinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Cash control - closing"
msgstr "Kontantkontrol - lukning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.js:0
#, python-format
msgid "Cash control - opening"
msgstr "Kontantkontrol - åbning"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Loss)"
msgstr "Kontant difference observeret under tællingen (Tab)"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Profit)"
msgstr "Kontant difference observeret under tællingen (Indtægt)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
#, python-format
msgid "Cash in / out"
msgstr "Kontanter ind/ud"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "Kontanter ind/ud for %s er ignoreret."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register"
msgstr "Kasseapparat"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr "Kontant afrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Åbn kasseapparatet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
#, python-format
msgid "Cashier"
msgstr "Ekspedient"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Kategorier bruges til at vise dine produkter ved brugen af \n"
"                trykfølsom skærm."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/category_selector/category_selector.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Kategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Kategorinavn"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_ids
msgid "Category used in the Point of Sale."
msgstr "Kategori brugt i PoS"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Stole"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Change"
msgstr "Byttepenge"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Change Tip"
msgstr "Skift drikkepenge"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Change:"
msgstr "Byttepenge:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""
"Kontroller om produktet skal vejes ved hjælp af hardware skala integrationen"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Kontroller om dette produkt skal vises i PoS."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Sæt flueben hvis du ønsker at gruppere produkter fra denne kategori i "
"kasseordrer"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Check beløbet i kassen ved åbning og lukning."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"Tjek internetforbindelsen og prøv så at synkronisere igen, ved at klikke på "
"den røde wifi knap (oppe til højre i skærmen)."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Underkategorier"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Vælg en specifik skatteposition ved bestillingen, afhængig af hvilken slags "
"kunde (momsfri, on-site vs. takeway, osv.)."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "City"
msgstr "By"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr "Klik her for at lukke sessionen"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Kunde"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Close"
msgstr "Luk"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "Luk session"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "Luk Session & Send Posteringer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "Luk session wizard"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Lukket og posteret"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Kontrol"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Afslutningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "Afsluttende noter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing Session"
msgstr "Lukningssession"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "Lukkedifference i %s (%s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing note"
msgstr "Lukkenote"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Closing session error"
msgstr "Fejl ved lukning af session"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "Mønt/regningsværdi"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#, python-format
msgid "Coins/Bills"
msgstr "Mønter/Sedler"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__combo_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__combo_ids
msgid "Combinations"
msgstr "Kombinationer"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "Kombinér %s POS betalinger fra %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_id
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__detailed_type__combo
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__type__combo
msgid "Combo"
msgstr "Kombination"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_combo
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Combo Choices"
msgstr "Kombinationsvalg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "Kombinations linjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combo Name"
msgstr "Kombinations navn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "Kombinations forælder"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "Combo products cannot contains variants or attributes"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combos"
msgstr "Kombinationer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr "Virksomhed har en kontoplan"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Complete the selection to proceed"
msgstr "Fuldfør valget for at fortsætte"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfiguration"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "Konfigurationer &gt; Indstillinger"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr "Konfigurer mindst ét Point of Sale."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "Bekræft"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#, python-format
msgid "Confirm?"
msgstr "Bekræft?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr "Tilslut enheden til din PoS uden en IoT-boks"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Forbind enheder til din PoS uden en IoT boks."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr "Forbind enheder via en IoT Boks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr "Forbundne enheder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "Forbundet, Ikke ejet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "Opret forbindelse til Proxy"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Connection error"
msgstr "Forbindelses fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "Forbindelse til IoT boks slog fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Forbindelse til printer slog fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Continue"
msgstr "Fortsæt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "Fortsæt med at sælge"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "Continue with limited functionalities"
msgstr "Fortsæt med begrænsede funktioner"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"Conversion Error: The following unit of measure conversions result in a zero"
" quantity due to rounding:"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Valutakurs"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Valutakurs fra virksomheds valuta til bestillings valuta."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Cost:"
msgstr "Kostpris:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Counted"
msgstr "Talte"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Country"
msgstr "Land"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__country_code
msgid "Country Code"
msgstr "Lande kode"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Create"
msgstr "Opret"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "Opret en ny POS ordre"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr "Opret et nyt PoS"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Opret en ny produktvariant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Valutakurs"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Nuværende session "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Nuværende session ansvarlig"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Nuværende session tilstand"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "Tilpasset"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr "Brugerdefineret sidehoved og sidefod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__custom_attribute_value_ids
msgid "Custom Values"
msgstr "Tilpasset værdier"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "Konto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Customer Display"
msgstr "Kunde visning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "Kunde rettet visning"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Kundefaktura"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "Kundenote"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr "Kundeportal URL"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Customer Required"
msgstr "Kunde påkrævet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen"
msgstr "Kundeskærm"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen Connected"
msgstr "Kundeskærm tilsluttet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Customer Screen Unsupported. Please upgrade the IoT Box"
msgstr "Kundeskærm understøttes ikke. Opgrader venligst IoT-boksen"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "Kunder er påkrævet for %s betalingsmetode."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "Kundetip, kan ikke ændres direkte"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Kunder"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_PRODUCT_NAME"
msgstr "DEMO_PRODUKT_NAVN"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_REF"
msgstr "DEMO_REF"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "DETAILS"
msgstr "DETALJER"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Daily Sale"
msgstr "Dagligt salg"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Daily Sales Report"
msgstr "Daglig salgsrapport"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr "Daglige sessioner holder salg fra dit Point of Sale."

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Kontrolpanel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
#, python-format
msgid "Date"
msgstr "Dato"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#, python-format
msgid "DatePicker"
msgstr "Datovælger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Days"
msgstr "Dage"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Debug Window"
msgstr "Fejlfindingsvindue"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr "Standard"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "Standard debitorkonto (POS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Standard konteringsgruppe"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr "Standard journaler"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Default Price"
msgstr "Standardpris"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr "Standard prisliste"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Standard salgsmoms"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Standard salgsmoms"

#. module: point_of_sale
#: model:account.tax,name:point_of_sale.pos_taxes_0
msgid "Default Tax for PoS"
msgstr "Standard moms for PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr "Standard midlertidig konto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr "Standardkladder for ordrer og fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Standard salgsafgift for produkter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standard måleenhed, der anvendes til alle lageroperationer."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Opret en ny kategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Definér den mindste møntfod for valutaen der bruges til kontant betaling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"Angiver navnet op betalingsmetoden, som vil blive vist i Point of Sale, når "
"betalingerne er valgt."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Forsink validering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Delete"
msgstr "Slet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "Slet betalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Paid Orders?"
msgstr "Slet betalte ordrer?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Slet ubetalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Unpaid Orders?"
msgstr "Slet ubetalte ordrer?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo 3-03-2000 5:00 PM"
msgstr "Demo 3-03-2000 5:00 PM"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo Name"
msgstr "Demo Navn"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "Demo products are no longer available"
msgstr "Demoprodukter er ikke længere tilgængelige"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Skrivebord organisator "

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "Skrivebord blok"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Skriveborde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "Destinationskonto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "Destinationskonto er readonly"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Difference"
msgstr "Forskel"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "Difference ved lukning af PoS session"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr ""
"Forskel mellem den teoretiske slut balance og den faktiske slut balance."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Opsummering"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Rabat%"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "Rabat:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Discard"
msgstr "Kassér"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Disconnected"
msgstr "Afbrudt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
#, python-format
msgid "Discount"
msgstr "Rabat"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Rabat (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Rabat varsel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Discount:"
msgstr "Rabat:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Produkt med rabat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Discounts"
msgstr "Rabatter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Discounts:"
msgstr "Rabatter:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Dismiss"
msgstr "Afvis"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr "Vis ordrer på klargøringsskærmen"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Har ikke adgang, spring over disse data til brugerens opsamlings e-mail"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"Do you want to accept payments difference and post a profit/loss journal "
"entry?"
msgstr ""
"Ønsker du at acceptere betalingsdifference og bogføre en overskud/tab "
"journalbilag?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "Ønsker du at åbne kundeliste for at vælge en kunde?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Do you want to print using the web printer? "
msgstr "Vil du udskrive med webprinteren?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "Download betalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "Download ubetalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Download a report with all the sales of the current PoS Session"
msgstr "Download en rapport med alt salg for den nuværende PoS session"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "Download fejl traceback"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""
"Hver bestillings printer har en IP adresse som definerer IoT Boks/Hardware\n"
"Proxy hvor printeren kan findes, og en liste af produkt kategorier\n"
"En bestillings printer vil kun printe opdateringer for produkter der tilhører en af\n"
"dens kategorier."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr "Rediger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Elektronisk vægt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Email sent."
msgstr "Email afsendt."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"Ansatte kan skanne deres navneskilt eller angive en PIN for at logge ind på "
"en PoS session. Disse legitimationsoplysninger kan konfigureres i *HR "
"Indstillinger* fanen på medarbejder formularen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Empty Order"
msgstr "Tom ordre"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Aktiver stregkode scanning med en fjernforbundet stregkode scanner og kort "
"aflæsning med en Vantiv kortlæser."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Aktiverer elektronisk vægt integration."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "Stødte på fejl ved indlæsning af billede. Forsøg venligst igen."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Slut dato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Afslutningsbalance"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error"
msgstr "Fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "Fejl med traceback"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "Fejl! Du kan ikke oprette rekursive kategorier."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "Fejl: ingen internetforbindelse."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "Eksisterende ordrelinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Exit Pos"
msgstr "Forlad POS"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Expected"
msgstr "Forventet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Expected delivery:"
msgstr "Forventet levering:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "Eksport betalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "Eksport ubetalte ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Ekstra info"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "Stof"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "Fejlede Pluk"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "Kunne ikke udskrive ændringerne i ordren"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Financials"
msgstr "Finanser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "Afsluttede import ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Bogføringsgruppe"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "Bogføringsgruppe ikke fundet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "Bogføringsgrupper"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr "Fleksible prislister"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr "Fleksible skatter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr "Sidefod"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "for upræcise industrielle touchscreens."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "Gennemtving lukning af session"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force Done"
msgstr "Gennemtving færdigbehandling"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force done"
msgstr "Gennemtving færdigbehandling"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "Tvungen udestående konto"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"Gennemtvinger angivelse af kunde ved brug af denne betalingsmetode og "
"opdeler journalbilag for hver kunde. Det kan gøre lukkeproceduren "
"langsommere."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Free"
msgstr "Gratis"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "Fra fakturabetalinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "Fuld Produktnavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr "Generer en kode på sagen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr "Generering af dine ordre referencer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr "Hent min faktura"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Angiver rækkefølgen ved visning af en liste over varekategorier."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr "Globale rabatter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "Go Back"
msgstr "Gå tilbage"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "Gå til"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "Større end tilladt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Sortér efter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Gruppér produkter i POS"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "HTTPS forbindelse til IoT boks slog fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "Hardware arrangementer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "Hardware status"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr "Har Aktive Sessioner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Har kassekontrol"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr "Har billede"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "Har refunderbare linjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr "Overskrift"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Category Images"
msgstr "Skjul kategoribilleder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Product Images"
msgstr "Skjul produktbilleder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Skjul Brug betalingsterminal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "VIGTIGT: Bug Rapporter fra Odoo POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr "IP adresse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "Identificér kunde"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"Hvis denne ordrelinje er en returnering, så er den returnerede ordrelinje "
"specificeret i dette felt."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Hvis du levere alle produkter samtidigt, vil leveringsordren blive planlagt "
"ud fra den længste produkt ledetid. Ellers vil den være baseret på den "
"korteste."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "Iface Kunde rettet visning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "Billede"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Import Orders"
msgstr "Importer ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Forbedre navigation for upræcise industrielle touchscreens"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "I gang"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "For at slette et salg, skal det være nyt eller annulleret."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time (accurate but slower)"
msgstr "I realtid (præcis men langsommere)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "Fejl i leveringsadresse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "Ukorrekt afrunding"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "Informationsbesked"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_start_categ_id
msgid "Initial Category"
msgstr "Første kategori"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"Installering af kontoplanen fra Generelle Indstillinger for \n"
"              Fakturering/Bogføring applikationen, vil oprette Bank og Kontant betalingsmetoder autoamtisk."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Integrerede kortbetalinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Mellemgående konto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr "Mellemmandskonto brugt til uidentificerede kunder."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Interne kategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Interne notater"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Invalid action"
msgstr "Ugyldig handling"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Invalid email."
msgstr "Ugyldig email."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Lager"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Lagerhåndtering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr "Faktura journal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Invoice Name"
msgstr "Fakturanavn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr "Faktura anmodning "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "Fakturabetaling for %s (%s) med %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Invoiced"
msgstr "Faktureret"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr "Fakturaer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr "Faktureringsbekræftelse"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr "IoT Boks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr "IoT boks IP adresser"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Er faktureret"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_kiosk_mode
msgid "Is Kiosk Mode"
msgstr "Er Kiosk Mode"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "Er refunderet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "Er total kostpris beregnet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Gør brug af virksomheds valuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Er en bar/restaurant"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Er fuld bogføring installeret"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "Er dette allerede tippet?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "Er dette betalingens vekselpenge?"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Det er forbudt at redigere en moms anvendt i POS bestilling der ikke er "
"posteret. Du skal lukke POS sessionerne før redigering af momsen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "It is not allowed to mix refunds and sales"
msgstr "Det er ikke tilladt at blande refusion og salg"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "It is possible to print your tickets by making use of an IoT Box."
msgstr "Det er muligt at printe dine sager ved at gøre brug af en IoT Box."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS Tests"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "Journal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Journalbilag"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Journal Items"
msgstr "Journalposter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "KPI POS samlet værdi"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED lampe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Tekst"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Language"
msgstr "Sprog"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Laptop model x"
msgstr "Bærbar model x"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Store scrollbars"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Sidste session afsluttende kontanter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Sidste session afsluttende dato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr "Sidste forberedelsesændring"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr "Sidste udskrevne tilstand af ordren"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "Læder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Leave a reason here"
msgstr "Efterlad en grund her"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""
"Lad være tomt for at anvende standard kontoen fra virksomhedens "
"indstillinger"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"Lad være tom for at bruge standardkontoen fra virksomhedsindstillingen.\n"
"Konto brugt som udestående konto ved oprettelse af regnskabsbetalingsposter for bankbetalinger."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"Lad være tom for at bruge standardkontoen fra virksomhedsindstillingen.\n"
"Tilsidesætter virksomhedens tilgodehavendekonto (for salgssted), der bruges i kladdeposterne."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "Lad være tom for at anvende kundens debitorkonto"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"Lad være tomt for at bruge kundens tilgodehavendekonto.\n"
"Definerer journalen, hvor de akkumulerede betalinger skal bogføres (eller individuel betaling, hvis Identificer kunde er sandt) efter afslutning af sessionen.\n"
"For kassekladde skriver vi direkte til standardkontoen i kladden via opgørelseslinjer.\n"
"For bankkladde skriver vi til den udestående konto, der er angivet i denne betalingsmetode.\n"
"Kun kontanter og bankjournaler er tilladt."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Brevbakke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr "Linjerabatter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Linje nr."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Load Order"
msgstr "Indlæs ordre"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr "Indlæs produktmenu"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Loading Image Error"
msgstr "Indlæser Billede Fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Loading..."
msgstr "Indlæser..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "Lokal Kunde rettet visning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Login serienummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Lot navn"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Lot Number"
msgstr "Lot nummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Parti/serienumre påkrævet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Lot/serie nummer"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Magnetisk tavle"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Opret betaling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Gør flere prislister tilgængelige i Point of Sale. Du kan også anvende en "
"prisliste for specifikke kunder fra deres kontakt formular (I Salgs fanen). "
"For at være gyldigt, skal denne prisliste være angivet som en tilgængelig "
"prisliste. Ellers vil standard prislisten blive anvendt."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"Vær sikker på at du bruger IoT boks v18.12 eller nyere. Naviger til %s for "
"at acceptere certifikatet på din IoT boks."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr "Administrer kampagner, der vil give kunder rabatter eller gaver"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Avance"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "Margin (%)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Margin:"
msgstr "Avance:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Margins & Costs"
msgstr "Marginer og omkostninger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "Maksimum overskredet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "Maksimal værdi nået"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid ""
"Meanwhile connection is back, Odoo Point of Sale will operate limited "
"operations. Check your connection or continue with limited functionalities"
msgstr ""
"I mellemtiden er forbindelsen tilbage, Odoo Point of Sale vil drive "
"begrænset operationer. Tjek din forbindelse, eller fortsæt med begrænsede "
"funktioner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr "Metode navn"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Misc"
msgstr "Diverse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Mobile"
msgstr "Mobil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr "Modul POS HR"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Skærmfod"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "More info"
msgstr "Mere info"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr "Flere indstillinger:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#, python-format
msgid "More..."
msgstr "Mere..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Multi Employees per Session"
msgstr "Flere medarbejdere pr. session"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Multiple Invoiced Orders Selected"
msgstr "Flere fakturerede ordrer valgt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline på mine aktiviteter "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Mine sessioner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "NY"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTAT"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
#, python-format
msgid "Name"
msgstr "Navn"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "Mangler kunde at fakturere"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"Mangler tabskonto for de følgende journaler for at kunne bogføre tab: %s\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"Mangler gevinstkonto for de følgende journaler for at kunne bogføre "
"gevinster: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Network Error"
msgstr "Netværksfejl"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Ny"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "New Order"
msgstr "Ny ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Ny ekspedition"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.js:0
#, python-format
msgid "New amount"
msgstr "Nyt beløb"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Avisstander"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste aktivitet for kalenderarrangement"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Next Order List"
msgstr "Næste Ordreliste"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "No"
msgstr "Nej"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr "Ingen Point of Sale valgt"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "No Taxes"
msgstr "Ingen moms"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid ""
"No barcode nomenclature has been configured. This can be changed in the "
"configuration settings."
msgstr ""
"Der er ikke konfigureret nogen stregkode nomenklatur. Dette kan ændres i "
"konfigurationsindstillingerne."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"Ingen kontoudtog fundet for denne ekspedition. Kan ikke bogføre returnerede "
"kontanter."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"No chart of account configured, go to the \"configuration / settings\" menu,"
" and install one from the Invoicing tab."
msgstr ""
"Ingen kontoplan konfigureret, gå til menuen \"konfiguration / "
"indstillinger\", og installer en fra fanen Fakturering."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "Ingen data endnu!"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "Intet link til faktura for %s."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "No more customer found for \"%s\"."
msgstr "Der blev ikke fundet flere kunder for \"%s\"."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "No more product found for \"%s\"."
msgstr "Der blev ikke fundet flere produkter for \"%s\"."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__num_of_products
msgid "No of Products"
msgstr "Antal produkter"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr "Ingen ordre fundet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products available. Explore"
msgstr "Ingen tilgængelige produkter. Udforsk"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products found for"
msgstr "Ingen produkter fundet for"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "No sale order found."
msgstr "Ingen salgsordre fundet."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr "Ingen sessioner fundet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenklature"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "None"
msgstr "Ingen"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Not Categorized"
msgstr "Ikke kategoriseret"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Ikke faktureret"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "Note"
msgstr "Notat"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "Notater"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Antal udskrifter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "Antal af returneringer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr "Antal redningssessioner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "Antal af varer refunderet i denne ordrelinje."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelelser der kræver handling"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Number of transactions:"
msgstr "Antal transaktioner:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "OK"
msgstr "Ok"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.office_combo_product_template
msgid "Office combo"
msgstr "Kontor kombinationer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Offline Orders"
msgstr "Offline ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Ongoing"
msgstr "Igangværende"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"Kun et negativt antal er tilladt for denne returneringslinje. Klik på +/- "
"for at ændre antallet, der skal refunderes."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Only administrators can edit receipt headers and footers"
msgstr ""
"Kun administratorer kan redigere overskrifter og sidefødder på kvitteringer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Anvend kun afrunding på kontant"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""
"Kun kladder af typen 'Kontanter' eller 'Bank' kunne bruges med "
"betalingsmetoder."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr "Kun ved kontantmetoder"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Kun brugere med Leder adgangsrettigheder til appen Kasse kan ændre "
"produktpriser på ordrer."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Kun web-kompatible billedformater så som .png eller .jpeg understøttes."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "Åben pengekasse"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Åben PoS sessioner som anvender denne betalingsmetode."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "Åbn sessioner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open session"
msgstr "Åben session"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "Åben pop-up'en med pengedetaljer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "Åbnet Af"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Åbnet af"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Opening"
msgstr "Åbner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening Cash Control"
msgstr "Åbning af kontantkontrol"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Åbningskontrol"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Åbningsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "Åbningsnoter"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr "Åbningssaldo summeret til alle kontanttransaktioner."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening cash"
msgstr "Åbningskontanter"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening note"
msgstr "Åbningsnote"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr "Operation type"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "Operations typer vises på Inventar kontrolpanel."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Order %s"
msgstr "Ordre %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "Ordre %s ikke fuldt betalt."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Ordre antal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Bestilingsdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr "Ordre-ID sekvens"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Ordre linje ID'er sekvens"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tracking_number
#, python-format
msgid "Order Number"
msgstr "Ordrenummer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "Bestillings printer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr "Ordrer udskrivninger"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"Ordre Printere bruges af restauranter og barer til at udskrive\n"
"ordre opdateringer i køkkenet/baren, når tjeneren opdaterer ordren."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order Ref"
msgstr "Ordre reference"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "Ordre reference"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Ordresekvens"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Ordrelinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Order number"
msgstr "Ordrenummer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order reference"
msgstr "Ordre reference"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "Order saved for later"
msgstr "Ordre gemt til senere"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""
"Ordrelinjer i dette fejl er de ordrelinjer, der refunderede denne "
"ordrelinje."

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Ordre"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Analyse af ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__lst_price
msgid "Original Price"
msgstr "Oprindelig pris"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr "Andre enheder"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Anden information"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Others"
msgstr "Andre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "Udestående Konto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "POS ordre"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "Kasseordrer %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "POS ordrelinie"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "POS ordrelinjer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "POS ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "POS ordrerlinjer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "POS betalingsmetode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr "POS printer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "POS produktkategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "POS salg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "POS session"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "POS ordrelinje %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Betalt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Moder-kategori"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Partner"
msgstr "Kontakt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Pay"
msgstr "Betal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Betal ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PayTM"
msgstr "PayTM"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_paytm
msgid "PayTM Payment Terminal"
msgstr "PayTM betalingsterminal"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Betaling"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Betalingsdato"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "Betalingsmetode"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Payment Name Demo"
msgstr "Betalingsnavn Demo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "Betaling Kvittering Info"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Betalingsreference"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "Betalings status"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Payment Successful"
msgstr "Betaling vellykket"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "Betaling terminaler"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "Betaling Transaktion ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Payment method"
msgstr "Betalingsmetode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr "Betalingsmetoder tilgængelige"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment request pending"
msgstr "Betalingsanmodning afventer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment reversed"
msgstr "Betaling tilbageført"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Betalinger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Payments Difference"
msgstr "Betalingsforskel"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Betalingsmetoder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Payments in"
msgstr "Betalinger i"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Payments:"
msgstr "Betalinger:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Person som anvender kasseapparatet. Det kan være en praktikant, midlertidig "
"ansat, eller flex ansat."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product categories are available"
msgstr "Vælg hvilke produktkategorier er tilgængelige"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Plukning"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Pluk antal"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "Pluk POS"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr "Pluk type"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "Plukninger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Picture"
msgstr "Billede"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "Plastik"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Bekræft venligst stort antal"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_combo.py:0
#, python-format
msgid "Please add products in combo."
msgstr "Tilføj venligst produkter i kombination."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Tjek venligst om IoT boksen stadig er forbundet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue. "
msgstr ""
"Kontroller, om printeren stadig er tilsluttet.\n"
"Nogle browsere tillader ikke HTTP-kald fra hjemmeside til enheder på netværket (af sikkerhedsmæssige årsager). Hvis det er tilfældet, skal du følge Odoos dokumentation for 'Selvsigneret certifikat til ePOS-printere' og 'Sikker forbindelse (HTTPS)' for at løse problemet."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Luk venligst alle point of sale sessioner for denne periode før du lukker den.\n"
"Åbne sessioner er: %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""
"Luk og valider venligst følgende åbne PoS-sessioner, før du ændrer denne betalingsmetode.\n"
"Åbne sessioner: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "Konfigurer venligst en betalingsmetode i dit POS."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Please create/select a Point of Sale above to show the configuration "
"options."
msgstr ""
"Opret/vælg et Point of Sale ovenfor for at vise konfigurationsmulighederne."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "Venligst angive indtægtsbeløb for dette produkt: \"%s\" (id:%d)."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please define income account for this product: '%s' (id:%d)."
msgstr "Definer venligst indkomstkonto for dette produkt: '%s' (id:%d)."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr ""
"Indtast venligst dine faktureringsoplysninger <small class=\"text-"
"muted\">eller</small>"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "Please fill all the required fields."
msgstr "Udfyld venligst alle de påkrævede felter."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Loss Account. This account will be "
"used to record cash difference."
msgstr ""
"Gå venligst til %s journalen og definer en Tabs Konto. Denne konto vil blive"
" brugt til at registrere kontant differencer."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Profit Account. This account will "
"be used to record cash difference."
msgstr ""
"Gå venligst til %s journalen og definer en Gevinst Konto. Denne konto vil "
"blive brugt til at registrere kontant differencer."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "Print venligst fakturaen fra back-end'en"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Sæt en partner til rådighed for salget."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "Vælg venligst en betalingsmetode."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "Vælg venligst kunden"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr "PoS brugerflade"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_attribute_custom_value__pos_order_line_id
msgid "PoS Order Line"
msgstr "PoS ordrelinje"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "POS ordrer"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr "PoS produkt kategorier"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "PoS produkt kategori"

#. module: point_of_sale
#: model:account.tax.group,name:point_of_sale.pos_taxe_group_0
msgid "PoS Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "PoS order %s can not be processed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Point Of Sale"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "POS analyse"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_ids
msgid "Point of Sale Category"
msgstr "POS kategori"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "POS konfiguration"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "POS konfiguration"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "Daglig rapport for PoS"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "PoS detaljer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Point of Sale detaljer rapport"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Point of Sale faktura rapport"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr "Point of Sale journal"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr "Point of Sale Liste"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Point of Sale opret betaling guide"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Point of Sale leder grupper"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Point of Sale operations type"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Point of Sale linjer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "POS ordrer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Point of Sale ordre rapport"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Point of Sale betalingsmetoder"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Point of Sale betalinger"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Point of Sale Printer"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "PoS session"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "Point of Sale Tests"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "POS kundegruppe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr "POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr "Portal adgangs URL"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr "Pos Tilladt prisliste"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "POS konfig."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr "Pos er Ordre Printer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "PoS ordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Pos Ordre antal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "POS ordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "POS betaling"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "POS produktkategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_refunded_invoice_ids
msgid "Pos Refunded Invoice"
msgstr "Pos Refunderet faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr "Pos Valgbar kateg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "Pos Session"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Pos Session varighed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Pos Session status"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Pos Session brugernavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Pos Sessioner"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr "PoS session"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "Positivt antal ikke tilladt"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Bogført"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Powered by"
msgstr "Drevet af"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Powered by Odoo"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr "Klargøringsvisning"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model:ir.ui.menu,name:point_of_sale.menu_pos_preparation_printer
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr "Klargøringsprintere"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Forrige Ordreliste"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Price"
msgstr "Pris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr "Priskontrol"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_price
msgid "Price Extra"
msgstr "Pris Tillæg"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "Prisrabat fra %s -> %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Price excl. Tax:"
msgstr "Pris ekskl. moms:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr "Pris tillæg"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#, python-format
msgid "Price list"
msgstr "Pris liste"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Prissat produkt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Prisliste"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Prislister"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Priser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Udskriv"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/reprint_receipt_button/reprint_receipt_button.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Udskriv kvittering"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Print a QR code on the receipt to allow the user to easily request the "
"invoice for an order."
msgstr ""
"Udskriv en QR-kode på kvitteringen, så brugeren nemt kan anmode om fakturaen"
" for en ordre."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Print en rapport med alle salg for den nuværende PoS session"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "Print ordrer i køkkenet, i baren osv."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Print kvitteringer automatisk når betaling er registreret"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Print via Proxy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Udskrevne produkt kategorier"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Printer"
msgstr "Printer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr "Printernavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Printer type"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr "Printere"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing error"
msgstr "Printefejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Printing failed"
msgstr "Print fejlede"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "Udprintning understøttes ikke af visse browsere"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Indkøbsgruppe"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Produkt egenskab tilpasset værdi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Produktkategori"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo_line
msgid "Product Combo Items"
msgstr "Produktkombinerede varer"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_combo
msgid "Product Combos"
msgstr "Produktkombinationer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "Produktinformation"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__base_price
msgid "Product Price"
msgstr "Produkt Pris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "Produktpriser"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Vare produkt-kategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Antal produkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Produktskabelon"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Produkttype"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Vareenhed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "Vare enhed"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Produkt måleenhed kategorier"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr "Varevariant"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Produktvarianter"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo
msgid "Product combo choices"
msgstr "Valg af produktkombinationer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Product information"
msgstr "Produktinformation"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"Produkt er ikke indlæst. Forsøgte at indlæse produktet fra serveren, men der"
" er ikke nogen netværksforbindelse."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr "Produktpriser på kvitteringer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr "Produkt råd"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Produkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__combo_line_ids
msgid "Products in Combo"
msgstr "Produkter i kombination"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr "Produkter:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "Kampagner, kuponer, gavekort og loyalitets program"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "Proxy forbundet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "Proxy ikke forbundet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Proxy IP Adresse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "Proxy advarsel"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Qty"
msgstr "Antal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Antal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "REASON"
msgstr "ÅRSAG"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "REFUNDED:"
msgstr "REFUNDERING:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr "Bedømmelser"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "Aflæs vægt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "Klar til at afvikle dit <b>point of sale</b>?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Reason"
msgstr "Årsag"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Receipt"
msgstr "Modtagelse"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "Kvittering %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr "Sidefod på kvittering"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr "Sidehoved på kvittering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "Kvitterings nummer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr "Udskrivning af kvittering"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "Registrer betalinger med en terminal i denne journal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Genopretning session"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Ref 876787"
msgstr "Ref 876787"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "Genopfrisk visning"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Refund"
msgstr "Kreditnota"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "Retur ordrelinjer"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "Refundér ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Refund and Sales not allowed"
msgstr "Refusion og salg ikke tilladt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunded"
msgstr "Returneret"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "Refunderet ordre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "Refunderet ordrelinje"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "Refunderede ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "Antal refunderede ordrer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "Refunderet antal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunding"
msgstr "Refundering"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Krediteringer"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr "Genindlæs POS-menu"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Remaining"
msgstr "Resterende"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "Tilbageværende usynkroniserede ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Remove"
msgstr "Fjern"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Replenishment"
msgstr "Genopfyldning"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Rapportering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "Genudskriv faktura"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request invoice"
msgstr "Anmod om faktura"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Request sent"
msgstr "Anmodning afsendt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Reset"
msgstr "Nulstil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restaurant Mode"
msgstr "Restauranttilstand"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr "Begræns kategorier"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Begræns prisændringer til ledere"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr "Begræns prisændringer til ledere"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Resume Order"
msgstr "Gennemse ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Retry"
msgstr "Forsøg igen"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Returner produkter"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Returneret"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of POS closing entry %s for order %s from session %s"
msgstr "Tilbageførsel af POS-lukningsindgang %s for ordre %s fra session%s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Tilbageførsel af: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "Tilbageførsels anmodning afsendt til terminal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "Tilbagefør"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Tilbagebetal betaling"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Review"
msgstr "Gennemse"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Rounding"
msgstr "Afrunding"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "Afrundingsmetode"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "Afrundingsfejl i betalingslinjer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "Kør Point of Sale JS Tests"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "SN"
msgstr "SN"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "SOLD:"
msgstr "SOLGT:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Salgslinje antal"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Salgslinie"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Sales"
msgstr "Salg"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Salgsdetaljer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "Salgs journal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Closing Note"
msgstr "Eksempel på lukkenote"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Config Name"
msgstr "Eksempel på Konfigurationsnavn"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Opening Note"
msgstr "Eksempel på Åbningsnote"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Save"
msgstr "Gem"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Gem denne side og kom tilbage hertil for at konfigurere funktionen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scale"
msgstr "Vægt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan"
msgstr "Skan"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "Scan EAN-13"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Scan me to request an invoice for your purchase."
msgstr "Scan mig for at anmode om en faktura for dit køb."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Scan via Proxy"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scanner"
msgstr "Skanner"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Search Customers..."
msgstr "Søg kunder..."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Search Orders..."
msgstr "Søg ordre..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Søg salgsordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Search more"
msgstr "Søg mere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__access_token
msgid "Security Token"
msgstr "Sikkerhedstoken"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#, python-format
msgid "Select"
msgstr "Vælg"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "Vælg skatteposition"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr "Vælg PoS for at begynde at dele ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select a payment method to validate the order."
msgstr "Vælg en betalingsmetode for at validere ordren."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Select the pricelist"
msgstr "Vælg prislisten"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "Vælg produkt(erne) til refundering og angiv antal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select the shipping date"
msgstr "Vælg forsendelsesdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__attribute_value_ids
msgid "Selected Attributes"
msgstr "Valgte attributter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr "Sælg produkter og lever dem senere."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send"
msgstr "Send"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "Send betalingsanmodning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Send by email"
msgstr "Send pr. e-mail"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "Kunne ikke sende emailen. Prøv venligst igen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending in progress."
msgstr "Afsendelse i gang."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Nummerserie"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "Serie/lot nummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Served by"
msgstr "Betjent af"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Server Error"
msgstr "Server fejl"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "Ekspedition"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session Control"
msgstr "Sessions kontrol"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "Ekspedition ID"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session ID:"
msgstr "Sessions ID:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Session Journal postering"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr "Sessions rapport"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Session ids:"
msgstr "Ekspedition IDs:"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Session åben i over 7 dage"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Ekspeditioner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr "Angiv maksimumdifference"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Set Weight"
msgstr "Indstil vægt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"Angiv en maksimumdifference, som er tilladt mellem det forventede og det "
"optalte beløb ved lukning af sessionen"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"Angiv en maksimumdifference, som er tilladt mellem det forventede og det "
"optalte beløb ved lukning af sessionen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "Angiv skatteposition"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Angiv flere priser pr. produkt, automatiske rabatter osv."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new discount"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "Angiv den nye mængde"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "Opsætning"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr "Indstillingerne på denne side gælder for denne point of sale."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr "Del åbne ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
#, python-format
msgid "Ship Later"
msgstr "Afsend senere"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr "Forsendelsesdato"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr "Afsendelses måde"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Shop"
msgstr "Shop"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Shopping cart"
msgstr "Indkøbskurv"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Category Images"
msgstr "Vis kategoribilleder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Product Images"
msgstr "Vis produktbilleder"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr "Vis checkout til kunder via en anden skærm"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "Vis check-out til kunder med fjern forbundne skærme."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Show checkout to customers."
msgstr "Vis checkout til kunder."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr "Vis ansatte login skærm"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr "Vis marginer og omkostninger på produktinformation"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr "Vis ordrer på klargøringsskærmen."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr "Log ind"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Six"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Six Betalingsterminal"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "Størrelse"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Spring forhåndsvisning over"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr "Spring linje over når bestilling sendes til køkken printere."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Lille hylde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "Nogle serie-/lot numre mangler"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Visse ordre kunne ikke indsendes til serveren, på grund af konfigurations "
"fejl. Du kan forlade Point of Sale, men luk ikke sessionen før fejlen er "
"rettet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Visse ordre kunne ikke indsendes til serveren, på grund af fejl med internet"
" forbindelsen. Du kan forlade Point of Sale, men luk ikke sessionen før "
"fejlen er rettet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""
"Nogle, hvis ikke alle, efterbehandling efter synkroniseringsordren "
"mislykkedes."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr "Specifik rute"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Specificer produkt lot/serienummer på pos ordrelinje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr "Specifik rute for produkter, der leveres senere."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_start_category
msgid "Start Category"
msgstr "Start kategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Start dato"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.js:0
#, python-format
msgid "Start adding products"
msgstr "Begynd at tilføje produkter"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Start category should belong in the available categories."
msgstr "Startkategori skal høre til i de tilgængelige kategorier."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Start selling from a default product category"
msgstr "Begynd at sælge fra en standard produktkategori"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Opstartsbalance"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "State"
msgstr "Stat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Lagerflytning"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Lager regel"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock input for %s"
msgstr "Lagerinput for %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock output for %s"
msgstr "Lagerudgang for %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "Lager bør opdateres ved lukning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Street"
msgstr "Vej"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr "Stribe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr "Stripe Betalingsterminal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
msgid "Subtotal"
msgstr "Subtotal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Subtotal uden moms"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Subtotal uden rabat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "Succesfuldt importeret"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "Veludført en kontant %s på %s."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Sum af subtotaler"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Summary"
msgstr "Opsummering"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Switch Product View"
msgstr "Skift produktvisning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "Synkronisering forbundet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "Synkronisering forbinder"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "Synkronisering forbindelse afbrudt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "Synkronisering fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "TOTAL"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Moms"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Momsbeløb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr "Momsvisning"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Tax ID"
msgstr "Moms ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "Tax ID: %(vatId)s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Name"
msgstr "Momsnavn"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Moms regime valg værdi"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Pris eksklusiv moms"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Pris inklusiv moms"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Moms"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on refunds"
msgstr "Moms på krediteringer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on sales"
msgstr "Moms på salg"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Afgifter der skal anvendes"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Taxes:"
msgstr "Moms"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuff"
msgstr "Tekniske ting"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuffs"
msgstr "Tekniske ting"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Tel:"
msgstr "Telefon:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Thank you for your purchase!"
msgstr "Tak for dit køb!"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The %s must be filled in your details."
msgstr "%s skal udfyldes i dine oplysninger."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "IP adresse eller værtsnavn for printerens hardware proxy"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-landekoden med to tegn. \n"
"Dette felt kan bruges til hurtig søgning."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, customer, employee or action "
"associated with the scanned barcode."
msgstr ""
"Point of Sale kunne ikke finde noget produkt, kunde, medarbejder eller "
"handling forbundet med den scannede stregkode."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"Point of Sale understøtter kun \"tilføj en afrundingslinje\" "
"afrundingsstrategien."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The Point of Sale order with the following reference %s was received by the Odoo server, but the order processing phase failed.\n"
"The datas received from the point of sale has been saved in the attachments.\n"
"Please contact your support service to assist you on restoring it"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The Ticket Number should be at least 14 characters long."
msgstr "Sagsnummeret skal være på mindst 14 tegn."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"Beløbet kan ikke være højere end det forfaldne beløb, hvis du ikke har "
"konfigureret en kontantbetalingsmetode."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the transaction.\n"
"The rounding precision is %s so you should set %s or %s as payment amount instead of %s."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""
"Kontantafrundingsstrategien i point of sale %(pos)s skal være: '%(value)s'"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "Standardprislisten skal være inkluderet i de tilgængelige prislister."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"Standard prislisten skal enten ikke tilhøre nogen virksomhed eller skal "
"tilhøre POS'ens virksomhed."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default tip product is missing. Please manually specify the tip product."
" (See Tips field.)"
msgstr ""
"Standard tipproduktet mangler. Angiv venligst tipproduktet manuelt. (Se "
"feltet Tips.)"

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""
"Skrivebordsorganisatoren er perfekt til opbevaring af alle slags småting og "
"da de 5 kasser sidder løst, kan du flytte og placere dem på den måde, der "
"passer dig og dine ting bedst."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"Den bogføringsgruppe, der blev brugt i den oprindelige ordre, er ikke "
"indlæst. Sørg for, at den er indlæst ved at tilføje den i pos-"
"konfigurationen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The function to load %s has not been implemented."
msgstr "Funktionen til at indlæse %s er ikke implementeret."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"Værtsnavnet eller IP adressen på hardware proxy'en. Vil blive fundet "
"automatisk hvis sat til tom."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Fakturajournalen skal have samme valuta som Salgsjournalen eller "
"virksomhedsvalutaen, hvis det ikke er indstillet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"The maximum difference allowed is %s.\n"
"Please contact your manager to accept the closing difference."
msgstr ""
"Den maksimalt tilladte forskel er %s.\n"
"Kontakt venligst din leder for at acceptere kassedifferencen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_bill.py:0
#, python-format
msgid "The name of the Coins/Bills must be a number."
msgstr "Navnet på mønterne/sedlerne skal være et tal."

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr "Navnet på denne POS-session skal være unikt!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "Antallet af Point of Sale ordre forbundet med denne kunde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Denne ordre kunne ikke sendes til serveren grundet en ukendt fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "The order has been already paid."
msgstr "Ordren er allerede betalt."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"Ordren blev synkroniserede tidligere. Opret venligst fakturaen fra back-"
"end'en for ordre:"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr ""
"Den valgte betalingsmetode er ikke tilladt i konfigurationen af PoS "
"sessionen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "Betalingsmetoderne i point of sale %s skal tilhøre dens virksomhed."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"Point of Sale'n vil vise denne produkt kategori per standard. Hvis ingen "
"kategori er specificeret, vil alle tilgængelige produkter blive vist."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"Point of Sale vil kun vise produkter som er indenfor en af de valgte "
"kategori træer. Hvis ingen kategori er specificeret, vil alle tilgængelige "
"produkter blive vist"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured if any."
msgstr ""
"Prislisten der anvendes hvis ingen kunde er valgt, eller hvis kunden ikke "
"har nogen salgs prisliste konfigurere."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Valutakursen til valuta på kursen anvendelig på datoen for ordren"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Kvitteringsskærmbilledet springes over, hvis kvitteringen kan udskrives "
"automatisk."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "Kvitteringen printes automatisk ved afslutning af hver ordre."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"Det anmodede antal til refundering er højere end det bestilte antal. %s er "
"anmodet, hvorimod kun %s kan blive refunderet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr ""
"Det antal, der er anmodet om at få refunderet, er højere end det "
"refundérbare antal på %s."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo_line__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Salgs prisen administreres fra produkt skabelonen. Klik på 'Konfigurer "
"varianter' knappen, og angiv den ekstra egenskabs priser."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "Den valgte kunde mangler en adresse."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"De valgte prislister skal enten tilhøre ingen virksomhed, eller virksomheden"
" for point of sale."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "Der opstod en fejl med serveren under modtagelsen af din ordre."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The session has been already closed by another User. All sales completed in "
"the meantime have been saved in a Rescue Session, which can be reviewed "
"anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""
"Sessionen er allerede blevet lukket af en anden bruger. Alle salg, der er "
"gennemført i mellemtiden, er blevet gemt i en redningssession, som til "
"enhver tid kan gennemgås og bogføres til Regnskab fra Point of Sale "
"kontrolpanel."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr ""
"Sessionen har været åbnet i usædvanligt lang tid. Overvej venligst at "
"afslutte."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Transaktionerne er behandlet af Adyen. Angiv dine Adyen "
"legitimationsoplysninger på den relaterede betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_paytm
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by PayTM. Set your PayTM credentials on the "
"related payment method."
msgstr ""
"Transaktionerne behandles af PayTM. Indstil dine PayTM-"
"legitimationsoplysninger på den relaterede betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"Transaktionerne behandles af Six. Angiv IP-adressen på terminalen på den "
"relaterede betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Stripe. Set your Stripe credentials on the"
" related payment method."
msgstr ""
"Transaktionerne behandles af Stripe. Indstil dine Stripe-"
"legitimationsoplysninger på den relaterede betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"Transaktionerne er behandlet af Vantiv. Angiv dine Vantiv "
"legitimationsoplysninger på den relateret betalingsmetode."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo__base_price
msgid ""
"The value from which pro-rating of the component price is based. This is to "
"ensure that whatever product the user chooses for a component, it will "
"always be they same price."
msgstr ""
"Den værdi, som forholdsmæssig vurdering af komponentprisen er baseret på. "
"Dette er for at sikre, at uanset hvilket produkt brugeren vælger til en "
"komponent, vil det altid være den samme pris."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teoretisk slut balance"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "Der er ingen produkter i denne kategori."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"Der er stadig ordre i kladde tilstand i sessionen. Betal eller annuller ordre for at godkende sessionen:\n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr "Der er usynkroniserede ordre. Vil du synkronisere disse ordre?"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"Der er en difference mellem beløbene til bogføring og beløbene på ordrerne. "
"Det er formentligt på grund af ændringer i moms eller regnskabsopsætningen."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "Der er allerede en elektronisk betaling under behandling."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"Der er ingen kontoplan konfigureret for virksomheden. Gå venligst til "
"faktura indstillinger for at installere en kontoplan."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"Der er ikke nogen tilgængelig kontantbetalingsmetode i denne kasse til at håndtere byttepenge.\n"
"\n"
"Betal venligst det præcise beløb eller tilføj en kontantbetalingsmetode i kassekonfigurationen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash payment method for this PoS Session"
msgstr "Der er ingen kontantbetalingsmetode for denne PoS-session"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "Der er ikke noget kasseapparat til denne session."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"Der skal være mindst et produkt i din ordre, før den kan valideres og "
"faktureres."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""
"Denne kontantbetalingsmetode bruges allerede i et andet Point of Sale.\n"
"Der skal oprettes en ny kontantbetalingsmetode for dette Point of Sale."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Dette felt viser den maksimale difference tilladt mellem den afsluttende "
"saldo og den teoretiske saldo ved afslutning af sessionen, for ikke-POS "
"ledere. Hvis maksimum nås, vil brugeren få en fejlbesked ved afslutning af "
"deres session, som informere dem om, at de skal kontakte deres leder."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"Dette felt er her for at overføre ID'et for POS ledere gruppen til point of "
"sale klienten."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"Dette felt er her for at overføre ID for pos bruger gruppen til point of "
"sale klienten."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "This invoice has been created from the point of sale session: %s"
msgstr "Denne faktura er oprettet fra point of sale sessionen: %s"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dette er nyttigt for restauranter med afhentning eller take-away tjenester "
"der indbefatter specifikke afgifter."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""
"Denne journal anvendes på en betalingsmetode. Du kan ikke ændre dens type."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with payment method %s that is being used by "
"order %s in the active pos session %s"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"Denne operation vil destruere alle ubetalte ordre i browseren. Du vil miste "
"alt data der ikke er gemt, og forlade point of sale. Denne operation kan "
"ikke omstødes."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Denne handling vil permanent slette alle ordrer lokalt. Du vil miste al "
"data. Denne handling kan ikke fortrydes"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"Denne ordre har allerede refunderingslinjer til %s. Vi kan ikke ændre den "
"tilknyttede kunde. Opret en ny ordre til den nye kunde."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "This order is empty"
msgstr "Denne ordre er tom"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Denne ordre er endnu ikke synkroniseret til serveren. Sørg for, at det er "
"synkroniseret, og prøv derefter igen."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Dette produkt bruges som reference på kunde kvitteringer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Denne nummerserie oprettes automatisk af Odoo, men du kan ændre den, så du "
"kan tilpasse referencenumrene på dine ordrelinjer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Denne nummerserie oprettes automatisk af Odoo, men du kan ændre den, så du "
"kan tilpasse referencenumrene på dine ordrer."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "Denne session er allerede lukket."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "Denne afgift pålægges ethvert nyt produkt oprettet i kataloget."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr "Disse indstillinger er fælles for alle PoS."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr "Sagskode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr "Sags nr"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Tip"
msgstr "Tip"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "Tip Beløb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr "Tillæg af drikkepenge"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr "Drikkepenge"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "Drikkepenge:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Skal lukkes"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "To Pay"
msgstr "Til betaling"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "To Refund:"
msgstr "Til refundering:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Vej med vægt"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"To delete a product, make sure all point of sale sessions are closed.\n"
"\n"
"Deleting a product available in a session would be like attempting to snatch a hamburger from a customer’s hand mid-bite; chaos will ensue as ketchup and mayo go flying everywhere!"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "Til fakturering"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "For at registrere nye ordre, bedes du starte en ny session."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "For at returnere produkt(er), skal du åbne en session i POS'en %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "I alt"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total (Tax excluded)"
msgstr "Total (ekskl. moms)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Samlet kontant transaktion"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "Total kostpris:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Total rabat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Total Due"
msgstr "Saldo i alt"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "Total avance:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Samlet betalt (med afrunding)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Samlet betalings mængde"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Total pris"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Price excl. Tax:"
msgstr "Total pris ekskl. moms:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Samlet mængde af betalingen."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "Total kostpris"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Total antal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Transaktion"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "Transaktion annulleret"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Overfør"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr "Pålidelig POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr "Pålidelige Point of Sale konfigurationer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__type
msgid "Type"
msgstr "Type"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "Kort type anvendt"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "UNSELECT"
msgstr "FRAVÆLG"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"Kune ikke lukke og godkende sessionen.\n"
"Vær venlig at angive den tilsvarende afgifts konto i hver omdelings linje for følgende afgifter:\n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "Kunne ikke hente faktura ned."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"Kunne ikke modificere denne PoS konfiguration, fordi du ikke kan modificere "
"%s imens en session er åben."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid "Unable to parse barcode"
msgstr "Kan ikke parse stregkoden"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "Ude af stand til at vise information om denne fejl."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "Kunne ikke synkronisere ordre"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Unique Code:"
msgstr "Unik kode:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr "Unik kode"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Unit"
msgstr "Enhed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Enhedspris"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#, python-format
msgid "Unknown Barcode:"
msgstr "Ukendt stregkode:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "Ukendt fejl"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "Unselect"
msgstr "Fravælg"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "Ikke-understøttet fil format"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Unsupported search operation"
msgstr "Ikke-understøttet søgeoperation"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Usynkroniseret ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "Enhed"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "Opdater mængde på lager"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Use QR code on ticket"
msgstr "Brug QR-kode på sagen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Brug en betalingsterminal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr "Anvend en prisliste."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "Brug stregkoder til at scanne produkter, kunde kort, osv."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr "Brug bogføringsgrupper til at få forskellige skatter efter ordre"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Used to record product pickings. Products are consumed from its default "
"source location."
msgstr ""
"Bruges til at registrere produktudvalg. Produkter forbruges fra deres "
"standardkildeplacering."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Bruger"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Brugermærke"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Validate"
msgstr "Validér"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (USA & Canada)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Vantiv Betalingsterminal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Waiting for card"
msgstr "Afventer kort"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Væg hylde enhed"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "Lagerstyring"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr "Lager (PoS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "Hjemmesidens kommunikations historik"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Weighing"
msgstr "Vejning"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Vejet produkt"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Når alle produkter er klar"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid ""
"When disabled, only PoS manager can view the margin and cost of product "
"among the Product info."
msgstr ""
"Når den er deaktiveret, er det kun PoS-manager, der kan se margenen og "
"omkostningerne for produktet blandt produktoplysningerne."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Hver gang du afslutter en session, genereres én postering i den følgende "
"regnskabs journal, for alle ordre der ikke er faktureret. Fakturaer "
"registreres i regnskabet separat."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Whiteboard"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Tavle pen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "With a"
msgstr "Med en"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Yes"
msgstr "Ja"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Du har ikke tilladelse til at ændre kontant afrundings konfigurationen imens"
" pos sessionen der bruger den, allerede er åbnet."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "Du har ikke tilladelse til at ændre denne mængde"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"Du forsøger at sælge produkter med serie-/lotnumre, men nogle af dem er ikke indstillet.\n"
"Vil du alligevel fortsætte?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "You can go to"
msgstr "Du kan gå til"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can only unlink PoS order lines that are related to orders in new or "
"cancelled state."
msgstr ""
"Du kan kun fjerne linket til PoS-ordrelinjer, der er relateret til ordrer i "
"ny eller annulleret tilstand."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"Du kan ikke: oprette en pos ordre fra backend-grænsefladen, eller deaktivere"
" prislisten, eller oprette en pos.order i en python-test med "
"formularværktøjet, eller redigere formularvisningen i studiet, hvis der ikke"
" findes en PoS-ordre"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid "You cannot archive '%s' as it is used by a POS configuration '%s'."
msgstr ""
"Du kan ikke arkivere '%s', da det bruges af en POS-konfiguration '%s'."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"Du kan ikke afslutte POS når faktura endnu ikke er posteret.\n"
"Faktura: %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "Du kan ikke lukke PoS når ordre stadig er i kladde"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "Du kan ikke oprette en session før konto låsedatoen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""
"Du kan ikke slette en point of sale kategori imens en session stadig er "
"åben."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You cannot delete a rounding method that is used in a Point of Sale "
"configuration."
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You cannot invoice orders belonging to different companies."
msgstr "Du kan ikke fakturere ordrer tilhørende forskellige firmaer."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "You cannot save an empty order"
msgstr "Du kan ikke gemme en tom ordre"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You cannot share open orders with configuration that does not use the same "
"currency."
msgstr ""
"Du kan ikke dele åbne ordrer med en konfiguration, der ikke bruger samme "
"valuta."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr "Du kan ikke bruge den samme kladde på flere kontantbetalingsmetoder."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You do not have permission to open a POS session. Please try opening a "
"session with a different user"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""
"Du har ikke adgangsrettigheder til at få kontroldata for point of sales "
"lukning."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"Du har aktiveret muligheden \"Identificer kunde\" for %s betalingsmetode, "
"men ordren %s indeholder ikke en kunde."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"You have selected orderlines from multiple invoiced orders. To proceed "
"refund, please select orderlines from the same invoiced order."
msgstr ""
"Du har valgt ordrelinjer fra flere forskellige fakturerede ordrer. For at "
"fortsætte skal du venligst vælge ordrelinjer fra den samme fakturerede "
"ordre."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "Du skal afrunde dine betalingslinjer. Er ikke afrundet."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Du skal definere en vare for alt, du sælger gennem\n"
"                POS-grænsefladen."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "You must first remove this product from the %s combo"
msgstr "Du skal først fjerne dette produkt fra %s kombinationen"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""
"Du skal have mindst én betalingsmetode konfigureret for at påbegynde en "
"session."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "Du skal have et tab samt en indtægts konto på din kontant journal"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr "Du skal vælge kunden, før du kan fakturere eller levere en ordre."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Du skal tildele en kasse til din ekspedition"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "You're offline"
msgstr "Du er offline"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Your Order"
msgstr "Din bestilling"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"Din PoS Session har været åben siden %(date)s, vi tilråder, at du lukker den"
" og opretter en ny."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""
"Din adresse mangler eller er ufuldstændig. <br/>\n"
"                                Sørg venligst for at"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Zip"
msgstr "Postnummer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "available,"
msgstr "tilgængelig,"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr "før du fortsætter."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "Tilhører en anden ekspedition:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_tree
msgid "combos"
msgstr "kombinationer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "create your own"
msgstr "opret din egen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "demo data"
msgstr "demo data"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "discount"
msgstr "rabat"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "e.g. Burger Menu"
msgstr "f.eks. Burger Menu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "fx kontanter"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr "fx Virksomhedsadresse, Hjemmeside"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "fx NYC butik"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "f.eks. Returnerings police, tak for du handlede ved os!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "f.eks. sodavand"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr "ePos Printer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr "udfyld alle relevante oplysninger"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr "for"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "for an order of"
msgstr "For en ordre af"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "forecasted"
msgstr "forventet"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "in"
msgstr "i"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "in this category."
msgstr "i denne kategori."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "varer"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "notat"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "or"
msgstr "eller"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "orders:"
msgstr "ordrer:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr "fremragende redningssession"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "paid orders"
msgstr "betalte ordrer"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr "qx9h1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "Tilbage"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the invoice"
msgstr "fakturaen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the receipt"
msgstr "kvitteringen"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "ubetalte ordrer"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "ubetalte ordrer kunne ikke importeres"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "bruger"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "var duplikater af eksisterende ordrer"
