# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON> Ketem <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__code
msgid "Code"
msgstr "קוד"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "לא נמצאה עסקה המתאימה למספר האסמכתא %s."

#. module: payment_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_provider__code__payumoney
#: model:payment.provider,name:payment_payumoney.payment_provider_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_provider
msgid "Payment Provider"
msgstr "ספק תשלום"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "עסקת תשלום"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr ""

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr "התשלום נתקל בשגיאה עם הקוד %s"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "הקוד הטכני של ספק התשלומים הזה."

#. module: payment_payumoney
#: model_terms:ir.ui.view,arch_db:payment_payumoney.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Razorpay</strong>."
msgstr ""

#. module: payment_payumoney
#: model_terms:payment.provider,auth_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been authorized."
msgstr "התשלום שלך אושר."

#. module: payment_payumoney
#: model_terms:payment.provider,cancel_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been cancelled."
msgstr "התשלום שלך בוטל."

#. module: payment_payumoney
#: model_terms:payment.provider,pending_msg:payment_payumoney.payment_provider_payumoney
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "התשלום שלך עבר עיבוד בהצלחה אך הוא ממתין לאישור."

#. module: payment_payumoney
#: model_terms:payment.provider,done_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been successfully processed."
msgstr "התשלום שלך עובד בהצלחה."
