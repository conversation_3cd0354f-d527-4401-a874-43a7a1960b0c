<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.sax.xmlreader — Interface for XML parsers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.sax.reader.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/sax/xmlreader.py SAX parsers implement the XMLReader interface. They are implemented in a Python module, which must provide a function create_parser(). This function is invoked..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/sax/xmlreader.py SAX parsers implement the XMLReader interface. They are implemented in a Python module, which must provide a function create_parser(). This function is invoked..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.sax.xmlreader — Interface for XML parsers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.parsers.expat — Fast XML parsing using Expat" href="pyexpat.html" />
    <link rel="prev" title="xml.sax.saxutils — SAX Utilities" href="xml.sax.utils.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.sax.reader.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a><ul>
<li><a class="reference internal" href="#xmlreader-objects">XMLReader Objects</a></li>
<li><a class="reference internal" href="#incrementalparser-objects">IncrementalParser Objects</a></li>
<li><a class="reference internal" href="#locator-objects">Locator Objects</a></li>
<li><a class="reference internal" href="#inputsource-objects">InputSource Objects</a></li>
<li><a class="reference internal" href="#the-attributes-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> Interface</a></li>
<li><a class="reference internal" href="#the-attributesns-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.utils.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.saxutils</span></code> — SAX Utilities</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pyexpat.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.sax.reader.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pyexpat.html" title="xml.parsers.expat — Fast XML parsing using Expat"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.sax.utils.html" title="xml.sax.saxutils — SAX Utilities"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.sax.xmlreader">
<span id="xml-sax-xmlreader-interface-for-xml-parsers"></span><h1><a class="reference internal" href="#module-xml.sax.xmlreader" title="xml.sax.xmlreader: Interface which SAX-compliant XML parsers must implement."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code></a> — Interface for XML parsers<a class="headerlink" href="#module-xml.sax.xmlreader" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/sax/xmlreader.py">Lib/xml/sax/xmlreader.py</a></p>
<hr class="docutils" />
<p>SAX parsers implement the <a class="reference internal" href="#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a> interface. They are implemented in
a Python module, which must provide a function <code class="xref py py-func docutils literal notranslate"><span class="pre">create_parser()</span></code>. This
function is invoked by  <a class="reference internal" href="xml.sax.html#xml.sax.make_parser" title="xml.sax.make_parser"><code class="xref py py-func docutils literal notranslate"><span class="pre">xml.sax.make_parser()</span></code></a> with no arguments to create
a new  parser object.</p>
<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">XMLReader</span></span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader" title="Link to this definition">¶</a></dt>
<dd><p>Base class which can be inherited by SAX parsers.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.IncrementalParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">IncrementalParser</span></span><a class="headerlink" href="#xml.sax.xmlreader.IncrementalParser" title="Link to this definition">¶</a></dt>
<dd><p>In some cases, it is desirable not to parse an input source at once, but to feed
chunks of the document as they get available. Note that the reader will normally
not read the entire file, but read it in chunks as well; still <code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code>
won’t return until the entire document is processed. So these interfaces should
be used if the blocking behaviour of <code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code> is not desirable.</p>
<p>When the parser is instantiated it is ready to begin accepting data from the
feed method immediately. After parsing has been finished with a call to close
the reset method must be called to make the parser ready to accept new data,
either from feed or using the parse method.</p>
<p>Note that these methods must <em>not</em> be called during parsing, that is, after
parse has been called and before it returns.</p>
<p>By default, the class also implements the parse method of the XMLReader
interface using the feed, close and reset methods of the IncrementalParser
interface as a convenience to SAX 2.0 driver writers.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Locator">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">Locator</span></span><a class="headerlink" href="#xml.sax.xmlreader.Locator" title="Link to this definition">¶</a></dt>
<dd><p>Interface for associating a SAX event with a document location. A locator object
will return valid results only during calls to DocumentHandler methods; at any
other time, the results are unpredictable. If information is not available,
methods may return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">InputSource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">system_id</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource" title="Link to this definition">¶</a></dt>
<dd><p>Encapsulation of the information needed by the <a class="reference internal" href="#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a> to read
entities.</p>
<p>This class may include information about the public identifier, system
identifier, byte stream (possibly with character encoding information) and/or
the character stream of an entity.</p>
<p>Applications will create objects of this class for use in the
<a class="reference internal" href="#xml.sax.xmlreader.XMLReader.parse" title="xml.sax.xmlreader.XMLReader.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLReader.parse()</span></code></a> method and for returning from
EntityResolver.resolveEntity.</p>
<p>An <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a> belongs to the application, the <a class="reference internal" href="#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a> is
not allowed to modify <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a> objects passed to it from the
application, although it may make copies and modify those.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesImpl">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">AttributesImpl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesImpl" title="Link to this definition">¶</a></dt>
<dd><p>This is an implementation of the <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> interface (see section
<a class="reference internal" href="#attributes-objects"><span class="std std-ref">The Attributes Interface</span></a>).  This is a dictionary-like object which
represents the element attributes in a <code class="xref py py-meth docutils literal notranslate"><span class="pre">startElement()</span></code> call. In addition
to the most useful dictionary operations, it supports a number of other
methods as described by the interface. Objects of this class should be
instantiated by readers; <em>attrs</em> must be a dictionary-like object containing
a mapping from attribute names to attribute values.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesNSImpl">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.sax.xmlreader.</span></span><span class="sig-name descname"><span class="pre">AttributesNSImpl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qnames</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesNSImpl" title="Link to this definition">¶</a></dt>
<dd><p>Namespace-aware variant of <a class="reference internal" href="#xml.sax.xmlreader.AttributesImpl" title="xml.sax.xmlreader.AttributesImpl"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesImpl</span></code></a>, which will be passed to
<code class="xref py py-meth docutils literal notranslate"><span class="pre">startElementNS()</span></code>. It is derived from <a class="reference internal" href="#xml.sax.xmlreader.AttributesImpl" title="xml.sax.xmlreader.AttributesImpl"><code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesImpl</span></code></a>, but
understands attribute names as two-tuples of <em>namespaceURI</em> and
<em>localname</em>. In addition, it provides a number of methods expecting qualified
names as they appear in the original document.  This class implements the
<code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> interface (see section <a class="reference internal" href="#attributes-ns-objects"><span class="std std-ref">The AttributesNS Interface</span></a>).</p>
</dd></dl>

<section id="xmlreader-objects">
<span id="id1"></span><h2>XMLReader Objects<a class="headerlink" href="#xmlreader-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#xml.sax.xmlreader.XMLReader" title="xml.sax.xmlreader.XMLReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLReader</span></code></a> interface supports the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.parse">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.parse" title="Link to this definition">¶</a></dt>
<dd><p>Process an input source, producing SAX events. The <em>source</em> object can be a
system identifier (a string identifying the input source – typically a file
name or a URL), a <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> or <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like</span></a>
object, or an <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a> object. When
<a class="reference internal" href="#xml.sax.xmlreader.XMLReader.parse" title="xml.sax.xmlreader.XMLReader.parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">parse()</span></code></a> returns, the input is completely processed, and the parser object
can be discarded or reset.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added support of character streams.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added support of path-like objects.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getContentHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getContentHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getContentHandler" title="Link to this definition">¶</a></dt>
<dd><p>Return the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.ContentHandler" title="xml.sax.handler.ContentHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContentHandler</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setContentHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setContentHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setContentHandler" title="Link to this definition">¶</a></dt>
<dd><p>Set the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.ContentHandler" title="xml.sax.handler.ContentHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContentHandler</span></code></a>.  If no
<a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.ContentHandler" title="xml.sax.handler.ContentHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContentHandler</span></code></a> is set, content events will be
discarded.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getDTDHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getDTDHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getDTDHandler" title="Link to this definition">¶</a></dt>
<dd><p>Return the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.DTDHandler" title="xml.sax.handler.DTDHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DTDHandler</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setDTDHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setDTDHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setDTDHandler" title="Link to this definition">¶</a></dt>
<dd><p>Set the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.DTDHandler" title="xml.sax.handler.DTDHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DTDHandler</span></code></a>.  If no
<a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.DTDHandler" title="xml.sax.handler.DTDHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">DTDHandler</span></code></a> is set, DTD
events will be discarded.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getEntityResolver">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getEntityResolver</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getEntityResolver" title="Link to this definition">¶</a></dt>
<dd><p>Return the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.EntityResolver" title="xml.sax.handler.EntityResolver"><code class="xref py py-class docutils literal notranslate"><span class="pre">EntityResolver</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setEntityResolver">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setEntityResolver</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setEntityResolver" title="Link to this definition">¶</a></dt>
<dd><p>Set the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.EntityResolver" title="xml.sax.handler.EntityResolver"><code class="xref py py-class docutils literal notranslate"><span class="pre">EntityResolver</span></code></a>.  If no
<a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.EntityResolver" title="xml.sax.handler.EntityResolver"><code class="xref py py-class docutils literal notranslate"><span class="pre">EntityResolver</span></code></a> is set,
attempts to resolve an external entity will result in opening the system
identifier for the entity, and fail if it is not available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getErrorHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getErrorHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getErrorHandler" title="Link to this definition">¶</a></dt>
<dd><p>Return the current <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.ErrorHandler" title="xml.sax.handler.ErrorHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ErrorHandler</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setErrorHandler">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setErrorHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setErrorHandler" title="Link to this definition">¶</a></dt>
<dd><p>Set the current error handler.  If no <a class="reference internal" href="xml.sax.handler.html#xml.sax.handler.ErrorHandler" title="xml.sax.handler.ErrorHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ErrorHandler</span></code></a>
is set, errors will be raised as exceptions, and warnings will be printed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setLocale">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setLocale</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">locale</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setLocale" title="Link to this definition">¶</a></dt>
<dd><p>Allow an application to set the locale for errors and warnings.</p>
<p>SAX parsers are not required to provide localization for errors and warnings; if
they cannot support the requested locale, however, they must raise a SAX
exception.  Applications may request a locale change in the middle of a parse.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getFeature">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getFeature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getFeature" title="Link to this definition">¶</a></dt>
<dd><p>Return the current setting for feature <em>featurename</em>.  If the feature is not
recognized, <code class="xref py py-exc docutils literal notranslate"><span class="pre">SAXNotRecognizedException</span></code> is raised. The well-known
featurenames are listed in the module <a class="reference internal" href="xml.sax.handler.html#module-xml.sax.handler" title="xml.sax.handler: Base classes for SAX event handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setFeature">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setFeature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">featurename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setFeature" title="Link to this definition">¶</a></dt>
<dd><p>Set the <em>featurename</em> to <em>value</em>. If the feature is not recognized,
<code class="xref py py-exc docutils literal notranslate"><span class="pre">SAXNotRecognizedException</span></code> is raised. If the feature or its setting is not
supported by the parser, <em>SAXNotSupportedException</em> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.getProperty">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">getProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">propertyname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.getProperty" title="Link to this definition">¶</a></dt>
<dd><p>Return the current setting for property <em>propertyname</em>. If the property is not
recognized, a <code class="xref py py-exc docutils literal notranslate"><span class="pre">SAXNotRecognizedException</span></code> is raised. The well-known
propertynames are listed in the module <a class="reference internal" href="xml.sax.handler.html#module-xml.sax.handler" title="xml.sax.handler: Base classes for SAX event handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.handler</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.XMLReader.setProperty">
<span class="sig-prename descclassname"><span class="pre">XMLReader.</span></span><span class="sig-name descname"><span class="pre">setProperty</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">propertyname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.XMLReader.setProperty" title="Link to this definition">¶</a></dt>
<dd><p>Set the <em>propertyname</em> to <em>value</em>. If the property is not recognized,
<code class="xref py py-exc docutils literal notranslate"><span class="pre">SAXNotRecognizedException</span></code> is raised. If the property or its setting is
not supported by the parser, <em>SAXNotSupportedException</em> is raised.</p>
</dd></dl>

</section>
<section id="incrementalparser-objects">
<span id="incremental-parser-objects"></span><h2>IncrementalParser Objects<a class="headerlink" href="#incrementalparser-objects" title="Link to this heading">¶</a></h2>
<p>Instances of <a class="reference internal" href="#xml.sax.xmlreader.IncrementalParser" title="xml.sax.xmlreader.IncrementalParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">IncrementalParser</span></code></a> offer the following additional methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.IncrementalParser.feed">
<span class="sig-prename descclassname"><span class="pre">IncrementalParser.</span></span><span class="sig-name descname"><span class="pre">feed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.IncrementalParser.feed" title="Link to this definition">¶</a></dt>
<dd><p>Process a chunk of <em>data</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.IncrementalParser.close">
<span class="sig-prename descclassname"><span class="pre">IncrementalParser.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.IncrementalParser.close" title="Link to this definition">¶</a></dt>
<dd><p>Assume the end of the document. That will check well-formedness conditions that
can be checked only at the end, invoke handlers, and may clean up resources
allocated during parsing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.IncrementalParser.reset">
<span class="sig-prename descclassname"><span class="pre">IncrementalParser.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.IncrementalParser.reset" title="Link to this definition">¶</a></dt>
<dd><p>This method is called after close has been called to reset the parser so that it
is ready to parse new documents. The results of calling parse or feed after
close without calling reset are undefined.</p>
</dd></dl>

</section>
<section id="locator-objects">
<span id="id2"></span><h2>Locator Objects<a class="headerlink" href="#locator-objects" title="Link to this heading">¶</a></h2>
<p>Instances of <a class="reference internal" href="#xml.sax.xmlreader.Locator" title="xml.sax.xmlreader.Locator"><code class="xref py py-class docutils literal notranslate"><span class="pre">Locator</span></code></a> provide these methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Locator.getColumnNumber">
<span class="sig-prename descclassname"><span class="pre">Locator.</span></span><span class="sig-name descname"><span class="pre">getColumnNumber</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Locator.getColumnNumber" title="Link to this definition">¶</a></dt>
<dd><p>Return the column number where the current event begins.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Locator.getLineNumber">
<span class="sig-prename descclassname"><span class="pre">Locator.</span></span><span class="sig-name descname"><span class="pre">getLineNumber</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Locator.getLineNumber" title="Link to this definition">¶</a></dt>
<dd><p>Return the line number where the current event begins.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Locator.getPublicId">
<span class="sig-prename descclassname"><span class="pre">Locator.</span></span><span class="sig-name descname"><span class="pre">getPublicId</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Locator.getPublicId" title="Link to this definition">¶</a></dt>
<dd><p>Return the public identifier for the current event.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Locator.getSystemId">
<span class="sig-prename descclassname"><span class="pre">Locator.</span></span><span class="sig-name descname"><span class="pre">getSystemId</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Locator.getSystemId" title="Link to this definition">¶</a></dt>
<dd><p>Return the system identifier for the current event.</p>
</dd></dl>

</section>
<section id="inputsource-objects">
<span id="input-source-objects"></span><h2>InputSource Objects<a class="headerlink" href="#inputsource-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.setPublicId">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">setPublicId</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.setPublicId" title="Link to this definition">¶</a></dt>
<dd><p>Sets the public identifier of this <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.getPublicId">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">getPublicId</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.getPublicId" title="Link to this definition">¶</a></dt>
<dd><p>Returns the public identifier of this <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.setSystemId">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">setSystemId</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.setSystemId" title="Link to this definition">¶</a></dt>
<dd><p>Sets the system identifier of this <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.getSystemId">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">getSystemId</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.getSystemId" title="Link to this definition">¶</a></dt>
<dd><p>Returns the system identifier of this <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.setEncoding">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">setEncoding</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.setEncoding" title="Link to this definition">¶</a></dt>
<dd><p>Sets the character encoding of this <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a>.</p>
<p>The encoding must be a string acceptable for an XML encoding declaration (see
section 4.3.3 of the XML recommendation).</p>
<p>The encoding attribute of the <a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a> is ignored if the
<a class="reference internal" href="#xml.sax.xmlreader.InputSource" title="xml.sax.xmlreader.InputSource"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputSource</span></code></a> also contains a character stream.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.getEncoding">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">getEncoding</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.getEncoding" title="Link to this definition">¶</a></dt>
<dd><p>Get the character encoding of this InputSource.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.setByteStream">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">setByteStream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bytefile</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.setByteStream" title="Link to this definition">¶</a></dt>
<dd><p>Set the byte stream (a <a class="reference internal" href="../glossary.html#term-binary-file"><span class="xref std std-term">binary file</span></a>) for this input source.</p>
<p>The SAX parser will ignore this if there is also a character stream specified,
but it will use a byte stream in preference to opening a URI connection itself.</p>
<p>If the application knows the character encoding of the byte stream, it should
set it with the setEncoding method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.getByteStream">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">getByteStream</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.getByteStream" title="Link to this definition">¶</a></dt>
<dd><p>Get the byte stream for this input source.</p>
<p>The getEncoding method will return the character encoding for this byte stream,
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if unknown.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.setCharacterStream">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">setCharacterStream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">charfile</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.setCharacterStream" title="Link to this definition">¶</a></dt>
<dd><p>Set the character stream (a <a class="reference internal" href="../glossary.html#term-text-file"><span class="xref std std-term">text file</span></a>) for this input source.</p>
<p>If there is a character stream specified, the SAX parser will ignore any byte
stream and will not attempt to open a URI connection to the system identifier.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.InputSource.getCharacterStream">
<span class="sig-prename descclassname"><span class="pre">InputSource.</span></span><span class="sig-name descname"><span class="pre">getCharacterStream</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.InputSource.getCharacterStream" title="Link to this definition">¶</a></dt>
<dd><p>Get the character stream for this input source.</p>
</dd></dl>

</section>
<section id="the-attributes-interface">
<span id="attributes-objects"></span><h2>The <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> Interface<a class="headerlink" href="#the-attributes-interface" title="Link to this heading">¶</a></h2>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> objects implement a portion of the <a class="reference internal" href="../glossary.html#term-mapping"><span class="xref std std-term">mapping protocol</span></a>, including the methods <code class="xref py py-meth docutils literal notranslate"><span class="pre">copy()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">get()</span></code>, <a class="reference internal" href="../reference/datamodel.html#object.__contains__" title="object.__contains__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__contains__()</span></code></a>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">items()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">keys()</span></code>,
and <code class="xref py py-meth docutils literal notranslate"><span class="pre">values()</span></code>.  The following methods
are also provided:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Attributes.getLength">
<span class="sig-prename descclassname"><span class="pre">Attributes.</span></span><span class="sig-name descname"><span class="pre">getLength</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Attributes.getLength" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of attributes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Attributes.getNames">
<span class="sig-prename descclassname"><span class="pre">Attributes.</span></span><span class="sig-name descname"><span class="pre">getNames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Attributes.getNames" title="Link to this definition">¶</a></dt>
<dd><p>Return the names of the attributes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Attributes.getType">
<span class="sig-prename descclassname"><span class="pre">Attributes.</span></span><span class="sig-name descname"><span class="pre">getType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Attributes.getType" title="Link to this definition">¶</a></dt>
<dd><p>Returns the type of the attribute <em>name</em>, which is normally <code class="docutils literal notranslate"><span class="pre">'CDATA'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.Attributes.getValue">
<span class="sig-prename descclassname"><span class="pre">Attributes.</span></span><span class="sig-name descname"><span class="pre">getValue</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.Attributes.getValue" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of attribute <em>name</em>.</p>
</dd></dl>

</section>
<section id="the-attributesns-interface">
<span id="attributes-ns-objects"></span><h2>The <code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> Interface<a class="headerlink" href="#the-attributesns-interface" title="Link to this heading">¶</a></h2>
<p>This interface is a subtype of the <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> interface (see section
<a class="reference internal" href="#attributes-objects"><span class="std std-ref">The Attributes Interface</span></a>).  All methods supported by that interface are also
available on <code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> objects.</p>
<p>The following methods are also available:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesNS.getValueByQName">
<span class="sig-prename descclassname"><span class="pre">AttributesNS.</span></span><span class="sig-name descname"><span class="pre">getValueByQName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesNS.getValueByQName" title="Link to this definition">¶</a></dt>
<dd><p>Return the value for a qualified name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesNS.getNameByQName">
<span class="sig-prename descclassname"><span class="pre">AttributesNS.</span></span><span class="sig-name descname"><span class="pre">getNameByQName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesNS.getNameByQName" title="Link to this definition">¶</a></dt>
<dd><p>Return the <code class="docutils literal notranslate"><span class="pre">(namespace,</span> <span class="pre">localname)</span></code> pair for a qualified <em>name</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesNS.getQNameByName">
<span class="sig-prename descclassname"><span class="pre">AttributesNS.</span></span><span class="sig-name descname"><span class="pre">getQNameByName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesNS.getQNameByName" title="Link to this definition">¶</a></dt>
<dd><p>Return the qualified name for a <code class="docutils literal notranslate"><span class="pre">(namespace,</span> <span class="pre">localname)</span></code> pair.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.sax.xmlreader.AttributesNS.getQNames">
<span class="sig-prename descclassname"><span class="pre">AttributesNS.</span></span><span class="sig-name descname"><span class="pre">getQNames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.sax.xmlreader.AttributesNS.getQNames" title="Link to this definition">¶</a></dt>
<dd><p>Return the qualified names of all attributes.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a><ul>
<li><a class="reference internal" href="#xmlreader-objects">XMLReader Objects</a></li>
<li><a class="reference internal" href="#incrementalparser-objects">IncrementalParser Objects</a></li>
<li><a class="reference internal" href="#locator-objects">Locator Objects</a></li>
<li><a class="reference internal" href="#inputsource-objects">InputSource Objects</a></li>
<li><a class="reference internal" href="#the-attributes-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">Attributes</span></code> Interface</a></li>
<li><a class="reference internal" href="#the-attributesns-interface">The <code class="xref py py-class docutils literal notranslate"><span class="pre">AttributesNS</span></code> Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.utils.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.saxutils</span></code> — SAX Utilities</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pyexpat.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.sax.reader.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pyexpat.html" title="xml.parsers.expat — Fast XML parsing using Expat"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.sax.utils.html" title="xml.sax.saxutils — SAX Utilities"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>