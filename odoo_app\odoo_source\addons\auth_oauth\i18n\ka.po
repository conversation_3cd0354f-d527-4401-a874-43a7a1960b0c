# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_oauth
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:00+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Georgian (https://www.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Tutorial"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:100
#, python-format
msgid "Access Denied"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings_auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_enabled
msgid "Allowed"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_auth_endpoint
msgid "Authentication URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_body
msgid "Body"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_css_class
msgid "CSS class"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings_auth_oauth_google_client_id
msgid "Client ID"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_create_uid
msgid "Created by"
msgstr "შემქმნელი"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_create_date
msgid "Created on"
msgstr "შექმნის თარიღი"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_data_endpoint
msgid "Data URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_display_name
msgid "Display Name"
msgstr "სახელი"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_id
msgid "ID"
msgstr "იდენტიფიკატორი"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider___last_update
msgid "Last Modified on"
msgstr "ბოლოს განახლებულია"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_write_uid
msgid "Last Updated by"
msgstr "ბოლოს განაახლა"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_write_date
msgid "Last Updated on"
msgstr "ბოლოს განახლებულია"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_access_token
msgid "OAuth Access Token"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_provider_id
msgid "OAuth Provider"
msgstr ""

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr ""

#. module: auth_oauth
#: sql_constraint:res.users:0
msgid "OAuth UID must be unique per provider"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_uid
msgid "OAuth User ID"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_oauth_uid
msgid "Oauth Provider user_id"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_name
msgid "Provider name"
msgstr ""

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_scope
msgid "Scope"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_sequence
msgid "Sequence"
msgstr "მიმდევრობა"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings_server_uri_google
msgid "Server uri"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:98
#, python-format
msgid "Sign up is not allowed on this database."
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "Users"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_validation_endpoint
msgid "Validation URL"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:102
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "ir.config_parameter"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "res.config.settings"
msgstr ""
