<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.dom — The Document Object Model API" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.dom.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/dom/__init__.py The Document Object Model, or “DOM,” is a cross-language API from the World Wide Web Consortium (W3C) for accessing and modifying XML documents. A DOM implement..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/dom/__init__.py The Document Object Model, or “DOM,” is a cross-language API from the World Wide Web Consortium (W3C) for accessing and modifying XML documents. A DOM implement..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.dom — The Document Object Model API &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.dom.minidom — Minimal DOM implementation" href="xml.dom.minidom.html" />
    <link rel="prev" title="xml.etree.ElementTree — The ElementTree XML API" href="xml.etree.elementtree.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.dom.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
<li><a class="reference internal" href="#objects-in-the-dom">Objects in the DOM</a><ul>
<li><a class="reference internal" href="#domimplementation-objects">DOMImplementation Objects</a></li>
<li><a class="reference internal" href="#node-objects">Node Objects</a></li>
<li><a class="reference internal" href="#nodelist-objects">NodeList Objects</a></li>
<li><a class="reference internal" href="#documenttype-objects">DocumentType Objects</a></li>
<li><a class="reference internal" href="#document-objects">Document Objects</a></li>
<li><a class="reference internal" href="#element-objects">Element Objects</a></li>
<li><a class="reference internal" href="#attr-objects">Attr Objects</a></li>
<li><a class="reference internal" href="#namednodemap-objects">NamedNodeMap Objects</a></li>
<li><a class="reference internal" href="#comment-objects">Comment Objects</a></li>
<li><a class="reference internal" href="#text-and-cdatasection-objects">Text and CDATASection Objects</a></li>
<li><a class="reference internal" href="#processinginstruction-objects">ProcessingInstruction Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#conformance">Conformance</a><ul>
<li><a class="reference internal" href="#type-mapping">Type Mapping</a></li>
<li><a class="reference internal" href="#accessor-methods">Accessor Methods</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.etree.elementtree.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.minidom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.dom.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.minidom.html" title="xml.dom.minidom — Minimal DOM implementation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.etree.elementtree.html" title="xml.etree.ElementTree — The ElementTree XML API"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.dom">
<span id="xml-dom-the-document-object-model-api"></span><h1><a class="reference internal" href="#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a> — The Document Object Model API<a class="headerlink" href="#module-xml.dom" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/dom/__init__.py">Lib/xml/dom/__init__.py</a></p>
<hr class="docutils" />
<p>The Document Object Model, or “DOM,” is a cross-language API from the World Wide
Web Consortium (W3C) for accessing and modifying XML documents.  A DOM
implementation presents an XML document as a tree structure, or allows client
code to build such a structure from scratch.  It then gives access to the
structure through a set of objects which provided well-known interfaces.</p>
<p>The DOM is extremely useful for random-access applications.  SAX only allows you
a view of one bit of the document at a time.  If you are looking at one SAX
element, you have no access to another.  If you are looking at a text node, you
have no access to a containing element. When you write a SAX application, you
need to keep track of your program’s position in the document somewhere in your
own code.  SAX does not do it for you.  Also, if you need to look ahead in the
XML document, you are just out of luck.</p>
<p>Some applications are simply impossible in an event driven model with no access
to a tree.  Of course you could build some sort of tree yourself in SAX events,
but the DOM allows you to avoid writing that code.  The DOM is a standard tree
representation for XML data.</p>
<p>The Document Object Model is being defined by the W3C in stages, or “levels” in
their terminology.  The Python mapping of the API is substantially based on the
DOM Level 2 recommendation.</p>
<p>DOM applications typically start by parsing some XML into a DOM.  How this is
accomplished is not covered at all by DOM Level 1, and Level 2 provides only
limited improvements: There is a <code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code> object class which
provides access to <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> creation methods, but no way to access an
XML reader/parser/Document builder in an implementation-independent way. There
is also no well-defined way to access these methods without an existing
<code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object.  In Python, each DOM implementation will provide a
function <a class="reference internal" href="#xml.dom.getDOMImplementation" title="xml.dom.getDOMImplementation"><code class="xref py py-func docutils literal notranslate"><span class="pre">getDOMImplementation()</span></code></a>. DOM Level 3 adds a Load/Store
specification, which defines an interface to the reader, but this is not yet
available in the Python standard library.</p>
<p>Once you have a DOM document object, you can access the parts of your XML
document through its properties and methods.  These properties are defined in
the DOM specification; this portion of the reference manual describes the
interpretation of the specification in Python.</p>
<p>The specification provided by the W3C defines the DOM API for Java, ECMAScript,
and OMG IDL.  The Python mapping defined here is based in large part on the IDL
version of the specification, but strict compliance is not required (though
implementations are free to support the strict mapping from IDL).  See section
<a class="reference internal" href="#dom-conformance"><span class="std std-ref">Conformance</span></a> for a detailed discussion of mapping requirements.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/">Document Object Model (DOM) Level 2 Specification</a></dt><dd><p>The W3C recommendation upon which the Python DOM API is based.</p>
</dd>
<dt><a class="reference external" href="https://www.w3.org/TR/REC-DOM-Level-1/">Document Object Model (DOM) Level 1 Specification</a></dt><dd><p>The W3C recommendation for the DOM supported by <a class="reference internal" href="xml.dom.minidom.html#module-xml.dom.minidom" title="xml.dom.minidom: Minimal Document Object Model (DOM) implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code></a>.</p>
</dd>
<dt><a class="reference external" href="https://www.omg.org/spec/PYTH/1.2/PDF">Python Language Mapping Specification</a></dt><dd><p>This specifies the mapping from OMG IDL to Python.</p>
</dd>
</dl>
</div>
<section id="module-contents">
<h2>Module Contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a> contains the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="xml.dom.registerDOMImplementation">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">registerDOMImplementation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">factory</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.registerDOMImplementation" title="Link to this definition">¶</a></dt>
<dd><p>Register the <em>factory</em> function with the name <em>name</em>.  The factory function
should return an object which implements the <code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code>
interface.  The factory function can return the same object every time, or a new
one for each call, as appropriate for the specific implementation (e.g. if that
implementation supports some customization).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.dom.getDOMImplementation">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">getDOMImplementation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">features</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.getDOMImplementation" title="Link to this definition">¶</a></dt>
<dd><p>Return a suitable DOM implementation. The <em>name</em> is either well-known, the
module name of a DOM implementation, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. If it is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, imports
the corresponding module and returns a <code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code> object if the
import succeeds.  If no name is given, and if the environment variable
<span class="target" id="index-0"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHON_DOM</span></code> is set, this variable is used to find the implementation.</p>
<p>If name is not given, this examines the available implementations to find one
with the required feature set.  If no implementation can be found, raise an
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a>.  The features list must be a sequence of <code class="docutils literal notranslate"><span class="pre">(feature,</span>
<span class="pre">version)</span></code> pairs which are passed to the <code class="xref py py-meth docutils literal notranslate"><span class="pre">hasFeature()</span></code> method on available
<code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code> objects.</p>
</dd></dl>

<p>Some convenience constants are also provided:</p>
<dl class="py data">
<dt class="sig sig-object py" id="xml.dom.EMPTY_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">EMPTY_NAMESPACE</span></span><a class="headerlink" href="#xml.dom.EMPTY_NAMESPACE" title="Link to this definition">¶</a></dt>
<dd><p>The value used to indicate that no namespace is associated with a node in the
DOM.  This is typically found as the <code class="xref py py-attr docutils literal notranslate"><span class="pre">namespaceURI</span></code> of a node, or used as
the <em>namespaceURI</em> parameter to a namespaces-specific method.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.dom.XML_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">XML_NAMESPACE</span></span><a class="headerlink" href="#xml.dom.XML_NAMESPACE" title="Link to this definition">¶</a></dt>
<dd><p>The namespace URI associated with the reserved prefix <code class="docutils literal notranslate"><span class="pre">xml</span></code>, as defined by
<a class="reference external" href="https://www.w3.org/TR/REC-xml-names/">Namespaces in XML</a> (section 4).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.dom.XMLNS_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">XMLNS_NAMESPACE</span></span><a class="headerlink" href="#xml.dom.XMLNS_NAMESPACE" title="Link to this definition">¶</a></dt>
<dd><p>The namespace URI for namespace declarations, as defined by <a class="reference external" href="https://www.w3.org/TR/DOM-Level-2-Core/core.html">Document Object
Model (DOM) Level 2 Core Specification</a> (section 1.1.8).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.dom.XHTML_NAMESPACE">
<span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">XHTML_NAMESPACE</span></span><a class="headerlink" href="#xml.dom.XHTML_NAMESPACE" title="Link to this definition">¶</a></dt>
<dd><p>The URI of the XHTML namespace as defined by <a class="reference external" href="https://www.w3.org/TR/xhtml1/">XHTML 1.0: The Extensible
HyperText Markup Language</a> (section 3.1.1).</p>
</dd></dl>

<p>In addition, <a class="reference internal" href="#module-xml.dom" title="xml.dom: Document Object Model API for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code></a> contains a base <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> class and the DOM
exception classes.  The <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> class provided by this module does not
implement any of the methods or attributes defined by the DOM specification;
concrete DOM implementations must provide those.  The <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> class
provided as part of this module does provide the constants used for the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">nodeType</span></code> attribute on concrete <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> objects; they are located
within the class rather than at the module level to conform with the DOM
specifications.</p>
</section>
<section id="objects-in-the-dom">
<span id="dom-objects"></span><h2>Objects in the DOM<a class="headerlink" href="#objects-in-the-dom" title="Link to this heading">¶</a></h2>
<p>The definitive documentation for the DOM is the DOM specification from the W3C.</p>
<p>Note that DOM attributes may also be manipulated as nodes instead of as simple
strings.  It is fairly rare that you must do this, however, so this usage is not
yet documented.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Interface</p></th>
<th class="head"><p>Section</p></th>
<th class="head"><p>Purpose</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code></p></td>
<td><p><a class="reference internal" href="#dom-implementation-objects"><span class="std std-ref">DOMImplementation Objects</span></a></p></td>
<td><p>Interface to the underlying
implementation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code></p></td>
<td><p><a class="reference internal" href="#dom-node-objects"><span class="std std-ref">Node Objects</span></a></p></td>
<td><p>Base interface for most objects
in a document.</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code></p></td>
<td><p><a class="reference internal" href="#dom-nodelist-objects"><span class="std std-ref">NodeList Objects</span></a></p></td>
<td><p>Interface for a sequence of
nodes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code></p></td>
<td><p><a class="reference internal" href="#dom-documenttype-objects"><span class="std std-ref">DocumentType Objects</span></a></p></td>
<td><p>Information about the
declarations needed to process
a document.</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code></p></td>
<td><p><a class="reference internal" href="#dom-document-objects"><span class="std std-ref">Document Objects</span></a></p></td>
<td><p>Object which represents an
entire document.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></p></td>
<td><p><a class="reference internal" href="#dom-element-objects"><span class="std std-ref">Element Objects</span></a></p></td>
<td><p>Element nodes in the document
hierarchy.</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Attr</span></code></p></td>
<td><p><a class="reference internal" href="#dom-attr-objects"><span class="std std-ref">Attr Objects</span></a></p></td>
<td><p>Attribute value nodes on
element nodes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Comment</span></code></p></td>
<td><p><a class="reference internal" href="#dom-comment-objects"><span class="std std-ref">Comment Objects</span></a></p></td>
<td><p>Representation of comments in
the source document.</p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Text</span></code></p></td>
<td><p><a class="reference internal" href="#dom-text-objects"><span class="std std-ref">Text and CDATASection Objects</span></a></p></td>
<td><p>Nodes containing textual
content from the document.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">ProcessingInstruction</span></code></p></td>
<td><p><a class="reference internal" href="#dom-pi-objects"><span class="std std-ref">ProcessingInstruction Objects</span></a></p></td>
<td><p>Processing instruction
representation.</p></td>
</tr>
</tbody>
</table>
<p>An additional section describes the exceptions defined for working with the DOM
in Python.</p>
<section id="domimplementation-objects">
<span id="dom-implementation-objects"></span><h3>DOMImplementation Objects<a class="headerlink" href="#domimplementation-objects" title="Link to this heading">¶</a></h3>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code> interface provides a way for applications to
determine the availability of particular features in the DOM they are using.
DOM Level 2 added the ability to create new <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> and
<code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> objects using the <code class="xref py py-class docutils literal notranslate"><span class="pre">DOMImplementation</span></code> as well.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.DOMImplementation.hasFeature">
<span class="sig-prename descclassname"><span class="pre">DOMImplementation.</span></span><span class="sig-name descname"><span class="pre">hasFeature</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">feature</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.DOMImplementation.hasFeature" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the feature identified by the pair of strings <em>feature</em> and
<em>version</em> is implemented.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.DOMImplementation.createDocument">
<span class="sig-prename descclassname"><span class="pre">DOMImplementation.</span></span><span class="sig-name descname"><span class="pre">createDocument</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceUri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualifiedName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doctype</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.DOMImplementation.createDocument" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object (the root of the DOM), with a child
<code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> object having the given <em>namespaceUri</em> and <em>qualifiedName</em>. The
<em>doctype</em> must be a <code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> object created by
<a class="reference internal" href="#xml.dom.DOMImplementation.createDocumentType" title="xml.dom.DOMImplementation.createDocumentType"><code class="xref py py-meth docutils literal notranslate"><span class="pre">createDocumentType()</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. In the Python DOM API, the first two
arguments can also be <code class="docutils literal notranslate"><span class="pre">None</span></code> in order to indicate that no <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code>
child is to be created.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.DOMImplementation.createDocumentType">
<span class="sig-prename descclassname"><span class="pre">DOMImplementation.</span></span><span class="sig-name descname"><span class="pre">createDocumentType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">qualifiedName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.DOMImplementation.createDocumentType" title="Link to this definition">¶</a></dt>
<dd><p>Return a new <code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> object that encapsulates the given
<em>qualifiedName</em>, <em>publicId</em>, and <em>systemId</em> strings, representing the
information contained in an XML document type declaration.</p>
</dd></dl>

</section>
<section id="node-objects">
<span id="dom-node-objects"></span><h3>Node Objects<a class="headerlink" href="#node-objects" title="Link to this heading">¶</a></h3>
<p>All of the components of an XML document are subclasses of <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.nodeType">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">nodeType</span></span><a class="headerlink" href="#xml.dom.Node.nodeType" title="Link to this definition">¶</a></dt>
<dd><p>An integer representing the node type.  Symbolic constants for the types are on
the <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> object: <code class="xref py py-const docutils literal notranslate"><span class="pre">ELEMENT_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">ATTRIBUTE_NODE</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">TEXT_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">CDATA_SECTION_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">ENTITY_NODE</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">PROCESSING_INSTRUCTION_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">COMMENT_NODE</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">DOCUMENT_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">DOCUMENT_TYPE_NODE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">NOTATION_NODE</span></code>.
This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.parentNode">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">parentNode</span></span><a class="headerlink" href="#xml.dom.Node.parentNode" title="Link to this definition">¶</a></dt>
<dd><p>The parent of the current node, or <code class="docutils literal notranslate"><span class="pre">None</span></code> for the document node. The value is
always a <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> object or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  For <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> nodes, this
will be the parent element, except for the root element, in which case it will
be the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object. For <code class="xref py py-class docutils literal notranslate"><span class="pre">Attr</span></code> nodes, this is always
<code class="docutils literal notranslate"><span class="pre">None</span></code>. This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.attributes">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">attributes</span></span><a class="headerlink" href="#xml.dom.Node.attributes" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">NamedNodeMap</span></code> of attribute objects.  Only elements have actual values
for this; others provide <code class="docutils literal notranslate"><span class="pre">None</span></code> for this attribute. This is a read-only
attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.previousSibling">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">previousSibling</span></span><a class="headerlink" href="#xml.dom.Node.previousSibling" title="Link to this definition">¶</a></dt>
<dd><p>The node that immediately precedes this one with the same parent.  For
instance the element with an end-tag that comes just before the <em>self</em>
element’s start-tag.  Of course, XML documents are made up of more than just
elements so the previous sibling could be text, a comment, or something else.
If this node is the first child of the parent, this attribute will be
<code class="docutils literal notranslate"><span class="pre">None</span></code>. This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.nextSibling">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">nextSibling</span></span><a class="headerlink" href="#xml.dom.Node.nextSibling" title="Link to this definition">¶</a></dt>
<dd><p>The node that immediately follows this one with the same parent.  See also
<a class="reference internal" href="#xml.dom.Node.previousSibling" title="xml.dom.Node.previousSibling"><code class="xref py py-attr docutils literal notranslate"><span class="pre">previousSibling</span></code></a>.  If this is the last child of the parent, this
attribute will be <code class="docutils literal notranslate"><span class="pre">None</span></code>. This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.childNodes">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">childNodes</span></span><a class="headerlink" href="#xml.dom.Node.childNodes" title="Link to this definition">¶</a></dt>
<dd><p>A list of nodes contained within this node. This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.firstChild">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">firstChild</span></span><a class="headerlink" href="#xml.dom.Node.firstChild" title="Link to this definition">¶</a></dt>
<dd><p>The first child of the node, if there are any, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. This is a read-only
attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.lastChild">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">lastChild</span></span><a class="headerlink" href="#xml.dom.Node.lastChild" title="Link to this definition">¶</a></dt>
<dd><p>The last child of the node, if there are any, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. This is a read-only
attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.localName">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">localName</span></span><a class="headerlink" href="#xml.dom.Node.localName" title="Link to this definition">¶</a></dt>
<dd><p>The part of the <code class="xref py py-attr docutils literal notranslate"><span class="pre">tagName</span></code> following the colon if there is one, else the
entire <code class="xref py py-attr docutils literal notranslate"><span class="pre">tagName</span></code>.  The value is a string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.prefix">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">prefix</span></span><a class="headerlink" href="#xml.dom.Node.prefix" title="Link to this definition">¶</a></dt>
<dd><p>The part of the <code class="xref py py-attr docutils literal notranslate"><span class="pre">tagName</span></code> preceding the colon if there is one, else the
empty string.  The value is a string, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.namespaceURI">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">namespaceURI</span></span><a class="headerlink" href="#xml.dom.Node.namespaceURI" title="Link to this definition">¶</a></dt>
<dd><p>The namespace associated with the element name.  This will be a string or
<code class="docutils literal notranslate"><span class="pre">None</span></code>.  This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.nodeName">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">nodeName</span></span><a class="headerlink" href="#xml.dom.Node.nodeName" title="Link to this definition">¶</a></dt>
<dd><p>This has a different meaning for each node type; see the DOM specification for
details.  You can always get the information you would get here from another
property such as the <code class="xref py py-attr docutils literal notranslate"><span class="pre">tagName</span></code> property for elements or the <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code>
property for attributes. For all node types, the value of this attribute will be
either a string or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Node.nodeValue">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">nodeValue</span></span><a class="headerlink" href="#xml.dom.Node.nodeValue" title="Link to this definition">¶</a></dt>
<dd><p>This has a different meaning for each node type; see the DOM specification for
details.  The situation is similar to that with <a class="reference internal" href="#xml.dom.Node.nodeName" title="xml.dom.Node.nodeName"><code class="xref py py-attr docutils literal notranslate"><span class="pre">nodeName</span></code></a>.  The value is
a string or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.hasAttributes">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">hasAttributes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.hasAttributes" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the node has any attributes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.hasChildNodes">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">hasChildNodes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.hasChildNodes" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the node has any child nodes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.isSameNode">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">isSameNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.isSameNode" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>other</em> refers to the same node as this node. This is especially
useful for DOM implementations which use any sort of proxy architecture (because
more than one object can refer to the same node).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This is based on a proposed DOM Level 3 API which is still in the “working
draft” stage, but this particular interface appears uncontroversial.  Changes
from the W3C will not necessarily affect this method in the Python DOM interface
(though any new W3C API for this would also be supported).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.appendChild">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">appendChild</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newChild</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.appendChild" title="Link to this definition">¶</a></dt>
<dd><p>Add a new child node to this node at the end of the list of
children, returning <em>newChild</em>. If the node was already in
the tree, it is removed first.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.insertBefore">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">insertBefore</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newChild</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">refChild</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.insertBefore" title="Link to this definition">¶</a></dt>
<dd><p>Insert a new child node before an existing child.  It must be the case that
<em>refChild</em> is a child of this node; if not, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.
<em>newChild</em> is returned. If <em>refChild</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, it inserts <em>newChild</em> at the
end of the children’s list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.removeChild">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">removeChild</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">oldChild</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.removeChild" title="Link to this definition">¶</a></dt>
<dd><p>Remove a child node.  <em>oldChild</em> must be a child of this node; if not,
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.  <em>oldChild</em> is returned on success.  If <em>oldChild</em>
will not be used further, its <code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code> method should be called.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.replaceChild">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">replaceChild</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newChild</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">oldChild</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.replaceChild" title="Link to this definition">¶</a></dt>
<dd><p>Replace an existing node with a new node. It must be the case that  <em>oldChild</em>
is a child of this node; if not, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.normalize">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">normalize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.normalize" title="Link to this definition">¶</a></dt>
<dd><p>Join adjacent text nodes so that all stretches of text are stored as single
<code class="xref py py-class docutils literal notranslate"><span class="pre">Text</span></code> instances.  This simplifies processing text from a DOM tree for
many applications.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Node.cloneNode">
<span class="sig-prename descclassname"><span class="pre">Node.</span></span><span class="sig-name descname"><span class="pre">cloneNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">deep</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Node.cloneNode" title="Link to this definition">¶</a></dt>
<dd><p>Clone this node.  Setting <em>deep</em> means to clone all child nodes as well.  This
returns the clone.</p>
</dd></dl>

</section>
<section id="nodelist-objects">
<span id="dom-nodelist-objects"></span><h3>NodeList Objects<a class="headerlink" href="#nodelist-objects" title="Link to this heading">¶</a></h3>
<p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> represents a sequence of nodes.  These objects are used in
two ways in the DOM Core recommendation:  an <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> object provides
one as its list of child nodes, and the <code class="xref py py-meth docutils literal notranslate"><span class="pre">getElementsByTagName()</span></code> and
<code class="xref py py-meth docutils literal notranslate"><span class="pre">getElementsByTagNameNS()</span></code> methods of <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> return objects with this
interface to represent query results.</p>
<p>The DOM Level 2 recommendation defines one method and one attribute for these
objects:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.NodeList.item">
<span class="sig-prename descclassname"><span class="pre">NodeList.</span></span><span class="sig-name descname"><span class="pre">item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">i</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.NodeList.item" title="Link to this definition">¶</a></dt>
<dd><p>Return the <em>i</em>’th item from the sequence, if there is one, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  The
index <em>i</em> is not allowed to be less than zero or greater than or equal to the
length of the sequence.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.NodeList.length">
<span class="sig-prename descclassname"><span class="pre">NodeList.</span></span><span class="sig-name descname"><span class="pre">length</span></span><a class="headerlink" href="#xml.dom.NodeList.length" title="Link to this definition">¶</a></dt>
<dd><p>The number of nodes in the sequence.</p>
</dd></dl>

<p>In addition, the Python DOM interface requires that some additional support is
provided to allow <code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> objects to be used as Python sequences.  All
<code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> implementations must include support for
<a class="reference internal" href="../reference/datamodel.html#object.__len__" title="object.__len__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__len__()</span></code></a> and
<a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a>; this allows iteration over the <code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> in
<a class="reference internal" href="../reference/compound_stmts.html#for"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">for</span></code></a> statements and proper support for the <a class="reference internal" href="functions.html#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len()</span></code></a> built-in
function.</p>
<p>If a DOM implementation supports modification of the document, the
<code class="xref py py-class docutils literal notranslate"><span class="pre">NodeList</span></code> implementation must also support the
<a class="reference internal" href="../reference/datamodel.html#object.__setitem__" title="object.__setitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setitem__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__delitem__" title="object.__delitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__delitem__()</span></code></a> methods.</p>
</section>
<section id="documenttype-objects">
<span id="dom-documenttype-objects"></span><h3>DocumentType Objects<a class="headerlink" href="#documenttype-objects" title="Link to this heading">¶</a></h3>
<p>Information about the notations and entities declared by a document (including
the external subset if the parser uses it and can provide the information) is
available from a <code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> object.  The <code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> for a
document is available from the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> object’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">doctype</span></code>
attribute; if there is no <code class="docutils literal notranslate"><span class="pre">DOCTYPE</span></code> declaration for the document, the
document’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">doctype</span></code> attribute will be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> instead of an
instance of this interface.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">DocumentType</span></code> is a specialization of <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>, and adds the
following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.publicId">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">publicId</span></span><a class="headerlink" href="#xml.dom.DocumentType.publicId" title="Link to this definition">¶</a></dt>
<dd><p>The public identifier for the external subset of the document type definition.
This will be a string or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.systemId">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">systemId</span></span><a class="headerlink" href="#xml.dom.DocumentType.systemId" title="Link to this definition">¶</a></dt>
<dd><p>The system identifier for the external subset of the document type definition.
This will be a URI as a string, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.internalSubset">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">internalSubset</span></span><a class="headerlink" href="#xml.dom.DocumentType.internalSubset" title="Link to this definition">¶</a></dt>
<dd><p>A string giving the complete internal subset from the document. This does not
include the brackets which enclose the subset.  If the document has no internal
subset, this should be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.name">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#xml.dom.DocumentType.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the root element as given in the <code class="docutils literal notranslate"><span class="pre">DOCTYPE</span></code> declaration, if
present.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.entities">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">entities</span></span><a class="headerlink" href="#xml.dom.DocumentType.entities" title="Link to this definition">¶</a></dt>
<dd><p>This is a <code class="xref py py-class docutils literal notranslate"><span class="pre">NamedNodeMap</span></code> giving the definitions of external entities.
For entity names defined more than once, only the first definition is provided
(others are ignored as required by the XML recommendation).  This may be
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the information is not provided by the parser, or if no entities are
defined.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.DocumentType.notations">
<span class="sig-prename descclassname"><span class="pre">DocumentType.</span></span><span class="sig-name descname"><span class="pre">notations</span></span><a class="headerlink" href="#xml.dom.DocumentType.notations" title="Link to this definition">¶</a></dt>
<dd><p>This is a <code class="xref py py-class docutils literal notranslate"><span class="pre">NamedNodeMap</span></code> giving the definitions of notations. For
notation names defined more than once, only the first definition is provided
(others are ignored as required by the XML recommendation).  This may be
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the information is not provided by the parser, or if no notations
are defined.</p>
</dd></dl>

</section>
<section id="document-objects">
<span id="dom-document-objects"></span><h3>Document Objects<a class="headerlink" href="#document-objects" title="Link to this heading">¶</a></h3>
<p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> represents an entire XML document, including its constituent
elements, attributes, processing instructions, comments etc.  Remember that it
inherits properties from <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Document.documentElement">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">documentElement</span></span><a class="headerlink" href="#xml.dom.Document.documentElement" title="Link to this definition">¶</a></dt>
<dd><p>The one and only root element of the document.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createElement">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createElement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createElement" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a new element node.  The element is not inserted into the
document when it is created.  You need to explicitly insert it with one of the
other methods such as <code class="xref py py-meth docutils literal notranslate"><span class="pre">insertBefore()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">appendChild()</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createElementNS">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createElementNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tagName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createElementNS" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a new element with a namespace.  The <em>tagName</em> may have a
prefix.  The element is not inserted into the document when it is created.  You
need to explicitly insert it with one of the other methods such as
<code class="xref py py-meth docutils literal notranslate"><span class="pre">insertBefore()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">appendChild()</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createTextNode">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createTextNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createTextNode" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a text node containing the data passed as a parameter.  As
with the other creation methods, this one does not insert the node into the
tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createComment">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createComment</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createComment" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a comment node containing the data passed as a parameter.  As
with the other creation methods, this one does not insert the node into the
tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createProcessingInstruction">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createProcessingInstruction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createProcessingInstruction" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a processing instruction node containing the <em>target</em> and
<em>data</em> passed as parameters.  As with the other creation methods, this one does
not insert the node into the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createAttribute">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Create and return an attribute node.  This method does not associate the
attribute node with any particular element.  You must use
<code class="xref py py-meth docutils literal notranslate"><span class="pre">setAttributeNode()</span></code> on the appropriate <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> object to use the
newly created attribute instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.createAttributeNS">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">createAttributeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualifiedName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.createAttributeNS" title="Link to this definition">¶</a></dt>
<dd><p>Create and return an attribute node with a namespace.  The <em>tagName</em> may have a
prefix.  This method does not associate the attribute node with any particular
element.  You must use <code class="xref py py-meth docutils literal notranslate"><span class="pre">setAttributeNode()</span></code> on the appropriate
<code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> object to use the newly created attribute instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.getElementsByTagName">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">getElementsByTagName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.getElementsByTagName" title="Link to this definition">¶</a></dt>
<dd><p>Search for all descendants (direct children, children’s children, etc.) with a
particular element type name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Document.getElementsByTagNameNS">
<span class="sig-prename descclassname"><span class="pre">Document.</span></span><span class="sig-name descname"><span class="pre">getElementsByTagNameNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Document.getElementsByTagNameNS" title="Link to this definition">¶</a></dt>
<dd><p>Search for all descendants (direct children, children’s children, etc.) with a
particular namespace URI and localname.  The localname is the part of the
namespace after the prefix.</p>
</dd></dl>

</section>
<section id="element-objects">
<span id="dom-element-objects"></span><h3>Element Objects<a class="headerlink" href="#element-objects" title="Link to this heading">¶</a></h3>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> is a subclass of <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>, so inherits all the attributes
of that class.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Element.tagName">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">tagName</span></span><a class="headerlink" href="#xml.dom.Element.tagName" title="Link to this definition">¶</a></dt>
<dd><p>The element type name.  In a namespace-using document it may have colons in it.
The value is a string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getElementsByTagName">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getElementsByTagName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tagName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getElementsByTagName" title="Link to this definition">¶</a></dt>
<dd><p>Same as equivalent method in the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> class.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getElementsByTagNameNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getElementsByTagNameNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getElementsByTagNameNS" title="Link to this definition">¶</a></dt>
<dd><p>Same as equivalent method in the <code class="xref py py-class docutils literal notranslate"><span class="pre">Document</span></code> class.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.hasAttribute">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">hasAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.hasAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the element has an attribute named by <em>name</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.hasAttributeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">hasAttributeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.hasAttributeNS" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the element has an attribute named by <em>namespaceURI</em> and
<em>localName</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getAttribute">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the attribute named by <em>name</em> as a string. If no such
attribute exists, an empty string is returned, as if the attribute had no value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getAttributeNode">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getAttributeNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">attrname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getAttributeNode" title="Link to this definition">¶</a></dt>
<dd><p>Return the <code class="xref py py-class docutils literal notranslate"><span class="pre">Attr</span></code> node for the attribute named by <em>attrname</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getAttributeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getAttributeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getAttributeNS" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the attribute named by <em>namespaceURI</em> and <em>localName</em> as a
string. If no such attribute exists, an empty string is returned, as if the
attribute had no value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.getAttributeNodeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">getAttributeNodeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.getAttributeNodeNS" title="Link to this definition">¶</a></dt>
<dd><p>Return an attribute value as a node, given a <em>namespaceURI</em> and <em>localName</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.removeAttribute">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">removeAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.removeAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Remove an attribute by name.  If there is no matching attribute, a
<a class="reference internal" href="#xml.dom.NotFoundErr" title="xml.dom.NotFoundErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotFoundErr</span></code></a> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.removeAttributeNode">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">removeAttributeNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">oldAttr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.removeAttributeNode" title="Link to this definition">¶</a></dt>
<dd><p>Remove and return <em>oldAttr</em> from the attribute list, if present. If <em>oldAttr</em> is
not present, <a class="reference internal" href="#xml.dom.NotFoundErr" title="xml.dom.NotFoundErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotFoundErr</span></code></a> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.removeAttributeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">removeAttributeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.removeAttributeNS" title="Link to this definition">¶</a></dt>
<dd><p>Remove an attribute by name.  Note that it uses a localName, not a qname.  No
exception is raised if there is no matching attribute.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.setAttribute">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">setAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.setAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Set an attribute value from a string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.setAttributeNode">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">setAttributeNode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newAttr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.setAttributeNode" title="Link to this definition">¶</a></dt>
<dd><p>Add a new attribute node to the element, replacing an existing attribute if
necessary if the <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> attribute matches.  If a replacement occurs, the
old attribute node will be returned.  If <em>newAttr</em> is already in use,
<a class="reference internal" href="#xml.dom.InuseAttributeErr" title="xml.dom.InuseAttributeErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InuseAttributeErr</span></code></a> will be raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.setAttributeNodeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">setAttributeNodeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newAttr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.setAttributeNodeNS" title="Link to this definition">¶</a></dt>
<dd><p>Add a new attribute node to the element, replacing an existing attribute if
necessary if the <code class="xref py py-attr docutils literal notranslate"><span class="pre">namespaceURI</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">localName</span></code> attributes match.
If a replacement occurs, the old attribute node will be returned.  If <em>newAttr</em>
is already in use, <a class="reference internal" href="#xml.dom.InuseAttributeErr" title="xml.dom.InuseAttributeErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InuseAttributeErr</span></code></a> will be raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.Element.setAttributeNS">
<span class="sig-prename descclassname"><span class="pre">Element.</span></span><span class="sig-name descname"><span class="pre">setAttributeNS</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespaceURI</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.Element.setAttributeNS" title="Link to this definition">¶</a></dt>
<dd><p>Set an attribute value from a string, given a <em>namespaceURI</em> and a <em>qname</em>.
Note that a qname is the whole attribute name.  This is different than above.</p>
</dd></dl>

</section>
<section id="attr-objects">
<span id="dom-attr-objects"></span><h3>Attr Objects<a class="headerlink" href="#attr-objects" title="Link to this heading">¶</a></h3>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Attr</span></code> inherits from <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>, so inherits all its attributes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Attr.name">
<span class="sig-prename descclassname"><span class="pre">Attr.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#xml.dom.Attr.name" title="Link to this definition">¶</a></dt>
<dd><p>The attribute name.
In a namespace-using document it may include a colon.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Attr.localName">
<span class="sig-prename descclassname"><span class="pre">Attr.</span></span><span class="sig-name descname"><span class="pre">localName</span></span><a class="headerlink" href="#xml.dom.Attr.localName" title="Link to this definition">¶</a></dt>
<dd><p>The part of the name following the colon if there is one, else the
entire name.
This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Attr.prefix">
<span class="sig-prename descclassname"><span class="pre">Attr.</span></span><span class="sig-name descname"><span class="pre">prefix</span></span><a class="headerlink" href="#xml.dom.Attr.prefix" title="Link to this definition">¶</a></dt>
<dd><p>The part of the name preceding the colon if there is one, else the
empty string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Attr.value">
<span class="sig-prename descclassname"><span class="pre">Attr.</span></span><span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#xml.dom.Attr.value" title="Link to this definition">¶</a></dt>
<dd><p>The text value of the attribute.  This is a synonym for the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">nodeValue</span></code> attribute.</p>
</dd></dl>

</section>
<section id="namednodemap-objects">
<span id="dom-attributelist-objects"></span><h3>NamedNodeMap Objects<a class="headerlink" href="#namednodemap-objects" title="Link to this heading">¶</a></h3>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">NamedNodeMap</span></code> does <em>not</em> inherit from <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.NamedNodeMap.length">
<span class="sig-prename descclassname"><span class="pre">NamedNodeMap.</span></span><span class="sig-name descname"><span class="pre">length</span></span><a class="headerlink" href="#xml.dom.NamedNodeMap.length" title="Link to this definition">¶</a></dt>
<dd><p>The length of the attribute list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.dom.NamedNodeMap.item">
<span class="sig-prename descclassname"><span class="pre">NamedNodeMap.</span></span><span class="sig-name descname"><span class="pre">item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.dom.NamedNodeMap.item" title="Link to this definition">¶</a></dt>
<dd><p>Return an attribute with a particular index.  The order you get the attributes
in is arbitrary but will be consistent for the life of a DOM.  Each item is an
attribute node.  Get its value with the <code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code> attribute.</p>
</dd></dl>

<p>There are also experimental methods that give this class more mapping behavior.
You can use them or you can use the standardized <code class="xref py py-meth docutils literal notranslate"><span class="pre">getAttribute*()</span></code> family
of methods on the <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> objects.</p>
</section>
<section id="comment-objects">
<span id="dom-comment-objects"></span><h3>Comment Objects<a class="headerlink" href="#comment-objects" title="Link to this heading">¶</a></h3>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">Comment</span></code> represents a comment in the XML document.  It is a subclass of
<code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code>, but cannot have child nodes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Comment.data">
<span class="sig-prename descclassname"><span class="pre">Comment.</span></span><span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#xml.dom.Comment.data" title="Link to this definition">¶</a></dt>
<dd><p>The content of the comment as a string.  The attribute contains all characters
between the leading <code class="docutils literal notranslate"><span class="pre">&lt;!-</span></code><code class="docutils literal notranslate"><span class="pre">-</span></code> and trailing <code class="docutils literal notranslate"><span class="pre">-</span></code><code class="docutils literal notranslate"><span class="pre">-&gt;</span></code>, but does not
include them.</p>
</dd></dl>

</section>
<section id="text-and-cdatasection-objects">
<span id="dom-text-objects"></span><h3>Text and CDATASection Objects<a class="headerlink" href="#text-and-cdatasection-objects" title="Link to this heading">¶</a></h3>
<p>The <code class="xref py py-class docutils literal notranslate"><span class="pre">Text</span></code> interface represents text in the XML document.  If the parser
and DOM implementation support the DOM’s XML extension, portions of the text
enclosed in CDATA marked sections are stored in <code class="xref py py-class docutils literal notranslate"><span class="pre">CDATASection</span></code> objects.
These two interfaces are identical, but provide different values for the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">nodeType</span></code> attribute.</p>
<p>These interfaces extend the <code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> interface.  They cannot have child
nodes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.Text.data">
<span class="sig-prename descclassname"><span class="pre">Text.</span></span><span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#xml.dom.Text.data" title="Link to this definition">¶</a></dt>
<dd><p>The content of the text node as a string.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The use of a <code class="xref py py-class docutils literal notranslate"><span class="pre">CDATASection</span></code> node does not indicate that the node
represents a complete CDATA marked section, only that the content of the node
was part of a CDATA section.  A single CDATA section may be represented by more
than one node in the document tree.  There is no way to determine whether two
adjacent <code class="xref py py-class docutils literal notranslate"><span class="pre">CDATASection</span></code> nodes represent different CDATA marked sections.</p>
</div>
</section>
<section id="processinginstruction-objects">
<span id="dom-pi-objects"></span><h3>ProcessingInstruction Objects<a class="headerlink" href="#processinginstruction-objects" title="Link to this heading">¶</a></h3>
<p>Represents a processing instruction in the XML document; this inherits from the
<code class="xref py py-class docutils literal notranslate"><span class="pre">Node</span></code> interface and cannot have child nodes.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.ProcessingInstruction.target">
<span class="sig-prename descclassname"><span class="pre">ProcessingInstruction.</span></span><span class="sig-name descname"><span class="pre">target</span></span><a class="headerlink" href="#xml.dom.ProcessingInstruction.target" title="Link to this definition">¶</a></dt>
<dd><p>The content of the processing instruction up to the first whitespace character.
This is a read-only attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.dom.ProcessingInstruction.data">
<span class="sig-prename descclassname"><span class="pre">ProcessingInstruction.</span></span><span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#xml.dom.ProcessingInstruction.data" title="Link to this definition">¶</a></dt>
<dd><p>The content of the processing instruction following the first whitespace
character.</p>
</dd></dl>

</section>
<section id="exceptions">
<span id="dom-exceptions"></span><h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<p>The DOM Level 2 recommendation defines a single exception, <a class="reference internal" href="#xml.dom.DOMException" title="xml.dom.DOMException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DOMException</span></code></a>,
and a number of constants that allow applications to determine what sort of
error occurred. <a class="reference internal" href="#xml.dom.DOMException" title="xml.dom.DOMException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DOMException</span></code></a> instances carry a <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-attr docutils literal notranslate"><span class="pre">code</span></code></a> attribute
that provides the appropriate value for the specific exception.</p>
<p>The Python DOM interface provides the constants, but also expands the set of
exceptions so that a specific exception exists for each of the exception codes
defined by the DOM.  The implementations must raise the appropriate specific
exception, each of which carries the appropriate value for the <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-attr docutils literal notranslate"><span class="pre">code</span></code></a>
attribute.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.DOMException">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">DOMException</span></span><a class="headerlink" href="#xml.dom.DOMException" title="Link to this definition">¶</a></dt>
<dd><p>Base exception class used for all specific DOM exceptions.  This exception class
cannot be directly instantiated.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.DomstringSizeErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">DomstringSizeErr</span></span><a class="headerlink" href="#xml.dom.DomstringSizeErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a specified range of text does not fit into a string. This is not
known to be used in the Python DOM implementations, but may be received from DOM
implementations not written in Python.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.HierarchyRequestErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">HierarchyRequestErr</span></span><a class="headerlink" href="#xml.dom.HierarchyRequestErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an attempt is made to insert a node where the node type is not
allowed.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.IndexSizeErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">IndexSizeErr</span></span><a class="headerlink" href="#xml.dom.IndexSizeErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an index or size parameter to a method is negative or exceeds the
allowed values.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.InuseAttributeErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">InuseAttributeErr</span></span><a class="headerlink" href="#xml.dom.InuseAttributeErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an attempt is made to insert an <code class="xref py py-class docutils literal notranslate"><span class="pre">Attr</span></code> node that is already
present elsewhere in the document.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.InvalidAccessErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">InvalidAccessErr</span></span><a class="headerlink" href="#xml.dom.InvalidAccessErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised if a parameter or an operation is not supported on the underlying object.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.InvalidCharacterErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">InvalidCharacterErr</span></span><a class="headerlink" href="#xml.dom.InvalidCharacterErr" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised when a string parameter contains a character that is
not permitted in the context it’s being used in by the XML 1.0 recommendation.
For example, attempting to create an <code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code> node with a space in the
element type name will cause this error to be raised.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.InvalidModificationErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">InvalidModificationErr</span></span><a class="headerlink" href="#xml.dom.InvalidModificationErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an attempt is made to modify the type of a node.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.InvalidStateErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">InvalidStateErr</span></span><a class="headerlink" href="#xml.dom.InvalidStateErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an attempt is made to use an object that is not defined or is no
longer usable.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.NamespaceErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">NamespaceErr</span></span><a class="headerlink" href="#xml.dom.NamespaceErr" title="Link to this definition">¶</a></dt>
<dd><p>If an attempt is made to change any object in a way that is not permitted with
regard to the <a class="reference external" href="https://www.w3.org/TR/REC-xml-names/">Namespaces in XML</a>
recommendation, this exception is raised.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.NotFoundErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">NotFoundErr</span></span><a class="headerlink" href="#xml.dom.NotFoundErr" title="Link to this definition">¶</a></dt>
<dd><p>Exception when a node does not exist in the referenced context.  For example,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">NamedNodeMap.removeNamedItem()</span></code> will raise this if the node passed in does
not exist in the map.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.NotSupportedErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">NotSupportedErr</span></span><a class="headerlink" href="#xml.dom.NotSupportedErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when the implementation does not support the requested type of object or
operation.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.NoDataAllowedErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">NoDataAllowedErr</span></span><a class="headerlink" href="#xml.dom.NoDataAllowedErr" title="Link to this definition">¶</a></dt>
<dd><p>This is raised if data is specified for a node which does not support data.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.NoModificationAllowedErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">NoModificationAllowedErr</span></span><a class="headerlink" href="#xml.dom.NoModificationAllowedErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised on attempts to modify an object where modifications are not allowed (such
as for read-only nodes).</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.SyntaxErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">SyntaxErr</span></span><a class="headerlink" href="#xml.dom.SyntaxErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an invalid or illegal string is specified.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.dom.WrongDocumentErr">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.dom.</span></span><span class="sig-name descname"><span class="pre">WrongDocumentErr</span></span><a class="headerlink" href="#xml.dom.WrongDocumentErr" title="Link to this definition">¶</a></dt>
<dd><p>Raised when a node is inserted in a different document than it currently belongs
to, and the implementation does not support migrating the node from one document
to the other.</p>
</dd></dl>

<p>The exception codes defined in the DOM recommendation map to the exceptions
described above according to this table:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Constant</p></th>
<th class="head"><p>Exception</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">DOMSTRING_SIZE_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.DomstringSizeErr" title="xml.dom.DomstringSizeErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DomstringSizeErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">HIERARCHY_REQUEST_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.HierarchyRequestErr" title="xml.dom.HierarchyRequestErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HierarchyRequestErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INDEX_SIZE_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.IndexSizeErr" title="xml.dom.IndexSizeErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IndexSizeErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INUSE_ATTRIBUTE_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.InuseAttributeErr" title="xml.dom.InuseAttributeErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InuseAttributeErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INVALID_ACCESS_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.InvalidAccessErr" title="xml.dom.InvalidAccessErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InvalidAccessErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INVALID_CHARACTER_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.InvalidCharacterErr" title="xml.dom.InvalidCharacterErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InvalidCharacterErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INVALID_MODIFICATION_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.InvalidModificationErr" title="xml.dom.InvalidModificationErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InvalidModificationErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">INVALID_STATE_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.InvalidStateErr" title="xml.dom.InvalidStateErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">InvalidStateErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">NAMESPACE_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.NamespaceErr" title="xml.dom.NamespaceErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NamespaceErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">NOT_FOUND_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.NotFoundErr" title="xml.dom.NotFoundErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotFoundErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">NOT_SUPPORTED_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.NotSupportedErr" title="xml.dom.NotSupportedErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotSupportedErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">NO_DATA_ALLOWED_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.NoDataAllowedErr" title="xml.dom.NoDataAllowedErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NoDataAllowedErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">NO_MODIFICATION_ALLOWED_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.NoModificationAllowedErr" title="xml.dom.NoModificationAllowedErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NoModificationAllowedErr</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">SYNTAX_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.SyntaxErr" title="xml.dom.SyntaxErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxErr</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><code class="xref py py-const docutils literal notranslate"><span class="pre">WRONG_DOCUMENT_ERR</span></code></p></td>
<td><p><a class="reference internal" href="#xml.dom.WrongDocumentErr" title="xml.dom.WrongDocumentErr"><code class="xref py py-exc docutils literal notranslate"><span class="pre">WrongDocumentErr</span></code></a></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="conformance">
<span id="dom-conformance"></span><h2>Conformance<a class="headerlink" href="#conformance" title="Link to this heading">¶</a></h2>
<p>This section describes the conformance requirements and relationships between
the Python DOM API, the W3C DOM recommendations, and the OMG IDL mapping for
Python.</p>
<section id="type-mapping">
<span id="dom-type-mapping"></span><h3>Type Mapping<a class="headerlink" href="#type-mapping" title="Link to this heading">¶</a></h3>
<p>The IDL types used in the DOM specification are mapped to Python types
according to the following table.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>IDL Type</p></th>
<th class="head"><p>Python Type</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">boolean</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bool</span></code> or <code class="docutils literal notranslate"><span class="pre">int</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">int</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">long</span> <span class="pre">int</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">unsigned</span> <span class="pre">int</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">int</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DOMString</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">str</span></code> or <code class="docutils literal notranslate"><span class="pre">bytes</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">null</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="accessor-methods">
<span id="dom-accessor-methods"></span><h3>Accessor Methods<a class="headerlink" href="#accessor-methods" title="Link to this heading">¶</a></h3>
<p>The mapping from OMG IDL to Python defines accessor functions for IDL
<code class="docutils literal notranslate"><span class="pre">attribute</span></code> declarations in much the way the Java mapping does.
Mapping the IDL declarations</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">readonly</span> <span class="n">attribute</span> <span class="n">string</span> <span class="n">someValue</span><span class="p">;</span>
         <span class="n">attribute</span> <span class="n">string</span> <span class="n">anotherValue</span><span class="p">;</span>
</pre></div>
</div>
<p>yields three accessor functions:  a “get” method for <code class="xref py py-attr docutils literal notranslate"><span class="pre">someValue</span></code>
(<code class="xref py py-meth docutils literal notranslate"><span class="pre">_get_someValue()</span></code>), and “get” and “set” methods for <code class="xref py py-attr docutils literal notranslate"><span class="pre">anotherValue</span></code>
(<code class="xref py py-meth docutils literal notranslate"><span class="pre">_get_anotherValue()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">_set_anotherValue()</span></code>).  The mapping, in
particular, does not require that the IDL attributes are accessible as normal
Python attributes:  <code class="docutils literal notranslate"><span class="pre">object.someValue</span></code> is <em>not</em> required to work, and may
raise an <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a>.</p>
<p>The Python DOM API, however, <em>does</em> require that normal attribute access work.
This means that the typical surrogates generated by Python IDL compilers are not
likely to work, and wrapper objects may be needed on the client if the DOM
objects are accessed via CORBA. While this does require some additional
consideration for CORBA DOM clients, the implementers with experience using DOM
over CORBA from Python do not consider this a problem.  Attributes that are
declared <code class="docutils literal notranslate"><span class="pre">readonly</span></code> may not restrict write access in all DOM
implementations.</p>
<p>In the Python DOM API, accessor functions are not required.  If provided, they
should take the form defined by the Python IDL mapping, but these methods are
considered unnecessary since the attributes are accessible directly from Python.
“Set” accessors should never be provided for <code class="docutils literal notranslate"><span class="pre">readonly</span></code> attributes.</p>
<p>The IDL definitions do not fully embody the requirements of the W3C DOM API,
such as the notion of certain objects, such as the return value of
<code class="xref py py-meth docutils literal notranslate"><span class="pre">getElementsByTagName()</span></code>, being “live”.  The Python DOM API does not require
implementations to enforce such requirements.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
<li><a class="reference internal" href="#objects-in-the-dom">Objects in the DOM</a><ul>
<li><a class="reference internal" href="#domimplementation-objects">DOMImplementation Objects</a></li>
<li><a class="reference internal" href="#node-objects">Node Objects</a></li>
<li><a class="reference internal" href="#nodelist-objects">NodeList Objects</a></li>
<li><a class="reference internal" href="#documenttype-objects">DocumentType Objects</a></li>
<li><a class="reference internal" href="#document-objects">Document Objects</a></li>
<li><a class="reference internal" href="#element-objects">Element Objects</a></li>
<li><a class="reference internal" href="#attr-objects">Attr Objects</a></li>
<li><a class="reference internal" href="#namednodemap-objects">NamedNodeMap Objects</a></li>
<li><a class="reference internal" href="#comment-objects">Comment Objects</a></li>
<li><a class="reference internal" href="#text-and-cdatasection-objects">Text and CDATASection Objects</a></li>
<li><a class="reference internal" href="#processinginstruction-objects">ProcessingInstruction Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#conformance">Conformance</a><ul>
<li><a class="reference internal" href="#type-mapping">Type Mapping</a></li>
<li><a class="reference internal" href="#accessor-methods">Accessor Methods</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.etree.elementtree.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.minidom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom.minidom</span></code> — Minimal DOM implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.dom.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.minidom.html" title="xml.dom.minidom — Minimal DOM implementation"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.etree.elementtree.html" title="xml.etree.ElementTree — The ElementTree XML API"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>