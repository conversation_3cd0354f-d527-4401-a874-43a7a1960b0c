# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# Sergo S, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON><PERSON> <koro<PERSON><EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# valmasone, 2023
# alenafairy, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# Wil Odoo, 2024
# Collex100, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Ilya Rozhkov, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid ""
"\n"
"\n"
"Note: products that you don't have access to will not be shown above."
msgstr ""
"\n"
"\n"
"Примечание: продукты, к которым у вас нет доступа, не будут показаны выше."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# Варианты продуктов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Продукты"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$14.00"
msgstr "$14.00"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "$15.00"
msgstr "$15.00"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"%(base)s with a %(discount)s %% discount and %(surcharge)s extra fee\n"
"Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"
msgstr ""
"%(base)s со скидкой %(discount)s %% и %(surcharge)s дополнительная плата\n"
"Пример: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%(percentage)s %% discount and %(price)s surcharge"
msgstr "%(percentage)s %% скидка и %(price)s надбавка"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%s %% discount"
msgstr "%s %% скидка"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#: code:addons/product/models/product_tag.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "%s: end date (%s) should be greater than start date (%s)"
msgstr "%s: дата окончания (%s) должна быть больше даты начала (%s)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_template_label_2x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x12_noprice
#: model:ir.actions.report,print_report_name:product.report_product_template_label_4x7
#: model:ir.actions.report,print_report_name:product.report_product_template_label_dymo
msgid "'Products Labels - %s' % (object.name)"
msgstr "'Ярлыки продуктов - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr "'Упаковка продуктов - %s' % (object.name)"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "(e.g: product description, ebook, legal notice, ...)."
msgstr ""
"(например, описание продукта, электронная книга, юридическое уведомление, "
"...)."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "- Barcode \"%s\" already assigned to product(s): %s"
msgstr "- Штрих-код \"%s\" уже присвоен товару(ам): %s"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: the variants creation mode cannot be changed once the attribute is used on at least one product."
msgstr ""
"- Мгновенно: Все возможные варианты создаются, как только атрибут и его значения добавляются к продукту.\n"
"        - Динамически: Каждый вариант создается только тогда, когда соответствующие атрибуты и значения добавляются к заказу на продажу.\n"
"        - Никогда: Варианты никогда не создаются для атрибута.\n"
"        Примечание: режим создания вариантов не может быть изменен, если атрибут используется хотя бы в одном продукте."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 год"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "10"
msgstr "10"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "10 Units"
msgstr "10 единиц"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "123456789012"
msgstr "123456789012"

#. module: product
#: model:product.template,description_sale:product.product_product_4_product_template
msgid "160x80cm, with large legs."
msgstr "160x80 см, с большими ножками."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 с ценой"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 год"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 с ценой"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 с ценой"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Валюта\" title=\"Валюта\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Extra Prices\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Extra Price\n"
"                                    </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"pricelist_item_count == 1\">\n"
"                                        Дополнительные цены\n"
"                                    </span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"pricelist_item_count != 1\">\n"
"                                        Дополнительная цена\n"
"                                    </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Продукты</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "<span class=\"o_stat_text\">Related Products</span>"
msgstr "<span class=\"o_stat_text\">Сопутствующие товары</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Архивировано</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span class=\"text-bg-secondary\">Variant</span>"
msgstr "<span class=\"text-bg-secondary\">Вариант</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr "<span>Все общие настройки этого продукта управляются на</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "<span>Variant </span>"
msgstr "<span>Вариант </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>Кол-во: </strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back\n"
"                                here to set up the feature."
msgstr ""
"<strong>Сохраните</strong> эту страницу и вернитесь\n"
"                                сюда, чтобы настроить функцию."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>Внимание</strong>: добавление или удаление атрибутов\n"
"                        приведет к удалению и воссозданию существующих вариантов и\n"
"                        к потере их возможных настроек."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_barcode_uniq
msgid "A barcode can only be assigned to one packaging."
msgstr "Штрих-код может быть присвоен только одной упаковке."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Описание продукта, который вы хотите сообщить своим клиентам. Это описание "
"будет скопировано на каждый заказ клиента, заказ на поставку и счет-фактуру "
"клиента / кредитную заметку"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "A packaging already uses the barcode"
msgstr "В упаковке уже используется штрих-код"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"            This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""
"Прайс - это набор продажных цен или правил для расчета стоимости строк заказов на продажу на основе продуктов, категорий продуктов, дат и заказанных количеств.\n"
"            Это идеальный инструмент для работы с несколькими ценами, сезонными скидками и т. д."

#. module: product
#. odoo-python
#: code:addons/product/models/product_packaging.py:0
#, python-format
msgid "A product already uses the barcode"
msgstr "В продукте уже используется штрихкод"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__detailed_type
#: model:ir.model.fields,help:product.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Складируемый - это продукт, для которого вы отслеживаете остатки на складе. "
"Необходимо установить приложение Склад. Потребляемый - это продукт, для "
"которого остатки не учитываются. Услуга - это нематериальный продукт, "
"который вы предоставляете."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__access_token
msgid "Access Token"
msgstr "Токен доступа"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget"
msgstr "Acme Widget"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Acme Widget - Blue"
msgstr "Acme Widget - синий"

#. module: product
#: model:product.template,name:product.product_product_25_product_template
msgid "Acoustic Bloc Screens"
msgstr "Экраны Acoustic Bloc"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__active
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "Активный"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active Products"
msgstr "Активные продукты"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_ids
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Активность"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_state
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Состояние активности"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_icon
msgid "Activity Type Icon"
msgstr "Значок типа активности"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Add"
msgstr "Добавить"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Add a quantity"
msgstr "Добавить количество"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__additional_product_tag_ids
msgid "Additional Product Tags"
msgstr "Дополнительные теги товаров"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_sale_pricelist
#: model:res.groups,name:product.group_sale_pricelist
msgid "Advanced Pricelists"
msgstr "Расширенные прейскуранты"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__advanced
msgid "Advanced price rules (discounts, formulas)"
msgstr "Расширенные правила ценообразования (скидки, формулы)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__all_product_tag_ids
msgid "All Product Tag"
msgstr "Тег для всех продуктов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_ids
msgid "All Product Variants using this Tag"
msgstr "Все варианты продуктов, использующие этот тег"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#, python-format
msgid "All Products"
msgstr "Все товары"

#. module: product
#. odoo-python
#: code:addons/product/controllers/product_document.py:0
#, python-format
msgid "All files uploaded"
msgstr "Все загруженные файлы"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:product.field_product_template_attribute_value__is_custom
msgid "Allow users to input custom values for this attribute value"
msgstr ""
"Разрешить пользователям вводить пользовательские значения для этого атрибута"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""
"Позволяет управлять различными ценами в зависимости от правил для каждой категории клиентов.\n"
"                Пример: 10% для розничные торговцы, продвижение 5 евро на этот продукт и т.д."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "Aluminium"
msgstr "Алюминий"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "Применяется на"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "Applied On"
msgstr "Применяется на"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Применить на"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "Архивировано"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Присваивает приоритет списку поставщиков продуктов."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid ""
"At most %s quantities can be displayed simultaneously. Remove a selected "
"quantity to add others."
msgstr ""
"Одновременно может отображаться не более %s количеств. Удалите выбранное "
"количество, чтобы добавить другие."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Attached To"
msgstr "Прикрепить к"

#. module: product
#: model:ir.model,name:product.model_ir_attachment
msgid "Attachment"
msgstr "Вложение"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__local_url
msgid "Attachment URL"
msgstr "URL-адрес вложения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Атрибут"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr "Линия атрибутов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Имя атрибута"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
msgid "Attribute Value"
msgstr "Значение атрибута"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_list
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Значения атрибута"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Attributes"
msgstr "Атрибуты"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Attributes & Variants"
msgstr "Атрибуты и варианты"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Availability"
msgstr "Доступность"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
#, python-format
msgid "Back to Order"
msgstr "Назад к заказу"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_controller.js:0
#, python-format
msgid "Back to Quotation"
msgstr "Назад к Коммерческим предложениям"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
msgid "Barcode"
msgstr "Штрихкод"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""
"Штрихкод, используемый для идентификации упаковки. Отсканируйте этот "
"штрихкод упаковки из передачи в приложении \"Штрихкод\", чтобы переместить "
"все содержащиеся в ней единицы"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid ""
"Barcode(s) already assigned:\n"
"\n"
"%s"
msgstr ""
"Штрих-код(ы) уже присвоен(ы):\n"
"\n"
"%s"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price: The base price will be the cost price.\n"
"Other Pricelist: Computation of the base price based on another Pricelist."
msgstr ""
"Базовая цена для расчета.\n"
"Цена продажи: В качестве базовой цены будет использоваться цена продажи.\n"
"Себестоимость: Базовой ценой будет себестоимость.\n"
"Другой прейскурант: Вычисление базовой цены на основе другого прейскуранта."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Основано на"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr "Основные прейскуранты"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Черный"

#. module: product
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "Шкаф с дверцами"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Можно ли увеличить изображение 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr "Можно ли изменить масштаб изображения 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "Можно приобрести"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Может быть продан"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category"
msgstr "Категория"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Category: %s"
msgstr "Категория: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__checksum
msgid "Checksum/SHA1"
msgstr "Контрольная сумма/SHA1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Дочерние категории"

#. module: product
#: model:ir.actions.act_window,name:product.action_open_label_layout
msgid "Choose Labels Layout"
msgstr "Выберите макет этикетки"

#. module: product
#: model:ir.model,name:product.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Выберите макет листа для печати этикеток"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Коды"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:product.field_product_tag__color
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__color
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__color
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Цвет"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__color
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Цветовой индекс"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__columns
msgid "Columns"
msgstr "Колонки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr "Комбинированные индексы"

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__company_id
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Компания"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Полное имя"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Computation"
msgstr "Вычисления"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
msgid "Compute Price"
msgstr "Рассчитать стоимость"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Conditions"
msgstr "Условия"

#. module: product
#: model:product.template,name:product.product_product_11_product_template
msgid "Conference Chair"
msgstr "Cтул конференционный"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr "Стол для конференц-зала"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Configuration"
msgstr "Конфигурация"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Configure"
msgstr "Настройка"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Configure tags"
msgstr "Настройка тегов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__consu
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
msgid "Consumable"
msgstr "Расходный материал"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"Consumables are physical products for which you don't manage the inventory "
"level: they are always available."
msgstr ""
"Расходные материалы - это физические продукты, для которых вы не управляете "
"уровнем запасов: они всегда доступны."

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Contact Us"
msgstr "Свяжитесь с нами"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "Содержащееся количество"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_positive_qty
msgid "Contained Quantity should be positive."
msgstr "Содержащееся количество должно быть положительным."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "Содержится количество"

#. module: product
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Left Sit"
msgstr "Угловой письменный стол с левым сиденьем"

#. module: product
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "Стол угловой правый"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "Стоимость"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "Валюта стоимости"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Группы стран"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Группы стран"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "Создать новый прайс-лист"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Создайте новый продукт"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "Создать новый вариант продукта"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid "Create a product"
msgstr "Создать продукт"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_document__create_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_tag__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
msgid "Created by"
msgstr "Создано"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_document__create_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_tag__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
msgid "Created on"
msgstr "Создано"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "Creation"
msgstr "Создать"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "Кубические футы"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "Кубические метры"

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "Пользовательское значение"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Справка клиента"

#. module: product
#: model:product.template,name:product.product_product_4_product_template
msgid "Customizable Desk"
msgstr "Настраиваемый стол"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__db_datas
msgid "Database Data"
msgstr "Данные базы данных"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Десятичная точность"

#. module: product
#. odoo-python
#: code:addons/product/models/res_company.py:0
#, python-format
msgid "Default %s pricelist"
msgstr "По умолчанию %s прейскурант"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__default_extra_price
msgid "Default Extra Price"
msgstr "Дополнительная цена по умолчанию"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Единица измерения по умолчанию используется для всех складских операций."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"Единица измерения по умолчанию, используемая для заказов на поставку. Она "
"должна находиться в той же категории, что и единица измерения по умолчанию."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Define a new tag"
msgstr "Определите новый тег"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your volume unit of measure"
msgstr "Определите единицу измерения объема"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your weight unit of measure"
msgstr "Определите единицу измерения веса"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Delete"
msgstr "Удалить"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Срок поставки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__description
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Описание"

#. module: product
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "Стол комбинированный"

#. module: product
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "Подставка настольная с экраном"

#. module: product
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr "Стол комбинированный, черно-коричневый: стул + стол + ящик."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "Определите порядок отображения"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Discard"
msgstr "Отменить"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Discount"
msgstr "Скидка"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__discount
msgid "Discount (%)"
msgstr "Скидка (%)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__discount_policy
msgid "Discount Policy"
msgstr "Политика скидок"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__with_discount
msgid "Discount included in the price"
msgstr "Скидка включена в стоимость"

#. module: product
#: model:res.groups,name:product.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Скидка на линии"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price_discounted
msgid "Discounted Price"
msgstr "Цена со скидкой"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_discount_per_so_line
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Discounts"
msgstr "Скидки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_document__display_name
#: model:ir.model.fields,field_description:product.field_product_label_layout__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_tag__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Display Pricelist"
msgstr "Показать прейскурант"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_type
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_type
msgid "Display Type"
msgstr "Тип отображения"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Document"
msgstr "Документ"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#: model:ir.model.fields,field_description:product.field_product_product__product_document_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_document_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#, python-format
msgid "Documents"
msgstr "Документы"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_document_count
#: model:ir.model.fields,field_description:product.field_product_template__product_document_count
msgid "Documents Count"
msgstr "Подсчет документов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_search
msgid "Documents of this variant"
msgstr "Документы этого варианта"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Download"
msgstr "Скачать"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Download examples"
msgstr "Примеры для скачивания"

#. module: product
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "Ящик"

#. module: product
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "Черный ящик"

#. module: product
#: model_terms:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "Ящик с возможностью установки двух направляющих."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Продолжительность"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "Динамически"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr ""
"Каждое значение должно быть определено только один раз для каждого атрибута "
"продукта."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Eco-friendly Wooden Chair"
msgstr "Экологически чистый деревянный стул"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
msgid "Edit"
msgstr "Редактировать"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Дата окончания"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Дата окончания действия цены данного поставщика"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid ""
"Ending datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Время окончания проверки элемента прейскуранта\n"
"Отображаемое значение зависит от часового пояса, установленного в настройках."

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Ergonomic"
msgstr "Эргономичный"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "Исключить для"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Явное имя правила для этой строки прейскуранта."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__extra_html
msgid "Extra Content"
msgstr "Дополнительный контент"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Extra Fee"
msgstr "Дополнительная плата"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""
"Дополнительная цена для варианта с данным значением атрибута по цене "
"продажи. Например, 200 цен дополнительно, 1000 + 200 = 1200."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__priority
#: model:ir.model.fields,field_description:product.field_product_template__priority
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__1
msgid "Favorite"
msgstr "Избранное"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Favorites"
msgstr "Избранное"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__datas
msgid "File Content (base64)"
msgstr "Содержимое файла (base64)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__raw
msgid "File Content (raw)"
msgstr "Содержимое файла (необработанное)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__file_size
msgid "File Size"
msgstr "Размер файла"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "Фиксированная цена"

#. module: product
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "Перекидной"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_type_icon
#: model:ir.model.fields,help:product.field_product_product__activity_type_icon
#: model:ir.model.fields,help:product.field_product_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Шрифт, отличный значок, например. к.-а.-задачи"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Чтобы правило применялось, купленное/продаваемое количество должно быть больше или равно минимальному количеству, указанному в этом поле.\n"
"Выражается в стандартной единице измерения продукта."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__print_format
msgid "Format"
msgstr "Формат"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "Формула"

#. module: product
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "Стол четырехместный"

#. module: product
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "Современная офисная рабочая станция на четыре человека"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Планируемые действия"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Основная информация"

#. module: product
#: model:ir.actions.server,name:product.action_product_price_list_report
#: model:ir.actions.server,name:product.action_product_template_price_list_report
msgid "Generate Pricelist Report"
msgstr "Создать отчет по прейскуранту"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Get product pictures using Barcode"
msgstr "Получение изображений товаров с помощью штрих-кода"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "Дает разные способы упаковки одного и того же продукта."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Указывает порядок следования при отображении списка товаров"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Gold Member Pricelist"
msgstr "Прейскурант для золотых членов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Google Images"
msgstr "Google Images"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Group By"
msgstr "Группировать по"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "Индекс цвета HTML"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__has_message
#: model:ir.model.fields,field_description:product.field_product_product__has_message
#: model:ir.model.fields,field_description:product.field_product_template__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__html_color
#: model:ir.model.fields,help:product.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""
"Здесь вы можете задать определенный индекс цвета HTML (например, #ff0000) "
"для отображения цвета, если тип атрибута - 'Color'."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "History"
msgstr "История"

#. module: product
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Размещение в отеле"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_document__id
#: model:ir.model.fields,field_description:product.field_product_label_layout__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_tag__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий исключение Дела."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""
"Если не установлено, цена поставщика будет применяться ко всем вариантам "
"этого продукта."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr "Если флажок снят, это позволит вам скрыть прейскурант, не удаляя его."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "Если флажок не установлен, он позволит скрыть продукт, не удаляя его."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__image
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__image
msgid "Image"
msgstr "Изображение"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "Изображение 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "Изображение 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "Изображение 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "Изображение 512"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_height
msgid "Image Height"
msgstr "Высота изображения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_src
msgid "Image Src"
msgstr "Источник изображения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__image_width
msgid "Image Width"
msgstr "Ширина изображения"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_kanban
msgid "Image is a link"
msgstr "Изображение является ссылкой"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Import Template for Pricelists"
msgstr "Импорт шаблона для прайс-листов"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Шаблон импорта для продуктов"

#. module: product
#. odoo-python
#: code:addons/product/models/product_supplierinfo.py:0
#, python-format
msgid "Import Template for Vendor Pricelists"
msgstr "Импорт шаблона для прайс-листов поставщиков"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "Неактивный"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__index_content
msgid "Indexed Content"
msgstr "Индексированное содержимое"

#. module: product
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "Индивидуальное рабочее место"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "Немедленно"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "Внутренние заметки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "Внутренняя ссылка"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
msgid "International Article Number used for product identification."
msgstr "Международный номер изделия, используемый для идентификации продукта."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Склад"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "Является вариантом продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr "Является настраиваемым продуктом"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "Является вариантом продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__is_custom
msgid "Is custom value"
msgstr "Является пользовательским значением"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__public
msgid "Is public document"
msgstr "Является публичным документом"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilograms"
msgstr "Килограммы"

#. module: product
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "Шкаф большой"

#. module: product
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "Стол большой"

#. module: product
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "Стол большой переговорный"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_document__write_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_tag__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_document__write_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_tag__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Поздние Мероприятия"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"Время ожидания в днях между подтверждением заказа на покупку и поступлением "
"продукции на ваш склад. Используется планировщиком для автоматического "
"расчета планирования заказов на поставку."

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "Ножки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "Линии"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Locally handmade"
msgstr "Местная ручная работа"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "Логистика"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"Вам нужна индивидуальная окраска бамбука под имеющуюся мебель? Свяжитесь с "
"нами, чтобы узнать цену."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""
"Сделайте значение этого атрибута несовместимым с другими значениями продукта"
" или некоторыми значениями атрибутов дополнительных и вспомогательных "
"продуктов."

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Управление упаковкой продукции"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Управление вариантами продуктов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Margins"
msgstr "Маржа"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Макс. Маржа"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Макс. Ценовая маржа"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_ids
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__mimetype
msgid "Mime Type"
msgstr "Mime-тип"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Мин. Маржа"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Мин. Цена"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Мин. Количество"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__multi
msgid "Multi-checkbox (option)"
msgstr "Многофункциональный флажок (опция)"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_check_multi_checkbox_no_variant
msgid ""
"Multi-checkbox display type is not compatible with the creation of variants"
msgstr ""
"Тип отображения с несколькими флажками не совместим с созданием вариантов"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__basic
msgid "Multiple prices per product"
msgstr "Несколько цен на один продукт"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__product_pricelist_setting
msgid ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."
msgstr ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__name
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_document__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_tag__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Имя"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never (option)"
msgstr "Никогда (опция)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следующее событие календаря активности"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Следующий срок мероприятия"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_summary
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего действия"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Следующий Тип Мероприятия"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid ""
"No product to print, if the product is archived please unarchive it before "
"printing its label."
msgstr ""
"Нет продукта для печати, если продукт заархивирован, пожалуйста, "
"разархивируйте его перед печатью этикетки."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid "No products could be found."
msgstr "Продукты не могут быть найдены."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid "No vendor pricelist found"
msgstr "Прейскурант поставщика не найден"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__0
msgid "Normal"
msgstr "Нормальный"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Note:"
msgstr "Заметка:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__number_related_products
msgid "Number Related Products"
msgstr "Номер Сопутствующие товары"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr "Количество ценовых правил"

#. module: product
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "Cтул офисный"

#. module: product
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "Cтул офисный черный"

#. module: product
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "Программное обеспечение для дизайна офиса"

#. module: product
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "Лампа офисная"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"On the product %(product)s you cannot associate the value %(value)s with the"
" attribute %(attribute)s because they do not match."
msgstr ""
"Для продукта %(product)s вы не можете связать значение %(value)s с атрибутом"
" %(attribute)s, потому что они не совпадают."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"On the product %(product)s you cannot transform the attribute "
"%(attribute_src)s into the attribute %(attribute_dest)s."
msgstr ""
"На изделии %(product)s нельзя преобразовать атрибут %(attribute_src)s в "
"атрибут %(attribute_dest)s."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Оригинальное (неоптимизированное, без размера) вложение"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
msgid "Other Pricelist"
msgstr "Другой прайс-лист"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Package Type A"
msgstr "Тип упаковки A"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Упаковка"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Родительская категория"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: product
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "Корзина с педалью"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Процент цены"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__pills
msgid "Pills"
msgstr "Таблетки"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "Please enter a positive whole number."
msgstr "Пожалуйста, введите целое положительное число."

#. module: product
#. odoo-python
#: code:addons/product/models/product_document.py:0
#, python-format
msgid ""
"Please enter a valid URL.\n"
"Example: https://www.odoo.com\n"
"\n"
"Invalid URL: %s"
msgstr ""
"Введите действительный URL-адрес.\n"
"Пример: https://www.odoo.com\n"
"\n"
"Недопустимый URL: %s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Please specify the category for which this rule should be applied"
msgstr "Укажите категорию, для которой должно применяться это правило"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Please specify the product for which this rule should be applied"
msgstr "Укажите продукт, для которого должно применяться это правило"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr "Укажите вариант продукта, для которого должно применяться это правило"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pounds"
msgstr "Фунты стерлингов"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""
"Нажмите кнопку и наблюдайте, как за считанные секунды ваш стол плавно "
"переходит из положения сидя в положение стоя."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "Цена"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Расчет цены"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Скидка с цены"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Округление цен"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#, python-format
msgid "Price Rules"
msgstr "Правила ценообразования"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Price Surcharge"
msgstr "Надбавка к цене"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
msgid "Price at which the product is sold to customers."
msgstr "Цена, по которой товар продается покупателям."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Цена:"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_label_layout__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#, python-format
msgid "Pricelist"
msgstr "Прайс-лист"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Позиция прайс-листа, применимая к выбранному варианту"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Название прейскуранта"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#: model:ir.model,name:product.model_report_product_report_pricelist
#, python-format
msgid "Pricelist Report"
msgstr "Отчет по прейскуранту"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Pricelist Rule"
msgstr "Правило прейскуранта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Pricelist Rules"
msgstr "Правила прейскуранта"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Pricelist:"
msgstr "Прайс-лист:"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Прайс-листы"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_pricelist_setting
msgid "Pricelists Method"
msgstr "Метод прейскурантов"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Прайс-листами управляют на"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Ценообразование"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Print"
msgstr "Печать"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Print Labels"
msgstr "Печать этикеток"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_ids
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_view_kanban_catalog
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product"
msgstr "Товар"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Атрибут продукта"

#. module: product
#: model:ir.model,name:product.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Пользовательское значение атрибута продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "Значения атрибута продукта"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Ценности и атрибуты продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Атрибуты товаров"

#. module: product
#: model:ir.model,name:product.model_product_catalog_mixin
msgid "Product Catalog Mixin"
msgstr "Миксин каталога товаров"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Категории товаров"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Category"
msgstr "Категория товара"

#. module: product
#: model:ir.model,name:product.model_product_document
msgid "Product Document"
msgstr "Документ товара"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_dymo
msgid "Product Label (PDF)"
msgstr "Этикетка продукта (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_2x7
msgid "Product Label 2x7 (PDF)"
msgstr "Этикетка для продукции 2x7 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12
msgid "Product Label 4x12 (PDF)"
msgstr "Этикетка для продукции 4x12 (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x12_noprice
msgid "Product Label 4x12 No Price (PDF)"
msgstr "Товарная этикетка 4x12 без цены (PDF)"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label_4x7
msgid "Product Label 4x7 (PDF)"
msgstr "Этикетка для продукции 4x7 (PDF)"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel_dymo
msgid "Product Label Report"
msgstr "Отчет об этикетке продукта"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel2x7
msgid "Product Label Report 2x7"
msgstr "Отчет об этикетке продукта 2x7"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12
msgid "Product Label Report 4x12"
msgstr "Отчет об этикетке продукта 4x12"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x12noprice
msgid "Product Label Report 4x12 No Price"
msgstr "Отчет об этикетке продукта 4x12 Без цены"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel4x7
msgid "Product Label Report 4x7"
msgstr "Отчет об этикетке продукта 4x7"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Название товара"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Пакеты продуктов"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model:ir.model.fields,field_description:product.field_product_packaging__name
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "Упаковка продукта"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "Упаковка продукта (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "Упаковки продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_properties_definition
msgid "Product Properties"
msgstr "Свойства продукта"

#. module: product
#: model:ir.model,name:product.model_product_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Tag"
msgstr "Метки товара"

#. module: product
#: model:ir.actions.act_window,name:product.product_tag_action
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Tags"
msgstr "Теги товара"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Template"
msgstr "Шаблон Продукта"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "Исключение атрибутов шаблона продукта"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Позиция атрибута шаблона продукта"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Значение атрибута шаблона продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tag_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_tag_ids
msgid "Product Template Tags"
msgstr "Теги шаблона товара"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_tag__product_template_ids
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_tree_tag
msgid "Product Templates"
msgstr "Шаблоны продуктов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Шаблон Продукта"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tooltip
#: model:ir.model.fields,field_description:product.field_product_template__product_tooltip
msgid "Product Tooltip"
msgstr "Подсказка по продукту"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__detailed_type
#: model:ir.model.fields,field_description:product.field_product_template__detailed_type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Product Type"
msgstr "Тип товара"

#. module: product
#: model:ir.model,name:product.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Единица измерения продукта"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_document_form
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_tag_tree_view
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid "Product Variant Values"
msgstr "Значения варианта продукта"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.model.fields,field_description:product.field_product_tag__product_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_product_view_tree_tag
#: model_terms:ir.ui.view,arch_db:product.product_tag_form_view
msgid "Product Variants"
msgstr "Варианты продуктов"

#. module: product
#. odoo-python
#: code:addons/product/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "Модель продукта не определена, обратитесь к администратору."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Product: %s"
msgstr "Продукт: %s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_catalog_mixin.py:0
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
#, python-format
msgid "Products"
msgstr "Товары"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Цена товара"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Прайс-лист продукции"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr "Правила поиска цен на товары"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Поиск цен на товары"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#, python-format
msgid "Products: %(category)s"
msgstr "Продукты: %(category)s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_loyalty
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "Акции, купоны, подарочные карты и программа лояльности"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_properties
#: model:ir.model.fields,field_description:product.field_product_template__product_properties
msgid "Properties"
msgstr "Свойства"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Purchase"
msgstr "Покупка"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Описание покупки"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase UoM"
msgstr "Приобрести ЕИ"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Quantities"
msgstr "Количество"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Quantities (Price)"
msgstr "Количество (цена)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__custom_quantity
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "Количество"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "Quantity already present (%s)."
msgstr "Количество уже присутствует (%s)."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr "Количество продуктов, содержащихся в упаковке."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr "Радио"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
msgid "Reference"
msgstr "Справка"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid ""
"Register the prices requested by your vendors for each product, based on the"
" quantity and the period."
msgstr ""
"Зарегистрируйте цены, запрашиваемые вашими поставщиками для каждого "
"продукта, в зависимости от количества и периода."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
#, python-format
msgid "Related Products"
msgstr "Похожие товары"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "Похожие варианты"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__ir_attachment_id
msgid "Related attachment"
msgstr "Связанное крепление"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Remove"
msgstr "Удалить"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Remove quantity"
msgstr "Удалить количество"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_field
msgid "Resource Field"
msgstr "Поле ресурсов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_id
msgid "Resource ID"
msgstr "Номер ресурса"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_model
msgid "Resource Model"
msgstr "Модель ресурсов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__res_name
msgid "Resource Name"
msgstr "Название Ресурса"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Ответственный пользователь"

#. module: product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "Расходы на ресторан"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Метод округления"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__rows
msgid "Rows"
msgstr "Ряды"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__rule_tip
msgid "Rule Tip"
msgstr "Совет по правилам"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Продажи"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales Description"
msgstr "Описание продаж"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "Sales Grid Entry"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales Price"
msgstr "Цена продажи"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sales Price"
msgstr "Цена продажи"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__select
msgid "Select"
msgstr "Выбрать"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__service
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "Услуга"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_view_search_catalog
msgid "Services"
msgstr "Услуги"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Устанавливает цену кратной данному значению.\n"
"Округление применяется после скидки, но  перед надбавкой.\n"
"Так, чтобы цена заканчивалась на 9,99, установите округление 10, надбавку -0,01"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия наступает до "
"сегодняшнего дня"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__without_discount
msgid "Show public price & discount to the customer"
msgstr "Показать публичную цену & скидку для клиента"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Укажите категорию продукта, если это правило применяется только к продуктам,"
" принадлежащим к этой категории или ее дочерним категориям. В противном "
"случае оставьте пустым."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Укажите продукт, если это правило применяется только к одному продукту. В "
"противном случае оставьте пустым."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Укажите шаблон, если это правило применяется только к одному шаблону "
"продукта. В противном случае оставьте пустым."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or subtract (if negative) to the amount "
"calculated with the discount."
msgstr ""
"Укажите фиксированную сумму, которую нужно прибавить или вычесть (если она "
"отрицательная) к сумме, рассчитанной с учетом скидки."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Укажите максимальный размер наценки по отношению к базовой цене."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Укажите минимальный размер маржи по отношению к базовой цене."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Дата начала"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Дата начала действия цены данного поставщика"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid ""
"Starting datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Время начала проверки элементов прейскуранта\n"
"Отображаемое значение зависит от часового пояса, установленного в настройках."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_state
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус, основанный на мероприятии\n"
"Просроченная: Дата, уже прошла\n"
"Сегодня: Дата мероприятия сегодня\n"
"Запланировано: будущая деятельность."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "Steel"
msgstr "Сталь"

#. module: product
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "Коробка для хранения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__store_fname
msgid "Stored Filename"
msgstr "Сохраненное имя файла"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Прайс-лист поставщика"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Название тега уже существует!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Tags"
msgstr "Теги"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_tag_action
msgid "Tags are used to search product for a given theme."
msgstr "Теги используются для поиска товаров по заданной теме."

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Internal Reference '%s' already exists."
msgstr "Внутренняя ссылка '%s' уже существует."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Type of this product doesn't match the Detailed Type"
msgstr "Тип этого продукта не соответствует подробному типу"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"The attribute %(attribute)s must have at least one value for the product "
"%(product)s."
msgstr ""
"Атрибут %(attribute)s должен иметь хотя бы одно значение для продукта "
"%(product)s."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr ""
"Атрибут не может быть изменен, если его значение используется хотя бы в "
"одном продукте."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr "Вычисленная цена выражается в единице измерения товара по умолчанию."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""
"Единица измерения по умолчанию и единица измерения закупок должны быть в "
"одной категории."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__display_type
#: model:ir.model.fields,help:product.field_product_attribute_value__display_type
#: model:ir.model.fields,help:product.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr "Тип дисплея, используемый в конфигураторе продукта."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "Первый в последовательности является стандартным."

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""
"Минимальная высота - 65 см, а для работы стоя максимальная высота - 125 см."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "The minimum margin should be lower than the maximum margin."
msgstr "Минимальная маржа должна быть меньше максимальной."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""
"Количество продуктов этой категории (не учитывает подчиненные категории )"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The number of variants to generate is above allowed limit. You should either"
" not generate variants for each combination or generate them on demand from "
"the sales order. To do so, open the form view of attributes and change the "
"mode of *Create Variants*."
msgstr ""
"Количество генерируемых вариантов превышает допустимый предел. Вам следует "
"либо не генерировать варианты для каждой комбинации, либо генерировать их по"
" запросу из заказа на продажу. Для этого откройте вид формы атрибутов и "
"измените режим *Создавать варианты*."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "Цена для покупки продукта"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The product template is archived so no combination is possible."
msgstr "Шаблон продукта в архиве, поэтому комбинация невозможна."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""
"Количество, которое необходимо приобрести у данного продавца, чтобы "
"воспользоваться ценой, выраженной в единицах измерения продукта продавца, "
"если таковых нет, в единицах измерения продукта по умолчанию, в противном "
"случае."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "The rounding method must be strictly positive."
msgstr "Метод округления должен быть строго положительным."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"Цена продажи задается в шаблоне продукта. Нажмите кнопку «Конфигурирование "
"вариантов», чтобы установить цены дополнительных атрибутов."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"The value %(value)s is not defined for the attribute %(attribute)s on the "
"product %(product)s."
msgstr ""
"Значение %(value)s не определено для атрибута %(attribute)s на продукте "
"%(product)s."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no possible combination."
msgstr "Нет возможных комбинаций."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining closest combination."
msgstr "Ближайших комбинаций не осталось."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining possible combination."
msgstr "Нет оставшихся возможных комбинаций."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""
"Такая конфигурация атрибутов, значений и исключений продукта не приведет ни "
"к одному возможному варианту. Пожалуйста, заархивируйте или удалите ваш "
"продукт напрямую, если это необходимо."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Это сумма дополнительных цен на все атрибуты"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr "Эта заметка добавляется в заказы на продажу и счета-фактуры."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "Это примечание только для внутренних целей."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Этот прейскурант будет использоваться вместо прейскуранта по умолчанию при "
"продажах текущему партнеру"

#. module: product
#. odoo-python
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %s and 1."
msgstr ""
"Эта точность округления выше, чем точность десятичной дроби (%s цифр).\n"
"Это может привести к несоответствиям в вычислениях.\n"
"Пожалуйста, установите точность между %s и 1."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Этот код продукта поставщика будет использоваться при печати запроса на "
"коммерческое предложение. Оставьте пустым, чтобы использовать внутренний "
"код."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Название продукта этого поставщика будет использоваться при печати запроса "
"на коммерческое предложение. Оставьте пустым, чтобы использовать внутреннее "
"название."

#. module: product
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr "Трехместный диван с шезлонгом в стальном сером цвете"

#. module: product
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "Диван трехместный"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Сегодняшние Дела"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__type
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
msgid "Type"
msgstr "Тип"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип Дела для исключения в записи."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "UOM"
msgstr "ЕИ"

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "Unable to find report template for %s format"
msgstr "Невозможно найти шаблон отчета для формата %s"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Unit"
msgstr "Единица"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Unit Price"
msgstr "Цена за единицу"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Ед. изм"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "Наименование единицы измерения"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "Unit price:"
msgstr "Цена за единицу:"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "Units"
msgstr "ед."

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Единицы измерения"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/product_document_kanban/product_document_kanban_controller.xml:0
#, python-format
msgid "Upload"
msgstr "Загрузить"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Upload files to your product"
msgstr "Загружайте файлы в свой продукт"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Upsell & Cross-Sell"
msgstr "Продажи и перекрестные продажи"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__url
msgid "Url"
msgstr "Url"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"Use this feature to store any files you would like to share with your "
"customers"
msgstr ""
"Используйте эту функцию для хранения любых файлов, которыми вы хотели бы "
"поделиться со своими клиентами"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr "Используется на изделиях"

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.xml:0
#, python-format
msgid "Username"
msgstr "Имя пользователя"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Действительные строки атрибутов продукта"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Период действия"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Значение"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_count
msgid "Value Count"
msgstr "Подсчет значений"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Value Price Extra"
msgstr "Стоимость Цена Дополнительно"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"Value of the product (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"Стоимость продукта (автоматически рассчитывается в AVCO).\n"
"        Используется для определения стоимости продукта, когда стоимость покупки неизвестна (например, при корректировке запасов).\n"
"        Используется для расчета наценки на заказы на продажу."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Значения"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "Вариант"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Подсчет вариантов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "Вариант изображения"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "Вариант изображения 1024"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "Вариант изображения 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "Вариант изображения 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "Вариант изображения 512"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Информация о варианте"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Вариант Цена Дополнительно"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "Вариант продавца"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_variant_value_ids
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "Значения переменных"

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid "Variant: %s"
msgstr "Вариант: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Варианты"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variants Creation Mode"
msgstr "Режим создания вариантов"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__partner_id
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Продавец"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Счета поставщиков"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Информация о продавце"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Прайс-листы поставщика"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Код продукта поставщика"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Название продукта поставщика"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Продавцы"

#. module: product
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "Виртуальная постановка дома"

#. module: product
#: model:product.template,name:product.product_product_1_product_template
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Virtual Interior Design"
msgstr "Виртуальный дизайн интерьера"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_document__voice_ids
msgid "Voice"
msgstr "Голос"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Volume"
msgstr "Объем"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "Volume unit of measure"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Метка единицы измерения объема"

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "Внимание!"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "Предупреждения"

#. module: product
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"Мы уделяем особое внимание деталям, поэтому наши столы отличаются "
"превосходным качеством."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight"
msgstr "Вес"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Weight unit of measure"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Этикетка единицы измерения массы"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Белый"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__write_date
msgid "Write Date"
msgstr "Дата записи"

#. module: product
#. odoo-python
#: code:addons/product/models/res_config_settings.py:0
#, python-format
msgid ""
"You are deactivating the pricelist feature. Every active pricelist will be "
"archived."
msgstr ""
"Вы отключаете функцию прейскуранта. Каждый активный прейскурант будет "
"заархивирован."

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""
"Вы устанавливаете десятичную точность менее точную, чем ЕИ:\n"
"%s\n"
"Это может привести к несоответствиям в вычислениях.\n"
"Пожалуйста, увеличьте округление этих единиц измерения или разряды этой десятичной точности."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__percent_price
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_discount
msgid "You can apply a mark-up by setting a negative discount."
msgstr "Вы можете применить наценку, установив отрицательную скидку."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"Вы можете назначить прайс-листы своим клиентам или выбрать один при создании"
" нового коммерческого предложения."

#. module: product
#: model:ir.model.fields,help:product.field_product_document__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Вы можете загрузить файл с вашего компьютера или скопировать/вставить ссылку"
" на свой файл в Интернете."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__image
#: model:ir.model.fields,help:product.field_product_template_attribute_value__image
msgid ""
"You can upload an image that will be used as the color of the attribute "
"value."
msgstr ""
"Вы можете загрузить изображение, которое будет использоваться в качестве "
"цвета значения атрибута."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/order_line/order_line.xml:0
#, python-format
msgid "You can't edit this product in the catalog."
msgstr "Вы не можете редактировать этот продукт в каталоге."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist_item.py:0
#, python-format
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""
"Вы не можете назначить основной прайс-лист как другой прайс-лист в элементе "
"прайс-листа"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the Variants Creation Mode of the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"Вы не можете изменить режим создания вариантов для атрибута %(attribute)s, потому что он используется для следующих продуктов:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the attribute of the value %(value)s because it is used on"
" the following products: %(products)s"
msgstr ""
"Вы не можете изменить атрибут со значением %(value)s, потому что он "
"используется в следующих продуктах: %(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the product of the value %(value)s set on product "
"%(product)s."
msgstr ""
"Вы не можете изменить продукт значения %(value)s, установленного на продукте"
" %(product)s."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot change the value of the value %(value)s set on product "
"%(product)s."
msgstr ""
"Вы не можете изменить значение %(value)s, установленное на изделии "
"%(product)s."

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#, python-format
msgid "You cannot create recursive categories."
msgstr "Вы не можете создавать рекурсивные категории."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_value_value_company_uniq
msgid ""
"You cannot create two values with the same name for the same attribute."
msgstr ""
"Вы не можете создать два значения с одинаковым именем для одного и того же "
"атрибута."

#. module: product
#. odoo-python
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"Вы не можете определить десятичную точность 'Аккаунта' как более высокую, "
"чем коэффициент округления основной валюты компании"

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#: code:addons/product/models/product_category.py:0
#, python-format
msgid "You cannot delete the %s product category."
msgstr "Вы не можете удалить категорию продуктов %s."

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the attribute %(attribute)s because it is used on the following products:\n"
"%(products)s"
msgstr ""
"Вы не можете удалить атрибут %(attribute)s, потому что он используется в следующих продуктах:\n"
"%(products)s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid ""
"You cannot delete the value %(value)s because it is used on the following products:\n"
"%(products)s\n"
" If the value has been associated to a product in the past, you will not be able to delete it."
msgstr ""
"Вы не можете удалить значение %(value)s, поскольку оно используется для следующих продуктов:\n"
"%(products)s\n"
" Если значение было связано с продуктом в прошлом, вы не сможете его удалить."

#. module: product
#. odoo-python
#: code:addons/product/models/product_category.py:0
#, python-format
msgid ""
"You cannot delete this product category, it is the default generic category."
msgstr ""
"Вы не можете удалить эту категорию товаров, она является общей категорией по"
" умолчанию."

#. module: product
#. odoo-python
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot delete those pricelist(s):\n"
"(%s)\n"
", they are used in other pricelist(s):\n"
"%s"
msgstr ""
"Вы не можете удалить этот прейскурант(ы):\n"
"(%s)\n"
", они используются в других прейскурантах:\n"
"%s"

#. module: product
#. odoo-python
#: code:addons/product/models/product_attribute_value.py:0
#, python-format
msgid "You cannot delete value %s because it was used in some products."
msgstr ""
"Вы не можете удалить значение %s, потому что оно использовалось в некоторых "
"продуктах."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_line.py:0
#, python-format
msgid ""
"You cannot move the attribute %(attribute)s from the product %(product_src)s"
" to the product %(product_dest)s."
msgstr ""
"Вы не можете переместить атрибут %(attribute)s из изделия %(product_src)s в "
"изделие %(product_dest)s."

#. module: product
#. odoo-python
#: code:addons/product/models/product_template_attribute_value.py:0
#: code:addons/product/models/product_template_attribute_value.py:0
#, python-format
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""
"Вы не можете обновить связанные варианты из значений. Пожалуйста, обновите "
"связанные значения из вариантов."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/product_catalog/kanban_renderer.xml:0
#, python-format
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service."
msgstr ""
"Вы должны определить продукт для всего, что вы продаете или покупаете,\n"
"                            будь то хранимый продукт, расходный материал или услуга."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Вы должны определить продукт для всего, что вы продаете или покупаете,\n"
"                будь то хранимый продукт, расходный материал или услуга."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Вы должны заполнить информацию о продукте для всего, что вы продаете или покупаете,\n"
"                будь то хранимый продукт, расходный материал или услуга.\n"
"                Форма продукта содержит информацию, упрощающую процесс продажи:\n"
"                цена, примечания в предложении, данные учета, способы закупки и т. д."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Вы должны определить продукт для всего, что вы продаете, будь то физические продукты,\n"
"                расходные материалы или услуги, которые вы предлагаете клиентам.\n"
"                Форма продукта содержит информацию для упрощения процесса продажи:\n"
"                цена, примечания в заказах, бухгалтерские данные, методы закупок и т.д."

#. module: product
#. odoo-javascript
#: code:addons/product/static/src/js/pricelist_report/product_pricelist_report.js:0
#, python-format
msgid "You must leave at least one quantity."
msgstr "Вы должны оставить хотя бы одно количество."

#. module: product
#. odoo-python
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "You need to set a positive quantity."
msgstr "Вам нужно установить положительное значение."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "дней"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Cheese Burger"
msgstr "например, бургер с сыром"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "например, Лампы"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "например, Odoo для предприятий"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "например, Продавцы USD"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_document_form
msgid "on"
msgstr "вкл"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr "за"

#. module: product
#. odoo-python
#: code:addons/product/models/product_product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "product"
msgstr "товар"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "материнская компания"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "шаблон продукта."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "в"
