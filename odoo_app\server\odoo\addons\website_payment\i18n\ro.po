# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
".\n"
"                        <br/>\n"
"                        Vă mulțumim pentru sprijinul acordat organizației noastre.\n"
"                        <br/>\n"
"                        Cu respect."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>Comentariu:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>Data donației:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>Email donator:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>Nume donator:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>ID plată:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>Metodă plată:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Țară...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Nu a fost găsită nicio opțiune de plată potrivită.</strong><br/>\n"
"                                Dacă credeți că este o eroare, vă rugăm să contactați administratorul site-ului."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Atenție</strong> Moneda lipsește sau este incorectă."

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "O donație a fost făcută pe site-ul dvs."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "Un an de revenire culturală."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Payments"
msgstr "Activează plățile"

#. module: website_payment
#: model:ir.actions.server,name:website_payment.action_activate_stripe
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Activate Stripe"
msgstr "Activează Stripe"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Adaugă o descriere aici"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "Adaugă o nouă opțiune pre-completată"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Amount"
msgstr "Sumă"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Amount ("
msgstr "Sumă ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "Sumă("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "Îngrijirea unui copil pentru 1 lună."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "Alegeți suma"

#. module: website_payment
#: model:ir.model,name:website_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/res_config_settings.py:0
#, python-format
msgid "Configure %s"
msgstr "Configurare %s"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#: model:ir.model,name:website_payment.model_res_country
#, python-format
msgid "Country"
msgstr "Țară"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Country\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Țară\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "Țara este necesară."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/options.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "Sumă personalizată"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Dragă"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "Sumă implicită"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Descriptions"
msgstr "Descrieri"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "Afișează opțiunile"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donate"
msgstr "Donați"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "Donează acum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation"
msgstr "Donare"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.snippets
msgid "Donation Button"
msgstr "Buton donație"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "Suma donată trebuie să fie de cel puțin %.2f."

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "Confirmare donație"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "Notificare donație"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email"
msgstr "Email"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Email\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "Email-ul este invalid"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "Email-ul este necesar."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "Câmpul '%s' este obligatoriu"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__first_provider_label
msgid "First Provider Label"
msgstr "Etichetă prim furnizor"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "Intrare"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "Este donație"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__is_stripe_supported_country
#: model:ir.model.fields,field_description:website_payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "Este țară suportată de Stripe"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "Este donație"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "Faceți o donație"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Maximum"
msgstr "Maximum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Minimum"
msgstr "Minimum"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Name"
msgstr "Nume"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid ""
"Name\n"
"                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Nume\n"
"                    <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "Numele este necesar."

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__none
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "Fără"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "Un an în școala primară."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "Un an în școala gimnazială."

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__other_than_paypal
msgid "Other than Paypal"
msgstr "Alta decât Paypal"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Payment Details"
msgstr "Detalii plată"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_provider
msgid "Payment Provider"
msgstr "Furnizor de plată"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Tranzacție plată"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "Procesarea plății a eșuat"

#. module: website_payment
#. odoo-python
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "Plata primită de la donație cu următoarele detalii:"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "Plăți"

#. module: website_payment
#: model:ir.model.fields.selection,name:website_payment.selection__res_config_settings__providers_state__paypal_only
msgid "Paypal Only"
msgstr "Doar Paypal"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "Vă rugăm să selectați sau să introduceți o sumă"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "Opțiuni pre-completate"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_res_config_settings__providers_state
msgid "Providers State"
msgstr "Stare(Județ) furnizori"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "E-mail destinatar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "Shop - Payment"
msgstr "Magazin - Plată"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "Slider"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "Mare sau mică, contribuția ta este esențială."

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/js/payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "Unele informații lipsesc pentru a procesa plata."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Step"
msgstr "Pas"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect nu este disponibil în țara ta, vă rugăm să folosiți alt "
"furnizor de plată."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid ""
"Support most payment methods; Visa, Mastercard, Maestro, Google Pay, Apple "
"Pay, etc. as well as recurring charges."
msgstr ""
"Suportă cele mai multe metode de plată; Visa, Mastercard, Maestro, Google "
"Pay, Apple Pay, etc. precum și plăți recurente."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "Vă mulțumim pentru donația dumneavoastră de"

#. module: website_payment
#. odoo-javascript
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "Suma minimă de donație este %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "Nu există nimic de plătit."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View Alternatives"
msgstr "Vizualizați alternativele"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.res_config_settings_view_form
msgid "View other providers"
msgstr "Vizualizați alți furnizori"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_provider__website_id
msgid "Website"
msgstr "Site web"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
msgid "Website: Donation"
msgstr "Pagină web: Donație"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Write us a comment"
msgstr "Scrie un comentariu"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_information
msgid "Your comment"
msgstr "Comentariul tău"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "făcut pe"
