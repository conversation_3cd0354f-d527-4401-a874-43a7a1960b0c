<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Python Runtime Services" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/python.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The modules described in this chapter provide a wide range of services related to the Python interpreter and its interaction with its environment. Here’s an overview: sys — System-specific paramete..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The modules described in this chapter provide a wide range of services related to the Python interpreter and its interaction with its environment. Here’s an overview: sys — System-specific paramete..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Python Runtime Services &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="sys — System-specific parameters and functions" href="sys.html" />
    <link rel="prev" title="zipapp — Manage executable Python zip archives" href="zipapp.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/python.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipapp.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipapp</span></code> — Manage executable Python zip archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/python.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys.html" title="sys — System-specific parameters and functions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="zipapp.html" title="zipapp — Manage executable Python zip archives"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Runtime Services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="python-runtime-services">
<span id="python"></span><h1>Python Runtime Services<a class="headerlink" href="#python-runtime-services" title="Link to this heading">¶</a></h1>
<p>The modules described in this chapter provide a wide range of services related
to the Python interpreter and its interaction with its environment.  Here’s an
overview:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="sys.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></li>
<li class="toctree-l1"><a class="reference internal" href="sys.monitoring.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sys.monitoring.html#tool-identifiers">Tool identifiers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#registering-and-using-tools">Registering and using tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sys.monitoring.html#events">Events</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#local-events">Local events</a></li>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#ancillary-events">Ancillary events</a></li>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#other-events">Other events</a></li>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#the-stop-iteration-event">The STOP_ITERATION event</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sys.monitoring.html#turning-events-on-and-off">Turning events on and off</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#setting-events-globally">Setting events globally</a></li>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#per-code-object-events">Per code object events</a></li>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#disabling-events">Disabling events</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sys.monitoring.html#registering-callback-functions">Registering callback functions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sys.monitoring.html#callback-function-arguments">Callback function arguments</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sysconfig.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#configuration-variables">Configuration variables</a></li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#installation-paths">Installation paths</a></li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#user-scheme">User scheme</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#posix-user"><code class="docutils literal notranslate"><span class="pre">posix_user</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#nt-user"><code class="docutils literal notranslate"><span class="pre">nt_user</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#osx-framework-user"><code class="docutils literal notranslate"><span class="pre">osx_framework_user</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#home-scheme">Home scheme</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#posix-home"><code class="docutils literal notranslate"><span class="pre">posix_home</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#prefix-scheme">Prefix scheme</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#posix-prefix"><code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="sysconfig.html#nt"><code class="docutils literal notranslate"><span class="pre">nt</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#installation-path-functions">Installation path functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#other-functions">Other functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="sysconfig.html#using-sysconfig-as-a-script">Using <code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> as a script</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="builtins.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code> — Built-in objects</a></li>
<li class="toctree-l1"><a class="reference internal" href="__main__.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code> — Top-level code environment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="__main__.html#name-main"><code class="docutils literal notranslate"><span class="pre">__name__</span> <span class="pre">==</span> <span class="pre">'__main__'</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="__main__.html#what-is-the-top-level-code-environment">What is the “top-level code environment”?</a></li>
<li class="toctree-l3"><a class="reference internal" href="__main__.html#idiomatic-usage">Idiomatic Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="__main__.html#packaging-considerations">Packaging Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="__main__.html#main-py-in-python-packages"><code class="docutils literal notranslate"><span class="pre">__main__.py</span></code> in Python Packages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="__main__.html#id1">Idiomatic Usage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="__main__.html#import-main"><code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">__main__</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="warnings.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code> — Warning control</a><ul>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#warning-categories">Warning Categories</a></li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#the-warnings-filter">The Warnings Filter</a><ul>
<li class="toctree-l3"><a class="reference internal" href="warnings.html#describing-warning-filters">Describing Warning Filters</a></li>
<li class="toctree-l3"><a class="reference internal" href="warnings.html#default-warning-filter">Default Warning Filter</a></li>
<li class="toctree-l3"><a class="reference internal" href="warnings.html#overriding-the-default-filter">Overriding the default filter</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#temporarily-suppressing-warnings">Temporarily Suppressing Warnings</a></li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#testing-warnings">Testing Warnings</a></li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#updating-code-for-new-versions-of-dependencies">Updating Code For New Versions of Dependencies</a></li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#available-functions">Available Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="warnings.html#available-context-managers">Available Context Managers</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="dataclasses.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dataclasses</span></code> — Data Classes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#module-contents">Module contents</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#post-init-processing">Post-init processing</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#class-variables">Class variables</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#init-only-variables">Init-only variables</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#frozen-instances">Frozen instances</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#inheritance">Inheritance</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#re-ordering-of-keyword-only-parameters-in-init">Re-ordering of keyword-only parameters in <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#default-factory-functions">Default factory functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#mutable-default-values">Mutable default values</a></li>
<li class="toctree-l2"><a class="reference internal" href="dataclasses.html#descriptor-typed-fields">Descriptor-typed fields</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="contextlib.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">contextlib</span></code> — Utilities for <code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code>-statement contexts</a><ul>
<li class="toctree-l2"><a class="reference internal" href="contextlib.html#utilities">Utilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="contextlib.html#examples-and-recipes">Examples and Recipes</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#supporting-a-variable-number-of-context-managers">Supporting a variable number of context managers</a></li>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#catching-exceptions-from-enter-methods">Catching exceptions from <code class="docutils literal notranslate"><span class="pre">__enter__</span></code> methods</a></li>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#cleaning-up-in-an-enter-implementation">Cleaning up in an <code class="docutils literal notranslate"><span class="pre">__enter__</span></code> implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#replacing-any-use-of-try-finally-and-flag-variables">Replacing any use of <code class="docutils literal notranslate"><span class="pre">try-finally</span></code> and flag variables</a></li>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#using-a-context-manager-as-a-function-decorator">Using a context manager as a function decorator</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contextlib.html#single-use-reusable-and-reentrant-context-managers">Single use, reusable and reentrant context managers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#reentrant-context-managers">Reentrant context managers</a></li>
<li class="toctree-l3"><a class="reference internal" href="contextlib.html#reusable-context-managers">Reusable context managers</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="abc.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">abc</span></code> — Abstract Base Classes</a></li>
<li class="toctree-l1"><a class="reference internal" href="atexit.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code> — Exit handlers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="atexit.html#atexit-example"><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code> Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="traceback.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a><ul>
<li class="toctree-l2"><a class="reference internal" href="traceback.html#tracebackexception-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="traceback.html#stacksummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="traceback.html#framesummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="traceback.html#traceback-examples">Traceback Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="__future__.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="__future__.html#module-contents">Module Contents</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="gc.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></li>
<li class="toctree-l1"><a class="reference internal" href="inspect.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a><ul>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#types-and-members">Types and members</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#retrieving-source-code">Retrieving source code</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#introspecting-callables-with-the-signature-object">Introspecting callables with the Signature object</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#classes-and-functions">Classes and functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#the-interpreter-stack">The interpreter stack</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#fetching-attributes-statically">Fetching attributes statically</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#current-state-of-generators-coroutines-and-asynchronous-generators">Current State of Generators, Coroutines, and Asynchronous Generators</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#code-objects-bit-flags">Code Objects Bit Flags</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#buffer-flags">Buffer flags</a></li>
<li class="toctree-l2"><a class="reference internal" href="inspect.html#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="site.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a><ul>
<li class="toctree-l2"><a class="reference internal" href="site.html#module-sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="site.html#module-usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="site.html#readline-configuration">Readline configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="site.html#module-contents">Module contents</a></li>
<li class="toctree-l2"><a class="reference internal" href="site.html#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipapp.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipapp</span></code> — Manage executable Python zip archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/python.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys.html" title="sys — System-specific parameters and functions"
             >next</a> |</li>
        <li class="right" >
          <a href="zipapp.html" title="zipapp — Manage executable Python zip archives"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Runtime Services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>