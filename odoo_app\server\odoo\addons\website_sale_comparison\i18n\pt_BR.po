# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_comparison
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_8
msgid "134.7 x 200 x 7.2 mm"
msgstr "134,7 x 200 x 7,2 mm"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_7
msgid "308 g"
msgstr "308 g"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" title=\"Collapse\"/><span>Tags</span>"
msgstr ""
"<i class=\"fa fa-chevron-circle-down o_product_comparison_collpase\" "
"role=\"img\" aria-label=\"Collapse\" "
"title=\"Collapse\"/><span>Marcadores</span>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<i class=\"fa fa-shopping-cart me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-shopping-cart me-2\"/>Adicionar ao carrinho"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"
msgstr "<i class=\"fa fa-trash\" role=\"img\" aria-label=\"Remove\"/>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
msgid "<span class=\"fa fa-exchange me-2\"/>Compare"
msgstr "<span class=\"fa fa-exchange me-2\"/>Comparar"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<span>Tags</span>"
msgstr "<span>Marcadores</span>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>Price:</strong>"
msgstr "<strong>Preço:</strong>"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "<strong>x</strong>"
msgstr "<strong>x</strong>"

#. module: website_sale_comparison
#: model:product.attribute.value,name:website_sale_comparison.product_attribute_value_1
msgid "Apple"
msgstr "Apple"

#. module: website_sale_comparison
#: model:ir.actions.act_window,name:website_sale_comparison.product_attribute_category_action
#: model:ir.ui.menu,name:website_sale_comparison.menu_attribute_category_action
msgid "Attribute Categories"
msgstr "Categorias de atributos"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Bottom of Page"
msgstr "Parte inferior da página"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__name
msgid "Category Name"
msgstr "Nome da categoria"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_add_to_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
#, python-format
msgid "Compare"
msgstr "Comparar"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/js/website_sale_comparison.js:0
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#, python-format
msgid "Compare Products"
msgstr "Comparar produtos"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Contact Us"
msgstr "Entre em contato"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid "Create a new attribute category"
msgstr "Criar uma nova categoria de atributo"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_8
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_2
msgid "Dimensions"
msgstr "Dimensões"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_duration
msgid "Duration"
msgstr "Duração"

#. module: website_sale_comparison
#: model:product.attribute.category,name:website_sale_comparison.product_attribute_category_general_features
msgid "General Features"
msgstr "Recursos gerais"

#. module: website_sale_comparison
#: model_terms:ir.actions.act_window,help:website_sale_comparison.product_attribute_category_action
msgid ""
"Group attributes by category that will appear in the specification\n"
"                part of a product page."
msgstr ""
"Agrupar atributos por categoria que aparecerão na parte de especificação\n"
"de uma página de produto."

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__id
msgid "ID"
msgstr "ID"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "None"
msgstr "Nenhum"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Product"
msgstr "Produto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute
msgid "Product Attribute"
msgstr "Atributo de produto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_attribute_category
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attribute_category_tree_view
msgid "Product Attribute Category"
msgstr "Categoria de atributo do produto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Linha de atributo de modelo de produto"

#. module: website_sale_comparison
#: model:ir.model,name:website_sale_comparison.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Product image"
msgstr "Imagem do produto"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__attribute_ids
msgid "Related Attributes"
msgstr "Atributos relacionados"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_product
msgid "Remove"
msgstr "Remover"

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute_category__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: website_sale_comparison
#: model:ir.model.fields,help:website_sale_comparison.field_product_attribute__category_id
msgid ""
"Set a category to regroup similar attributes under the same section in the "
"Comparison page of eCommerce."
msgstr ""
"Defina uma categoria para reagrupar atributos semelhantes na mesma seção na "
"página de comparação do e-Commerce"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Shop Comparator"
msgstr "Comparador da loja"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.snippet_options
msgid "Specification"
msgstr "Especificação"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "Specifications"
msgstr "Especificações"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_compare
msgid "Uncategorized"
msgstr "Sem categoria"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: website_sale_comparison
#: model:product.attribute,name:website_sale_comparison.product_attribute_7
msgid "Weight"
msgstr "Peso"

#. module: website_sale_comparison
#. odoo-javascript
#: code:addons/website_sale_comparison/static/src/xml/comparison.xml:0
#, python-format
msgid "You can compare max 4 products."
msgstr "Você pode comparar no máximo 4 produtos."

#. module: website_sale_comparison
#: model:ir.model.fields,field_description:website_sale_comparison.field_product_attribute__category_id
msgid "eCommerce Category"
msgstr "Categoria do e-Commerce"

#. module: website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale_comparison.product_attributes_body
msgid "or"
msgstr "ou"
