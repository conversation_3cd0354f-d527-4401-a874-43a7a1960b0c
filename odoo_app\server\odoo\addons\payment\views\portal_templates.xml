<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Integration of a conditional "Manage payment methods" link in /my -->
    <!-- This template is no more used and should be removed once portal is merged. -->
    <template id="payment.pay_meth_link" inherit_id="portal.side_content">
          <!--<xpath expr="//div[hasclass('o_portal_my_details')]" position="inside">
            <t t-set="partner" t-value="request.env.user.partner_id"/>
            <t t-set="providers_allowing_tokenization"
               t-value="request.env['payment.provider'].sudo()._get_compatible_providers(request.env.company.id, partner.id, 0., force_tokenization=True, is_validation=True)"/>
            <t t-set="existing_tokens" t-value="partner.payment_token_ids + partner.commercial_partner_id.sudo().payment_token_ids"/>
             Only show the link if a token can be created or if one already exists
            <div t-if="providers_allowing_tokenization or existing_tokens"
                 class='manage_payment_method mt16'>
                <a href="/my/payment_method">Manage payment methods</a>
            </div>
        </xpath>
        -->
    </template>

    <!-- Display of /payment/pay -->
    <template id="payment.pay">
        <!-- Parameters description:
            - reference_prefix: The custom prefix to compute the full transaction reference.
            - amount: The amount to pay.
            - currency: The currency of the payment, as a `res.currency` record.
            - partner_id: The id of the partner on behalf of whom the payment should be made.
            - payment_methods_sudo: The compatible payment methods, as a sudoed `payment.method`
                                    recordset.
            - tokens_sudo: The available payment tokens, as a sudoed `payment.token` recordset.
            - res_company: The company in which the payment if made (for the company logo).
            - company_mismatch: Whether the user should make the payment in another company.
            - expected_company: The record of the company that the user should switch to.
            - partner_is_different: Whether the partner logged in is the one making the payment.
        -->
        <t t-call="portal.frontend_layout">
            <t t-set="page_title" t-value="'Payment'"/>
            <t t-set="additional_title"><t t-esc="page_title"/></t>
            <div class="wrap">
                <div class="container">
                    <!-- Portal breadcrumb -->
                    <t t-call="payment.portal_breadcrumb"/>
                    <!-- Payment page -->
                    <div class="row justify-content-center my-3">
                        <div class="col-lg-7">
                            <div t-if="not amount" class="alert alert-info">
                                There is nothing to pay.
                            </div>
                            <div t-elif="not currency" class="alert alert-warning">
                                <strong>Warning</strong> The currency is missing or incorrect.
                            </div>
                            <div t-elif="not partner_id" class="alert alert-warning">
                                <strong>Warning</strong> You must be logged in to pay.
                            </div>
                            <div t-elif="company_mismatch">
                                <t t-call="payment.company_mismatch_warning"/>
                            </div>
                            <div t-elif="not payment_methods_sudo and not tokens_sudo"
                                 class="alert alert-warning"
                            >
                                <strong>No suitable payment method could be found.</strong><br/>
                                If you believe that it is an error, please contact the website
                                administrator.
                            </div>
                            <t t-else="">
                                <div t-if="partner_is_different" class="alert alert-warning">
                                    <strong>Warning</strong> Make sure you are logged in as the
                                    correct partner before making this payment.
                                </div>
                                <div class="o_cc o_cc2 row row-cols-1 row-cols-md-2 mx-0 py-2 rounded">
                                    <t t-call="payment.summary_item">
                                        <t t-set="name" t-value="'amount'"/>
                                        <t t-set="label">Amount</t>
                                        <t t-set="value" t-value="amount"/>
                                        <t t-set="options"
                                           t-value="{'widget': 'monetary', 'display_currency': currency}"
                                        />
                                    </t>
                                    <t t-call="payment.summary_item">
                                        <t t-set="name" t-value="'reference'"/>
                                        <t t-set="label">Reference</t>
                                        <t t-set="value" t-value="reference_prefix"/>
                                        <t t-set="include_separator" t-value="True"/>
                                    </t>
                                </div>
                                <div class="mt-4">
                                    <t t-call="payment.form"/>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="payment.company_mismatch_warning" name="Company Mismatch Warning">
        <!-- Parameters description:
            - expected_company: The record of the company that the user should switch to.
        -->
        <div class="row mr16">
            <div class="alert alert-warning col-lg-12 ms-3 me-3" role="alert">
                <p>
                    Please switch to company <t t-esc="expected_company.name"/> to make this
                    payment.
                </p>
            </div>
        </div>
    </template>

    <!-- Display of /my/payment_methods -->
    <template id="payment.payment_methods" name="Payment Methods">
        <t t-call="portal.frontend_layout">
            <t t-set="page_title" t-value="'Payment Methods'"/>
            <t t-set="additional_title"><t t-esc="page_title"/></t>
            <div class="wrap">
                <div class="container">
                    <!-- Portal breadcrumb -->
                    <t t-call="payment.portal_breadcrumb"/>
                    <!-- Payment methods page -->
                    <div class="row justify-content-center">
                        <div class="col-lg-7">
                            <div t-if="not payment_methods_sudo and not tokens_sudo"
                                 class="alert alert-warning"
                            >
                                <strong>No suitable payment method could be found.</strong><br/>
                                If you believe that it is an error, please contact the website
                                administrator.
                            </div>
                            <t t-else="" t-call="payment.form"/>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Display of /payment/status -->
    <template id="payment.payment_status" name="Payment Status">
        <t t-call="portal.frontend_layout">
            <t t-set="page_title" t-value="'Payment Status'"/>
            <t t-set="additional_title"><t t-esc="page_title"/></t>
            <div class="wrap">
                <div class="container">
                    <!-- Portal breadcrumb -->
                    <t t-call="payment.portal_breadcrumb"/>
                    <!-- Status page -->
                    <div name="o_payment_status" class="row justify-content-center my-3">
                        <div name="o_payment_status_content"
                             class="col-12 col-lg-8">
                            <!-- The content is generated in JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Display of /payment/confirmation -->
    <template id="payment.confirm">
        <!-- Parameters description:
            - tx: The transaction to display.
        -->
        <t t-call="portal.frontend_layout">
            <t t-set="page_title" t-value="'Payment Confirmation'"/>
            <t t-set="additional_title"><t t-esc="page_title"/></t>
            <t t-set="show_pm" t-value="tx.payment_method_code != 'unknown'"/>
            <div class="wrap">
                <div class="container">
                    <!-- Portal breadcrumb -->
                    <t t-call="payment.portal_breadcrumb"/>
                    <div class="row justify-content-center my-3">
                    <div class="col-12 col-lg-7">
                        <!-- Confirmation page -->
                        <div class="row">
                            <div class="col">
                                <t t-call="payment.transaction_status"/>
                            </div>
                        </div>
                        <div t-att-class="'o_cc o_cc2 row row-cols-1 mx-0 mb-3 py-2 rounded'
                                          + (' row-cols-md-4' if show_pm else ' row-cols-md-3')"
                        >
                            <t t-call="payment.summary_item">
                                <t t-set="name" t-value="'amount'"/>
                                <t t-set="label">Amount</t>
                                <t t-set="value" t-value="tx.amount"/>
                                <t t-set="options"
                                   t-value="{'widget': 'monetary', 'display_currency': tx.currency_id}"
                                />
                            </t>
                            <t t-call="payment.summary_item">
                                <t t-set="name" t-value="'reference'"/>
                                <t t-set="label">Reference</t>
                                <t t-set="value" t-value="tx.reference"/>
                                <t t-set="include_separator" t-value="True"/>
                            </t>
                            <t t-if="tx.payment_method_code != 'unknown'">
                               <t t-call="payment.summary_item">
                                    <t t-set="name" t-value="'method'"/>
                                    <t t-set="label">Payment Method</t>
                                    <t t-set="value" t-value="tx.payment_method_id.name"/>
                                    <t t-set="include_separator" t-value="True"/>
                               </t>
                            </t>
                            <t t-call="payment.summary_item">
                                <t t-set="name" t-value="'provider'"/>
                                <t t-set="label">Processed by</t>
                                <t t-set="value" t-value="tx.provider_id.sudo().name"/>
                                <t t-set="include_separator" t-value="True"/>
                            </t>
                        </div>

                        <div class="row">
                            <div class="col offset-md-3 ps-0">
                                <a role="button" class="btn btn-primary float-end" href="/my/home">
                                    Go to my Account <i class="oi oi-arrow-right ms-2"/>
                                </a>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Breadcrumb for the portal -->
    <template id="payment.portal_breadcrumb">
        <!-- Parameters description:
            - page_title: The title of the breadcrumb item.
        -->
        <div class="row">
            <div class="col-md-6">
                <ol class="breadcrumb px-0 mt16">
                    <li id="o_payment_portal_home" class="breadcrumb-item">
                        <a href="/my/home">
                            <i class="fa fa-home"
                               role="img"
                               title="Home"
                               aria-label="Home"/>
                        </a>
                    </li>
                    <li class="breadcrumb-item"><t t-esc="page_title"/></li>
                </ol>
            </div>
        </div>
    </template>

    <template id="payment.summary_item">
        <!-- Parameters description:
            - name: The summary item name that is suffixed to `o_payment_summary_` to create the id.
            - label: The label that is displayed.
            - value: The value of the summary item.
            - options: The widget options to set.
            - include_separator: Whether the summary item should be preceded by a separator.
        -->
        <t t-set="options" t-value="options or {'widget': 'string'}"/>
        <hr t-if="include_separator" class="d-md-none m-0 text-300 opacity-100"/>
        <div t-att-class="'col my-3 text-break'
                          + (' o_payment_summary_separator' if include_separator else '')"
        >
            <label t-attf-for="o_payment_summary_{{name}}"
                   t-out="label"
                   class="d-block small text-muted"
            />
            <span t-attf-id="o_payment_summary_{{name}}"
                  t-out="value"
                  t-options="options"
                  class="fs-5 fw-bold"
            />
        </div>
    </template>

    <template id="payment.transaction_status">
        <!-- Parameters description:
            - tx: The transaction whose status must be displayed.
        -->
        <t t-if="tx.state == 'draft'">
            <t t-set="alert_style" t-value="'info'"/>
            <t t-set="status_message">
                <p>Your payment has not been processed yet.</p>
            </t>
        </t>
        <t t-elif="tx.state == 'pending'">
            <t t-set="alert_style" t-value="'warning'"/>
            <t t-set="status_message" t-value="tx.provider_id.sudo().pending_msg"/>
        </t>
        <t t-elif="tx.state == 'authorized'">
            <t t-set="alert_style" t-value="'success'"/>
            <t t-set="status_message" t-value="tx.provider_id.sudo().auth_msg"/>
        </t>
        <t t-elif="tx.state == 'done'">
            <t t-set="alert_style" t-value="'success'"/>
            <t t-set="status_heading">Thank you!</t>
            <t t-set="status_message" t-value="tx.provider_id.sudo().done_msg"/>
        </t>
        <t t-elif="tx.state == 'cancel'">
            <t t-set="alert_style" t-value="'danger'"/>
            <t t-set="status_message" t-value="tx.provider_id.sudo().cancel_msg"/>
        </t>
        <t t-elif="tx.state == 'error'">
            <t t-set="alert_style" t-value="'danger'"/>
            <t t-set="status_message">
                <p class="mb-0">An error occurred during the processing of your payment.</p>
            </t>
        </t>

        <t t-if="is_html_empty(status_message)" t-set="status_message" t-value="''"/>
        <t t-set="o_payment_status_alert_class"
           t-value="'alert alert-'+ alert_style +' d-flex gap-3'"
        />

        <div t-if="status_message or tx.state_message"
             name="o_payment_status_alert"
             t-attf-class="{{o_payment_status_alert_class}}"
        >
            <div id="o_payment_status_icon">
                <i t-attf-class="fa fa-{{'check' if alert_style == 'success' else 'info-circle'
                                 if alert_style == 'info' else 'exclamation-triangle'}}"
                />
            </div>
            <div id="o_payment_status_message" class="w-100">
                <h5 t-if="status_heading" t-out="status_heading" class="alert-heading mb-0"/>
                <t t-if="status_message" t-out="status_message" class="mb-0"/>
                <t t-if="tx.state_message" t-out="tx.state_message"  class="mb-0"/>
            </div>
        </div>
    </template>

    <!-- "Manage payment methods" card on /my -->
    <template id="portal_my_home_payment" name="Show Payment Method" customize_show="True" inherit_id="portal.portal_my_home" priority="60">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_client_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_client_category" position="inside">
            <t t-set="partner_sudo" t-value="request.env.user.partner_id"/>
            <t t-set="providers_sudo"
               t-value="request.env['payment.provider'].sudo()._get_compatible_providers(request.env.company.id, partner_sudo.id, 0., force_tokenization=True, is_validation=True)"/>
            <t t-set="methods_allowing_tokenization"
               t-value="request.env['payment.method'].sudo()._get_compatible_payment_methods(
                            providers_sudo.ids,
                            partner_sudo.id,
                            force_tokenization=True,
                        )"
            />
            <t t-set="existing_tokens" t-value="partner_sudo.payment_token_ids + partner_sudo.commercial_partner_id.payment_token_ids"/>
            <t t-if="methods_allowing_tokenization or existing_tokens" t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/payment/static/img/payment-methods.svg'"/>
                <t t-set="title">Payment methods</t>
                <t t-set="text">Manage your payment methods</t>
                <t t-set="url" t-value="'/my/payment_method'"/>
                <t t-set="config_card" t-value="True"/>
            </t>
        </div>
    </template>

</odoo>
