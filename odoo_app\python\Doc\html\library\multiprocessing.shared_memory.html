<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="multiprocessing.shared_memory — Shared memory for direct access across processes" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/multiprocessing.shared_memory.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/multiprocessing/shared_memory.py This module provides a class, SharedMemory, for the allocation and management of shared memory to be accessed by one or more processes on a multico..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/multiprocessing/shared_memory.py This module provides a class, SharedMemory, for the allocation and management of shared memory to be accessed by one or more processes on a multico..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>multiprocessing.shared_memory — Shared memory for direct access across processes &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="The concurrent package" href="concurrent.html" />
    <link rel="prev" title="multiprocessing — Process-based parallelism" href="multiprocessing.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/multiprocessing.shared_memory.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="multiprocessing.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code> — Process-based parallelism</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="concurrent.html"
                          title="next chapter">The <code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent</span></code> package</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/multiprocessing.shared_memory.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="concurrent.html" title="The concurrent package"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="multiprocessing.html" title="multiprocessing — Process-based parallelism"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" accesskey="U">Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing.shared_memory</span></code> — Shared memory for direct access across processes</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-multiprocessing.shared_memory">
<span id="multiprocessing-shared-memory-shared-memory-for-direct-access-across-processes"></span><h1><a class="reference internal" href="#module-multiprocessing.shared_memory" title="multiprocessing.shared_memory: Provides shared memory for direct access across processes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing.shared_memory</span></code></a> — Shared memory for direct access across processes<a class="headerlink" href="#module-multiprocessing.shared_memory" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/multiprocessing/shared_memory.py">Lib/multiprocessing/shared_memory.py</a></p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<hr class="docutils" id="index-0" />
<p>This module provides a class, <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>, for the allocation
and management of shared memory to be accessed by one or more processes
on a multicore or symmetric multiprocessor (SMP) machine.  To assist with
the life-cycle management of shared memory especially across distinct
processes, a <a class="reference internal" href="multiprocessing.html#multiprocessing.managers.BaseManager" title="multiprocessing.managers.BaseManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseManager</span></code></a> subclass,
<a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager" title="multiprocessing.managers.SharedMemoryManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code></a>, is also provided in the
<a class="reference internal" href="multiprocessing.html#module-multiprocessing.managers" title="multiprocessing.managers: Share data between process with shared objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing.managers</span></code></a> module.</p>
<p>In this module, shared memory refers to “POSIX style” shared memory blocks
(though is not necessarily implemented explicitly as such) and does not refer
to “distributed shared memory”.  This style of shared memory permits distinct
processes to potentially read and write to a common (or shared) region of
volatile memory.  Processes are conventionally limited to only have access to
their own process memory space but shared memory permits the sharing
of data between processes, avoiding the need to instead send messages between
processes containing that data.  Sharing data directly via memory can provide
significant performance benefits compared to sharing data via disk or socket
or other communications requiring the serialization/deserialization and
copying of data.</p>
<dl class="py class">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">multiprocessing.shared_memory.</span></span><span class="sig-name descname"><span class="pre">SharedMemory</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">create</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory" title="Link to this definition">¶</a></dt>
<dd><p>Create an instance of the <code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code> class for either
creating a new shared memory block or attaching to an existing shared
memory block.  Each shared memory block is assigned a unique name.
In this way, one process can create a shared memory block with a
particular name and a different process can attach to that same shared
memory block using that same name.</p>
<p>As a resource for sharing data across processes, shared memory blocks
may outlive the original process that created them.  When one process
no longer needs access to a shared memory block that might still be
needed by other processes, the <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.close" title="multiprocessing.shared_memory.SharedMemory.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> method should be called.
When a shared memory block is no longer needed by any process, the
<a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.unlink" title="multiprocessing.shared_memory.SharedMemory.unlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code></a> method should be called to ensure proper cleanup.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> (<a class="reference internal" href="stdtypes.html#str" title="str"><em>str</em></a><em> | </em><em>None</em>) – The unique name for the requested shared memory, specified as a string.
When creating a new shared memory block, if <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default)
is supplied for the name, a novel name will be generated.</p></li>
<li><p><strong>create</strong> (<a class="reference internal" href="functions.html#bool" title="bool"><em>bool</em></a>) – Control whether a new shared memory block is created (<code class="docutils literal notranslate"><span class="pre">True</span></code>)
or an existing shared memory block is attached (<code class="docutils literal notranslate"><span class="pre">False</span></code>).</p></li>
<li><p><strong>size</strong> (<a class="reference internal" href="functions.html#int" title="int"><em>int</em></a>) – The requested number of bytes when creating a new shared memory block.
Because some platforms choose to allocate chunks of memory
based upon that platform’s memory page size, the exact size of the shared
memory block may be larger or equal to the size requested.
When attaching to an existing shared memory block,
the <em>size</em> parameter is ignored.</p></li>
</ul>
</dd>
</dl>
<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory.close" title="Link to this definition">¶</a></dt>
<dd><p>Close access to the shared memory from this instance.  In order to
ensure proper cleanup of resources, all instances should call
<a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.close" title="multiprocessing.shared_memory.SharedMemory.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> once the instance is no longer needed.  Note that calling
<code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code> does not cause the shared memory block itself to be
destroyed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory.unlink">
<span class="sig-name descname"><span class="pre">unlink</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory.unlink" title="Link to this definition">¶</a></dt>
<dd><p>Request that the underlying shared memory block be destroyed.  In
order to ensure proper cleanup of resources, <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.unlink" title="multiprocessing.shared_memory.SharedMemory.unlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code></a> should be
called once (and only once) across all processes which have need
for the shared memory block.  After requesting its destruction, a
shared memory block may or may not be immediately destroyed and
this behavior may differ across platforms.  Attempts to access data
inside the shared memory block after <code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code> has been called may
result in memory access errors.  Note: the last process relinquishing
its hold on a shared memory block may call <code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code> and
<a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.close" title="multiprocessing.shared_memory.SharedMemory.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> in either order.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory.buf">
<span class="sig-name descname"><span class="pre">buf</span></span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory.buf" title="Link to this definition">¶</a></dt>
<dd><p>A memoryview of contents of the shared memory block.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory.name" title="Link to this definition">¶</a></dt>
<dd><p>Read-only access to the unique name of the shared memory block.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.SharedMemory.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#multiprocessing.shared_memory.SharedMemory.size" title="Link to this definition">¶</a></dt>
<dd><p>Read-only access to size in bytes of the shared memory block.</p>
</dd></dl>

</dd></dl>

<p>The following example demonstrates low-level use of <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>
instances:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_a</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">SharedMemory</span><span class="p">(</span><span class="n">create</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">shm_a</span><span class="o">.</span><span class="n">buf</span><span class="p">)</span>
<span class="go">&lt;class &#39;memoryview&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">buffer</span> <span class="o">=</span> <span class="n">shm_a</span><span class="o">.</span><span class="n">buf</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">buffer</span><span class="p">)</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">buffer</span><span class="p">[:</span><span class="mi">4</span><span class="p">]</span> <span class="o">=</span> <span class="nb">bytearray</span><span class="p">([</span><span class="mi">22</span><span class="p">,</span> <span class="mi">33</span><span class="p">,</span> <span class="mi">44</span><span class="p">,</span> <span class="mi">55</span><span class="p">])</span>  <span class="c1"># Modify multiple at once</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">buffer</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span> <span class="o">=</span> <span class="mi">100</span>                           <span class="c1"># Modify single byte at a time</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Attach to an existing shared memory block</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_b</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">SharedMemory</span><span class="p">(</span><span class="n">shm_a</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">array</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">array</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="n">shm_b</span><span class="o">.</span><span class="n">buf</span><span class="p">[:</span><span class="mi">5</span><span class="p">])</span>  <span class="c1"># Copy the data into a new array.array</span>
<span class="go">array(&#39;b&#39;, [22, 33, 44, 55, 100])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_b</span><span class="o">.</span><span class="n">buf</span><span class="p">[:</span><span class="mi">5</span><span class="p">]</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;howdy&#39;</span>  <span class="c1"># Modify via shm_b using bytes</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bytes</span><span class="p">(</span><span class="n">shm_a</span><span class="o">.</span><span class="n">buf</span><span class="p">[:</span><span class="mi">5</span><span class="p">])</span>      <span class="c1"># Access via shm_a</span>
<span class="go">b&#39;howdy&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_b</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>   <span class="c1"># Close each SharedMemory instance</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_a</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm_a</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>  <span class="c1"># Call unlink only once to release the shared memory</span>
</pre></div>
</div>
<p>The following example demonstrates a practical use of the <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>
class with <a class="reference external" href="https://numpy.org/">NumPy arrays</a>, accessing the
same <code class="xref py py-class docutils literal notranslate"><span class="pre">numpy.ndarray</span></code> from two distinct Python shells:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># In the first Python interactive shell</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">8</span><span class="p">])</span>  <span class="c1"># Start with an existing NumPy array</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">SharedMemory</span><span class="p">(</span><span class="n">create</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="n">a</span><span class="o">.</span><span class="n">nbytes</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Now create a NumPy array backed by shared memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">(</span><span class="n">a</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="n">a</span><span class="o">.</span><span class="n">dtype</span><span class="p">,</span> <span class="n">buffer</span><span class="o">=</span><span class="n">shm</span><span class="o">.</span><span class="n">buf</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="p">[:]</span> <span class="o">=</span> <span class="n">a</span><span class="p">[:]</span>  <span class="c1"># Copy the original data into shared memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span>
<span class="go">array([1, 1, 2, 3, 5, 8])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">b</span><span class="p">)</span>
<span class="go">&lt;class &#39;numpy.ndarray&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">&lt;class &#39;numpy.ndarray&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm</span><span class="o">.</span><span class="n">name</span>  <span class="c1"># We did not specify a name so one was chosen for us</span>
<span class="go">&#39;psm_21467_46075&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># In either the same shell or a new Python shell on the same machine</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Attach to the existing shared memory block</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">existing_shm</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">SharedMemory</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s1">&#39;psm_21467_46075&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Note that a.shape is (6,) and a.dtype is np.int64 in this example</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">((</span><span class="mi">6</span><span class="p">,),</span> <span class="n">dtype</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">int64</span><span class="p">,</span> <span class="n">buffer</span><span class="o">=</span><span class="n">existing_shm</span><span class="o">.</span><span class="n">buf</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span>
<span class="go">array([1, 1, 2, 3, 5, 8])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="mi">888</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span>
<span class="go">array([  1,   1,   2,   3,   5, 888])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Back in the first Python interactive shell, b reflects this change</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span>
<span class="go">array([  1,   1,   2,   3,   5, 888])</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Clean up from within the second Python shell</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">del</span> <span class="n">c</span>  <span class="c1"># Unnecessary; merely emphasizing the array is no longer used</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">existing_shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Clean up from within the first Python shell</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">del</span> <span class="n">b</span>  <span class="c1"># Unnecessary; merely emphasizing the array is no longer used</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>  <span class="c1"># Free and release the shared memory block at the very end</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="multiprocessing.managers.SharedMemoryManager">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">multiprocessing.managers.</span></span><span class="sig-name descname"><span class="pre">SharedMemoryManager</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">address</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">authkey</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.managers.SharedMemoryManager" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="multiprocessing.html#multiprocessing.managers.BaseManager" title="multiprocessing.managers.BaseManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">multiprocessing.managers.BaseManager</span></code></a> which can be
used for the management of shared memory blocks across processes.</p>
<p>A call to <a class="reference internal" href="multiprocessing.html#multiprocessing.managers.BaseManager.start" title="multiprocessing.managers.BaseManager.start"><code class="xref py py-meth docutils literal notranslate"><span class="pre">start()</span></code></a> on a
<code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code> instance causes a new process to be started.
This new process’s sole purpose is to manage the life cycle
of all shared memory blocks created through it.  To trigger the release
of all shared memory blocks managed by that process, call
<a class="reference internal" href="multiprocessing.html#multiprocessing.managers.BaseManager.shutdown" title="multiprocessing.managers.BaseManager.shutdown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">shutdown()</span></code></a> on the instance.
This triggers a <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory.unlink" title="multiprocessing.shared_memory.SharedMemory.unlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unlink()</span></code></a> call
on all of the <a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager.SharedMemory" title="multiprocessing.managers.SharedMemoryManager.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a> objects managed by that process and then
stops the process itself.  By creating <code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code> instances
through a <code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code>, we avoid the need to manually track
and trigger the freeing of shared memory resources.</p>
<p>This class provides methods for creating and returning <a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager.SharedMemory" title="multiprocessing.managers.SharedMemoryManager.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>
instances and for creating a list-like object (<a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager.ShareableList" title="multiprocessing.managers.SharedMemoryManager.ShareableList"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code></a>)
backed by shared memory.</p>
<p>Refer to <a class="reference internal" href="multiprocessing.html#multiprocessing.managers.BaseManager" title="multiprocessing.managers.BaseManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseManager</span></code></a> for a description
of the inherited <em>address</em> and <em>authkey</em> optional input arguments and how
they may be used to connect to an existing <code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code> service
from other processes.</p>
<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.managers.SharedMemoryManager.SharedMemory">
<span class="sig-name descname"><span class="pre">SharedMemory</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.managers.SharedMemoryManager.SharedMemory" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a new <a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager.SharedMemory" title="multiprocessing.managers.SharedMemoryManager.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a> object with the
specified <em>size</em> in bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.managers.SharedMemoryManager.ShareableList">
<span class="sig-name descname"><span class="pre">ShareableList</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.managers.SharedMemoryManager.ShareableList" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a new <a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager.ShareableList" title="multiprocessing.managers.SharedMemoryManager.ShareableList"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code></a> object, initialized
by the values from the input <em>sequence</em>.</p>
</dd></dl>

</dd></dl>

<p>The following example demonstrates the basic mechanisms of a
<a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager" title="multiprocessing.managers.SharedMemoryManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code></a>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing.managers</span> <span class="kn">import</span> <span class="n">SharedMemoryManager</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">smm</span> <span class="o">=</span> <span class="n">SharedMemoryManager</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">smm</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>  <span class="c1"># Start the process that manages the shared memory blocks</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span> <span class="o">=</span> <span class="n">smm</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">4</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span>
<span class="go">ShareableList([0, 1, 2, 3], name=&#39;psm_6572_7512&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">raw_shm</span> <span class="o">=</span> <span class="n">smm</span><span class="o">.</span><span class="n">SharedMemory</span><span class="p">(</span><span class="n">size</span><span class="o">=</span><span class="mi">128</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">another_sl</span> <span class="o">=</span> <span class="n">smm</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="s1">&#39;alpha&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">another_sl</span>
<span class="go">ShareableList([&#39;a&#39;, &#39;l&#39;, &#39;p&#39;, &#39;h&#39;, &#39;a&#39;], name=&#39;psm_6572_12221&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">smm</span><span class="o">.</span><span class="n">shutdown</span><span class="p">()</span>  <span class="c1"># Calls unlink() on sl, raw_shm, and another_sl</span>
</pre></div>
</div>
<p>The following example depicts a potentially more convenient pattern for using
<a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager" title="multiprocessing.managers.SharedMemoryManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code></a> objects via the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement to ensure that all shared memory blocks are released
after they are no longer needed:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">SharedMemoryManager</span><span class="p">()</span> <span class="k">as</span> <span class="n">smm</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">sl</span> <span class="o">=</span> <span class="n">smm</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">2000</span><span class="p">))</span>
<span class="gp">... </span>    <span class="c1"># Divide the work among two processes, storing partial results in sl</span>
<span class="gp">... </span>    <span class="n">p1</span> <span class="o">=</span> <span class="n">Process</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">do_work</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="n">sl</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1000</span><span class="p">))</span>
<span class="gp">... </span>    <span class="n">p2</span> <span class="o">=</span> <span class="n">Process</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">do_work</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="n">sl</span><span class="p">,</span> <span class="mi">1000</span><span class="p">,</span> <span class="mi">2000</span><span class="p">))</span>
<span class="gp">... </span>    <span class="n">p1</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">p2</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>  <span class="c1"># A multiprocessing.Pool might be more efficient</span>
<span class="gp">... </span>    <span class="n">p1</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">p2</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>   <span class="c1"># Wait for all work to complete in both processes</span>
<span class="gp">... </span>    <span class="n">total_result</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">sl</span><span class="p">)</span>  <span class="c1"># Consolidate the partial results now in sl</span>
</pre></div>
</div>
<p>When using a <a class="reference internal" href="#multiprocessing.managers.SharedMemoryManager" title="multiprocessing.managers.SharedMemoryManager"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemoryManager</span></code></a>
in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement, the shared memory blocks created using that
manager are all released when the <code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement’s code block
finishes execution.</p>
<dl class="py class">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.ShareableList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">multiprocessing.shared_memory.</span></span><span class="sig-name descname"><span class="pre">ShareableList</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.ShareableList" title="Link to this definition">¶</a></dt>
<dd><p>Provide a mutable list-like object where all values stored within are
stored in a shared memory block.
This constrains storable values to the following built-in data types:</p>
<ul class="simple">
<li><p><a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> (signed 64-bit)</p></li>
<li><p><a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a></p></li>
<li><p><a class="reference internal" href="functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a></p></li>
<li><p><a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> (less than 10M bytes each when encoded as UTF-8)</p></li>
<li><p><a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> (less than 10M bytes each)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
</ul>
<p>It also notably differs from the built-in <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> type
in that these lists can not change their overall length
(i.e. no <code class="xref py py-meth docutils literal notranslate"><span class="pre">append()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">insert()</span></code>, etc.) and do not
support the dynamic creation of new <code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code> instances
via slicing.</p>
<p><em>sequence</em> is used in populating a new <code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code> full of values.
Set to <code class="docutils literal notranslate"><span class="pre">None</span></code> to instead attach to an already existing
<code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code> by its unique shared memory name.</p>
<p><em>name</em> is the unique name for the requested shared memory, as described
in the definition for <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>.  When attaching to an
existing <code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code>, specify its shared memory block’s unique
name while leaving <em>sequence</em> set to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A known issue exists for <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> values.
If they end with <code class="docutils literal notranslate"><span class="pre">\x00</span></code> nul bytes or characters, those may be
<em>silently stripped</em> when fetching them by index from the
<code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code>. This <code class="docutils literal notranslate"><span class="pre">.rstrip(b'\x00')</span></code> behavior is
considered a bug and may go away in the future. See <a class="reference external" href="https://github.com/python/cpython/issues/106939">gh-106939</a>.</p>
</div>
<p>For applications where rstripping of trailing nulls is a problem,
work around it by always unconditionally appending an extra non-0
byte to the end of such values when storing and unconditionally
removing it when fetching:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nul_bug_demo</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">([</span><span class="s1">&#39;?</span><span class="se">\x00</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x03\x02\x01\x00\x00\x00</span><span class="s1">&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nul_bug_demo</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">&#39;?&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nul_bug_demo</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
<span class="go">b&#39;\x03\x02\x01&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nul_bug_demo</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">padded</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">([</span><span class="s1">&#39;?</span><span class="se">\x00\x07</span><span class="s1">&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x03\x02\x01\x00\x00\x00\x07</span><span class="s1">&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">padded</span><span class="p">[</span><span class="mi">0</span><span class="p">][:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
<span class="go">&#39;?\x00&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">padded</span><span class="p">[</span><span class="mi">1</span><span class="p">][:</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
<span class="go">b&#39;\x03\x02\x01\x00\x00\x00&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">padded</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</pre></div>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.ShareableList.count">
<span class="sig-name descname"><span class="pre">count</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.ShareableList.count" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of occurrences of <em>value</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.ShareableList.index">
<span class="sig-name descname"><span class="pre">index</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#multiprocessing.shared_memory.ShareableList.index" title="Link to this definition">¶</a></dt>
<dd><p>Return first index position of <em>value</em>.
Raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>value</em> is not present.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.ShareableList.format">
<span class="sig-name descname"><span class="pre">format</span></span><a class="headerlink" href="#multiprocessing.shared_memory.ShareableList.format" title="Link to this definition">¶</a></dt>
<dd><p>Read-only attribute containing the <a class="reference internal" href="struct.html#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> packing format used by
all currently stored values.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="multiprocessing.shared_memory.ShareableList.shm">
<span class="sig-name descname"><span class="pre">shm</span></span><a class="headerlink" href="#multiprocessing.shared_memory.ShareableList.shm" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a> instance where the values are stored.</p>
</dd></dl>

</dd></dl>

<p>The following example demonstrates basic use of a <a class="reference internal" href="#multiprocessing.shared_memory.ShareableList" title="multiprocessing.shared_memory.ShareableList"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code></a>
instance:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">([</span><span class="s1">&#39;howdy&#39;</span><span class="p">,</span> <span class="sa">b</span><span class="s1">&#39;HoWdY&#39;</span><span class="p">,</span> <span class="o">-</span><span class="mf">273.154</span><span class="p">,</span> <span class="mi">100</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="mi">42</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span> <span class="nb">type</span><span class="p">(</span><span class="n">entry</span><span class="p">)</span> <span class="k">for</span> <span class="n">entry</span> <span class="ow">in</span> <span class="n">a</span> <span class="p">]</span>
<span class="go">[&lt;class &#39;str&#39;&gt;, &lt;class &#39;bytes&#39;&gt;, &lt;class &#39;float&#39;&gt;, &lt;class &#39;int&#39;&gt;, &lt;class &#39;NoneType&#39;&gt;, &lt;class &#39;bool&#39;&gt;, &lt;class &#39;int&#39;&gt;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
<span class="go">-273.154</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="o">-</span><span class="mf">78.5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
<span class="go">-78.5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;dry ice&#39;</span>  <span class="c1"># Changing data types is supported as well</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
<span class="go">&#39;dry ice&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;larger than previously allocated storage space&#39;</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">exceeds available storage for existing str</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
<span class="go">&#39;dry ice&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">7</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">index</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
<span class="go">6</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">count</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;howdy&#39;</span><span class="p">)</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">count</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;HoWdY&#39;</span><span class="p">)</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">del</span> <span class="n">a</span>  <span class="c1"># Use of a ShareableList after call to unlink() is unsupported</span>
</pre></div>
</div>
<p>The following example depicts how one, two, or many processes may access the
same <a class="reference internal" href="#multiprocessing.shared_memory.ShareableList" title="multiprocessing.shared_memory.ShareableList"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code></a> by supplying the name of the shared memory block
behind it:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">))</span>         <span class="c1"># In a first process</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="n">b</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>  <span class="c1"># In a second process</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span>
<span class="go">ShareableList([0, 1, 2, 3, 4], name=&#39;...&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="o">-</span><span class="mi">999</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
<span class="go">-999</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</pre></div>
</div>
<p>The following examples demonstrates that <a class="reference internal" href="#multiprocessing.shared_memory.ShareableList" title="multiprocessing.shared_memory.ShareableList"><code class="xref py py-class docutils literal notranslate"><span class="pre">ShareableList</span></code></a>
(and underlying <a class="reference internal" href="#multiprocessing.shared_memory.SharedMemory" title="multiprocessing.shared_memory.SharedMemory"><code class="xref py py-class docutils literal notranslate"><span class="pre">SharedMemory</span></code></a>) objects
can be pickled and unpickled if needed.
Note, that it will still be the same shared object.
This happens, because the deserialized object has
the same unique name and is just attached to an existing
object with the same name (if the object is still alive):</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pickle</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">multiprocessing</span> <span class="kn">import</span> <span class="n">shared_memory</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span> <span class="o">=</span> <span class="n">shared_memory</span><span class="o">.</span><span class="n">ShareableList</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">sl</span><span class="p">)</span>
<span class="go">[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]</span>
</pre></div>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">deserialized_sl</span> <span class="o">=</span> <span class="n">pickle</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">pickle</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">sl</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">deserialized_sl</span><span class="p">)</span>
<span class="go">[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]</span>
</pre></div>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">deserialized_sl</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="o">-</span><span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">sl</span><span class="p">)</span>
<span class="go">[-1, -2, 2, 3, 4, 5, 6, 7, 8, 9]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">deserialized_sl</span><span class="p">)</span>
<span class="go">[-1, -2, 2, 3, 4, 5, 6, 7, 8, 9]</span>
</pre></div>
</div>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sl</span><span class="o">.</span><span class="n">shm</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="multiprocessing.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing</span></code> — Process-based parallelism</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="concurrent.html"
                          title="next chapter">The <code class="xref py py-mod docutils literal notranslate"><span class="pre">concurrent</span></code> package</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/multiprocessing.shared_memory.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="concurrent.html" title="The concurrent package"
             >next</a> |</li>
        <li class="right" >
          <a href="multiprocessing.html" title="multiprocessing — Process-based parallelism"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concurrency.html" >Concurrent Execution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing.shared_memory</span></code> — Shared memory for direct access across processes</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>