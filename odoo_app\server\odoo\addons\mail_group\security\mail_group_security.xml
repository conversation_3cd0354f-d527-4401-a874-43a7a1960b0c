<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="mail_group_rule_read_all" model="ir.rule">
        <field name="name">Mail Group: Access only public and joined groups</field>
        <field name="model_id" ref="model_mail_group"/>
        <field name="domain_force">[
            '|',
            '|',
            '|',
                ('moderator_ids', 'in', user.id),
                ('access_mode', '=', 'public'),
                '&amp;',
                    ('access_mode', '=', 'groups'),
                    ('access_group_id', 'in', [g.id for g in user.groups_id]),
                '&amp;',
                    ('access_mode', '=', 'members'),
                    ('member_partner_ids', 'in', [user.partner_id.id]),
            ]
        </field>
        <field name="groups" eval="[(4, ref('base.group_user')), (4, ref('base.group_portal')), (4, ref('base.group_public'))]"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>
    <record id="mail_group_rule_write_all" model="ir.rule">
        <field name="name">Mail Group: Moderator have write access on their group</field>
        <field name="model_id" ref="model_mail_group"/>
        <field name="domain_force">[('moderator_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_read" eval="False"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_rule_administrator" model="ir.rule">
        <field name="name">Mail Group: Administrator have access to all mail group</field>
        <field name="model_id" ref="model_mail_group"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('mail_group.group_mail_group_manager'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_message_rule_public" model="ir.rule">
        <field name="name">Mail Group Message: Only accepted message are accessible</field>
        <field name="model_id" ref="model_mail_group_message"/>
        <field name="domain_force">[
            '&amp;',
                ('moderation_status', '=', 'accepted'),
                '|',
                '|',
                '|',
                    ('mail_group_id.moderator_ids', 'in', user.id),
                    ('mail_group_id.access_mode', '=', 'public'),
                    '&amp;',
                        ('mail_group_id.access_mode', '=', 'groups'),
                        ('mail_group_id.access_group_id', 'in', [g.id for g in user.groups_id]),
                    '&amp;',
                        ('mail_group_id.access_mode', '=', 'members'),
                        ('mail_group_id.member_partner_ids', 'in', [user.partner_id.id]),
            ]
        </field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_message_rule_user" model="ir.rule">
        <field name="name">Mail Group Message: Non-accepted messages are accessible only by moderators</field>
        <field name="model_id" ref="model_mail_group_message"/>
        <field name="domain_force">[
                '&amp;',
                    '|',
                        ('moderation_status', '=', 'accepted'),
                        ('mail_group_id.moderator_ids', 'in', user.id),
                    '|',
                    '|',
                    '|',
                        ('mail_group_id.moderator_ids', 'in', user.id),
                        ('mail_group_id.access_mode', '=', 'public'),
                        '&amp;',
                            ('mail_group_id.access_mode', '=', 'groups'),
                            ('mail_group_id.access_group_id', 'in', [g.id for g in user.groups_id]),
                        '&amp;',
                            ('mail_group_id.access_mode', '=', 'members'),
                            ('mail_group_id.member_partner_ids', 'in', [user.partner_id.id]),
            ]
        </field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_message_rule_administrator" model="ir.rule">
        <field name="name">Mail Group Message: Administrator have access to all messages</field>
        <field name="model_id" ref="model_mail_group_message"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('mail_group.group_mail_group_manager'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_member_rule_user" model="ir.rule">
        <field name="name">Mail Group Member: Members are accessible only by moderators</field>
        <field name="model_id" ref="model_mail_group_member"/>
        <field name="domain_force">[('mail_group_id.moderator_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_member_rule_administrator" model="ir.rule">
        <field name="name">Mail Group Member: Administrator have access to all members</field>
        <field name="model_id" ref="model_mail_group_member"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('mail_group.group_mail_group_manager'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_moderation_rule_user" model="ir.rule">
        <field name="name">Mail Group Moderation: Moderation rules are accessible only by moderators</field>
        <field name="model_id" ref="model_mail_group_moderation"/>
        <field name="domain_force">[('mail_group_id.moderator_ids', 'in', user.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="mail_group_moderation_rule_administrator" model="ir.rule">
        <field name="name">Mail Group Moderation: Administrator have access to all moderation rules</field>
        <field name="model_id" ref="model_mail_group_moderation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('mail_group.group_mail_group_manager'))]"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
