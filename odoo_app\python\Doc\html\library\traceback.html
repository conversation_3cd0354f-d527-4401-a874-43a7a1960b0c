<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="traceback — Print or retrieve a stack traceback" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/traceback.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/traceback.py This module provides a standard interface to extract, format and print stack traces of Python programs. It exactly mimics the behavior of the Python interpreter when i..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/traceback.py This module provides a standard interface to extract, format and print stack traces of Python programs. It exactly mimics the behavior of the Python interpreter when i..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>traceback — Print or retrieve a stack traceback &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="__future__ — Future statement definitions" href="__future__.html" />
    <link rel="prev" title="atexit — Exit handlers" href="atexit.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/traceback.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a><ul>
<li><a class="reference internal" href="#tracebackexception-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> Objects</a></li>
<li><a class="reference internal" href="#stacksummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> Objects</a></li>
<li><a class="reference internal" href="#framesummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> Objects</a></li>
<li><a class="reference internal" href="#traceback-examples">Traceback Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="atexit.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code> — Exit handlers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="__future__.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/traceback.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="__future__.html" title="__future__ — Future statement definitions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="atexit.html" title="atexit — Exit handlers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-traceback">
<span id="traceback-print-or-retrieve-a-stack-traceback"></span><h1><a class="reference internal" href="#module-traceback" title="traceback: Print or retrieve a stack traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code></a> — Print or retrieve a stack traceback<a class="headerlink" href="#module-traceback" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/traceback.py">Lib/traceback.py</a></p>
<hr class="docutils" />
<p>This module provides a standard interface to extract, format and print stack
traces of Python programs.  It exactly mimics the behavior of the Python
interpreter when it prints a stack trace.  This is useful when you want to print
stack traces under program control, such as in a “wrapper” around the
interpreter.</p>
<p id="index-0">The module uses <a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback objects</span></a> — these are
objects of type <a class="reference internal" href="types.html#types.TracebackType" title="types.TracebackType"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.TracebackType</span></code></a>,
which are assigned to the <a class="reference internal" href="exceptions.html#BaseException.__traceback__" title="BaseException.__traceback__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__traceback__</span></code></a> field of
<a class="reference internal" href="exceptions.html#BaseException" title="BaseException"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseException</span></code></a> instances.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="faulthandler.html#module-faulthandler" title="faulthandler: Dump the Python traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code></a></dt><dd><p>Used to dump Python tracebacks explicitly, on a fault, after a timeout, or on a user signal.</p>
</dd>
<dt>Module <a class="reference internal" href="pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a></dt><dd><p>Interactive source code debugger for Python programs.</p>
</dd>
</dl>
</div>
<p>The module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="traceback.print_tb">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">print_tb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.print_tb" title="Link to this definition">¶</a></dt>
<dd><p>Print up to <em>limit</em> stack trace entries from
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback object</span></a> <em>tb</em> (starting
from the caller’s frame) if <em>limit</em> is positive.  Otherwise, print the last
<code class="docutils literal notranslate"><span class="pre">abs(limit)</span></code> entries.  If <em>limit</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, all entries are
printed.  If <em>file</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, the output goes to
<a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>; otherwise it should be an open
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file</span></a> or <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a> to
receive the output.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added negative <em>limit</em> support.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.print_exception">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">print_exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">/</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">limit=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.print_exception" title="Link to this definition">¶</a></dt>
<dd><p>Print exception information and stack trace entries from
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback object</span></a>
<em>tb</em> to <em>file</em>. This differs from <a class="reference internal" href="#traceback.print_tb" title="traceback.print_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_tb()</span></code></a> in the following
ways:</p>
<ul class="simple">
<li><p>if <em>tb</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, it prints a header <code class="docutils literal notranslate"><span class="pre">Traceback</span> <span class="pre">(most</span> <span class="pre">recent</span>
<span class="pre">call</span> <span class="pre">last):</span></code></p></li>
<li><p>it prints the exception type and <em>value</em> after the stack trace</p></li>
</ul>
<ul class="simple" id="index-1">
<li><p>if <em>type(value)</em> is <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> and <em>value</em> has the appropriate
format, it prints the line where the syntax error occurred with a caret
indicating the approximate position of the error.</p></li>
</ul>
<p>Since Python 3.10, instead of passing <em>value</em> and <em>tb</em>, an exception object
can be passed as the first argument. If <em>value</em> and <em>tb</em> are provided, the
first argument is ignored in order to provide backwards compatibility.</p>
<p>The optional <em>limit</em> argument has the same meaning as for <a class="reference internal" href="#traceback.print_tb" title="traceback.print_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_tb()</span></code></a>.
If <em>chain</em> is true (the default), then chained exceptions (the
<a class="reference internal" href="exceptions.html#BaseException.__cause__" title="BaseException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a> or <a class="reference internal" href="exceptions.html#BaseException.__context__" title="BaseException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a>
attributes of the exception) will be
printed as well, like the interpreter itself does when printing an unhandled
exception.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>etype</em> argument is ignored and inferred from the type of <em>value</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>etype</em> parameter has been renamed to <em>exc</em> and is now
positional-only.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.print_exc">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">print_exc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.print_exc" title="Link to this definition">¶</a></dt>
<dd><p>This is a shorthand for <code class="docutils literal notranslate"><span class="pre">print_exception(sys.exception(),</span> <span class="pre">limit,</span> <span class="pre">file,</span>
<span class="pre">chain)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.print_last">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">print_last</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.print_last" title="Link to this definition">¶</a></dt>
<dd><p>This is a shorthand for <code class="docutils literal notranslate"><span class="pre">print_exception(sys.last_exc,</span> <span class="pre">limit,</span> <span class="pre">file,</span>
<span class="pre">chain)</span></code>.  In general it will work only after an exception has reached
an interactive prompt (see <a class="reference internal" href="sys.html#sys.last_exc" title="sys.last_exc"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.last_exc</span></code></a>).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.print_stack">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">print_stack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.print_stack" title="Link to this definition">¶</a></dt>
<dd><p>Print up to <em>limit</em> stack trace entries (starting from the invocation
point) if <em>limit</em> is positive.  Otherwise, print the last <code class="docutils literal notranslate"><span class="pre">abs(limit)</span></code>
entries.  If <em>limit</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, all entries are printed.
The optional <em>f</em> argument can be used to specify an alternate
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">stack frame</span></a>
to start.  The optional <em>file</em> argument has the same meaning as for
<a class="reference internal" href="#traceback.print_tb" title="traceback.print_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_tb()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added negative <em>limit</em> support.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.extract_tb">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">extract_tb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.extract_tb" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#traceback.StackSummary" title="traceback.StackSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code></a> object representing a list of “pre-processed”
stack trace entries extracted from the
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback object</span></a> <em>tb</em>.  It is useful
for alternate formatting of stack traces.  The optional <em>limit</em> argument has
the same meaning as for <a class="reference internal" href="#traceback.print_tb" title="traceback.print_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_tb()</span></code></a>.  A “pre-processed” stack trace
entry is a <a class="reference internal" href="#traceback.FrameSummary" title="traceback.FrameSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code></a> object containing attributes
<a class="reference internal" href="#traceback.FrameSummary.filename" title="traceback.FrameSummary.filename"><code class="xref py py-attr docutils literal notranslate"><span class="pre">filename</span></code></a>, <a class="reference internal" href="#traceback.FrameSummary.lineno" title="traceback.FrameSummary.lineno"><code class="xref py py-attr docutils literal notranslate"><span class="pre">lineno</span></code></a>,
<a class="reference internal" href="#traceback.FrameSummary.name" title="traceback.FrameSummary.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a>, and <a class="reference internal" href="#traceback.FrameSummary.line" title="traceback.FrameSummary.line"><code class="xref py py-attr docutils literal notranslate"><span class="pre">line</span></code></a> representing the
information that is usually printed for a stack trace.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.extract_stack">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">extract_stack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.extract_stack" title="Link to this definition">¶</a></dt>
<dd><p>Extract the raw traceback from the current
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">stack frame</span></a>.  The return value has
the same format as for <a class="reference internal" href="#traceback.extract_tb" title="traceback.extract_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">extract_tb()</span></code></a>.  The optional <em>f</em> and <em>limit</em>
arguments have the same meaning as for <a class="reference internal" href="#traceback.print_stack" title="traceback.print_stack"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_stack()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_list">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">extracted_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_list" title="Link to this definition">¶</a></dt>
<dd><p>Given a list of tuples or <a class="reference internal" href="#traceback.FrameSummary" title="traceback.FrameSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code></a> objects as returned by
<a class="reference internal" href="#traceback.extract_tb" title="traceback.extract_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">extract_tb()</span></code></a> or <a class="reference internal" href="#traceback.extract_stack" title="traceback.extract_stack"><code class="xref py py-func docutils literal notranslate"><span class="pre">extract_stack()</span></code></a>, return a list of strings ready
for printing.  Each string in the resulting list corresponds to the item with
the same index in the argument list.  Each string ends in a newline; the
strings may contain internal newlines as well, for those items whose source
text line is not <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_exception_only">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_exception_only</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">/</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_exception_only" title="Link to this definition">¶</a></dt>
<dd><p>Format the exception part of a traceback using an exception value such as
given by <a class="reference internal" href="sys.html#sys.last_value" title="sys.last_value"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.last_value</span></code></a>.  The return value is a list of strings, each
ending in a newline.  The list contains the exception’s message, which is
normally a single string; however, for <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> exceptions, it
contains several lines that (when printed) display detailed information
about where the syntax error occurred. Following the message, the list
contains the exception’s <a class="reference internal" href="exceptions.html#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">notes</span></code></a>.</p>
<p>Since Python 3.10, instead of passing <em>value</em>, an exception object
can be passed as the first argument.  If <em>value</em> is provided, the first
argument is ignored in order to provide backwards compatibility.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>etype</em> parameter has been renamed to <em>exc</em> and is now
positional-only.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The returned list now includes any
<a class="reference internal" href="exceptions.html#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">notes</span></code></a> attached to the exception.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_exception">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">/</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">limit=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain=True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_exception" title="Link to this definition">¶</a></dt>
<dd><p>Format a stack trace and the exception information.  The arguments  have the
same meaning as the corresponding arguments to <a class="reference internal" href="#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_exception()</span></code></a>.  The
return value is a list of strings, each ending in a newline and some
containing internal newlines.  When these lines are concatenated and printed,
exactly the same text is printed as does <a class="reference internal" href="#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_exception()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>etype</em> argument is ignored and inferred from the type of <em>value</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>This function’s behavior and signature were modified to match
<a class="reference internal" href="#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_exception()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_exc">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_exc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_exc" title="Link to this definition">¶</a></dt>
<dd><p>This is like <code class="docutils literal notranslate"><span class="pre">print_exc(limit)</span></code> but returns a string instead of printing to
a file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_tb">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_tb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_tb" title="Link to this definition">¶</a></dt>
<dd><p>A shorthand for <code class="docutils literal notranslate"><span class="pre">format_list(extract_tb(tb,</span> <span class="pre">limit))</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.format_stack">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">format_stack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.format_stack" title="Link to this definition">¶</a></dt>
<dd><p>A shorthand for <code class="docutils literal notranslate"><span class="pre">format_list(extract_stack(f,</span> <span class="pre">limit))</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.clear_frames">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">clear_frames</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.clear_frames" title="Link to this definition">¶</a></dt>
<dd><p>Clears the local variables of all the stack frames in a
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback</span></a> <em>tb</em>
by calling the <a class="reference internal" href="../reference/datamodel.html#frame.clear" title="frame.clear"><code class="xref py py-meth docutils literal notranslate"><span class="pre">clear()</span></code></a> method of each
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame object</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.walk_stack">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">walk_stack</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.walk_stack" title="Link to this definition">¶</a></dt>
<dd><p>Walk a stack following <a class="reference internal" href="../reference/datamodel.html#frame.f_back" title="frame.f_back"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f.f_back</span></code></a> from the given frame,
yielding the frame
and line number for each frame. If <em>f</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the current stack is
used. This helper is used with <a class="reference internal" href="#traceback.StackSummary.extract" title="traceback.StackSummary.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">StackSummary.extract()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="traceback.walk_tb">
<span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">walk_tb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.walk_tb" title="Link to this definition">¶</a></dt>
<dd><p>Walk a traceback following <a class="reference internal" href="../reference/datamodel.html#traceback.tb_next" title="traceback.tb_next"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tb_next</span></code></a> yielding the frame and
line number
for each frame. This helper is used with <a class="reference internal" href="#traceback.StackSummary.extract" title="traceback.StackSummary.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">StackSummary.extract()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<p>The module also defines the following classes:</p>
<section id="tracebackexception-objects">
<h2><code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> Objects<a class="headerlink" href="#tracebackexception-objects" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> objects are created from actual exceptions to
capture data for later printing in a lightweight fashion.</p>
<dl class="py class">
<dt class="sig sig-object py" id="traceback.TracebackException">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">TracebackException</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc_type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc_value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exc_traceback</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lookup_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capture_locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compact</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_group_width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">15</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_group_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.TracebackException" title="Link to this definition">¶</a></dt>
<dd><p>Capture an exception for later rendering. <em>limit</em>, <em>lookup_lines</em> and
<em>capture_locals</em> are as for the <a class="reference internal" href="#traceback.StackSummary" title="traceback.StackSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code></a> class.</p>
<p>If <em>compact</em> is true, only data that is required by
<code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code>’s <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a> method
is saved in the class attributes. In particular, the
<a class="reference internal" href="#traceback.TracebackException.__context__" title="traceback.TracebackException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a> field is calculated only if <a class="reference internal" href="#traceback.TracebackException.__cause__" title="traceback.TracebackException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a> is
<code class="docutils literal notranslate"><span class="pre">None</span></code> and <a class="reference internal" href="#traceback.TracebackException.__suppress_context__" title="traceback.TracebackException.__suppress_context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__suppress_context__</span></code></a> is false.</p>
<p>Note that when locals are captured, they are also shown in the traceback.</p>
<p><em>max_group_width</em> and <em>max_group_depth</em> control the formatting of exception
groups (see <a class="reference internal" href="exceptions.html#BaseExceptionGroup" title="BaseExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseExceptionGroup</span></code></a>). The depth refers to the nesting
level of the group, and the width refers to the size of a single exception
group’s exceptions array. The formatted output is truncated when either
limit is exceeded.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added the <em>compact</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <em>max_group_width</em> and <em>max_group_depth</em> parameters.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.__cause__">
<span class="sig-name descname"><span class="pre">__cause__</span></span><a class="headerlink" href="#traceback.TracebackException.__cause__" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> of the original
<a class="reference internal" href="exceptions.html#BaseException.__cause__" title="BaseException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.__context__">
<span class="sig-name descname"><span class="pre">__context__</span></span><a class="headerlink" href="#traceback.TracebackException.__context__" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> of the original
<a class="reference internal" href="exceptions.html#BaseException.__context__" title="BaseException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.exceptions">
<span class="sig-name descname"><span class="pre">exceptions</span></span><a class="headerlink" href="#traceback.TracebackException.exceptions" title="Link to this definition">¶</a></dt>
<dd><p>If <code class="docutils literal notranslate"><span class="pre">self</span></code> represents an <a class="reference internal" href="exceptions.html#ExceptionGroup" title="ExceptionGroup"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExceptionGroup</span></code></a>, this field holds a list of
<code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> instances representing the nested exceptions.
Otherwise it is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.__suppress_context__">
<span class="sig-name descname"><span class="pre">__suppress_context__</span></span><a class="headerlink" href="#traceback.TracebackException.__suppress_context__" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="exceptions.html#BaseException.__suppress_context__" title="BaseException.__suppress_context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__suppress_context__</span></code></a> value from the original
exception.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.__notes__">
<span class="sig-name descname"><span class="pre">__notes__</span></span><a class="headerlink" href="#traceback.TracebackException.__notes__" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="exceptions.html#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__notes__</span></code></a> value from the original exception,
or <code class="docutils literal notranslate"><span class="pre">None</span></code>
if the exception does not have any notes. If it is not <code class="docutils literal notranslate"><span class="pre">None</span></code>
is it formatted in the traceback after the exception string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.stack">
<span class="sig-name descname"><span class="pre">stack</span></span><a class="headerlink" href="#traceback.TracebackException.stack" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="#traceback.StackSummary" title="traceback.StackSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code></a> representing the traceback.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.exc_type">
<span class="sig-name descname"><span class="pre">exc_type</span></span><a class="headerlink" href="#traceback.TracebackException.exc_type" title="Link to this definition">¶</a></dt>
<dd><p>The class of the original traceback.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#traceback.TracebackException.filename" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the file name where the error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#traceback.TracebackException.lineno" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the line number where the error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.end_lineno">
<span class="sig-name descname"><span class="pre">end_lineno</span></span><a class="headerlink" href="#traceback.TracebackException.end_lineno" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the end line number where the error occurred.
Can be <code class="docutils literal notranslate"><span class="pre">None</span></code> if not present.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.text">
<span class="sig-name descname"><span class="pre">text</span></span><a class="headerlink" href="#traceback.TracebackException.text" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the text where the error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.offset">
<span class="sig-name descname"><span class="pre">offset</span></span><a class="headerlink" href="#traceback.TracebackException.offset" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the offset into the text where the error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.end_offset">
<span class="sig-name descname"><span class="pre">end_offset</span></span><a class="headerlink" href="#traceback.TracebackException.end_offset" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the end offset into the text where the error occurred.
Can be <code class="docutils literal notranslate"><span class="pre">None</span></code> if not present.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.TracebackException.msg">
<span class="sig-name descname"><span class="pre">msg</span></span><a class="headerlink" href="#traceback.TracebackException.msg" title="Link to this definition">¶</a></dt>
<dd><p>For syntax errors - the compiler error message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.TracebackException.from_exception">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lookup_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capture_locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.TracebackException.from_exception" title="Link to this definition">¶</a></dt>
<dd><p>Capture an exception for later rendering. <em>limit</em>, <em>lookup_lines</em> and
<em>capture_locals</em> are as for the <a class="reference internal" href="#traceback.StackSummary" title="traceback.StackSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code></a> class.</p>
<p>Note that when locals are captured, they are also shown in the traceback.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.TracebackException.print">
<span class="sig-name descname"><span class="pre">print</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.TracebackException.print" title="Link to this definition">¶</a></dt>
<dd><p>Print to <em>file</em> (default <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>) the exception information returned by
<a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">format()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.TracebackException.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">chain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.TracebackException.format" title="Link to this definition">¶</a></dt>
<dd><p>Format the exception.</p>
<p>If <em>chain</em> is not <code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="#traceback.TracebackException.__cause__" title="traceback.TracebackException.__cause__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__cause__</span></code></a> and <a class="reference internal" href="#traceback.TracebackException.__context__" title="traceback.TracebackException.__context__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__context__</span></code></a>
will not be formatted.</p>
<p>The return value is a generator of strings, each ending in a newline and
some containing internal newlines. <a class="reference internal" href="#traceback.print_exception" title="traceback.print_exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">print_exception()</span></code></a>
is a wrapper around this method which just prints the lines to a file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.TracebackException.format_exception_only">
<span class="sig-name descname"><span class="pre">format_exception_only</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#traceback.TracebackException.format_exception_only" title="Link to this definition">¶</a></dt>
<dd><p>Format the exception part of the traceback.</p>
<p>The return value is a generator of strings, each ending in a newline.</p>
<p>The generator emits the exception’s message followed by its notes
(if it has any). The exception message is normally a single string;
however, for <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> exceptions, it consists of several
lines that (when printed) display detailed information about where
the syntax error occurred.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The exception’s <a class="reference internal" href="exceptions.html#BaseException.__notes__" title="BaseException.__notes__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">notes</span></code></a> are now
included in the output.</p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="stacksummary-objects">
<h2><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> Objects<a class="headerlink" href="#stacksummary-objects" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> objects represent a call stack ready for formatting.</p>
<dl class="py class">
<dt class="sig sig-object py" id="traceback.StackSummary">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">StackSummary</span></span><a class="headerlink" href="#traceback.StackSummary" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="traceback.StackSummary.extract">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_gen</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lookup_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capture_locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.StackSummary.extract" title="Link to this definition">¶</a></dt>
<dd><p>Construct a <code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> object from a frame generator (such as
is returned by <a class="reference internal" href="#traceback.walk_stack" title="traceback.walk_stack"><code class="xref py py-func docutils literal notranslate"><span class="pre">walk_stack()</span></code></a> or
<a class="reference internal" href="#traceback.walk_tb" title="traceback.walk_tb"><code class="xref py py-func docutils literal notranslate"><span class="pre">walk_tb()</span></code></a>).</p>
<p>If <em>limit</em> is supplied, only this many frames are taken from <em>frame_gen</em>.
If <em>lookup_lines</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the returned <a class="reference internal" href="#traceback.FrameSummary" title="traceback.FrameSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code></a>
objects will not have read their lines in yet, making the cost of
creating the <code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> cheaper (which may be valuable if it
may not actually get formatted). If <em>capture_locals</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> the
local variables in each <code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> are captured as object
representations.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Exceptions raised from <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> on a local variable (when
<em>capture_locals</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>) are no longer propagated to the caller.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.StackSummary.from_list">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.StackSummary.from_list" title="Link to this definition">¶</a></dt>
<dd><p>Construct a <code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> object from a supplied list of
<a class="reference internal" href="#traceback.FrameSummary" title="traceback.FrameSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code></a> objects or old-style list of tuples.  Each tuple
should be a 4-tuple with <em>filename</em>, <em>lineno</em>, <em>name</em>, <em>line</em> as the
elements.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.StackSummary.format">
<span class="sig-name descname"><span class="pre">format</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#traceback.StackSummary.format" title="Link to this definition">¶</a></dt>
<dd><p>Returns a list of strings ready for printing.  Each string in the
resulting list corresponds to a single <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame</span></a> from
the stack.
Each string ends in a newline; the strings may contain internal
newlines as well, for those items with source text lines.</p>
<p>For long sequences of the same frame and line, the first few
repetitions are shown, followed by a summary line stating the exact
number of further repetitions.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Long sequences of repeated frames are now abbreviated.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="traceback.StackSummary.format_frame_summary">
<span class="sig-name descname"><span class="pre">format_frame_summary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">frame_summary</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.StackSummary.format_frame_summary" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string for printing one of the <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frames</span></a>
involved in the stack.
This method is called for each <a class="reference internal" href="#traceback.FrameSummary" title="traceback.FrameSummary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code></a> object to be
printed by <a class="reference internal" href="#traceback.StackSummary.format" title="traceback.StackSummary.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">StackSummary.format()</span></code></a>. If it returns <code class="docutils literal notranslate"><span class="pre">None</span></code>, the
frame is omitted from the output.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="framesummary-objects">
<h2><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> Objects<a class="headerlink" href="#framesummary-objects" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>A <code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> object represents a single <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame</span></a>
in a <a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback</span></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="traceback.FrameSummary">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">traceback.</span></span><span class="sig-name descname"><span class="pre">FrameSummary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lookup_line</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">line</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#traceback.FrameSummary" title="Link to this definition">¶</a></dt>
<dd><p>Represents a single <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame</span></a> in the
<a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback</span></a> or stack that is being formatted
or printed. It may optionally have a stringified version of the frame’s
locals included in it. If <em>lookup_line</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the source code is not
looked up until the <code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> has the <a class="reference internal" href="#traceback.FrameSummary.line" title="traceback.FrameSummary.line"><code class="xref py py-attr docutils literal notranslate"><span class="pre">line</span></code></a>
attribute accessed (which also happens when casting it to a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a>).
<a class="reference internal" href="#traceback.FrameSummary.line" title="traceback.FrameSummary.line"><code class="xref py py-attr docutils literal notranslate"><span class="pre">line</span></code></a> may be directly provided, and will prevent line
lookups happening at all. <em>locals</em> is an optional local variable
dictionary, and if supplied the variable representations are stored in the
summary for later display.</p>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> instances have the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.FrameSummary.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#traceback.FrameSummary.filename" title="Link to this definition">¶</a></dt>
<dd><p>The filename of the source code for this frame. Equivalent to accessing
<a class="reference internal" href="../reference/datamodel.html#codeobject.co_filename" title="codeobject.co_filename"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f.f_code.co_filename</span></code></a> on a
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame object</span></a> <em>f</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.FrameSummary.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#traceback.FrameSummary.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line number of the source code for this frame.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.FrameSummary.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#traceback.FrameSummary.name" title="Link to this definition">¶</a></dt>
<dd><p>Equivalent to accessing <a class="reference internal" href="../reference/datamodel.html#codeobject.co_name" title="codeobject.co_name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f.f_code.co_name</span></code></a> on
a <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame object</span></a> <em>f</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="traceback.FrameSummary.line">
<span class="sig-name descname"><span class="pre">line</span></span><a class="headerlink" href="#traceback.FrameSummary.line" title="Link to this definition">¶</a></dt>
<dd><p>A string representing the source code for this frame, with leading and
trailing whitespace stripped.
If the source is not available, it is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

</dd></dl>

</section>
<section id="traceback-examples">
<span id="traceback-example"></span><h2>Traceback Examples<a class="headerlink" href="#traceback-examples" title="Link to this heading">¶</a></h2>
<p>This simple example implements a basic read-eval-print loop, similar to (but
less useful than) the standard Python interactive interpreter loop.  For a more
complete implementation of the interpreter loop, refer to the <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-mod docutils literal notranslate"><span class="pre">code</span></code></a>
module.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span><span class="o">,</span> <span class="nn">traceback</span>

<span class="k">def</span> <span class="nf">run_user_code</span><span class="p">(</span><span class="n">envdir</span><span class="p">):</span>
    <span class="n">source</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;&gt;&gt;&gt; &quot;</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">exec</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">envdir</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">Exception</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Exception in user code:&quot;</span><span class="p">)</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;-&quot;</span><span class="o">*</span><span class="mi">60</span><span class="p">)</span>
        <span class="n">traceback</span><span class="o">.</span><span class="n">print_exc</span><span class="p">(</span><span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;-&quot;</span><span class="o">*</span><span class="mi">60</span><span class="p">)</span>

<span class="n">envdir</span> <span class="o">=</span> <span class="p">{}</span>
<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">run_user_code</span><span class="p">(</span><span class="n">envdir</span><span class="p">)</span>
</pre></div>
</div>
<p>The following example demonstrates the different ways to print and format the
exception and traceback:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">sys</span><span class="o">,</span> <span class="nn">traceback</span>

<span class="k">def</span> <span class="nf">lumberjack</span><span class="p">():</span>
    <span class="n">bright_side_of_life</span><span class="p">()</span>

<span class="k">def</span> <span class="nf">bright_side_of_life</span><span class="p">():</span>
    <span class="k">return</span> <span class="nb">tuple</span><span class="p">()[</span><span class="mi">0</span><span class="p">]</span>

<span class="k">try</span><span class="p">:</span>
    <span class="n">lumberjack</span><span class="p">()</span>
<span class="k">except</span> <span class="ne">IndexError</span><span class="p">:</span>
    <span class="n">exc</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">exception</span><span class="p">()</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** print_tb:&quot;</span><span class="p">)</span>
    <span class="n">traceback</span><span class="o">.</span><span class="n">print_tb</span><span class="p">(</span><span class="n">exc</span><span class="o">.</span><span class="n">__traceback__</span><span class="p">,</span> <span class="n">limit</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** print_exception:&quot;</span><span class="p">)</span>
    <span class="n">traceback</span><span class="o">.</span><span class="n">print_exception</span><span class="p">(</span><span class="n">exc</span><span class="p">,</span> <span class="n">limit</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** print_exc:&quot;</span><span class="p">)</span>
    <span class="n">traceback</span><span class="o">.</span><span class="n">print_exc</span><span class="p">(</span><span class="n">limit</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** format_exc, first and last line:&quot;</span><span class="p">)</span>
    <span class="n">formatted_lines</span> <span class="o">=</span> <span class="n">traceback</span><span class="o">.</span><span class="n">format_exc</span><span class="p">()</span><span class="o">.</span><span class="n">splitlines</span><span class="p">()</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">formatted_lines</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">formatted_lines</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">])</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** format_exception:&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">traceback</span><span class="o">.</span><span class="n">format_exception</span><span class="p">(</span><span class="n">exc</span><span class="p">)))</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** extract_tb:&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">traceback</span><span class="o">.</span><span class="n">extract_tb</span><span class="p">(</span><span class="n">exc</span><span class="o">.</span><span class="n">__traceback__</span><span class="p">)))</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** format_tb:&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">traceback</span><span class="o">.</span><span class="n">format_tb</span><span class="p">(</span><span class="n">exc</span><span class="o">.</span><span class="n">__traceback__</span><span class="p">)))</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;*** tb_lineno:&quot;</span><span class="p">,</span> <span class="n">exc</span><span class="o">.</span><span class="n">__traceback__</span><span class="o">.</span><span class="n">tb_lineno</span><span class="p">)</span>
</pre></div>
</div>
<p>The output for the example would look similar to this:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>*** print_tb:
  File &quot;&lt;doctest...&gt;&quot;, line 10, in &lt;module&gt;
    lumberjack()
*** print_exception:
Traceback (most recent call last):
  File &quot;&lt;doctest...&gt;&quot;, line 10, in &lt;module&gt;
    lumberjack()
  File &quot;&lt;doctest...&gt;&quot;, line 4, in lumberjack
    bright_side_of_life()
IndexError: tuple index out of range
*** print_exc:
Traceback (most recent call last):
  File &quot;&lt;doctest...&gt;&quot;, line 10, in &lt;module&gt;
    lumberjack()
  File &quot;&lt;doctest...&gt;&quot;, line 4, in lumberjack
    bright_side_of_life()
IndexError: tuple index out of range
*** format_exc, first and last line:
Traceback (most recent call last):
IndexError: tuple index out of range
*** format_exception:
[&#39;Traceback (most recent call last):\n&#39;,
 &#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 10, in &lt;module&gt;\n    lumberjack()\n&#39;,
 &#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 4, in lumberjack\n    bright_side_of_life()\n&#39;,
 &#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 7, in bright_side_of_life\n    return tuple()[0]\n           ~~~~~~~^^^\n&#39;,
 &#39;IndexError: tuple index out of range\n&#39;]
*** extract_tb:
[&lt;FrameSummary file &lt;doctest...&gt;, line 10 in &lt;module&gt;&gt;,
 &lt;FrameSummary file &lt;doctest...&gt;, line 4 in lumberjack&gt;,
 &lt;FrameSummary file &lt;doctest...&gt;, line 7 in bright_side_of_life&gt;]
*** format_tb:
[&#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 10, in &lt;module&gt;\n    lumberjack()\n&#39;,
 &#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 4, in lumberjack\n    bright_side_of_life()\n&#39;,
 &#39;  File &quot;&lt;doctest default[0]&gt;&quot;, line 7, in bright_side_of_life\n    return tuple()[0]\n           ~~~~~~~^^^\n&#39;]
*** tb_lineno: 10
</pre></div>
</div>
<p>The following example shows the different ways to print and format the stack:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">traceback</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">another_function</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">lumberstack</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">lumberstack</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">traceback</span><span class="o">.</span><span class="n">print_stack</span><span class="p">()</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">traceback</span><span class="o">.</span><span class="n">extract_stack</span><span class="p">()))</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">traceback</span><span class="o">.</span><span class="n">format_stack</span><span class="p">()))</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">another_function</span><span class="p">()</span>
  File <span class="nb">&quot;&lt;doctest&gt;&quot;</span>, line <span class="m">10</span>, in <span class="n">&lt;module&gt;</span>
<span class="w">    </span><span class="n">another_function</span><span class="p">()</span>
  File <span class="nb">&quot;&lt;doctest&gt;&quot;</span>, line <span class="m">3</span>, in <span class="n">another_function</span>
<span class="w">    </span><span class="n">lumberstack</span><span class="p">()</span>
  File <span class="nb">&quot;&lt;doctest&gt;&quot;</span>, line <span class="m">6</span>, in <span class="n">lumberstack</span>
<span class="w">    </span><span class="n">traceback</span><span class="o">.</span><span class="n">print_stack</span><span class="p">()</span>
<span class="x">[(&#39;&lt;doctest&gt;&#39;, 10, &#39;&lt;module&gt;&#39;, &#39;another_function()&#39;),</span>
<span class="x"> (&#39;&lt;doctest&gt;&#39;, 3, &#39;another_function&#39;, &#39;lumberstack()&#39;),</span>
<span class="x"> (&#39;&lt;doctest&gt;&#39;, 7, &#39;lumberstack&#39;, &#39;print(repr(traceback.extract_stack()))&#39;)]</span>
<span class="x">[&#39;  File &quot;&lt;doctest&gt;&quot;, line 10, in &lt;module&gt;\n    another_function()\n&#39;,</span>
<span class="x"> &#39;  File &quot;&lt;doctest&gt;&quot;, line 3, in another_function\n    lumberstack()\n&#39;,</span>
<span class="x"> &#39;  File &quot;&lt;doctest&gt;&quot;, line 8, in lumberstack\n    print(repr(traceback.format_stack()))\n&#39;]</span>
</pre></div>
</div>
<p>This last example demonstrates the final few formatting functions:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">traceback</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">traceback</span><span class="o">.</span><span class="n">format_list</span><span class="p">([(</span><span class="s1">&#39;spam.py&#39;</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="s1">&#39;&lt;module&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;spam.eggs()&#39;</span><span class="p">),</span>
<span class="gp">... </span>                       <span class="p">(</span><span class="s1">&#39;eggs.py&#39;</span><span class="p">,</span> <span class="mi">42</span><span class="p">,</span> <span class="s1">&#39;eggs&#39;</span><span class="p">,</span> <span class="s1">&#39;return &quot;bacon&quot;&#39;</span><span class="p">)])</span>
<span class="go">[&#39;  File &quot;spam.py&quot;, line 3, in &lt;module&gt;\n    spam.eggs()\n&#39;,</span>
<span class="go"> &#39;  File &quot;eggs.py&quot;, line 42, in eggs\n    return &quot;bacon&quot;\n&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">an_error</span> <span class="o">=</span> <span class="ne">IndexError</span><span class="p">(</span><span class="s1">&#39;tuple index out of range&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">traceback</span><span class="o">.</span><span class="n">format_exception_only</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">an_error</span><span class="p">),</span> <span class="n">an_error</span><span class="p">)</span>
<span class="go">[&#39;IndexError: tuple index out of range\n&#39;]</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a><ul>
<li><a class="reference internal" href="#tracebackexception-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">TracebackException</span></code> Objects</a></li>
<li><a class="reference internal" href="#stacksummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">StackSummary</span></code> Objects</a></li>
<li><a class="reference internal" href="#framesummary-objects"><code class="xref py py-class docutils literal notranslate"><span class="pre">FrameSummary</span></code> Objects</a></li>
<li><a class="reference internal" href="#traceback-examples">Traceback Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="atexit.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">atexit</span></code> — Exit handlers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="__future__.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/traceback.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="__future__.html" title="__future__ — Future statement definitions"
             >next</a> |</li>
        <li class="right" >
          <a href="atexit.html" title="atexit — Exit handlers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>