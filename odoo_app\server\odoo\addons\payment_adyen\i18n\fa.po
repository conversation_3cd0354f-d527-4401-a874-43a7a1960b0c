# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_adyen
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# odooers ir, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Most<PERSON><PERSON> Barmshory <<EMAIL>>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid ""
"<strong>Warning:</strong> To capture the amount manually, you also need to set\n"
"                    the Capture Delay to manual on your Adyen account settings."
msgstr ""
"<strong>هشدار:</strong> برای ثبت مبلغ به صورت دستی، باید تأخیر ثبت را در "
"تنظیمات حساب Adyen خود به صورت دستی تنظیم کنید."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "A request was sent to void the transaction with reference %s (%s)."
msgstr "یک درخواست برای باطل کردن تراکنش با مرجع %s (%s) ارسال شد."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_key
msgid "API Key"
msgstr "کلید API"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "API URL Prefix"
msgstr "پیشوند آدرس URL API"

#. module: payment_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_provider__code__adyen
msgid "Adyen"
msgstr "آدین"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr "یک خطا در پردازش پرداخت شما رخ داده است. لطفاً دوباره تلاش کنید."

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot display the payment form"
msgstr "نمی‌توان فرم پرداخت را نمایش داد"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_client_key
msgid "Client Key"
msgstr "کلید مشتری"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__code
msgid "Code"
msgstr "کد"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "اتصال به API برقرار نشد."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "HMAC Key"
msgstr "کلید HMAC"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_capture_wizard__has_adyen_tx
msgid "Has Adyen Tx"
msgstr "<br> دارای تراکنش Adyen"

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect payment details"
msgstr "جزئیات پرداخت نادرست"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid "Learn More"
msgstr "بیشتر بدانید"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "Merchant Account"
msgstr "حساب بازرگان"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "هیچ تراکنشی با مرجع %s پیدا نشد."

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "جادوگر دریافت پرداخت"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_provider
msgid "Payment Provider"
msgstr "سرویس دهنده پرداخت"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_token
msgid "Payment Token"
msgstr "توکن پرداخت"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "تراکنش پرداخت"

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "پردازش پرداخت ناموفق بود"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data for child transaction with missing transaction values"
msgstr "داده‌های دریافت‌شده برای تراکنش فرعی با مقادیر تراکنش مفقود شده"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment state: %s"
msgstr "داده‌های دریافت‌شده با وضعیت نامعتبر پرداخت: %s"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "داده‌های دریافتی با مرجع فروشنده از دست رفته"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr "داده‌های دریافتی با وضعیت پرداخت ناموجود."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "داده‌های درخواستی پرداخت دستکاری‌شده دریافت شد."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "Shopper Reference"
msgstr "ارجاع خریدار"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_key
msgid "The API key of the webservice user"
msgstr "کلید API کاربران وب سرویس"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "The HMAC key of the webhook"
msgstr "کلید HMAC وبهوک"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"The amount processed by Adyen for the transaction %s is different than the "
"one requested. Another transaction is created with the correct amount."
msgstr ""
"مبلغ پردازش شده توسط Adyen برای تراکنش %s با مبلغ درخواستی متفاوت است. "
"تراکنش دیگری با مبلغ صحیح ایجاد شده است."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "The base URL for the API endpoints"
msgstr "آدرس پایه برای نقطه‌های پایانی API"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The capture of the transaction with reference %s failed."
msgstr "تراکنش با مرجع %s ناموفق بود."

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_capture_wizard_view_form
msgid ""
"The capture or void of the transaction might take a few minutes to be\n"
"                    processed by Adyen and reflected in Odoo."
msgstr ""
"تکمیل یا لغو تراکنش ممکن است چند دقیقه طول بکشد تا توسط Adyen پردازش شده و "
"در Odoo منعکس شود."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"The capture request of %(amount)s for the transaction with reference %(ref)s"
" has been requested (%(provider_name)s)."
msgstr ""
"درخواست دریافت مبلغ %(amount)s برای تراکنش با مرجع %(ref)s درخواست شده است "
"(%(provider_name)s)."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_client_key
msgid "The client key of the webservice user"
msgstr "کلید کلاینت کاربر وب سرویس"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "The code of the merchant account to use with this provider"
msgstr "کد حساب بازرگان برای استفاده با این ارائه‌دهنده"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed. Details: %s"
msgstr "ارتباط با API با خطا مواجه شد. جزئیات: %s"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "کد فنی این ارائه دهنده پرداخت."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "تراکنش به یک توکن متصل نیست."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "The unique reference of the partner owning this token"
msgstr "مرجع منحصربه‌فرد شریک صاحب این توکن"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The void of the transaction with reference %s failed."
msgstr "پراکندگی تراکنش با مرجع %s با شکست مواجه شد."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Your payment was refused. Please try again."
msgstr "پرداخت شما رد شد. لطفاً دوباره امتحان کنید."
