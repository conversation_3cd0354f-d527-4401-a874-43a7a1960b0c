<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order_sale.CartPage" t-inherit="pos_self_order.CartPage" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('order-content')]" position="inside">
            <div t-if="optionalProducts.length" class="upsale-content border-top bg-view mt-2 px-3 py-4 py-md-5">
                <h2 class="mb-5">Want to add something ?</h2>
                <div class="upsale-product row justify-content-between justify-content-md-start row-cols-sm-2 row-cols-md-4 row-cols-lg-4 row-cols-xl-5 row-cols-xxl-6">
                    <t t-foreach="optionalProducts" t-as="product" t-key="product.id">
                        <ProductCard product="product" />
                    </t>
                </div>
            </div>
        </xpath>

        <xpath expr="//div[hasclass('order-content')]" position="attributes">
            <attribute name="class" remove="pb-4" separator=" "/>
        </xpath>
    </t>
</templates>
