<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sys — System-specific parameters and functions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sys.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides access to some variables used or maintained by the interpreter and to functions that interact strongly with the interpreter. It is always available. Citations C99, ISO/IEC 9899..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides access to some variables used or maintained by the interpreter and to functions that interact strongly with the interpreter. It is always available. Citations C99, ISO/IEC 9899..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sys — System-specific parameters and functions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="sys.monitoring — Execution event monitoring" href="sys.monitoring.html" />
    <link rel="prev" title="Python Runtime Services" href="python.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sys.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="python.html"
                          title="previous chapter">Python Runtime Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys.monitoring.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys.monitoring.html" title="sys.monitoring — Execution event monitoring"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="python.html" title="Python Runtime Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sys">
<span id="sys-system-specific-parameters-and-functions"></span><h1><a class="reference internal" href="#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> — System-specific parameters and functions<a class="headerlink" href="#module-sys" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides access to some variables used or maintained by the
interpreter and to functions that interact strongly with the interpreter. It is
always available.</p>
<dl class="py data">
<dt class="sig sig-object py" id="sys.abiflags">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">abiflags</span></span><a class="headerlink" href="#sys.abiflags" title="Link to this definition">¶</a></dt>
<dd><p>On POSIX systems where Python was built with the standard <code class="docutils literal notranslate"><span class="pre">configure</span></code>
script, this contains the ABI flags as specified by <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3149/"><strong>PEP 3149</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Default flags became an empty string (<code class="docutils literal notranslate"><span class="pre">m</span></code> flag for pymalloc has been
removed).</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.addaudithook">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">addaudithook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hook</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.addaudithook" title="Link to this definition">¶</a></dt>
<dd><p>Append the callable <em>hook</em> to the list of active auditing hooks for the
current (sub)interpreter.</p>
<p>When an auditing event is raised through the <a class="reference internal" href="#sys.audit" title="sys.audit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.audit()</span></code></a> function, each
hook will be called in the order it was added with the event name and the
tuple of arguments. Native hooks added by <a class="reference internal" href="../c-api/sys.html#c.PySys_AddAuditHook" title="PySys_AddAuditHook"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddAuditHook()</span></code></a> are
called first, followed by hooks added in the current (sub)interpreter.  Hooks
can then log the event, raise an exception to abort the operation,
or terminate the process entirely.</p>
<p>Note that audit hooks are primarily for collecting information about internal
or otherwise unobservable actions, whether by Python or libraries written in
Python. They are not suitable for implementing a “sandbox”. In particular,
malicious code can trivially disable or bypass hooks added using this
function. At a minimum, any security-sensitive hooks must be added using the
C API <a class="reference internal" href="../c-api/sys.html#c.PySys_AddAuditHook" title="PySys_AddAuditHook"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddAuditHook()</span></code></a> before initialising the runtime, and any
modules allowing arbitrary memory modification (such as <a class="reference internal" href="ctypes.html#module-ctypes" title="ctypes: A foreign function library for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ctypes</span></code></a>) should
be completely removed or closely monitored.</p>
<p class="audit-hook"><p>Calling <a class="reference internal" href="#sys.addaudithook" title="sys.addaudithook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.addaudithook()</span></code></a> will itself raise an auditing event
named <code class="docutils literal notranslate"><span class="pre">sys.addaudithook</span></code> with no arguments. If any
existing hooks raise an exception derived from <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>, the
new hook will not be added and the exception suppressed. As a result,
callers cannot assume that their hook has been added unless they control
all existing hooks.</p>
</p>
<p>See the <a class="reference internal" href="audit_events.html#audit-events"><span class="std std-ref">audit events table</span></a> for all events raised by
CPython, and <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0578/"><strong>PEP 578</strong></a> for the original design discussion.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8.1: </span>Exceptions derived from <a class="reference internal" href="exceptions.html#Exception" title="Exception"><code class="xref py py-class docutils literal notranslate"><span class="pre">Exception</span></code></a> but not <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>
are no longer suppressed.</p>
</div>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> When tracing is enabled (see <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a>), Python hooks are only
traced if the callable has a <code class="docutils literal notranslate"><span class="pre">__cantrace__</span></code> member that is set to a
true value. Otherwise, trace functions will skip the hook.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.argv">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">argv</span></span><a class="headerlink" href="#sys.argv" title="Link to this definition">¶</a></dt>
<dd><p>The list of command line arguments passed to a Python script. <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> is the
script name (it is operating system dependent whether this is a full pathname or
not).  If the command was executed using the <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a> command line option to
the interpreter, <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> is set to the string <code class="docutils literal notranslate"><span class="pre">'-c'</span></code>.  If no script name
was passed to the Python interpreter, <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> is the empty string.</p>
<p>To loop over the standard input, or the list of files given on the
command line, see the <a class="reference internal" href="fileinput.html#module-fileinput" title="fileinput: Loop over standard input or a list of files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fileinput</span></code></a> module.</p>
<p>See also <a class="reference internal" href="#sys.orig_argv" title="sys.orig_argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.orig_argv</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Unix, command line arguments are passed by bytes from OS.  Python decodes
them with filesystem encoding and “surrogateescape” error handler.
When you need original bytes, you can get it by
<code class="docutils literal notranslate"><span class="pre">[os.fsencode(arg)</span> <span class="pre">for</span> <span class="pre">arg</span> <span class="pre">in</span> <span class="pre">sys.argv]</span></code>.</p>
</div>
</dd></dl>

<dl class="py function" id="auditing">
<dt class="sig sig-object py" id="sys.audit">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">audit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">event</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.audit" title="Link to this definition">¶</a></dt>
<dd><p id="index-2">Raise an auditing event and trigger any active auditing hooks.
<em>event</em> is a string identifying the event, and <em>args</em> may contain
optional arguments with more information about the event.  The
number and types of arguments for a given event are considered a
public and stable API and should not be modified between releases.</p>
<p>For example, one auditing event is named <code class="docutils literal notranslate"><span class="pre">os.chdir</span></code>. This event has
one argument called <em>path</em> that will contain the requested new
working directory.</p>
<p><a class="reference internal" href="#sys.audit" title="sys.audit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.audit()</span></code></a> will call the existing auditing hooks, passing
the event name and arguments, and will re-raise the first exception
from any hook. In general, if an exception is raised, it should not
be handled and the process should be terminated as quickly as
possible. This allows hook implementations to decide how to respond
to particular events: they can merely log the event or abort the
operation by raising an exception.</p>
<p>Hooks are added using the <a class="reference internal" href="#sys.addaudithook" title="sys.addaudithook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.addaudithook()</span></code></a> or
<a class="reference internal" href="../c-api/sys.html#c.PySys_AddAuditHook" title="PySys_AddAuditHook"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddAuditHook()</span></code></a> functions.</p>
<p>The native equivalent of this function is <a class="reference internal" href="../c-api/sys.html#c.PySys_Audit" title="PySys_Audit"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_Audit()</span></code></a>. Using the
native function is preferred when possible.</p>
<p>See the <a class="reference internal" href="audit_events.html#audit-events"><span class="std std-ref">audit events table</span></a> for all events raised by
CPython.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.base_exec_prefix">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">base_exec_prefix</span></span><a class="headerlink" href="#sys.base_exec_prefix" title="Link to this definition">¶</a></dt>
<dd><p>Set during Python startup, before <code class="docutils literal notranslate"><span class="pre">site.py</span></code> is run, to the same value as
<a class="reference internal" href="#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">exec_prefix</span></code></a>. If not running in a
<a class="reference internal" href="venv.html#venv-def"><span class="std std-ref">virtual environment</span></a>, the values will stay the same; if
<code class="docutils literal notranslate"><span class="pre">site.py</span></code> finds that a virtual environment is in use, the values of
<a class="reference internal" href="#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">prefix</span></code></a> and <a class="reference internal" href="#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">exec_prefix</span></code></a> will be changed to point to the
virtual environment, whereas <a class="reference internal" href="#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_prefix</span></code></a> and
<a class="reference internal" href="#sys.base_exec_prefix" title="sys.base_exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_exec_prefix</span></code></a> will remain pointing to the base Python
installation (the one which the virtual environment was created from).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.base_prefix">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">base_prefix</span></span><a class="headerlink" href="#sys.base_prefix" title="Link to this definition">¶</a></dt>
<dd><p>Set during Python startup, before <code class="docutils literal notranslate"><span class="pre">site.py</span></code> is run, to the same value as
<a class="reference internal" href="#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">prefix</span></code></a>. If not running in a <a class="reference internal" href="venv.html#venv-def"><span class="std std-ref">virtual environment</span></a>, the values
will stay the same; if <code class="docutils literal notranslate"><span class="pre">site.py</span></code> finds that a virtual environment is in
use, the values of <a class="reference internal" href="#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">prefix</span></code></a> and <a class="reference internal" href="#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">exec_prefix</span></code></a> will be changed to
point to the virtual environment, whereas <a class="reference internal" href="#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_prefix</span></code></a> and
<a class="reference internal" href="#sys.base_exec_prefix" title="sys.base_exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_exec_prefix</span></code></a> will remain pointing to the base Python
installation (the one which the virtual environment was created from).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.byteorder">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">byteorder</span></span><a class="headerlink" href="#sys.byteorder" title="Link to this definition">¶</a></dt>
<dd><p>An indicator of the native byte order.  This will have the value <code class="docutils literal notranslate"><span class="pre">'big'</span></code> on
big-endian (most-significant byte first) platforms, and <code class="docutils literal notranslate"><span class="pre">'little'</span></code> on
little-endian (least-significant byte first) platforms.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.builtin_module_names">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">builtin_module_names</span></span><a class="headerlink" href="#sys.builtin_module_names" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of strings containing the names of all modules that are compiled into this
Python interpreter.  (This information is not available in any other way —
<code class="docutils literal notranslate"><span class="pre">modules.keys()</span></code> only lists the imported modules.)</p>
<p>See also the <a class="reference internal" href="#sys.stdlib_module_names" title="sys.stdlib_module_names"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdlib_module_names</span></code></a> list.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.call_tracing">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">call_tracing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">func</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.call_tracing" title="Link to this definition">¶</a></dt>
<dd><p>Call <code class="docutils literal notranslate"><span class="pre">func(*args)</span></code>, while tracing is enabled.  The tracing state is saved,
and restored afterwards.  This is intended to be called from a debugger from
a checkpoint, to recursively debug or profile some other code.</p>
<p>Tracing is suspended while calling a tracing function set by
<a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a> or <a class="reference internal" href="#sys.setprofile" title="sys.setprofile"><code class="xref py py-func docutils literal notranslate"><span class="pre">setprofile()</span></code></a> to avoid infinite recursion.
<code class="xref py py-func docutils literal notranslate"><span class="pre">call_tracing()</span></code> enables explicit recursion of the tracing function.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.copyright">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">copyright</span></span><a class="headerlink" href="#sys.copyright" title="Link to this definition">¶</a></dt>
<dd><p>A string containing the copyright pertaining to the Python interpreter.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._clear_type_cache">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_clear_type_cache</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._clear_type_cache" title="Link to this definition">¶</a></dt>
<dd><p>Clear the internal type cache. The type cache is used to speed up attribute
and method lookups. Use the function <em>only</em> to drop unnecessary references
during reference leak debugging.</p>
<p>This function should be used for internal and specialized purposes only.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._current_frames">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_current_frames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._current_frames" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary mapping each thread’s identifier to the topmost stack frame
currently active in that thread at the time the function is called. Note that
functions in the <a class="reference internal" href="traceback.html#module-traceback" title="traceback: Print or retrieve a stack traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code></a> module can build the call stack given such a
frame.</p>
<p>This is most useful for debugging deadlock:  this function does not require the
deadlocked threads’ cooperation, and such threads’ call stacks are frozen for as
long as they remain deadlocked.  The frame returned for a non-deadlocked thread
may bear no relationship to that thread’s current activity by the time calling
code examines the frame.</p>
<p>This function should be used for internal and specialized purposes only.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys._current_frames</span></code> with no arguments.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._current_exceptions">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_current_exceptions</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._current_exceptions" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary mapping each thread’s identifier to the topmost exception
currently active in that thread at the time the function is called.
If a thread is not currently handling an exception, it is not included in
the result dictionary.</p>
<p>This is most useful for statistical profiling.</p>
<p>This function should be used for internal and specialized purposes only.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys._current_exceptions</span></code> with no arguments.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Each value in the dictionary is now a single exception instance, rather
than a 3-tuple as returned from <code class="docutils literal notranslate"><span class="pre">sys.exc_info()</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.breakpointhook">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">breakpointhook</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.breakpointhook" title="Link to this definition">¶</a></dt>
<dd><p>This hook function is called by built-in <a class="reference internal" href="functions.html#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a>.  By default,
it drops you into the <a class="reference internal" href="pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> debugger, but it can be set to any other
function so that you can choose which debugger gets used.</p>
<p>The signature of this function is dependent on what it calls.  For example,
the default binding (e.g. <code class="docutils literal notranslate"><span class="pre">pdb.set_trace()</span></code>) expects no arguments, but
you might bind it to a function that expects additional arguments
(positional and/or keyword).  The built-in <code class="docutils literal notranslate"><span class="pre">breakpoint()</span></code> function passes
its <code class="docutils literal notranslate"><span class="pre">*args</span></code> and <code class="docutils literal notranslate"><span class="pre">**kws</span></code> straight through.  Whatever
<code class="docutils literal notranslate"><span class="pre">breakpointhooks()</span></code> returns is returned from <code class="docutils literal notranslate"><span class="pre">breakpoint()</span></code>.</p>
<p>The default implementation first consults the environment variable
<span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONBREAKPOINT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONBREAKPOINT</span></code></a>.  If that is set to <code class="docutils literal notranslate"><span class="pre">&quot;0&quot;</span></code> then this function
returns immediately; i.e. it is a no-op.  If the environment variable is
not set, or is set to the empty string, <code class="docutils literal notranslate"><span class="pre">pdb.set_trace()</span></code> is called.
Otherwise this variable should name a function to run, using Python’s
dotted-import nomenclature, e.g. <code class="docutils literal notranslate"><span class="pre">package.subpackage.module.function</span></code>.
In this case, <code class="docutils literal notranslate"><span class="pre">package.subpackage.module</span></code> would be imported and the
resulting module must have a callable named <code class="docutils literal notranslate"><span class="pre">function()</span></code>.  This is run,
passing in <code class="docutils literal notranslate"><span class="pre">*args</span></code> and <code class="docutils literal notranslate"><span class="pre">**kws</span></code>, and whatever <code class="docutils literal notranslate"><span class="pre">function()</span></code> returns,
<code class="docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code> returns to the built-in <a class="reference internal" href="functions.html#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a>
function.</p>
<p>Note that if anything goes wrong while importing the callable named by
<span class="target" id="index-4"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONBREAKPOINT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONBREAKPOINT</span></code></a>, a <a class="reference internal" href="exceptions.html#RuntimeWarning" title="RuntimeWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeWarning</span></code></a> is reported and the
breakpoint is ignored.</p>
<p>Also note that if <code class="docutils literal notranslate"><span class="pre">sys.breakpointhook()</span></code> is overridden programmatically,
<span class="target" id="index-5"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONBREAKPOINT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONBREAKPOINT</span></code></a> is <em>not</em> consulted.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._debugmallocstats">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_debugmallocstats</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._debugmallocstats" title="Link to this definition">¶</a></dt>
<dd><p>Print low-level information to stderr about the state of CPython’s memory
allocator.</p>
<p>If Python is <a class="reference internal" href="../using/configure.html#debug-build"><span class="std std-ref">built in debug mode</span></a> (<a class="reference internal" href="../using/configure.html#cmdoption-with-pydebug"><code class="xref std std-option docutils literal notranslate"><span class="pre">configure</span>
<span class="pre">--with-pydebug</span> <span class="pre">option</span></code></a>), it also performs some expensive
internal consistency checks.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This function is specific to CPython.  The exact output format is not
defined here, and may change.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.dllhandle">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">dllhandle</span></span><a class="headerlink" href="#sys.dllhandle" title="Link to this definition">¶</a></dt>
<dd><p>Integer specifying the handle of the Python DLL.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.displayhook">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">displayhook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.displayhook" title="Link to this definition">¶</a></dt>
<dd><p>If <em>value</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, this function prints <code class="docutils literal notranslate"><span class="pre">repr(value)</span></code> to
<code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>, and saves <em>value</em> in <code class="docutils literal notranslate"><span class="pre">builtins._</span></code>. If <code class="docutils literal notranslate"><span class="pre">repr(value)</span></code> is
not encodable to <code class="docutils literal notranslate"><span class="pre">sys.stdout.encoding</span></code> with <code class="docutils literal notranslate"><span class="pre">sys.stdout.errors</span></code> error
handler (which is probably <code class="docutils literal notranslate"><span class="pre">'strict'</span></code>), encode it to
<code class="docutils literal notranslate"><span class="pre">sys.stdout.encoding</span></code> with <code class="docutils literal notranslate"><span class="pre">'backslashreplace'</span></code> error handler.</p>
<p><code class="docutils literal notranslate"><span class="pre">sys.displayhook</span></code> is called on the result of evaluating an <a class="reference internal" href="../glossary.html#term-expression"><span class="xref std std-term">expression</span></a>
entered in an interactive Python session.  The display of these values can be
customized by assigning another one-argument function to <code class="docutils literal notranslate"><span class="pre">sys.displayhook</span></code>.</p>
<p>Pseudo-code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">displayhook</span><span class="p">(</span><span class="n">value</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">value</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="c1"># Set &#39;_&#39; to None to avoid recursion</span>
    <span class="n">builtins</span><span class="o">.</span><span class="n">_</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">text</span> <span class="o">=</span> <span class="nb">repr</span><span class="p">(</span><span class="n">value</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">UnicodeEncodeError</span><span class="p">:</span>
        <span class="nb">bytes</span> <span class="o">=</span> <span class="n">text</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">encoding</span><span class="p">,</span> <span class="s1">&#39;backslashreplace&#39;</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="s1">&#39;buffer&#39;</span><span class="p">):</span>
            <span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">buffer</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">bytes</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">text</span> <span class="o">=</span> <span class="nb">bytes</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">encoding</span><span class="p">,</span> <span class="s1">&#39;strict&#39;</span><span class="p">)</span>
            <span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">text</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">stdout</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="n">builtins</span><span class="o">.</span><span class="n">_</span> <span class="o">=</span> <span class="n">value</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Use <code class="docutils literal notranslate"><span class="pre">'backslashreplace'</span></code> error handler on <a class="reference internal" href="exceptions.html#UnicodeEncodeError" title="UnicodeEncodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">UnicodeEncodeError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.dont_write_bytecode">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">dont_write_bytecode</span></span><a class="headerlink" href="#sys.dont_write_bytecode" title="Link to this definition">¶</a></dt>
<dd><p>If this is true, Python won’t try to write <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files on the
import of source modules.  This value is initially set to <code class="docutils literal notranslate"><span class="pre">True</span></code> or
<code class="docutils literal notranslate"><span class="pre">False</span></code> depending on the <a class="reference internal" href="../using/cmdline.html#cmdoption-B"><code class="xref std std-option docutils literal notranslate"><span class="pre">-B</span></code></a> command line option and the
<span class="target" id="index-6"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDONTWRITEBYTECODE</span></code></a> environment variable, but you can set it
yourself to control bytecode file generation.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys._emscripten_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_emscripten_info</span></span><a class="headerlink" href="#sys._emscripten_info" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> holding information about the environment on the
<em>wasm32-emscripten</em> platform. The named tuple is provisional and may change
in the future.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sys._emscripten_info.emscripten_version">
<span class="sig-prename descclassname"><span class="pre">_emscripten_info.</span></span><span class="sig-name descname"><span class="pre">emscripten_version</span></span><a class="headerlink" href="#sys._emscripten_info.emscripten_version" title="Link to this definition">¶</a></dt>
<dd><p>Emscripten version as tuple of ints (major, minor, micro), e.g. <code class="docutils literal notranslate"><span class="pre">(3,</span> <span class="pre">1,</span> <span class="pre">8)</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys._emscripten_info.runtime">
<span class="sig-prename descclassname"><span class="pre">_emscripten_info.</span></span><span class="sig-name descname"><span class="pre">runtime</span></span><a class="headerlink" href="#sys._emscripten_info.runtime" title="Link to this definition">¶</a></dt>
<dd><p>Runtime string, e.g. browser user agent, <code class="docutils literal notranslate"><span class="pre">'Node.js</span> <span class="pre">v14.18.2'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'UNKNOWN'</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys._emscripten_info.pthreads">
<span class="sig-prename descclassname"><span class="pre">_emscripten_info.</span></span><span class="sig-name descname"><span class="pre">pthreads</span></span><a class="headerlink" href="#sys._emscripten_info.pthreads" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if Python is compiled with Emscripten pthreads support.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys._emscripten_info.shared_memory">
<span class="sig-prename descclassname"><span class="pre">_emscripten_info.</span></span><span class="sig-name descname"><span class="pre">shared_memory</span></span><a class="headerlink" href="#sys._emscripten_info.shared_memory" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if Python is compiled with shared memory support.</p>
</dd></dl>

<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Emscripten.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.pycache_prefix">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">pycache_prefix</span></span><a class="headerlink" href="#sys.pycache_prefix" title="Link to this definition">¶</a></dt>
<dd><p>If this is set (not <code class="docutils literal notranslate"><span class="pre">None</span></code>), Python will write bytecode-cache <code class="docutils literal notranslate"><span class="pre">.pyc</span></code>
files to (and read them from) a parallel directory tree rooted at this
directory, rather than from <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code> directories in the source code
tree. Any <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code> directories in the source code tree will be ignored
and new <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files written within the pycache prefix. Thus if you use
<a class="reference internal" href="compileall.html#module-compileall" title="compileall: Tools for byte-compiling all Python source files in a directory tree."><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code></a> as a pre-build step, you must ensure you run it with the
same pycache prefix (if any) that you will use at runtime.</p>
<p>A relative path is interpreted relative to the current working directory.</p>
<p>This value is initially set based on the value of the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a>
<code class="docutils literal notranslate"><span class="pre">pycache_prefix=PATH</span></code> command-line option or the
<span class="target" id="index-7"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPYCACHEPREFIX"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPYCACHEPREFIX</span></code></a> environment variable (command-line takes
precedence). If neither are set, it is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.excepthook">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">excepthook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">traceback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.excepthook" title="Link to this definition">¶</a></dt>
<dd><p>This function prints out a given traceback and exception to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>.</p>
<p>When an exception other than <a class="reference internal" href="exceptions.html#SystemExit" title="SystemExit"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemExit</span></code></a> is raised and uncaught, the interpreter calls
<code class="docutils literal notranslate"><span class="pre">sys.excepthook</span></code> with three arguments, the exception class, exception
instance, and a traceback object.  In an interactive session this happens just
before control is returned to the prompt; in a Python program this happens just
before the program exits.  The handling of such top-level exceptions can be
customized by assigning another three-argument function to <code class="docutils literal notranslate"><span class="pre">sys.excepthook</span></code>.</p>
<p class="audit-hook"><p>Raise an auditing event <code class="docutils literal notranslate"><span class="pre">sys.excepthook</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">hook</span></code>,
<code class="docutils literal notranslate"><span class="pre">type</span></code>, <code class="docutils literal notranslate"><span class="pre">value</span></code>, <code class="docutils literal notranslate"><span class="pre">traceback</span></code> when an uncaught exception occurs.
If no hook has been set, <code class="docutils literal notranslate"><span class="pre">hook</span></code> may be <code class="docutils literal notranslate"><span class="pre">None</span></code>. If any hook raises
an exception derived from <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-class docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> the call to the hook will
be suppressed. Otherwise, the audit hook exception will be reported as
unraisable and <code class="docutils literal notranslate"><span class="pre">sys.excepthook</span></code> will be called.</p>
</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference internal" href="#sys.unraisablehook" title="sys.unraisablehook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.unraisablehook()</span></code></a> function handles unraisable exceptions
and the <a class="reference internal" href="threading.html#threading.excepthook" title="threading.excepthook"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.excepthook()</span></code></a> function handles exception raised
by <a class="reference internal" href="threading.html#threading.Thread.run" title="threading.Thread.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.Thread.run()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.__breakpointhook__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__breakpointhook__</span></span><a class="headerlink" href="#sys.__breakpointhook__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.__displayhook__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__displayhook__</span></span><a class="headerlink" href="#sys.__displayhook__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.__excepthook__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__excepthook__</span></span><a class="headerlink" href="#sys.__excepthook__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.__unraisablehook__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__unraisablehook__</span></span><a class="headerlink" href="#sys.__unraisablehook__" title="Link to this definition">¶</a></dt>
<dd><p>These objects contain the original values of <code class="docutils literal notranslate"><span class="pre">breakpointhook</span></code>,
<code class="docutils literal notranslate"><span class="pre">displayhook</span></code>, <code class="docutils literal notranslate"><span class="pre">excepthook</span></code>, and <code class="docutils literal notranslate"><span class="pre">unraisablehook</span></code> at the start of the
program.  They are saved so that <code class="docutils literal notranslate"><span class="pre">breakpointhook</span></code>, <code class="docutils literal notranslate"><span class="pre">displayhook</span></code> and
<code class="docutils literal notranslate"><span class="pre">excepthook</span></code>, <code class="docutils literal notranslate"><span class="pre">unraisablehook</span></code> can be restored in case they happen to
get replaced with broken or alternative objects.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>__breakpointhook__</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>__unraisablehook__</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.exception">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">exception</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.exception" title="Link to this definition">¶</a></dt>
<dd><p>This function, when called while an exception handler is executing (such as
an <code class="docutils literal notranslate"><span class="pre">except</span></code> or <code class="docutils literal notranslate"><span class="pre">except*</span></code> clause), returns the exception instance that
was caught by this handler. When exception handlers are nested within one
another, only the exception handled by the innermost handler is accessible.</p>
<p>If no exception handler is executing, this function returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.exc_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">exc_info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.exc_info" title="Link to this definition">¶</a></dt>
<dd><p>This function returns the old-style representation of the handled
exception. If an exception <code class="docutils literal notranslate"><span class="pre">e</span></code> is currently handled (so
<a class="reference internal" href="#sys.exception" title="sys.exception"><code class="xref py py-func docutils literal notranslate"><span class="pre">exception()</span></code></a> would return <code class="docutils literal notranslate"><span class="pre">e</span></code>), <a class="reference internal" href="#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">exc_info()</span></code></a> returns the
tuple <code class="docutils literal notranslate"><span class="pre">(type(e),</span> <span class="pre">e,</span> <span class="pre">e.__traceback__)</span></code>.
That is, a tuple containing the type of the exception (a subclass of
<a class="reference internal" href="exceptions.html#BaseException" title="BaseException"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BaseException</span></code></a>), the exception itself, and a <a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">traceback
object</span></a> which typically encapsulates the call
stack at the point where the exception last occurred.</p>
<p id="index-8">If no exception is being handled anywhere on the stack, this function
return a tuple containing three <code class="docutils literal notranslate"><span class="pre">None</span></code> values.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <code class="docutils literal notranslate"><span class="pre">type</span></code> and <code class="docutils literal notranslate"><span class="pre">traceback</span></code> fields are now derived from the <code class="docutils literal notranslate"><span class="pre">value</span></code>
(the exception instance), so when an exception is modified while it is
being handled, the changes are reflected in the results of subsequent
calls to <a class="reference internal" href="#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">exc_info()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.exec_prefix">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">exec_prefix</span></span><a class="headerlink" href="#sys.exec_prefix" title="Link to this definition">¶</a></dt>
<dd><p>A string giving the site-specific directory prefix where the platform-dependent
Python files are installed; by default, this is also <code class="docutils literal notranslate"><span class="pre">'/usr/local'</span></code>.  This can
be set at build time with the <code class="docutils literal notranslate"><span class="pre">--exec-prefix</span></code> argument to the
<strong class="program">configure</strong> script.  Specifically, all configuration files (e.g. the
<code class="file docutils literal notranslate"><span class="pre">pyconfig.h</span></code> header file) are installed in the directory
<code class="file docutils literal notranslate"><em><span class="pre">exec_prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/config</span></code>, and shared library modules are
installed in <code class="file docutils literal notranslate"><em><span class="pre">exec_prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/lib-dynload</span></code>, where <em>X.Y</em>
is the version number of Python, for example <code class="docutils literal notranslate"><span class="pre">3.2</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If a <a class="reference internal" href="venv.html#venv-def"><span class="std std-ref">virtual environment</span></a> is in effect, this
value will be changed in <code class="docutils literal notranslate"><span class="pre">site.py</span></code> to point to the virtual environment.
The value for the Python installation will still be available, via
<a class="reference internal" href="#sys.base_exec_prefix" title="sys.base_exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_exec_prefix</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.executable">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">executable</span></span><a class="headerlink" href="#sys.executable" title="Link to this definition">¶</a></dt>
<dd><p>A string giving the absolute path of the executable binary for the Python
interpreter, on systems where this makes sense. If Python is unable to retrieve
the real path to its executable, <a class="reference internal" href="#sys.executable" title="sys.executable"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.executable</span></code></a> will be an empty string
or <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.exit">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">exit</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.exit" title="Link to this definition">¶</a></dt>
<dd><p>Raise a <a class="reference internal" href="exceptions.html#SystemExit" title="SystemExit"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemExit</span></code></a> exception, signaling an intention to exit the interpreter.</p>
<p>The optional argument <em>arg</em> can be an integer giving the exit status
(defaulting to zero), or another type of object.  If it is an integer, zero
is considered “successful termination” and any nonzero value is considered
“abnormal termination” by shells and the like.  Most systems require it to be
in the range 0–127, and produce undefined results otherwise.  Some systems
have a convention for assigning specific meanings to specific exit codes, but
these are generally underdeveloped; Unix programs generally use 2 for command
line syntax errors and 1 for all other kind of errors.  If another type of
object is passed, <code class="docutils literal notranslate"><span class="pre">None</span></code> is equivalent to passing zero, and any other
object is printed to <a class="reference internal" href="#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">stderr</span></code></a> and results in an exit code of 1.  In
particular, <code class="docutils literal notranslate"><span class="pre">sys.exit(&quot;some</span> <span class="pre">error</span> <span class="pre">message&quot;)</span></code> is a quick way to exit a
program when an error occurs.</p>
<p>Since <a class="reference internal" href="constants.html#exit" title="exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">exit()</span></code></a> ultimately “only” raises an exception, it will only exit
the process when called from the main thread, and the exception is not
intercepted. Cleanup actions specified by finally clauses of <a class="reference internal" href="../reference/compound_stmts.html#try"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">try</span></code></a> statements
are honored, and it is possible to intercept the exit attempt at an outer level.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>If an error occurs in the cleanup after the Python interpreter
has caught <a class="reference internal" href="exceptions.html#SystemExit" title="SystemExit"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SystemExit</span></code></a> (such as an error flushing buffered data
in the standard streams), the exit status is changed to 120.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.flags">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">flags</span></span><a class="headerlink" href="#sys.flags" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> <em>flags</em> exposes the status of command line
flags. The attributes are read only.</p>
<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.debug">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">debug</span></span><a class="headerlink" href="#sys.flags.debug" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-d"><code class="xref std std-option docutils literal notranslate"><span class="pre">-d</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.inspect">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">inspect</span></span><a class="headerlink" href="#sys.flags.inspect" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.interactive">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">interactive</span></span><a class="headerlink" href="#sys.flags.interactive" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.isolated">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">isolated</span></span><a class="headerlink" href="#sys.flags.isolated" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.optimize">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">optimize</span></span><a class="headerlink" href="#sys.flags.optimize" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">-O</span></code></a> or <a class="reference internal" href="../using/cmdline.html#cmdoption-OO"><code class="xref std std-option docutils literal notranslate"><span class="pre">-OO</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.dont_write_bytecode">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">dont_write_bytecode</span></span><a class="headerlink" href="#sys.flags.dont_write_bytecode" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-B"><code class="xref std std-option docutils literal notranslate"><span class="pre">-B</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.no_user_site">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">no_user_site</span></span><a class="headerlink" href="#sys.flags.no_user_site" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.no_site">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">no_site</span></span><a class="headerlink" href="#sys.flags.no_site" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.ignore_environment">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">ignore_environment</span></span><a class="headerlink" href="#sys.flags.ignore_environment" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">-E</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.verbose">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">verbose</span></span><a class="headerlink" href="#sys.flags.verbose" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-v"><code class="xref std std-option docutils literal notranslate"><span class="pre">-v</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.bytes_warning">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">bytes_warning</span></span><a class="headerlink" href="#sys.flags.bytes_warning" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-b"><code class="xref std std-option docutils literal notranslate"><span class="pre">-b</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.quiet">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">quiet</span></span><a class="headerlink" href="#sys.flags.quiet" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-q"><code class="xref std std-option docutils literal notranslate"><span class="pre">-q</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.hash_randomization">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">hash_randomization</span></span><a class="headerlink" href="#sys.flags.hash_randomization" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-R"><code class="xref std std-option docutils literal notranslate"><span class="pre">-R</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.dev_mode">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">dev_mode</span></span><a class="headerlink" href="#sys.flags.dev_mode" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">dev</span></code></a> (<a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python Development Mode</span></a>)</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.utf8_mode">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">utf8_mode</span></span><a class="headerlink" href="#sys.flags.utf8_mode" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">utf8</span></code></a></p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.safe_path">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">safe_path</span></span><a class="headerlink" href="#sys.flags.safe_path" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-P"><code class="xref std std-option docutils literal notranslate"><span class="pre">-P</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.int_max_str_digits">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">int_max_str_digits</span></span><a class="headerlink" href="#sys.flags.int_max_str_digits" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">int_max_str_digits</span></code></a>
(<a class="reference internal" href="stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string conversion length limitation</span></a>)</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.flags.warn_default_encoding">
<span class="sig-prename descclassname"><span class="pre">flags.</span></span><span class="sig-name descname"><span class="pre">warn_default_encoding</span></span><a class="headerlink" href="#sys.flags.warn_default_encoding" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">warn_default_encoding</span></code></a></p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added <code class="docutils literal notranslate"><span class="pre">quiet</span></code> attribute for the new <a class="reference internal" href="../using/cmdline.html#cmdoption-q"><code class="xref std std-option docutils literal notranslate"><span class="pre">-q</span></code></a> flag.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.3: </span>The <code class="docutils literal notranslate"><span class="pre">hash_randomization</span></code> attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Removed obsolete <code class="docutils literal notranslate"><span class="pre">division_warning</span></code> attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added <code class="docutils literal notranslate"><span class="pre">isolated</span></code> attribute for <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> <code class="docutils literal notranslate"><span class="pre">isolated</span></code> flag.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <code class="docutils literal notranslate"><span class="pre">dev_mode</span></code> attribute for the new <a class="reference internal" href="devmode.html#devmode"><span class="std std-ref">Python Development
Mode</span></a> and the <code class="docutils literal notranslate"><span class="pre">utf8_mode</span></code> attribute for the new  <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a>
<code class="docutils literal notranslate"><span class="pre">utf8</span></code> flag.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added <code class="docutils literal notranslate"><span class="pre">warn_default_encoding</span></code> attribute for <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> <code class="docutils literal notranslate"><span class="pre">warn_default_encoding</span></code> flag.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <code class="docutils literal notranslate"><span class="pre">safe_path</span></code> attribute for <a class="reference internal" href="../using/cmdline.html#cmdoption-P"><code class="xref std std-option docutils literal notranslate"><span class="pre">-P</span></code></a> option.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added the <code class="docutils literal notranslate"><span class="pre">int_max_str_digits</span></code> attribute.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.float_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">float_info</span></span><a class="headerlink" href="#sys.float_info" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> holding information about the float type. It
contains low level information about the precision and internal
representation.  The values correspond to the various floating-point
constants defined in the standard header file <code class="file docutils literal notranslate"><span class="pre">float.h</span></code> for the ‘C’
programming language; see section 5.2.4.2.2 of the 1999 ISO/IEC C standard
<a class="reference internal" href="#c99" id="id1"><span>[C99]</span></a>, ‘Characteristics of floating types’, for details.</p>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">Attributes of the <code class="xref py py-data docutils literal notranslate"><span class="pre">float_info</span></code> <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a></span><a class="headerlink" href="#id2" title="Link to this table">¶</a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>attribute</p></th>
<th class="head"><p>float.h macro</p></th>
<th class="head"><p>explanation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.epsilon">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">epsilon</span></span><a class="headerlink" href="#sys.float_info.epsilon" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_EPSILON</span></code></p></td>
<td><p>difference between 1.0 and the least value greater than 1.0 that is
representable as a float.</p>
<p>See also <a class="reference internal" href="math.html#math.ulp" title="math.ulp"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.ulp()</span></code></a>.</p>
</td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.dig">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">dig</span></span><a class="headerlink" href="#sys.float_info.dig" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_DIG</span></code></p></td>
<td><p>The maximum number of decimal digits that can be faithfully
represented in a float; see below.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.mant_dig">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">mant_dig</span></span><a class="headerlink" href="#sys.float_info.mant_dig" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MANT_DIG</span></code></p></td>
<td><p>Float precision: the number of base-<code class="docutils literal notranslate"><span class="pre">radix</span></code> digits in the
significand of a float.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.max">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">max</span></span><a class="headerlink" href="#sys.float_info.max" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MAX</span></code></p></td>
<td><p>The maximum representable positive finite float.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.max_exp">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">max_exp</span></span><a class="headerlink" href="#sys.float_info.max_exp" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MAX_EXP</span></code></p></td>
<td><p>The maximum integer <em>e</em> such that <code class="docutils literal notranslate"><span class="pre">radix**(e-1)</span></code> is a representable
finite float.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.max_10_exp">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">max_10_exp</span></span><a class="headerlink" href="#sys.float_info.max_10_exp" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MAX_10_EXP</span></code></p></td>
<td><p>The maximum integer <em>e</em> such that <code class="docutils literal notranslate"><span class="pre">10**e</span></code> is in the range of
representable finite floats.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.min">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">min</span></span><a class="headerlink" href="#sys.float_info.min" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MIN</span></code></p></td>
<td><p>The minimum representable positive <em>normalized</em> float.</p>
<p>Use <a class="reference internal" href="math.html#math.ulp" title="math.ulp"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.ulp(0.0)</span></code></a> to get the smallest positive
<em>denormalized</em> representable float.</p>
</td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.min_exp">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">min_exp</span></span><a class="headerlink" href="#sys.float_info.min_exp" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MIN_EXP</span></code></p></td>
<td><p>The minimum integer <em>e</em> such that <code class="docutils literal notranslate"><span class="pre">radix**(e-1)</span></code> is a normalized
float.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.min_10_exp">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">min_10_exp</span></span><a class="headerlink" href="#sys.float_info.min_10_exp" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">DBL_MIN_10_EXP</span></code></p></td>
<td><p>The minimum integer <em>e</em> such that <code class="docutils literal notranslate"><span class="pre">10**e</span></code> is a normalized float.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.radix">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">radix</span></span><a class="headerlink" href="#sys.float_info.radix" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">FLT_RADIX</span></code></p></td>
<td><p>The radix of exponent representation.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="sys.float_info.rounds">
<span class="sig-prename descclassname"><span class="pre">float_info.</span></span><span class="sig-name descname"><span class="pre">rounds</span></span><a class="headerlink" href="#sys.float_info.rounds" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p><code class="xref c c-macro docutils literal notranslate"><span class="pre">FLT_ROUNDS</span></code></p></td>
<td><p>An integer representing the rounding mode for floating-point arithmetic.
This reflects the value of the system <code class="xref c c-macro docutils literal notranslate"><span class="pre">FLT_ROUNDS</span></code> macro
at interpreter startup time:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">-1</span></code>: indeterminable</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">0</span></code>: toward zero</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">1</span></code>: to nearest</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">2</span></code>: toward positive infinity</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">3</span></code>: toward negative infinity</p></li>
</ul>
<p>All other values for <code class="xref c c-macro docutils literal notranslate"><span class="pre">FLT_ROUNDS</span></code> characterize
implementation-defined rounding behavior.</p>
</td>
</tr>
</tbody>
</table>
<p>The attribute <a class="reference internal" href="#sys.float_info.dig" title="sys.float_info.dig"><code class="xref py py-attr docutils literal notranslate"><span class="pre">sys.float_info.dig</span></code></a> needs further explanation.  If
<code class="docutils literal notranslate"><span class="pre">s</span></code> is any string representing a decimal number with at most
<code class="xref py py-attr docutils literal notranslate"><span class="pre">sys.float_info.dig</span></code> significant digits, then converting <code class="docutils literal notranslate"><span class="pre">s</span></code> to a
float and back again will recover a string representing the same decimal
value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">sys</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sys</span><span class="o">.</span><span class="n">float_info</span><span class="o">.</span><span class="n">dig</span>
<span class="go">15</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;3.14159265358979&#39;</span>    <span class="c1"># decimal string with 15 significant digits</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">s</span><span class="p">),</span> <span class="s1">&#39;.15g&#39;</span><span class="p">)</span>  <span class="c1"># convert to float and back -&gt; same value</span>
<span class="go">&#39;3.14159265358979&#39;</span>
</pre></div>
</div>
<p>But for strings with more than <a class="reference internal" href="#sys.float_info.dig" title="sys.float_info.dig"><code class="xref py py-attr docutils literal notranslate"><span class="pre">sys.float_info.dig</span></code></a> significant digits,
this isn’t always true:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;9876543211234567&#39;</span>    <span class="c1"># 16 significant digits is too many!</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">s</span><span class="p">),</span> <span class="s1">&#39;.16g&#39;</span><span class="p">)</span>  <span class="c1"># conversion changes value</span>
<span class="go">&#39;9876543211234568&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.float_repr_style">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">float_repr_style</span></span><a class="headerlink" href="#sys.float_repr_style" title="Link to this definition">¶</a></dt>
<dd><p>A string indicating how the <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> function behaves for
floats.  If the string has value <code class="docutils literal notranslate"><span class="pre">'short'</span></code> then for a finite
float <code class="docutils literal notranslate"><span class="pre">x</span></code>, <code class="docutils literal notranslate"><span class="pre">repr(x)</span></code> aims to produce a short string with the
property that <code class="docutils literal notranslate"><span class="pre">float(repr(x))</span> <span class="pre">==</span> <span class="pre">x</span></code>.  This is the usual behaviour
in Python 3.1 and later.  Otherwise, <code class="docutils literal notranslate"><span class="pre">float_repr_style</span></code> has value
<code class="docutils literal notranslate"><span class="pre">'legacy'</span></code> and <code class="docutils literal notranslate"><span class="pre">repr(x)</span></code> behaves in the same way as it did in
versions of Python prior to 3.1.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getallocatedblocks">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getallocatedblocks</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getallocatedblocks" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of memory blocks currently allocated by the interpreter,
regardless of their size.  This function is mainly useful for tracking
and debugging memory leaks.  Because of the interpreter’s internal
caches, the result can vary from call to call; you may have to call
<a class="reference internal" href="#sys._clear_type_cache" title="sys._clear_type_cache"><code class="xref py py-func docutils literal notranslate"><span class="pre">_clear_type_cache()</span></code></a> and <a class="reference internal" href="gc.html#gc.collect" title="gc.collect"><code class="xref py py-func docutils literal notranslate"><span class="pre">gc.collect()</span></code></a> to get more
predictable results.</p>
<p>If a Python build or implementation cannot reasonably compute this
information, <a class="reference internal" href="#sys.getallocatedblocks" title="sys.getallocatedblocks"><code class="xref py py-func docutils literal notranslate"><span class="pre">getallocatedblocks()</span></code></a> is allowed to return 0 instead.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getunicodeinternedsize">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getunicodeinternedsize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getunicodeinternedsize" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of unicode objects that have been interned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getandroidapilevel">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getandroidapilevel</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getandroidapilevel" title="Link to this definition">¶</a></dt>
<dd><p>Return the build time API version of Android as an integer.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Android.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getdefaultencoding">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getdefaultencoding</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getdefaultencoding" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the current default string encoding used by the Unicode
implementation.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getdlopenflags">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getdlopenflags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getdlopenflags" title="Link to this definition">¶</a></dt>
<dd><p>Return the current value of the flags that are used for
<code class="xref c c-func docutils literal notranslate"><span class="pre">dlopen()</span></code> calls.  Symbolic names for the flag values can be
found in the <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module (<code class="samp docutils literal notranslate"><span class="pre">RTLD_</span><em><span class="pre">xxx</span></em></code> constants, e.g.
<a class="reference internal" href="os.html#os.RTLD_LAZY" title="os.RTLD_LAZY"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.RTLD_LAZY</span></code></a>).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getfilesystemencoding">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getfilesystemencoding</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getfilesystemencoding" title="Link to this definition">¶</a></dt>
<dd><p>Get the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding</span></a>:
the encoding used with the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem error handler</span></a> to convert between Unicode filenames and bytes
filenames. The filesystem error handler is returned from
<a class="reference internal" href="#sys.getfilesystemencodeerrors" title="sys.getfilesystemencodeerrors"><code class="xref py py-func docutils literal notranslate"><span class="pre">getfilesystemencodeerrors()</span></code></a>.</p>
<p>For best compatibility, str should be used for filenames in all cases,
although representing filenames as bytes is also supported. Functions
accepting or returning filenames should support either str or bytes and
internally convert to the system’s preferred representation.</p>
<p><a class="reference internal" href="os.html#os.fsencode" title="os.fsencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsencode()</span></code></a> and <a class="reference internal" href="os.html#os.fsdecode" title="os.fsdecode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsdecode()</span></code></a> should be used to ensure that
the correct encoding and errors mode are used.</p>
<p>The <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a> are configured at Python
startup by the <a class="reference internal" href="../c-api/init_config.html#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> function: see
<a class="reference internal" href="../c-api/init_config.html#c.PyConfig.filesystem_encoding" title="PyConfig.filesystem_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_encoding</span></code></a> and
<a class="reference internal" href="../c-api/init_config.html#c.PyConfig.filesystem_errors" title="PyConfig.filesystem_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_errors</span></code></a> members of <a class="reference internal" href="../c-api/init_config.html#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><a class="reference internal" href="#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">getfilesystemencoding()</span></code></a> result cannot be <code class="docutils literal notranslate"><span class="pre">None</span></code> anymore.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Windows is no longer guaranteed to return <code class="docutils literal notranslate"><span class="pre">'mbcs'</span></code>. See <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0529/"><strong>PEP 529</strong></a>
and <a class="reference internal" href="#sys._enablelegacywindowsfsencoding" title="sys._enablelegacywindowsfsencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">_enablelegacywindowsfsencoding()</span></code></a> for more information.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Return <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code> if the <a class="reference internal" href="os.html#utf8-mode"><span class="std std-ref">Python UTF-8 Mode</span></a> is
enabled.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getfilesystemencodeerrors">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getfilesystemencodeerrors</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getfilesystemencodeerrors" title="Link to this definition">¶</a></dt>
<dd><p>Get the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem error handler</span></a>: the error handler used with the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding</span></a> to convert between Unicode
filenames and bytes filenames. The filesystem encoding is returned from
<a class="reference internal" href="#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">getfilesystemencoding()</span></code></a>.</p>
<p><a class="reference internal" href="os.html#os.fsencode" title="os.fsencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsencode()</span></code></a> and <a class="reference internal" href="os.html#os.fsdecode" title="os.fsdecode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsdecode()</span></code></a> should be used to ensure that
the correct encoding and errors mode are used.</p>
<p>The <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a> are configured at Python
startup by the <a class="reference internal" href="../c-api/init_config.html#c.PyConfig_Read" title="PyConfig_Read"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyConfig_Read()</span></code></a> function: see
<a class="reference internal" href="../c-api/init_config.html#c.PyConfig.filesystem_encoding" title="PyConfig.filesystem_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_encoding</span></code></a> and
<a class="reference internal" href="../c-api/init_config.html#c.PyConfig.filesystem_errors" title="PyConfig.filesystem_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">filesystem_errors</span></code></a> members of <a class="reference internal" href="../c-api/init_config.html#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.get_int_max_str_digits">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">get_int_max_str_digits</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.get_int_max_str_digits" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current value for the <a class="reference internal" href="stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string conversion length
limitation</span></a>. See also <a class="reference internal" href="#sys.set_int_max_str_digits" title="sys.set_int_max_str_digits"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_int_max_str_digits()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getrefcount">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getrefcount</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.getrefcount" title="Link to this definition">¶</a></dt>
<dd><p>Return the reference count of the <em>object</em>.  The count returned is generally one
higher than you might expect, because it includes the (temporary) reference as
an argument to <a class="reference internal" href="#sys.getrefcount" title="sys.getrefcount"><code class="xref py py-func docutils literal notranslate"><span class="pre">getrefcount()</span></code></a>.</p>
<p>Note that the returned value may not actually reflect how many
references to the object are actually held.  For example, some
objects are “immortal” and have a very high refcount that does not
reflect the actual number of references.  Consequently, do not rely
on the returned value to be accurate, other than a value of 0 or 1.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Immortal objects have very large refcounts that do not match
the actual number of references to the object.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getrecursionlimit">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getrecursionlimit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getrecursionlimit" title="Link to this definition">¶</a></dt>
<dd><p>Return the current value of the recursion limit, the maximum depth of the Python
interpreter stack.  This limit prevents infinite recursion from causing an
overflow of the C stack and crashing Python.  It can be set by
<a class="reference internal" href="#sys.setrecursionlimit" title="sys.setrecursionlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">setrecursionlimit()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getsizeof">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getsizeof</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">object</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getsizeof" title="Link to this definition">¶</a></dt>
<dd><p>Return the size of an object in bytes. The object can be any type of
object. All built-in objects will return correct results, but this
does not have to hold true for third-party extensions as it is implementation
specific.</p>
<p>Only the memory consumption directly attributed to the object is
accounted for, not the memory consumption of objects it refers to.</p>
<p>If given, <em>default</em> will be returned if the object does not provide means to
retrieve the size.  Otherwise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> will be raised.</p>
<p><a class="reference internal" href="#sys.getsizeof" title="sys.getsizeof"><code class="xref py py-func docutils literal notranslate"><span class="pre">getsizeof()</span></code></a> calls the object’s <code class="docutils literal notranslate"><span class="pre">__sizeof__</span></code> method and adds an
additional garbage collector overhead if the object is managed by the garbage
collector.</p>
<p>See <a class="reference external" href="https://code.activestate.com/recipes/577504/">recursive sizeof recipe</a>
for an example of using <a class="reference internal" href="#sys.getsizeof" title="sys.getsizeof"><code class="xref py py-func docutils literal notranslate"><span class="pre">getsizeof()</span></code></a> recursively to find the size of
containers and all their contents.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getswitchinterval">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getswitchinterval</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getswitchinterval" title="Link to this definition">¶</a></dt>
<dd><p>Return the interpreter’s “thread switch interval”; see
<a class="reference internal" href="#sys.setswitchinterval" title="sys.setswitchinterval"><code class="xref py py-func docutils literal notranslate"><span class="pre">setswitchinterval()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._getframe">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_getframe</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._getframe" title="Link to this definition">¶</a></dt>
<dd><p>Return a frame object from the call stack.  If optional integer <em>depth</em> is
given, return the frame object that many calls below the top of the stack.  If
that is deeper than the call stack, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.  The default
for <em>depth</em> is zero, returning the frame at the top of the call stack.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys._getframe</span></code> with argument <code class="docutils literal notranslate"><span class="pre">frame</span></code>.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This function should be used for internal and specialized purposes only.
It is not guaranteed to exist in all implementations of Python.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._getframemodulename">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_getframemodulename</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._getframemodulename" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of a module from the call stack.  If optional integer <em>depth</em>
is given, return the module that many calls below the top of the stack.  If
that is deeper than the call stack, or if the module is unidentifiable,
<code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.  The default for <em>depth</em> is zero, returning the
module at the top of the call stack.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys._getframemodulename</span></code> with argument <code class="docutils literal notranslate"><span class="pre">depth</span></code>.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This function should be used for internal and specialized purposes only.
It is not guaranteed to exist in all implementations of Python.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getprofile">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getprofile</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getprofile" title="Link to this definition">¶</a></dt>
<dd><p id="index-10">Get the profiler function as set by <a class="reference internal" href="#sys.setprofile" title="sys.setprofile"><code class="xref py py-func docutils literal notranslate"><span class="pre">setprofile()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.gettrace">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">gettrace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.gettrace" title="Link to this definition">¶</a></dt>
<dd><p id="index-11">Get the trace function as set by <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a>.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> The <a class="reference internal" href="#sys.gettrace" title="sys.gettrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">gettrace()</span></code></a> function is intended only for implementing debuggers,
profilers, coverage tools and the like.  Its behavior is part of the
implementation platform, rather than part of the language definition, and
thus may not be available in all Python implementations.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.getwindowsversion">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">getwindowsversion</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.getwindowsversion" title="Link to this definition">¶</a></dt>
<dd><p>Return a named tuple describing the Windows version
currently running.  The named elements are <em>major</em>, <em>minor</em>,
<em>build</em>, <em>platform</em>, <em>service_pack</em>, <em>service_pack_minor</em>,
<em>service_pack_major</em>, <em>suite_mask</em>, <em>product_type</em> and
<em>platform_version</em>. <em>service_pack</em> contains a string,
<em>platform_version</em> a 3-tuple and all other values are
integers. The components can also be accessed by name, so
<code class="docutils literal notranslate"><span class="pre">sys.getwindowsversion()[0]</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">sys.getwindowsversion().major</span></code>. For compatibility with prior
versions, only the first 5 elements are retrievable by indexing.</p>
<p><em>platform</em> will be <code class="docutils literal notranslate"><span class="pre">2</span></code> (VER_PLATFORM_WIN32_NT).</p>
<p><em>product_type</em> may be one of the following values:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Constant</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code> (VER_NT_WORKSTATION)</p></td>
<td><p>The system is a workstation.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code> (VER_NT_DOMAIN_CONTROLLER)</p></td>
<td><p>The system is a domain
controller.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">3</span></code> (VER_NT_SERVER)</p></td>
<td><p>The system is a server, but not
a domain controller.</p></td>
</tr>
</tbody>
</table>
<p>This function wraps the Win32 <code class="xref c c-func docutils literal notranslate"><span class="pre">GetVersionEx()</span></code> function; see the
Microsoft documentation on <code class="xref c c-func docutils literal notranslate"><span class="pre">OSVERSIONINFOEX()</span></code> for more information
about these fields.</p>
<p><em>platform_version</em> returns the major version, minor version and
build number of the current operating system, rather than the version that
is being emulated for the process. It is intended for use in logging rather
than for feature detection.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><em>platform_version</em> derives the version from kernel32.dll which can be of a different
version than the OS version. Please use <a class="reference internal" href="platform.html#module-platform" title="platform: Retrieves as much platform identifying data as possible."><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code></a> module for achieving accurate
OS version.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Changed to a named tuple and added <em>service_pack_minor</em>,
<em>service_pack_major</em>, <em>suite_mask</em>, and <em>product_type</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added <em>platform_version</em></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.get_asyncgen_hooks">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">get_asyncgen_hooks</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.get_asyncgen_hooks" title="Link to this definition">¶</a></dt>
<dd><p>Returns an <em>asyncgen_hooks</em> object, which is similar to a
<a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a> of the form <code class="docutils literal notranslate"><span class="pre">(firstiter,</span> <span class="pre">finalizer)</span></code>,
where <em>firstiter</em> and <em>finalizer</em> are expected to be either <code class="docutils literal notranslate"><span class="pre">None</span></code> or
functions which take an <a class="reference internal" href="../glossary.html#term-asynchronous-generator-iterator"><span class="xref std std-term">asynchronous generator iterator</span></a> as an
argument, and are used to schedule finalization of an asynchronous
generator by an event loop.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>See <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-0525/"><strong>PEP 525</strong></a> for more details.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function has been added on a provisional basis (see <span class="target" id="index-13"></span><a class="pep reference external" href="https://peps.python.org/pep-0411/"><strong>PEP 411</strong></a>
for details.)</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.get_coroutine_origin_tracking_depth">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">get_coroutine_origin_tracking_depth</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.get_coroutine_origin_tracking_depth" title="Link to this definition">¶</a></dt>
<dd><p>Get the current coroutine origin tracking depth, as set by
<a class="reference internal" href="#sys.set_coroutine_origin_tracking_depth" title="sys.set_coroutine_origin_tracking_depth"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_coroutine_origin_tracking_depth()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function has been added on a provisional basis (see <span class="target" id="index-14"></span><a class="pep reference external" href="https://peps.python.org/pep-0411/"><strong>PEP 411</strong></a>
for details.)  Use it only for debugging purposes.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.hash_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">hash_info</span></span><a class="headerlink" href="#sys.hash_info" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> giving parameters of the numeric hash
implementation.  For more details about hashing of numeric types, see
<a class="reference internal" href="stdtypes.html#numeric-hash"><span class="std std-ref">Hashing of numeric types</span></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.width">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">width</span></span><a class="headerlink" href="#sys.hash_info.width" title="Link to this definition">¶</a></dt>
<dd><p>The width in bits used for hash values</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.modulus">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">modulus</span></span><a class="headerlink" href="#sys.hash_info.modulus" title="Link to this definition">¶</a></dt>
<dd><p>The prime modulus P used for numeric hash scheme</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.inf">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">inf</span></span><a class="headerlink" href="#sys.hash_info.inf" title="Link to this definition">¶</a></dt>
<dd><p>The hash value returned for a positive infinity</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.nan">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">nan</span></span><a class="headerlink" href="#sys.hash_info.nan" title="Link to this definition">¶</a></dt>
<dd><p>(This attribute is no longer used)</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.imag">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">imag</span></span><a class="headerlink" href="#sys.hash_info.imag" title="Link to this definition">¶</a></dt>
<dd><p>The multiplier used for the imaginary part of a complex number</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.algorithm">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">algorithm</span></span><a class="headerlink" href="#sys.hash_info.algorithm" title="Link to this definition">¶</a></dt>
<dd><p>The name of the algorithm for hashing of str, bytes, and memoryview</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.hash_bits">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">hash_bits</span></span><a class="headerlink" href="#sys.hash_info.hash_bits" title="Link to this definition">¶</a></dt>
<dd><p>The internal output size of the hash algorithm</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.hash_info.seed_bits">
<span class="sig-prename descclassname"><span class="pre">hash_info.</span></span><span class="sig-name descname"><span class="pre">seed_bits</span></span><a class="headerlink" href="#sys.hash_info.seed_bits" title="Link to this definition">¶</a></dt>
<dd><p>The size of the seed key of the hash algorithm</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added <em>algorithm</em>, <em>hash_bits</em> and <em>seed_bits</em></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.hexversion">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">hexversion</span></span><a class="headerlink" href="#sys.hexversion" title="Link to this definition">¶</a></dt>
<dd><p>The version number encoded as a single integer.  This is guaranteed to increase
with each version, including proper support for non-production releases.  For
example, to test that the Python interpreter is at least version 1.5.2, use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">sys</span><span class="o">.</span><span class="n">hexversion</span> <span class="o">&gt;=</span> <span class="mh">0x010502F0</span><span class="p">:</span>
    <span class="c1"># use some advanced feature</span>
    <span class="o">...</span>
<span class="k">else</span><span class="p">:</span>
    <span class="c1"># use an alternative implementation or warn the user</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>This is called <code class="docutils literal notranslate"><span class="pre">hexversion</span></code> since it only really looks meaningful when viewed
as the result of passing it to the built-in <a class="reference internal" href="functions.html#hex" title="hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">hex()</span></code></a> function.  The
<a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a>  <a class="reference internal" href="#sys.version_info" title="sys.version_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version_info</span></code></a> may be used for a more
human-friendly encoding of the same information.</p>
<p>More details of <code class="docutils literal notranslate"><span class="pre">hexversion</span></code> can be found at <a class="reference internal" href="../c-api/apiabiversion.html#apiabiversion"><span class="std std-ref">API and ABI Versioning</span></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.implementation">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">implementation</span></span><a class="headerlink" href="#sys.implementation" title="Link to this definition">¶</a></dt>
<dd><p>An object containing information about the implementation of the
currently running Python interpreter.  The following attributes are
required to exist in all Python implementations.</p>
<p><em>name</em> is the implementation’s identifier, e.g. <code class="docutils literal notranslate"><span class="pre">'cpython'</span></code>.  The actual
string is defined by the Python implementation, but it is guaranteed to be
lower case.</p>
<p><em>version</em> is a named tuple, in the same format as
<a class="reference internal" href="#sys.version_info" title="sys.version_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version_info</span></code></a>.  It represents the version of the Python
<em>implementation</em>.  This has a distinct meaning from the specific
version of the Python <em>language</em> to which the currently running
interpreter conforms, which <code class="docutils literal notranslate"><span class="pre">sys.version_info</span></code> represents.  For
example, for PyPy 1.8 <code class="docutils literal notranslate"><span class="pre">sys.implementation.version</span></code> might be
<code class="docutils literal notranslate"><span class="pre">sys.version_info(1,</span> <span class="pre">8,</span> <span class="pre">0,</span> <span class="pre">'final',</span> <span class="pre">0)</span></code>, whereas <code class="docutils literal notranslate"><span class="pre">sys.version_info</span></code>
would be <code class="docutils literal notranslate"><span class="pre">sys.version_info(2,</span> <span class="pre">7,</span> <span class="pre">2,</span> <span class="pre">'final',</span> <span class="pre">0)</span></code>.  For CPython they
are the same value, since it is the reference implementation.</p>
<p><em>hexversion</em> is the implementation version in hexadecimal format, like
<a class="reference internal" href="#sys.hexversion" title="sys.hexversion"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.hexversion</span></code></a>.</p>
<p><em>cache_tag</em> is the tag used by the import machinery in the filenames of
cached modules.  By convention, it would be a composite of the
implementation’s name and version, like <code class="docutils literal notranslate"><span class="pre">'cpython-33'</span></code>.  However, a
Python implementation may use some other value if appropriate.  If
<code class="docutils literal notranslate"><span class="pre">cache_tag</span></code> is set to <code class="docutils literal notranslate"><span class="pre">None</span></code>, it indicates that module caching should
be disabled.</p>
<p><a class="reference internal" href="#sys.implementation" title="sys.implementation"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.implementation</span></code></a> may contain additional attributes specific to
the Python implementation.  These non-standard attributes must start with
an underscore, and are not described here.  Regardless of its contents,
<a class="reference internal" href="#sys.implementation" title="sys.implementation"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.implementation</span></code></a> will not change during a run of the interpreter,
nor between implementation versions.  (It may change between Python
language versions, however.)  See <span class="target" id="index-15"></span><a class="pep reference external" href="https://peps.python.org/pep-0421/"><strong>PEP 421</strong></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The addition of new required attributes must go through the normal PEP
process. See <span class="target" id="index-16"></span><a class="pep reference external" href="https://peps.python.org/pep-0421/"><strong>PEP 421</strong></a> for more information.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.int_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">int_info</span></span><a class="headerlink" href="#sys.int_info" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> that holds information about Python’s internal
representation of integers.  The attributes are read only.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sys.int_info.bits_per_digit">
<span class="sig-prename descclassname"><span class="pre">int_info.</span></span><span class="sig-name descname"><span class="pre">bits_per_digit</span></span><a class="headerlink" href="#sys.int_info.bits_per_digit" title="Link to this definition">¶</a></dt>
<dd><p>The number of bits held in each digit.
Python integers are stored internally in base <code class="docutils literal notranslate"><span class="pre">2**int_info.bits_per_digit</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.int_info.sizeof_digit">
<span class="sig-prename descclassname"><span class="pre">int_info.</span></span><span class="sig-name descname"><span class="pre">sizeof_digit</span></span><a class="headerlink" href="#sys.int_info.sizeof_digit" title="Link to this definition">¶</a></dt>
<dd><p>The size in bytes of the C type used to represent a digit.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.int_info.default_max_str_digits">
<span class="sig-prename descclassname"><span class="pre">int_info.</span></span><span class="sig-name descname"><span class="pre">default_max_str_digits</span></span><a class="headerlink" href="#sys.int_info.default_max_str_digits" title="Link to this definition">¶</a></dt>
<dd><p>The default value for <a class="reference internal" href="#sys.get_int_max_str_digits" title="sys.get_int_max_str_digits"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.get_int_max_str_digits()</span></code></a>
when it is not otherwise explicitly configured.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.int_info.str_digits_check_threshold">
<span class="sig-prename descclassname"><span class="pre">int_info.</span></span><span class="sig-name descname"><span class="pre">str_digits_check_threshold</span></span><a class="headerlink" href="#sys.int_info.str_digits_check_threshold" title="Link to this definition">¶</a></dt>
<dd><p>The minimum non-zero value for <a class="reference internal" href="#sys.set_int_max_str_digits" title="sys.set_int_max_str_digits"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.set_int_max_str_digits()</span></code></a>,
<span class="target" id="index-17"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONINTMAXSTRDIGITS"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONINTMAXSTRDIGITS</span></code></a>, or <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span> <span class="pre">int_max_str_digits</span></code></a>.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added <a class="reference internal" href="#sys.int_info.default_max_str_digits" title="sys.int_info.default_max_str_digits"><code class="xref py py-attr docutils literal notranslate"><span class="pre">default_max_str_digits</span></code></a> and
<a class="reference internal" href="#sys.int_info.str_digits_check_threshold" title="sys.int_info.str_digits_check_threshold"><code class="xref py py-attr docutils literal notranslate"><span class="pre">str_digits_check_threshold</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.__interactivehook__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__interactivehook__</span></span><a class="headerlink" href="#sys.__interactivehook__" title="Link to this definition">¶</a></dt>
<dd><p>When this attribute exists, its value is automatically called (with no
arguments) when the interpreter is launched in <a class="reference internal" href="../tutorial/interpreter.html#tut-interactive"><span class="std std-ref">interactive mode</span></a>.  This is done after the <span class="target" id="index-18"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSTARTUP"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONSTARTUP</span></code></a> file is
read, so that you can set this hook there.  The <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module
<a class="reference internal" href="site.html#rlcompleter-config"><span class="std std-ref">sets this</span></a>.</p>
<p class="audit-hook"><p>Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">cpython.run_interactivehook</span></code> with the hook object as the argument when
the hook is called on startup.</p>
</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.intern">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">intern</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.intern" title="Link to this definition">¶</a></dt>
<dd><p>Enter <em>string</em> in the table of “interned” strings and return the interned string
– which is <em>string</em> itself or a copy. Interning strings is useful to gain a
little performance on dictionary lookup – if the keys in a dictionary are
interned, and the lookup key is interned, the key comparisons (after hashing)
can be done by a pointer compare instead of a string compare.  Normally, the
names used in Python programs are automatically interned, and the dictionaries
used to hold module, class or instance attributes have interned keys.</p>
<p>Interned strings are not immortal; you must keep a reference to the return
value of <a class="reference internal" href="#sys.intern" title="sys.intern"><code class="xref py py-func docutils literal notranslate"><span class="pre">intern()</span></code></a> around to benefit from it.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.is_finalizing">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">is_finalizing</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.is_finalizing" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if the Python interpreter is
<a class="reference internal" href="../glossary.html#term-interpreter-shutdown"><span class="xref std std-term">shutting down</span></a>, <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> otherwise.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.last_exc">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">last_exc</span></span><a class="headerlink" href="#sys.last_exc" title="Link to this definition">¶</a></dt>
<dd><p>This variable is not always defined; it is set to the exception instance
when an exception is not handled and the interpreter prints an error message
and a stack traceback.  Its intended use is to allow an interactive user to
import a debugger module and engage in post-mortem debugging without having
to re-execute the command that caused the error.  (Typical use is
<code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">pdb;</span> <span class="pre">pdb.pm()</span></code> to enter the post-mortem debugger; see <a class="reference internal" href="pdb.html#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a>
module for more information.)</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.last_type">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">last_type</span></span><a class="headerlink" href="#sys.last_type" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.last_value">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">last_value</span></span><a class="headerlink" href="#sys.last_value" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.last_traceback">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">last_traceback</span></span><a class="headerlink" href="#sys.last_traceback" title="Link to this definition">¶</a></dt>
<dd><p>These three variables are deprecated; use <a class="reference internal" href="#sys.last_exc" title="sys.last_exc"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.last_exc</span></code></a> instead.
They hold the legacy representation of <code class="docutils literal notranslate"><span class="pre">sys.last_exc</span></code>, as returned
from <a class="reference internal" href="#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">exc_info()</span></code></a> above.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.maxsize">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">maxsize</span></span><a class="headerlink" href="#sys.maxsize" title="Link to this definition">¶</a></dt>
<dd><p>An integer giving the maximum value a variable of type <a class="reference internal" href="../c-api/intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a> can
take.  It’s usually <code class="docutils literal notranslate"><span class="pre">2**31</span> <span class="pre">-</span> <span class="pre">1</span></code> on a 32-bit platform and <code class="docutils literal notranslate"><span class="pre">2**63</span> <span class="pre">-</span> <span class="pre">1</span></code> on a
64-bit platform.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.maxunicode">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">maxunicode</span></span><a class="headerlink" href="#sys.maxunicode" title="Link to this definition">¶</a></dt>
<dd><p>An integer giving the value of the largest Unicode code point,
i.e. <code class="docutils literal notranslate"><span class="pre">1114111</span></code> (<code class="docutils literal notranslate"><span class="pre">0x10FFFF</span></code> in hexadecimal).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Before <span class="target" id="index-19"></span><a class="pep reference external" href="https://peps.python.org/pep-0393/"><strong>PEP 393</strong></a>, <code class="docutils literal notranslate"><span class="pre">sys.maxunicode</span></code> used to be either <code class="docutils literal notranslate"><span class="pre">0xFFFF</span></code>
or <code class="docutils literal notranslate"><span class="pre">0x10FFFF</span></code>, depending on the configuration option that specified
whether Unicode characters were stored as UCS-2 or UCS-4.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.meta_path">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">meta_path</span></span><a class="headerlink" href="#sys.meta_path" title="Link to this definition">¶</a></dt>
<dd><p>A list of <a class="reference internal" href="../glossary.html#term-meta-path-finder"><span class="xref std std-term">meta path finder</span></a> objects that have their
<a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder.find_spec" title="importlib.abc.MetaPathFinder.find_spec"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find_spec()</span></code></a> methods called to see if one
of the objects can find the module to be imported. By default, it holds entries
that implement Python’s default import semantics. The
<a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder.find_spec" title="importlib.abc.MetaPathFinder.find_spec"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find_spec()</span></code></a> method is called with at
least the absolute name of the module being imported. If the module to be
imported is contained in a package, then the parent package’s <a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a>
attribute is passed in as a second argument. The method returns a
<a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">module spec</span></a>, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the module cannot be found.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder" title="importlib.abc.MetaPathFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.abc.MetaPathFinder</span></code></a></dt><dd><p>The abstract base class defining the interface of finder objects on
<a class="reference internal" href="#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">meta_path</span></code></a>.</p>
</dd>
<dt><a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec</span></code></a></dt><dd><p>The concrete class which
<a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder.find_spec" title="importlib.abc.MetaPathFinder.find_spec"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find_spec()</span></code></a> should return
instances of.</p>
</dd>
</dl>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><a class="reference internal" href="../glossary.html#term-module-spec"><span class="xref std std-term">Module specs</span></a> were introduced in Python 3.4, by
<span class="target" id="index-20"></span><a class="pep reference external" href="https://peps.python.org/pep-0451/"><strong>PEP 451</strong></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Removed the fallback that looked for a <code class="xref py py-meth docutils literal notranslate"><span class="pre">find_module()</span></code> method
if a <a class="reference internal" href="#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">meta_path</span></code></a> entry didn’t have a
<a class="reference internal" href="importlib.html#importlib.abc.MetaPathFinder.find_spec" title="importlib.abc.MetaPathFinder.find_spec"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find_spec()</span></code></a> method.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.modules">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">modules</span></span><a class="headerlink" href="#sys.modules" title="Link to this definition">¶</a></dt>
<dd><p>This is a dictionary that maps module names to modules which have already been
loaded.  This can be manipulated to force reloading of modules and other tricks.
However, replacing the dictionary will not necessarily work as expected and
deleting essential items from the dictionary may cause Python to fail.  If
you want to iterate over this global dictionary always use
<code class="docutils literal notranslate"><span class="pre">sys.modules.copy()</span></code> or <code class="docutils literal notranslate"><span class="pre">tuple(sys.modules)</span></code> to avoid exceptions as its
size may change during iteration as a side effect of code or activity in
other threads.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.orig_argv">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">orig_argv</span></span><a class="headerlink" href="#sys.orig_argv" title="Link to this definition">¶</a></dt>
<dd><p>The list of the original command line arguments passed to the Python
executable.</p>
<p>The elements of <a class="reference internal" href="#sys.orig_argv" title="sys.orig_argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.orig_argv</span></code></a> are the arguments to the Python interpreter,
while the elements of <a class="reference internal" href="#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a> are the arguments to the user’s program.
Arguments consumed by the interpreter itself will be present in <a class="reference internal" href="#sys.orig_argv" title="sys.orig_argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.orig_argv</span></code></a>
and missing from <a class="reference internal" href="#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.path">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">path</span></span><a class="headerlink" href="#sys.path" title="Link to this definition">¶</a></dt>
<dd><p id="index-21">A list of strings that specifies the search path for modules. Initialized from
the environment variable <span class="target" id="index-22"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a>, plus an installation-dependent
default.</p>
<p>By default, as initialized upon program startup, a potentially unsafe path
is prepended to <a class="reference internal" href="#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> (<em>before</em> the entries inserted as a result
of <span class="target" id="index-23"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a>):</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">module</span></code> command line: prepend the current working
directory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">script.py</span></code> command line: prepend the script’s directory.
If it’s a symbolic link, resolve symbolic links.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-c</span> <span class="pre">code</span></code> and <code class="docutils literal notranslate"><span class="pre">python</span></code> (REPL) command lines: prepend an empty
string, which means the current working directory.</p></li>
</ul>
<p>To not prepend this potentially unsafe path, use the <a class="reference internal" href="../using/cmdline.html#cmdoption-P"><code class="xref std std-option docutils literal notranslate"><span class="pre">-P</span></code></a> command
line option or the <span class="target" id="index-24"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSAFEPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONSAFEPATH</span></code></a> environment variable.</p>
<p>A program is free to modify this list for its own purposes.  Only strings
should be added to <a class="reference internal" href="#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>; all other data types are
ignored during import.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p>Module <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> This describes how to use .pth files to
extend <a class="reference internal" href="#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
</ul>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.path_hooks">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">path_hooks</span></span><a class="headerlink" href="#sys.path_hooks" title="Link to this definition">¶</a></dt>
<dd><p>A list of callables that take a path argument to try to create a
<a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> for the path. If a finder can be created, it is to be
returned by the callable, else raise <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a>.</p>
<p>Originally specified in <span class="target" id="index-25"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.path_importer_cache">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">path_importer_cache</span></span><a class="headerlink" href="#sys.path_importer_cache" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary acting as a cache for <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> objects. The keys are
paths that have been passed to <a class="reference internal" href="#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> and the values are
the finders that are found. If a path is a valid file system path but no
finder is found on <a class="reference internal" href="#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> then <code class="docutils literal notranslate"><span class="pre">None</span></code> is
stored.</p>
<p>Originally specified in <span class="target" id="index-26"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.platform">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">platform</span></span><a class="headerlink" href="#sys.platform" title="Link to this definition">¶</a></dt>
<dd><p>This string contains a platform identifier that can be used to append
platform-specific components to <a class="reference internal" href="#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>, for instance.</p>
<p>For Unix systems, except on Linux and AIX, this is the lowercased OS name as
returned by <code class="docutils literal notranslate"><span class="pre">uname</span> <span class="pre">-s</span></code> with the first part of the version as returned by
<code class="docutils literal notranslate"><span class="pre">uname</span> <span class="pre">-r</span></code> appended, e.g. <code class="docutils literal notranslate"><span class="pre">'sunos5'</span></code> or <code class="docutils literal notranslate"><span class="pre">'freebsd8'</span></code>, <em>at the time
when Python was built</em>.  Unless you want to test for a specific system
version, it is therefore recommended to use the following idiom:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">sys</span><span class="o">.</span><span class="n">platform</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;freebsd&#39;</span><span class="p">):</span>
    <span class="c1"># FreeBSD-specific code here...</span>
<span class="k">elif</span> <span class="n">sys</span><span class="o">.</span><span class="n">platform</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;linux&#39;</span><span class="p">):</span>
    <span class="c1"># Linux-specific code here...</span>
<span class="k">elif</span> <span class="n">sys</span><span class="o">.</span><span class="n">platform</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;aix&#39;</span><span class="p">):</span>
    <span class="c1"># AIX-specific code here...</span>
</pre></div>
</div>
<p>For other systems, the values are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>System</p></th>
<th class="head"><p><code class="docutils literal notranslate"><span class="pre">platform</span></code> value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>AIX</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'aix'</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Emscripten</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'emscripten'</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Linux</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'linux'</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>WASI</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'wasi'</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Windows</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'win32'</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Windows/Cygwin</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'cygwin'</span></code></p></td>
</tr>
<tr class="row-even"><td><p>macOS</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'darwin'</span></code></p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>On Linux, <a class="reference internal" href="#sys.platform" title="sys.platform"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platform</span></code></a> doesn’t contain the major version anymore.
It is always <code class="docutils literal notranslate"><span class="pre">'linux'</span></code>, instead of <code class="docutils literal notranslate"><span class="pre">'linux2'</span></code> or <code class="docutils literal notranslate"><span class="pre">'linux3'</span></code>.  Since
older Python versions include the version number, it is recommended to
always use the <code class="docutils literal notranslate"><span class="pre">startswith</span></code> idiom presented above.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>On AIX, <a class="reference internal" href="#sys.platform" title="sys.platform"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platform</span></code></a> doesn’t contain the major version anymore.
It is always <code class="docutils literal notranslate"><span class="pre">'aix'</span></code>, instead of <code class="docutils literal notranslate"><span class="pre">'aix5'</span></code> or <code class="docutils literal notranslate"><span class="pre">'aix7'</span></code>.  Since
older Python versions include the version number, it is recommended to
always use the <code class="docutils literal notranslate"><span class="pre">startswith</span></code> idiom presented above.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="os.html#os.name" title="os.name"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.name</span></code></a> has a coarser granularity.  <a class="reference internal" href="os.html#os.uname" title="os.uname"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.uname()</span></code></a> gives
system-dependent version information.</p>
<p>The <a class="reference internal" href="platform.html#module-platform" title="platform: Retrieves as much platform identifying data as possible."><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code></a> module provides detailed checks for the
system’s identity.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.platlibdir">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">platlibdir</span></span><a class="headerlink" href="#sys.platlibdir" title="Link to this definition">¶</a></dt>
<dd><p>Name of the platform-specific library directory. It is used to build the
path of standard library and the paths of installed extension modules.</p>
<p>It is equal to <code class="docutils literal notranslate"><span class="pre">&quot;lib&quot;</span></code> on most platforms. On Fedora and SuSE, it is equal
to <code class="docutils literal notranslate"><span class="pre">&quot;lib64&quot;</span></code> on 64-bit platforms which gives the following <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>
paths (where <code class="docutils literal notranslate"><span class="pre">X.Y</span></code> is the Python <code class="docutils literal notranslate"><span class="pre">major.minor</span></code> version):</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">/usr/lib64/pythonX.Y/</span></code>:
Standard library (like <code class="docutils literal notranslate"><span class="pre">os.py</span></code> of the <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/usr/lib64/pythonX.Y/lib-dynload/</span></code>:
C extension modules of the standard library (like the <a class="reference internal" href="errno.html#module-errno" title="errno: Standard errno system symbols."><code class="xref py py-mod docutils literal notranslate"><span class="pre">errno</span></code></a> module,
the exact filename is platform specific)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/usr/lib/pythonX.Y/site-packages/</span></code> (always use <code class="docutils literal notranslate"><span class="pre">lib</span></code>, not
<a class="reference internal" href="#sys.platlibdir" title="sys.platlibdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platlibdir</span></code></a>): Third-party modules</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/usr/lib64/pythonX.Y/site-packages/</span></code>:
C extension modules of third-party packages</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.prefix">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">prefix</span></span><a class="headerlink" href="#sys.prefix" title="Link to this definition">¶</a></dt>
<dd><p>A string giving the site-specific directory prefix where the platform
independent Python files are installed; on Unix, the default is
<code class="file docutils literal notranslate"><span class="pre">/usr/local</span></code>. This can be set at build time with the <a class="reference internal" href="../using/configure.html#cmdoption-prefix"><code class="xref std std-option docutils literal notranslate"><span class="pre">--prefix</span></code></a>
argument to the <strong class="program">configure</strong> script.  See
<a class="reference internal" href="sysconfig.html#installation-paths"><span class="std std-ref">Installation paths</span></a> for derived paths.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If a <a class="reference internal" href="venv.html#venv-def"><span class="std std-ref">virtual environment</span></a> is in effect, this
value will be changed in <code class="docutils literal notranslate"><span class="pre">site.py</span></code> to point to the virtual
environment. The value for the Python installation will still be
available, via <a class="reference internal" href="#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">base_prefix</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.ps1">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">ps1</span></span><a class="headerlink" href="#sys.ps1" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.ps2">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">ps2</span></span><a class="headerlink" href="#sys.ps2" title="Link to this definition">¶</a></dt>
<dd><p id="index-27">Strings specifying the primary and secondary prompt of the interpreter.  These
are only defined if the interpreter is in interactive mode.  Their initial
values in this case are <code class="docutils literal notranslate"><span class="pre">'&gt;&gt;&gt;</span> <span class="pre">'</span></code> and <code class="docutils literal notranslate"><span class="pre">'...</span> <span class="pre">'</span></code>.  If a non-string object is
assigned to either variable, its <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-func docutils literal notranslate"><span class="pre">str()</span></code></a> is re-evaluated each time the
interpreter prepares to read a new interactive command; this can be used to
implement a dynamic prompt.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.setdlopenflags">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">setdlopenflags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.setdlopenflags" title="Link to this definition">¶</a></dt>
<dd><p>Set the flags used by the interpreter for <code class="xref c c-func docutils literal notranslate"><span class="pre">dlopen()</span></code> calls, such as when
the interpreter loads extension modules.  Among other things, this will enable a
lazy resolving of symbols when importing a module, if called as
<code class="docutils literal notranslate"><span class="pre">sys.setdlopenflags(0)</span></code>.  To share symbols across extension modules, call as
<code class="docutils literal notranslate"><span class="pre">sys.setdlopenflags(os.RTLD_GLOBAL)</span></code>.  Symbolic names for the flag values
can be found in the <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module (<code class="samp docutils literal notranslate"><span class="pre">RTLD_</span><em><span class="pre">xxx</span></em></code> constants, e.g.
<a class="reference internal" href="os.html#os.RTLD_LAZY" title="os.RTLD_LAZY"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.RTLD_LAZY</span></code></a>).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.set_int_max_str_digits">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">set_int_max_str_digits</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">maxdigits</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.set_int_max_str_digits" title="Link to this definition">¶</a></dt>
<dd><p>Set the <a class="reference internal" href="stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string conversion length limitation</span></a> used by this interpreter. See also
<a class="reference internal" href="#sys.get_int_max_str_digits" title="sys.get_int_max_str_digits"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_int_max_str_digits()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.setprofile">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">setprofile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">profilefunc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.setprofile" title="Link to this definition">¶</a></dt>
<dd><p id="index-28">Set the system’s profile function, which allows you to implement a Python source
code profiler in Python.  See chapter <a class="reference internal" href="profile.html#profile"><span class="std std-ref">The Python Profilers</span></a> for more information on the
Python profiler.  The system’s profile function is called similarly to the
system’s trace function (see <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a>), but it is called with different events,
for example it isn’t called for each executed line of code (only on call and return,
but the return event is reported even when an exception has been set). The function is
thread-specific, but there is no way for the profiler to know about context switches between
threads, so it does not make sense to use this in the presence of multiple threads. Also,
its return value is not used, so it can simply return <code class="docutils literal notranslate"><span class="pre">None</span></code>.  Error in the profile
function will cause itself unset.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The same tracing mechanism is used for <code class="xref py py-func docutils literal notranslate"><span class="pre">setprofile()</span></code> as <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a>.
To trace calls with <code class="xref py py-func docutils literal notranslate"><span class="pre">setprofile()</span></code> inside a tracing function
(e.g. in a debugger breakpoint), see <a class="reference internal" href="#sys.call_tracing" title="sys.call_tracing"><code class="xref py py-func docutils literal notranslate"><span class="pre">call_tracing()</span></code></a>.</p>
</div>
<p>Profile functions should have three arguments: <em>frame</em>, <em>event</em>, and
<em>arg</em>. <em>frame</em> is the current stack frame.  <em>event</em> is a string: <code class="docutils literal notranslate"><span class="pre">'call'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'return'</span></code>, <code class="docutils literal notranslate"><span class="pre">'c_call'</span></code>, <code class="docutils literal notranslate"><span class="pre">'c_return'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'c_exception'</span></code>. <em>arg</em> depends
on the event type.</p>
<p>The events have the following meaning:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">'call'</span></code></dt><dd><p>A function is called (or some other code block entered).  The
profile function is called; <em>arg</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'return'</span></code></dt><dd><p>A function (or other code block) is about to return.  The profile
function is called; <em>arg</em> is the value that will be returned, or <code class="docutils literal notranslate"><span class="pre">None</span></code>
if the event is caused by an exception being raised.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'c_call'</span></code></dt><dd><p>A C function is about to be called.  This may be an extension function or
a built-in.  <em>arg</em> is the C function object.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'c_return'</span></code></dt><dd><p>A C function has returned. <em>arg</em> is the C function object.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'c_exception'</span></code></dt><dd><p>A C function has raised an exception.  <em>arg</em> is the C function object.</p>
</dd>
</dl>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys.setprofile</span></code> with no arguments.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.setrecursionlimit">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">setrecursionlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.setrecursionlimit" title="Link to this definition">¶</a></dt>
<dd><p>Set the maximum depth of the Python interpreter stack to <em>limit</em>.  This limit
prevents infinite recursion from causing an overflow of the C stack and crashing
Python.</p>
<p>The highest possible limit is platform-dependent.  A user may need to set the
limit higher when they have a program that requires deep recursion and a platform
that supports a higher limit.  This should be done with care, because a too-high
limit can lead to a crash.</p>
<p>If the new limit is too low at the current recursion depth, a
<a class="reference internal" href="exceptions.html#RecursionError" title="RecursionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RecursionError</span></code></a> exception is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5.1: </span>A <a class="reference internal" href="exceptions.html#RecursionError" title="RecursionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RecursionError</span></code></a> exception is now raised if the new limit is too
low at the current recursion depth.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.setswitchinterval">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">setswitchinterval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interval</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.setswitchinterval" title="Link to this definition">¶</a></dt>
<dd><p>Set the interpreter’s thread switch interval (in seconds).  This floating-point
value determines the ideal duration of the “timeslices” allocated to
concurrently running Python threads.  Please note that the actual value
can be higher, especially if long-running internal functions or methods
are used.  Also, which thread becomes scheduled at the end of the interval
is the operating system’s decision.  The interpreter doesn’t have its
own scheduler.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.settrace">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">settrace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tracefunc</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.settrace" title="Link to this definition">¶</a></dt>
<dd><p id="index-29">Set the system’s trace function, which allows you to implement a Python
source code debugger in Python.  The function is thread-specific; for a
debugger to support multiple threads, it must register a trace function using
<a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a> for each thread being debugged or use <a class="reference internal" href="threading.html#threading.settrace" title="threading.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.settrace()</span></code></a>.</p>
<p>Trace functions should have three arguments: <em>frame</em>, <em>event</em>, and
<em>arg</em>. <em>frame</em> is the current stack frame.  <em>event</em> is a string: <code class="docutils literal notranslate"><span class="pre">'call'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'line'</span></code>, <code class="docutils literal notranslate"><span class="pre">'return'</span></code>, <code class="docutils literal notranslate"><span class="pre">'exception'</span></code> or <code class="docutils literal notranslate"><span class="pre">'opcode'</span></code>.  <em>arg</em> depends on
the event type.</p>
<p>The trace function is invoked (with <em>event</em> set to <code class="docutils literal notranslate"><span class="pre">'call'</span></code>) whenever a new
local scope is entered; it should return a reference to a local trace
function to be used for the new scope, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if the scope shouldn’t be
traced.</p>
<p>The local trace function should return a reference to itself, or to another
function which would then be used as the local trace function for the scope.</p>
<p>If there is any error occurred in the trace function, it will be unset, just
like <code class="docutils literal notranslate"><span class="pre">settrace(None)</span></code> is called.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Tracing is disabled while calling the trace function (e.g. a function set by
<code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code>). For recursive tracing see <a class="reference internal" href="#sys.call_tracing" title="sys.call_tracing"><code class="xref py py-func docutils literal notranslate"><span class="pre">call_tracing()</span></code></a>.</p>
</div>
<p>The events have the following meaning:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">'call'</span></code></dt><dd><p>A function is called (or some other code block entered).  The
global trace function is called; <em>arg</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>; the return value
specifies the local trace function.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'line'</span></code></dt><dd><p>The interpreter is about to execute a new line of code or re-execute the
condition of a loop.  The local trace function is called; <em>arg</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code>; the return value specifies the new local trace function.  See
<code class="file docutils literal notranslate"><span class="pre">Objects/lnotab_notes.txt</span></code> for a detailed explanation of how this
works.
Per-line events may be disabled for a frame by setting
<a class="reference internal" href="../reference/datamodel.html#frame.f_trace_lines" title="frame.f_trace_lines"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_lines</span></code></a> to <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a> on that
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'return'</span></code></dt><dd><p>A function (or other code block) is about to return.  The local trace
function is called; <em>arg</em> is the value that will be returned, or <code class="docutils literal notranslate"><span class="pre">None</span></code>
if the event is caused by an exception being raised.  The trace function’s
return value is ignored.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'exception'</span></code></dt><dd><p>An exception has occurred.  The local trace function is called; <em>arg</em> is a
tuple <code class="docutils literal notranslate"><span class="pre">(exception,</span> <span class="pre">value,</span> <span class="pre">traceback)</span></code>; the return value specifies the
new local trace function.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'opcode'</span></code></dt><dd><p>The interpreter is about to execute a new opcode (see <a class="reference internal" href="dis.html#module-dis" title="dis: Disassembler for Python bytecode."><code class="xref py py-mod docutils literal notranslate"><span class="pre">dis</span></code></a> for
opcode details).  The local trace function is called; <em>arg</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code>; the return value specifies the new local trace function.
Per-opcode events are not emitted by default: they must be explicitly
requested by setting <a class="reference internal" href="../reference/datamodel.html#frame.f_trace_opcodes" title="frame.f_trace_opcodes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_opcodes</span></code></a> to <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> on the
<a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame</span></a>.</p>
</dd>
</dl>
<p>Note that as an exception is propagated down the chain of callers, an
<code class="docutils literal notranslate"><span class="pre">'exception'</span></code> event is generated at each level.</p>
<p>For more fine-grained usage, it’s possible to set a trace function by
assigning <code class="docutils literal notranslate"><span class="pre">frame.f_trace</span> <span class="pre">=</span> <span class="pre">tracefunc</span></code> explicitly, rather than relying on
it being set indirectly via the return value from an already installed
trace function. This is also required for activating the trace function on
the current frame, which <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a> doesn’t do. Note that in order
for this to work, a global tracing function must have been installed
with <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a> in order to enable the runtime tracing machinery,
but it doesn’t need to be the same tracing function (e.g. it could be a
low overhead tracing function that simply returns <code class="docutils literal notranslate"><span class="pre">None</span></code> to disable
itself immediately on each frame).</p>
<p>For more information on code and frame objects, refer to <a class="reference internal" href="../reference/datamodel.html#types"><span class="std std-ref">The standard type hierarchy</span></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys.settrace</span></code> with no arguments.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> The <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a> function is intended only for implementing debuggers,
profilers, coverage tools and the like.  Its behavior is part of the
implementation platform, rather than part of the language definition, and
thus may not be available in all Python implementations.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><code class="docutils literal notranslate"><span class="pre">'opcode'</span></code> event type added; <a class="reference internal" href="../reference/datamodel.html#frame.f_trace_lines" title="frame.f_trace_lines"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_lines</span></code></a> and
<a class="reference internal" href="../reference/datamodel.html#frame.f_trace_opcodes" title="frame.f_trace_opcodes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_opcodes</span></code></a> attributes added to frames</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">'opcode'</span></code> event will only be emitted if <a class="reference internal" href="../reference/datamodel.html#frame.f_trace_opcodes" title="frame.f_trace_opcodes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_opcodes</span></code></a>
of at least one frame has been set to <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> before <a class="reference internal" href="#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">settrace()</span></code></a>
is called. This behavior will be changed back in 3.13 to be consistent with
previous versions.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.set_asyncgen_hooks">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">set_asyncgen_hooks</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="pre">[firstiter]</span> <span class="pre">[,</span> <span class="pre">finalizer]</span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.set_asyncgen_hooks" title="Link to this definition">¶</a></dt>
<dd><p>Accepts two optional keyword arguments which are callables that accept an
<a class="reference internal" href="../glossary.html#term-asynchronous-generator-iterator"><span class="xref std std-term">asynchronous generator iterator</span></a> as an argument. The <em>firstiter</em>
callable will be called when an asynchronous generator is iterated for the
first time. The <em>finalizer</em> will be called when an asynchronous generator
is about to be garbage collected.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys.set_asyncgen_hooks_firstiter</span></code> with no arguments.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">sys.set_asyncgen_hooks_finalizer</span></code> with no arguments.</p>
<p>Two auditing events are raised because the underlying API consists of two
calls, each of which must raise its own event.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>See <span class="target" id="index-30"></span><a class="pep reference external" href="https://peps.python.org/pep-0525/"><strong>PEP 525</strong></a> for more details, and for a reference example of a
<em>finalizer</em> method see the implementation of
<code class="docutils literal notranslate"><span class="pre">asyncio.Loop.shutdown_asyncgens</span></code> in
<a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/asyncio/base_events.py">Lib/asyncio/base_events.py</a></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function has been added on a provisional basis (see <span class="target" id="index-31"></span><a class="pep reference external" href="https://peps.python.org/pep-0411/"><strong>PEP 411</strong></a>
for details.)</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.set_coroutine_origin_tracking_depth">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">set_coroutine_origin_tracking_depth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">depth</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.set_coroutine_origin_tracking_depth" title="Link to this definition">¶</a></dt>
<dd><p>Allows enabling or disabling coroutine origin tracking. When
enabled, the <code class="docutils literal notranslate"><span class="pre">cr_origin</span></code> attribute on coroutine objects will
contain a tuple of (filename, line number, function name) tuples
describing the traceback where the coroutine object was created,
with the most recent call first. When disabled, <code class="docutils literal notranslate"><span class="pre">cr_origin</span></code> will
be None.</p>
<p>To enable, pass a <em>depth</em> value greater than zero; this sets the
number of frames whose information will be captured. To disable,
pass set <em>depth</em> to zero.</p>
<p>This setting is thread-specific.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This function has been added on a provisional basis (see <span class="target" id="index-32"></span><a class="pep reference external" href="https://peps.python.org/pep-0411/"><strong>PEP 411</strong></a>
for details.)  Use it only for debugging purposes.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.activate_stack_trampoline">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">activate_stack_trampoline</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">backend</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.activate_stack_trampoline" title="Link to this definition">¶</a></dt>
<dd><p>Activate the stack profiler trampoline <em>backend</em>.
The only supported backend is <code class="docutils literal notranslate"><span class="pre">&quot;perf&quot;</span></code>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><a class="reference internal" href="../howto/perf_profiling.html#perf-profiling"><span class="std std-ref">Python support for the Linux perf profiler</span></a></p></li>
<li><p><a class="reference external" href="https://perf.wiki.kernel.org">https://perf.wiki.kernel.org</a></p></li>
</ul>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.deactivate_stack_trampoline">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">deactivate_stack_trampoline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.deactivate_stack_trampoline" title="Link to this definition">¶</a></dt>
<dd><p>Deactivate the current stack profiler trampoline backend.</p>
<p>If no stack profiler is activated, this function has no effect.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.is_stack_trampoline_active">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">is_stack_trampoline_active</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys.is_stack_trampoline_active" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if a stack profiler trampoline is active.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys._enablelegacywindowsfsencoding">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_enablelegacywindowsfsencoding</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sys._enablelegacywindowsfsencoding" title="Link to this definition">¶</a></dt>
<dd><p>Changes the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a> to ‘mbcs’ and
‘replace’ respectively, for consistency with versions of Python prior to
3.6.</p>
<p>This is equivalent to defining the <span class="target" id="index-33"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSFSENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSFSENCODING</span></code></a>
environment variable before launching Python.</p>
<p>See also <a class="reference internal" href="#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencoding()</span></code></a> and
<a class="reference internal" href="#sys.getfilesystemencodeerrors" title="sys.getfilesystemencodeerrors"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencodeerrors()</span></code></a>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>See <span class="target" id="index-34"></span><a class="pep reference external" href="https://peps.python.org/pep-0529/"><strong>PEP 529</strong></a> for more details.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.stdin">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">stdin</span></span><a class="headerlink" href="#sys.stdin" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.stdout">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">stdout</span></span><a class="headerlink" href="#sys.stdout" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.stderr">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">stderr</span></span><a class="headerlink" href="#sys.stderr" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">File objects</span></a> used by the interpreter for standard
input, output and errors:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">stdin</span></code> is used for all interactive input (including calls to
<a class="reference internal" href="functions.html#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a>);</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">stdout</span></code> is used for the output of <a class="reference internal" href="functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> and <a class="reference internal" href="../glossary.html#term-expression"><span class="xref std std-term">expression</span></a>
statements and for the prompts of <a class="reference internal" href="functions.html#input" title="input"><code class="xref py py-func docutils literal notranslate"><span class="pre">input()</span></code></a>;</p></li>
<li><p>The interpreter’s own prompts and its error messages go to <code class="docutils literal notranslate"><span class="pre">stderr</span></code>.</p></li>
</ul>
<p>These streams are regular <a class="reference internal" href="../glossary.html#term-text-file"><span class="xref std std-term">text files</span></a> like those
returned by the <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function.  Their parameters are chosen as
follows:</p>
<ul>
<li><p>The encoding and error handling are is initialized from
<a class="reference internal" href="../c-api/init_config.html#c.PyConfig.stdio_encoding" title="PyConfig.stdio_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.stdio_encoding</span></code></a> and <a class="reference internal" href="../c-api/init_config.html#c.PyConfig.stdio_errors" title="PyConfig.stdio_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.stdio_errors</span></code></a>.</p>
<p>On Windows, UTF-8 is used for the console device.  Non-character
devices such as disk files and pipes use the system locale
encoding (i.e. the ANSI codepage).  Non-console character
devices such as NUL (i.e. where <code class="docutils literal notranslate"><span class="pre">isatty()</span></code> returns <code class="docutils literal notranslate"><span class="pre">True</span></code>) use the
value of the console input and output codepages at startup,
respectively for stdin and stdout/stderr. This defaults to the
system <a class="reference internal" href="../glossary.html#term-locale-encoding"><span class="xref std std-term">locale encoding</span></a> if the process is not initially attached
to a console.</p>
<p>The special behaviour of the console can be overridden
by setting the environment variable PYTHONLEGACYWINDOWSSTDIO
before starting Python. In that case, the console codepages are
used as for any other character device.</p>
<p>Under all platforms, you can override the character encoding by
setting the <span class="target" id="index-35"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONIOENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONIOENCODING</span></code></a> environment variable before
starting Python or by using the new <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> <code class="docutils literal notranslate"><span class="pre">utf8</span></code> command
line option and <span class="target" id="index-36"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUTF8"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUTF8</span></code></a> environment variable.  However,
for the Windows console, this only applies when
<span class="target" id="index-37"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSSTDIO"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSSTDIO</span></code></a> is also set.</p>
</li>
<li><p>When interactive, the <code class="docutils literal notranslate"><span class="pre">stdout</span></code> stream is line-buffered. Otherwise,
it is block-buffered like regular text files.  The <code class="docutils literal notranslate"><span class="pre">stderr</span></code> stream
is line-buffered in both cases.  You can make both streams unbuffered
by passing the <a class="reference internal" href="../using/cmdline.html#cmdoption-u"><code class="xref std std-option docutils literal notranslate"><span class="pre">-u</span></code></a> command-line option or setting the
<span class="target" id="index-38"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUNBUFFERED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUNBUFFERED</span></code></a> environment variable.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Non-interactive <code class="docutils literal notranslate"><span class="pre">stderr</span></code> is now line-buffered instead of fully
buffered.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>To write or read binary data from/to the standard streams, use the
underlying binary <a class="reference internal" href="io.html#io.TextIOBase.buffer" title="io.TextIOBase.buffer"><code class="xref py py-data docutils literal notranslate"><span class="pre">buffer</span></code></a> object.  For example, to
write bytes to <a class="reference internal" href="#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">stdout</span></code></a>, use <code class="docutils literal notranslate"><span class="pre">sys.stdout.buffer.write(b'abc')</span></code>.</p>
<p>However, if you are writing a library (and do not control in which
context its code will be executed), be aware that the standard streams
may be replaced with file-like objects like <a class="reference internal" href="io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.StringIO</span></code></a> which
do not support the <code class="xref py py-attr docutils literal notranslate"><span class="pre">buffer</span></code> attribute.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.__stdin__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__stdin__</span></span><a class="headerlink" href="#sys.__stdin__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.__stdout__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__stdout__</span></span><a class="headerlink" href="#sys.__stdout__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sys.__stderr__">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">__stderr__</span></span><a class="headerlink" href="#sys.__stderr__" title="Link to this definition">¶</a></dt>
<dd><p>These objects contain the original values of <code class="docutils literal notranslate"><span class="pre">stdin</span></code>, <code class="docutils literal notranslate"><span class="pre">stderr</span></code> and
<code class="docutils literal notranslate"><span class="pre">stdout</span></code> at the start of the program.  They are used during finalization,
and could be useful to print to the actual standard stream no matter if the
<code class="docutils literal notranslate"><span class="pre">sys.std*</span></code> object has been redirected.</p>
<p>It can also be used to restore the actual files to known working file objects
in case they have been overwritten with a broken object.  However, the
preferred way to do this is to explicitly save the previous stream before
replacing it, and restore the saved object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Under some conditions <code class="docutils literal notranslate"><span class="pre">stdin</span></code>, <code class="docutils literal notranslate"><span class="pre">stdout</span></code> and <code class="docutils literal notranslate"><span class="pre">stderr</span></code> as well as the
original values <code class="docutils literal notranslate"><span class="pre">__stdin__</span></code>, <code class="docutils literal notranslate"><span class="pre">__stdout__</span></code> and <code class="docutils literal notranslate"><span class="pre">__stderr__</span></code> can be
<code class="docutils literal notranslate"><span class="pre">None</span></code>. It is usually the case for Windows GUI apps that aren’t connected
to a console and Python apps started with <strong class="program">pythonw</strong>.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.stdlib_module_names">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">stdlib_module_names</span></span><a class="headerlink" href="#sys.stdlib_module_names" title="Link to this definition">¶</a></dt>
<dd><p>A frozenset of strings containing the names of standard library modules.</p>
<p>It is the same on all platforms. Modules which are not available on
some platforms and modules disabled at Python build are also listed.
All module kinds are listed: pure Python, built-in, frozen and extension
modules. Test modules are excluded.</p>
<p>For packages, only the main package is listed: sub-packages and sub-modules
are not listed. For example, the <code class="docutils literal notranslate"><span class="pre">email</span></code> package is listed, but the
<code class="docutils literal notranslate"><span class="pre">email.mime</span></code> sub-package and the <code class="docutils literal notranslate"><span class="pre">email.message</span></code> sub-module are not
listed.</p>
<p>See also the <a class="reference internal" href="#sys.builtin_module_names" title="sys.builtin_module_names"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.builtin_module_names</span></code></a> list.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.thread_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">thread_info</span></span><a class="headerlink" href="#sys.thread_info" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named tuple</span></a> holding information about the thread
implementation.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sys.thread_info.name">
<span class="sig-prename descclassname"><span class="pre">thread_info.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#sys.thread_info.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the thread implementation:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;nt&quot;</span></code>: Windows threads</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;pthread&quot;</span></code>: POSIX threads</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;pthread-stubs&quot;</span></code>: stub POSIX threads
(on WebAssembly platforms without threading support)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;solaris&quot;</span></code>: Solaris threads</p></li>
</ul>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.thread_info.lock">
<span class="sig-prename descclassname"><span class="pre">thread_info.</span></span><span class="sig-name descname"><span class="pre">lock</span></span><a class="headerlink" href="#sys.thread_info.lock" title="Link to this definition">¶</a></dt>
<dd><p>The name of the lock implementation:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;semaphore&quot;</span></code>: a lock uses a semaphore</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">&quot;mutex+cond&quot;</span></code>: a lock uses a mutex and a condition variable</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">None</span></code> if this information is unknown</p></li>
</ul>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sys.thread_info.version">
<span class="sig-prename descclassname"><span class="pre">thread_info.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#sys.thread_info.version" title="Link to this definition">¶</a></dt>
<dd><p>The name and version of the thread library.
It is a string, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if this information is unknown.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.tracebacklimit">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">tracebacklimit</span></span><a class="headerlink" href="#sys.tracebacklimit" title="Link to this definition">¶</a></dt>
<dd><p>When this variable is set to an integer value, it determines the maximum number
of levels of traceback information printed when an unhandled exception occurs.
The default is <code class="docutils literal notranslate"><span class="pre">1000</span></code>.  When set to <code class="docutils literal notranslate"><span class="pre">0</span></code> or less, all traceback information
is suppressed and only the exception type and value are printed.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sys.unraisablehook">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">unraisablehook</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unraisable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sys.unraisablehook" title="Link to this definition">¶</a></dt>
<dd><p>Handle an unraisable exception.</p>
<p>Called when an exception has occurred but there is no way for Python to
handle it. For example, when a destructor raises an exception or during
garbage collection (<a class="reference internal" href="gc.html#gc.collect" title="gc.collect"><code class="xref py py-func docutils literal notranslate"><span class="pre">gc.collect()</span></code></a>).</p>
<p>The <em>unraisable</em> argument has the following attributes:</p>
<ul class="simple">
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_type</span></code>: Exception type.</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_value</span></code>: Exception value, can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_traceback</span></code>: Exception traceback, can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">err_msg</span></code>: Error message, can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
<li><p><code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code>: Object causing the exception, can be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p></li>
</ul>
<p>The default hook formats <code class="xref py py-attr docutils literal notranslate"><span class="pre">err_msg</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code> as:
<code class="docutils literal notranslate"><span class="pre">f'{err_msg}:</span> <span class="pre">{object!r}'</span></code>; use “Exception ignored in” error message
if <code class="xref py py-attr docutils literal notranslate"><span class="pre">err_msg</span></code> is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p><a class="reference internal" href="#sys.unraisablehook" title="sys.unraisablehook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.unraisablehook()</span></code></a> can be overridden to control how unraisable
exceptions are handled.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#sys.excepthook" title="sys.excepthook"><code class="xref py py-func docutils literal notranslate"><span class="pre">excepthook()</span></code></a> which handles uncaught exceptions.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Storing <code class="xref py py-attr docutils literal notranslate"><span class="pre">exc_value</span></code> using a custom hook can create a reference cycle.
It should be cleared explicitly to break the reference cycle when the
exception is no longer needed.</p>
<p>Storing <code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code> using a custom hook can resurrect it if it is set to an
object which is being finalized. Avoid storing <code class="xref py py-attr docutils literal notranslate"><span class="pre">object</span></code> after the custom
hook completes to avoid resurrecting objects.</p>
</div>
<p class="audit-hook"><p>Raise an auditing event <code class="docutils literal notranslate"><span class="pre">sys.unraisablehook</span></code> with arguments
<em>hook</em>, <em>unraisable</em> when an exception that cannot be handled occurs.
The <em>unraisable</em> object is the same as what will be passed to the hook.
If no hook has been set, <em>hook</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.version">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#sys.version" title="Link to this definition">¶</a></dt>
<dd><p>A string containing the version number of the Python interpreter plus additional
information on the build number and compiler used.  This string is displayed
when the interactive interpreter is started.  Do not extract version information
out of it, rather, use <a class="reference internal" href="#sys.version_info" title="sys.version_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">version_info</span></code></a> and the functions provided by the
<a class="reference internal" href="platform.html#module-platform" title="platform: Retrieves as much platform identifying data as possible."><code class="xref py py-mod docutils literal notranslate"><span class="pre">platform</span></code></a> module.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.api_version">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">api_version</span></span><a class="headerlink" href="#sys.api_version" title="Link to this definition">¶</a></dt>
<dd><p>The C API version for this interpreter.  Programmers may find this useful when
debugging version conflicts between Python and extension modules.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.version_info">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">version_info</span></span><a class="headerlink" href="#sys.version_info" title="Link to this definition">¶</a></dt>
<dd><p>A tuple containing the five components of the version number: <em>major</em>, <em>minor</em>,
<em>micro</em>, <em>releaselevel</em>, and <em>serial</em>.  All values except <em>releaselevel</em> are
integers; the release level is <code class="docutils literal notranslate"><span class="pre">'alpha'</span></code>, <code class="docutils literal notranslate"><span class="pre">'beta'</span></code>, <code class="docutils literal notranslate"><span class="pre">'candidate'</span></code>, or
<code class="docutils literal notranslate"><span class="pre">'final'</span></code>.  The <code class="docutils literal notranslate"><span class="pre">version_info</span></code> value corresponding to the Python version 2.0
is <code class="docutils literal notranslate"><span class="pre">(2,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">'final',</span> <span class="pre">0)</span></code>.  The components can also be accessed by name,
so <code class="docutils literal notranslate"><span class="pre">sys.version_info[0]</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">sys.version_info.major</span></code>
and so on.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added named component attributes.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.warnoptions">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">warnoptions</span></span><a class="headerlink" href="#sys.warnoptions" title="Link to this definition">¶</a></dt>
<dd><p>This is an implementation detail of the warnings framework; do not modify this
value.  Refer to the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module for more information on the warnings
framework.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys.winver">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">winver</span></span><a class="headerlink" href="#sys.winver" title="Link to this definition">¶</a></dt>
<dd><p>The version number used to form registry keys on Windows platforms. This is
stored as string resource 1000 in the Python DLL.  The value is normally the
major and minor versions of the running Python interpreter.  It is provided in the <a class="reference internal" href="#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a>
module for informational purposes; modifying this value has no effect on the
registry keys used by Python.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">monitoring</span></span></dt>
<dd><p>Namespace containing functions and constants for register callbacks
and controlling monitoring events.
See  <a class="reference internal" href="sys.monitoring.html#module-sys.monitoring" title="sys.monitoring: Access and control event monitoring"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code></a> for details.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sys._xoptions">
<span class="sig-prename descclassname"><span class="pre">sys.</span></span><span class="sig-name descname"><span class="pre">_xoptions</span></span><a class="headerlink" href="#sys._xoptions" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary of the various implementation-specific flags passed through
the <a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a> command-line option.  Option names are either mapped to
their values, if given explicitly, or to <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>.  Example:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>./python<span class="w"> </span>-Xa<span class="o">=</span>b<span class="w"> </span>-Xc
<span class="go">Python 3.2a3+ (py3k, Oct 16 2010, 20:14:50)</span>
<span class="go">[GCC 4.4.3] on linux2</span>
<span class="go">Type &quot;help&quot;, &quot;copyright&quot;, &quot;credits&quot; or &quot;license&quot; for more information.</span>
<span class="go">&gt;&gt;&gt; import sys</span>
<span class="go">&gt;&gt;&gt; sys._xoptions</span>
<span class="go">{&#39;a&#39;: &#39;b&#39;, &#39;c&#39;: True}</span>
</pre></div>
</div>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> This is a CPython-specific way of accessing options passed through
<a class="reference internal" href="../using/cmdline.html#cmdoption-X"><code class="xref std std-option docutils literal notranslate"><span class="pre">-X</span></code></a>.  Other implementations may export them through other
means, or not at all.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<p class="rubric">Citations</p>
<div role="list" class="citation-list">
<div class="citation" id="c99" role="doc-biblioentry">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">C99</a><span class="fn-bracket">]</span></span>
<p>ISO/IEC 9899:1999.  “Programming languages – C.”  A public draft of this standard is available at <a class="reference external" href="https://www.open-std.org/jtc1/sc22/wg14/www/docs/n1256.pdf">https://www.open-std.org/jtc1/sc22/wg14/www/docs/n1256.pdf</a>.</p>
</div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="python.html"
                          title="previous chapter">Python Runtime Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="sys.monitoring.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sys.monitoring.html" title="sys.monitoring — Execution event monitoring"
             >next</a> |</li>
        <li class="right" >
          <a href="python.html" title="Python Runtime Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code> — System-specific parameters and functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>