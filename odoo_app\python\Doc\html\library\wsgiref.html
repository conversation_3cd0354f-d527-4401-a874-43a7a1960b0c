<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="wsgiref — WSGI Utilities and Reference Implementation" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/wsgiref.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/wsgiref The Web Server Gateway Interface (WSGI) is a standard interface between web server software and web applications written in Python. Having a standard interface makes it eas..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/wsgiref The Web Server Gateway Interface (WSGI) is a standard interface between web server software and web applications written in Python. Having a standard interface makes it eas..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>wsgiref — WSGI Utilities and Reference Implementation &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="urllib — URL handling modules" href="urllib.html" />
    <link rel="prev" title="webbrowser — Convenient web-browser controller" href="webbrowser.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/wsgiref.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a><ul>
<li><a class="reference internal" href="#module-wsgiref.util"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code> – WSGI environment utilities</a></li>
<li><a class="reference internal" href="#module-wsgiref.headers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.headers</span></code> – WSGI response header tools</a></li>
<li><a class="reference internal" href="#module-wsgiref.simple_server"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code> – a simple WSGI HTTP server</a></li>
<li><a class="reference internal" href="#module-wsgiref.validate"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.validate</span></code> — WSGI conformance checker</a></li>
<li><a class="reference internal" href="#module-wsgiref.handlers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code> – server/gateway base classes</a></li>
<li><a class="reference internal" href="#module-wsgiref.types"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.types</span></code> – WSGI types for static type checking</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="webbrowser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="urllib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code> — URL handling modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/wsgiref.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="urllib.html" title="urllib — URL handling modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="webbrowser.html" title="webbrowser — Convenient web-browser controller"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-wsgiref">
<span id="wsgiref-wsgi-utilities-and-reference-implementation"></span><h1><a class="reference internal" href="#module-wsgiref" title="wsgiref: WSGI Utilities and Reference Implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code></a> — WSGI Utilities and Reference Implementation<a class="headerlink" href="#module-wsgiref" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/wsgiref">Lib/wsgiref</a></p>
<hr class="docutils" />
<p>The Web Server Gateway Interface (WSGI) is a standard interface between web
server software and web applications written in Python. Having a standard
interface makes it easy to use an application that supports WSGI with a number
of different web servers.</p>
<p>Only authors of web servers and programming frameworks need to know every detail
and corner case of the WSGI design.  You don’t need to understand every detail
of WSGI just to install a WSGI application or to write a web application using
an existing framework.</p>
<p><a class="reference internal" href="#module-wsgiref" title="wsgiref: WSGI Utilities and Reference Implementation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code></a> is a reference implementation of the WSGI specification that can
be used to add WSGI support to a web server or framework.  It provides utilities
for manipulating WSGI environment variables and response headers, base classes
for implementing WSGI servers, a demo HTTP server that serves WSGI applications,
types for static type checking,
and a validation tool that checks WSGI servers and applications for conformance
to the WSGI specification (<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>).</p>
<p>See <a class="reference external" href="https://wsgi.readthedocs.io/">wsgi.readthedocs.io</a> for more information about WSGI, and links
to tutorials and other resources.</p>
<section id="module-wsgiref.util">
<span id="wsgiref-util-wsgi-environment-utilities"></span><h2><a class="reference internal" href="#module-wsgiref.util" title="wsgiref.util: WSGI environment utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code></a> – WSGI environment utilities<a class="headerlink" href="#module-wsgiref.util" title="Link to this heading">¶</a></h2>
<p>This module provides a variety of utility functions for working with WSGI
environments.  A WSGI environment is a dictionary containing HTTP request
variables as described in <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.  All of the functions taking an <em>environ</em>
parameter expect a WSGI-compliant dictionary to be supplied; please see
<span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a> for a detailed specification and
<a class="reference internal" href="#wsgiref.types.WSGIEnvironment" title="wsgiref.types.WSGIEnvironment"><code class="xref py py-data docutils literal notranslate"><span class="pre">WSGIEnvironment</span></code></a> for a type alias that can be used
in type annotations.</p>
<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.guess_scheme">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">guess_scheme</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.guess_scheme" title="Link to this definition">¶</a></dt>
<dd><p>Return a guess for whether <code class="docutils literal notranslate"><span class="pre">wsgi.url_scheme</span></code> should be “http” or “https”, by
checking for a <code class="docutils literal notranslate"><span class="pre">HTTPS</span></code> environment variable in the <em>environ</em> dictionary.  The
return value is a string.</p>
<p>This function is useful when creating a gateway that wraps CGI or a CGI-like
protocol such as FastCGI.  Typically, servers providing such protocols will
include a <code class="docutils literal notranslate"><span class="pre">HTTPS</span></code> variable with a value of “1”, “yes”, or “on” when a request
is received via SSL.  So, this function returns “https” if such a value is
found, and “http” otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.request_uri">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">request_uri</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_query</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.request_uri" title="Link to this definition">¶</a></dt>
<dd><p>Return the full request URI, optionally including the query string, using the
algorithm found in the “URL Reconstruction” section of <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.  If
<em>include_query</em> is false, the query string is not included in the resulting URI.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.application_uri">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">application_uri</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.application_uri" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#wsgiref.util.request_uri" title="wsgiref.util.request_uri"><code class="xref py py-func docutils literal notranslate"><span class="pre">request_uri()</span></code></a>, except that the <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> and
<code class="docutils literal notranslate"><span class="pre">QUERY_STRING</span></code> variables are ignored.  The result is the base URI of the
application object addressed by the request.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.shift_path_info">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">shift_path_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.shift_path_info" title="Link to this definition">¶</a></dt>
<dd><p>Shift a single name from <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> to <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code> and return the name.
The <em>environ</em> dictionary is <em>modified</em> in-place; use a copy if you need to keep
the original <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> or <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code> intact.</p>
<p>If there are no remaining path segments in <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code>, <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.</p>
<p>Typically, this routine is used to process each portion of a request URI path,
for example to treat the path as a series of dictionary keys. This routine
modifies the passed-in environment to make it suitable for invoking another WSGI
application that is located at the target URI. For example, if there is a WSGI
application at <code class="docutils literal notranslate"><span class="pre">/foo</span></code>, and the request URI path is <code class="docutils literal notranslate"><span class="pre">/foo/bar/baz</span></code>, and the
WSGI application at <code class="docutils literal notranslate"><span class="pre">/foo</span></code> calls <a class="reference internal" href="#wsgiref.util.shift_path_info" title="wsgiref.util.shift_path_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">shift_path_info()</span></code></a>, it will receive the
string “bar”, and the environment will be updated to be suitable for passing to
a WSGI application at <code class="docutils literal notranslate"><span class="pre">/foo/bar</span></code>.  That is, <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code> will change from
<code class="docutils literal notranslate"><span class="pre">/foo</span></code> to <code class="docutils literal notranslate"><span class="pre">/foo/bar</span></code>, and <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> will change from <code class="docutils literal notranslate"><span class="pre">/bar/baz</span></code> to
<code class="docutils literal notranslate"><span class="pre">/baz</span></code>.</p>
<p>When <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> is just a “/”, this routine returns an empty string and
appends a trailing slash to <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code>, even though empty path segments are
normally ignored, and <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code> doesn’t normally end in a slash.  This is
intentional behavior, to ensure that an application can tell the difference
between URIs ending in <code class="docutils literal notranslate"><span class="pre">/x</span></code> from ones ending in <code class="docutils literal notranslate"><span class="pre">/x/</span></code> when using this
routine to do object traversal.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.setup_testing_defaults">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">setup_testing_defaults</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.setup_testing_defaults" title="Link to this definition">¶</a></dt>
<dd><p>Update <em>environ</em> with trivial defaults for testing purposes.</p>
<p>This routine adds various parameters required for WSGI, including <code class="docutils literal notranslate"><span class="pre">HTTP_HOST</span></code>,
<code class="docutils literal notranslate"><span class="pre">SERVER_NAME</span></code>, <code class="docutils literal notranslate"><span class="pre">SERVER_PORT</span></code>, <code class="docutils literal notranslate"><span class="pre">REQUEST_METHOD</span></code>, <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code>,
<code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code>, and all of the <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>-defined <code class="docutils literal notranslate"><span class="pre">wsgi.*</span></code> variables.  It
only supplies default values, and does not replace any existing settings for
these variables.</p>
<p>This routine is intended to make it easier for unit tests of WSGI servers and
applications to set up dummy environments.  It should NOT be used by actual WSGI
servers or applications, since the data is fake!</p>
<p>Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">wsgiref.util</span> <span class="kn">import</span> <span class="n">setup_testing_defaults</span>
<span class="kn">from</span> <span class="nn">wsgiref.simple_server</span> <span class="kn">import</span> <span class="n">make_server</span>

<span class="c1"># A relatively simple WSGI application. It&#39;s going to print out the</span>
<span class="c1"># environment dictionary after being updated by setup_testing_defaults</span>
<span class="k">def</span> <span class="nf">simple_app</span><span class="p">(</span><span class="n">environ</span><span class="p">,</span> <span class="n">start_response</span><span class="p">):</span>
    <span class="n">setup_testing_defaults</span><span class="p">(</span><span class="n">environ</span><span class="p">)</span>

    <span class="n">status</span> <span class="o">=</span> <span class="s1">&#39;200 OK&#39;</span>
    <span class="n">headers</span> <span class="o">=</span> <span class="p">[(</span><span class="s1">&#39;Content-type&#39;</span><span class="p">,</span> <span class="s1">&#39;text/plain; charset=utf-8&#39;</span><span class="p">)]</span>

    <span class="n">start_response</span><span class="p">(</span><span class="n">status</span><span class="p">,</span> <span class="n">headers</span><span class="p">)</span>

    <span class="n">ret</span> <span class="o">=</span> <span class="p">[(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2">: </span><span class="si">%s</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">))</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s2">&quot;utf-8&quot;</span><span class="p">)</span>
           <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">environ</span><span class="o">.</span><span class="n">items</span><span class="p">()]</span>
    <span class="k">return</span> <span class="n">ret</span>

<span class="k">with</span> <span class="n">make_server</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">,</span> <span class="n">simple_app</span><span class="p">)</span> <span class="k">as</span> <span class="n">httpd</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Serving on port 8000...&quot;</span><span class="p">)</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<p>In addition to the environment functions above, the <a class="reference internal" href="#module-wsgiref.util" title="wsgiref.util: WSGI environment utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code></a> module
also provides these miscellaneous utilities:</p>
<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.util.is_hop_by_hop">
<span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">is_hop_by_hop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.is_hop_by_hop" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if ‘header_name’ is an HTTP/1.1 “Hop-by-Hop” header, as defined by
<span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html"><strong>RFC 2616</strong></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.util.FileWrapper">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.util.</span></span><span class="sig-name descname"><span class="pre">FileWrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filelike</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blksize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">8192</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.util.FileWrapper" title="Link to this definition">¶</a></dt>
<dd><p>A concrete implementation of the <a class="reference internal" href="#wsgiref.types.FileWrapper" title="wsgiref.types.FileWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">wsgiref.types.FileWrapper</span></code></a>
protocol used to convert a file-like object to an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a>.
The resulting objects
are <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a>s. As the object is iterated over, the
optional <em>blksize</em> parameter will be repeatedly passed to the <em>filelike</em>
object’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code> method to obtain bytestrings to yield.  When <code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code>
returns an empty bytestring, iteration is ended and is not resumable.</p>
<p>If <em>filelike</em> has a <code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code> method, the returned object will also have a
<code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code> method, and it will invoke the <em>filelike</em> object’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code>
method when called.</p>
<p>Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">io</span> <span class="kn">import</span> <span class="n">StringIO</span>
<span class="kn">from</span> <span class="nn">wsgiref.util</span> <span class="kn">import</span> <span class="n">FileWrapper</span>

<span class="c1"># We&#39;re using a StringIO-buffer for as the file-like object</span>
<span class="n">filelike</span> <span class="o">=</span> <span class="n">StringIO</span><span class="p">(</span><span class="s2">&quot;This is an example file-like object&quot;</span><span class="o">*</span><span class="mi">10</span><span class="p">)</span>
<span class="n">wrapper</span> <span class="o">=</span> <span class="n">FileWrapper</span><span class="p">(</span><span class="n">filelike</span><span class="p">,</span> <span class="n">blksize</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>

<span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">wrapper</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Support for <a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a> method has been removed.</p>
</div>
</dd></dl>

</section>
<section id="module-wsgiref.headers">
<span id="wsgiref-headers-wsgi-response-header-tools"></span><h2><a class="reference internal" href="#module-wsgiref.headers" title="wsgiref.headers: WSGI response header tools."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.headers</span></code></a> – WSGI response header tools<a class="headerlink" href="#module-wsgiref.headers" title="Link to this heading">¶</a></h2>
<p>This module provides a single class, <a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a>, for convenient
manipulation of WSGI response headers using a mapping-like interface.</p>
<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.headers.Headers">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.headers.</span></span><span class="sig-name descname"><span class="pre">Headers</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">headers</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.headers.Headers" title="Link to this definition">¶</a></dt>
<dd><p>Create a mapping-like object wrapping <em>headers</em>, which must be a list of header
name/value tuples as described in <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>. The default value of <em>headers</em> is
an empty list.</p>
<p><a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a> objects support typical mapping operations including
<a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a>, <a class="reference internal" href="stdtypes.html#dict.get" title="dict.get"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get()</span></code></a>, <a class="reference internal" href="../reference/datamodel.html#object.__setitem__" title="object.__setitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setitem__()</span></code></a>,
<a class="reference internal" href="stdtypes.html#dict.setdefault" title="dict.setdefault"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setdefault()</span></code></a>,
<a class="reference internal" href="../reference/datamodel.html#object.__delitem__" title="object.__delitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__delitem__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__contains__" title="object.__contains__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__contains__()</span></code></a>.  For each of
these methods, the key is the header name (treated case-insensitively), and the
value is the first value associated with that header name.  Setting a header
deletes any existing values for that header, then adds a new value at the end of
the wrapped header list.  Headers’ existing order is generally maintained, with
new headers added to the end of the wrapped list.</p>
<p>Unlike a dictionary, <a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a> objects do not raise an error when you try
to get or delete a key that isn’t in the wrapped header list. Getting a
nonexistent header just returns <code class="docutils literal notranslate"><span class="pre">None</span></code>, and deleting a nonexistent header does
nothing.</p>
<p><a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a> objects also support <code class="xref py py-meth docutils literal notranslate"><span class="pre">keys()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">values()</span></code>, and
<code class="xref py py-meth docutils literal notranslate"><span class="pre">items()</span></code> methods.  The lists returned by <code class="xref py py-meth docutils literal notranslate"><span class="pre">keys()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">items()</span></code> can
include the same key more than once if there is a multi-valued header.  The
<code class="docutils literal notranslate"><span class="pre">len()</span></code> of a <a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a> object is the same as the length of its
<code class="xref py py-meth docutils literal notranslate"><span class="pre">items()</span></code>, which is the same as the length of the wrapped header list.  In
fact, the <code class="xref py py-meth docutils literal notranslate"><span class="pre">items()</span></code> method just returns a copy of the wrapped header list.</p>
<p>Calling <code class="docutils literal notranslate"><span class="pre">bytes()</span></code> on a <a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a> object returns a formatted bytestring
suitable for transmission as HTTP response headers.  Each header is placed on a
line with its value, separated by a colon and a space. Each line is terminated
by a carriage return and line feed, and the bytestring is terminated with a
blank line.</p>
<p>In addition to their mapping interface and formatting features, <a class="reference internal" href="#wsgiref.headers.Headers" title="wsgiref.headers.Headers"><code class="xref py py-class docutils literal notranslate"><span class="pre">Headers</span></code></a>
objects also have the following methods for querying and adding multi-valued
headers, and for adding headers with MIME parameters:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.headers.Headers.get_all">
<span class="sig-name descname"><span class="pre">get_all</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.headers.Headers.get_all" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of all the values for the named header.</p>
<p>The returned list will be sorted in the order they appeared in the original
header list or were added to this instance, and may contain duplicates.  Any
fields deleted and re-inserted are always appended to the header list.  If no
fields exist with the given name, returns an empty list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.headers.Headers.add_header">
<span class="sig-name descname"><span class="pre">add_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">_params</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.headers.Headers.add_header" title="Link to this definition">¶</a></dt>
<dd><p>Add a (possibly multi-valued) header, with optional MIME parameters specified
via keyword arguments.</p>
<p><em>name</em> is the header field to add.  Keyword arguments can be used to set MIME
parameters for the header field.  Each parameter must be a string or <code class="docutils literal notranslate"><span class="pre">None</span></code>.
Underscores in parameter names are converted to dashes, since dashes are illegal
in Python identifiers, but many MIME parameter names include dashes.  If the
parameter value is a string, it is added to the header value parameters in the
form <code class="docutils literal notranslate"><span class="pre">name=&quot;value&quot;</span></code>. If it is <code class="docutils literal notranslate"><span class="pre">None</span></code>, only the parameter name is added.
(This is used for MIME parameters without a value.)  Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">h</span><span class="o">.</span><span class="n">add_header</span><span class="p">(</span><span class="s1">&#39;content-disposition&#39;</span><span class="p">,</span> <span class="s1">&#39;attachment&#39;</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="s1">&#39;bud.gif&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>The above will add a header that looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Content</span><span class="o">-</span><span class="n">Disposition</span><span class="p">:</span> <span class="n">attachment</span><span class="p">;</span> <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;bud.gif&quot;</span>
</pre></div>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span><em>headers</em> parameter is optional.</p>
</div>
</dd></dl>

</section>
<section id="module-wsgiref.simple_server">
<span id="wsgiref-simple-server-a-simple-wsgi-http-server"></span><h2><a class="reference internal" href="#module-wsgiref.simple_server" title="wsgiref.simple_server: A simple WSGI HTTP server."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code></a> – a simple WSGI HTTP server<a class="headerlink" href="#module-wsgiref.simple_server" title="Link to this heading">¶</a></h2>
<p>This module implements a simple HTTP server (based on <a class="reference internal" href="http.server.html#module-http.server" title="http.server: HTTP server and request handlers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">http.server</span></code></a>)
that serves WSGI applications.  Each server instance serves a single WSGI
application on a given host and port.  If you want to serve multiple
applications on a single host and port, you should create a WSGI application
that parses <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> to select which application to invoke for each
request.  (E.g., using the <code class="xref py py-func docutils literal notranslate"><span class="pre">shift_path_info()</span></code> function from
<a class="reference internal" href="#module-wsgiref.util" title="wsgiref.util: WSGI environment utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code></a>.)</p>
<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.simple_server.make_server">
<span class="sig-prename descclassname"><span class="pre">wsgiref.simple_server.</span></span><span class="sig-name descname"><span class="pre">make_server</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">app</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">WSGIServer</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">handler_class</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">WSGIRequestHandler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.make_server" title="Link to this definition">¶</a></dt>
<dd><p>Create a new WSGI server listening on <em>host</em> and <em>port</em>, accepting connections
for <em>app</em>.  The return value is an instance of the supplied <em>server_class</em>, and
will process requests using the specified <em>handler_class</em>.  <em>app</em> must be a WSGI
application object, as defined by <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.</p>
<p>Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">wsgiref.simple_server</span> <span class="kn">import</span> <span class="n">make_server</span><span class="p">,</span> <span class="n">demo_app</span>

<span class="k">with</span> <span class="n">make_server</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">,</span> <span class="n">demo_app</span><span class="p">)</span> <span class="k">as</span> <span class="n">httpd</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Serving HTTP on port 8000...&quot;</span><span class="p">)</span>

    <span class="c1"># Respond to requests until process is killed</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>

    <span class="c1"># Alternative: serve one request, then exit</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">handle_request</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.simple_server.demo_app">
<span class="sig-prename descclassname"><span class="pre">wsgiref.simple_server.</span></span><span class="sig-name descname"><span class="pre">demo_app</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_response</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.demo_app" title="Link to this definition">¶</a></dt>
<dd><p>This function is a small but complete WSGI application that returns a text page
containing the message “Hello world!” and a list of the key/value pairs provided
in the <em>environ</em> parameter.  It’s useful for verifying that a WSGI server (such
as <a class="reference internal" href="#module-wsgiref.simple_server" title="wsgiref.simple_server: A simple WSGI HTTP server."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code></a>) is able to run a simple WSGI application
correctly.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIServer">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.simple_server.</span></span><span class="sig-name descname"><span class="pre">WSGIServer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">server_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">RequestHandlerClass</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIServer" title="Link to this definition">¶</a></dt>
<dd><p>Create a <a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> instance.  <em>server_address</em> should be a
<code class="docutils literal notranslate"><span class="pre">(host,port)</span></code> tuple, and <em>RequestHandlerClass</em> should be the subclass of
<a class="reference internal" href="http.server.html#http.server.BaseHTTPRequestHandler" title="http.server.BaseHTTPRequestHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.server.BaseHTTPRequestHandler</span></code></a> that will be used to process
requests.</p>
<p>You do not normally need to call this constructor, as the <a class="reference internal" href="#wsgiref.simple_server.make_server" title="wsgiref.simple_server.make_server"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_server()</span></code></a>
function can handle all the details for you.</p>
<p><a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> is a subclass of <a class="reference internal" href="http.server.html#http.server.HTTPServer" title="http.server.HTTPServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.server.HTTPServer</span></code></a>, so all
of its methods (such as <code class="xref py py-meth docutils literal notranslate"><span class="pre">serve_forever()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_request()</span></code>) are
available. <a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> also provides these WSGI-specific methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIServer.set_app">
<span class="sig-name descname"><span class="pre">set_app</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">application</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIServer.set_app" title="Link to this definition">¶</a></dt>
<dd><p>Sets the callable <em>application</em> as the WSGI application that will receive
requests.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIServer.get_app">
<span class="sig-name descname"><span class="pre">get_app</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIServer.get_app" title="Link to this definition">¶</a></dt>
<dd><p>Returns the currently set application callable.</p>
</dd></dl>

<p>Normally, however, you do not need to use these additional methods, as
<a class="reference internal" href="#wsgiref.simple_server.WSGIServer.set_app" title="wsgiref.simple_server.WSGIServer.set_app"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_app()</span></code></a> is normally called by <a class="reference internal" href="#wsgiref.simple_server.make_server" title="wsgiref.simple_server.make_server"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_server()</span></code></a>, and the
<a class="reference internal" href="#wsgiref.simple_server.WSGIServer.get_app" title="wsgiref.simple_server.WSGIServer.get_app"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_app()</span></code></a> exists mainly for the benefit of request handler instances.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIRequestHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.simple_server.</span></span><span class="sig-name descname"><span class="pre">WSGIRequestHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">client_address</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">server</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIRequestHandler" title="Link to this definition">¶</a></dt>
<dd><p>Create an HTTP handler for the given <em>request</em> (i.e. a socket), <em>client_address</em>
(a <code class="docutils literal notranslate"><span class="pre">(host,port)</span></code> tuple), and <em>server</em> (<a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> instance).</p>
<p>You do not need to create instances of this class directly; they are
automatically created as needed by <a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> objects.  You can,
however, subclass this class and supply it as a <em>handler_class</em> to the
<a class="reference internal" href="#wsgiref.simple_server.make_server" title="wsgiref.simple_server.make_server"><code class="xref py py-func docutils literal notranslate"><span class="pre">make_server()</span></code></a> function.  Some possibly relevant methods for overriding in
subclasses:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIRequestHandler.get_environ">
<span class="sig-name descname"><span class="pre">get_environ</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIRequestHandler.get_environ" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#wsgiref.types.WSGIEnvironment" title="wsgiref.types.WSGIEnvironment"><code class="xref py py-data docutils literal notranslate"><span class="pre">WSGIEnvironment</span></code></a> dictionary for a
request.  The default
implementation copies the contents of the <a class="reference internal" href="#wsgiref.simple_server.WSGIServer" title="wsgiref.simple_server.WSGIServer"><code class="xref py py-class docutils literal notranslate"><span class="pre">WSGIServer</span></code></a> object’s
<code class="xref py py-attr docutils literal notranslate"><span class="pre">base_environ</span></code> dictionary attribute and then adds various headers derived
from the HTTP request.  Each call to this method should return a new dictionary
containing all of the relevant CGI environment variables as specified in
<span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIRequestHandler.get_stderr">
<span class="sig-name descname"><span class="pre">get_stderr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIRequestHandler.get_stderr" title="Link to this definition">¶</a></dt>
<dd><p>Return the object that should be used as the <code class="docutils literal notranslate"><span class="pre">wsgi.errors</span></code> stream. The default
implementation just returns <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.simple_server.WSGIRequestHandler.handle">
<span class="sig-name descname"><span class="pre">handle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.simple_server.WSGIRequestHandler.handle" title="Link to this definition">¶</a></dt>
<dd><p>Process the HTTP request.  The default implementation creates a handler instance
using a <a class="reference internal" href="#module-wsgiref.handlers" title="wsgiref.handlers: WSGI server/gateway base classes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code></a> class to implement the actual WSGI application
interface.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-wsgiref.validate">
<span id="wsgiref-validate-wsgi-conformance-checker"></span><h2><a class="reference internal" href="#module-wsgiref.validate" title="wsgiref.validate: WSGI conformance checker."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.validate</span></code></a> — WSGI conformance checker<a class="headerlink" href="#module-wsgiref.validate" title="Link to this heading">¶</a></h2>
<p>When creating new WSGI application objects, frameworks, servers, or middleware,
it can be useful to validate the new code’s conformance using
<a class="reference internal" href="#module-wsgiref.validate" title="wsgiref.validate: WSGI conformance checker."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.validate</span></code></a>.  This module provides a function that creates WSGI
application objects that validate communications between a WSGI server or
gateway and a WSGI application object, to check both sides for protocol
conformance.</p>
<p>Note that this utility does not guarantee complete <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a> compliance; an
absence of errors from this module does not necessarily mean that errors do not
exist.  However, if this module does produce an error, then it is virtually
certain that either the server or application is not 100% compliant.</p>
<p>This module is based on the <code class="xref py py-mod docutils literal notranslate"><span class="pre">paste.lint</span></code> module from Ian Bicking’s “Python
Paste” library.</p>
<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.validate.validator">
<span class="sig-prename descclassname"><span class="pre">wsgiref.validate.</span></span><span class="sig-name descname"><span class="pre">validator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">application</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.validate.validator" title="Link to this definition">¶</a></dt>
<dd><p>Wrap <em>application</em> and return a new WSGI application object.  The returned
application will forward all requests to the original <em>application</em>, and will
check that both the <em>application</em> and the server invoking it are conforming to
the WSGI specification and to <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html"><strong>RFC 2616</strong></a>.</p>
<p>Any detected nonconformance results in an <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> being raised;
note, however, that how these errors are handled is server-dependent.  For
example, <a class="reference internal" href="#module-wsgiref.simple_server" title="wsgiref.simple_server: A simple WSGI HTTP server."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code></a> and other servers based on
<a class="reference internal" href="#module-wsgiref.handlers" title="wsgiref.handlers: WSGI server/gateway base classes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code></a> (that don’t override the error handling methods to do
something else) will simply output a message that an error has occurred, and
dump the traceback to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> or some other error stream.</p>
<p>This wrapper may also generate output using the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> module to
indicate behaviors that are questionable but which may not actually be
prohibited by <span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.  Unless they are suppressed using Python command-line
options or the <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warnings</span></code></a> API, any such warnings will be written to
<code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> (<em>not</em> <code class="docutils literal notranslate"><span class="pre">wsgi.errors</span></code>, unless they happen to be the same
object).</p>
<p>Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">wsgiref.validate</span> <span class="kn">import</span> <span class="n">validator</span>
<span class="kn">from</span> <span class="nn">wsgiref.simple_server</span> <span class="kn">import</span> <span class="n">make_server</span>

<span class="c1"># Our callable object which is intentionally not compliant to the</span>
<span class="c1"># standard, so the validator is going to break</span>
<span class="k">def</span> <span class="nf">simple_app</span><span class="p">(</span><span class="n">environ</span><span class="p">,</span> <span class="n">start_response</span><span class="p">):</span>
    <span class="n">status</span> <span class="o">=</span> <span class="s1">&#39;200 OK&#39;</span>  <span class="c1"># HTTP Status</span>
    <span class="n">headers</span> <span class="o">=</span> <span class="p">[(</span><span class="s1">&#39;Content-type&#39;</span><span class="p">,</span> <span class="s1">&#39;text/plain&#39;</span><span class="p">)]</span>  <span class="c1"># HTTP Headers</span>
    <span class="n">start_response</span><span class="p">(</span><span class="n">status</span><span class="p">,</span> <span class="n">headers</span><span class="p">)</span>

    <span class="c1"># This is going to break because we need to return a list, and</span>
    <span class="c1"># the validator is going to inform us</span>
    <span class="k">return</span> <span class="sa">b</span><span class="s2">&quot;Hello World&quot;</span>

<span class="c1"># This is the application wrapped in a validator</span>
<span class="n">validator_app</span> <span class="o">=</span> <span class="n">validator</span><span class="p">(</span><span class="n">simple_app</span><span class="p">)</span>

<span class="k">with</span> <span class="n">make_server</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">,</span> <span class="n">validator_app</span><span class="p">)</span> <span class="k">as</span> <span class="n">httpd</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Listening on port 8000....&quot;</span><span class="p">)</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="module-wsgiref.handlers">
<span id="wsgiref-handlers-server-gateway-base-classes"></span><h2><a class="reference internal" href="#module-wsgiref.handlers" title="wsgiref.handlers: WSGI server/gateway base classes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code></a> – server/gateway base classes<a class="headerlink" href="#module-wsgiref.handlers" title="Link to this heading">¶</a></h2>
<p>This module provides base handler classes for implementing WSGI servers and
gateways.  These base classes handle most of the work of communicating with a
WSGI application, as long as they are given a CGI-like environment, along with
input, output, and error streams.</p>
<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.handlers.CGIHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">CGIHandler</span></span><a class="headerlink" href="#wsgiref.handlers.CGIHandler" title="Link to this definition">¶</a></dt>
<dd><p>CGI-based invocation via <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>, <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>, <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> and
<code class="docutils literal notranslate"><span class="pre">os.environ</span></code>.  This is useful when you have a WSGI application and want to run
it as a CGI script.  Simply invoke <code class="docutils literal notranslate"><span class="pre">CGIHandler().run(app)</span></code>, where <code class="docutils literal notranslate"><span class="pre">app</span></code> is
the WSGI application object you wish to invoke.</p>
<p>This class is a subclass of <a class="reference internal" href="#wsgiref.handlers.BaseCGIHandler" title="wsgiref.handlers.BaseCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCGIHandler</span></code></a> that sets <code class="docutils literal notranslate"><span class="pre">wsgi.run_once</span></code>
to true, <code class="docutils literal notranslate"><span class="pre">wsgi.multithread</span></code> to false, and <code class="docutils literal notranslate"><span class="pre">wsgi.multiprocess</span></code> to true, and
always uses <a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> and <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> to obtain the necessary CGI streams and
environment.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.handlers.IISCGIHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">IISCGIHandler</span></span><a class="headerlink" href="#wsgiref.handlers.IISCGIHandler" title="Link to this definition">¶</a></dt>
<dd><p>A specialized alternative to <a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a>, for use when deploying on
Microsoft’s IIS web server, without having set the config allowPathInfo
option (IIS&gt;=7) or metabase allowPathInfoForScriptMappings (IIS&lt;7).</p>
<p>By default, IIS gives a <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code> that duplicates the <code class="docutils literal notranslate"><span class="pre">SCRIPT_NAME</span></code> at
the front, causing problems for WSGI applications that wish to implement
routing. This handler strips any such duplicated path.</p>
<p>IIS can be configured to pass the correct <code class="docutils literal notranslate"><span class="pre">PATH_INFO</span></code>, but this causes
another bug where <code class="docutils literal notranslate"><span class="pre">PATH_TRANSLATED</span></code> is wrong. Luckily this variable is
rarely used and is not guaranteed by WSGI. On IIS&lt;7, though, the
setting can only be made on a vhost level, affecting all other script
mappings, many of which break when exposed to the <code class="docutils literal notranslate"><span class="pre">PATH_TRANSLATED</span></code> bug.
For this reason IIS&lt;7 is almost never deployed with the fix (Even IIS7
rarely uses it because there is still no UI for it.).</p>
<p>There is no way for CGI code to tell whether the option was set, so a
separate handler class is provided.  It is used in the same way as
<a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a>, i.e., by calling <code class="docutils literal notranslate"><span class="pre">IISCGIHandler().run(app)</span></code>, where
<code class="docutils literal notranslate"><span class="pre">app</span></code> is the WSGI application object you wish to invoke.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseCGIHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">BaseCGIHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stdin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">environ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multithread</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multiprocess</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseCGIHandler" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a>, but instead of using the <a class="reference internal" href="sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> and
<a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> modules, the CGI environment and I/O streams are specified explicitly.
The <em>multithread</em> and <em>multiprocess</em> values are used to set the
<code class="docutils literal notranslate"><span class="pre">wsgi.multithread</span></code> and <code class="docutils literal notranslate"><span class="pre">wsgi.multiprocess</span></code> flags for any applications run by
the handler instance.</p>
<p>This class is a subclass of <a class="reference internal" href="#wsgiref.handlers.SimpleHandler" title="wsgiref.handlers.SimpleHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHandler</span></code></a> intended for use with
software other than HTTP “origin servers”.  If you are writing a gateway
protocol implementation (such as CGI, FastCGI, SCGI, etc.) that uses a
<code class="docutils literal notranslate"><span class="pre">Status:</span></code> header to send an HTTP status, you probably want to subclass this
instead of <a class="reference internal" href="#wsgiref.handlers.SimpleHandler" title="wsgiref.handlers.SimpleHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">SimpleHandler</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.handlers.SimpleHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">SimpleHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stdin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">environ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multithread</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">multiprocess</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.SimpleHandler" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#wsgiref.handlers.BaseCGIHandler" title="wsgiref.handlers.BaseCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCGIHandler</span></code></a>, but designed for use with HTTP origin
servers.  If you are writing an HTTP server implementation, you will probably
want to subclass this instead of <a class="reference internal" href="#wsgiref.handlers.BaseCGIHandler" title="wsgiref.handlers.BaseCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCGIHandler</span></code></a>.</p>
<p>This class is a subclass of <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>.  It overrides the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>, <a class="reference internal" href="#wsgiref.handlers.BaseHandler.get_stdin" title="wsgiref.handlers.BaseHandler.get_stdin"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_stdin()</span></code></a>,
<a class="reference internal" href="#wsgiref.handlers.BaseHandler.get_stderr" title="wsgiref.handlers.BaseHandler.get_stderr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_stderr()</span></code></a>, <a class="reference internal" href="#wsgiref.handlers.BaseHandler.add_cgi_vars" title="wsgiref.handlers.BaseHandler.add_cgi_vars"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_cgi_vars()</span></code></a>,
<a class="reference internal" href="#wsgiref.handlers.BaseHandler._write" title="wsgiref.handlers.BaseHandler._write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_write()</span></code></a>, and <a class="reference internal" href="#wsgiref.handlers.BaseHandler._flush" title="wsgiref.handlers.BaseHandler._flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_flush()</span></code></a> methods to
support explicitly setting the
environment and streams via the constructor.  The supplied environment and
streams are stored in the <code class="xref py py-attr docutils literal notranslate"><span class="pre">stdin</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">stdout</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">stderr</span></code>, and
<code class="xref py py-attr docutils literal notranslate"><span class="pre">environ</span></code> attributes.</p>
<p>The <a class="reference internal" href="io.html#io.BufferedIOBase.write" title="io.BufferedIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> method of <em>stdout</em> should write
each chunk in full, like <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedIOBase</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">BaseHandler</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler" title="Link to this definition">¶</a></dt>
<dd><p>This is an abstract base class for running WSGI applications.  Each instance
will handle a single HTTP request, although in principle you could create a
subclass that was reusable for multiple requests.</p>
<p><a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> instances have only one method intended for external use:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">app</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.run" title="Link to this definition">¶</a></dt>
<dd><p>Run the specified WSGI application, <em>app</em>.</p>
</dd></dl>

<p>All of the other <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> methods are invoked by this method in the
process of running the application, and thus exist primarily to allow
customizing the process.</p>
<p>The following methods MUST be overridden in a subclass:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler._write">
<span class="sig-name descname"><span class="pre">_write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler._write" title="Link to this definition">¶</a></dt>
<dd><p>Buffer the bytes <em>data</em> for transmission to the client.  It’s okay if this
method actually transmits the data; <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> just separates write
and flush operations for greater efficiency when the underlying system actually
has such a distinction.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler._flush">
<span class="sig-name descname"><span class="pre">_flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler._flush" title="Link to this definition">¶</a></dt>
<dd><p>Force buffered data to be transmitted to the client.  It’s okay if this method
is a no-op (i.e., if <a class="reference internal" href="#wsgiref.handlers.BaseHandler._write" title="wsgiref.handlers.BaseHandler._write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_write()</span></code></a> actually sends the data).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.get_stdin">
<span class="sig-name descname"><span class="pre">get_stdin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.get_stdin" title="Link to this definition">¶</a></dt>
<dd><p>Return an object compatible with <a class="reference internal" href="#wsgiref.types.InputStream" title="wsgiref.types.InputStream"><code class="xref py py-class docutils literal notranslate"><span class="pre">InputStream</span></code></a>
suitable for use as the <code class="docutils literal notranslate"><span class="pre">wsgi.input</span></code> of the
request currently being processed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.get_stderr">
<span class="sig-name descname"><span class="pre">get_stderr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.get_stderr" title="Link to this definition">¶</a></dt>
<dd><p>Return an object compatible with <a class="reference internal" href="#wsgiref.types.ErrorStream" title="wsgiref.types.ErrorStream"><code class="xref py py-class docutils literal notranslate"><span class="pre">ErrorStream</span></code></a>
suitable for use as the <code class="docutils literal notranslate"><span class="pre">wsgi.errors</span></code> of the
request currently being processed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.add_cgi_vars">
<span class="sig-name descname"><span class="pre">add_cgi_vars</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.add_cgi_vars" title="Link to this definition">¶</a></dt>
<dd><p>Insert CGI variables for the current request into the <code class="xref py py-attr docutils literal notranslate"><span class="pre">environ</span></code> attribute.</p>
</dd></dl>

<p>Here are some other methods and attributes you may wish to override. This list
is only a summary, however, and does not include every method that can be
overridden.  You should consult the docstrings and source code for additional
information before attempting to create a customized <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>
subclass.</p>
<p>Attributes and methods for customizing the WSGI environment:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.wsgi_multithread">
<span class="sig-name descname"><span class="pre">wsgi_multithread</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.wsgi_multithread" title="Link to this definition">¶</a></dt>
<dd><p>The value to be used for the <code class="docutils literal notranslate"><span class="pre">wsgi.multithread</span></code> environment variable.  It
defaults to true in <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but may have a different default (or
be set by the constructor) in the other subclasses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.wsgi_multiprocess">
<span class="sig-name descname"><span class="pre">wsgi_multiprocess</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.wsgi_multiprocess" title="Link to this definition">¶</a></dt>
<dd><p>The value to be used for the <code class="docutils literal notranslate"><span class="pre">wsgi.multiprocess</span></code> environment variable.  It
defaults to true in <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but may have a different default (or
be set by the constructor) in the other subclasses.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.wsgi_run_once">
<span class="sig-name descname"><span class="pre">wsgi_run_once</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.wsgi_run_once" title="Link to this definition">¶</a></dt>
<dd><p>The value to be used for the <code class="docutils literal notranslate"><span class="pre">wsgi.run_once</span></code> environment variable.  It
defaults to false in <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but <a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a> sets it to
true by default.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.os_environ">
<span class="sig-name descname"><span class="pre">os_environ</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.os_environ" title="Link to this definition">¶</a></dt>
<dd><p>The default environment variables to be included in every request’s WSGI
environment.  By default, this is a copy of <code class="docutils literal notranslate"><span class="pre">os.environ</span></code> at the time that
<a class="reference internal" href="#module-wsgiref.handlers" title="wsgiref.handlers: WSGI server/gateway base classes."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code></a> was imported, but subclasses can either create their own
at the class or instance level.  Note that the dictionary should be considered
read-only, since the default value is shared between multiple classes and
instances.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.server_software">
<span class="sig-name descname"><span class="pre">server_software</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.server_software" title="Link to this definition">¶</a></dt>
<dd><p>If the <a class="reference internal" href="#wsgiref.handlers.BaseHandler.origin_server" title="wsgiref.handlers.BaseHandler.origin_server"><code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_server</span></code></a> attribute is set, this attribute’s value is used to
set the default <code class="docutils literal notranslate"><span class="pre">SERVER_SOFTWARE</span></code> WSGI environment variable, and also to set a
default <code class="docutils literal notranslate"><span class="pre">Server:</span></code> header in HTTP responses.  It is ignored for handlers (such
as <a class="reference internal" href="#wsgiref.handlers.BaseCGIHandler" title="wsgiref.handlers.BaseCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCGIHandler</span></code></a> and <a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a>) that are not HTTP origin
servers.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The term “Python” is replaced with implementation specific term like
“CPython”, “Jython” etc.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.get_scheme">
<span class="sig-name descname"><span class="pre">get_scheme</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.get_scheme" title="Link to this definition">¶</a></dt>
<dd><p>Return the URL scheme being used for the current request.  The default
implementation uses the <code class="xref py py-func docutils literal notranslate"><span class="pre">guess_scheme()</span></code> function from <a class="reference internal" href="#module-wsgiref.util" title="wsgiref.util: WSGI environment utilities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code></a>
to guess whether the scheme should be “http” or “https”, based on the current
request’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">environ</span></code> variables.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.setup_environ">
<span class="sig-name descname"><span class="pre">setup_environ</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.setup_environ" title="Link to this definition">¶</a></dt>
<dd><p>Set the <code class="xref py py-attr docutils literal notranslate"><span class="pre">environ</span></code> attribute to a fully populated WSGI environment.  The
default implementation uses all of the above methods and attributes, plus the
<a class="reference internal" href="#wsgiref.handlers.BaseHandler.get_stdin" title="wsgiref.handlers.BaseHandler.get_stdin"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_stdin()</span></code></a>, <a class="reference internal" href="#wsgiref.handlers.BaseHandler.get_stderr" title="wsgiref.handlers.BaseHandler.get_stderr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_stderr()</span></code></a>, and <a class="reference internal" href="#wsgiref.handlers.BaseHandler.add_cgi_vars" title="wsgiref.handlers.BaseHandler.add_cgi_vars"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_cgi_vars()</span></code></a> methods and the
<a class="reference internal" href="#wsgiref.handlers.BaseHandler.wsgi_file_wrapper" title="wsgiref.handlers.BaseHandler.wsgi_file_wrapper"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wsgi_file_wrapper</span></code></a> attribute.  It also inserts a <code class="docutils literal notranslate"><span class="pre">SERVER_SOFTWARE</span></code> key
if not present, as long as the <a class="reference internal" href="#wsgiref.handlers.BaseHandler.origin_server" title="wsgiref.handlers.BaseHandler.origin_server"><code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_server</span></code></a> attribute is a true value
and the <a class="reference internal" href="#wsgiref.handlers.BaseHandler.server_software" title="wsgiref.handlers.BaseHandler.server_software"><code class="xref py py-attr docutils literal notranslate"><span class="pre">server_software</span></code></a> attribute is set.</p>
</dd></dl>

<p>Methods and attributes for customizing exception handling:</p>
<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.log_exception">
<span class="sig-name descname"><span class="pre">log_exception</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">exc_info</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.log_exception" title="Link to this definition">¶</a></dt>
<dd><p>Log the <em>exc_info</em> tuple in the server log.  <em>exc_info</em> is a <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">value,</span>
<span class="pre">traceback)</span></code> tuple.  The default implementation simply writes the traceback to
the request’s <code class="docutils literal notranslate"><span class="pre">wsgi.errors</span></code> stream and flushes it.  Subclasses can override
this method to change the format or retarget the output, mail the traceback to
an administrator, or whatever other action may be deemed suitable.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.traceback_limit">
<span class="sig-name descname"><span class="pre">traceback_limit</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.traceback_limit" title="Link to this definition">¶</a></dt>
<dd><p>The maximum number of frames to include in tracebacks output by the default
<a class="reference internal" href="#wsgiref.handlers.BaseHandler.log_exception" title="wsgiref.handlers.BaseHandler.log_exception"><code class="xref py py-meth docutils literal notranslate"><span class="pre">log_exception()</span></code></a> method.  If <code class="docutils literal notranslate"><span class="pre">None</span></code>, all frames are included.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.error_output">
<span class="sig-name descname"><span class="pre">error_output</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">environ</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_response</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.error_output" title="Link to this definition">¶</a></dt>
<dd><p>This method is a WSGI application to generate an error page for the user.  It is
only invoked if an error occurs before headers are sent to the client.</p>
<p>This method can access the current error using <code class="docutils literal notranslate"><span class="pre">sys.exception()</span></code>,
and should pass that information to <em>start_response</em> when calling it (as
described in the “Error Handling” section of <span class="target" id="index-12"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>).</p>
<p>The default implementation just uses the <a class="reference internal" href="#wsgiref.handlers.BaseHandler.error_status" title="wsgiref.handlers.BaseHandler.error_status"><code class="xref py py-attr docutils literal notranslate"><span class="pre">error_status</span></code></a>,
<a class="reference internal" href="#wsgiref.handlers.BaseHandler.error_headers" title="wsgiref.handlers.BaseHandler.error_headers"><code class="xref py py-attr docutils literal notranslate"><span class="pre">error_headers</span></code></a>, and <a class="reference internal" href="#wsgiref.handlers.BaseHandler.error_body" title="wsgiref.handlers.BaseHandler.error_body"><code class="xref py py-attr docutils literal notranslate"><span class="pre">error_body</span></code></a> attributes to generate an output
page.  Subclasses can override this to produce more dynamic error output.</p>
<p>Note, however, that it’s not recommended from a security perspective to spit out
diagnostics to any old user; ideally, you should have to do something special to
enable diagnostic output, which is why the default implementation doesn’t
include any.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.error_status">
<span class="sig-name descname"><span class="pre">error_status</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.error_status" title="Link to this definition">¶</a></dt>
<dd><p>The HTTP status used for error responses.  This should be a status string as
defined in <span class="target" id="index-13"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>; it defaults to a 500 code and message.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.error_headers">
<span class="sig-name descname"><span class="pre">error_headers</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.error_headers" title="Link to this definition">¶</a></dt>
<dd><p>The HTTP headers used for error responses.  This should be a list of WSGI
response headers (<code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> tuples), as described in <span class="target" id="index-14"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.  The
default list just sets the content type to <code class="docutils literal notranslate"><span class="pre">text/plain</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.error_body">
<span class="sig-name descname"><span class="pre">error_body</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.error_body" title="Link to this definition">¶</a></dt>
<dd><p>The error response body.  This should be an HTTP response body bytestring. It
defaults to the plain text, “A server error occurred.  Please contact the
administrator.”</p>
</dd></dl>

<p>Methods and attributes for <span class="target" id="index-15"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>’s “Optional Platform-Specific File
Handling” feature:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.wsgi_file_wrapper">
<span class="sig-name descname"><span class="pre">wsgi_file_wrapper</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.wsgi_file_wrapper" title="Link to this definition">¶</a></dt>
<dd><p>A <code class="docutils literal notranslate"><span class="pre">wsgi.file_wrapper</span></code> factory, compatible with
<a class="reference internal" href="#wsgiref.types.FileWrapper" title="wsgiref.types.FileWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">wsgiref.types.FileWrapper</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  The default value
of this attribute is the <a class="reference internal" href="#wsgiref.util.FileWrapper" title="wsgiref.util.FileWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">wsgiref.util.FileWrapper</span></code></a> class.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.sendfile">
<span class="sig-name descname"><span class="pre">sendfile</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.sendfile" title="Link to this definition">¶</a></dt>
<dd><p>Override to implement platform-specific file transmission.  This method is
called only if the application’s return value is an instance of the class
specified by the <a class="reference internal" href="#wsgiref.handlers.BaseHandler.wsgi_file_wrapper" title="wsgiref.handlers.BaseHandler.wsgi_file_wrapper"><code class="xref py py-attr docutils literal notranslate"><span class="pre">wsgi_file_wrapper</span></code></a> attribute.  It should return a true
value if it was able to successfully transmit the file, so that the default
transmission code will not be executed. The default implementation of this
method just returns a false value.</p>
</dd></dl>

<p>Miscellaneous methods and attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.origin_server">
<span class="sig-name descname"><span class="pre">origin_server</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.origin_server" title="Link to this definition">¶</a></dt>
<dd><p>This attribute should be set to a true value if the handler’s <a class="reference internal" href="#wsgiref.handlers.BaseHandler._write" title="wsgiref.handlers.BaseHandler._write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_write()</span></code></a> and
<a class="reference internal" href="#wsgiref.handlers.BaseHandler._flush" title="wsgiref.handlers.BaseHandler._flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_flush()</span></code></a> are being used to communicate directly to the client, rather than
via a CGI-like gateway protocol that wants the HTTP status in a special
<code class="docutils literal notranslate"><span class="pre">Status:</span></code> header.</p>
<p>This attribute’s default value is true in <a class="reference internal" href="#wsgiref.handlers.BaseHandler" title="wsgiref.handlers.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but false in
<a class="reference internal" href="#wsgiref.handlers.BaseCGIHandler" title="wsgiref.handlers.BaseCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseCGIHandler</span></code></a> and <a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="wsgiref.handlers.BaseHandler.http_version">
<span class="sig-name descname"><span class="pre">http_version</span></span><a class="headerlink" href="#wsgiref.handlers.BaseHandler.http_version" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#wsgiref.handlers.BaseHandler.origin_server" title="wsgiref.handlers.BaseHandler.origin_server"><code class="xref py py-attr docutils literal notranslate"><span class="pre">origin_server</span></code></a> is true, this string attribute is used to set the HTTP
version of the response set to the client.  It defaults to <code class="docutils literal notranslate"><span class="pre">&quot;1.0&quot;</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="wsgiref.handlers.read_environ">
<span class="sig-prename descclassname"><span class="pre">wsgiref.handlers.</span></span><span class="sig-name descname"><span class="pre">read_environ</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#wsgiref.handlers.read_environ" title="Link to this definition">¶</a></dt>
<dd><p>Transcode CGI variables from <code class="docutils literal notranslate"><span class="pre">os.environ</span></code> to <span class="target" id="index-16"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a> “bytes in unicode”
strings, returning a new dictionary.  This function is used by
<a class="reference internal" href="#wsgiref.handlers.CGIHandler" title="wsgiref.handlers.CGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CGIHandler</span></code></a> and <a class="reference internal" href="#wsgiref.handlers.IISCGIHandler" title="wsgiref.handlers.IISCGIHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">IISCGIHandler</span></code></a> in place of directly using
<code class="docutils literal notranslate"><span class="pre">os.environ</span></code>, which is not necessarily WSGI-compliant on all platforms
and web servers using Python 3 – specifically, ones where the OS’s
actual environment is Unicode (i.e. Windows), or ones where the environment
is bytes, but the system encoding used by Python to decode it is anything
other than ISO-8859-1 (e.g. Unix systems using UTF-8).</p>
<p>If you are implementing a CGI-based handler of your own, you probably want
to use this routine instead of just copying values out of <code class="docutils literal notranslate"><span class="pre">os.environ</span></code>
directly.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="module-wsgiref.types">
<span id="wsgiref-types-wsgi-types-for-static-type-checking"></span><h2><a class="reference internal" href="#module-wsgiref.types" title="wsgiref.types: WSGI types for static type checking"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.types</span></code></a> – WSGI types for static type checking<a class="headerlink" href="#module-wsgiref.types" title="Link to this heading">¶</a></h2>
<p>This module provides various types for static type checking as described
in <span class="target" id="index-17"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.types.StartResponse">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">StartResponse</span></span><a class="headerlink" href="#wsgiref.types.StartResponse" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="typing.html#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Protocol</span></code></a> describing <a class="reference external" href="https://peps.python.org/pep-3333/#the-start-response-callable">start_response()</a>
callables (<span class="target" id="index-18"></span><a class="pep reference external" href="https://peps.python.org/pep-3333/"><strong>PEP 3333</strong></a>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="wsgiref.types.WSGIEnvironment">
<span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">WSGIEnvironment</span></span><a class="headerlink" href="#wsgiref.types.WSGIEnvironment" title="Link to this definition">¶</a></dt>
<dd><p>A type alias describing a WSGI environment dictionary.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="wsgiref.types.WSGIApplication">
<span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">WSGIApplication</span></span><a class="headerlink" href="#wsgiref.types.WSGIApplication" title="Link to this definition">¶</a></dt>
<dd><p>A type alias describing a WSGI application callable.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.types.InputStream">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">InputStream</span></span><a class="headerlink" href="#wsgiref.types.InputStream" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="typing.html#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Protocol</span></code></a> describing a <a class="reference external" href="https://peps.python.org/pep-3333/#input-and-error-streams">WSGI Input Stream</a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.types.ErrorStream">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">ErrorStream</span></span><a class="headerlink" href="#wsgiref.types.ErrorStream" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="typing.html#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Protocol</span></code></a> describing a <a class="reference external" href="https://peps.python.org/pep-3333/#input-and-error-streams">WSGI Error Stream</a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="wsgiref.types.FileWrapper">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">wsgiref.types.</span></span><span class="sig-name descname"><span class="pre">FileWrapper</span></span><a class="headerlink" href="#wsgiref.types.FileWrapper" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="typing.html#typing.Protocol" title="typing.Protocol"><code class="xref py py-class docutils literal notranslate"><span class="pre">typing.Protocol</span></code></a> describing a <a class="reference external" href="https://peps.python.org/pep-3333/#optional-platform-specific-file-handling">file wrapper</a>.
See <a class="reference internal" href="#wsgiref.util.FileWrapper" title="wsgiref.util.FileWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">wsgiref.util.FileWrapper</span></code></a> for a concrete implementation of this
protocol.</p>
</dd></dl>

</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>This is a working “Hello World” WSGI application:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Every WSGI application must have an application object - a callable</span>
<span class="sd">object that accepts two arguments. For that purpose, we&#39;re going to</span>
<span class="sd">use a function (note that you&#39;re not limited to a function, you can</span>
<span class="sd">use a class for example). The first argument passed to the function</span>
<span class="sd">is a dictionary containing CGI-style environment variables and the</span>
<span class="sd">second variable is the callable object.</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="kn">from</span> <span class="nn">wsgiref.simple_server</span> <span class="kn">import</span> <span class="n">make_server</span>


<span class="k">def</span> <span class="nf">hello_world_app</span><span class="p">(</span><span class="n">environ</span><span class="p">,</span> <span class="n">start_response</span><span class="p">):</span>
    <span class="n">status</span> <span class="o">=</span> <span class="s2">&quot;200 OK&quot;</span>  <span class="c1"># HTTP Status</span>
    <span class="n">headers</span> <span class="o">=</span> <span class="p">[(</span><span class="s2">&quot;Content-type&quot;</span><span class="p">,</span> <span class="s2">&quot;text/plain; charset=utf-8&quot;</span><span class="p">)]</span>  <span class="c1"># HTTP Headers</span>
    <span class="n">start_response</span><span class="p">(</span><span class="n">status</span><span class="p">,</span> <span class="n">headers</span><span class="p">)</span>

    <span class="c1"># The returned object is going to be printed</span>
    <span class="k">return</span> <span class="p">[</span><span class="sa">b</span><span class="s2">&quot;Hello World&quot;</span><span class="p">]</span>

<span class="k">with</span> <span class="n">make_server</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="mi">8000</span><span class="p">,</span> <span class="n">hello_world_app</span><span class="p">)</span> <span class="k">as</span> <span class="n">httpd</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Serving on port 8000...&quot;</span><span class="p">)</span>

    <span class="c1"># Serve until process is killed</span>
    <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
</pre></div>
</div>
<p>Example of a WSGI application serving the current directory, accept optional
directory and port number (default: 8000) on the command line:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Small wsgiref based web server. Takes a path to serve from and an</span>
<span class="sd">optional port number (defaults to 8000), then tries to serve files.</span>
<span class="sd">MIME types are guessed from the file names, 404 errors are raised</span>
<span class="sd">if the file is not found.</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="kn">import</span> <span class="nn">mimetypes</span>
<span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">from</span> <span class="nn">wsgiref</span> <span class="kn">import</span> <span class="n">simple_server</span><span class="p">,</span> <span class="n">util</span>


<span class="k">def</span> <span class="nf">app</span><span class="p">(</span><span class="n">environ</span><span class="p">,</span> <span class="n">respond</span><span class="p">):</span>
    <span class="c1"># Get the file name and MIME type</span>
    <span class="n">fn</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">environ</span><span class="p">[</span><span class="s2">&quot;PATH_INFO&quot;</span><span class="p">][</span><span class="mi">1</span><span class="p">:])</span>
    <span class="k">if</span> <span class="s2">&quot;.&quot;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">fn</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">sep</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]:</span>
        <span class="n">fn</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">fn</span><span class="p">,</span> <span class="s2">&quot;index.html&quot;</span><span class="p">)</span>
    <span class="n">mime_type</span> <span class="o">=</span> <span class="n">mimetypes</span><span class="o">.</span><span class="n">guess_type</span><span class="p">(</span><span class="n">fn</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

    <span class="c1"># Return 200 OK if file exists, otherwise 404 Not Found</span>
    <span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">exists</span><span class="p">(</span><span class="n">fn</span><span class="p">):</span>
        <span class="n">respond</span><span class="p">(</span><span class="s2">&quot;200 OK&quot;</span><span class="p">,</span> <span class="p">[(</span><span class="s2">&quot;Content-Type&quot;</span><span class="p">,</span> <span class="n">mime_type</span><span class="p">)])</span>
        <span class="k">return</span> <span class="n">util</span><span class="o">.</span><span class="n">FileWrapper</span><span class="p">(</span><span class="nb">open</span><span class="p">(</span><span class="n">fn</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">))</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">respond</span><span class="p">(</span><span class="s2">&quot;404 Not Found&quot;</span><span class="p">,</span> <span class="p">[(</span><span class="s2">&quot;Content-Type&quot;</span><span class="p">,</span> <span class="s2">&quot;text/plain&quot;</span><span class="p">)])</span>
        <span class="k">return</span> <span class="p">[</span><span class="sa">b</span><span class="s2">&quot;not found&quot;</span><span class="p">]</span>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="c1"># Get the path and port from command-line arguments</span>
    <span class="n">path</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="k">else</span> <span class="n">os</span><span class="o">.</span><span class="n">getcwd</span><span class="p">()</span>
    <span class="n">port</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">[</span><span class="mi">2</span><span class="p">])</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">argv</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">2</span> <span class="k">else</span> <span class="mi">8000</span>

    <span class="c1"># Make and start the server until control-c</span>
    <span class="n">httpd</span> <span class="o">=</span> <span class="n">simple_server</span><span class="o">.</span><span class="n">make_server</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">port</span><span class="p">,</span> <span class="n">app</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Serving </span><span class="si">{</span><span class="n">path</span><span class="si">}</span><span class="s2"> on port </span><span class="si">{</span><span class="n">port</span><span class="si">}</span><span class="s2">, control-C to stop&quot;</span><span class="p">)</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">httpd</span><span class="o">.</span><span class="n">serve_forever</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Shutting down.&quot;</span><span class="p">)</span>
        <span class="n">httpd</span><span class="o">.</span><span class="n">server_close</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a><ul>
<li><a class="reference internal" href="#module-wsgiref.util"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.util</span></code> – WSGI environment utilities</a></li>
<li><a class="reference internal" href="#module-wsgiref.headers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.headers</span></code> – WSGI response header tools</a></li>
<li><a class="reference internal" href="#module-wsgiref.simple_server"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.simple_server</span></code> – a simple WSGI HTTP server</a></li>
<li><a class="reference internal" href="#module-wsgiref.validate"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.validate</span></code> — WSGI conformance checker</a></li>
<li><a class="reference internal" href="#module-wsgiref.handlers"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.handlers</span></code> – server/gateway base classes</a></li>
<li><a class="reference internal" href="#module-wsgiref.types"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref.types</span></code> – WSGI types for static type checking</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="webbrowser.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="urllib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code> — URL handling modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/wsgiref.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="urllib.html" title="urllib — URL handling modules"
             >next</a> |</li>
        <li class="right" >
          <a href="webbrowser.html" title="webbrowser — Convenient web-browser controller"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>