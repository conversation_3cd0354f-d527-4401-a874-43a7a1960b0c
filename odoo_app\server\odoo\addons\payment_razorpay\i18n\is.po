# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_razorpay
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-22 15:54+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Icelandic (https://app.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Enable recurring payments on Razorpay"
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__code
msgid "Code"
msgstr "Kóði"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Key Id"
msgstr ""

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Key Secret"
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_razorpay
#: model:ir.model,name:payment_razorpay.model_payment_provider
msgid "Payment Provider"
msgstr "Greiðsluveita"

#. module: payment_razorpay
#: model:ir.model,name:payment_razorpay.model_payment_transaction
msgid "Payment Transaction"
msgstr "Greiðsluviðskipti"

#. module: payment_razorpay
#. odoo-javascript
#: code:addons/payment_razorpay/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields.selection,name:payment_razorpay.selection__payment_provider__code__razorpay
msgid "Razorpay"
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_key_id
msgid "Razorpay Key Id"
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_key_secret
msgid "Razorpay Key Secret"
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_webhook_secret
msgid "Razorpay Webhook Secret"
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_provider.py:0
#, python-format
msgid "Razorpay gave us the following information: '%s'"
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid status: %s"
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing entity id."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing status."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received incomplete refund data."
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,help:payment_razorpay.field_payment_provider__razorpay_key_id
msgid "The key solely used to identify the account with Razorpay."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The phone number is invalid."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The phone number is missing."
msgstr ""

#. module: payment_razorpay
#: model:ir.model.fields,help:payment_razorpay.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Transactions processed by Razorpay can't be manually voided from Odoo."
msgstr ""

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Webhook Secret"
msgstr ""
