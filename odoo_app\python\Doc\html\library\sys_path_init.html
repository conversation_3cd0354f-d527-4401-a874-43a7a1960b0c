<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="The initialization of the sys.path module search path" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sys_path_init.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="A module search path is initialized when Python starts. This module search path may be accessed at sys.path. The first entry in the module search path is the directory that contains the input scrip..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="A module search path is initialized when Python starts. This module search path may be accessed at sys.path. The first entry in the module search path is the directory that contains the input scrip..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>The initialization of the sys.path module search path &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Python Language Services" href="language.html" />
    <link rel="prev" title="importlib.metadata – Accessing package metadata" href="importlib.metadata.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sys_path_init.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a><ul>
<li><a class="reference internal" href="#virtual-environments">Virtual environments</a></li>
<li><a class="reference internal" href="#pth-files">_pth files</a></li>
<li><a class="reference internal" href="#embedded-python">Embedded Python</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.metadata.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="language.html"
                          title="next chapter">Python Language Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys_path_init.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="language.html" title="Python Language Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="importlib.metadata.html" title="importlib.metadata – Accessing package metadata"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="the-initialization-of-the-sys-path-module-search-path">
<span id="sys-path-init"></span><h1>The initialization of the <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> module search path<a class="headerlink" href="#the-initialization-of-the-sys-path-module-search-path" title="Link to this heading">¶</a></h1>
<p>A module search path is initialized when Python starts. This module search path
may be accessed at <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>The first entry in the module search path is the directory that contains the
input script, if there is one. Otherwise, the first entry is the current
directory, which is the case when executing the interactive shell, a <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a>
command, or <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> module.</p>
<p>The <span class="target" id="index-0"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a> environment variable is often used to add directories
to the search path. If this environment variable is found then the contents are
added to the module search path.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a> will affect all installed Python versions/environments.
Be wary of setting this in your shell profile or global environment variables.
The <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module offers more nuanced techniques as mentioned below.</p>
</div>
<p>The next items added are the directories containing standard Python modules as
well as any <a class="reference internal" href="../glossary.html#term-extension-module"><span class="xref std std-term">extension module</span></a>s that these modules depend on. Extension
modules are <code class="docutils literal notranslate"><span class="pre">.pyd</span></code> files on Windows and <code class="docutils literal notranslate"><span class="pre">.so</span></code> files on other platforms. The
directory with the platform-independent Python modules is called <code class="docutils literal notranslate"><span class="pre">prefix</span></code>.
The directory with the extension modules is called <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code>.</p>
<p>The <span class="target" id="index-2"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a> environment variable may be used to set the <code class="docutils literal notranslate"><span class="pre">prefix</span></code>
and <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code> locations. Otherwise these directories are found by using
the Python executable as a starting point and then looking for various ‘landmark’
files and directories. Note that any symbolic links are followed so the real
Python executable location is used as the search starting point. The Python
executable location is called <code class="docutils literal notranslate"><span class="pre">home</span></code>.</p>
<p>Once <code class="docutils literal notranslate"><span class="pre">home</span></code> is determined, the <code class="docutils literal notranslate"><span class="pre">prefix</span></code> directory is found by first looking
for <code class="file docutils literal notranslate"><span class="pre">python</span><em><span class="pre">majorversion</span></em><em><span class="pre">minorversion</span></em><span class="pre">.zip</span></code> (<code class="docutils literal notranslate"><span class="pre">python311.zip</span></code>). On Windows
the zip archive is searched for in <code class="docutils literal notranslate"><span class="pre">home</span></code> and on Unix the archive is expected
to be in <code class="file docutils literal notranslate"><span class="pre">lib</span></code>. Note that the expected zip archive location is added to the
module search path even if the archive does not exist. If no archive was found,
Python on Windows will continue the search for <code class="docutils literal notranslate"><span class="pre">prefix</span></code> by looking for <code class="file docutils literal notranslate"><span class="pre">Lib\os.py</span></code>.
Python on Unix will look for <code class="file docutils literal notranslate"><span class="pre">lib/python</span><em><span class="pre">majorversion</span></em><span class="pre">.</span><em><span class="pre">minorversion</span></em><span class="pre">/os.py</span></code>
(<code class="docutils literal notranslate"><span class="pre">lib/python3.11/os.py</span></code>). On Windows <code class="docutils literal notranslate"><span class="pre">prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code> are the same,
however on other platforms <code class="file docutils literal notranslate"><span class="pre">lib/python</span><em><span class="pre">majorversion</span></em><span class="pre">.</span><em><span class="pre">minorversion</span></em><span class="pre">/lib-dynload</span></code>
(<code class="docutils literal notranslate"><span class="pre">lib/python3.11/lib-dynload</span></code>) is searched for and used as an anchor for
<code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code>. On some platforms <code class="file docutils literal notranslate"><span class="pre">lib</span></code> may be <code class="file docutils literal notranslate"><span class="pre">lib64</span></code> or another value,
see <a class="reference internal" href="sys.html#sys.platlibdir" title="sys.platlibdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platlibdir</span></code></a> and <span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPLATLIBDIR"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPLATLIBDIR</span></code></a>.</p>
<p>Once found, <code class="docutils literal notranslate"><span class="pre">prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code> are available at <a class="reference internal" href="sys.html#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.prefix</span></code></a> and
<a class="reference internal" href="sys.html#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code></a> respectively.</p>
<p>Finally, the <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module is processed and <code class="file docutils literal notranslate"><span class="pre">site-packages</span></code> directories
are added to the module search path. A common way to customize the search path is
to create <a class="reference internal" href="site.html#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a> or <a class="reference internal" href="site.html#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a> modules as described in
the <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module documentation.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Certain command line options may further affect path calculations.
See <a class="reference internal" href="../using/cmdline.html#cmdoption-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">-E</span></code></a>, <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a>, <a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a> and <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> for further details.</p>
</div>
<section id="virtual-environments">
<h2>Virtual environments<a class="headerlink" href="#virtual-environments" title="Link to this heading">¶</a></h2>
<p>If Python is run in a virtual environment (as described at <a class="reference internal" href="../tutorial/venv.html#tut-venv"><span class="std std-ref">Virtual Environments and Packages</span></a>)
then <code class="docutils literal notranslate"><span class="pre">prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code> are specific to the virtual environment.</p>
<p>If a <code class="docutils literal notranslate"><span class="pre">pyvenv.cfg</span></code> file is found alongside the main executable, or in the
directory one level above the executable, the following variations apply:</p>
<ul class="simple">
<li><p>If <code class="docutils literal notranslate"><span class="pre">home</span></code> is an absolute path and <span class="target" id="index-4"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a> is not set, this
path is used instead of the path to the main executable when deducing <code class="docutils literal notranslate"><span class="pre">prefix</span></code>
and <code class="docutils literal notranslate"><span class="pre">exec_prefix</span></code>.</p></li>
</ul>
</section>
<section id="pth-files">
<h2>_pth files<a class="headerlink" href="#pth-files" title="Link to this heading">¶</a></h2>
<p>To completely override <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> create a <code class="docutils literal notranslate"><span class="pre">._pth</span></code> file with the same
name as the shared library or executable (<code class="docutils literal notranslate"><span class="pre">python._pth</span></code> or <code class="docutils literal notranslate"><span class="pre">python311._pth</span></code>).
The shared library path is always known on Windows, however it may not be
available on other platforms. In the <code class="docutils literal notranslate"><span class="pre">._pth</span></code> file specify one line for each path
to add to <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>. The file based on the shared library name overrides
the one based on the executable, which allows paths to be restricted for any
program loading the runtime if desired.</p>
<p>When the file exists, all registry and environment variables are ignored,
isolated mode is enabled, and <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> is not imported unless one line in the
file specifies <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">site</span></code>. Blank paths and lines starting with <code class="docutils literal notranslate"><span class="pre">#</span></code> are
ignored. Each path may be absolute or relative to the location of the file.
Import statements other than to <code class="docutils literal notranslate"><span class="pre">site</span></code> are not permitted, and arbitrary code
cannot be specified.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">.pth</span></code> files (without leading underscore) will be processed normally
by the <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module when <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">site</span></code> has been specified.</p>
</section>
<section id="embedded-python">
<h2>Embedded Python<a class="headerlink" href="#embedded-python" title="Link to this heading">¶</a></h2>
<p>If Python is embedded within another application <a class="reference internal" href="../c-api/init_config.html#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> and
the <a class="reference internal" href="../c-api/init_config.html#c.PyConfig" title="PyConfig"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyConfig</span></code></a> structure can be used to initialize Python. The path specific
details are described at <a class="reference internal" href="../c-api/init_config.html#init-path-config"><span class="std std-ref">Python Path Configuration</span></a>. Alternatively the older <a class="reference internal" href="../c-api/init.html#c.Py_SetPath" title="Py_SetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPath()</span></code></a>
can be used to bypass the initialization of the module search path.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><a class="reference internal" href="../using/windows.html#windows-finding-modules"><span class="std std-ref">Finding modules</span></a> for detailed Windows notes.</p></li>
<li><p><a class="reference internal" href="../using/unix.html#using-on-unix"><span class="std std-ref">Using Python on Unix platforms</span></a> for Unix details.</p></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a><ul>
<li><a class="reference internal" href="#virtual-environments">Virtual environments</a></li>
<li><a class="reference internal" href="#pth-files">_pth files</a></li>
<li><a class="reference internal" href="#embedded-python">Embedded Python</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.metadata.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="language.html"
                          title="next chapter">Python Language Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sys_path_init.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="language.html" title="Python Language Services"
             >next</a> |</li>
        <li class="right" >
          <a href="importlib.metadata.html" title="importlib.metadata – Accessing package metadata"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The initialization of the <code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code> module search path</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>