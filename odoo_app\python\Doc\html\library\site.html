<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="site — Site-specific configuration hook" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/site.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/site.py This module is automatically imported during initialization. The automatic import can be suppressed using the interpreter’s-S option. Importing this module will append site..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/site.py This module is automatically imported during initialization. The automatic import can be suppressed using the interpreter’s-S option. Importing this module will append site..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>site — Site-specific configuration hook &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Custom Python Interpreters" href="custominterp.html" />
    <link rel="prev" title="inspect — Inspect live objects" href="inspect.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/site.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a><ul>
<li><a class="reference internal" href="#module-sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a></li>
<li><a class="reference internal" href="#module-usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a></li>
<li><a class="reference internal" href="#readline-configuration">Readline configuration</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a></li>
<li><a class="reference internal" href="#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="inspect.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="custominterp.html"
                          title="next chapter">Custom Python Interpreters</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/site.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="custominterp.html" title="Custom Python Interpreters"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="inspect.html" title="inspect — Inspect live objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-site">
<span id="site-site-specific-configuration-hook"></span><h1><a class="reference internal" href="#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> — Site-specific configuration hook<a class="headerlink" href="#module-site" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/site.py">Lib/site.py</a></p>
<hr class="docutils" />
<p><strong>This module is automatically imported during initialization.</strong> The automatic
import can be suppressed using the interpreter’s <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> option.</p>
<p id="index-0">Importing this module will append site-specific paths to the module search path
and add a few builtins, unless <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> was used.  In that case, this module
can be safely imported with no automatic modifications to the module search path
or additions to the builtins.  To explicitly trigger the usual site-specific
additions, call the <a class="reference internal" href="#site.main" title="site.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">main()</span></code></a> function.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Importing the module used to trigger paths manipulation even when using
<a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a>.</p>
</div>
<p id="index-1">It starts by constructing up to four directories from a head and a tail part.
For the head part, it uses <code class="docutils literal notranslate"><span class="pre">sys.prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code>; empty heads
are skipped.  For the tail part, it uses the empty string and then
<code class="file docutils literal notranslate"><span class="pre">lib/site-packages</span></code> (on Windows) or
<code class="file docutils literal notranslate"><span class="pre">lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code> (on Unix and macOS).  For each
of the distinct head-tail combinations, it sees if it refers to an existing
directory, and if so, adds it to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> and also inspects the newly
added path for configuration files.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Support for the “site-python” directory has been removed.</p>
</div>
<p>If a file named “pyvenv.cfg” exists one directory above sys.executable,
sys.prefix and sys.exec_prefix are set to that directory and
it is also checked for site-packages (sys.base_prefix and
sys.base_exec_prefix will always be the “real” prefixes of the Python
installation). If “pyvenv.cfg” (a bootstrap configuration file) contains
the key “include-system-site-packages” set to anything other than “true”
(case-insensitive), the system-level prefixes will not be
searched for site-packages; otherwise they will.</p>
<p id="index-2">A path configuration file is a file whose name has the form <code class="file docutils literal notranslate"><em><span class="pre">name</span></em><span class="pre">.pth</span></code>
and exists in one of the four directories mentioned above; its contents are
additional items (one per line) to be added to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>.  Non-existing items
are never added to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>, and no check is made that the item refers to a
directory rather than a file.  No item is added to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> more than
once.  Blank lines and lines beginning with <code class="docutils literal notranslate"><span class="pre">#</span></code> are skipped.  Lines starting
with <code class="docutils literal notranslate"><span class="pre">import</span></code> (followed by space or tab) are executed.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>An executable line in a <code class="file docutils literal notranslate"><span class="pre">.pth</span></code> file is run at every Python startup,
regardless of whether a particular module is actually going to be used.
Its impact should thus be kept to a minimum.
The primary intended purpose of executable lines is to make the
corresponding module(s) importable
(load 3rd-party import hooks, adjust <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code> etc).
Any other initialization is supposed to be done upon a module’s
actual import, if and when it happens.
Limiting a code chunk to a single line is a deliberate measure
to discourage putting anything more complex here.</p>
</div>
<p id="index-4">For example, suppose <code class="docutils literal notranslate"><span class="pre">sys.prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code> are set to
<code class="file docutils literal notranslate"><span class="pre">/usr/local</span></code>.  The Python X.Y library is then installed in
<code class="file docutils literal notranslate"><span class="pre">/usr/local/lib/python</span><em><span class="pre">X.Y</span></em></code>.  Suppose this has
a subdirectory <code class="file docutils literal notranslate"><span class="pre">/usr/local/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code> with three
subsubdirectories, <code class="file docutils literal notranslate"><span class="pre">foo</span></code>, <code class="file docutils literal notranslate"><span class="pre">bar</span></code> and <code class="file docutils literal notranslate"><span class="pre">spam</span></code>, and two path
configuration files, <code class="file docutils literal notranslate"><span class="pre">foo.pth</span></code> and <code class="file docutils literal notranslate"><span class="pre">bar.pth</span></code>.  Assume
<code class="file docutils literal notranslate"><span class="pre">foo.pth</span></code> contains the following:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span># foo package configuration

foo
bar
bletch
</pre></div>
</div>
<p>and <code class="file docutils literal notranslate"><span class="pre">bar.pth</span></code> contains:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span># bar package configuration

bar
</pre></div>
</div>
<p>Then the following version-specific directories are added to
<code class="docutils literal notranslate"><span class="pre">sys.path</span></code>, in this order:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>/usr/local/lib/pythonX.Y/site-packages/bar
/usr/local/lib/pythonX.Y/site-packages/foo
</pre></div>
</div>
<p>Note that <code class="file docutils literal notranslate"><span class="pre">bletch</span></code> is omitted because it doesn’t exist; the <code class="file docutils literal notranslate"><span class="pre">bar</span></code>
directory precedes the <code class="file docutils literal notranslate"><span class="pre">foo</span></code> directory because <code class="file docutils literal notranslate"><span class="pre">bar.pth</span></code> comes
alphabetically before <code class="file docutils literal notranslate"><span class="pre">foo.pth</span></code>; and <code class="file docutils literal notranslate"><span class="pre">spam</span></code> is omitted because it is
not mentioned in either path configuration file.</p>
<section id="module-sitecustomize">
<span id="sitecustomize"></span><h2><a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a><a class="headerlink" href="#module-sitecustomize" title="Link to this heading">¶</a></h2>
<p>After these path manipulations, an attempt is made to import a module named
<a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a>, which can perform arbitrary site-specific customizations.
It is typically created by a system administrator in the site-packages
directory.  If this import fails with an <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> or its subclass
exception, and the exception’s <a class="reference internal" href="exceptions.html#ImportError.name" title="ImportError.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a>
attribute equals to <code class="docutils literal notranslate"><span class="pre">'sitecustomize'</span></code>,
it is silently ignored.  If Python is started without output streams available, as
with <code class="file docutils literal notranslate"><span class="pre">pythonw.exe</span></code> on Windows (which is used by default to start IDLE),
attempted output from <a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a> is ignored.  Any other exception
causes a silent and perhaps mysterious failure of the process.</p>
</section>
<section id="module-usercustomize">
<span id="usercustomize"></span><h2><a class="reference internal" href="#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a><a class="headerlink" href="#module-usercustomize" title="Link to this heading">¶</a></h2>
<p>After this, an attempt is made to import a module named <a class="reference internal" href="#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a>,
which can perform arbitrary user-specific customizations, if
<a class="reference internal" href="#site.ENABLE_USER_SITE" title="site.ENABLE_USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">ENABLE_USER_SITE</span></code></a> is true.  This file is intended to be created in the
user site-packages directory (see below), which is part of <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> unless
disabled by <a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a>.  If this import fails with an <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> or
its subclass exception, and the exception’s <a class="reference internal" href="exceptions.html#ImportError.name" title="ImportError.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a>
attribute equals to <code class="docutils literal notranslate"><span class="pre">'usercustomize'</span></code>, it is silently ignored.</p>
<p>Note that for some non-Unix systems, <code class="docutils literal notranslate"><span class="pre">sys.prefix</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code> are
empty, and the path manipulations are skipped; however the import of
<a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a> and <a class="reference internal" href="#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a> is still attempted.</p>
</section>
<section id="readline-configuration">
<span id="rlcompleter-config"></span><h2>Readline configuration<a class="headerlink" href="#readline-configuration" title="Link to this heading">¶</a></h2>
<p>On systems that support <a class="reference internal" href="readline.html#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a>, this module will also import and
configure the <a class="reference internal" href="rlcompleter.html#module-rlcompleter" title="rlcompleter: Python identifier completion, suitable for the GNU readline library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">rlcompleter</span></code></a> module, if Python is started in
<a class="reference internal" href="../tutorial/interpreter.html#tut-interactive"><span class="std std-ref">interactive mode</span></a> and without the <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> option.
The default behavior is enable tab-completion and to use
<code class="file docutils literal notranslate"><span class="pre">~/.python_history</span></code> as the history save file.  To disable it, delete (or
override) the <a class="reference internal" href="sys.html#sys.__interactivehook__" title="sys.__interactivehook__"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.__interactivehook__</span></code></a> attribute in your
<a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a> or <a class="reference internal" href="#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a> module or your
<span class="target" id="index-5"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSTARTUP"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONSTARTUP</span></code></a> file.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Activation of rlcompleter and history was made automatic.</p>
</div>
</section>
<section id="module-contents">
<h2>Module contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="site.PREFIXES">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">PREFIXES</span></span><a class="headerlink" href="#site.PREFIXES" title="Link to this definition">¶</a></dt>
<dd><p>A list of prefixes for site-packages directories.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="site.ENABLE_USER_SITE">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">ENABLE_USER_SITE</span></span><a class="headerlink" href="#site.ENABLE_USER_SITE" title="Link to this definition">¶</a></dt>
<dd><p>Flag showing the status of the user site-packages directory.  <code class="docutils literal notranslate"><span class="pre">True</span></code> means
that it is enabled and was added to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>.  <code class="docutils literal notranslate"><span class="pre">False</span></code> means that it
was disabled by user request (with <a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a> or
<span class="target" id="index-6"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONNOUSERSITE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONNOUSERSITE</span></code></a>).  <code class="docutils literal notranslate"><span class="pre">None</span></code> means it was disabled for security
reasons (mismatch between user or group id and effective id) or by an
administrator.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="site.USER_SITE">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">USER_SITE</span></span><a class="headerlink" href="#site.USER_SITE" title="Link to this definition">¶</a></dt>
<dd><p>Path to the user site-packages for the running Python.  Can be <code class="docutils literal notranslate"><span class="pre">None</span></code> if
<a class="reference internal" href="#site.getusersitepackages" title="site.getusersitepackages"><code class="xref py py-func docutils literal notranslate"><span class="pre">getusersitepackages()</span></code></a> hasn’t been called yet.  Default value is
<code class="file docutils literal notranslate"><span class="pre">~/.local/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code> for UNIX and non-framework
macOS builds, <code class="file docutils literal notranslate"><span class="pre">~/Library/Python/</span><em><span class="pre">X.Y</span></em><span class="pre">/lib/python/site-packages</span></code> for macOS
framework builds, and <code class="file docutils literal notranslate"><em><span class="pre">%APPDATA%</span></em><span class="pre">\Python\Python</span><em><span class="pre">XY</span></em><span class="pre">\site-packages</span></code>
on Windows.  This directory is a site directory, which means that
<code class="file docutils literal notranslate"><span class="pre">.pth</span></code> files in it will be processed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="site.USER_BASE">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">USER_BASE</span></span><a class="headerlink" href="#site.USER_BASE" title="Link to this definition">¶</a></dt>
<dd><p>Path to the base directory for the user site-packages.  Can be <code class="docutils literal notranslate"><span class="pre">None</span></code> if
<a class="reference internal" href="#site.getuserbase" title="site.getuserbase"><code class="xref py py-func docutils literal notranslate"><span class="pre">getuserbase()</span></code></a> hasn’t been called yet.  Default value is
<code class="file docutils literal notranslate"><span class="pre">~/.local</span></code> for UNIX and macOS non-framework builds,
<code class="file docutils literal notranslate"><span class="pre">~/Library/Python/</span><em><span class="pre">X.Y</span></em></code> for macOS framework builds, and
<code class="file docutils literal notranslate"><em><span class="pre">%APPDATA%</span></em><span class="pre">\Python</span></code> for Windows.  This value is used to
compute the installation directories for scripts, data files, Python modules,
etc. for the <a class="reference internal" href="sysconfig.html#sysconfig-user-scheme"><span class="std std-ref">user installation scheme</span></a>.
See also <span class="target" id="index-7"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUSERBASE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUSERBASE</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="site.main">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#site.main" title="Link to this definition">¶</a></dt>
<dd><p>Adds all the standard site-specific directories to the module search
path.  This function is called automatically when this module is imported,
unless the Python interpreter was started with the <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> flag.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This function used to be called unconditionally.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="site.addsitedir">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">addsitedir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sitedir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">known_paths</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#site.addsitedir" title="Link to this definition">¶</a></dt>
<dd><p>Add a directory to sys.path and process its <code class="file docutils literal notranslate"><span class="pre">.pth</span></code> files.  Typically
used in <a class="reference internal" href="#module-sitecustomize" title="sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a> or <a class="reference internal" href="#module-usercustomize" title="usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a> (see above).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="site.getsitepackages">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">getsitepackages</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#site.getsitepackages" title="Link to this definition">¶</a></dt>
<dd><p>Return a list containing all global site-packages directories.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="site.getuserbase">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">getuserbase</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#site.getuserbase" title="Link to this definition">¶</a></dt>
<dd><p>Return the path of the user base directory, <a class="reference internal" href="#site.USER_BASE" title="site.USER_BASE"><code class="xref py py-data docutils literal notranslate"><span class="pre">USER_BASE</span></code></a>.  If it is not
initialized yet, this function will also set it, respecting
<span class="target" id="index-8"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUSERBASE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUSERBASE</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="site.getusersitepackages">
<span class="sig-prename descclassname"><span class="pre">site.</span></span><span class="sig-name descname"><span class="pre">getusersitepackages</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#site.getusersitepackages" title="Link to this definition">¶</a></dt>
<dd><p>Return the path of the user-specific site-packages directory,
<a class="reference internal" href="#site.USER_SITE" title="site.USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">USER_SITE</span></code></a>.  If it is not initialized yet, this function will also set
it, respecting <a class="reference internal" href="#site.USER_BASE" title="site.USER_BASE"><code class="xref py py-data docutils literal notranslate"><span class="pre">USER_BASE</span></code></a>.  To determine if the user-specific
site-packages was added to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> <a class="reference internal" href="#site.ENABLE_USER_SITE" title="site.ENABLE_USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">ENABLE_USER_SITE</span></code></a> should be
used.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="command-line-interface">
<span id="site-commandline"></span><h2>Command Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module also provides a way to get the user directories from the
command line:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>site<span class="w"> </span>--user-site
<span class="go">/home/<USER>/.local/lib/python3.11/site-packages</span>
</pre></div>
</div>
<p>If it is called without arguments, it will print the contents of
<a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> on the standard output, followed by the value of
<a class="reference internal" href="#site.USER_BASE" title="site.USER_BASE"><code class="xref py py-data docutils literal notranslate"><span class="pre">USER_BASE</span></code></a> and whether the directory exists, then the same thing for
<a class="reference internal" href="#site.USER_SITE" title="site.USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">USER_SITE</span></code></a>, and finally the value of <a class="reference internal" href="#site.ENABLE_USER_SITE" title="site.ENABLE_USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">ENABLE_USER_SITE</span></code></a>.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-site-user-base">
<span class="sig-name descname"><span class="pre">--user-base</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-site-user-base" title="Link to this definition">¶</a></dt>
<dd><p>Print the path to the user base directory.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-site-user-site">
<span class="sig-name descname"><span class="pre">--user-site</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-site-user-site" title="Link to this definition">¶</a></dt>
<dd><p>Print the path to the user site-packages directory.</p>
</dd></dl>

<p>If both options are given, user base and user site will be printed (always in
this order), separated by <a class="reference internal" href="os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>.</p>
<p>If any option is given, the script will exit with one of these values: <code class="docutils literal notranslate"><span class="pre">0</span></code> if
the user site-packages directory is enabled, <code class="docutils literal notranslate"><span class="pre">1</span></code> if it was disabled by the
user, <code class="docutils literal notranslate"><span class="pre">2</span></code> if it is disabled for security reasons or by an administrator, and a
value greater than 2 if there is an error.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0370/"><strong>PEP 370</strong></a> – Per user site-packages directory</p></li>
<li><p><a class="reference internal" href="sys_path_init.html#sys-path-init"><span class="std std-ref">The initialization of the sys.path module search path</span></a> – The initialization of <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a><ul>
<li><a class="reference internal" href="#module-sitecustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sitecustomize</span></code></a></li>
<li><a class="reference internal" href="#module-usercustomize"><code class="xref py py-mod docutils literal notranslate"><span class="pre">usercustomize</span></code></a></li>
<li><a class="reference internal" href="#readline-configuration">Readline configuration</a></li>
<li><a class="reference internal" href="#module-contents">Module contents</a></li>
<li><a class="reference internal" href="#command-line-interface">Command Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="inspect.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">inspect</span></code> — Inspect live objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="custominterp.html"
                          title="next chapter">Custom Python Interpreters</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/site.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="custominterp.html" title="Custom Python Interpreters"
             >next</a> |</li>
        <li class="right" >
          <a href="inspect.html" title="inspect — Inspect live objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code> — Site-specific configuration hook</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>