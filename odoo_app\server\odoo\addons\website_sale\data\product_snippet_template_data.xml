<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Templates for Dynamic Snippet -->
        <template id="dynamic_filter_template_product_product_add_to_cart" name="Classic Card">
            <t t-foreach="records" t-as="data" data-thumb="/website_sale/static/src/img/snippets_options/product_add_to_cart.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card h-100 w-100" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered overflow-hidden" t-att-href="record.website_url">
                        <img class="card-img-top o_img_product_square o_img_product_cover h-auto" loading="lazy" t-att-src="data['image_512']"
                            t-att-alt="record.display_name"/>
                    </a>
                    <i t-if="data.get('_latest_viewed')" class="fa fa-trash o_carousel_product_remove js_remove"/>
                    <div class="o_carousel_product_card_body card-body d-flex flex-wrap">
                        <a t-att-href="record.website_url" class="text-decoration-none d-block w-100">
                            <div class="h6 card-title mb-0" t-field="record.display_name"/>
                        </a>
                        <div class="mt-2">
                            <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                <t t-set="rating_avg" t-value="record.rating_avg"/>
                                <t t-set="rating_count" t-value="record.rating_count"/>
                            </t>
                        </div>
                        <div class="w-100 d-flex flex-wrap flex-md-column flex-lg-row align-items-center align-self-end justify-content-between mt-3">
                            <div class="py-2">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                            <div class="o_dynamic_snippet_btn_wrapper" t-if="record._website_show_quick_add()">
                                <button type="button" role="button" class="btn btn-primary js_add_cart ms-auto" title="Add to Cart">
                                    <i class="fa fa-fw fa-shopping-cart"/>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_view_detail" name="Classic Card - Detailed">
            <t t-foreach="records" t-as="data" data-number-of-elements="3" data-thumb="/website_sale/static/src/img/snippets_options/product_view_detail.svg">
                <t t-set="record" t-value="data['_record']" data-arrow-position="bottom"/>
                <div class="o_carousel_product_card card h-100 w-100" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered overflow-hidden" t-att-href="record.website_url">
                        <img class="card-img-top o_img_product_square o_img_product_cover h-auto" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                    <div class="o_carousel_product_card_body card-body d-flex flex-column justify-content-between">
                        <div class="card-title h5" t-field="record.display_name"/>
                        <div class="card-text flex-grow-1 text-muted h6" t-field="record.description_sale"/>
                        <div class="mt-2">
                            <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                <t t-set="rating_avg" t-value="record.rating_avg"/>
                                <t t-set="rating_count" t-value="record.rating_count"/>
                            </t>
                        </div>
                        <div class="d-flex justify-content-between flex-wrap flex-md-column flex-lg-row align-items-center align-self-end w-100 mt-2 pt-3 border-top">
                            <div class="pb-2">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                            <a class="btn btn-primary" t-att-href="record.website_url">
                                View product
                            </a>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_mini_image" name="Image only">
            <t t-foreach="records" t-as="data" data-number-of-elements="4" data-number-of-elements-sm="1" data-thumb="/website_sale/static/src/img/snippets_options/product_image_only.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="card h-100 border-0 w-100 rounded-0 bg-transparent" t-att-data-url="record.website_url">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered overflow-hidden" t-att-href="record.website_url">
                        <img class="card-img-top h-auto o_img_product_square o_img_product_cover rounded" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_mini_price" name="Image with price">
            <t t-foreach="records" t-as="data" data-thumb="/website_sale/static/src/img/snippets_options/product_image_with_price.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="card h-100 border-0 w-100 rounded-0 bg-transparent o_dynamic_product_hovered" t-att-data-url="record.website_url">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered overflow-hidden" t-att-href="record.website_url">
                        <img class="card-img-top h-auto o_img_product_square o_img_product_cover rounded" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                    <div class="o_carousel_product_card_body mt-2 d-flex justify-content-between">
                        <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                            <t t-set="rating_style_compressed" t-value="true"/>
                            <t t-set="rating_avg" t-value="record.rating_avg"/>
                            <t t-set="rating_count" t-value="record.rating_count"/>
                        </t>
                        <div class="ms-auto">
                            <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_mini_name" name="Image with name">
            <t t-foreach="records" t-as="data" data-thumb="/website_sale/static/src/img/snippets_options/product_image_with_name.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="card h-100 border-0 w-100 rounded-0 bg-transparent o_dynamic_product_hovered" t-att-data-url="record.website_url">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <a class="o_carousel_product_img_link overflow-hidden" t-att-href="record.website_url">
                        <img class="card-img-top h-auto o_img_product_square o_img_product_cover rounded" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                    <div class="h6 text-center mt-2 p-2" t-field="record.display_name"/>
                    <div class="text-center">
                        <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                            <t t-set="rating_avg" t-value="record.rating_avg"/>
                            <t t-set="rating_count" t-value="record.rating_count"/>
                        </t>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_centered" name="Centered Product">
            <t t-foreach="records" t-as="data" data-arrow-position="bottom" data-thumb="/website_sale/static/src/img/snippets_options/product_centered.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <a class="o_carousel_product_img_link position-absolute mx-auto" t-att-href="record.website_url">
                        <img class="card-img-top" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                    <div class="o_carousel_product_card_body card-body d-flex flex-column justify-content-between">
                        <div class="card-title h5 text-center" t-field="record.display_name"/>
                        <div class="text-center">
                            <div class="h5">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                            <div class="h6 mb-0">
                                <t t-if="is_view_active('website_sale.product_comment')">
                                    <t t-call="portal_rating.rating_widget_stars_static">
                                        <t t-set="rating_avg" t-value="record.rating_avg"/>
                                        <t t-set="rating_count" t-value="record.rating_count"/>
                                    </t>
                                </t>
                            </div>
                        </div>
                    </div>
                    <div class="o_carousel_product_card_footer d-flex align-items-center justify-content-center pb-4">
                        <a class="btn btn-primary d-block" t-att-href="record.website_url">
                            View Product
                        </a>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_borderless_1" name="Borderless Product n°1">
            <t t-foreach="records" t-as="data" data-thumb="/website_sale/static/src/img/snippets_options/product_borderless_1.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card bg-transparent w-100 card border-0">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered stretched-link" t-att-href="record.website_url">
                        <div class="overflow-hidden rounded">
                            <img class="card-img-top o_img_product_square o_img_product_cover h-auto" loading="lazy" t-att-src="data['image_512']"
                            t-att-alt="record.display_name"/>
                        </div>
                    </a>
                    <div class="o_carousel_product_card_body d-flex flex-wrap flex-column justify-content-between h-100 p-3">
                        <div class="h6 card-title" t-field="record.display_name"/>
                        <div>
                            <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                <t t-set="rating_avg" t-value="record.rating_avg"/>
                                <t t-set="rating_count" t-value="record.rating_count"/>
                            </t>
                            <div class="mt-2">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_borderless_2" name="Borderless Product n°2">
            <t t-foreach="records" t-as="data" data-thumb="/website_sale/static/src/img/snippets_options/product_borderless_2.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100 border-0 bg-transparent" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <a class="o_carousel_product_img_link o_dynamic_product_hovered" t-att-href="record.website_url">
                        <div class="overflow-hidden rounded">
                            <img class="card-img-top o_img_product_square o_img_product_cover h-auto" loading="lazy" t-att-src="data['image_512']"
                            t-att-alt="record.display_name"/>
                        </div>
                    </a>
                    <div class="o_carousel_product_card_body h-100 p-3 d-flex flex-column justify-content-between">
                        <div class="d-flex justify-content-between align-items-center flex-wrap mb-2">
                            <div class="h5 mb-0 me-4">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                            <div class="h6 mb-0">
                                <t t-if="is_view_active('website_sale.product_comment')">
                                    <t t-call="portal_rating.rating_widget_stars_static">
                                        <t t-set="rating_style_compressed" t-value="true"/>
                                        <t t-set="rating_avg" t-value="record.rating_avg"/>
                                        <t t-set="rating_count" t-value="record.rating_count"/>
                                    </t>
                                </t>
                            </div>
                        </div>
                        <div class="card-title h6 flex-grow-1 w-100 mt-2 mb-3" t-field="record.display_name"/>
                        <div class="text-end o_dynamic_snippet_btn_wrapper" t-if="record._website_show_quick_add()">
                            <button type="button" role="button" class="btn btn-primary js_add_cart w-100" title="Add to Cart">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_banner" name="Large Banner">
            <t t-foreach="records" t-as="data" data-number-of-elements="1" data-number-of-elements-sm="1" data-thumb="/website_sale/static/src/img/snippets_options/product_banner.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <div class="row flex-row-reverse">
                        <div class="col-lg-6 d-flex align-items-center justify-content-center justify-content-lg-end o_wrap_product_img position-relative">
                            <img class="img img-fluid position-absolute o_img_product_cover w-100 h-100" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                        </div>
                        <div class="col-lg-6 px-5 d-flex align-items-center">
                            <div class="o_carousel_product_card_body card-body p-5">
                                <div class="card-title h1" t-field="record.display_name"/>
                                <div class="d-flex align-items-center my-4">
                                    <div class="h4 mb-0 me-3">
                                        <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                                    </div>
                                    <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                        <t t-set="rating_avg" t-value="record.rating_avg"/>
                                        <t t-set="rating_count" t-value="record.rating_count"/>
                                    </t>
                                </div>
                                <div class="card-text text-muted" t-field="record.description_sale"/>
                                <div class="mt-4">
                                    <button t-if="record._website_show_quick_add()" type="button" role="button" class="btn btn-primary js_add_cart mt-1" title="Add to Cart">
                                        Add to Cart
                                    </button>
                                    <a class="btn btn-link me-1 mt-1" t-att-href="record.website_url">
                                        View Product
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_horizontal_card" name="Horizontal Card">
            <t t-foreach="records" t-as="data"
                data-number-of-elements="3"
                data-number-of-elements-sm="1"
                data-row-per-slide="2"
                data-arrow-position="bottom"
                data-extra-classes="o_carousel_multiple_rows"
                data-thumb="/website_sale/static/src/img/snippets_options/product_horizontal_card.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100 border-0 bg-light p-3" t-att-data-add2cart-rerender="data.get('_add2cart_rerender')">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <div class="row h-100 p-0">
                        <div class="col-lg-4 position-static">
                            <a class="stretched-link o_dynamic_product_hovered" t-att-href="record.website_url">
                                <img class="img img-fluid mx-auto o_img_product_square" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                            </a>
                        </div>
                        <div class="o_carousel_product_card_body col-lg-8 d-flex flex-column justify-content-between">
                            <div>
                                <div class="card-title h6" t-field="record.display_name"/>
                            </div>
                            <div>
                                <div class="mb-1">
                                    <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                        <t t-set="rating_avg" t-value="record.rating_avg"/>
                                        <t t-set="rating_count" t-value="record.rating_count"/>
                                    </t>
                                </div>
                                <div class="d-flex align-items-center flex-wrap">
                                    <div class="my-2">
                                        <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                                    </div>
                                    <div t-if="record._website_show_quick_add()" class="o_dynamic_snippet_btn_wrapper ms-auto">
                                        <button type="button" role="button" class="btn btn-primary js_add_cart" title="Add to Cart">
                                            <i class="fa fa-fw fa-shopping-cart"/>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        <template id="dynamic_filter_template_product_product_horizontal_card_2" name="Horizontal Card width covered image">
            <t t-foreach="records" t-as="data"
                data-row-per-slide="2"
                data-arrow-position="bottom"
                data-number-of-elements="2"
                data-number-of-elements-sm="1"
                data-extra-classes="o_carousel_multiple_rows"
                data-thumb="/website_sale/static/src/img/snippets_options/product_horizontal_card_2.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100 border-0 o_dynamic_product_hovered o_cc o_cc5">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <a class="stretched-link" t-att-href="record.website_url">
                        <img class="img img-fluid position-absolute w-100 h-100 o_img_product_cover" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                    </a>
                    <div class="o_carousel_product_card_body d-flex flex-column justify-content-between h-100 bg-black-50 p-3 position-relative">
                        <div class="mb-3">
                            <div class="card-title h5" t-field="record.display_name"/>
                            <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                    <t t-set="rating_avg" t-value="record.rating_avg"/>
                                    <t t-set="rating_count" t-value="record.rating_count"/>
                                </t>
                        </div>
                        <div class="card-text h6 flex-grow-1" t-field="record.description_sale"/>
                        <div class="d-flex justify-content-between align-items-center flex-wrap mt-3">
                            <div class="h5 mb-0 me-2">
                                <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                            </div>
                            <div t-if="record._website_show_quick_add()" class="o_dynamic_snippet_btn_wrapper">
                                <button type="button" role="button" class="btn btn-primary js_add_cart" title="Add to Cart">
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="dynamic_filter_template_product_product_card_group" name="Card group">
            <t t-foreach="records" t-as="data"
                data-row-per-slide="2"
                data-number-of-elements="2"
                data-number-of-elements-sm="1"
                data-arrow-position="bottom"
                data-extra-classes="o_card_group rounded"
                data-thumb="/website_sale/static/src/img/snippets_options/product_card_group.svg">
                <t t-set="record" t-value="data['_record']"/>
                <div class="o_carousel_product_card card w-100 rounded-0 border-top-0 border-start-0" t-att-data-url="record.website_url">
                    <div t-if="is_sample" class="h5 o_ribbon_right bg-primary text-uppercase">Sample</div>
                    <input type="hidden" name="product-id" t-att-data-product-id="record.id"/>
                    <div class="o_carousel_product_card_body card-body justify-content-between h-100 p-3">
                        <div class="row h-100">
                            <div class="col-8 d-flex flex-column">
                                <div class="card-title h5" t-field="record.display_name"/>
                                <div class="card-text h6 text-muted" t-field="record.description_sale"/>
                                <div class="d-flex justify-content-between align-items-center flex-wrap w-100 mt-auto">
                                    <div class="h5 text-primary mb-0 me-2">
                                        <t t-call="website_sale.price_dynamic_filter_template_product_product"/>
                                    </div>
                                    <t t-if="is_view_active('website_sale.product_comment')" t-call="portal_rating.rating_widget_stars_static">
                                        <t t-set="rating_avg" t-value="record.rating_avg"/>
                                        <t t-set="rating_count" t-value="record.rating_count"/>
                                    </t>
                                </div>
                            </div>
                            <div class="col-4 position-static">
                                <div class="overflow-hidden position-static">
                                    <a class="stretched-link o_dynamic_product_hovered" t-att-href="record.website_url">
                                        <img class="img img-fluid o_img_product_square o_img_product_cover h-auto" loading="lazy" t-att-src="data['image_512']" t-att-alt="record.display_name"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        <template id="price_dynamic_filter_template_product_product" name="Dynamic Product Filter Price">
            <t t-set="record_price" t-value="record._get_contextual_price_tax_selection()"/>
            <t t-if="not website.prevent_zero_price_sale or record_price">
                <span t-esc="record_price" class="fw-bold"
                      t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
                <del t-if="data.get('has_discounted_price')" class="text-danger ms-1 h6" style="white-space: nowrap;"
                     t-esc="data['list_price']"
                     t-options="{'widget': 'monetary', 'display_currency': website.currency_id}"/>
            </t>
            <t t-else="">
                <span t-field="website.prevent_zero_price_sale_text"/>
            </t>
        </template>

        <!-- Assets -->
        <record id="website_sale.s_dynamic_snippet_products_000_scss" model="ir.asset">
            <field name="name">Dynamic snippet products 000 SCSS</field>
            <field name="bundle">web.assets_frontend</field>
            <field name="path">website_sale/static/src/snippets/s_dynamic_snippet_products/000.scss</field>
        </record>
    </data>
</odoo>
