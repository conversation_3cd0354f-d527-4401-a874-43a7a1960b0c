<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">Ogone Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='provider_creation_warning']" position="after">
                <div class="alert alert-danger"
                     role="alert"
                     invisible="code != 'ogone'">
                    This provider is deprecated.
                    Consider disabling it and moving to <strong>Stripe</strong>.
                </div>
            </xpath>
            <group name="provider_credentials" position="inside">
                <group invisible="code != 'ogone'">
                    <field name="ogone_pspid" required="code == 'ogone' and state != 'disabled'"/>
                    <field name="ogone_userid" required="code == 'ogone' and state != 'disabled'"/>
                    <field name="ogone_password" required="code == 'ogone' and state != 'disabled'" password="True"/>
                    <field name="ogone_shakey_in" required="code == 'ogone' and state != 'disabled'" password="True"/>
                    <field name="ogone_shakey_out" required="code == 'ogone' and state != 'disabled'" password="True"/>
                    <field name="ogone_hash_function" required="code == 'ogone' and state != 'disabled'" groups="base.group_no_one"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
