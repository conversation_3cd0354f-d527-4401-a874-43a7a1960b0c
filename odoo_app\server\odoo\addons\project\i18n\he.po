# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# שהאב <PERSON>י<PERSON>ן <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# NoaFarkash, 2023
# david <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# MichaelHadar, 2024
# <PERSON><PERSON> BLONDER <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Jonathan Spier, 2024
# yael terner, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid " - Tasks by Deadline"
msgstr "- משימות עם תאריכי יעד"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "#משתפי פעולה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# דירוגים"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "מס' משימות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__done_task_count
msgid "# of Done Tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
#, python-format
msgid "# of Tasks"
msgstr "מס' משימות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s /%(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Burndown Chart"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Milestones"
msgstr "אבני דרך של %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Rating"
msgstr "דירוג של %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Tasks Analysis"
msgstr "ניתוח משימות של %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Updates"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(בשל"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(עדכון אחרון של הפרויקט),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
" <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- הגיע ל"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>גרור </b>את הכרטיס המשימה כדי לשנות את השלב שלה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br><br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Thank you for your enquiry.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> פרטי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on "
"projects\"/><b>דירוגי לקוחות </b>מושבתים בפרויקט(ים) הבאים:<br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/> בחזרה למצב עריכה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">שלב</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\"> משתמשים מוקצים</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">לקוח</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> בוצע</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\">משימות</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Closed</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text order-2\">תת משימות</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"משתפי פעולה\n"
"</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">דירוג אחרון</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">משימת אב</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr "<span class=\"text-muted o_row ps-1 pb-3\">שלח בקשת דירוג:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>דו\"חות</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>תצוגה</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">קבצים מצורפים</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>תאריך יעד:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>אבני דרך:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>פרויקט:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>אבני דרך</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"מילון פייתון שיוערך כדי לספק ערכי ברירת מחדל בעת יצירת רשומות חדשות לכינוי "
"זה."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"לא ניתן לבחור שותף יותר מפעם אחת בגישה לשיתוף הפרויקט. אנא הסר כפילו(יות) "
"ונסה שוב."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr "לא ניתן לקשר שלב אישי לפרויקט מכיוון שהוא גלוי רק למשתמש המתאים לו."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "למשימה פרטית לא יכול להיות הורה."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "משימת משנה לא יכולה להיות חוזרת."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "תגית עם אותו שם כבר קיים."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "למשימה יכולה להיות רק שלב אישי יחיד לכל משתמש."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "קבל הודעות דוא\"ל מ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_instruction_message
msgid "Access Instruction Message"
msgstr "הוראות גישה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "מצב גישה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "אזהרת גישה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "פעיל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "פעילויות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "תכניות פעילויות "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Add Milestone"
msgstr "הוסף אבן דרך"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_task_plan
msgid "Add a new plan"
msgstr "הוסף תכנית חדשה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "הוסף הערה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"הוסף עמודות כדי לארגן את המשימות שלך בתוך <b>שלבים</b><i>, למשל. חדש - "
"בתהליך - בוצע.</i>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "הוסף אנשי קשר כדי לשתף את הפרויקט..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "הוסף פרטים על משימה זו..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "הוסף תוכן נוסף שיוצג בדוא\"ל"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "שלח את לוח הזמנים לעובדים שלך ברגע שהוא מוכן."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "פיתוח תוכנה זריז (Agile)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "כינוי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "כינוי אבטחה של איש קשר"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "שם-מתחם (דומיין)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "כינוי שם (דומיין)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "קידומת מייל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "שם כינוי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "סטטוס"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "סטטוס כינוי הוערך בהודעה האחרונה שהתקבלה."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "מודל בעל כינוי"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "הכל"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "כל המשימות"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "כל המשתמשים הפנימיים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "זמן מוקצה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "זמן מוקצה:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_rating
msgid "Allow Customer Ratings"
msgstr "אפשר דירוגי לקוחות"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "חשבון אנליטי"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__analytic_plan_id
msgid "Analytic Plan"
msgstr "כלל ברירת מחדל אנליטיים"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task and its timesheets are linked.\n"
"Track the costs and revenues of your task by setting its analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"By default, the analytic account of the project is set. However, it can be changed on each task individually if necessary."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "ניתוח נתונים"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"נתח באיזו מהירות הצוות שלך מסיים את משימות הפרויקט שלך ובדוק אם הכל מתקדם "
"בהתאם לתוכנית."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "נתח את התקדמות הפרויקטים שלך ואת הביצועים של העובדים שלך."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "אושר"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "שלב ארכיון"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "בארכיון"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "האם אתה בטוח שברצונך להמשיך?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "האם אתה בטוח שברצונך למחוק את השלבים האלה?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "האם אתה בטוח שברצונך למחוק רשומה זו?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "חץ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "סמל (אייקון) חץ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "הרכבה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Assign a responsible to your task"
msgstr "הקצה אחראי למשימה שלך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "משויך"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "משימות ששויכו"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "משויכת ל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignees"
msgstr "מוקצה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "תאריך שיוך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "תאריך שיוך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "תאריך שיוך"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "בסיכון"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr ""
"צרף את כל המסמכים או הקישורים למשימה ישירות, כדי לרכז את כל מידע המחקר."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "קבצים מצורפים שלא מגיעים מהודעה."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "מחבר"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "יצירת משימות אוטומטיות לפעילויות רגילות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "סטטוס תצוגה אוטומטי"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Avatar"
msgstr "אווטר"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
#, python-format
msgid "Average Rating"
msgstr "דירוג ממוצע"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "דירוג ממוצע (%)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "דירוג ממוצע: לא מרוצה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "דירוג ממוצע: בסדר"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "דירוג ממוצע: מרוצה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "פיגור"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "יתרה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Billed"
msgstr "חויב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "חסום"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "חסום"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "חסום על ידי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "חוסם"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocking Tasks"
msgstr "חסימת משימות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "סיעור מוחות"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "תרשים ניצול"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__can_be_marked_as_done
msgid "Can Be Marked As Done"
msgstr "ניתן לסמן כבוצע"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Canceled"
msgstr "בוטל"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "שינויים מבוקשים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr "בחר <b>שם</b> משימה <i>(למשל עיצוב אתר, רכישת מוצרים...)</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "סקירת לקוח"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "נסגר ב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_subtask_count
msgid "Closed Sub-tasks Count"
msgstr "כמות משימות משנה סגורות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__closed_task_count
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_count
msgid "Closed Task Count"
msgstr "כמות משימות סגורות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_percentage
msgid "Closed Task Percentage"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "משימות סגורות"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid ""
"Collaborate efficiently with key stakeholders by sharing with them the "
"Kanban view of your tasks. Collaborators will be able to edit parts of tasks"
" and send messages."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "משתף פעולה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
msgid "Collaborators"
msgstr "משתפי פעולה"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "משתפי פעולה בפרויקט שיתופי"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "צבע"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "אינדקס צבעים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "היסטוריית התקשרות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "חברה"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "תצורה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "הגדר שלבים"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "אשר"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#, python-format
msgid "Confirmation"
msgstr "אישור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Congratulations, you are now a master of project management."
msgstr "מזל טוב, עכשיו אתה מאסטר בניהול פרויקטים."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "ייעוץ"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "איש קשר"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "המר משימה"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
#, python-format
msgid "Convert to Task/Sub-Task"
msgstr "המר למשימה/תת- משימה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "קופירייטינג"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Costs"
msgstr "עלויות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "תמונת הכריכה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr "צור <b>פעילויות</b>ת כדי להגדיר לעצמך מטלות או לתזמן פגישות."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "תאריך יצירה"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "צור פרויקט"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "צור שלב חדש בצינור המשימות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "צור פרוייקט"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"צור פרויקטים כדי לארגן את המשימות שלך ולהגדיר זרימת עבודה שונה עבור כל "
"פרויקט."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""
"צור פרויקטים כדי לארגן את המשימות שלך. הגדר זרימת עבודה שונה עבור כל פרויקט."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "צור משימות על ידי שליחת אימייל אל"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "צור משימות על ידי שליחת אימייל לכתובת הדוא\"ל של הפרויקט שלך."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "נוצר ב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "הפרויקט הנוכחי של המשימה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "השלב הנוכחי של משימה זו"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr "כרגע זמין לכל מי שצופה במסמך זה, לחיצה תגביל גישה לעובדים בלבד."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"כרגע מוגבל לעובדים פנימיים, לחץ כדי להפוך אותו לזמין לכל מי שצופה במסמך זה."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "הודעה מותאמת אישית להודעות שגויות"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Customer"
msgstr "לקוח"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Customer Email"
msgstr "דוא\"ל לקוח"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "משוב לקוח"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "כתובת אתר של פורטל לקוחות"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "דירוגי לקוח"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "סטטוס דירוג לקוחות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Customers will be added to the followers of their project and tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "יומי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "Date and Stage"
msgstr "תאריך ושלב"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"התאריך שבו פרויקט זה מסתיים. מסגרת הזמן שהוגדרה בפרויקט נלקחת בחשבון בעת "
"​​הצגת התכנון שלו."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"התאריך שבו משימה זו הוקצתה לאחרונה (או בוטלה). בהתבסס על זה, אתה יכול לקבל "
"נתונים סטטיסטיים על הזמן שלוקח בדרך כלל להקצות משימות."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "ימים"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "מס' ימים עד לתאריך היעד"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
#, python-format
msgid "Deadline"
msgstr "תאריך יעד"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "יקר"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "ערכי ברירת מחדל"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"הגדר את הצעדים שישמשו בפרויקט מתוך \n"
"                יצירת המשימה, עד לסגירת המשימה או הגיליון.\n"
"                תוכל להשתמש בשלבים אלה כדי לעקוב אחר ההתקדמות\n"
"                בפתרון משימה או בעיה."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr "הגדירו את השלבים שהפרויקטים שלכם עוברים מהיצירה ועד ההשלמה."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete"
msgstr "מחק"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "מחק את אבן דרך"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#, python-format
msgid "Delete Project Stage"
msgstr "מחק את שלב הפרויקט"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
#, python-format
msgid "Delete Stage"
msgstr "מחק שלב"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#, python-format
msgid ""
"Deleting a task will also delete its associated sub-tasks. If you wish to "
"preserve the sub-tasks, make sure to unlink them from their parent task "
"beforehand. Are you sure you want to proceed?"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"ספק את השירותים שלך באופן אוטומטי כאשר אבן דרך מושגת על ידי קישורו לפריט "
"הזמנת מכירות."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "נמסר"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "משימות תלויות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "תיאור"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "תיאור כדי לספק מידע והקשר נוסף על פרויקט זה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "עיצוב"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "קבע את סדר ביצוע המשימות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "פיתוח"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "תמצית"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "שיווק דיגיטלי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "אזהרת דירוג מושבתת"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "בטל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "תצוגת מצב גישה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_in_project
msgid "Display In Project"
msgstr "תצוגה בפרויקט"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_parent_task_button
msgid "Display Parent Task Button"
msgstr "הצג את לחצן משימת הורה"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "אין לך גישה, דלג על נתונים אלה עבור תקציר דוא\"ל של המשתמשים"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "בוצע"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "טיוטה"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
msgid "Edit"
msgstr "ערוך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "עריכה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_email
msgid "Email"
msgstr "דוא\"ל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "קידומת מייל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "תבנית דוא\"ל"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "עותק דוא\"ל"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "דומיין דוא\"ל, למשל, 'example.com' ב-'<EMAIL>'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "מיילים שנשלחו אל"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Employees Only"
msgstr "לעובדים בלבד"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "תאריך סיום"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "תאריך סיום"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "שגיאה! לא ניתן ליצור היררכיה רקורסיבית של משימות."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr "כל אחד יכול להציע רעיונות, והעורך מסמן את הטובים ביותר בתור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Expected"
msgstr "צפוי"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "ניסוי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "תאריך תפוגה"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "חיצוני"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "מידע נוסף"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "מועדף"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "פרויקטים אהובים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "מסמך סופי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "מקופל בקנבן"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "עקוב ורשום הערות במשימות מהפרויקטים שלך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "עקוב אחר ההתקדמות בפרויקטים שלך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"עקוב אחר פרויקט זה כדי לעקוב באופן אוטומטי אחר האירועים הקשורים למשימות "
"ובעיות של פרויקט זה."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "במעקב"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "תמיד"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "תדירות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"קבל תמונת מצב של הפרויקט שלך ושתף את ההתקדמות שלו עם בעלי עניין מרכזיים."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "אפשר גישה לפורטל"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant portal users access to your project or tasks by adding them as "
"followers. Customers automatically get access to their tasks in their "
"portal."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "קבץ לפי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "למסור את"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "פרצוף שמח"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__has_late_and_unreached_milestone
msgid "Has Late And Unreached Milestone"
msgstr "אבן דרך מאוחרת ולא הושגה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "גבוה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "היסטוריה"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "שעות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "איך הולך הפרויקט הזה?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "מזהה"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"מזהה של רשומת האב המחזיקה בכינוי (דוגמה: פרויקט המחזיק בכינוי ליצירת "
"המשימות)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "רעיונות"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"אם מוגדר, תוכן זה יישלח אוטומטית למשתמשים לא מורשים במקום להודעת ברירת "
"המחדל."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כ- לא נכון, הוא יאפשר להסתיר את הפרויקט מבלי להסיר אותו."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "בתהליך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "בפיתוח"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "דואר נכנס"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "פנימי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal Note"
msgstr "הערה פנימית"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"דוא\"ל פנימי המשויך לפרויקט זה. הודעות דוא\"ל נכנסות מסונכרנות אוטומטית עם "
"משימות (או בעיות אפשריות אם מודול Tracker Issue מותקן)."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "הערות פנימיות מוצגות רק למשתמשים פנימיים."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "ערך לא חוקי: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Invite Collaborators"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "להזמין אנשים"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "משתמשים פנימיים שהוזמנו (פרטי)"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "משתמשי פורטל מוזמנים וכל המשתמשים הפנימיים (פומבי)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Invoiced"
msgstr "חויב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "האם תאריך היעד עבר?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "האם תאריך היעד עתידי?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_milestone_exceeded
msgid "Is Milestone Exceeded"
msgstr "אבן דרך חריגה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Is toggle mode"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "פרסם גירסה"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "מדד ערך פתוח למשימת פרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "חודש אחרון"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Last Stage Update"
msgstr "עדכון השלב האחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "עדכון אחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "צבע עדכון אחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "סטטוס עדכון אחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "עודכן לאחרונה ב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "אבני דרך שעבר תאריכם"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "מאוחר יותר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Leave a comment"
msgstr "רשום תגובה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "בוא ניצור את <b>הפרויקט</b>הראשון שלך."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>stage</b>."
msgstr "בוא ניצור את <b>השלב</b> הראשון."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "בוא ניצור את <b>המשימה</b> הראשונה שלך."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your second <b>stage</b>."
msgstr "בוא ניצור את <b>השלב </b>השני. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"בוא נחזור ל<b>תצוגת קנבן</b>כדי לקבל סקירה כללית של המשימות הבאות שלך."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "בוא נתחיל לעבוד על המשימה שלך."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "בואו נחכה שהלקוחות שלכם יבואו לידי ביטוי."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Link"
msgstr "קישור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "חי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "זיהוי נכנסות מבוסס חלק מקומי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "עיצוב לוגו"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Look for the"
msgstr "חפש את ה"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "נמוך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "קבצים מצורפים עיקריים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "ייצור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Margin"
msgstr "מרווח"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "Mark as done"
msgstr "סמן כבוצע"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "מקורות חומרים"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "אנשי צוות"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "תפריט"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "הודעה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Milestone"
msgstr "אבן דרך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "ספירת אבני דרך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count_reached
msgid "Milestone Count Reached"
msgstr "ספירת אבן הדרך הגיעה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_project_task__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Milestones"
msgstr "אבני דרך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "ערבוב"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "חודשים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "תאריך היעד האחרון לפעילת הזאת"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "המועדפים שלי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "הפרוייקטים שלי"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "המשימות שלי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "העדכונים שלי"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "שם"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "שם חתוך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "שם של המשימות"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "פרצוף ניטרלי"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#, python-format
msgid "New"
msgstr "חדש"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "תכונה חדשה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#, python-format
msgid "New Milestone"
msgstr "אבן דרך חדשה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "הזמנות חדשות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
#, python-format
msgid "New Project"
msgstr "פרויקט חדש"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "פרויקטים חדשים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "בקשה חדשה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid "New Task"
msgstr "משימה חדשה"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "החדש ביותר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Next"
msgstr "הבא"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "הפעילות הבאה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "לקוח חסר"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "לא נמצאה אבן דרך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "No Subject"
msgstr "ללא נושא"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "לא נמצאו סוגי פעילות. בואו נייצר אחד!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid "No collaborators found"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "אין עדיין דירוג לקוחות"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "אין מידע עדיין"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "לא נמצאו פרויקטים. בואו ניצור אחד!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "לא נמצאו שלבים. בואו ניצור אחת!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "לא נמצאו תגיות. בואו ניצור אחת!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "לא נמצאו משימות. בואו ניצור אחת!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "לא נמצאו עדכונים. בואו ניצור אחת!"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Not Implemented."
msgstr "לא מיושם."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "הערה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__projects_count
msgid "Number of Projects"
msgstr "מספר פרויקטים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "מספר המשימות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "מספר מסמכים מצורפים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "לא לפי המסלול"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "עיצוב משרד"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "ספרינט ישן הושלם"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "מושהה"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "לפי התכנית"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "פעם בחודש"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "אופס! משהו השתבש. נסה לטעון מחדש את הדף ולהיכנס."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__open_task_count
msgid "Open Task Count"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "משימות פתוחות"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Operation not supported"
msgstr "הפעולה אינה נתמכת"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"מזהה אפשרי של שרשור (רשומה) שאליו יצורפו כל ההודעות הנכנסות, גם אם לא השיבו "
"אליו. אם מוגדר, הדבר יבטל את יצירת הרשומות החדשות לחלוטין."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Organize priorities amongst orders using the"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "אחר"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "משימות שעברו"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "רעיונות לדפים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "מודל אב"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "מזהה רשומת שרשור אב "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
#, python-format
msgid "Parent Task"
msgstr "משימת אב"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"מודל אב המחזיק בכינוי. המודל המחזיק במזהה לכינוי אינו בהכרח המודל שניתן על "
"ידי alias_model_id (דוגמה: project (parent_model) ומשימה (model))"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "אחוז הדירוגים השמחים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "שלב אישי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "סטטוס שלב אישי"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
msgid "Personal Stages"
msgstr "שלבים אישיים"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "שלב המשימות האישיות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "תאריך מתוכנן"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr "אנא הסר משימות קיימות בפרויקט המקושר לחשבונות שברצונך למחוק."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "הפקת פודקאסט והפקת וידאו"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"מדיניות שליחת הודעה במסמך באמצעות שער הדואר.\n"
"- כולם: כולם יכולים לשלוח\n"
"- לקוחות/ספקים: רק לקוחות/ספקים מאומתים\n"
"- עוקבים: רק עוקבים של המסמך הקשור או חברים בערוצים הבאים\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "כתובת גישה לפורטל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "שמות משתמש בפורטל"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "משתמשי הפורטל יוסרו מהעוקבים אחר הפרויקט ומשימותיו."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Previous"
msgstr "קודם"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Prioritize your tasks by marking important ones using the"
msgstr "תיעדוף את המשימות שלך על ידי סימון חשיבות באמצעות ה"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#, python-format
msgid "Priority"
msgstr "קְדִימוּת"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr "הצגת אזהרה "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
#, python-format
msgid "Private"
msgstr "פרטי"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "משימות פרטיות "

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project for "
"the task to gain access to this feature."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Profitability"
msgstr "רווחיות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "התקדמות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "אחוז התקדמות"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
#, python-format
msgid "Project"
msgstr "פרויקט"

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "משתפי פעולה בפרויקט"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "כמות פרויקטים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "מנהל פרויקט"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "אבן דרך בפרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "שם הפרויקט"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "סטטוס דירוג הפרויקט"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "פרויקט משותף"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "שיתוף פרויקטים"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "שיתוף פרויקטים: משימה"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "שלב הפרויקט"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "שלב הפרוייקט השתנה"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "אשף מחיקת שלב הפרויקט"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "שלבי הפרויקט"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "תגיות פרויקט"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr " אשף המחיקה של שלב משימת הפרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "משימות הפרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "כותרת פרויקט "

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "עדכון פרוייקט"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "עדכוני פרוייקט"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "נראות הפרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "תיאור פרויקט..."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "משימות הפרויקט"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "פרויקט: פרויקט הושלם "

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr ""

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "פרויקט: שלח דירוג"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "פרויקט: בקשת דירוג משימה"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_tags__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_res_partner__project_ids
#: model:ir.model.fields,field_description:project.field_res_users__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#, python-format
msgid "Projects"
msgstr "פרויקטים"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr "פרויקטים מכילים משימות באותו נושא, ולכל אחד יש לוח מחוונים משלו."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "מאפיינים"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "פורסם"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Published on"
msgstr "פורסם ב"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "הוצאה לאור"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "רבעוני"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""
"בדוק במהירות את הסטטוס של משימות לאישורים או בקשות לשינוי, וזהה את אלו "
"הממתינות עד לפתרון התלות באמצעות סמל שעון החול."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "דירוג"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "דירוג (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "דירוג טקסט ממוצע"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "תבנית דוא\"ל דירוג"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "תדירות דירוג"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "דירוג משוב אחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "דירוג תמונה אחרונה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "דירוג ערך אחרון"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "תאריך יעד לבקשת דירוג"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "שביעות רצון דירוג"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "סטטוס דירוג"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#, python-format
msgid "Rating Value (/5)"
msgstr "ערך דירוג (/5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "כמות דירוגים"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "הגיע"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "תאריך הגעה"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "קריאה בלבד"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "קליטה של ​​{{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "נמענים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "מזהה רשומת שרשור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "הקלטה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "חזרה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "אירוע חוזר"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "משימות חוזרות "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "נדחה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "מסמכים מקשורים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "מזהה מסמך קשור"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "דגם מסמך קשור"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "שיפוצים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "חזור כל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "חזור על יחידה"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "דו\"חות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "מחקר"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "מחקר ופיתוח"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "פרויקט מחקר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "חוקר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "הקצאת משאבים"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Revenues"
msgstr "הכנסות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "פרצוף עצוב"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to drag images in description"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to paste images in description"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Schedule your activity once it is ready."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "תסריט"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "חפש <span class=\"nolabel\"> (בתוכן)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "חפש פרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "עדכון חיפוש"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "חפש בהכל"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "חפש במי שאחראי"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Customer"
msgstr "חפש בלקוח"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "חפש בהודעות"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Milestone"
msgstr "חפש באבן דרך"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "חפש בפריוריטי"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "חפש בפרויקט"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "חפש בהפנייה"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "חפש בשלבים"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "חפש בסטטוס"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#, python-format
msgid "Send"
msgstr "שלח"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "שלח דוא\"ל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "רצף"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "הגדר תמונת כריכה/כיסוי"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
#, python-format
msgid "Set Status"
msgstr "הגדר סטטוס"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "הגדר תבנית דוא\"ל דירוג של שלבים"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
#, python-format
msgid "Set priority as %s"
msgstr "הגדר עדיפות כ %s"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Set state as..."
msgstr ""

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"הגדר תבנית זו בשלב של פרויקט כדי להפוך דוא\"ל לאוטומטי כאשר המשימות מגיעות "
"לשלבים"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "הגדרות"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "שתף"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "שיתוף ניתן לעריכה"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "שתף את הפרויקט"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Read-only"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "הצג את הפרויקט בלוח המחוונים"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "מאז"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "פיתוח תוכנה"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "מצטער. לא ניתן להגדיר משימה כמשימת האב שלה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "מפרט"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "ספרינט פיגור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "ספרינט הושלם"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "ספרינט בתהליך"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Stage"
msgstr "שלב"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "השלב השתנה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "אחראי שלב"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "השלב השתנה"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Stage: %s"
msgstr "שלב :%s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stages_active
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "שלב פעיל"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stage_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "שלבים למחיקה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "תאריך תחילה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "מדינה"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "סטטוס"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Status Update - "
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "סטטוס זמן"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "ספירת משימות משנה"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "משימות משנה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_allocated_hours
msgid "Sub-tasks Allocated Time"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "נשלח ל"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "תקציר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "הדפסת חולצות"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "תגיות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Task"
msgstr "משימה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "פעילויות משימה"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "משימה אושרה "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Canceled"
msgstr "משימה בוטלה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
#: model:ir.model.fields,field_description:project.field_project_update__task_count
msgid "Task Count"
msgstr "כמות משימות"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "המשימה נוצרה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "תלות בין משימות"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "משימה בוצעה"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "משימה בביצוע"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "יומני פעילות"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_task_plan
msgid "Task Plans"
msgstr "תוכניות משימה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "מאפייני משימה"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "דירוג משימה"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "הישנות המשימות"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "שלב המשימה"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "שלב המשימה השתנה"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "שלבי המשימות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "שם המשימה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "שם משימה..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "משימה ממתינה"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "משימה אושרה"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task canceled"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "משימה בוצעה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "משימה:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "משימה: בקשת דירוג"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_milestone__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_project_tags__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Tasks"
msgstr "משימות"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "ניתוח נתוני משימות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "ניהול משימות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "שלבי המשימות"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
#, python-format
msgid "Tasks in Recurrence"
msgstr "משימות בחזרה מחודשת"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "בדיקות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "The Burndown Chart must be grouped by"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "השלב האישי של המשתמש הנוכחי."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "שלב המשימה האישית של המשתמש הנוכחי"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "הקובץ המצורף לא קיים או שאין לך הרשאות אליו."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The end date should be in the future"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "אבן הדרך הבאה נוספה:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "אבן הדרך הבאה נוספה:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The interval should be greater than 0"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"המודל (סוג מסמך Odoo) שאליו הכינוי הזה תואם. כל דוא\"ל נכנס שלא יענה לרשומה "
"קיימת יביא ליצירת רשומה חדשה של מודל זה (למשל משימת פרויקט)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"שם כינוי הדוא\"ל, למשל 'עבודות' אם ברצונך לקבל הודעות דוא\"ל ל "
"<<EMAIL>>"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"לא ניתן לשתף את המשימה עם הנמענ(ים) מכיוון שפרטיות הפרויקט מוגבלת מדי. הגדר "
"את פרטיות הפרויקט כ'גלויה למעקב לקוחות 'על מנת להנגיש אותו לנמענ(ים)."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "תאריך התחלת הפרויקט חייב להיות לפני תאריך הסיום."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "The search does not support the %s operator or %s value."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr "המשימה והשותף המשויך חייבים להיות מקושרים לאותה חברה."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"לא ניתן לשתף את המשימה עם הנמענ(ים) מכיוון שפרטיות הפרויקט מוגבלת מדי. הגדר "
"את פרטיות הפרויקט כ'גלויה למעקב לקוחות 'על מנת להנגיש אותו לנמענ(ים)."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "The view must be grouped by date and by stage_id"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "אין תגובות לעת עתה."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "אין פרויקטים."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "אין דירוגים לפרויקט זה כרגע"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "אין משימות."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "There is nothing to report."
msgstr "אין מה לדווח."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read-only "
"mode on your website. This includes leads/opportunities, quotations/sales "
"orders, purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "חודש נוכחי"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "השבוע"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is associated with %s, whereas the selected stage belongs to "
"%s. There are a couple of options to consider: either remove the company "
"designation from the project or from the stage. Alternatively, you can "
"update the company information for these records to align them under the "
"same company."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is not associated to any company, while the stage is associated"
" to %s. There are a couple of options to consider: either change the "
"project's company to align with the stage's company or remove the company "
"designation from the stage"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "This task is blocked by another unfinished task"
msgstr "משימה זו חסומה ע\"י משימה אחרת שלא הסתיימה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr "זה יאחסן את השלבים ואת כל המשימות שהם מכילים מהפרויקטים הבאים:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"אלה מייצגים את הקטגוריות השונות של דברים שעליך לעשות (למשל \"התקשר\" או "
"\"שלח אימייל\")."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "ניהול זמן"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "טיפ: צור משימות מאימיילים נכנסים"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#, python-format
msgid "Title"
msgstr "תיאור"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Bill"
msgstr "לחיוב"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr " לביצוע"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Invoice"
msgstr "לחיוב"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "להדפיס"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"כדי לבצע דברים, השתמש בפעילויות ובסטטוס במשימות.<br>\n"
"צ'אט בזמן אמת או בדוא\"ל כדי לשתף פעולה ביעילות."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "היום"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Total"
msgstr "סה\"כ"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "עקוב אחר שביעות רצון הלקוחות במשימות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr "עקוב אחר נקודות התקדמות עיקריות שיש להגיע אליהן כדי להשיג הצלחה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr "עקוב אחר נקודות התקדמות עיקריות שיש להגיע אליהן כדי להשיג הצלחה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your project with analytic accounting. Select the"
" analytic plan for new projects:"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your projects. Any project, its tasks and "
"timesheets are linked to an analytic account and any analytic account "
"belongs to a plan."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "עקוב אחר התקדמות הפרויקטים שלך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "עקוב אחר הזמן המושקע בפרויקטים ומשימות"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "פעמיים בחודש"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Two tasks cannot depend on each other."
msgstr "שתי משימות לא יכולות להיות תלויות זו בזו. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid "Unarchive Projects"
msgstr "הוצא פרויקטים מהארכיון"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "Unarchive Tasks"
msgstr "הוצא משימות מהארכיון"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Unassigned"
msgstr "לא משויך"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "חשבון אנליטי לא ידוע"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "עד"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "עדכן"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "עדכון נוצר"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "השתמש בדירוג בפרויקט"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "השתמש במשימות חוזרות"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "השתמש ב-שלבים (Stages) בפרויקט"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "השתמש במשימות תלויות זו בזו"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "השתמש במשימות כ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "השתמש בזה לפרויקט שלי"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "השתמש בתגיות כדי לסווג את המשימות שלך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use the"
msgstr "השתמש ב-"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "משתמש"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "תצוגה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "ראה משימה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "ראה משימות"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "יוצג ל:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Visible"
msgstr "גלוי"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
#, python-format
msgid "Waiting"
msgstr "ממתין"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr "רוצה דרך טובה יותר <b>לנהל את הפרויקטים שלך</b>? <i>זה מתחיל כאן.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "עיצוב מחדש של אתר האינטרנט"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "שבועי"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "שבועות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in edit mode, you are giving people access to the kanban and list views of your tasks.\n"
"                    Collaborators will be able to view and modify part of the tasks' information."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in read-only mode, you are giving people access to the tasks in their portal.\n"
"                    These people will be able to view the tasks, but not edit them."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "מס' ימי עבודה לשיוך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "מס' ימי עבודה לסגירה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "שעות עבודה לשיוך"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "שעות עבודה לסגירה"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "זמן עבודה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "זמן עבודה לשיוך"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "זמן עבודה לסגירה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Write a message..."
msgstr "כתוב הודעה..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "כתיבה"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "שנתי"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "שנים"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You can only set a personal stage on a private task."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""
"לא ניתן למחוק שלבים המכילים פרויקטים. אתה יכול לאחסן אותם בארכיון או למחוק "
"תחילה את כל הפרויקטים שלהם."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr ""
"לא ניתן למחוק שלבים המכילים פרויקטים. תחילה עליך למחוק את כל הפרויקטים שלהם."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"לא ניתן למחוק שלבים המכילים משימות. אתה יכול לאחסן אותם בארכיון או למחוק "
"תחילה את כל המשימות שלהם."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"לא ניתן למחוק שלבים המכילים משימות. תחילה עליך למחוק את כל המשימות שבהם."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "הוקצת ל%s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "הוקצת ל"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "אין לך גישת כתיבה של%sשדה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "You must be"
msgstr "אתה חייב להיות"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Your managers decide which feedback is accepted"
msgstr "המנהלים שלך מחליטים איזה משוב יתקבל"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "and"
msgstr "וגם"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"ואיזה משוב \n"
"מועבר לעמודת \"מסורב\"."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "avatar"
msgstr "אווטר"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "button."
msgstr "כפתור."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "comments"
msgstr "הערות"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "למשל מסיבת משרד"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "למשל השקת מוצר..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. domain.com"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "למשל מסיבת משרד"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon to organize your daily activities."
msgstr "אייקון כדי לארגן את הפעילויות היומיות שלך"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or canceled, all of its dependencies will be unblocked."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "logged in"
msgstr "מחובר"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "תצוגת תפריט במשימות שלי"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "על בסיס תקופתי "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "פרוייקט."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as canceled."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as complete."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
#, python-format
msgid "sub-tasks)"
msgstr "תת- משימות)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "task"
msgstr "משימה"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "המועד האחרון לאבן הדרך הבאה עודכן:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "המועד האחרון לאבני הדרך הבאות עודכן:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"כדי להגדיר האם הפרויקט\n"
"מוכן לעבור לצעד הבא."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "to post a comment."
msgstr "לרשום תגובה."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to signalize what is the current status of your Idea."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "כאשר מגיעים לשלב נתון"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr ""

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"{{ record.closed_subtask_count.value }} sub-tasks closed out of {{ "
"record.subtask_count.value }}"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "👤 Unassigned"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "🔒 Private"
msgstr ""
