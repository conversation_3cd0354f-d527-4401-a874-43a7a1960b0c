<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="mailcap — Mailcap file handling" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/mailcap.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/mailcap.py Mailcap files are used to configure how MIME-aware applications such as mail readers and web browsers react to files with different MIME types. (The name “mailcap” is de..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/mailcap.py Mailcap files are used to configure how MIME-aware applications such as mail readers and web browsers react to files with different MIME types. (The name “mailcap” is de..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>mailcap — Mailcap file handling &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="msilib — Read and write Microsoft Installer files" href="msilib.html" />
    <link rel="prev" title="imghdr — Determine the type of an image" href="imghdr.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/mailcap.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="imghdr.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code> — Determine the type of an image</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="msilib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msilib</span></code> — Read and write Microsoft Installer files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mailcap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="msilib.html" title="msilib — Read and write Microsoft Installer files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="imghdr.html" title="imghdr — Determine the type of an image"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code> — Mailcap file handling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-mailcap">
<span id="mailcap-mailcap-file-handling"></span><h1><a class="reference internal" href="#module-mailcap" title="mailcap: Mailcap file handling. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code></a> — Mailcap file handling<a class="headerlink" href="#module-mailcap" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/mailcap.py">Lib/mailcap.py</a></p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-mailcap" title="mailcap: Mailcap file handling. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code></a> module is deprecated
(see <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#mailcap"><strong>PEP 594</strong></a> for details).
The <a class="reference internal" href="mimetypes.html#module-mimetypes" title="mimetypes: Mapping of filename extensions to MIME types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">mimetypes</span></code></a> module provides an alternative.</p>
</div>
<hr class="docutils" />
<p>Mailcap files are used to configure how MIME-aware applications such as mail
readers and web browsers react to files with different MIME types. (The name
“mailcap” is derived from the phrase “mail capability”.)  For example, a mailcap
file might contain a line like <code class="docutils literal notranslate"><span class="pre">video/mpeg;</span> <span class="pre">xmpeg</span> <span class="pre">%s</span></code>.  Then, if the user
encounters an email message or web document with the MIME type
<em class="mimetype">video/mpeg</em>, <code class="docutils literal notranslate"><span class="pre">%s</span></code> will be replaced by a filename (usually one
belonging to a temporary file) and the <strong class="program">xmpeg</strong> program can be
automatically started to view the file.</p>
<p>The mailcap format is documented in <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1524.html"><strong>RFC 1524</strong></a>, “A User Agent Configuration
Mechanism For Multimedia Mail Format Information”, but is not an internet
standard.  However, mailcap files are supported on most Unix systems.</p>
<dl class="py function">
<dt class="sig sig-object py" id="mailcap.findmatch">
<span class="sig-prename descclassname"><span class="pre">mailcap.</span></span><span class="sig-name descname"><span class="pre">findmatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">caps</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">MIMEtype</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'view'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'/dev/null'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plist</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">[]</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#mailcap.findmatch" title="Link to this definition">¶</a></dt>
<dd><p>Return a 2-tuple; the first element is a string containing the command line to
be executed (which can be passed to <a class="reference internal" href="os.html#os.system" title="os.system"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.system()</span></code></a>), and the second element
is the mailcap entry for a given MIME type.  If no matching MIME type can be
found, <code class="docutils literal notranslate"><span class="pre">(None,</span> <span class="pre">None)</span></code> is returned.</p>
<p><em>key</em> is the name of the field desired, which represents the type of activity to
be performed; the default value is ‘view’, since in the  most common case you
simply want to view the body of the MIME-typed data.  Other possible values
might be ‘compose’ and ‘edit’, if you wanted to create a new body of the given
MIME type or alter the existing body data.  See <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1524.html"><strong>RFC 1524</strong></a> for a complete list
of these fields.</p>
<p><em>filename</em> is the filename to be substituted for <code class="docutils literal notranslate"><span class="pre">%s</span></code> in the command line; the
default value is <code class="docutils literal notranslate"><span class="pre">'/dev/null'</span></code> which is almost certainly not what you want, so
usually you’ll override it by specifying a filename.</p>
<p><em>plist</em> can be a list containing named parameters; the default value is simply
an empty list.  Each entry in the list must be a string containing the parameter
name, an equals sign (<code class="docutils literal notranslate"><span class="pre">'='</span></code>), and the parameter’s value.  Mailcap entries can
contain  named parameters like <code class="docutils literal notranslate"><span class="pre">%{foo}</span></code>, which will be replaced by the value
of the parameter named ‘foo’.  For example, if the command line <code class="docutils literal notranslate"><span class="pre">showpartial</span>
<span class="pre">%{id}</span> <span class="pre">%{number}</span> <span class="pre">%{total}</span></code> was in a mailcap file, and <em>plist</em> was set to
<code class="docutils literal notranslate"><span class="pre">['id=1',</span> <span class="pre">'number=2',</span> <span class="pre">'total=3']</span></code>, the resulting command line would be
<code class="docutils literal notranslate"><span class="pre">'showpartial</span> <span class="pre">1</span> <span class="pre">2</span> <span class="pre">3'</span></code>.</p>
<p>In a mailcap file, the “test” field can optionally be specified to test some
external condition (such as the machine architecture, or the window system in
use) to determine whether or not the mailcap line applies.  <a class="reference internal" href="#mailcap.findmatch" title="mailcap.findmatch"><code class="xref py py-func docutils literal notranslate"><span class="pre">findmatch()</span></code></a>
will automatically check such conditions and skip the entry if the check fails.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>To prevent security issues with shell metacharacters (symbols that have
special effects in a shell command line), <code class="docutils literal notranslate"><span class="pre">findmatch</span></code> will refuse
to inject ASCII characters other than alphanumerics and <code class="docutils literal notranslate"><span class="pre">&#64;+=:,./-_</span></code>
into the returned command line.</p>
<p>If a disallowed character appears in <em>filename</em>, <code class="docutils literal notranslate"><span class="pre">findmatch</span></code> will always
return <code class="docutils literal notranslate"><span class="pre">(None,</span> <span class="pre">None)</span></code> as if no entry was found.
If such a character appears elsewhere (a value in <em>plist</em> or in <em>MIMEtype</em>),
<code class="docutils literal notranslate"><span class="pre">findmatch</span></code> will ignore all mailcap entries which use that value.
A <a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-mod docutils literal notranslate"><span class="pre">warning</span></code></a> will be raised in either case.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="mailcap.getcaps">
<span class="sig-prename descclassname"><span class="pre">mailcap.</span></span><span class="sig-name descname"><span class="pre">getcaps</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#mailcap.getcaps" title="Link to this definition">¶</a></dt>
<dd><p>Returns a dictionary mapping MIME types to a list of mailcap file entries. This
dictionary must be passed to the <a class="reference internal" href="#mailcap.findmatch" title="mailcap.findmatch"><code class="xref py py-func docutils literal notranslate"><span class="pre">findmatch()</span></code></a> function.  An entry is stored
as a list of dictionaries, but it shouldn’t be necessary to know the details of
this representation.</p>
<p>The information is derived from all of the mailcap files found on the system.
Settings in the user’s mailcap file <code class="file docutils literal notranslate"><span class="pre">$HOME/.mailcap</span></code> will override
settings in the system mailcap files <code class="file docutils literal notranslate"><span class="pre">/etc/mailcap</span></code>,
<code class="file docutils literal notranslate"><span class="pre">/usr/etc/mailcap</span></code>, and <code class="file docutils literal notranslate"><span class="pre">/usr/local/etc/mailcap</span></code>.</p>
</dd></dl>

<p>An example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">mailcap</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="n">mailcap</span><span class="o">.</span><span class="n">getcaps</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mailcap</span><span class="o">.</span><span class="n">findmatch</span><span class="p">(</span><span class="n">d</span><span class="p">,</span> <span class="s1">&#39;video/mpeg&#39;</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="s1">&#39;tmp1223&#39;</span><span class="p">)</span>
<span class="go">(&#39;xmpeg tmp1223&#39;, {&#39;view&#39;: &#39;xmpeg %s&#39;})</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="imghdr.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imghdr</span></code> — Determine the type of an image</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="msilib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msilib</span></code> — Read and write Microsoft Installer files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/mailcap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="msilib.html" title="msilib — Read and write Microsoft Installer files"
             >next</a> |</li>
        <li class="right" >
          <a href="imghdr.html" title="imghdr — Determine the type of an image"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailcap</span></code> — Mailcap file handling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>