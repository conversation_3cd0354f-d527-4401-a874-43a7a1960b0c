<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="json — JSON encoder and decoder" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/json.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/json/__init__.py JSON (JavaScript Object Notation), specified by RFC 7159(which obsoletes RFC 4627) and by ECMA-404, is a lightweight data interchange format inspired by JavaScript..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/json/__init__.py JSON (JavaScript Object Notation), specified by RFC 7159(which obsoletes RFC 4627) and by ECMA-404, is a lightweight data interchange format inspired by JavaScript..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>json — JSON encoder and decoder &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="mailbox — Manipulate mailboxes in various formats" href="mailbox.html" />
    <link rel="prev" title="email.iterators: Iterators" href="email.iterators.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/json.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a><ul>
<li><a class="reference internal" href="#basic-usage">Basic Usage</a></li>
<li><a class="reference internal" href="#encoders-and-decoders">Encoders and Decoders</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#standard-compliance-and-interoperability">Standard Compliance and Interoperability</a><ul>
<li><a class="reference internal" href="#character-encodings">Character Encodings</a></li>
<li><a class="reference internal" href="#infinite-and-nan-number-values">Infinite and NaN Number Values</a></li>
<li><a class="reference internal" href="#repeated-names-within-an-object">Repeated Names Within an Object</a></li>
<li><a class="reference internal" href="#top-level-non-object-non-array-values">Top-level Non-Object, Non-Array Values</a></li>
<li><a class="reference internal" href="#implementation-limitations">Implementation Limitations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-json.tool">Command Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command line options</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.iterators.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.iterators</span></code>: Iterators</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mailbox.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/json.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mailbox.html" title="mailbox — Manipulate mailboxes in various formats"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="email.iterators.html" title="email.iterators: Iterators"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" accesskey="U">Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-json">
<span id="json-json-encoder-and-decoder"></span><h1><a class="reference internal" href="#module-json" title="json: Encode and decode the JSON format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a> — JSON encoder and decoder<a class="headerlink" href="#module-json" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/json/__init__.py">Lib/json/__init__.py</a></p>
<hr class="docutils" />
<p><a class="reference external" href="https://json.org">JSON (JavaScript Object Notation)</a>, specified by
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7159.html"><strong>RFC 7159</strong></a> (which obsoletes <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4627.html"><strong>RFC 4627</strong></a>) and by
<a class="reference external" href="https://www.ecma-international.org/publications-and-standards/standards/ecma-404/">ECMA-404</a>,
is a lightweight data interchange format inspired by
<a class="reference external" href="https://en.wikipedia.org/wiki/JavaScript">JavaScript</a> object literal syntax
(although it is not a strict subset of JavaScript <a class="footnote-reference brackets" href="#rfc-errata" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> ).</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Be cautious when parsing JSON data from untrusted sources. A malicious
JSON string may cause the decoder to consume considerable CPU and memory
resources. Limiting the size of data to be parsed is recommended.</p>
</div>
<p><a class="reference internal" href="#module-json" title="json: Encode and decode the JSON format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a> exposes an API familiar to users of the standard library
<a class="reference internal" href="marshal.html#module-marshal" title="marshal: Convert Python objects to streams of bytes and back (with different constraints)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">marshal</span></code></a> and <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> modules.</p>
<p>Encoding basic Python object hierarchies:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">([</span><span class="s1">&#39;foo&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;bar&#39;</span><span class="p">:</span> <span class="p">(</span><span class="s1">&#39;baz&#39;</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="mf">1.0</span><span class="p">,</span> <span class="mi">2</span><span class="p">)}])</span>
<span class="go">&#39;[&quot;foo&quot;, {&quot;bar&quot;: [&quot;baz&quot;, null, 1.0, 2]}]&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\&quot;</span><span class="s2">foo</span><span class="se">\b</span><span class="s2">ar&quot;</span><span class="p">))</span>
<span class="go">&quot;\&quot;foo\bar&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\u1234</span><span class="s1">&#39;</span><span class="p">))</span>
<span class="go">&quot;\u1234&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="s1">&#39;</span><span class="se">\\</span><span class="s1">&#39;</span><span class="p">))</span>
<span class="go">&quot;\\&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span><span class="s2">&quot;c&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;b&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;a&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">},</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
<span class="go">{&quot;a&quot;: 0, &quot;b&quot;: 0, &quot;c&quot;: 0}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">io</span> <span class="kn">import</span> <span class="n">StringIO</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">io</span> <span class="o">=</span> <span class="n">StringIO</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">([</span><span class="s1">&#39;streaming API&#39;</span><span class="p">],</span> <span class="n">io</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">io</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>
<span class="go">&#39;[&quot;streaming API&quot;]&#39;</span>
</pre></div>
</div>
<p>Compact encoding:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">([</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;4&#39;</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="s1">&#39;6&#39;</span><span class="p">:</span> <span class="mi">7</span><span class="p">}],</span> <span class="n">separators</span><span class="o">=</span><span class="p">(</span><span class="s1">&#39;,&#39;</span><span class="p">,</span> <span class="s1">&#39;:&#39;</span><span class="p">))</span>
<span class="go">&#39;[1,2,3,{&quot;4&quot;:5,&quot;6&quot;:7}]&#39;</span>
</pre></div>
</div>
<p>Pretty printing:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">({</span><span class="s1">&#39;4&#39;</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="s1">&#39;6&#39;</span><span class="p">:</span> <span class="mi">7</span><span class="p">},</span> <span class="n">sort_keys</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">4</span><span class="p">))</span>
<span class="go">{</span>
<span class="go">    &quot;4&quot;: 5,</span>
<span class="go">    &quot;6&quot;: 7</span>
<span class="go">}</span>
</pre></div>
</div>
<p>Decoding JSON:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;[&quot;foo&quot;, {&quot;bar&quot;:[&quot;baz&quot;, null, 1.0, 2]}]&#39;</span><span class="p">)</span>
<span class="go">[&#39;foo&#39;, {&#39;bar&#39;: [&#39;baz&#39;, None, 1.0, 2]}]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;&quot;</span><span class="se">\\</span><span class="s1">&quot;foo</span><span class="se">\\</span><span class="s1">bar&quot;&#39;</span><span class="p">)</span>
<span class="go">&#39;&quot;foo\x08ar&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">io</span> <span class="kn">import</span> <span class="n">StringIO</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">io</span> <span class="o">=</span> <span class="n">StringIO</span><span class="p">(</span><span class="s1">&#39;[&quot;streaming API&quot;]&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">io</span><span class="p">)</span>
<span class="go">[&#39;streaming API&#39;]</span>
</pre></div>
</div>
<p>Specializing JSON object decoding:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">as_complex</span><span class="p">(</span><span class="n">dct</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">if</span> <span class="s1">&#39;__complex__&#39;</span> <span class="ow">in</span> <span class="n">dct</span><span class="p">:</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="nb">complex</span><span class="p">(</span><span class="n">dct</span><span class="p">[</span><span class="s1">&#39;real&#39;</span><span class="p">],</span> <span class="n">dct</span><span class="p">[</span><span class="s1">&#39;imag&#39;</span><span class="p">])</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="n">dct</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;{&quot;__complex__&quot;: true, &quot;real&quot;: 1, &quot;imag&quot;: 2}&#39;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">object_hook</span><span class="o">=</span><span class="n">as_complex</span><span class="p">)</span>
<span class="go">(1+2j)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">decimal</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;1.1&#39;</span><span class="p">,</span> <span class="n">parse_float</span><span class="o">=</span><span class="n">decimal</span><span class="o">.</span><span class="n">Decimal</span><span class="p">)</span>
<span class="go">Decimal(&#39;1.1&#39;)</span>
</pre></div>
</div>
<p>Extending <a class="reference internal" href="#json.JSONEncoder" title="json.JSONEncoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONEncoder</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">json</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">ComplexEncoder</span><span class="p">(</span><span class="n">json</span><span class="o">.</span><span class="n">JSONEncoder</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">default</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="nb">complex</span><span class="p">):</span>
<span class="gp">... </span>            <span class="k">return</span> <span class="p">[</span><span class="n">obj</span><span class="o">.</span><span class="n">real</span><span class="p">,</span> <span class="n">obj</span><span class="o">.</span><span class="n">imag</span><span class="p">]</span>
<span class="gp">... </span>        <span class="c1"># Let the base class default method raise the TypeError</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">default</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">1</span><span class="n">j</span><span class="p">,</span> <span class="bp">cls</span><span class="o">=</span><span class="n">ComplexEncoder</span><span class="p">)</span>
<span class="go">&#39;[2.0, 1.0]&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ComplexEncoder</span><span class="p">()</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">1</span><span class="n">j</span><span class="p">)</span>
<span class="go">&#39;[2.0, 1.0]&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">ComplexEncoder</span><span class="p">()</span><span class="o">.</span><span class="n">iterencode</span><span class="p">(</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">1</span><span class="n">j</span><span class="p">))</span>
<span class="go">[&#39;[2.0&#39;, &#39;, 1.0&#39;, &#39;]&#39;]</span>
</pre></div>
</div>
<p>Using <a class="reference internal" href="#module-json.tool" title="json.tool: A command line to validate and pretty-print JSON."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json.tool</span></code></a> from the shell to validate and pretty-print:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;{&quot;json&quot;:&quot;obj&quot;}&#39;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool
<span class="go">{</span>
<span class="go">    &quot;json&quot;: &quot;obj&quot;</span>
<span class="go">}</span>
<span class="gp">$ </span><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;{1.2:3.4}&#39;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool
<span class="go">Expecting property name enclosed in double quotes: line 1 column 2 (char 1)</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="#json-commandline"><span class="std std-ref">Command Line Interface</span></a> for detailed documentation.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>JSON is a subset of <a class="reference external" href="https://yaml.org/">YAML</a> 1.2.  The JSON produced by
this module’s default settings (in particular, the default <em>separators</em>
value) is also a subset of YAML 1.0 and 1.1.  This module can thus also be
used as a YAML serializer.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This module’s encoders and decoders preserve input and output order by
default.  Order is only lost if the underlying containers are unordered.</p>
</div>
<section id="basic-usage">
<h2>Basic Usage<a class="headerlink" href="#basic-usage" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="json.dump">
<span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">dump</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skipkeys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ensure_ascii</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check_circular</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_nan</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">separators</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_keys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.dump" title="Link to this definition">¶</a></dt>
<dd><p>Serialize <em>obj</em> as a JSON formatted stream to <em>fp</em> (a <code class="docutils literal notranslate"><span class="pre">.write()</span></code>-supporting
<a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a>) using this <a class="reference internal" href="#py-to-json-table"><span class="std std-ref">conversion table</span></a>.</p>
<p>If <em>skipkeys</em> is true (default: <code class="docutils literal notranslate"><span class="pre">False</span></code>), then dict keys that are not
of a basic type (<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference internal" href="functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>,
<code class="docutils literal notranslate"><span class="pre">None</span></code>) will be skipped instead of raising a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
<p>The <a class="reference internal" href="#module-json" title="json: Encode and decode the JSON format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a> module always produces <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> objects, not
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> objects. Therefore, <code class="docutils literal notranslate"><span class="pre">fp.write()</span></code> must support <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>
input.</p>
<p>If <em>ensure_ascii</em> is true (the default), the output is guaranteed to
have all incoming non-ASCII characters escaped.  If <em>ensure_ascii</em> is
false, these characters will be output as-is.</p>
<p>If <em>check_circular</em> is false (default: <code class="docutils literal notranslate"><span class="pre">True</span></code>), then the circular
reference check for container types will be skipped and a circular reference
will result in a <a class="reference internal" href="exceptions.html#RecursionError" title="RecursionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RecursionError</span></code></a> (or worse).</p>
<p>If <em>allow_nan</em> is false (default: <code class="docutils literal notranslate"><span class="pre">True</span></code>), then it will be a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> to serialize out of range <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> values (<code class="docutils literal notranslate"><span class="pre">nan</span></code>,
<code class="docutils literal notranslate"><span class="pre">inf</span></code>, <code class="docutils literal notranslate"><span class="pre">-inf</span></code>) in strict compliance of the JSON specification.
If <em>allow_nan</em> is true, their JavaScript equivalents (<code class="docutils literal notranslate"><span class="pre">NaN</span></code>,
<code class="docutils literal notranslate"><span class="pre">Infinity</span></code>, <code class="docutils literal notranslate"><span class="pre">-Infinity</span></code>) will be used.</p>
<p>If <em>indent</em> is a non-negative integer or string, then JSON array elements and
object members will be pretty-printed with that indent level.  An indent level
of 0, negative, or <code class="docutils literal notranslate"><span class="pre">&quot;&quot;</span></code> will only insert newlines.  <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default)
selects the most compact representation. Using a positive integer indent
indents that many spaces per level.  If <em>indent</em> is a string (such as <code class="docutils literal notranslate"><span class="pre">&quot;\t&quot;</span></code>),
that string is used to indent each level.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Allow strings for <em>indent</em> in addition to integers.</p>
</div>
<p>If specified, <em>separators</em> should be an <code class="docutils literal notranslate"><span class="pre">(item_separator,</span> <span class="pre">key_separator)</span></code>
tuple.  The default is <code class="docutils literal notranslate"><span class="pre">(',</span> <span class="pre">',</span> <span class="pre">':</span> <span class="pre">')</span></code> if <em>indent</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> and
<code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':</span> <span class="pre">')</span></code> otherwise.  To get the most compact JSON representation,
you should specify <code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':')</span></code> to eliminate whitespace.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Use <code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':</span> <span class="pre">')</span></code> as default if <em>indent</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
<p>If specified, <em>default</em> should be a function that gets called for objects that
can’t otherwise be serialized.  It should return a JSON encodable version of
the object or raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.  If not specified, <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>
is raised.</p>
<p>If <em>sort_keys</em> is true (default: <code class="docutils literal notranslate"><span class="pre">False</span></code>), then the output of
dictionaries will be sorted by key.</p>
<p>To use a custom <a class="reference internal" href="#json.JSONEncoder" title="json.JSONEncoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONEncoder</span></code></a> subclass (e.g. one that overrides the
<a class="reference internal" href="#json.JSONEncoder.default" title="json.JSONEncoder.default"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default()</span></code></a> method to serialize additional types), specify it with the
<em>cls</em> kwarg; otherwise <a class="reference internal" href="#json.JSONEncoder" title="json.JSONEncoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONEncoder</span></code></a> is used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>All optional parameters are now <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> and <a class="reference internal" href="marshal.html#module-marshal" title="marshal: Convert Python objects to streams of bytes and back (with different constraints)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">marshal</span></code></a>, JSON is not a framed protocol,
so trying to serialize multiple objects with repeated calls to
<a class="reference internal" href="#json.dump" title="json.dump"><code class="xref py py-func docutils literal notranslate"><span class="pre">dump()</span></code></a> using the same <em>fp</em> will result in an invalid JSON file.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="json.dumps">
<span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">dumps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skipkeys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ensure_ascii</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check_circular</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_nan</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">separators</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_keys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.dumps" title="Link to this definition">¶</a></dt>
<dd><p>Serialize <em>obj</em> to a JSON formatted <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> using this <a class="reference internal" href="#py-to-json-table"><span class="std std-ref">conversion
table</span></a>.  The arguments have the same meaning as in
<a class="reference internal" href="#json.dump" title="json.dump"><code class="xref py py-func docutils literal notranslate"><span class="pre">dump()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Keys in key/value pairs of JSON are always of the type <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>. When
a dictionary is converted into JSON, all the keys of the dictionary are
coerced to strings. As a result of this, if a dictionary is converted
into JSON and then back into a dictionary, the dictionary may not equal
the original one. That is, <code class="docutils literal notranslate"><span class="pre">loads(dumps(x))</span> <span class="pre">!=</span> <span class="pre">x</span></code> if x has non-string
keys.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="json.load">
<span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">load</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_float</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_int</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_constant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_pairs_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.load" title="Link to this definition">¶</a></dt>
<dd><p>Deserialize <em>fp</em> (a <code class="docutils literal notranslate"><span class="pre">.read()</span></code>-supporting <a class="reference internal" href="../glossary.html#term-text-file"><span class="xref std std-term">text file</span></a> or
<a class="reference internal" href="../glossary.html#term-binary-file"><span class="xref std std-term">binary file</span></a> containing a JSON document) to a Python object using
this <a class="reference internal" href="#json-to-py-table"><span class="std std-ref">conversion table</span></a>.</p>
<p><em>object_hook</em> is an optional function that will be called with the result of
any object literal decoded (a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>).  The return value of
<em>object_hook</em> will be used instead of the <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.  This feature can be used
to implement custom decoders (e.g. <a class="reference external" href="https://www.jsonrpc.org">JSON-RPC</a>
class hinting).</p>
<p><em>object_pairs_hook</em> is an optional function that will be called with the
result of any object literal decoded with an ordered list of pairs.  The
return value of <em>object_pairs_hook</em> will be used instead of the
<a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.  This feature can be used to implement custom decoders.
If <em>object_hook</em> is also defined, the <em>object_pairs_hook</em> takes priority.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added support for <em>object_pairs_hook</em>.</p>
</div>
<p><em>parse_float</em>, if specified, will be called with the string of every JSON
float to be decoded.  By default, this is equivalent to <code class="docutils literal notranslate"><span class="pre">float(num_str)</span></code>.
This can be used to use another datatype or parser for JSON floats
(e.g. <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a>).</p>
<p><em>parse_int</em>, if specified, will be called with the string of every JSON int
to be decoded.  By default, this is equivalent to <code class="docutils literal notranslate"><span class="pre">int(num_str)</span></code>.  This can
be used to use another datatype or parser for JSON integers
(e.g. <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The default <em>parse_int</em> of <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-func docutils literal notranslate"><span class="pre">int()</span></code></a> now limits the maximum length of
the integer string via the interpreter’s <a class="reference internal" href="stdtypes.html#int-max-str-digits"><span class="std std-ref">integer string
conversion length limitation</span></a> to help avoid denial
of service attacks.</p>
</div>
<p><em>parse_constant</em>, if specified, will be called with one of the following
strings: <code class="docutils literal notranslate"><span class="pre">'-Infinity'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Infinity'</span></code>, <code class="docutils literal notranslate"><span class="pre">'NaN'</span></code>.
This can be used to raise an exception if invalid JSON numbers
are encountered.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span><em>parse_constant</em> doesn’t get called on ‘null’, ‘true’, ‘false’ anymore.</p>
</div>
<p>To use a custom <a class="reference internal" href="#json.JSONDecoder" title="json.JSONDecoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONDecoder</span></code></a> subclass, specify it with the <code class="docutils literal notranslate"><span class="pre">cls</span></code>
kwarg; otherwise <a class="reference internal" href="#json.JSONDecoder" title="json.JSONDecoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONDecoder</span></code></a> is used.  Additional keyword arguments
will be passed to the constructor of the class.</p>
<p>If the data being deserialized is not a valid JSON document, a
<a class="reference internal" href="#json.JSONDecodeError" title="json.JSONDecodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">JSONDecodeError</span></code></a> will be raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>All optional parameters are now <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>fp</em> can now be a <a class="reference internal" href="../glossary.html#term-binary-file"><span class="xref std std-term">binary file</span></a>. The input encoding should be
UTF-8, UTF-16 or UTF-32.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="json.loads">
<span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">loads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_float</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_int</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_constant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_pairs_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.loads" title="Link to this definition">¶</a></dt>
<dd><p>Deserialize <em>s</em> (a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>
instance containing a JSON document) to a Python object using this
<a class="reference internal" href="#json-to-py-table"><span class="std std-ref">conversion table</span></a>.</p>
<p>The other arguments have the same meaning as in <a class="reference internal" href="#json.load" title="json.load"><code class="xref py py-func docutils literal notranslate"><span class="pre">load()</span></code></a>.</p>
<p>If the data being deserialized is not a valid JSON document, a
<a class="reference internal" href="#json.JSONDecodeError" title="json.JSONDecodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">JSONDecodeError</span></code></a> will be raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><em>s</em> can now be of type <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>. The
input encoding should be UTF-8, UTF-16 or UTF-32.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The keyword argument <em>encoding</em> has been removed.</p>
</div>
</dd></dl>

</section>
<section id="encoders-and-decoders">
<h2>Encoders and Decoders<a class="headerlink" href="#encoders-and-decoders" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="json.JSONDecoder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">JSONDecoder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_float</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_int</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse_constant</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_pairs_hook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONDecoder" title="Link to this definition">¶</a></dt>
<dd><p>Simple JSON decoder.</p>
<p>Performs the following translations in decoding by default:</p>
<table class="docutils align-default" id="json-to-py-table">
<thead>
<tr class="row-odd"><th class="head"><p>JSON</p></th>
<th class="head"><p>Python</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>object</p></td>
<td><p>dict</p></td>
</tr>
<tr class="row-odd"><td><p>array</p></td>
<td><p>list</p></td>
</tr>
<tr class="row-even"><td><p>string</p></td>
<td><p>str</p></td>
</tr>
<tr class="row-odd"><td><p>number (int)</p></td>
<td><p>int</p></td>
</tr>
<tr class="row-even"><td><p>number (real)</p></td>
<td><p>float</p></td>
</tr>
<tr class="row-odd"><td><p>true</p></td>
<td><p>True</p></td>
</tr>
<tr class="row-even"><td><p>false</p></td>
<td><p>False</p></td>
</tr>
<tr class="row-odd"><td><p>null</p></td>
<td><p>None</p></td>
</tr>
</tbody>
</table>
<p>It also understands <code class="docutils literal notranslate"><span class="pre">NaN</span></code>, <code class="docutils literal notranslate"><span class="pre">Infinity</span></code>, and <code class="docutils literal notranslate"><span class="pre">-Infinity</span></code> as their
corresponding <code class="docutils literal notranslate"><span class="pre">float</span></code> values, which is outside the JSON spec.</p>
<p><em>object_hook</em>, if specified, will be called with the result of every JSON
object decoded and its return value will be used in place of the given
<a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.  This can be used to provide custom deserializations (e.g. to
support <a class="reference external" href="https://www.jsonrpc.org">JSON-RPC</a> class hinting).</p>
<p><em>object_pairs_hook</em>, if specified will be called with the result of every
JSON object decoded with an ordered list of pairs.  The return value of
<em>object_pairs_hook</em> will be used instead of the <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>.  This
feature can be used to implement custom decoders.  If <em>object_hook</em> is also
defined, the <em>object_pairs_hook</em> takes priority.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added support for <em>object_pairs_hook</em>.</p>
</div>
<p><em>parse_float</em>, if specified, will be called with the string of every JSON
float to be decoded.  By default, this is equivalent to <code class="docutils literal notranslate"><span class="pre">float(num_str)</span></code>.
This can be used to use another datatype or parser for JSON floats
(e.g. <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a>).</p>
<p><em>parse_int</em>, if specified, will be called with the string of every JSON int
to be decoded.  By default, this is equivalent to <code class="docutils literal notranslate"><span class="pre">int(num_str)</span></code>.  This can
be used to use another datatype or parser for JSON integers
(e.g. <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>).</p>
<p><em>parse_constant</em>, if specified, will be called with one of the following
strings: <code class="docutils literal notranslate"><span class="pre">'-Infinity'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Infinity'</span></code>, <code class="docutils literal notranslate"><span class="pre">'NaN'</span></code>.
This can be used to raise an exception if invalid JSON numbers
are encountered.</p>
<p>If <em>strict</em> is false (<code class="docutils literal notranslate"><span class="pre">True</span></code> is the default), then control characters
will be allowed inside strings.  Control characters in this context are
those with character codes in the 0–31 range, including <code class="docutils literal notranslate"><span class="pre">'\t'</span></code> (tab),
<code class="docutils literal notranslate"><span class="pre">'\n'</span></code>, <code class="docutils literal notranslate"><span class="pre">'\r'</span></code> and <code class="docutils literal notranslate"><span class="pre">'\0'</span></code>.</p>
<p>If the data being deserialized is not a valid JSON document, a
<a class="reference internal" href="#json.JSONDecodeError" title="json.JSONDecodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">JSONDecodeError</span></code></a> will be raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>All parameters are now <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="json.JSONDecoder.decode">
<span class="sig-name descname"><span class="pre">decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONDecoder.decode" title="Link to this definition">¶</a></dt>
<dd><p>Return the Python representation of <em>s</em> (a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> instance
containing a JSON document).</p>
<p><a class="reference internal" href="#json.JSONDecodeError" title="json.JSONDecodeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">JSONDecodeError</span></code></a> will be raised if the given JSON document is not
valid.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="json.JSONDecoder.raw_decode">
<span class="sig-name descname"><span class="pre">raw_decode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">s</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONDecoder.raw_decode" title="Link to this definition">¶</a></dt>
<dd><p>Decode a JSON document from <em>s</em> (a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> beginning with a
JSON document) and return a 2-tuple of the Python representation
and the index in <em>s</em> where the document ended.</p>
<p>This can be used to decode a JSON document from a string that may have
extraneous data at the end.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="json.JSONEncoder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">JSONEncoder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skipkeys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ensure_ascii</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check_circular</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allow_nan</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sort_keys</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">separators</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONEncoder" title="Link to this definition">¶</a></dt>
<dd><p>Extensible JSON encoder for Python data structures.</p>
<p>Supports the following objects and types by default:</p>
<table class="docutils align-default" id="py-to-json-table">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>JSON</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>dict</p></td>
<td><p>object</p></td>
</tr>
<tr class="row-odd"><td><p>list, tuple</p></td>
<td><p>array</p></td>
</tr>
<tr class="row-even"><td><p>str</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-odd"><td><p>int, float, int- &amp; float-derived Enums</p></td>
<td><p>number</p></td>
</tr>
<tr class="row-even"><td><p>True</p></td>
<td><p>true</p></td>
</tr>
<tr class="row-odd"><td><p>False</p></td>
<td><p>false</p></td>
</tr>
<tr class="row-even"><td><p>None</p></td>
<td><p>null</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for int- and float-derived Enum classes.</p>
</div>
<p>To extend this to recognize other objects, subclass and implement a
<a class="reference internal" href="#json.JSONEncoder.default" title="json.JSONEncoder.default"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default()</span></code></a> method with another method that returns a serializable object
for <code class="docutils literal notranslate"><span class="pre">o</span></code> if possible, otherwise it should call the superclass implementation
(to raise <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>).</p>
<p>If <em>skipkeys</em> is false (the default), a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> will be raised when
trying to encode keys that are not <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>
or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  If <em>skipkeys</em> is true, such items are simply skipped.</p>
<p>If <em>ensure_ascii</em> is true (the default), the output is guaranteed to
have all incoming non-ASCII characters escaped.  If <em>ensure_ascii</em> is
false, these characters will be output as-is.</p>
<p>If <em>check_circular</em> is true (the default), then lists, dicts, and custom
encoded objects will be checked for circular references during encoding to
prevent an infinite recursion (which would cause a <a class="reference internal" href="exceptions.html#RecursionError" title="RecursionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RecursionError</span></code></a>).
Otherwise, no such check takes place.</p>
<p>If <em>allow_nan</em> is true (the default), then <code class="docutils literal notranslate"><span class="pre">NaN</span></code>, <code class="docutils literal notranslate"><span class="pre">Infinity</span></code>, and
<code class="docutils literal notranslate"><span class="pre">-Infinity</span></code> will be encoded as such.  This behavior is not JSON
specification compliant, but is consistent with most JavaScript based
encoders and decoders.  Otherwise, it will be a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> to encode
such floats.</p>
<p>If <em>sort_keys</em> is true (default: <code class="docutils literal notranslate"><span class="pre">False</span></code>), then the output of dictionaries
will be sorted by key; this is useful for regression tests to ensure that
JSON serializations can be compared on a day-to-day basis.</p>
<p>If <em>indent</em> is a non-negative integer or string, then JSON array elements and
object members will be pretty-printed with that indent level.  An indent level
of 0, negative, or <code class="docutils literal notranslate"><span class="pre">&quot;&quot;</span></code> will only insert newlines.  <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default)
selects the most compact representation. Using a positive integer indent
indents that many spaces per level.  If <em>indent</em> is a string (such as <code class="docutils literal notranslate"><span class="pre">&quot;\t&quot;</span></code>),
that string is used to indent each level.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Allow strings for <em>indent</em> in addition to integers.</p>
</div>
<p>If specified, <em>separators</em> should be an <code class="docutils literal notranslate"><span class="pre">(item_separator,</span> <span class="pre">key_separator)</span></code>
tuple.  The default is <code class="docutils literal notranslate"><span class="pre">(',</span> <span class="pre">',</span> <span class="pre">':</span> <span class="pre">')</span></code> if <em>indent</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> and
<code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':</span> <span class="pre">')</span></code> otherwise.  To get the most compact JSON representation,
you should specify <code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':')</span></code> to eliminate whitespace.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Use <code class="docutils literal notranslate"><span class="pre">(',',</span> <span class="pre">':</span> <span class="pre">')</span></code> as default if <em>indent</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</div>
<p>If specified, <em>default</em> should be a function that gets called for objects that
can’t otherwise be serialized.  It should return a JSON encodable version of
the object or raise a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.  If not specified, <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>
is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>All parameters are now <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a>.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="json.JSONEncoder.default">
<span class="sig-name descname"><span class="pre">default</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">o</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONEncoder.default" title="Link to this definition">¶</a></dt>
<dd><p>Implement this method in a subclass such that it returns a serializable
object for <em>o</em>, or calls the base implementation (to raise a
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>).</p>
<p>For example, to support arbitrary iterators, you could implement
<a class="reference internal" href="#json.JSONEncoder.default" title="json.JSONEncoder.default"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default()</span></code></a> like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">default</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">o</span><span class="p">):</span>
   <span class="k">try</span><span class="p">:</span>
       <span class="n">iterable</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">o</span><span class="p">)</span>
   <span class="k">except</span> <span class="ne">TypeError</span><span class="p">:</span>
       <span class="k">pass</span>
   <span class="k">else</span><span class="p">:</span>
       <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">iterable</span><span class="p">)</span>
   <span class="c1"># Let the base class default method raise the TypeError</span>
   <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">default</span><span class="p">(</span><span class="n">o</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="json.JSONEncoder.encode">
<span class="sig-name descname"><span class="pre">encode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">o</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONEncoder.encode" title="Link to this definition">¶</a></dt>
<dd><p>Return a JSON string representation of a Python data structure, <em>o</em>.  For
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">JSONEncoder</span><span class="p">()</span><span class="o">.</span><span class="n">encode</span><span class="p">({</span><span class="s2">&quot;foo&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;bar&quot;</span><span class="p">,</span> <span class="s2">&quot;baz&quot;</span><span class="p">]})</span>
<span class="go">&#39;{&quot;foo&quot;: [&quot;bar&quot;, &quot;baz&quot;]}&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="json.JSONEncoder.iterencode">
<span class="sig-name descname"><span class="pre">iterencode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">o</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONEncoder.iterencode" title="Link to this definition">¶</a></dt>
<dd><p>Encode the given object, <em>o</em>, and yield each string representation as
available.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="n">json</span><span class="o">.</span><span class="n">JSONEncoder</span><span class="p">()</span><span class="o">.</span><span class="n">iterencode</span><span class="p">(</span><span class="n">bigobject</span><span class="p">):</span>
    <span class="n">mysocket</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="exceptions">
<h2>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h2>
<dl class="py exception">
<dt class="sig sig-object py" id="json.JSONDecodeError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">json.</span></span><span class="sig-name descname"><span class="pre">JSONDecodeError</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doc</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#json.JSONDecodeError" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> with the following additional attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="json.JSONDecodeError.msg">
<span class="sig-name descname"><span class="pre">msg</span></span><a class="headerlink" href="#json.JSONDecodeError.msg" title="Link to this definition">¶</a></dt>
<dd><p>The unformatted error message.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="json.JSONDecodeError.doc">
<span class="sig-name descname"><span class="pre">doc</span></span><a class="headerlink" href="#json.JSONDecodeError.doc" title="Link to this definition">¶</a></dt>
<dd><p>The JSON document being parsed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="json.JSONDecodeError.pos">
<span class="sig-name descname"><span class="pre">pos</span></span><a class="headerlink" href="#json.JSONDecodeError.pos" title="Link to this definition">¶</a></dt>
<dd><p>The start index of <em>doc</em> where parsing failed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="json.JSONDecodeError.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#json.JSONDecodeError.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line corresponding to <em>pos</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="json.JSONDecodeError.colno">
<span class="sig-name descname"><span class="pre">colno</span></span><a class="headerlink" href="#json.JSONDecodeError.colno" title="Link to this definition">¶</a></dt>
<dd><p>The column corresponding to <em>pos</em>.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</section>
<section id="standard-compliance-and-interoperability">
<h2>Standard Compliance and Interoperability<a class="headerlink" href="#standard-compliance-and-interoperability" title="Link to this heading">¶</a></h2>
<p>The JSON format is specified by <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7159.html"><strong>RFC 7159</strong></a> and by
<a class="reference external" href="https://www.ecma-international.org/publications-and-standards/standards/ecma-404/">ECMA-404</a>.
This section details this module’s level of compliance with the RFC.
For simplicity, <a class="reference internal" href="#json.JSONEncoder" title="json.JSONEncoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONEncoder</span></code></a> and <a class="reference internal" href="#json.JSONDecoder" title="json.JSONDecoder"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONDecoder</span></code></a> subclasses, and
parameters other than those explicitly mentioned, are not considered.</p>
<p>This module does not comply with the RFC in a strict fashion, implementing some
extensions that are valid JavaScript but not valid JSON.  In particular:</p>
<ul class="simple">
<li><p>Infinite and NaN number values are accepted and output;</p></li>
<li><p>Repeated names within an object are accepted, and only the value of the last
name-value pair is used.</p></li>
</ul>
<p>Since the RFC permits RFC-compliant parsers to accept input texts that are not
RFC-compliant, this module’s deserializer is technically RFC-compliant under
default settings.</p>
<section id="character-encodings">
<h3>Character Encodings<a class="headerlink" href="#character-encodings" title="Link to this heading">¶</a></h3>
<p>The RFC requires that JSON be represented using either UTF-8, UTF-16, or
UTF-32, with UTF-8 being the recommended default for maximum interoperability.</p>
<p>As permitted, though not required, by the RFC, this module’s serializer sets
<em>ensure_ascii=True</em> by default, thus escaping the output so that the resulting
strings only contain ASCII characters.</p>
<p>Other than the <em>ensure_ascii</em> parameter, this module is defined strictly in
terms of conversion between Python objects and
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">Unicode</span> <span class="pre">strings</span></code></a>, and thus does not otherwise directly address
the issue of character encodings.</p>
<p>The RFC prohibits adding a byte order mark (BOM) to the start of a JSON text,
and this module’s serializer does not add a BOM to its output.
The RFC permits, but does not require, JSON deserializers to ignore an initial
BOM in their input.  This module’s deserializer raises a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>
when an initial BOM is present.</p>
<p>The RFC does not explicitly forbid JSON strings which contain byte sequences
that don’t correspond to valid Unicode characters (e.g. unpaired UTF-16
surrogates), but it does note that they may cause interoperability problems.
By default, this module accepts and outputs (when present in the original
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>) code points for such sequences.</p>
</section>
<section id="infinite-and-nan-number-values">
<h3>Infinite and NaN Number Values<a class="headerlink" href="#infinite-and-nan-number-values" title="Link to this heading">¶</a></h3>
<p>The RFC does not permit the representation of infinite or NaN number values.
Despite that, by default, this module accepts and outputs <code class="docutils literal notranslate"><span class="pre">Infinity</span></code>,
<code class="docutils literal notranslate"><span class="pre">-Infinity</span></code>, and <code class="docutils literal notranslate"><span class="pre">NaN</span></code> as if they were valid JSON number literal values:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="c1"># Neither of these calls raises an exception, but the results are not valid JSON</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;-inf&#39;</span><span class="p">))</span>
<span class="go">&#39;-Infinity&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">dumps</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;nan&#39;</span><span class="p">))</span>
<span class="go">&#39;NaN&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># Same when deserializing</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;-Infinity&#39;</span><span class="p">)</span>
<span class="go">-inf</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="s1">&#39;NaN&#39;</span><span class="p">)</span>
<span class="go">nan</span>
</pre></div>
</div>
<p>In the serializer, the <em>allow_nan</em> parameter can be used to alter this
behavior.  In the deserializer, the <em>parse_constant</em> parameter can be used to
alter this behavior.</p>
</section>
<section id="repeated-names-within-an-object">
<h3>Repeated Names Within an Object<a class="headerlink" href="#repeated-names-within-an-object" title="Link to this heading">¶</a></h3>
<p>The RFC specifies that the names within a JSON object should be unique, but
does not mandate how repeated names in JSON objects should be handled.  By
default, this module does not raise an exception; instead, it ignores all but
the last name-value pair for a given name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">weird_json</span> <span class="o">=</span> <span class="s1">&#39;{&quot;x&quot;: 1, &quot;x&quot;: 2, &quot;x&quot;: 3}&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">json</span><span class="o">.</span><span class="n">loads</span><span class="p">(</span><span class="n">weird_json</span><span class="p">)</span>
<span class="go">{&#39;x&#39;: 3}</span>
</pre></div>
</div>
<p>The <em>object_pairs_hook</em> parameter can be used to alter this behavior.</p>
</section>
<section id="top-level-non-object-non-array-values">
<h3>Top-level Non-Object, Non-Array Values<a class="headerlink" href="#top-level-non-object-non-array-values" title="Link to this heading">¶</a></h3>
<p>The old version of JSON specified by the obsolete <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4627.html"><strong>RFC 4627</strong></a> required that
the top-level value of a JSON text must be either a JSON object or array
(Python <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> or <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a>), and could not be a JSON null,
boolean, number, or string value.  <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7159.html"><strong>RFC 7159</strong></a> removed that restriction, and
this module does not and has never implemented that restriction in either its
serializer or its deserializer.</p>
<p>Regardless, for maximum interoperability, you may wish to voluntarily adhere
to the restriction yourself.</p>
</section>
<section id="implementation-limitations">
<h3>Implementation Limitations<a class="headerlink" href="#implementation-limitations" title="Link to this heading">¶</a></h3>
<p>Some JSON deserializer implementations may set limits on:</p>
<ul class="simple">
<li><p>the size of accepted JSON texts</p></li>
<li><p>the maximum level of nesting of JSON objects and arrays</p></li>
<li><p>the range and precision of JSON numbers</p></li>
<li><p>the content and maximum length of JSON strings</p></li>
</ul>
<p>This module does not impose any such limits beyond those of the relevant
Python datatypes themselves or the Python interpreter itself.</p>
<p>When serializing to JSON, beware any such limitations in applications that may
consume your JSON.  In particular, it is common for JSON numbers to be
deserialized into IEEE 754 double precision numbers and thus subject to that
representation’s range and precision limitations.  This is especially relevant
when serializing Python <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> values of extremely large magnitude, or
when serializing instances of “exotic” numerical types such as
<a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a>.</p>
</section>
</section>
<section id="module-json.tool">
<span id="command-line-interface"></span><span id="json-commandline"></span><h2>Command Line Interface<a class="headerlink" href="#module-json.tool" title="Link to this heading">¶</a></h2>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/json/tool.py">Lib/json/tool.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-json.tool" title="json.tool: A command line to validate and pretty-print JSON."><code class="xref py py-mod docutils literal notranslate"><span class="pre">json.tool</span></code></a> module provides a simple command line interface to validate
and pretty-print JSON objects.</p>
<p>If the optional <code class="docutils literal notranslate"><span class="pre">infile</span></code> and <code class="docutils literal notranslate"><span class="pre">outfile</span></code> arguments are not
specified, <a class="reference internal" href="sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a> and <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> will be used respectively:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;{&quot;json&quot;: &quot;obj&quot;}&#39;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool
<span class="go">{</span>
<span class="go">    &quot;json&quot;: &quot;obj&quot;</span>
<span class="go">}</span>
<span class="gp">$ </span><span class="nb">echo</span><span class="w"> </span><span class="s1">&#39;{1.2:3.4}&#39;</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool
<span class="go">Expecting property name enclosed in double quotes: line 1 column 2 (char 1)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The output is now in the same order as the input. Use the
<a class="reference internal" href="#cmdoption-json.tool-sort-keys"><code class="xref std std-option docutils literal notranslate"><span class="pre">--sort-keys</span></code></a> option to sort the output of dictionaries
alphabetically by key.</p>
</div>
<section id="command-line-options">
<h3>Command line options<a class="headerlink" href="#command-line-options" title="Link to this heading">¶</a></h3>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-arg-infile">
<span class="sig-name descname"><span class="pre">infile</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-arg-infile" title="Link to this definition">¶</a></dt>
<dd><p>The JSON file to be validated or pretty-printed:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>json.tool<span class="w"> </span>mp_films.json
<span class="go">[</span>
<span class="go">    {</span>
<span class="go">        &quot;title&quot;: &quot;And Now for Something Completely Different&quot;,</span>
<span class="go">        &quot;year&quot;: 1971</span>
<span class="go">    },</span>
<span class="go">    {</span>
<span class="go">        &quot;title&quot;: &quot;Monty Python and the Holy Grail&quot;,</span>
<span class="go">        &quot;year&quot;: 1975</span>
<span class="go">    }</span>
<span class="go">]</span>
</pre></div>
</div>
<p>If <em>infile</em> is not specified, read from <a class="reference internal" href="sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a>.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-arg-outfile">
<span class="sig-name descname"><span class="pre">outfile</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-arg-outfile" title="Link to this definition">¶</a></dt>
<dd><p>Write the output of the <em>infile</em> to the given <em>outfile</em>. Otherwise, write it
to <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a>.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-sort-keys">
<span class="sig-name descname"><span class="pre">--sort-keys</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-sort-keys" title="Link to this definition">¶</a></dt>
<dd><p>Sort the output of dictionaries alphabetically by key.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-no-ensure-ascii">
<span class="sig-name descname"><span class="pre">--no-ensure-ascii</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-no-ensure-ascii" title="Link to this definition">¶</a></dt>
<dd><p>Disable escaping of non-ascii characters, see <a class="reference internal" href="#json.dumps" title="json.dumps"><code class="xref py py-func docutils literal notranslate"><span class="pre">json.dumps()</span></code></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-json-lines">
<span class="sig-name descname"><span class="pre">--json-lines</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-json-lines" title="Link to this definition">¶</a></dt>
<dd><p>Parse every input line as separate JSON object.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-indent">
<span id="cmdoption-json.tool-tab"></span><span id="cmdoption-json.tool-no-indent"></span><span id="cmdoption-json.tool-compact"></span><span class="sig-name descname"><span class="pre">--indent</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--tab</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--no-indent</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--compact</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-indent" title="Link to this definition">¶</a></dt>
<dd><p>Mutually exclusive options for whitespace control.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-json.tool-h">
<span id="cmdoption-json.tool-help"></span><span class="sig-name descname"><span class="pre">-h</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--help</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-json.tool-h" title="Link to this definition">¶</a></dt>
<dd><p>Show the help message.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="rfc-errata" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>As noted in <a class="reference external" href="https://www.rfc-editor.org/errata_search.php?rfc=7159">the errata for RFC 7159</a>,
JSON permits literal U+2028 (LINE SEPARATOR) and
U+2029 (PARAGRAPH SEPARATOR) characters in strings, whereas JavaScript
(as of ECMAScript Edition 5.1) does not.</p>
</aside>
</aside>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a><ul>
<li><a class="reference internal" href="#basic-usage">Basic Usage</a></li>
<li><a class="reference internal" href="#encoders-and-decoders">Encoders and Decoders</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
<li><a class="reference internal" href="#standard-compliance-and-interoperability">Standard Compliance and Interoperability</a><ul>
<li><a class="reference internal" href="#character-encodings">Character Encodings</a></li>
<li><a class="reference internal" href="#infinite-and-nan-number-values">Infinite and NaN Number Values</a></li>
<li><a class="reference internal" href="#repeated-names-within-an-object">Repeated Names Within an Object</a></li>
<li><a class="reference internal" href="#top-level-non-object-non-array-values">Top-level Non-Object, Non-Array Values</a></li>
<li><a class="reference internal" href="#implementation-limitations">Implementation Limitations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-json.tool">Command Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command line options</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="email.iterators.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">email.iterators</span></code>: Iterators</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="mailbox.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">mailbox</span></code> — Manipulate mailboxes in various formats</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/json.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="mailbox.html" title="mailbox — Manipulate mailboxes in various formats"
             >next</a> |</li>
        <li class="right" >
          <a href="email.iterators.html" title="email.iterators: Iterators"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="netdata.html" >Internet Data Handling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code> — JSON encoder and decoder</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>