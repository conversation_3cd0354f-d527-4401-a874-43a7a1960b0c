# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payulatam
# 
# Translators:
# Wil Odo<PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "Invalid payment status."
msgstr "Estado de pago no válido."

#. module: payment_payulatam
#. odoo-python
#: code:addons/payment_payulatam/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_payulatam
#: model:ir.model.fields.selection,name:payment_payulatam.selection__payment_provider__code__payulatam
#: model:payment.provider,name:payment_payulatam.payment_provider_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_api_key
msgid "PayU Latam API Key"
msgstr "Clave API de PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_account_id
msgid "PayU Latam Account ID"
msgstr "ID de la cuenta PayU Latam"

#. module: payment_payulatam
#: model:ir.model.fields,field_description:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "PayU Latam Merchant ID"
msgstr "ID del comerciante PayU Latam"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_provider
msgid "Payment Provider"
msgstr "Proveedor de pago"

#. module: payment_payulatam
#: model:ir.model,name:payment_payulatam.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_merchant_id
msgid "The ID solely used to identify the account with PayULatam"
msgstr "El ID que se utiliza solo para identificar la cuenta con PayULatam"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__payulatam_account_id
msgid ""
"The ID solely used to identify the country-dependent shop with PayULatam"
msgstr ""
"El ID que se utiliza solo para identificar la tienda dependiente del país "
"con PayULatam"

#. module: payment_payulatam
#: model:ir.model.fields,help:payment_payulatam.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "El código técnico de este proveedor de pagos."

#. module: payment_payulatam
#: model_terms:ir.ui.view,arch_db:payment_payulatam.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Mercado Pago</strong>."
msgstr ""
"Este es un proveedor obsoleto.\n"
"                    Considere deshabilitarlo y cambiar a <strong>Mercado Pago</strong>."

#. module: payment_payulatam
#: model_terms:payment.provider,auth_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been authorized."
msgstr "Se autorizó su pago."

#. module: payment_payulatam
#: model_terms:payment.provider,cancel_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been cancelled."
msgstr "Se canceló su pago."

#. module: payment_payulatam
#: model_terms:payment.provider,pending_msg:payment_payulatam.payment_provider_payulatam
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Su pago se procesó con éxito pero está en espera de aprobación."

#. module: payment_payulatam
#: model_terms:payment.provider,done_msg:payment_payulatam.payment_provider_payulatam
msgid "Your payment has been successfully processed."
msgstr "Su pago se proceso con éxito."
