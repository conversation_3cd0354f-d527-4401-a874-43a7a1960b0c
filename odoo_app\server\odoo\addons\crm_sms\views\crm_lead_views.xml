<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add action entry in the Action Menu for Leads -->
    <record id="crm_lead_act_window_sms_composer_single" model="ir.actions.act_window">
        <field name="name">Send SMS Text Message</field>
        <field name="res_model">sms.composer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_composition_mode': 'mass',
            'default_mass_keep_log': True,
            'default_res_ids': active_ids
        }</field>
        <field name="binding_model_id" ref="crm.model_crm_lead"/>
        <field name="binding_view_types">list</field>
    </record>
    <record id="crm_lead_act_window_sms_composer_multi" model="ir.actions.act_window">
        <field name="name">Send SMS Text Message</field>
        <field name="res_model">sms.composer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_composition_mode': 'comment',
            'default_res_id': active_id,
        }</field>
        <field name="binding_model_id" ref="crm.model_crm_lead"/>
        <field name="binding_view_types">form</field>
    </record>

    <record id="crm_case_tree_view_oppor" model="ir.ui.view">
        <field name="name">crm.lead.tree.opportunity.inherit.sms</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_case_tree_view_oppor"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="%(crm_sms.crm_lead_act_window_sms_composer_single)d" type="action" string="SMS" />
            </xpath>
            <xpath expr="//button[@name='%(crm.action_lead_mail_compose)d']" position="after">
                <button name="%(crm_sms.crm_lead_act_window_sms_composer_multi)d" type="action" string="SMS" icon="fa-comments" />
            </xpath>
        </field>
    </record>

    <record id="crm_lead_view_tree_opportunity_reporting" model="ir.ui.view">
        <field name="name">crm.lead.tree.opportunity.reporting.inherit.sms</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_lead_view_tree_opportunity_reporting"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='%(crm_sms.crm_lead_act_window_sms_composer_multi)d']" position="replace"/>
        </field>
    </record>

</odoo>
