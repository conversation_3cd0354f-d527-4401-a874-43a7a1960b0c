<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.PopupContainer">
        <div t-if="Object.keys(props.popups).length > 0" class="popups">
            <t t-foreach="props.popups" t-as="popup" t-key="popup">
                <div role="dialog"
                    class="modal-dialog position-absolute start-0 top-0 d-flex align-items-center justify-content-center h-100 w-100 mw-100 m-0 p-0 pe-auto bg-dark bg-opacity-50"
                    t-att-style="`z-index: ${popup_value.props.zIndex}`">
                    <t t-component="popup_value.component" t-props="popup_value.props" />
                </div>
            </t>
        </div>
    </t>

</templates>
