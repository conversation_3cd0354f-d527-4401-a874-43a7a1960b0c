<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="venv — Creation of virtual environments" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/venv.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/venv/ The venv module supports creating lightweight “virtual environments”, each with their own independent set of Python packages installed in their site directories. A virtual en..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/venv/ The venv module supports creating lightweight “virtual environments”, each with their own independent set of Python packages installed in their site directories. A virtual en..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>venv — Creation of virtual environments &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="zipapp — Manage executable Python zip archives" href="zipapp.html" />
    <link rel="prev" title="ensurepip — Bootstrapping the pip installer" href="ensurepip.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/venv.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a><ul>
<li><a class="reference internal" href="#creating-virtual-environments">Creating virtual environments</a></li>
<li><a class="reference internal" href="#how-venvs-work">How venvs work</a></li>
<li><a class="reference internal" href="#api">API</a></li>
<li><a class="reference internal" href="#an-example-of-extending-envbuilder">An example of extending <code class="docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ensurepip.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="zipapp.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipapp</span></code> — Manage executable Python zip archives</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/venv.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="zipapp.html" title="zipapp — Manage executable Python zip archives"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="ensurepip.html" title="ensurepip — Bootstrapping the pip installer"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="distribution.html" accesskey="U">Software Packaging and Distribution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-venv">
<span id="venv-creation-of-virtual-environments"></span><h1><a class="reference internal" href="#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code></a> — Creation of virtual environments<a class="headerlink" href="#module-venv" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/venv/">Lib/venv/</a></p>
<hr class="docutils" id="index-0" />
<p id="venv-intro"><span id="venv-def"></span>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> module supports creating lightweight “virtual environments”,
each with their own independent set of Python packages installed in
their <a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> directories.
A virtual environment is created on top of an existing
Python installation, known as the virtual environment’s “base” Python, and may
optionally be isolated from the packages in the base environment,
so only those explicitly installed in the virtual environment are available.</p>
<p>When used from within a virtual environment, common installation tools such as
<a class="reference external" href="https://pypi.org/project/pip/">pip</a> will install Python packages into a virtual environment
without needing to be told to do so explicitly.</p>
<p>A virtual environment is (amongst other things):</p>
<ul class="simple">
<li><p>Used to contain a specific Python interpreter and software libraries and
binaries which are needed to support a project (library or application). These
are by default isolated from software in other virtual environments and Python
interpreters and libraries installed in the operating system.</p></li>
<li><p>Contained in a directory, conventionally either named <code class="docutils literal notranslate"><span class="pre">venv</span></code> or <code class="docutils literal notranslate"><span class="pre">.venv</span></code> in
the project directory, or under a container directory for lots of virtual
environments, such as <code class="docutils literal notranslate"><span class="pre">~/.virtualenvs</span></code>.</p></li>
<li><p>Not checked into source control systems such as Git.</p></li>
<li><p>Considered as disposable – it should be simple to delete and recreate it from
scratch. You don’t place any project code in the environment</p></li>
<li><p>Not considered as movable or copyable – you just recreate the same
environment in the target location.</p></li>
</ul>
<p>See <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0405/"><strong>PEP 405</strong></a> for more background on Python virtual environments.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://packaging.python.org/guides/installing-using-pip-and-virtual-environments/#create-and-use-virtual-environments">Python Packaging User Guide: Creating and using virtual environments</a></p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<section id="creating-virtual-environments">
<h2>Creating virtual environments<a class="headerlink" href="#creating-virtual-environments" title="Link to this heading">¶</a></h2>
<p>Creation of <a class="reference internal" href="#venv-def"><span class="std std-ref">virtual environments</span></a> is done by executing the
command <code class="docutils literal notranslate"><span class="pre">venv</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">venv</span> <span class="o">/</span><span class="n">path</span><span class="o">/</span><span class="n">to</span><span class="o">/</span><span class="n">new</span><span class="o">/</span><span class="n">virtual</span><span class="o">/</span><span class="n">environment</span>
</pre></div>
</div>
<p>Running this command creates the target directory (creating any parent
directories that don’t exist already) and places a <code class="docutils literal notranslate"><span class="pre">pyvenv.cfg</span></code> file in it
with a <code class="docutils literal notranslate"><span class="pre">home</span></code> key pointing to the Python installation from which the command
was run (a common name for the target directory is <code class="docutils literal notranslate"><span class="pre">.venv</span></code>).  It also creates
a <code class="docutils literal notranslate"><span class="pre">bin</span></code> (or <code class="docutils literal notranslate"><span class="pre">Scripts</span></code> on Windows) subdirectory containing a copy/symlink
of the Python binary/binaries (as appropriate for the platform or arguments
used at environment creation time). It also creates an (initially empty)
<code class="docutils literal notranslate"><span class="pre">lib/pythonX.Y/site-packages</span></code> subdirectory (on Windows, this is
<code class="docutils literal notranslate"><span class="pre">Lib\site-packages</span></code>). If an existing directory is specified, it will be
re-used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The use of <code class="docutils literal notranslate"><span class="pre">venv</span></code> is now recommended for creating virtual environments.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span><code class="docutils literal notranslate"><span class="pre">pyvenv</span></code> was the recommended tool for creating virtual environments for
Python 3.3 and 3.4, and is
<a class="reference internal" href="../whatsnew/3.6.html#whatsnew36-venv"><span class="std std-ref">deprecated in Python 3.6</span></a>.</p>
</div>
<p>On Windows, invoke the <code class="docutils literal notranslate"><span class="pre">venv</span></code> command as follows:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>c:\&gt;Python35\python -m venv c:\path\to\myenv
</pre></div>
</div>
<p>Alternatively, if you configured the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> and <code class="docutils literal notranslate"><span class="pre">PATHEXT</span></code> variables for
your <a class="reference internal" href="../using/windows.html#using-on-windows"><span class="std std-ref">Python installation</span></a>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>c:\&gt;python -m venv c:\path\to\myenv
</pre></div>
</div>
<p>The command, if run with <code class="docutils literal notranslate"><span class="pre">-h</span></code>, will show the available options:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>usage: venv [-h] [--system-site-packages] [--symlinks | --copies] [--clear]
            [--upgrade] [--without-pip] [--prompt PROMPT] [--upgrade-deps]
            ENV_DIR [ENV_DIR ...]

Creates virtual Python environments in one or more target directories.

positional arguments:
  ENV_DIR               A directory to create the environment in.

optional arguments:
  -h, --help            show this help message and exit
  --system-site-packages
                        Give the virtual environment access to the system
                        site-packages dir.
  --symlinks            Try to use symlinks rather than copies, when symlinks
                        are not the default for the platform.
  --copies              Try to use copies rather than symlinks, even when
                        symlinks are the default for the platform.
  --clear               Delete the contents of the environment directory if it
                        already exists, before environment creation.
  --upgrade             Upgrade the environment directory to use this version
                        of Python, assuming Python has been upgraded in-place.
  --without-pip         Skips installing or upgrading pip in the virtual
                        environment (pip is bootstrapped by default)
  --prompt PROMPT       Provides an alternative prompt prefix for this
                        environment.
  --upgrade-deps        Upgrade core dependencies (pip) to the
                        latest version in PyPI

Once an environment has been created, you may wish to activate it, e.g. by
sourcing an activate script in its bin directory.
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">setuptools</span></code> is no longer a core venv dependency.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Add <code class="docutils literal notranslate"><span class="pre">--upgrade-deps</span></code> option to upgrade pip + setuptools to the latest on PyPI</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Installs pip by default, added the <code class="docutils literal notranslate"><span class="pre">--without-pip</span></code>  and <code class="docutils literal notranslate"><span class="pre">--copies</span></code>
options</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>In earlier versions, if the target directory already existed, an error was
raised, unless the <code class="docutils literal notranslate"><span class="pre">--clear</span></code> or <code class="docutils literal notranslate"><span class="pre">--upgrade</span></code> option was provided.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>While symlinks are supported on Windows, they are not recommended. Of
particular note is that double-clicking <code class="docutils literal notranslate"><span class="pre">python.exe</span></code> in File Explorer
will resolve the symlink eagerly and ignore the virtual environment.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Microsoft Windows, it may be required to enable the <code class="docutils literal notranslate"><span class="pre">Activate.ps1</span></code>
script by setting the execution policy for the user. You can do this by
issuing the following PowerShell command:</p>
<p>PS C:&gt; Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser</p>
<p>See <a class="reference external" href="https://go.microsoft.com/fwlink/?LinkID=135170">About Execution Policies</a>
for more information.</p>
</div>
<p>The created <code class="docutils literal notranslate"><span class="pre">pyvenv.cfg</span></code> file also includes the
<code class="docutils literal notranslate"><span class="pre">include-system-site-packages</span></code> key, set to <code class="docutils literal notranslate"><span class="pre">true</span></code> if <code class="docutils literal notranslate"><span class="pre">venv</span></code> is
run with the <code class="docutils literal notranslate"><span class="pre">--system-site-packages</span></code> option, <code class="docutils literal notranslate"><span class="pre">false</span></code> otherwise.</p>
<p>Unless the <code class="docutils literal notranslate"><span class="pre">--without-pip</span></code> option is given, <a class="reference internal" href="ensurepip.html#module-ensurepip" title="ensurepip: Bootstrapping the &quot;pip&quot; installer into an existing Python installation or virtual environment."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code></a> will be
invoked to bootstrap <code class="docutils literal notranslate"><span class="pre">pip</span></code> into the virtual environment.</p>
<p>Multiple paths can be given to <code class="docutils literal notranslate"><span class="pre">venv</span></code>, in which case an identical virtual
environment will be created, according to the given options, at each provided
path.</p>
</section>
<section id="how-venvs-work">
<span id="venv-explanation"></span><h2>How venvs work<a class="headerlink" href="#how-venvs-work" title="Link to this heading">¶</a></h2>
<p>When a Python interpreter is running from a virtual environment,
<a class="reference internal" href="sys.html#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.prefix</span></code></a> and <a class="reference internal" href="sys.html#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code></a>
point to the directories of the virtual environment,
whereas <a class="reference internal" href="sys.html#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.base_prefix</span></code></a> and <a class="reference internal" href="sys.html#sys.base_exec_prefix" title="sys.base_exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.base_exec_prefix</span></code></a>
point to those of the base Python used to create the environment.
It is sufficient to check
<code class="docutils literal notranslate"><span class="pre">sys.prefix</span> <span class="pre">!=</span> <span class="pre">sys.base_prefix</span></code> to determine if the current interpreter is
running from a virtual environment.</p>
<p>A virtual environment may be “activated” using a script in its binary directory
(<code class="docutils literal notranslate"><span class="pre">bin</span></code> on POSIX; <code class="docutils literal notranslate"><span class="pre">Scripts</span></code> on Windows).
This will prepend that directory to your <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code>, so that running
<strong class="program">python</strong> will invoke the environment’s Python interpreter
and you can run installed scripts without having to use their full path.
The invocation of the activation script is platform-specific
(<code class="samp docutils literal notranslate"><em><span class="pre">&lt;venv&gt;</span></em></code> must be replaced by the path to the directory
containing the virtual environment):</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Platform</p></th>
<th class="head"><p>Shell</p></th>
<th class="head"><p>Command to activate virtual environment</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td rowspan="4"><p>POSIX</p></td>
<td><p>bash/zsh</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">$</span> <span class="pre">source</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">/bin/activate</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>fish</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">$</span> <span class="pre">source</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">/bin/activate.fish</span></code></p></td>
</tr>
<tr class="row-even"><td><p>csh/tcsh</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">$</span> <span class="pre">source</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">/bin/activate.csh</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>PowerShell</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">$</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">/bin/Activate.ps1</span></code></p></td>
</tr>
<tr class="row-even"><td rowspan="2"><p>Windows</p></td>
<td><p>cmd.exe</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">C:\&gt;</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">\Scripts\activate.bat</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>PowerShell</p></td>
<td><p><code class="samp docutils literal notranslate"><span class="pre">PS</span> <span class="pre">C:\&gt;</span> <em><span class="pre">&lt;venv&gt;</span></em><span class="pre">\Scripts\Activate.ps1</span></code></p></td>
</tr>
</tbody>
</table>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span><strong class="program">fish</strong> and <strong class="program">csh</strong> activation scripts.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>PowerShell activation scripts installed under POSIX for PowerShell Core
support.</p>
</div>
<p>You don’t specifically <em>need</em> to activate a virtual environment,
as you can just specify the full path to that environment’s
Python interpreter when invoking Python.
Furthermore, all scripts installed in the environment
should be runnable without activating it.</p>
<p>In order to achieve this, scripts installed into virtual environments have
a “shebang” line which points to the environment’s Python interpreter,
i.e. <code class="samp docutils literal notranslate"><span class="pre">#!/</span><em><span class="pre">&lt;path-to-venv&gt;</span></em><span class="pre">/bin/python</span></code>.
This means that the script will run with that interpreter regardless of the
value of <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code>. On Windows, “shebang” line processing is supported if
you have the <a class="reference internal" href="../using/windows.html#launcher"><span class="std std-ref">Python Launcher for Windows</span></a> installed. Thus, double-clicking an installed
script in a Windows Explorer window should run it with the correct interpreter
without the environment needing to be activated or on the <span class="target" id="index-4"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code>.</p>
<p>When a virtual environment has been activated, the <code class="xref std std-envvar docutils literal notranslate"><span class="pre">VIRTUAL_ENV</span></code>
environment variable is set to the path of the environment.
Since explicitly activating a virtual environment is not required to use it,
<code class="xref std std-envvar docutils literal notranslate"><span class="pre">VIRTUAL_ENV</span></code> cannot be relied upon to determine
whether a virtual environment is being used.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Because scripts installed in environments should not expect the
environment to be activated, their shebang lines contain the absolute paths
to their environment’s interpreters. Because of this, environments are
inherently non-portable, in the general case. You should always have a
simple means of recreating an environment (for example, if you have a
requirements file <code class="docutils literal notranslate"><span class="pre">requirements.txt</span></code>, you can invoke <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">-r</span>
<span class="pre">requirements.txt</span></code> using the environment’s <code class="docutils literal notranslate"><span class="pre">pip</span></code> to install all of the
packages needed by the environment). If for any reason you need to move the
environment to a new location, you should recreate it at the desired
location and delete the one at the old location. If you move an environment
because you moved a parent directory of it, you should recreate the
environment in its new location. Otherwise, software installed into the
environment may not work as expected.</p>
</div>
<p>You can deactivate a virtual environment by typing <code class="docutils literal notranslate"><span class="pre">deactivate</span></code> in your shell.
The exact mechanism is platform-specific and is an internal implementation
detail (typically, a script or shell function will be used).</p>
</section>
<section id="api">
<span id="venv-api"></span><h2>API<a class="headerlink" href="#api" title="Link to this heading">¶</a></h2>
<p>The high-level method described above makes use of a simple API which provides
mechanisms for third-party virtual environment creators to customize environment
creation according to their needs, the <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> class.</p>
<dl class="py class">
<dt class="sig sig-object py" id="venv.EnvBuilder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">venv.</span></span><span class="sig-name descname"><span class="pre">EnvBuilder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">system_site_packages</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">clear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">upgrade</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">with_pip</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prompt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">upgrade_deps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> class accepts the following keyword arguments on
instantiation:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">system_site_packages</span></code> – a Boolean value indicating that the system Python
site-packages should be available to the environment (defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">clear</span></code> – a Boolean value which, if true, will delete the contents of
any existing target directory, before creating the environment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">symlinks</span></code> – a Boolean value indicating whether to attempt to symlink the
Python binary rather than copying.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">upgrade</span></code> – a Boolean value which, if true, will upgrade an existing
environment with the running Python - for use when that Python has been
upgraded in-place (defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">with_pip</span></code> – a Boolean value which, if true, ensures pip is
installed in the virtual environment. This uses <a class="reference internal" href="ensurepip.html#module-ensurepip" title="ensurepip: Bootstrapping the &quot;pip&quot; installer into an existing Python installation or virtual environment."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code></a> with
the <code class="docutils literal notranslate"><span class="pre">--default-pip</span></code> option.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">prompt</span></code> – a String to be used after virtual environment is activated
(defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code> which means directory name of the environment would
be used). If the special string <code class="docutils literal notranslate"><span class="pre">&quot;.&quot;</span></code> is provided, the basename of the
current directory is used as the prompt.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">upgrade_deps</span></code> – Update the base venv modules to the latest on PyPI</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <code class="docutils literal notranslate"><span class="pre">with_pip</span></code> parameter</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <code class="docutils literal notranslate"><span class="pre">prompt</span></code> parameter</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added the <code class="docutils literal notranslate"><span class="pre">upgrade_deps</span></code> parameter</p>
</div>
<p>Creators of third-party virtual environment tools will be free to use the
provided <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> class as a base class.</p>
<p>The returned env-builder is an object which has a method, <code class="docutils literal notranslate"><span class="pre">create</span></code>:</p>
<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.create">
<span class="sig-name descname"><span class="pre">create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">env_dir</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.create" title="Link to this definition">¶</a></dt>
<dd><p>Create a virtual environment by specifying the target directory
(absolute or relative to the current directory) which is to contain the
virtual environment.  The <code class="docutils literal notranslate"><span class="pre">create</span></code> method will either create the
environment in the specified directory, or raise an appropriate
exception.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">create</span></code> method of the <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> class illustrates the
hooks available for subclass customization:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">create</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">env_dir</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Create a virtualized Python environment in a directory.</span>
<span class="sd">    env_dir is the target directory to create an environment in.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">env_dir</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">abspath</span><span class="p">(</span><span class="n">env_dir</span><span class="p">)</span>
    <span class="n">context</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ensure_directories</span><span class="p">(</span><span class="n">env_dir</span><span class="p">)</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">create_configuration</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">setup_python</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">setup_scripts</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">post_setup</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>
</pre></div>
</div>
<p>Each of the methods <a class="reference internal" href="#venv.EnvBuilder.ensure_directories" title="venv.EnvBuilder.ensure_directories"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ensure_directories()</span></code></a>,
<a class="reference internal" href="#venv.EnvBuilder.create_configuration" title="venv.EnvBuilder.create_configuration"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create_configuration()</span></code></a>, <a class="reference internal" href="#venv.EnvBuilder.setup_python" title="venv.EnvBuilder.setup_python"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_python()</span></code></a>,
<a class="reference internal" href="#venv.EnvBuilder.setup_scripts" title="venv.EnvBuilder.setup_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_scripts()</span></code></a> and <a class="reference internal" href="#venv.EnvBuilder.post_setup" title="venv.EnvBuilder.post_setup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">post_setup()</span></code></a> can be overridden.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.ensure_directories">
<span class="sig-name descname"><span class="pre">ensure_directories</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">env_dir</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.ensure_directories" title="Link to this definition">¶</a></dt>
<dd><p>Creates the environment directory and all necessary subdirectories that
don’t already exist, and returns a context object.  This context object
is just a holder for attributes (such as paths) for use by the other
methods.  If the <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> is created with the arg
<code class="docutils literal notranslate"><span class="pre">clear=True</span></code>, contents of the environment directory will be cleared
and then all necessary subdirectories will be recreated.</p>
<p>The returned context object is a <a class="reference internal" href="types.html#types.SimpleNamespace" title="types.SimpleNamespace"><code class="xref py py-class docutils literal notranslate"><span class="pre">types.SimpleNamespace</span></code></a> with the
following attributes:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">env_dir</span></code> - The location of the virtual environment. Used for
<code class="docutils literal notranslate"><span class="pre">__VENV_DIR__</span></code> in activation scripts (see <a class="reference internal" href="#venv.EnvBuilder.install_scripts" title="venv.EnvBuilder.install_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install_scripts()</span></code></a>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">env_name</span></code> - The name of the virtual environment. Used for
<code class="docutils literal notranslate"><span class="pre">__VENV_NAME__</span></code> in activation scripts (see <a class="reference internal" href="#venv.EnvBuilder.install_scripts" title="venv.EnvBuilder.install_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install_scripts()</span></code></a>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">prompt</span></code> - The prompt to be used by the activation scripts. Used for
<code class="docutils literal notranslate"><span class="pre">__VENV_PROMPT__</span></code> in activation scripts (see <a class="reference internal" href="#venv.EnvBuilder.install_scripts" title="venv.EnvBuilder.install_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install_scripts()</span></code></a>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">executable</span></code> - The underlying Python executable used by the virtual
environment. This takes into account the case where a virtual environment
is created from another virtual environment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">inc_path</span></code> - The include path for the virtual environment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">lib_path</span></code> - The purelib path for the virtual environment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bin_path</span></code> - The script path for the virtual environment.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">bin_name</span></code> - The name of the script path relative to the virtual
environment location. Used for <code class="docutils literal notranslate"><span class="pre">__VENV_BIN_NAME__</span></code> in activation
scripts (see <a class="reference internal" href="#venv.EnvBuilder.install_scripts" title="venv.EnvBuilder.install_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install_scripts()</span></code></a>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">env_exe</span></code> - The name of the Python interpreter in the virtual
environment. Used for <code class="docutils literal notranslate"><span class="pre">__VENV_PYTHON__</span></code> in activation scripts
(see <a class="reference internal" href="#venv.EnvBuilder.install_scripts" title="venv.EnvBuilder.install_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install_scripts()</span></code></a>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">env_exec_cmd</span></code> - The name of the Python interpreter, taking into
account filesystem redirections. This can be used to run Python in
the virtual environment.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <em>venv</em>
<a class="reference internal" href="sysconfig.html#installation-paths"><span class="std std-ref">sysconfig installation scheme</span></a>
is used to construct the paths of the created directories.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The attribute <code class="docutils literal notranslate"><span class="pre">lib_path</span></code> was added to the context, and the context
object was documented.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.create_configuration">
<span class="sig-name descname"><span class="pre">create_configuration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.create_configuration" title="Link to this definition">¶</a></dt>
<dd><p>Creates the <code class="docutils literal notranslate"><span class="pre">pyvenv.cfg</span></code> configuration file in the environment.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.setup_python">
<span class="sig-name descname"><span class="pre">setup_python</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.setup_python" title="Link to this definition">¶</a></dt>
<dd><p>Creates a copy or symlink to the Python executable in the environment.
On POSIX systems, if a specific executable <code class="docutils literal notranslate"><span class="pre">python3.x</span></code> was used,
symlinks to <code class="docutils literal notranslate"><span class="pre">python</span></code> and <code class="docutils literal notranslate"><span class="pre">python3</span></code> will be created pointing to that
executable, unless files with those names already exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.setup_scripts">
<span class="sig-name descname"><span class="pre">setup_scripts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.setup_scripts" title="Link to this definition">¶</a></dt>
<dd><p>Installs activation scripts appropriate to the platform into the virtual
environment.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.upgrade_dependencies">
<span class="sig-name descname"><span class="pre">upgrade_dependencies</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.upgrade_dependencies" title="Link to this definition">¶</a></dt>
<dd><p>Upgrades the core venv dependency packages (currently <code class="docutils literal notranslate"><span class="pre">pip</span></code>)
in the environment. This is done by shelling out to the
<code class="docutils literal notranslate"><span class="pre">pip</span></code> executable in the environment.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><code class="docutils literal notranslate"><span class="pre">setuptools</span></code> is no longer a core venv dependency.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.post_setup">
<span class="sig-name descname"><span class="pre">post_setup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.post_setup" title="Link to this definition">¶</a></dt>
<dd><p>A placeholder method which can be overridden in third party
implementations to pre-install packages in the virtual environment or
perform other post-creation steps.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7.2: </span>Windows now uses redirector scripts for <code class="docutils literal notranslate"><span class="pre">python[w].exe</span></code> instead of
copying the actual binaries. In 3.7.2 only <a class="reference internal" href="#venv.EnvBuilder.setup_python" title="venv.EnvBuilder.setup_python"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_python()</span></code></a> does
nothing unless running from a build in the source tree.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7.3: </span>Windows copies the redirector scripts as part of <a class="reference internal" href="#venv.EnvBuilder.setup_python" title="venv.EnvBuilder.setup_python"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_python()</span></code></a>
instead of <a class="reference internal" href="#venv.EnvBuilder.setup_scripts" title="venv.EnvBuilder.setup_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_scripts()</span></code></a>. This was not the case in 3.7.2.
When using symlinks, the original executables will be linked.</p>
</div>
<p>In addition, <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> provides this utility method that can be
called from <a class="reference internal" href="#venv.EnvBuilder.setup_scripts" title="venv.EnvBuilder.setup_scripts"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setup_scripts()</span></code></a> or <a class="reference internal" href="#venv.EnvBuilder.post_setup" title="venv.EnvBuilder.post_setup"><code class="xref py py-meth docutils literal notranslate"><span class="pre">post_setup()</span></code></a> in subclasses to
assist in installing custom scripts into the virtual environment.</p>
<dl class="py method">
<dt class="sig sig-object py" id="venv.EnvBuilder.install_scripts">
<span class="sig-name descname"><span class="pre">install_scripts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.EnvBuilder.install_scripts" title="Link to this definition">¶</a></dt>
<dd><p><em>path</em> is the path to a directory that should contain subdirectories
“common”, “posix”, “nt”, each containing scripts destined for the bin
directory in the environment.  The contents of “common” and the
directory corresponding to <a class="reference internal" href="os.html#os.name" title="os.name"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.name</span></code></a> are copied after some text
replacement of placeholders:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">__VENV_DIR__</span></code> is replaced with the absolute path of the environment
directory.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__VENV_NAME__</span></code> is replaced with the environment name (final path
segment of environment directory).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__VENV_PROMPT__</span></code> is replaced with the prompt (the environment
name surrounded by parentheses and with a following space)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__VENV_BIN_NAME__</span></code> is replaced with the name of the bin directory
(either <code class="docutils literal notranslate"><span class="pre">bin</span></code> or <code class="docutils literal notranslate"><span class="pre">Scripts</span></code>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__VENV_PYTHON__</span></code> is replaced with the absolute path of the
environment’s executable.</p></li>
</ul>
<p>The directories are allowed to exist (for when an existing environment
is being upgraded).</p>
</dd></dl>

</dd></dl>

<p>There is also a module-level convenience function:</p>
<dl class="py function">
<dt class="sig sig-object py" id="venv.create">
<span class="sig-prename descclassname"><span class="pre">venv.</span></span><span class="sig-name descname"><span class="pre">create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">env_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system_site_packages</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">clear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">with_pip</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prompt</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">upgrade_deps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#venv.create" title="Link to this definition">¶</a></dt>
<dd><p>Create an <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> with the given keyword arguments, and call its
<a class="reference internal" href="#venv.EnvBuilder.create" title="venv.EnvBuilder.create"><code class="xref py py-meth docutils literal notranslate"><span class="pre">create()</span></code></a> method with the <em>env_dir</em> argument.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <code class="docutils literal notranslate"><span class="pre">with_pip</span></code> parameter</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added the <code class="docutils literal notranslate"><span class="pre">prompt</span></code> parameter</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added the <code class="docutils literal notranslate"><span class="pre">upgrade_deps</span></code> parameter</p>
</div>
</dd></dl>

</section>
<section id="an-example-of-extending-envbuilder">
<h2>An example of extending <code class="docutils literal notranslate"><span class="pre">EnvBuilder</span></code><a class="headerlink" href="#an-example-of-extending-envbuilder" title="Link to this heading">¶</a></h2>
<p>The following script shows how to extend <a class="reference internal" href="#venv.EnvBuilder" title="venv.EnvBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a> by implementing a
subclass which installs setuptools and pip into a created virtual environment:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">os.path</span>
<span class="kn">from</span> <span class="nn">subprocess</span> <span class="kn">import</span> <span class="n">Popen</span><span class="p">,</span> <span class="n">PIPE</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">from</span> <span class="nn">threading</span> <span class="kn">import</span> <span class="n">Thread</span>
<span class="kn">from</span> <span class="nn">urllib.parse</span> <span class="kn">import</span> <span class="n">urlparse</span>
<span class="kn">from</span> <span class="nn">urllib.request</span> <span class="kn">import</span> <span class="n">urlretrieve</span>
<span class="kn">import</span> <span class="nn">venv</span>

<span class="k">class</span> <span class="nc">ExtendedEnvBuilder</span><span class="p">(</span><span class="n">venv</span><span class="o">.</span><span class="n">EnvBuilder</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    This builder installs setuptools and pip so that you can pip or</span>
<span class="sd">    easy_install other packages into the created virtual environment.</span>

<span class="sd">    :param nodist: If true, setuptools and pip are not installed into the</span>
<span class="sd">                   created virtual environment.</span>
<span class="sd">    :param nopip: If true, pip is not installed into the created</span>
<span class="sd">                  virtual environment.</span>
<span class="sd">    :param progress: If setuptools or pip are installed, the progress of the</span>
<span class="sd">                     installation can be monitored by passing a progress</span>
<span class="sd">                     callable. If specified, it is called with two</span>
<span class="sd">                     arguments: a string indicating some progress, and a</span>
<span class="sd">                     context indicating where the string is coming from.</span>
<span class="sd">                     The context argument can have one of three values:</span>
<span class="sd">                     &#39;main&#39;, indicating that it is called from virtualize()</span>
<span class="sd">                     itself, and &#39;stdout&#39; and &#39;stderr&#39;, which are obtained</span>
<span class="sd">                     by reading lines from the output streams of a subprocess</span>
<span class="sd">                     which is used to install the app.</span>

<span class="sd">                     If a callable is not specified, default progress</span>
<span class="sd">                     information is output to sys.stderr.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">nodist</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;nodist&#39;</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">nopip</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;nopip&#39;</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">progress</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;progress&#39;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">verbose</span> <span class="o">=</span> <span class="n">kwargs</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="s1">&#39;verbose&#39;</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">post_setup</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">context</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Set up any packages which need to be pre-installed into the</span>
<span class="sd">        virtual environment being created.</span>

<span class="sd">        :param context: The information for the virtual environment</span>
<span class="sd">                        creation request being processed.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s1">&#39;VIRTUAL_ENV&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">env_dir</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">nodist</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">install_setuptools</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>
        <span class="c1"># Can&#39;t install pip without setuptools</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">nopip</span> <span class="ow">and</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">nodist</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">install_pip</span><span class="p">(</span><span class="n">context</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">reader</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">stream</span><span class="p">,</span> <span class="n">context</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Read lines from a subprocess&#39; output stream and either pass to a progress</span>
<span class="sd">        callable (if specified) or write progress information to sys.stderr.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">progress</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">progress</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="n">s</span> <span class="o">=</span> <span class="n">stream</span><span class="o">.</span><span class="n">readline</span><span class="p">()</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">s</span><span class="p">:</span>
                <span class="k">break</span>
            <span class="k">if</span> <span class="n">progress</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">progress</span><span class="p">(</span><span class="n">s</span><span class="p">,</span> <span class="n">context</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbose</span><span class="p">:</span>
                    <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">s</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
                <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
        <span class="n">stream</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

    <span class="k">def</span> <span class="nf">install_script</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">context</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">url</span><span class="p">):</span>
        <span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">path</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">urlparse</span><span class="p">(</span><span class="n">url</span><span class="p">)</span>
        <span class="n">fn</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">path</span><span class="p">)[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
        <span class="n">binpath</span> <span class="o">=</span> <span class="n">context</span><span class="o">.</span><span class="n">bin_path</span>
        <span class="n">distpath</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">binpath</span><span class="p">,</span> <span class="n">fn</span><span class="p">)</span>
        <span class="c1"># Download script into the virtual environment&#39;s binaries folder</span>
        <span class="n">urlretrieve</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">distpath</span><span class="p">)</span>
        <span class="n">progress</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">progress</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbose</span><span class="p">:</span>
            <span class="n">term</span> <span class="o">=</span> <span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">term</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span>
        <span class="k">if</span> <span class="n">progress</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">progress</span><span class="p">(</span><span class="s1">&#39;Installing </span><span class="si">%s</span><span class="s1"> ...</span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">term</span><span class="p">),</span> <span class="s1">&#39;main&#39;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;Installing </span><span class="si">%s</span><span class="s1"> ...</span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">term</span><span class="p">))</span>
            <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
        <span class="c1"># Install in the virtual environment</span>
        <span class="n">args</span> <span class="o">=</span> <span class="p">[</span><span class="n">context</span><span class="o">.</span><span class="n">env_exe</span><span class="p">,</span> <span class="n">fn</span><span class="p">]</span>
        <span class="n">p</span> <span class="o">=</span> <span class="n">Popen</span><span class="p">(</span><span class="n">args</span><span class="p">,</span> <span class="n">stdout</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">stderr</span><span class="o">=</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">cwd</span><span class="o">=</span><span class="n">binpath</span><span class="p">)</span>
        <span class="n">t1</span> <span class="o">=</span> <span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">reader</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stdout</span><span class="p">,</span> <span class="s1">&#39;stdout&#39;</span><span class="p">))</span>
        <span class="n">t1</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
        <span class="n">t2</span> <span class="o">=</span> <span class="n">Thread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">reader</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">stderr</span><span class="p">,</span> <span class="s1">&#39;stderr&#39;</span><span class="p">))</span>
        <span class="n">t2</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
        <span class="n">p</span><span class="o">.</span><span class="n">wait</span><span class="p">()</span>
        <span class="n">t1</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>
        <span class="n">t2</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>
        <span class="k">if</span> <span class="n">progress</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">progress</span><span class="p">(</span><span class="s1">&#39;done.&#39;</span><span class="p">,</span> <span class="s1">&#39;main&#39;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;done.</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
        <span class="c1"># Clean up - no longer needed</span>
        <span class="n">os</span><span class="o">.</span><span class="n">unlink</span><span class="p">(</span><span class="n">distpath</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">install_setuptools</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">context</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Install setuptools in the virtual environment.</span>

<span class="sd">        :param context: The information for the virtual environment</span>
<span class="sd">                        creation request being processed.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">url</span> <span class="o">=</span> <span class="s2">&quot;https://bootstrap.pypa.io/ez_setup.py&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">install_script</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="s1">&#39;setuptools&#39;</span><span class="p">,</span> <span class="n">url</span><span class="p">)</span>
        <span class="c1"># clear up the setuptools archive which gets downloaded</span>
        <span class="n">pred</span> <span class="o">=</span> <span class="k">lambda</span> <span class="n">o</span><span class="p">:</span> <span class="n">o</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;setuptools-&#39;</span><span class="p">)</span> <span class="ow">and</span> <span class="n">o</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">&#39;.tar.gz&#39;</span><span class="p">)</span>
        <span class="n">files</span> <span class="o">=</span> <span class="nb">filter</span><span class="p">(</span><span class="n">pred</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">listdir</span><span class="p">(</span><span class="n">context</span><span class="o">.</span><span class="n">bin_path</span><span class="p">))</span>
        <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">files</span><span class="p">:</span>
            <span class="n">f</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">context</span><span class="o">.</span><span class="n">bin_path</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
            <span class="n">os</span><span class="o">.</span><span class="n">unlink</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">install_pip</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">context</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Install pip in the virtual environment.</span>

<span class="sd">        :param context: The information for the virtual environment</span>
<span class="sd">                        creation request being processed.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">url</span> <span class="o">=</span> <span class="s1">&#39;https://bootstrap.pypa.io/get-pip.py&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">install_script</span><span class="p">(</span><span class="n">context</span><span class="p">,</span> <span class="s1">&#39;pip&#39;</span><span class="p">,</span> <span class="n">url</span><span class="p">)</span>


<span class="k">def</span> <span class="nf">main</span><span class="p">(</span><span class="n">args</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
    <span class="kn">import</span> <span class="nn">argparse</span>

    <span class="n">parser</span> <span class="o">=</span> <span class="n">argparse</span><span class="o">.</span><span class="n">ArgumentParser</span><span class="p">(</span><span class="n">prog</span><span class="o">=</span><span class="vm">__name__</span><span class="p">,</span>
                                     <span class="n">description</span><span class="o">=</span><span class="s1">&#39;Creates virtual Python &#39;</span>
                                                 <span class="s1">&#39;environments in one or &#39;</span>
                                                 <span class="s1">&#39;more target &#39;</span>
                                                 <span class="s1">&#39;directories.&#39;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;dirs&#39;</span><span class="p">,</span> <span class="n">metavar</span><span class="o">=</span><span class="s1">&#39;ENV_DIR&#39;</span><span class="p">,</span> <span class="n">nargs</span><span class="o">=</span><span class="s1">&#39;+&#39;</span><span class="p">,</span>
                        <span class="n">help</span><span class="o">=</span><span class="s1">&#39;A directory in which to create the &#39;</span>
                             <span class="s1">&#39;virtual environment.&#39;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--no-setuptools&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                        <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;nodist&#39;</span><span class="p">,</span>
                        <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Don&#39;t install setuptools or pip in the &quot;</span>
                             <span class="s2">&quot;virtual environment.&quot;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--no-pip&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                        <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;nopip&#39;</span><span class="p">,</span>
                        <span class="n">help</span><span class="o">=</span><span class="s2">&quot;Don&#39;t install pip in the virtual &quot;</span>
                             <span class="s2">&quot;environment.&quot;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--system-site-packages&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                        <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;system_site&#39;</span><span class="p">,</span>
                        <span class="n">help</span><span class="o">=</span><span class="s1">&#39;Give the virtual environment access to the &#39;</span>
                             <span class="s1">&#39;system site-packages dir.&#39;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s1">&#39;nt&#39;</span><span class="p">:</span>
        <span class="n">use_symlinks</span> <span class="o">=</span> <span class="kc">False</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">use_symlinks</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--symlinks&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">use_symlinks</span><span class="p">,</span>
                        <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span> <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;symlinks&#39;</span><span class="p">,</span>
                        <span class="n">help</span><span class="o">=</span><span class="s1">&#39;Try to use symlinks rather than copies, &#39;</span>
                             <span class="s1">&#39;when symlinks are not the default for &#39;</span>
                             <span class="s1">&#39;the platform.&#39;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--clear&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span>
                        <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;clear&#39;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s1">&#39;Delete the contents of the &#39;</span>
                                           <span class="s1">&#39;virtual environment &#39;</span>
                                           <span class="s1">&#39;directory if it already &#39;</span>
                                           <span class="s1">&#39;exists, before virtual &#39;</span>
                                           <span class="s1">&#39;environment creation.&#39;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--upgrade&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span>
                        <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;upgrade&#39;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s1">&#39;Upgrade the virtual &#39;</span>
                                             <span class="s1">&#39;environment directory to &#39;</span>
                                             <span class="s1">&#39;use this version of &#39;</span>
                                             <span class="s1">&#39;Python, assuming Python &#39;</span>
                                             <span class="s1">&#39;has been upgraded &#39;</span>
                                             <span class="s1">&#39;in-place.&#39;</span><span class="p">)</span>
    <span class="n">parser</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s1">&#39;--verbose&#39;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">action</span><span class="o">=</span><span class="s1">&#39;store_true&#39;</span><span class="p">,</span>
                        <span class="n">dest</span><span class="o">=</span><span class="s1">&#39;verbose&#39;</span><span class="p">,</span> <span class="n">help</span><span class="o">=</span><span class="s1">&#39;Display the output &#39;</span>
                                             <span class="s1">&#39;from the scripts which &#39;</span>
                                             <span class="s1">&#39;install setuptools and pip.&#39;</span><span class="p">)</span>
    <span class="n">options</span> <span class="o">=</span> <span class="n">parser</span><span class="o">.</span><span class="n">parse_args</span><span class="p">(</span><span class="n">args</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">options</span><span class="o">.</span><span class="n">upgrade</span> <span class="ow">and</span> <span class="n">options</span><span class="o">.</span><span class="n">clear</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;you cannot supply --upgrade and --clear together.&#39;</span><span class="p">)</span>
    <span class="n">builder</span> <span class="o">=</span> <span class="n">ExtendedEnvBuilder</span><span class="p">(</span><span class="n">system_site_packages</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">system_site</span><span class="p">,</span>
                                   <span class="n">clear</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">clear</span><span class="p">,</span>
                                   <span class="n">symlinks</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">symlinks</span><span class="p">,</span>
                                   <span class="n">upgrade</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">upgrade</span><span class="p">,</span>
                                   <span class="n">nodist</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">nodist</span><span class="p">,</span>
                                   <span class="n">nopip</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">nopip</span><span class="p">,</span>
                                   <span class="n">verbose</span><span class="o">=</span><span class="n">options</span><span class="o">.</span><span class="n">verbose</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">options</span><span class="o">.</span><span class="n">dirs</span><span class="p">:</span>
        <span class="n">builder</span><span class="o">.</span><span class="n">create</span><span class="p">(</span><span class="n">d</span><span class="p">)</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">rc</span> <span class="o">=</span> <span class="mi">1</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">main</span><span class="p">()</span>
        <span class="n">rc</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Error: </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">e</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
    <span class="n">sys</span><span class="o">.</span><span class="n">exit</span><span class="p">(</span><span class="n">rc</span><span class="p">)</span>
</pre></div>
</div>
<p>This script is also available for download <a class="reference external" href="https://gist.github.com/vsajip/4673395">online</a>.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a><ul>
<li><a class="reference internal" href="#creating-virtual-environments">Creating virtual environments</a></li>
<li><a class="reference internal" href="#how-venvs-work">How venvs work</a></li>
<li><a class="reference internal" href="#api">API</a></li>
<li><a class="reference internal" href="#an-example-of-extending-envbuilder">An example of extending <code class="docutils literal notranslate"><span class="pre">EnvBuilder</span></code></a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ensurepip.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ensurepip</span></code> — Bootstrapping the <code class="docutils literal notranslate"><span class="pre">pip</span></code> installer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="zipapp.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipapp</span></code> — Manage executable Python zip archives</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/venv.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="zipapp.html" title="zipapp — Manage executable Python zip archives"
             >next</a> |</li>
        <li class="right" >
          <a href="ensurepip.html" title="ensurepip — Bootstrapping the pip installer"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="distribution.html" >Software Packaging and Distribution</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">venv</span></code> — Creation of virtual environments</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>