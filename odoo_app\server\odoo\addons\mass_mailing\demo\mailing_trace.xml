<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mass_mail_1_stat_0" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_7"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">reply</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="open_datetime" eval="(DateTime.today() - relativedelta(days=4)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="reply_datetime" eval="(DateTime.today() - relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_1" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_13"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">reply</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="open_datetime" eval="(DateTime.today() - relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="reply_datetime" eval="(DateTime.today() - relativedelta(days=0)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=0)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_2" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_14"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">open</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="open_datetime" eval="(DateTime.today() - relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_3" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_24"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">open</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="open_datetime" eval="(DateTime.today() - relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_4" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_32"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">sent</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_5" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_33"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">error</field>
            <field name="sent_datetime" eval="False"/>
        </record>
        <record id="mass_mail_1_stat_6" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_34"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">bounce</field>
            <field name="sent_datetime" eval="(DateTime.today() - relativedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="write_date" eval="(DateTime.today() - relativedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>
        <record id="mass_mail_1_stat_7" model="mailing.trace">
            <field name="mass_mailing_id" ref="mass_mail_1"/>
            <field name="message_id"><EMAIL></field>
            <field name="model">res.partner</field>
            <field name="res_id" ref="base.res_partner_address_34"/>
            <field name="email"><EMAIL></field>
            <field name="trace_status">bounce</field>
            <field name="sent_datetime" eval="False"/>
        </record>

        <!-- Generate some clicks -->
        <function model="link.tracker.click" name="add_click">
            <value model="link.tracker.code"
                search="[('link_id.url', '=', 'http://www.example.com')]"
                use="code"/>
            <value name="ip">************</value>
            <value name="country_code">BE</value>
            <value name="mailing_trace_id" eval="ref('mass_mail_1_stat_0')"/>
        </function>
        <function model="link.tracker.click" name="add_click">
            <value model="link.tracker.code"
                search="[('link_id.url', '=', 'http://www.example.net/page/contactus')]"
                use="code"/>
            <value name="ip">************</value>
            <value name="country_code">BE</value>
            <value name="mailing_trace_id" eval="ref('mass_mail_1_stat_0')"/>
        </function>
        <function model="link.tracker.click" name="add_click">
            <value model="link.tracker.code"
                search="[('link_id.url', '=', 'http://www.example.com')]"
                use="code"/>
            <value name="ip">************</value>
            <value name="country_code">BE</value>
            <value name="mailing_trace_id" eval="ref('mass_mail_1_stat_1')"/>
        </function>
        <function model="link.tracker.click" name="add_click">
            <value model="link.tracker.code"
                search="[('link_id.url', '=', 'http://www.example.net/page/contactus')]"
                use="code"/>
            <value name="ip">************</value>
            <value name="country_code">BE</value>
            <value name="mailing_trace_id" eval="ref('mass_mail_1_stat_0')"/>
        </function>
        <function model="link.tracker.click" name="add_click">
            <value model="link.tracker.code"
                search="[('link_id.url', '=', 'http://www.example.com')]"
                use="code"/>
            <value name="ip">************</value>
            <value name="country_code">BE</value>
            <value name="mailing_trace_id" eval="ref('mass_mail_1_stat_2')"/>
        </function>

    </data>
</odoo>
