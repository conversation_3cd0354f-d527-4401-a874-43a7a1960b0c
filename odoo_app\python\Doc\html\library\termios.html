<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="termios — POSIX style tty control" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/termios.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides an interface to the POSIX calls for tty I/O control. For a complete description of these calls, see termios(3) Unix manual page. It is only available for those Unix versions th..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides an interface to the POSIX calls for tty I/O control. For a complete description of these calls, see termios(3) Unix manual page. It is only available for those Unix versions th..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>termios — POSIX style tty control &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tty — Terminal control functions" href="tty.html" />
    <link rel="prev" title="grp — The group database" href="grp.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/termios.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="grp.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">grp</span></code> — The group database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tty.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/termios.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tty.html" title="tty — Terminal control functions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="grp.html" title="grp — The group database"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" accesskey="U">Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-termios">
<span id="termios-posix-style-tty-control"></span><h1><a class="reference internal" href="#module-termios" title="termios: POSIX style tty control. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code></a> — POSIX style tty control<a class="headerlink" href="#module-termios" title="Link to this heading">¶</a></h1>
<hr class="docutils" id="index-0" />
<p>This module provides an interface to the POSIX calls for tty I/O control. For a
complete description of these calls, see <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/termios(3)">termios(3)</a></em> Unix manual
page.  It is only available for those Unix versions that support POSIX
<em>termios</em> style tty I/O control configured during installation.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<p>All functions in this module take a file descriptor <em>fd</em> as their first
argument.  This can be an integer file descriptor, such as returned by
<code class="docutils literal notranslate"><span class="pre">sys.stdin.fileno()</span></code>, or a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>, such as <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code> itself.</p>
<p>This module also defines all the constants needed to work with the functions
provided here; these have the same name as their counterparts in C.  Please
refer to your system documentation for more information on using these terminal
control interfaces.</p>
<p>The module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="termios.tcgetattr">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcgetattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcgetattr" title="Link to this definition">¶</a></dt>
<dd><p>Return a list containing the tty attributes for file descriptor <em>fd</em>, as
follows: <code class="docutils literal notranslate"><span class="pre">[iflag,</span> <span class="pre">oflag,</span> <span class="pre">cflag,</span> <span class="pre">lflag,</span> <span class="pre">ispeed,</span> <span class="pre">ospeed,</span> <span class="pre">cc]</span></code> where <em>cc</em> is a
list of the tty special characters (each a string of length 1, except the
items with indices <code class="xref py py-const docutils literal notranslate"><span class="pre">VMIN</span></code> and <code class="xref py py-const docutils literal notranslate"><span class="pre">VTIME</span></code>, which are integers when
these fields are defined).  The interpretation of the flags and the speeds as
well as the indexing in the <em>cc</em> array must be done using the symbolic
constants defined in the <a class="reference internal" href="#module-termios" title="termios: POSIX style tty control. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code></a> module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcsetattr">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcsetattr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">when</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attributes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcsetattr" title="Link to this definition">¶</a></dt>
<dd><p>Set the tty attributes for file descriptor <em>fd</em> from the <em>attributes</em>, which is
a list like the one returned by <a class="reference internal" href="#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">tcgetattr()</span></code></a>.  The <em>when</em> argument
determines when the attributes are changed:</p>
<dl class="py data">
<dt class="sig sig-object py" id="termios.TCSANOW">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">TCSANOW</span></span><a class="headerlink" href="#termios.TCSANOW" title="Link to this definition">¶</a></dt>
<dd><p>Change attributes immediately.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="termios.TCSADRAIN">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">TCSADRAIN</span></span><a class="headerlink" href="#termios.TCSADRAIN" title="Link to this definition">¶</a></dt>
<dd><p>Change attributes after transmitting all queued output.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="termios.TCSAFLUSH">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">TCSAFLUSH</span></span><a class="headerlink" href="#termios.TCSAFLUSH" title="Link to this definition">¶</a></dt>
<dd><p>Change attributes after transmitting all queued output and
discarding all queued input.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcsendbreak">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcsendbreak</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">duration</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcsendbreak" title="Link to this definition">¶</a></dt>
<dd><p>Send a break on file descriptor <em>fd</em>.  A zero <em>duration</em> sends a break for
0.25–0.5 seconds; a nonzero <em>duration</em> has a system dependent meaning.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcdrain">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcdrain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcdrain" title="Link to this definition">¶</a></dt>
<dd><p>Wait until all output written to file descriptor <em>fd</em> has been transmitted.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcflush">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcflush</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">queue</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcflush" title="Link to this definition">¶</a></dt>
<dd><p>Discard queued data on file descriptor <em>fd</em>.  The <em>queue</em> selector specifies
which queue: <code class="xref py py-const docutils literal notranslate"><span class="pre">TCIFLUSH</span></code> for the input queue, <code class="xref py py-const docutils literal notranslate"><span class="pre">TCOFLUSH</span></code> for the
output queue, or <code class="xref py py-const docutils literal notranslate"><span class="pre">TCIOFLUSH</span></code> for both queues.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcflow">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcflow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">action</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcflow" title="Link to this definition">¶</a></dt>
<dd><p>Suspend or resume input or output on file descriptor <em>fd</em>.  The <em>action</em>
argument can be <code class="xref py py-const docutils literal notranslate"><span class="pre">TCOOFF</span></code> to suspend output, <code class="xref py py-const docutils literal notranslate"><span class="pre">TCOON</span></code> to restart
output, <code class="xref py py-const docutils literal notranslate"><span class="pre">TCIOFF</span></code> to suspend input, or <code class="xref py py-const docutils literal notranslate"><span class="pre">TCION</span></code> to restart input.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcgetwinsize">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcgetwinsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcgetwinsize" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple <code class="docutils literal notranslate"><span class="pre">(ws_row,</span> <span class="pre">ws_col)</span></code> containing the tty window size for file
descriptor <em>fd</em>. Requires <code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCGWINSZ</span></code> or
<code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCGSIZE</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="termios.tcsetwinsize">
<span class="sig-prename descclassname"><span class="pre">termios.</span></span><span class="sig-name descname"><span class="pre">tcsetwinsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">winsize</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#termios.tcsetwinsize" title="Link to this definition">¶</a></dt>
<dd><p>Set the tty window size for file descriptor <em>fd</em> from <em>winsize</em>, which is
a two-item tuple <code class="docutils literal notranslate"><span class="pre">(ws_row,</span> <span class="pre">ws_col)</span></code> like the one returned by
<a class="reference internal" href="#termios.tcgetwinsize" title="termios.tcgetwinsize"><code class="xref py py-func docutils literal notranslate"><span class="pre">tcgetwinsize()</span></code></a>. Requires at least one of the pairs
(<code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCGWINSZ</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCSWINSZ</span></code>);
(<code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCGSIZE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">termios.TIOCSSIZE</span></code>) to be defined.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="tty.html#module-tty" title="tty: Utility functions that perform common terminal control operations. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code></a></dt><dd><p>Convenience functions for common terminal control operations.</p>
</dd>
</dl>
</div>
<section id="example">
<span id="termios-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>Here’s a function that prompts for a password with echoing turned off.  Note the
technique using a separate <a class="reference internal" href="#termios.tcgetattr" title="termios.tcgetattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">tcgetattr()</span></code></a> call and a <a class="reference internal" href="../reference/compound_stmts.html#try"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">try</span></code></a> …
<a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> statement to ensure that the old tty attributes are restored
exactly no matter what happens:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">getpass</span><span class="p">(</span><span class="n">prompt</span><span class="o">=</span><span class="s2">&quot;Password: &quot;</span><span class="p">):</span>
    <span class="kn">import</span> <span class="nn">termios</span><span class="o">,</span> <span class="nn">sys</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">fileno</span><span class="p">()</span>
    <span class="n">old</span> <span class="o">=</span> <span class="n">termios</span><span class="o">.</span><span class="n">tcgetattr</span><span class="p">(</span><span class="n">fd</span><span class="p">)</span>
    <span class="n">new</span> <span class="o">=</span> <span class="n">termios</span><span class="o">.</span><span class="n">tcgetattr</span><span class="p">(</span><span class="n">fd</span><span class="p">)</span>
    <span class="n">new</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">=</span> <span class="n">new</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">&amp;</span> <span class="o">~</span><span class="n">termios</span><span class="o">.</span><span class="n">ECHO</span>          <span class="c1"># lflags</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">termios</span><span class="o">.</span><span class="n">tcsetattr</span><span class="p">(</span><span class="n">fd</span><span class="p">,</span> <span class="n">termios</span><span class="o">.</span><span class="n">TCSADRAIN</span><span class="p">,</span> <span class="n">new</span><span class="p">)</span>
        <span class="n">passwd</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="n">prompt</span><span class="p">)</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="n">termios</span><span class="o">.</span><span class="n">tcsetattr</span><span class="p">(</span><span class="n">fd</span><span class="p">,</span> <span class="n">termios</span><span class="o">.</span><span class="n">TCSADRAIN</span><span class="p">,</span> <span class="n">old</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">passwd</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="grp.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">grp</span></code> — The group database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tty.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tty</span></code> — Terminal control functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/termios.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tty.html" title="tty — Terminal control functions"
             >next</a> |</li>
        <li class="right" >
          <a href="grp.html" title="grp — The group database"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" >Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">termios</span></code> — POSIX style tty control</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>