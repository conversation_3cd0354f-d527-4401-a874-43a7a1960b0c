<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="fractions — Rational numbers" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/fractions.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/fractions.py The fractions module provides support for rational number arithmetic. A Fraction instance can be constructed from a pair of integers, from another rational number, or ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/fractions.py The fractions module provides support for rational number arithmetic. A Fraction instance can be constructed from a pair of integers, from another rational number, or ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>fractions — Rational numbers &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="random — Generate pseudo-random numbers" href="random.html" />
    <link rel="prev" title="decimal — Decimal fixed point and floating point arithmetic" href="decimal.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/fractions.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="decimal.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">decimal</span></code> — Decimal fixed point and floating point arithmetic</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="random.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code> — Generate pseudo-random numbers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/fractions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="random.html" title="random — Generate pseudo-random numbers"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="decimal.html" title="decimal — Decimal fixed point and floating point arithmetic"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" accesskey="U">Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">fractions</span></code> — Rational numbers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-fractions">
<span id="fractions-rational-numbers"></span><h1><a class="reference internal" href="#module-fractions" title="fractions: Rational numbers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fractions</span></code></a> — Rational numbers<a class="headerlink" href="#module-fractions" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/fractions.py">Lib/fractions.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-fractions" title="fractions: Rational numbers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fractions</span></code></a> module provides support for rational number arithmetic.</p>
<p>A Fraction instance can be constructed from a pair of integers, from
another rational number, or from a string.</p>
<dl class="py class">
<dt class="sig sig-object py" id="fractions.Fraction">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fractions.</span></span><span class="sig-name descname"><span class="pre">Fraction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">numerator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">denominator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fractions.</span></span><span class="sig-name descname"><span class="pre">Fraction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other_fraction</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fractions.</span></span><span class="sig-name descname"><span class="pre">Fraction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">float</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fractions.</span></span><span class="sig-name descname"><span class="pre">Fraction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decimal</span></span></em><span class="sig-paren">)</span></dt>
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">fractions.</span></span><span class="sig-name descname"><span class="pre">Fraction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>The first version requires that <em>numerator</em> and <em>denominator</em> are instances
of <a class="reference internal" href="numbers.html#numbers.Rational" title="numbers.Rational"><code class="xref py py-class docutils literal notranslate"><span class="pre">numbers.Rational</span></code></a> and returns a new <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance
with value <code class="docutils literal notranslate"><span class="pre">numerator/denominator</span></code>. If <em>denominator</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code>, it
raises a <a class="reference internal" href="exceptions.html#ZeroDivisionError" title="ZeroDivisionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ZeroDivisionError</span></code></a>. The second version requires that
<em>other_fraction</em> is an instance of <a class="reference internal" href="numbers.html#numbers.Rational" title="numbers.Rational"><code class="xref py py-class docutils literal notranslate"><span class="pre">numbers.Rational</span></code></a> and returns a
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance with the same value.  The next two versions accept
either a <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> or a <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a> instance, and return a
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance with exactly the same value.  Note that due to the
usual issues with binary floating-point (see <a class="reference internal" href="../tutorial/floatingpoint.html#tut-fp-issues"><span class="std std-ref">Floating Point Arithmetic:  Issues and Limitations</span></a>), the
argument to <code class="docutils literal notranslate"><span class="pre">Fraction(1.1)</span></code> is not exactly equal to 11/10, and so
<code class="docutils literal notranslate"><span class="pre">Fraction(1.1)</span></code> does <em>not</em> return <code class="docutils literal notranslate"><span class="pre">Fraction(11,</span> <span class="pre">10)</span></code> as one might expect.
(But see the documentation for the <a class="reference internal" href="#fractions.Fraction.limit_denominator" title="fractions.Fraction.limit_denominator"><code class="xref py py-meth docutils literal notranslate"><span class="pre">limit_denominator()</span></code></a> method below.)
The last version of the constructor expects a string or unicode instance.
The usual form for this instance is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="n">sign</span><span class="p">]</span> <span class="n">numerator</span> <span class="p">[</span><span class="s1">&#39;/&#39;</span> <span class="n">denominator</span><span class="p">]</span>
</pre></div>
</div>
<p>where the optional <code class="docutils literal notranslate"><span class="pre">sign</span></code> may be either ‘+’ or ‘-’ and
<code class="docutils literal notranslate"><span class="pre">numerator</span></code> and <code class="docutils literal notranslate"><span class="pre">denominator</span></code> (if present) are strings of
decimal digits (underscores may be used to delimit digits as with
integral literals in code).  In addition, any string that represents a finite
value and is accepted by the <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> constructor is also
accepted by the <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> constructor.  In either form the
input string may also have leading and/or trailing whitespace.
Here are some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">fractions</span> <span class="kn">import</span> <span class="n">Fraction</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="mi">16</span><span class="p">,</span> <span class="o">-</span><span class="mi">10</span><span class="p">)</span>
<span class="go">Fraction(-8, 5)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="mi">123</span><span class="p">)</span>
<span class="go">Fraction(123, 1)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">()</span>
<span class="go">Fraction(0, 1)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;3/7&#39;</span><span class="p">)</span>
<span class="go">Fraction(3, 7)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39; -3/7 &#39;</span><span class="p">)</span>
<span class="go">Fraction(-3, 7)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;1.414213 </span><span class="se">\t\n</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="go">Fraction(1414213, 1000000)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;-.125&#39;</span><span class="p">)</span>
<span class="go">Fraction(-1, 8)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;7e-6&#39;</span><span class="p">)</span>
<span class="go">Fraction(7, 1000000)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="mf">2.25</span><span class="p">)</span>
<span class="go">Fraction(9, 4)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="mf">1.1</span><span class="p">)</span>
<span class="go">Fraction(2476979795053773, 2251799813685248)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">decimal</span> <span class="kn">import</span> <span class="n">Decimal</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="n">Decimal</span><span class="p">(</span><span class="s1">&#39;1.1&#39;</span><span class="p">))</span>
<span class="go">Fraction(11, 10)</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> class inherits from the abstract base class
<a class="reference internal" href="numbers.html#numbers.Rational" title="numbers.Rational"><code class="xref py py-class docutils literal notranslate"><span class="pre">numbers.Rational</span></code></a>, and implements all of the methods and
operations from that class.  <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instances are <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>,
and should be treated as immutable.  In addition,
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> has the following properties and methods:</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> constructor now accepts <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> and
<a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a> instances.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <a class="reference internal" href="math.html#math.gcd" title="math.gcd"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.gcd()</span></code></a> function is now used to normalize the <em>numerator</em>
and <em>denominator</em>. <a class="reference internal" href="math.html#math.gcd" title="math.gcd"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.gcd()</span></code></a> always return a <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> type.
Previously, the GCD type depended on <em>numerator</em> and <em>denominator</em>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Underscores are now permitted when creating a <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance
from a string, following <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0515/"><strong>PEP 515</strong></a> rules.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> implements <code class="docutils literal notranslate"><span class="pre">__int__</span></code> now to satisfy
<code class="docutils literal notranslate"><span class="pre">typing.SupportsInt</span></code> instance checks.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Space is allowed around the slash for string inputs: <code class="docutils literal notranslate"><span class="pre">Fraction('2</span> <span class="pre">/</span> <span class="pre">3')</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span><a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instances now support float-style formatting, with
presentation types <code class="docutils literal notranslate"><span class="pre">&quot;e&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;E&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;f&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;F&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;g&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;G&quot;</span></code>
and <code class="docutils literal notranslate"><span class="pre">&quot;%&quot;&quot;</span></code>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="fractions.Fraction.numerator">
<span class="sig-name descname"><span class="pre">numerator</span></span><a class="headerlink" href="#fractions.Fraction.numerator" title="Link to this definition">¶</a></dt>
<dd><p>Numerator of the Fraction in lowest term.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="fractions.Fraction.denominator">
<span class="sig-name descname"><span class="pre">denominator</span></span><a class="headerlink" href="#fractions.Fraction.denominator" title="Link to this definition">¶</a></dt>
<dd><p>Denominator of the Fraction in lowest term.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.as_integer_ratio">
<span class="sig-name descname"><span class="pre">as_integer_ratio</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.as_integer_ratio" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple of two integers, whose ratio is equal
to the original Fraction.  The ratio is in lowest terms
and has a positive denominator.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.is_integer">
<span class="sig-name descname"><span class="pre">is_integer</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.is_integer" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the Fraction is an integer.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.from_float">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_float</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.from_float" title="Link to this definition">¶</a></dt>
<dd><p>Alternative constructor which only accepts instances of
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> or <a class="reference internal" href="numbers.html#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">numbers.Integral</span></code></a>. Beware that
<code class="docutils literal notranslate"><span class="pre">Fraction.from_float(0.3)</span></code> is not the same value as <code class="docutils literal notranslate"><span class="pre">Fraction(3,</span> <span class="pre">10)</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>From Python 3.2 onwards, you can also construct a
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance directly from a <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.from_decimal">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_decimal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dec</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.from_decimal" title="Link to this definition">¶</a></dt>
<dd><p>Alternative constructor which only accepts instances of
<a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a> or <a class="reference internal" href="numbers.html#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">numbers.Integral</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>From Python 3.2 onwards, you can also construct a
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> instance directly from a <a class="reference internal" href="decimal.html#decimal.Decimal" title="decimal.Decimal"><code class="xref py py-class docutils literal notranslate"><span class="pre">decimal.Decimal</span></code></a>
instance.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.limit_denominator">
<span class="sig-name descname"><span class="pre">limit_denominator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_denominator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1000000</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.limit_denominator" title="Link to this definition">¶</a></dt>
<dd><p>Finds and returns the closest <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> to <code class="docutils literal notranslate"><span class="pre">self</span></code> that has
denominator at most max_denominator.  This method is useful for finding
rational approximations to a given floating-point number:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">fractions</span> <span class="kn">import</span> <span class="n">Fraction</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;3.1415926535897932&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">limit_denominator</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>
<span class="go">Fraction(355, 113)</span>
</pre></div>
</div>
<p>or for recovering a rational number that’s represented as a float:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">math</span> <span class="kn">import</span> <span class="n">pi</span><span class="p">,</span> <span class="n">cos</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="n">cos</span><span class="p">(</span><span class="n">pi</span><span class="o">/</span><span class="mi">3</span><span class="p">))</span>
<span class="go">Fraction(4503599627370497, 9007199254740992)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="n">cos</span><span class="p">(</span><span class="n">pi</span><span class="o">/</span><span class="mi">3</span><span class="p">))</span><span class="o">.</span><span class="n">limit_denominator</span><span class="p">()</span>
<span class="go">Fraction(1, 2)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fraction</span><span class="p">(</span><span class="mf">1.1</span><span class="p">)</span><span class="o">.</span><span class="n">limit_denominator</span><span class="p">()</span>
<span class="go">Fraction(11, 10)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.__floor__">
<span class="sig-name descname"><span class="pre">__floor__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.__floor__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the greatest <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> <code class="docutils literal notranslate"><span class="pre">&lt;=</span> <span class="pre">self</span></code>.  This method can
also be accessed through the <a class="reference internal" href="math.html#math.floor" title="math.floor"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.floor()</span></code></a> function:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">math</span> <span class="kn">import</span> <span class="n">floor</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">floor</span><span class="p">(</span><span class="n">Fraction</span><span class="p">(</span><span class="mi">355</span><span class="p">,</span> <span class="mi">113</span><span class="p">))</span>
<span class="go">3</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.__ceil__">
<span class="sig-name descname"><span class="pre">__ceil__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.__ceil__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the least <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> <code class="docutils literal notranslate"><span class="pre">&gt;=</span> <span class="pre">self</span></code>.  This method can
also be accessed through the <a class="reference internal" href="math.html#math.ceil" title="math.ceil"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.ceil()</span></code></a> function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.__round__">
<span class="sig-name descname"><span class="pre">__round__</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.__round__" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">__round__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ndigits</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>The first version returns the nearest <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> to <code class="docutils literal notranslate"><span class="pre">self</span></code>,
rounding half to even. The second version rounds <code class="docutils literal notranslate"><span class="pre">self</span></code> to the
nearest multiple of <code class="docutils literal notranslate"><span class="pre">Fraction(1,</span> <span class="pre">10**ndigits)</span></code> (logically, if
<code class="docutils literal notranslate"><span class="pre">ndigits</span></code> is negative), again rounding half toward even.  This
method can also be accessed through the <a class="reference internal" href="functions.html#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a> function.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="fractions.Fraction.__format__">
<span class="sig-name descname"><span class="pre">__format__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format_spec</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#fractions.Fraction.__format__" title="Link to this definition">¶</a></dt>
<dd><p>Provides support for float-style formatting of <a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a>
instances via the <a class="reference internal" href="stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a> method, the <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> built-in
function, or <a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">Formatted string literals</span></a>. The
presentation types <code class="docutils literal notranslate"><span class="pre">&quot;e&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;E&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;f&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;F&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;g&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;G&quot;</span></code>
and <code class="docutils literal notranslate"><span class="pre">&quot;%&quot;</span></code> are supported. For these presentation types, formatting for a
<a class="reference internal" href="#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">Fraction</span></code></a> object <code class="docutils literal notranslate"><span class="pre">x</span></code> follows the rules outlined for
the <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type in the <a class="reference internal" href="string.html#formatspec"><span class="std std-ref">Format Specification Mini-Language</span></a> section.</p>
<p>Here are some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">fractions</span> <span class="kn">import</span> <span class="n">Fraction</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="n">Fraction</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">7</span><span class="p">),</span> <span class="s1">&#39;.40g&#39;</span><span class="p">)</span>
<span class="go">&#39;0.1428571428571428571428571428571428571429&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">format</span><span class="p">(</span><span class="n">Fraction</span><span class="p">(</span><span class="s1">&#39;1234567.855&#39;</span><span class="p">),</span> <span class="s1">&#39;_.2f&#39;</span><span class="p">)</span>
<span class="go">&#39;1_234_567.86&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">Fraction</span><span class="p">(</span><span class="mi">355</span><span class="p">,</span><span class="w"> </span><span class="mi">113</span><span class="p">)</span><span class="si">:</span><span class="s2">*&gt;20.6e</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="go">&#39;********3.141593e+00&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">old_price</span><span class="p">,</span> <span class="n">new_price</span> <span class="o">=</span> <span class="mi">499</span><span class="p">,</span> <span class="mi">672</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s2">&quot;</span><span class="si">{:.2%}</span><span class="s2"> price increase&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">Fraction</span><span class="p">(</span><span class="n">new_price</span><span class="p">,</span> <span class="n">old_price</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span>
<span class="go">&#39;34.67% price increase&#39;</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="numbers.html#module-numbers" title="numbers: Numeric abstract base classes (Complex, Real, Integral, etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code></a></dt><dd><p>The abstract base classes making up the numeric tower.</p>
</dd>
</dl>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="decimal.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">decimal</span></code> — Decimal fixed point and floating point arithmetic</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="random.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code> — Generate pseudo-random numbers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/fractions.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="random.html" title="random — Generate pseudo-random numbers"
             >next</a> |</li>
        <li class="right" >
          <a href="decimal.html" title="decimal — Decimal fixed point and floating point arithmetic"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" >Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">fractions</span></code> — Rational numbers</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>