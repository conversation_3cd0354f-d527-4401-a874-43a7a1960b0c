# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* privacy_lookup
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__additional_note
msgid "Additional Note"
msgstr "Notă Adițională"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_email
msgid "Anonymized Email"
msgstr "Email Anonimizat"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_name
msgid "Anonymized Name"
msgstr "Nume anonimizat"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_archive_all
msgid "Archive Selection"
msgstr "Selecție arhivă"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
#, python-format
msgid "Archived"
msgstr "Arhivat"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Can be archived"
msgstr "Poate fi arhivat"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_res_partner
msgid "Contact"
msgstr "Contactați"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__date
msgid "Date"
msgstr "Dată"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Delete"
msgstr "Șterge"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_unlink_all
msgid "Delete Selection"
msgstr "Şterge selecţia"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Deleted"
msgstr "Șters"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model
msgid "Document Model"
msgstr "Model document"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__email
msgid "Email"
msgstr "E-mail"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__execution_details
msgid "Execution Details"
msgstr "Detalii de execu[ie"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__records_description
msgid "Found Records"
msgstr "Înregistrări găsite:"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__user_id
msgid "Handled By"
msgstr "Gestionat de"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__has_active
msgid "Has Active"
msgstr "Are activ"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__id
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "ID"
msgstr "ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_active
msgid "Is Active"
msgstr "Este Activ"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_unlinked
msgid "Is Unlinked"
msgstr "Este deconectat"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_ids
msgid "Line"
msgstr "Linie"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_count
msgid "Line Count"
msgstr "Contor linii"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__log_id
msgid "Log"
msgstr "Jurnal"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Lookup"
msgstr "Căutare"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Model"
msgstr "Model"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__name
msgid "Name"
msgstr "Nume"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Open Record"
msgstr "Deschidere înregistrare"

#. module: privacy_lookup
#: model:ir.ui.menu,name:privacy_lookup.privacy_menu
msgid "Privacy"
msgstr "Confidențialitate"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_log
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_form
msgid "Privacy Log"
msgstr "Jurnal de confidențialitate"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_action
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_form_action
#: model:ir.ui.menu,name:privacy_lookup.pricacy_log_menu
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_list
msgid "Privacy Logs"
msgstr "Jurnale de confidențialitate"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_partner
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_user
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
#, python-format
msgid "Privacy Lookup"
msgstr "Căutare confidențialitate"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard_line
msgid "Privacy Lookup Line"
msgstr "Linia de căutare a confidențialității"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard
msgid "Privacy Lookup Wizard"
msgstr "Expert căutare confidențialitate"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard_line
msgid "Privacy Lookup Wizard Line"
msgstr "Linie expert căutare confidențialitate"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__resource_ref
msgid "Record"
msgstr "Înregistrare"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__records_description
msgid "Records Description"
msgstr "Descrierea înregistrărilor"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "References"
msgstr "Referințe"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model_id
msgid "Related Document Model"
msgstr "Modelul Documentului Asociat"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_id
msgid "Resource ID"
msgstr "ID Resursa"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_name
msgid "Resource name"
msgstr "Nume resursă"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Search References"
msgstr "Caută referințe"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "The record is already unlinked."
msgstr "Înregistrarea este deja deconectată."

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/models/privacy_log.py:0
#, python-format
msgid "This email address is not valid (%s)"
msgstr "Aceasta adresa de e-mail nu este%s valida"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid ""
"This operation is irreversible. Do you wish to proceed to the record "
"deletion?"
msgstr ""
"Această operațiune este ireversibilă. Doriți să continuați cu ștergerea "
"înregistrării?"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#, python-format
msgid "Unarchived"
msgstr "Nearhivat"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__wizard_id
msgid "Wizard"
msgstr "Asistent"
