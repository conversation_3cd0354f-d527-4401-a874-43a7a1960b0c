<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="poplib — POP3 protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/poplib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/poplib.py This module defines a class, POP3, which encapsulates a connection to a POP3 server and implements the protocol as defined in RFC 1939. The POP3 class supports both the m..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/poplib.py This module defines a class, POP3, which encapsulates a connection to a POP3 server and implements the protocol as defined in RFC 1939. The POP3 class supports both the m..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>poplib — POP3 protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="imaplib — IMAP4 protocol client" href="imaplib.html" />
    <link rel="prev" title="ftplib — FTP protocol client" href="ftplib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/poplib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a><ul>
<li><a class="reference internal" href="#pop3-objects">POP3 Objects</a></li>
<li><a class="reference internal" href="#pop3-example">POP3 Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ftplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="imaplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/poplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="imaplib.html" title="imaplib — IMAP4 protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="ftplib.html" title="ftplib — FTP protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-poplib">
<span id="poplib-pop3-protocol-client"></span><h1><a class="reference internal" href="#module-poplib" title="poplib: POP3 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code></a> — POP3 protocol client<a class="headerlink" href="#module-poplib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/poplib.py">Lib/poplib.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module defines a class, <a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a>, which encapsulates a connection to a
POP3 server and implements the protocol as defined in <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1939.html"><strong>RFC 1939</strong></a>. The
<a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a> class supports both the minimal and optional command sets from
<span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1939.html"><strong>RFC 1939</strong></a>. The <a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a> class also supports the <code class="docutils literal notranslate"><span class="pre">STLS</span></code> command introduced
in <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2595.html"><strong>RFC 2595</strong></a> to enable encrypted communication on an already established connection.</p>
<p>Additionally, this module provides a class <a class="reference internal" href="#poplib.POP3_SSL" title="poplib.POP3_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3_SSL</span></code></a>, which provides
support for connecting to POP3 servers that use SSL as an underlying protocol
layer.</p>
<p>Note that POP3, though widely supported, is obsolescent.  The implementation
quality of POP3 servers varies widely, and too many are quite poor. If your
mailserver supports IMAP, you would be better off using the
<a class="reference internal" href="imaplib.html#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">imaplib.IMAP4</span></code></a> class, as IMAP servers tend to be better implemented.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>The <a class="reference internal" href="#module-poplib" title="poplib: POP3 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code></a> module provides two classes:</p>
<dl class="py class">
<dt class="sig sig-object py" id="poplib.POP3">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">poplib.</span></span><span class="sig-name descname"><span class="pre">POP3</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port=POP3_PORT</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3" title="Link to this definition">¶</a></dt>
<dd><p>This class implements the actual POP3 protocol.  The connection is created when
the instance is initialized. If <em>port</em> is omitted, the standard POP3 port (110)
is used. The optional <em>timeout</em> parameter specifies a timeout in seconds for the
connection attempt (if not specified, the global default timeout setting will
be used).</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">poplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<p class="audit-hook"><p>All commands will raise an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">poplib.putline</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code> and <code class="docutils literal notranslate"><span class="pre">line</span></code>,
where <code class="docutils literal notranslate"><span class="pre">line</span></code> is the bytes about to be sent to the remote host.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="poplib.POP3_SSL">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">poplib.</span></span><span class="sig-name descname"><span class="pre">POP3_SSL</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">POP3_SSL_PORT</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3_SSL" title="Link to this definition">¶</a></dt>
<dd><p>This is a subclass of <a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a> that connects to the server over an SSL
encrypted socket.  If <em>port</em> is not specified, 995, the standard POP3-over-SSL
port is used.  <em>timeout</em> works as in the <a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a> constructor.
<em>context</em> is an optional <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object which allows
bundling SSL configuration options, certificates and private keys into a
single (potentially long-lived) structure.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a>
for best practices.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">poplib.connect</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<p class="audit-hook"><p>All commands will raise an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">poplib.putline</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code> and <code class="docutils literal notranslate"><span class="pre">line</span></code>,
where <code class="docutils literal notranslate"><span class="pre">line</span></code> is the bytes about to be sent to the remote host.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>context</em> parameter added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The class now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If the <em>timeout</em> parameter is set to be zero, it will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to prevent the creation of a non-blocking socket.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>keyfile</em> and <em>certfile</em> parameters have been removed.</p>
</div>
</dd></dl>

<p>One exception is defined as an attribute of the <a class="reference internal" href="#module-poplib" title="poplib: POP3 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code></a> module:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="poplib.error_proto">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">poplib.</span></span><span class="sig-name descname"><span class="pre">error_proto</span></span><a class="headerlink" href="#poplib.error_proto" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised on any errors from this module (errors from <a class="reference internal" href="socket.html#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a>
module are not caught). The reason for the exception is passed to the
constructor as a string.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="imaplib.html#module-imaplib" title="imaplib: IMAP4 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code></a></dt><dd><p>The standard Python IMAP module.</p>
</dd>
<dt><a class="reference external" href="http://www.catb.org/~esr/fetchmail/fetchmail-FAQ.html">Frequently Asked Questions About Fetchmail</a></dt><dd><p>The FAQ for the <strong class="program">fetchmail</strong> POP/IMAP client collects information on
POP3 server variations and RFC noncompliance that may be useful if you need to
write an application based on the POP protocol.</p>
</dd>
</dl>
</div>
<section id="pop3-objects">
<span id="id1"></span><h2>POP3 Objects<a class="headerlink" href="#pop3-objects" title="Link to this heading">¶</a></h2>
<p>All POP3 commands are represented by methods of the same name, in lowercase;
most return the response text sent by the server.</p>
<p>A <a class="reference internal" href="#poplib.POP3" title="poplib.POP3"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3</span></code></a> instance has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.set_debuglevel">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">set_debuglevel</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.set_debuglevel" title="Link to this definition">¶</a></dt>
<dd><p>Set the instance’s debugging level.  This controls the amount of debugging
output printed.  The default, <code class="docutils literal notranslate"><span class="pre">0</span></code>, produces no debugging output.  A value of
<code class="docutils literal notranslate"><span class="pre">1</span></code> produces a moderate amount of debugging output, generally a single line
per request.  A value of <code class="docutils literal notranslate"><span class="pre">2</span></code> or higher produces the maximum amount of
debugging output, logging each line sent and received on the control connection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.getwelcome">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">getwelcome</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.getwelcome" title="Link to this definition">¶</a></dt>
<dd><p>Returns the greeting string sent by the POP3 server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.capa">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">capa</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.capa" title="Link to this definition">¶</a></dt>
<dd><p>Query the server’s capabilities as specified in <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2449.html"><strong>RFC 2449</strong></a>.
Returns a dictionary in the form <code class="docutils literal notranslate"><span class="pre">{'name':</span> <span class="pre">['param'...]}</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.user">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">username</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.user" title="Link to this definition">¶</a></dt>
<dd><p>Send user command, response should indicate that a password is required.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.pass_">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">pass_</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.pass_" title="Link to this definition">¶</a></dt>
<dd><p>Send password, response includes message count and mailbox size. Note: the
mailbox on the server is locked until <a class="reference internal" href="#poplib.POP3.quit" title="poplib.POP3.quit"><code class="xref py py-meth docutils literal notranslate"><span class="pre">quit()</span></code></a> is called.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.apop">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">apop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">secret</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.apop" title="Link to this definition">¶</a></dt>
<dd><p>Use the more secure APOP authentication to log into the POP3 server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.rpop">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">rpop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.rpop" title="Link to this definition">¶</a></dt>
<dd><p>Use RPOP authentication (similar to UNIX r-commands) to log into POP3 server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.stat">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">stat</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.stat" title="Link to this definition">¶</a></dt>
<dd><p>Get mailbox status.  The result is a tuple of 2 integers: <code class="docutils literal notranslate"><span class="pre">(message</span> <span class="pre">count,</span>
<span class="pre">mailbox</span> <span class="pre">size)</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.list">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">list</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">which</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.list" title="Link to this definition">¶</a></dt>
<dd><p>Request message list, result is in the form <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">['mesg_num</span> <span class="pre">octets',</span>
<span class="pre">...],</span> <span class="pre">octets)</span></code>. If <em>which</em> is set, it is the message to list.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.retr">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">retr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">which</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.retr" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve whole message number <em>which</em>, and set its seen flag. Result is in form
<code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">['line',</span> <span class="pre">...],</span> <span class="pre">octets)</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.dele">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">dele</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">which</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.dele" title="Link to this definition">¶</a></dt>
<dd><p>Flag message number <em>which</em> for deletion.  On most servers deletions are not
actually performed until QUIT (the major exception is Eudora QPOP, which
deliberately violates the RFCs by doing pending deletes on any disconnect).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.rset">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">rset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.rset" title="Link to this definition">¶</a></dt>
<dd><p>Remove any deletion marks for the mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.noop">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">noop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.noop" title="Link to this definition">¶</a></dt>
<dd><p>Do nothing.  Might be used as a keep-alive.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.quit">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.quit" title="Link to this definition">¶</a></dt>
<dd><p>Signoff:  commit changes, unlock mailbox, drop connection.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.top">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">top</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">which</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">howmuch</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.top" title="Link to this definition">¶</a></dt>
<dd><p>Retrieves the message header plus <em>howmuch</em> lines of the message after the
header of message number <em>which</em>. Result is in form <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">['line',</span> <span class="pre">...],</span>
<span class="pre">octets)</span></code>.</p>
<p>The POP3 TOP command this method uses, unlike the RETR command, doesn’t set the
message’s seen flag; unfortunately, TOP is poorly specified in the RFCs and is
frequently broken in off-brand servers. Test this method by hand against the
POP3 servers you will use before trusting it.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.uidl">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">uidl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">which</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.uidl" title="Link to this definition">¶</a></dt>
<dd><p>Return message digest (unique id) list. If <em>which</em> is specified, result contains
the unique id for that message in the form <code class="docutils literal notranslate"><span class="pre">'response</span> <span class="pre">mesgnum</span> <span class="pre">uid</span></code>, otherwise
result is list <code class="docutils literal notranslate"><span class="pre">(response,</span> <span class="pre">['mesgnum</span> <span class="pre">uid',</span> <span class="pre">...],</span> <span class="pre">octets)</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.utf8">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">utf8</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.utf8" title="Link to this definition">¶</a></dt>
<dd><p>Try to switch to UTF-8 mode. Returns the server response if successful,
raises <a class="reference internal" href="#poplib.error_proto" title="poplib.error_proto"><code class="xref py py-class docutils literal notranslate"><span class="pre">error_proto</span></code></a> if not. Specified in <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6856.html"><strong>RFC 6856</strong></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="poplib.POP3.stls">
<span class="sig-prename descclassname"><span class="pre">POP3.</span></span><span class="sig-name descname"><span class="pre">stls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#poplib.POP3.stls" title="Link to this definition">¶</a></dt>
<dd><p>Start a TLS session on the active connection as specified in <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2595.html"><strong>RFC 2595</strong></a>.
This is only allowed before user authentication</p>
<p><em>context</em> parameter is a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object which allows
bundling SSL configuration options, certificates and private keys into
a single (potentially long-lived) structure.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a>
for best practices.</p>
<p>This method supports hostname checking via
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p>Instances of <a class="reference internal" href="#poplib.POP3_SSL" title="poplib.POP3_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">POP3_SSL</span></code></a> have no additional methods. The interface of this
subclass is identical to its parent.</p>
</section>
<section id="pop3-example">
<span id="id2"></span><h2>POP3 Example<a class="headerlink" href="#pop3-example" title="Link to this heading">¶</a></h2>
<p>Here is a minimal example (without error checking) that opens a mailbox and
retrieves and prints all messages:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">getpass</span><span class="o">,</span> <span class="nn">poplib</span>

<span class="n">M</span> <span class="o">=</span> <span class="n">poplib</span><span class="o">.</span><span class="n">POP3</span><span class="p">(</span><span class="s1">&#39;localhost&#39;</span><span class="p">)</span>
<span class="n">M</span><span class="o">.</span><span class="n">user</span><span class="p">(</span><span class="n">getpass</span><span class="o">.</span><span class="n">getuser</span><span class="p">())</span>
<span class="n">M</span><span class="o">.</span><span class="n">pass_</span><span class="p">(</span><span class="n">getpass</span><span class="o">.</span><span class="n">getpass</span><span class="p">())</span>
<span class="n">numMessages</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">M</span><span class="o">.</span><span class="n">list</span><span class="p">()[</span><span class="mi">1</span><span class="p">])</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">numMessages</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="n">M</span><span class="o">.</span><span class="n">retr</span><span class="p">(</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="p">)[</span><span class="mi">1</span><span class="p">]:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">j</span><span class="p">)</span>
</pre></div>
</div>
<p>At the end of the module, there is a test section that contains a more extensive
example of usage.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a><ul>
<li><a class="reference internal" href="#pop3-objects">POP3 Objects</a></li>
<li><a class="reference internal" href="#pop3-example">POP3 Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ftplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code> — FTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="imaplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/poplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="imaplib.html" title="imaplib — IMAP4 protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="ftplib.html" title="ftplib — FTP protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>