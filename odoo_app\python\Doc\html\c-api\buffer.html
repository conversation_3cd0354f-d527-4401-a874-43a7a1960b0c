<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Buffer Protocol" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/buffer.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Certain objects available in Python wrap access to an underlying memory array or buffer. Such objects include the built-in bytes and bytearray, and some extension types like array.array. Third-part..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Certain objects available in Python wrap access to an underlying memory array or buffer. Such objects include the built-in bytes and bytearray, and some extension types like array.array. Third-part..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Buffer Protocol &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Old Buffer Protocol" href="objbuffer.html" />
    <link rel="prev" title="Iterator Protocol" href="iter.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/buffer.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Buffer Protocol</a><ul>
<li><a class="reference internal" href="#buffer-structure">Buffer structure</a></li>
<li><a class="reference internal" href="#buffer-request-types">Buffer request types</a><ul>
<li><a class="reference internal" href="#request-independent-fields">request-independent fields</a></li>
<li><a class="reference internal" href="#readonly-format">readonly, format</a></li>
<li><a class="reference internal" href="#shape-strides-suboffsets">shape, strides, suboffsets</a></li>
<li><a class="reference internal" href="#contiguity-requests">contiguity requests</a></li>
<li><a class="reference internal" href="#compound-requests">compound requests</a></li>
</ul>
</li>
<li><a class="reference internal" href="#complex-arrays">Complex arrays</a><ul>
<li><a class="reference internal" href="#numpy-style-shape-and-strides">NumPy-style: shape and strides</a></li>
<li><a class="reference internal" href="#pil-style-shape-strides-and-suboffsets">PIL-style: shape, strides and suboffsets</a></li>
</ul>
</li>
<li><a class="reference internal" href="#buffer-related-functions">Buffer-related functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="iter.html"
                          title="previous chapter">Iterator Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="objbuffer.html"
                          title="next chapter">Old Buffer Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/buffer.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="objbuffer.html" title="Old Buffer Protocol"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="iter.html" title="Iterator Protocol"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="abstract.html" accesskey="U">Abstract Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Buffer Protocol</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="buffer-protocol">
<span id="bufferobjects"></span><span id="index-0"></span><h1>Buffer Protocol<a class="headerlink" href="#buffer-protocol" title="Link to this heading">¶</a></h1>
<p>Certain objects available in Python wrap access to an underlying memory
array or <em>buffer</em>.  Such objects include the built-in <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and
<a class="reference internal" href="../library/stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a>, and some extension types like <a class="reference internal" href="../library/array.html#array.array" title="array.array"><code class="xref py py-class docutils literal notranslate"><span class="pre">array.array</span></code></a>.
Third-party libraries may define their own types for special purposes, such
as image processing or numeric analysis.</p>
<p>While each of these types have their own semantics, they share the common
characteristic of being backed by a possibly large memory buffer.  It is
then desirable, in some situations, to access that buffer directly and
without intermediate copying.</p>
<p>Python provides such a facility at the C level in the form of the <a class="reference internal" href="#bufferobjects"><span class="std std-ref">buffer
protocol</span></a>.  This protocol has two sides:</p>
<ul class="simple" id="index-1">
<li><p>on the producer side, a type can export a “buffer interface” which allows
objects of that type to expose information about their underlying buffer.
This interface is described in the section <a class="reference internal" href="typeobj.html#buffer-structs"><span class="std std-ref">Buffer Object Structures</span></a>;</p></li>
<li><p>on the consumer side, several means are available to obtain a pointer to
the raw underlying data of an object (for example a method parameter).</p></li>
</ul>
<p>Simple objects such as <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> and <a class="reference internal" href="../library/stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> expose their
underlying buffer in byte-oriented form.  Other forms are possible; for example,
the elements exposed by an <a class="reference internal" href="../library/array.html#array.array" title="array.array"><code class="xref py py-class docutils literal notranslate"><span class="pre">array.array</span></code></a> can be multi-byte values.</p>
<p>An example consumer of the buffer interface is the <a class="reference internal" href="../library/io.html#io.BufferedIOBase.write" title="io.BufferedIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a>
method of file objects: any object that can export a series of bytes through
the buffer interface can be written to a file.  While <code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code> only
needs read-only access to the internal contents of the object passed to it,
other methods such as <a class="reference internal" href="../library/io.html#io.BufferedIOBase.readinto" title="io.BufferedIOBase.readinto"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readinto()</span></code></a> need write access
to the contents of their argument.  The buffer interface allows objects to
selectively allow or reject exporting of read-write and read-only buffers.</p>
<p>There are two ways for a consumer of the buffer interface to acquire a buffer
over a target object:</p>
<ul class="simple">
<li><p>call <a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a> with the right parameters;</p></li>
<li><p>call <a class="reference internal" href="arg.html#c.PyArg_ParseTuple" title="PyArg_ParseTuple"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyArg_ParseTuple()</span></code></a> (or one of its siblings) with one of the
<code class="docutils literal notranslate"><span class="pre">y*</span></code>, <code class="docutils literal notranslate"><span class="pre">w*</span></code> or <code class="docutils literal notranslate"><span class="pre">s*</span></code> <a class="reference internal" href="arg.html#arg-parsing"><span class="std std-ref">format codes</span></a>.</p></li>
</ul>
<p>In both cases, <a class="reference internal" href="#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a> must be called when the buffer
isn’t needed anymore.  Failure to do so could lead to various issues such as
resource leaks.</p>
<section id="buffer-structure">
<span id="id1"></span><h2>Buffer structure<a class="headerlink" href="#buffer-structure" title="Link to this heading">¶</a></h2>
<p>Buffer structures (or simply “buffers”) are useful as a way to expose the
binary data from another object to the Python programmer.  They can also be
used as a zero-copy slicing mechanism.  Using their ability to reference a
block of memory, it is possible to expose any data to the Python programmer
quite easily.  The memory could be a large, constant array in a C extension,
it could be a raw block of memory for manipulation before passing to an
operating system library, or it could be used to pass around structured data
in its native, in-memory format.</p>
<p>Contrary to most data types exposed by the Python interpreter, buffers
are not <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> pointers but rather simple C structures.  This
allows them to be created and copied very simply.  When a generic wrapper
around a buffer is needed, a <a class="reference internal" href="memoryview.html#memoryview-objects"><span class="std std-ref">memoryview</span></a> object
can be created.</p>
<p>For short instructions how to write an exporting object, see
<a class="reference internal" href="typeobj.html#buffer-structs"><span class="std std-ref">Buffer Object Structures</span></a>. For obtaining
a buffer, see <a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a>.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.Py_buffer">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_buffer</span></span></span><a class="headerlink" href="#c.Py_buffer" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> (including all members) since version 3.11.</em><dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.buf">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">buf</span></span></span><a class="headerlink" href="#c.Py_buffer.buf" title="Link to this definition">¶</a><br /></dt>
<dd><p>A pointer to the start of the logical structure described by the buffer
fields. This can be any location within the underlying physical memory
block of the exporter. For example, with negative <a class="reference internal" href="#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a>
the value may point to the end of the memory block.</p>
<p>For <a class="reference internal" href="../glossary.html#term-contiguous"><span class="xref std std-term">contiguous</span></a> arrays, the value points to the beginning of
the memory block.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.obj">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">obj</span></span></span><a class="headerlink" href="#c.Py_buffer.obj" title="Link to this definition">¶</a><br /></dt>
<dd><p>A new reference to the exporting object. The reference is owned by
the consumer and automatically released
(i.e. reference count decremented)
and set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> by
<a class="reference internal" href="#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a>. The field is the equivalent of the return
value of any standard C-API function.</p>
<p>As a special case, for <em>temporary</em> buffers that are wrapped by
<a class="reference internal" href="memoryview.html#c.PyMemoryView_FromBuffer" title="PyMemoryView_FromBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMemoryView_FromBuffer()</span></code></a> or <a class="reference internal" href="#c.PyBuffer_FillInfo" title="PyBuffer_FillInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_FillInfo()</span></code></a>
this field is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>. In general, exporting objects MUST NOT
use this scheme.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.len">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">len</span></span></span><a class="headerlink" href="#c.Py_buffer.len" title="Link to this definition">¶</a><br /></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">product(shape)</span> <span class="pre">*</span> <span class="pre">itemsize</span></code>. For contiguous arrays, this is the length
of the underlying memory block. For non-contiguous arrays, it is the length
that the logical structure would have if it were copied to a contiguous
representation.</p>
<p>Accessing <code class="docutils literal notranslate"><span class="pre">((char</span> <span class="pre">*)buf)[0]</span> <span class="pre">up</span> <span class="pre">to</span> <span class="pre">((char</span> <span class="pre">*)buf)[len-1]</span></code> is only valid
if the buffer has been obtained by a request that guarantees contiguity. In
most cases such a request will be <a class="reference internal" href="#c.PyBUF_SIMPLE" title="PyBUF_SIMPLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_SIMPLE</span></code></a> or <a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.readonly">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">readonly</span></span></span><a class="headerlink" href="#c.Py_buffer.readonly" title="Link to this definition">¶</a><br /></dt>
<dd><p>An indicator of whether the buffer is read-only. This field is controlled
by the <a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a> flag.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.itemsize">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">itemsize</span></span></span><a class="headerlink" href="#c.Py_buffer.itemsize" title="Link to this definition">¶</a><br /></dt>
<dd><p>Item size in bytes of a single element. Same as the value of <a class="reference internal" href="../library/struct.html#struct.calcsize" title="struct.calcsize"><code class="xref py py-func docutils literal notranslate"><span class="pre">struct.calcsize()</span></code></a>
called on non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> <a class="reference internal" href="#c.Py_buffer.format" title="Py_buffer.format"><code class="xref c c-member docutils literal notranslate"><span class="pre">format</span></code></a> values.</p>
<p>Important exception: If a consumer requests a buffer without the
<a class="reference internal" href="#c.PyBUF_FORMAT" title="PyBUF_FORMAT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_FORMAT</span></code></a> flag, <a class="reference internal" href="#c.Py_buffer.format" title="Py_buffer.format"><code class="xref c c-member docutils literal notranslate"><span class="pre">format</span></code></a> will
be set to  <code class="docutils literal notranslate"><span class="pre">NULL</span></code>,  but <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a> still has
the value for the original format.</p>
<p>If <a class="reference internal" href="#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a> is present, the equality
<code class="docutils literal notranslate"><span class="pre">product(shape)</span> <span class="pre">*</span> <span class="pre">itemsize</span> <span class="pre">==</span> <span class="pre">len</span></code> still holds and the consumer
can use <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a> to navigate the buffer.</p>
<p>If <a class="reference internal" href="#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> as a result of a <a class="reference internal" href="#c.PyBUF_SIMPLE" title="PyBUF_SIMPLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_SIMPLE</span></code></a>
or a <a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a> request, the consumer must disregard
<a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a> and assume <code class="docutils literal notranslate"><span class="pre">itemsize</span> <span class="pre">==</span> <span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.format">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">format</span></span></span><a class="headerlink" href="#c.Py_buffer.format" title="Link to this definition">¶</a><br /></dt>
<dd><p>A <em>NUL</em> terminated string in <a class="reference internal" href="../library/struct.html#module-struct" title="struct: Interpret bytes as packed binary data."><code class="xref py py-mod docutils literal notranslate"><span class="pre">struct</span></code></a> module style syntax describing
the contents of a single item. If this is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;B&quot;</span></code> (unsigned bytes)
is assumed.</p>
<p>This field is controlled by the <a class="reference internal" href="#c.PyBUF_FORMAT" title="PyBUF_FORMAT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_FORMAT</span></code></a> flag.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.ndim">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">ndim</span></span></span><a class="headerlink" href="#c.Py_buffer.ndim" title="Link to this definition">¶</a><br /></dt>
<dd><p>The number of dimensions the memory represents as an n-dimensional array.
If it is <code class="docutils literal notranslate"><span class="pre">0</span></code>, <a class="reference internal" href="#c.Py_buffer.buf" title="Py_buffer.buf"><code class="xref c c-member docutils literal notranslate"><span class="pre">buf</span></code></a> points to a single item representing
a scalar. In this case, <a class="reference internal" href="#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a>, <a class="reference internal" href="#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a>
and <a class="reference internal" href="#c.Py_buffer.suboffsets" title="Py_buffer.suboffsets"><code class="xref c c-member docutils literal notranslate"><span class="pre">suboffsets</span></code></a> MUST be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.
The maximum number of dimensions is given by <a class="reference internal" href="#c.PyBUF_MAX_NDIM" title="PyBUF_MAX_NDIM"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_MAX_NDIM</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.shape">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">shape</span></span></span><a class="headerlink" href="#c.Py_buffer.shape" title="Link to this definition">¶</a><br /></dt>
<dd><p>An array of <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a> of length <a class="reference internal" href="#c.Py_buffer.ndim" title="Py_buffer.ndim"><code class="xref c c-member docutils literal notranslate"><span class="pre">ndim</span></code></a>
indicating the shape of the memory as an n-dimensional array. Note that
<code class="docutils literal notranslate"><span class="pre">shape[0]</span> <span class="pre">*</span> <span class="pre">...</span> <span class="pre">*</span> <span class="pre">shape[ndim-1]</span> <span class="pre">*</span> <span class="pre">itemsize</span></code> MUST be equal to
<a class="reference internal" href="#c.Py_buffer.len" title="Py_buffer.len"><code class="xref c c-member docutils literal notranslate"><span class="pre">len</span></code></a>.</p>
<p>Shape values are restricted to <code class="docutils literal notranslate"><span class="pre">shape[n]</span> <span class="pre">&gt;=</span> <span class="pre">0</span></code>. The case
<code class="docutils literal notranslate"><span class="pre">shape[n]</span> <span class="pre">==</span> <span class="pre">0</span></code> requires special attention. See <a class="reference internal" href="#complex-arrays">complex arrays</a>
for further information.</p>
<p>The shape array is read-only for the consumer.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.strides">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">strides</span></span></span><a class="headerlink" href="#c.Py_buffer.strides" title="Link to this definition">¶</a><br /></dt>
<dd><p>An array of <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a> of length <a class="reference internal" href="#c.Py_buffer.ndim" title="Py_buffer.ndim"><code class="xref c c-member docutils literal notranslate"><span class="pre">ndim</span></code></a>
giving the number of bytes to skip to get to a new element in each
dimension.</p>
<p>Stride values can be any integer. For regular arrays, strides are
usually positive, but a consumer MUST be able to handle the case
<code class="docutils literal notranslate"><span class="pre">strides[n]</span> <span class="pre">&lt;=</span> <span class="pre">0</span></code>. See <a class="reference internal" href="#complex-arrays">complex arrays</a> for further information.</p>
<p>The strides array is read-only for the consumer.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.suboffsets">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">suboffsets</span></span></span><a class="headerlink" href="#c.Py_buffer.suboffsets" title="Link to this definition">¶</a><br /></dt>
<dd><p>An array of <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_ssize_t</span></code></a> of length <a class="reference internal" href="#c.Py_buffer.ndim" title="Py_buffer.ndim"><code class="xref c c-member docutils literal notranslate"><span class="pre">ndim</span></code></a>.
If <code class="docutils literal notranslate"><span class="pre">suboffsets[n]</span> <span class="pre">&gt;=</span> <span class="pre">0</span></code>, the values stored along the nth dimension are
pointers and the suboffset value dictates how many bytes to add to each
pointer after de-referencing. A suboffset value that is negative
indicates that no de-referencing should occur (striding in a contiguous
memory block).</p>
<p>If all suboffsets are negative (i.e. no de-referencing is needed), then
this field must be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> (the default value).</p>
<p>This type of array representation is used by the Python Imaging Library
(PIL). See <a class="reference internal" href="#complex-arrays">complex arrays</a> for further information how to access elements
of such an array.</p>
<p>The suboffsets array is read-only for the consumer.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.Py_buffer.internal">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">internal</span></span></span><a class="headerlink" href="#c.Py_buffer.internal" title="Link to this definition">¶</a><br /></dt>
<dd><p>This is for use internally by the exporting object. For example, this
might be re-cast as an integer by the exporter and used to store flags
about whether or not the shape, strides, and suboffsets arrays must be
freed when the buffer is released. The consumer MUST NOT alter this
value.</p>
</dd></dl>

</dd></dl>

<p>Constants:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_MAX_NDIM">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_MAX_NDIM</span></span></span><a class="headerlink" href="#c.PyBUF_MAX_NDIM" title="Link to this definition">¶</a><br /></dt>
<dd><p>The maximum number of dimensions the memory represents.
Exporters MUST respect this limit, consumers of multi-dimensional
buffers SHOULD be able to handle up to <code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_MAX_NDIM</span></code> dimensions.
Currently set to 64.</p>
</dd></dl>

</section>
<section id="buffer-request-types">
<span id="id2"></span><h2>Buffer request types<a class="headerlink" href="#buffer-request-types" title="Link to this heading">¶</a></h2>
<p>Buffers are usually obtained by sending a buffer request to an exporting
object via <a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a>. Since the complexity of the logical
structure of the memory can vary drastically, the consumer uses the <em>flags</em>
argument to specify the exact buffer type it can handle.</p>
<p>All <a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_buffer</span></code></a> fields are unambiguously defined by the request
type.</p>
<section id="request-independent-fields">
<h3>request-independent fields<a class="headerlink" href="#request-independent-fields" title="Link to this heading">¶</a></h3>
<p>The following fields are not influenced by <em>flags</em> and must always be filled in
with the correct values: <a class="reference internal" href="#c.Py_buffer.obj" title="Py_buffer.obj"><code class="xref c c-member docutils literal notranslate"><span class="pre">obj</span></code></a>, <a class="reference internal" href="#c.Py_buffer.buf" title="Py_buffer.buf"><code class="xref c c-member docutils literal notranslate"><span class="pre">buf</span></code></a>,
<a class="reference internal" href="#c.Py_buffer.len" title="Py_buffer.len"><code class="xref c c-member docutils literal notranslate"><span class="pre">len</span></code></a>, <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a>, <a class="reference internal" href="#c.Py_buffer.ndim" title="Py_buffer.ndim"><code class="xref c c-member docutils literal notranslate"><span class="pre">ndim</span></code></a>.</p>
</section>
<section id="readonly-format">
<h3>readonly, format<a class="headerlink" href="#readonly-format" title="Link to this heading">¶</a></h3>
<blockquote>
<div><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_WRITABLE">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_WRITABLE</span></span></span><a class="headerlink" href="#c.PyBUF_WRITABLE" title="Link to this definition">¶</a><br /></dt>
<dd><p>Controls the <a class="reference internal" href="#c.Py_buffer.readonly" title="Py_buffer.readonly"><code class="xref c c-member docutils literal notranslate"><span class="pre">readonly</span></code></a> field. If set, the exporter
MUST provide a writable buffer or else report failure. Otherwise, the
exporter MAY provide either a read-only or writable buffer, but the choice
MUST be consistent for all consumers.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_FORMAT">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_FORMAT</span></span></span><a class="headerlink" href="#c.PyBUF_FORMAT" title="Link to this definition">¶</a><br /></dt>
<dd><p>Controls the <a class="reference internal" href="#c.Py_buffer.format" title="Py_buffer.format"><code class="xref c c-member docutils literal notranslate"><span class="pre">format</span></code></a> field. If set, this field MUST
be filled in correctly. Otherwise, this field MUST be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

</div></blockquote>
<p><a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a> can be |’d to any of the flags in the next section.
Since <a class="reference internal" href="#c.PyBUF_SIMPLE" title="PyBUF_SIMPLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_SIMPLE</span></code></a> is defined as 0, <a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a>
can be used as a stand-alone flag to request a simple writable buffer.</p>
<p><a class="reference internal" href="#c.PyBUF_FORMAT" title="PyBUF_FORMAT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_FORMAT</span></code></a> can be |’d to any of the flags except <a class="reference internal" href="#c.PyBUF_SIMPLE" title="PyBUF_SIMPLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_SIMPLE</span></code></a>.
The latter already implies format <code class="docutils literal notranslate"><span class="pre">B</span></code> (unsigned bytes).</p>
</section>
<section id="shape-strides-suboffsets">
<h3>shape, strides, suboffsets<a class="headerlink" href="#shape-strides-suboffsets" title="Link to this heading">¶</a></h3>
<p>The flags that control the logical structure of the memory are listed
in decreasing order of complexity. Note that each flag contains all bits
of the flags below it.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Request</p></th>
<th class="head"><p>shape</p></th>
<th class="head"><p>strides</p></th>
<th class="head"><p>suboffsets</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_INDIRECT">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_INDIRECT</span></span></span><a class="headerlink" href="#c.PyBUF_INDIRECT" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>if needed</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_STRIDES">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_STRIDES</span></span></span><a class="headerlink" href="#c.PyBUF_STRIDES" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_ND">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_ND</span></span></span><a class="headerlink" href="#c.PyBUF_ND" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_SIMPLE">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_SIMPLE</span></span></span><a class="headerlink" href="#c.PyBUF_SIMPLE" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
</tr>
</tbody>
</table>
</section>
<section id="contiguity-requests">
<span id="index-2"></span><h3>contiguity requests<a class="headerlink" href="#contiguity-requests" title="Link to this heading">¶</a></h3>
<p>C or Fortran <a class="reference internal" href="../glossary.html#term-contiguous"><span class="xref std std-term">contiguity</span></a> can be explicitly requested,
with and without stride information. Without stride information, the buffer
must be C-contiguous.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Request</p></th>
<th class="head"><p>shape</p></th>
<th class="head"><p>strides</p></th>
<th class="head"><p>suboffsets</p></th>
<th class="head"><p>contig</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_C_CONTIGUOUS">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_C_CONTIGUOUS</span></span></span><a class="headerlink" href="#c.PyBUF_C_CONTIGUOUS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>C</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_F_CONTIGUOUS">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_F_CONTIGUOUS</span></span></span><a class="headerlink" href="#c.PyBUF_F_CONTIGUOUS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>F</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_ANY_CONTIGUOUS">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_ANY_CONTIGUOUS</span></span></span><a class="headerlink" href="#c.PyBUF_ANY_CONTIGUOUS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>C or F</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyBUF_ND" title="PyBUF_ND"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_ND</span></code></a></p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
<td><p>C</p></td>
</tr>
</tbody>
</table>
</section>
<section id="compound-requests">
<h3>compound requests<a class="headerlink" href="#compound-requests" title="Link to this heading">¶</a></h3>
<p>All possible requests are fully defined by some combination of the flags in
the previous section. For convenience, the buffer protocol provides frequently
used combinations as single flags.</p>
<p>In the following table <em>U</em> stands for undefined contiguity. The consumer would
have to call <a class="reference internal" href="#c.PyBuffer_IsContiguous" title="PyBuffer_IsContiguous"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_IsContiguous()</span></code></a> to determine contiguity.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Request</p></th>
<th class="head"><p>shape</p></th>
<th class="head"><p>strides</p></th>
<th class="head"><p>suboffsets</p></th>
<th class="head"><p>contig</p></th>
<th class="head"><p>readonly</p></th>
<th class="head"><p>format</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_FULL">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_FULL</span></span></span><a class="headerlink" href="#c.PyBUF_FULL" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>if needed</p></td>
<td><p>U</p></td>
<td><p>0</p></td>
<td><p>yes</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_FULL_RO">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_FULL_RO</span></span></span><a class="headerlink" href="#c.PyBUF_FULL_RO" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>if needed</p></td>
<td><p>U</p></td>
<td><p>1 or 0</p></td>
<td><p>yes</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_RECORDS">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_RECORDS</span></span></span><a class="headerlink" href="#c.PyBUF_RECORDS" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>U</p></td>
<td><p>0</p></td>
<td><p>yes</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_RECORDS_RO">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_RECORDS_RO</span></span></span><a class="headerlink" href="#c.PyBUF_RECORDS_RO" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>U</p></td>
<td><p>1 or 0</p></td>
<td><p>yes</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_STRIDED">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_STRIDED</span></span></span><a class="headerlink" href="#c.PyBUF_STRIDED" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>U</p></td>
<td><p>0</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_STRIDED_RO">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_STRIDED_RO</span></span></span><a class="headerlink" href="#c.PyBUF_STRIDED_RO" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>U</p></td>
<td><p>1 or 0</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-even"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_CONTIG">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_CONTIG</span></span></span><a class="headerlink" href="#c.PyBUF_CONTIG" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
<td><p>C</p></td>
<td><p>0</p></td>
<td><p>NULL</p></td>
</tr>
<tr class="row-odd"><td><dl class="c macro">
<dt class="sig sig-object c" id="c.PyBUF_CONTIG_RO">
<span class="sig-name descname"><span class="n"><span class="pre">PyBUF_CONTIG_RO</span></span></span><a class="headerlink" href="#c.PyBUF_CONTIG_RO" title="Link to this definition">¶</a><br /></dt>
<dd></dd></dl>

</td>
<td><p>yes</p></td>
<td><p>NULL</p></td>
<td><p>NULL</p></td>
<td><p>C</p></td>
<td><p>1 or 0</p></td>
<td><p>NULL</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="complex-arrays">
<h2>Complex arrays<a class="headerlink" href="#complex-arrays" title="Link to this heading">¶</a></h2>
<section id="numpy-style-shape-and-strides">
<h3>NumPy-style: shape and strides<a class="headerlink" href="#numpy-style-shape-and-strides" title="Link to this heading">¶</a></h3>
<p>The logical structure of NumPy-style arrays is defined by <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a>,
<a class="reference internal" href="#c.Py_buffer.ndim" title="Py_buffer.ndim"><code class="xref c c-member docutils literal notranslate"><span class="pre">ndim</span></code></a>, <a class="reference internal" href="#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a> and <a class="reference internal" href="#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">ndim</span> <span class="pre">==</span> <span class="pre">0</span></code>, the memory location pointed to by <a class="reference internal" href="#c.Py_buffer.buf" title="Py_buffer.buf"><code class="xref c c-member docutils literal notranslate"><span class="pre">buf</span></code></a> is
interpreted as a scalar of size <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a>. In that case,
both <a class="reference internal" href="#c.Py_buffer.shape" title="Py_buffer.shape"><code class="xref c c-member docutils literal notranslate"><span class="pre">shape</span></code></a> and <a class="reference internal" href="#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a> are <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<p>If <a class="reference internal" href="#c.Py_buffer.strides" title="Py_buffer.strides"><code class="xref c c-member docutils literal notranslate"><span class="pre">strides</span></code></a> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the array is interpreted as
a standard n-dimensional C-array. Otherwise, the consumer must access an
n-dimensional array as follows:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">ptr</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="n">buf</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">indices</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">strides</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">indices</span><span class="p">[</span><span class="n">n</span><span class="mi">-1</span><span class="p">]</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">strides</span><span class="p">[</span><span class="n">n</span><span class="mi">-1</span><span class="p">];</span>
<span class="n">item</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">*</span><span class="p">((</span><span class="n">typeof</span><span class="p">(</span><span class="n">item</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="n">ptr</span><span class="p">);</span>
</pre></div>
</div>
<p>As noted above, <a class="reference internal" href="#c.Py_buffer.buf" title="Py_buffer.buf"><code class="xref c c-member docutils literal notranslate"><span class="pre">buf</span></code></a> can point to any location within
the actual memory block. An exporter can check the validity of a buffer with
this function:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">verify_structure</span><span class="p">(</span><span class="n">memlen</span><span class="p">,</span> <span class="n">itemsize</span><span class="p">,</span> <span class="n">ndim</span><span class="p">,</span> <span class="n">shape</span><span class="p">,</span> <span class="n">strides</span><span class="p">,</span> <span class="n">offset</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Verify that the parameters represent a valid array within</span>
<span class="sd">       the bounds of the allocated memory:</span>
<span class="sd">           char *mem: start of the physical memory block</span>
<span class="sd">           memlen: length of the physical memory block</span>
<span class="sd">           offset: (char *)buf - mem</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">offset</span> <span class="o">%</span> <span class="n">itemsize</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">False</span>
    <span class="k">if</span> <span class="n">offset</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">offset</span><span class="o">+</span><span class="n">itemsize</span> <span class="o">&gt;</span> <span class="n">memlen</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">False</span>
    <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="n">v</span> <span class="o">%</span> <span class="n">itemsize</span> <span class="k">for</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">strides</span><span class="p">):</span>
        <span class="k">return</span> <span class="kc">False</span>

    <span class="k">if</span> <span class="n">ndim</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">ndim</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">shape</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">strides</span>
    <span class="k">if</span> <span class="mi">0</span> <span class="ow">in</span> <span class="n">shape</span><span class="p">:</span>
        <span class="k">return</span> <span class="kc">True</span>

    <span class="n">imin</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">strides</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">*</span><span class="p">(</span><span class="n">shape</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">ndim</span><span class="p">)</span>
               <span class="k">if</span> <span class="n">strides</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">imax</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">strides</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">*</span><span class="p">(</span><span class="n">shape</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">ndim</span><span class="p">)</span>
               <span class="k">if</span> <span class="n">strides</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">)</span>

    <span class="k">return</span> <span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">offset</span><span class="o">+</span><span class="n">imin</span> <span class="ow">and</span> <span class="n">offset</span><span class="o">+</span><span class="n">imax</span><span class="o">+</span><span class="n">itemsize</span> <span class="o">&lt;=</span> <span class="n">memlen</span>
</pre></div>
</div>
</section>
<section id="pil-style-shape-strides-and-suboffsets">
<h3>PIL-style: shape, strides and suboffsets<a class="headerlink" href="#pil-style-shape-strides-and-suboffsets" title="Link to this heading">¶</a></h3>
<p>In addition to the regular items, PIL-style arrays can contain pointers
that must be followed in order to get to the next element in a dimension.
For example, the regular three-dimensional C-array <code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">v[2][2][3]</span></code> can
also be viewed as an array of 2 pointers to 2 two-dimensional arrays:
<code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">(*v[2])[2][3]</span></code>. In suboffsets representation, those two pointers
can be embedded at the start of <a class="reference internal" href="#c.Py_buffer.buf" title="Py_buffer.buf"><code class="xref c c-member docutils literal notranslate"><span class="pre">buf</span></code></a>, pointing
to two <code class="docutils literal notranslate"><span class="pre">char</span> <span class="pre">x[2][3]</span></code> arrays that can be located anywhere in memory.</p>
<p>Here is a function that returns a pointer to the element in an N-D array
pointed to by an N-dimensional index when there are both non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> strides
and suboffsets:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="nf">get_item_pointer</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">ndim</span><span class="p">,</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="o">*</span><span class="n">buf</span><span class="p">,</span><span class="w"> </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="o">*</span><span class="n">strides</span><span class="p">,</span>
<span class="w">                       </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="o">*</span><span class="n">suboffsets</span><span class="p">,</span><span class="w"> </span><span class="n">Py_ssize_t</span><span class="w"> </span><span class="o">*</span><span class="n">indices</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kt">char</span><span class="w"> </span><span class="o">*</span><span class="n">pointer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">char</span><span class="o">*</span><span class="p">)</span><span class="n">buf</span><span class="p">;</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">i</span><span class="p">;</span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">ndim</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">pointer</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="n">strides</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">indices</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">suboffsets</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="w"> </span><span class="o">&gt;=</span><span class="mi">0</span><span class="w"> </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">pointer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">*</span><span class="p">((</span><span class="kt">char</span><span class="o">**</span><span class="p">)</span><span class="n">pointer</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">suboffsets</span><span class="p">[</span><span class="n">i</span><span class="p">];</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="kt">void</span><span class="o">*</span><span class="p">)</span><span class="n">pointer</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="buffer-related-functions">
<h2>Buffer-related functions<a class="headerlink" href="#buffer-related-functions" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CheckBuffer">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CheckBuffer</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CheckBuffer" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Return <code class="docutils literal notranslate"><span class="pre">1</span></code> if <em>obj</em> supports the buffer interface otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.  When <code class="docutils literal notranslate"><span class="pre">1</span></code> is
returned, it doesn’t guarantee that <a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a> will
succeed.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_GetBuffer">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_GetBuffer</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">exporter</span></span>, <a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_GetBuffer" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Send a request to <em>exporter</em> to fill in <em>view</em> as specified by  <em>flags</em>.
If the exporter cannot provide a buffer of the exact type, it MUST raise
<a class="reference internal" href="../library/exceptions.html#BufferError" title="BufferError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BufferError</span></code></a>, set <code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code> to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and
return <code class="docutils literal notranslate"><span class="pre">-1</span></code>.</p>
<p>On success, fill in <em>view</em>, set <code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code> to a new reference
to <em>exporter</em> and return 0. In the case of chained buffer providers
that redirect requests to a single object, <code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code> MAY
refer to this object instead of <em>exporter</em> (See <a class="reference internal" href="typeobj.html#buffer-structs"><span class="std std-ref">Buffer Object Structures</span></a>).</p>
<p>Successful calls to <a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a> must be paired with calls
to <a class="reference internal" href="#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a>, similar to <code class="xref c c-func docutils literal notranslate"><span class="pre">malloc()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">free()</span></code>.
Thus, after the consumer is done with the buffer, <a class="reference internal" href="#c.PyBuffer_Release" title="PyBuffer_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyBuffer_Release()</span></code></a>
must be called exactly once.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_Release">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_Release</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_Release" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Release the buffer <em>view</em> and release the <a class="reference internal" href="../glossary.html#term-strong-reference"><span class="xref std std-term">strong reference</span></a>
(i.e. decrement the reference count) to the view’s supporting object,
<code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code>. This function MUST be called when the buffer
is no longer being used, otherwise reference leaks may occur.</p>
<p>It is an error to call this function on a buffer that was not obtained via
<a class="reference internal" href="#c.PyObject_GetBuffer" title="PyObject_GetBuffer"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetBuffer()</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_SizeFromFormat">
<a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_SizeFromFormat</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">format</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_SizeFromFormat" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Return the implied <a class="reference internal" href="#c.Py_buffer.itemsize" title="Py_buffer.itemsize"><code class="xref c c-member docutils literal notranslate"><span class="pre">itemsize</span></code></a> from <a class="reference internal" href="#c.Py_buffer.format" title="Py_buffer.format"><code class="xref c c-member docutils literal notranslate"><span class="pre">format</span></code></a>.
On error, raise an exception and return -1.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_IsContiguous">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_IsContiguous</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="n"><span class="pre">order</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_IsContiguous" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Return <code class="docutils literal notranslate"><span class="pre">1</span></code> if the memory defined by the <em>view</em> is C-style (<em>order</em> is
<code class="docutils literal notranslate"><span class="pre">'C'</span></code>) or Fortran-style (<em>order</em> is <code class="docutils literal notranslate"><span class="pre">'F'</span></code>) <a class="reference internal" href="../glossary.html#term-contiguous"><span class="xref std std-term">contiguous</span></a> or either one
(<em>order</em> is <code class="docutils literal notranslate"><span class="pre">'A'</span></code>).  Return <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_GetPointer">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_GetPointer</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">indices</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_GetPointer" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Get the memory area pointed to by the <em>indices</em> inside the given <em>view</em>.
<em>indices</em> must point to an array of <code class="docutils literal notranslate"><span class="pre">view-&gt;ndim</span></code> indices.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_FromContiguous">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_FromContiguous</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">buf</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">len</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="n"><span class="pre">fort</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_FromContiguous" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Copy contiguous <em>len</em> bytes from <em>buf</em> to <em>view</em>.
<em>fort</em> can be <code class="docutils literal notranslate"><span class="pre">'C'</span></code> or <code class="docutils literal notranslate"><span class="pre">'F'</span></code> (for C-style or Fortran-style ordering).
<code class="docutils literal notranslate"><span class="pre">0</span></code> is returned on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_ToContiguous">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_ToContiguous</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">buf</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">src</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">len</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="n"><span class="pre">order</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_ToContiguous" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Copy <em>len</em> bytes from <em>src</em> to its contiguous representation in <em>buf</em>.
<em>order</em> can be <code class="docutils literal notranslate"><span class="pre">'C'</span></code> or <code class="docutils literal notranslate"><span class="pre">'F'</span></code> or <code class="docutils literal notranslate"><span class="pre">'A'</span></code> (for C-style or Fortran-style
ordering or either one). <code class="docutils literal notranslate"><span class="pre">0</span></code> is returned on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
<p>This function fails if <em>len</em> != <em>src-&gt;len</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyObject_CopyData">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyObject_CopyData</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">dest</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">src</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyObject_CopyData" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Copy data from <em>src</em> to <em>dest</em> buffer. Can convert between C-style and
or Fortran-style buffers.</p>
<p><code class="docutils literal notranslate"><span class="pre">0</span></code> is returned on success, <code class="docutils literal notranslate"><span class="pre">-1</span></code> on error.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_FillContiguousStrides">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_FillContiguousStrides</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">ndims</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">shape</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">strides</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">itemsize</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="n"><span class="pre">order</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_FillContiguousStrides" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Fill the <em>strides</em> array with byte-strides of a <a class="reference internal" href="../glossary.html#term-contiguous"><span class="xref std std-term">contiguous</span></a> (C-style if
<em>order</em> is <code class="docutils literal notranslate"><span class="pre">'C'</span></code> or Fortran-style if <em>order</em> is <code class="docutils literal notranslate"><span class="pre">'F'</span></code>) array of the
given shape with the given number of bytes per element.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyBuffer_FillInfo">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyBuffer_FillInfo</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_buffer" title="Py_buffer"><span class="n"><span class="pre">Py_buffer</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">view</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">exporter</span></span>, <span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">buf</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">len</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">readonly</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">flags</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyBuffer_FillInfo" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>Handle buffer requests for an exporter that wants to expose <em>buf</em> of size <em>len</em>
with writability set according to <em>readonly</em>. <em>buf</em> is interpreted as a sequence
of unsigned bytes.</p>
<p>The <em>flags</em> argument indicates the request type. This function always fills in
<em>view</em> as specified by flags, unless <em>buf</em> has been designated as read-only
and <a class="reference internal" href="#c.PyBUF_WRITABLE" title="PyBUF_WRITABLE"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyBUF_WRITABLE</span></code></a> is set in <em>flags</em>.</p>
<p>On success, set <code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code> to a new reference to <em>exporter</em> and
return 0. Otherwise, raise <a class="reference internal" href="../library/exceptions.html#BufferError" title="BufferError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BufferError</span></code></a>, set
<code class="docutils literal notranslate"><span class="pre">view-&gt;obj</span></code> to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and return <code class="docutils literal notranslate"><span class="pre">-1</span></code>;</p>
<p>If this function is used as part of a <a class="reference internal" href="typeobj.html#buffer-structs"><span class="std std-ref">getbufferproc</span></a>,
<em>exporter</em> MUST be set to the exporting object and <em>flags</em> must be passed
unmodified. Otherwise, <em>exporter</em> MUST be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Buffer Protocol</a><ul>
<li><a class="reference internal" href="#buffer-structure">Buffer structure</a></li>
<li><a class="reference internal" href="#buffer-request-types">Buffer request types</a><ul>
<li><a class="reference internal" href="#request-independent-fields">request-independent fields</a></li>
<li><a class="reference internal" href="#readonly-format">readonly, format</a></li>
<li><a class="reference internal" href="#shape-strides-suboffsets">shape, strides, suboffsets</a></li>
<li><a class="reference internal" href="#contiguity-requests">contiguity requests</a></li>
<li><a class="reference internal" href="#compound-requests">compound requests</a></li>
</ul>
</li>
<li><a class="reference internal" href="#complex-arrays">Complex arrays</a><ul>
<li><a class="reference internal" href="#numpy-style-shape-and-strides">NumPy-style: shape and strides</a></li>
<li><a class="reference internal" href="#pil-style-shape-strides-and-suboffsets">PIL-style: shape, strides and suboffsets</a></li>
</ul>
</li>
<li><a class="reference internal" href="#buffer-related-functions">Buffer-related functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="iter.html"
                          title="previous chapter">Iterator Protocol</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="objbuffer.html"
                          title="next chapter">Old Buffer Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/buffer.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="objbuffer.html" title="Old Buffer Protocol"
             >next</a> |</li>
        <li class="right" >
          <a href="iter.html" title="Iterator Protocol"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="abstract.html" >Abstract Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Buffer Protocol</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>