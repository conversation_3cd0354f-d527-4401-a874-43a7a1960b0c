<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="website_sale.addToCartNotification">
        <div class="row g-2 mb-2" t-foreach="props.lines" t-as="line" t-key="line.id">
            <div class="col-3">
                <img class="img o_image_64_max rounded mb-2 img-fluid"
                    t-att-src="line.image_url"
                    t-att-alt="line.name"/>
            </div>
            <div class="col-6 d-flex flex-column align-items-start">
                <span t-out="getProductSummary(line)"/>
                <span class="text-muted small"
                    t-if="line.description"
                    t-out="line.description"/>
            </div>
            <div class="col-3 d-flex flex-column align-items-end gap-1"
                t-out="getFormattedPrice(line)"/>
        </div>
        <a role="button" class="w-100 btn btn-primary" href="/shop/cart">
            View cart
        </a>
    </t>

</templates>
