# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment_authorize
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 21:56+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: website_payment_authorize
#: model:ir.model.fields,field_description:website_payment_authorize.field_res_config_settings__authorize_capture_method
msgid "Authorize.net: Payment Capture Method"
msgstr ""

#. module: website_payment_authorize
#: model:ir.model.fields.selection,name:website_payment_authorize.selection__res_config_settings__authorize_capture_method__auto
msgid "Automatically Capture Payment"
msgstr ""

#. module: website_payment_authorize
#: model_terms:ir.ui.view,arch_db:website_payment_authorize.res_config_settings_view_form
msgid ""
"Charge order directly or authorize at the order and capture the payment "
"later on, manually."
msgstr ""

#. module: website_payment_authorize
#: model:ir.model,name:website_payment_authorize.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_payment_authorize
#: model:ir.model.fields.selection,name:website_payment_authorize.selection__res_config_settings__authorize_capture_method__manual
msgid "Manually Charge Later"
msgstr ""
