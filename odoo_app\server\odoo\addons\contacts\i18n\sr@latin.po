# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * contacts
#
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"Language: sr@latin\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_action_res_partner_bank_form
#: model:ir.ui.menu,name:contacts.menu_config_bank_accounts
msgid "Bank Accounts"
msgstr "Bankovni računi"

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_action_res_bank_form
msgid "Banks"
msgstr "Banke"

#. module: contacts
#: model:ir.ui.menu,name:contacts.res_partner_menu_config
msgid "Configuration"
msgstr "Postavka"

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_partner_category_form
msgid "Contact Tags"
msgstr ""

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_partner_title_contact
msgid "Contact Titles"
msgstr "Naslovi kontakta"

#. module: contacts
#: model:ir.actions.act_window,name:contacts.action_contacts
#: model:ir.ui.menu,name:contacts.menu_contacts
#: model:ir.ui.menu,name:contacts.res_partner_menu_contacts
msgid "Contacts"
msgstr "Kontakti"

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_country_partner
msgid "Countries"
msgstr "Zemlje"

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_country_group
msgid "Country Group"
msgstr "Regija"

#. module: contacts
#: model_terms:ir.actions.act_window,help:contacts.action_contacts
msgid "Create a Contact in your address book"
msgstr ""

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_country_state_partner
msgid "Fed. States"
msgstr ""

#. module: contacts
#: model:ir.ui.menu,name:contacts.res_partner_industry_menu
msgid "Industries"
msgstr ""

#. module: contacts
#: model:ir.ui.menu,name:contacts.menu_localisation
msgid "Localization"
msgstr "Lokalizacija"

#. module: contacts
#: model_terms:ir.actions.act_window,help:contacts.action_contacts
msgid "Odoo helps you track all activities related to your contacts."
msgstr ""

#. module: contacts
#: model:ir.model,name:contacts.model_res_users
msgid "User"
msgstr ""
