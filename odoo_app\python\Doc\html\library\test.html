<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="test — Regression tests package for Python" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/test.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The test package contains all regression tests for Python as well as the modules test.support and test.regrtest. test.support is used to enhance your tests while test.regrtest drives the testing su..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The test package contains all regression tests for Python as well as the modules test.support and test.regrtest. test.support is used to enhance your tests while test.regrtest drives the testing su..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>test — Regression tests package for Python &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Debugging and Profiling" href="debug.html" />
    <link rel="prev" title="2to3 — Automated Python 2 to 3 code translation" href="2to3.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/test.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> — Regression tests package for Python</a><ul>
<li><a class="reference internal" href="#writing-unit-tests-for-the-test-package">Writing Unit Tests for the <code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> package</a></li>
<li><a class="reference internal" href="#module-test.regrtest">Running tests using the command-line interface</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-test.support"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code> — Utilities for the Python test suite</a></li>
<li><a class="reference internal" href="#module-test.support.socket_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.socket_helper</span></code> — Utilities for socket tests</a></li>
<li><a class="reference internal" href="#module-test.support.script_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.script_helper</span></code> — Utilities for the Python execution tests</a></li>
<li><a class="reference internal" href="#module-test.support.bytecode_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.bytecode_helper</span></code> — Support tools for testing correct bytecode generation</a></li>
<li><a class="reference internal" href="#module-test.support.threading_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.threading_helper</span></code> — Utilities for threading tests</a></li>
<li><a class="reference internal" href="#module-test.support.os_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.os_helper</span></code> — Utilities for os tests</a></li>
<li><a class="reference internal" href="#module-test.support.import_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.import_helper</span></code> — Utilities for import tests</a></li>
<li><a class="reference internal" href="#module-test.support.warnings_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.warnings_helper</span></code> — Utilities for warnings tests</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="2to3.html"
                          title="previous chapter">2to3 — Automated Python 2 to 3 code translation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="debug.html"
                          title="next chapter">Debugging and Profiling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/test.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="debug.html" title="Debugging and Profiling"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="2to3.html" title="2to3 — Automated Python 2 to 3 code translation"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" accesskey="U">Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> — Regression tests package for Python</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-test">
<span id="test-regression-tests-package-for-python"></span><h1><a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> — Regression tests package for Python<a class="headerlink" href="#module-test" title="Link to this heading">¶</a></h1>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package is meant for internal use by Python only. It is
documented for the benefit of the core developers of Python. Any use of
this package outside of Python’s standard library is discouraged as code
mentioned here can change or be removed without notice between releases of
Python.</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package contains all regression tests for Python as well as the
modules <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> and <a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a>.
<a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> is used to enhance your tests while
<a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a> drives the testing suite.</p>
<p>Each module in the <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package whose name starts with <code class="docutils literal notranslate"><span class="pre">test_</span></code> is a
testing suite for a specific module or feature. All new tests should be written
using the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> or <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a> module.  Some older tests are
written using a “traditional” testing style that compares output printed to
<code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>; this style of test is considered deprecated.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a></dt><dd><p>Writing PyUnit regression tests.</p>
</dd>
<dt>Module <a class="reference internal" href="doctest.html#module-doctest" title="doctest: Test pieces of code within docstrings."><code class="xref py py-mod docutils literal notranslate"><span class="pre">doctest</span></code></a></dt><dd><p>Tests embedded in documentation strings.</p>
</dd>
</dl>
</div>
<section id="writing-unit-tests-for-the-test-package">
<span id="writing-tests"></span><h2>Writing Unit Tests for the <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package<a class="headerlink" href="#writing-unit-tests-for-the-test-package" title="Link to this heading">¶</a></h2>
<p>It is preferred that tests that use the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> module follow a few
guidelines. One is to name the test module by starting it with <code class="docutils literal notranslate"><span class="pre">test_</span></code> and end
it with the name of the module being tested. The test methods in the test module
should start with <code class="docutils literal notranslate"><span class="pre">test_</span></code> and end with a description of what the method is
testing. This is needed so that the methods are recognized by the test driver as
test methods. Also, no documentation string for the method should be included. A
comment (such as <code class="docutils literal notranslate"><span class="pre">#</span> <span class="pre">Tests</span> <span class="pre">function</span> <span class="pre">returns</span> <span class="pre">only</span> <span class="pre">True</span> <span class="pre">or</span> <span class="pre">False</span></code>) should be used
to provide documentation for test methods. This is done because documentation
strings get printed out if they exist and thus what test is being run is not
stated.</p>
<p>A basic boilerplate is often used:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">unittest</span>
<span class="kn">from</span> <span class="nn">test</span> <span class="kn">import</span> <span class="n">support</span>

<span class="k">class</span> <span class="nc">MyTestCase1</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>

    <span class="c1"># Only use setUp() and tearDown() if necessary</span>

    <span class="k">def</span> <span class="nf">setUp</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span> <span class="n">code</span> <span class="n">to</span> <span class="n">execute</span> <span class="ow">in</span> <span class="n">preparation</span> <span class="k">for</span> <span class="n">tests</span> <span class="o">...</span>

    <span class="k">def</span> <span class="nf">tearDown</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="o">...</span> <span class="n">code</span> <span class="n">to</span> <span class="n">execute</span> <span class="n">to</span> <span class="n">clean</span> <span class="n">up</span> <span class="n">after</span> <span class="n">tests</span> <span class="o">...</span>

    <span class="k">def</span> <span class="nf">test_feature_one</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Test feature one.</span>
        <span class="o">...</span> <span class="n">testing</span> <span class="n">code</span> <span class="o">...</span>

    <span class="k">def</span> <span class="nf">test_feature_two</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Test feature two.</span>
        <span class="o">...</span> <span class="n">testing</span> <span class="n">code</span> <span class="o">...</span>

    <span class="o">...</span> <span class="n">more</span> <span class="n">test</span> <span class="n">methods</span> <span class="o">...</span>

<span class="k">class</span> <span class="nc">MyTestCase2</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="o">...</span> <span class="n">same</span> <span class="n">structure</span> <span class="k">as</span> <span class="n">MyTestCase1</span> <span class="o">...</span>

<span class="o">...</span> <span class="n">more</span> <span class="n">test</span> <span class="n">classes</span> <span class="o">...</span>

<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="n">unittest</span><span class="o">.</span><span class="n">main</span><span class="p">()</span>
</pre></div>
</div>
<p>This code pattern allows the testing suite to be run by <a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a>,
on its own as a script that supports the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> CLI, or via the
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">unittest</span></code> CLI.</p>
<p>The goal for regression testing is to try to break code. This leads to a few
guidelines to be followed:</p>
<ul>
<li><p>The testing suite should exercise all classes, functions, and constants. This
includes not just the external API that is to be presented to the outside
world but also “private” code.</p></li>
<li><p>Whitebox testing (examining the code being tested when the tests are being
written) is preferred. Blackbox testing (testing only the published user
interface) is not complete enough to make sure all boundary and edge cases
are tested.</p></li>
<li><p>Make sure all possible values are tested including invalid ones. This makes
sure that not only all valid values are acceptable but also that improper
values are handled correctly.</p></li>
<li><p>Exhaust as many code paths as possible. Test where branching occurs and thus
tailor input to make sure as many different paths through the code are taken.</p></li>
<li><p>Add an explicit test for any bugs discovered for the tested code. This will
make sure that the error does not crop up again if the code is changed in the
future.</p></li>
<li><p>Make sure to clean up after your tests (such as close and remove all temporary
files).</p></li>
<li><p>If a test is dependent on a specific condition of the operating system then
verify the condition already exists before attempting the test.</p></li>
<li><p>Import as few modules as possible and do it as soon as possible. This
minimizes external dependencies of tests and also minimizes possible anomalous
behavior from side-effects of importing a module.</p></li>
<li><p>Try to maximize code reuse. On occasion, tests will vary by something as small
as what type of input is used. Minimize code duplication by subclassing a
basic test class with a class that specifies the input:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">TestFuncAcceptsSequencesMixin</span><span class="p">:</span>

    <span class="n">func</span> <span class="o">=</span> <span class="n">mySuperWhammyFunction</span>

    <span class="k">def</span> <span class="nf">test_func</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">func</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">arg</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">AcceptLists</span><span class="p">(</span><span class="n">TestFuncAcceptsSequencesMixin</span><span class="p">,</span> <span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="n">arg</span> <span class="o">=</span> <span class="p">[</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">]</span>

<span class="k">class</span> <span class="nc">AcceptStrings</span><span class="p">(</span><span class="n">TestFuncAcceptsSequencesMixin</span><span class="p">,</span> <span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="n">arg</span> <span class="o">=</span> <span class="s1">&#39;abc&#39;</span>

<span class="k">class</span> <span class="nc">AcceptTuples</span><span class="p">(</span><span class="n">TestFuncAcceptsSequencesMixin</span><span class="p">,</span> <span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="n">arg</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
<p>When using this pattern, remember that all classes that inherit from
<a class="reference internal" href="unittest.html#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestCase</span></code></a> are run as tests.  The <code class="xref py py-class docutils literal notranslate"><span class="pre">TestFuncAcceptsSequencesMixin</span></code> class in the example above
does not have any data and so can’t be run by itself, thus it does not
inherit from <a class="reference internal" href="unittest.html#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestCase</span></code></a>.</p>
</li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Test Driven Development</dt><dd><p>A book by Kent Beck on writing tests before code.</p>
</dd>
</dl>
</div>
</section>
<section id="module-test.regrtest">
<span id="running-tests-using-the-command-line-interface"></span><span id="regrtest"></span><h2>Running tests using the command-line interface<a class="headerlink" href="#module-test.regrtest" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package can be run as a script to drive Python’s regression
test suite, thanks to the <a class="reference internal" href="../using/cmdline.html#cmdoption-m"><code class="xref std std-option docutils literal notranslate"><span class="pre">-m</span></code></a> option: <strong class="program">python -m test</strong>. Under
the hood, it uses <a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a>; the call <strong class="program">python -m
test.regrtest</strong> used in previous Python versions still works.  Running the
script by itself automatically starts running all regression tests in the
<a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> package. It does this by finding all modules in the package whose
name starts with <code class="docutils literal notranslate"><span class="pre">test_</span></code>, importing them, and executing the function
<code class="xref py py-func docutils literal notranslate"><span class="pre">test_main()</span></code> if present or loading the tests via
unittest.TestLoader.loadTestsFromModule if <code class="docutils literal notranslate"><span class="pre">test_main</span></code> does not exist.  The
names of tests to execute may also be passed to the script. Specifying a single
regression test (<strong class="program">python -m test test_spam</strong>) will minimize output and
only print whether the test passed or failed.</p>
<p>Running <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> directly allows what resources are available for
tests to use to be set. You do this by using the <code class="docutils literal notranslate"><span class="pre">-u</span></code> command-line
option. Specifying <code class="docutils literal notranslate"><span class="pre">all</span></code> as the value for the <code class="docutils literal notranslate"><span class="pre">-u</span></code> option enables all
possible resources: <strong class="program">python -m test -uall</strong>.
If all but one resource is desired (a more common case), a
comma-separated list of resources that are not desired may be listed after
<code class="docutils literal notranslate"><span class="pre">all</span></code>. The command <strong class="program">python -m test -uall,-audio,-largefile</strong>
will run <a class="reference internal" href="#module-test" title="test: Regression tests package containing the testing suite for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code></a> with all resources except the <code class="docutils literal notranslate"><span class="pre">audio</span></code> and
<code class="docutils literal notranslate"><span class="pre">largefile</span></code> resources. For a list of all resources and more command-line
options, run <strong class="program">python -m test -h</strong>.</p>
<p>Some other ways to execute the regression tests depend on what platform the
tests are being executed on. On Unix, you can run <strong class="program">make test</strong> at the
top-level directory where Python was built. On Windows,
executing <strong class="program">rt.bat</strong> from your <code class="file docutils literal notranslate"><span class="pre">PCbuild</span></code> directory will run all
regression tests.</p>
</section>
</section>
<section id="module-test.support">
<span id="test-support-utilities-for-the-python-test-suite"></span><h1><a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> — Utilities for the Python test suite<a class="headerlink" href="#module-test.support" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> module provides support for Python’s regression
test suite.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> is not a public module.  It is documented here to help
Python developers write tests.  The API of this module is subject to change
without backwards compatibility concerns between releases.</p>
</div>
<p>This module defines the following exceptions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="test.support.TestFailed">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">TestFailed</span></span><a class="headerlink" href="#test.support.TestFailed" title="Link to this definition">¶</a></dt>
<dd><p>Exception to be raised when a test fails. This is deprecated in favor of
<a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a>-based tests and <a class="reference internal" href="unittest.html#unittest.TestCase" title="unittest.TestCase"><code class="xref py py-class docutils literal notranslate"><span class="pre">unittest.TestCase</span></code></a>’s assertion
methods.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="test.support.ResourceDenied">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">ResourceDenied</span></span><a class="headerlink" href="#test.support.ResourceDenied" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="unittest.html#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">unittest.SkipTest</span></code></a>. Raised when a resource (such as a
network connection) is not available. Raised by the <a class="reference internal" href="#test.support.requires" title="test.support.requires"><code class="xref py py-func docutils literal notranslate"><span class="pre">requires()</span></code></a>
function.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> module defines the following constants:</p>
<dl class="py data">
<dt class="sig sig-object py" id="test.support.verbose">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">verbose</span></span><a class="headerlink" href="#test.support.verbose" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> when verbose output is enabled. Should be checked when more
detailed information is desired about a running test. <em>verbose</em> is set by
<a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.is_jython">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">is_jython</span></span><a class="headerlink" href="#test.support.is_jython" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the running interpreter is Jython.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.is_android">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">is_android</span></span><a class="headerlink" href="#test.support.is_android" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> if the system is Android.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.unix_shell">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">unix_shell</span></span><a class="headerlink" href="#test.support.unix_shell" title="Link to this definition">¶</a></dt>
<dd><p>Path for shell if not on Windows; otherwise <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.LOOPBACK_TIMEOUT">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">LOOPBACK_TIMEOUT</span></span><a class="headerlink" href="#test.support.LOOPBACK_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Timeout in seconds for tests using a network server listening on the network
local loopback interface like <code class="docutils literal notranslate"><span class="pre">127.0.0.1</span></code>.</p>
<p>The timeout is long enough to prevent test failure: it takes into account
that the client and the server can run in different threads or even
different processes.</p>
<p>The timeout should be long enough for <a class="reference internal" href="socket.html#socket.socket.connect" title="socket.socket.connect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">connect()</span></code></a>,
<a class="reference internal" href="socket.html#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> and <a class="reference internal" href="socket.html#socket.socket.send" title="socket.socket.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">send()</span></code></a> methods of
<a class="reference internal" href="socket.html#socket.socket" title="socket.socket"><code class="xref py py-class docutils literal notranslate"><span class="pre">socket.socket</span></code></a>.</p>
<p>Its default value is 5 seconds.</p>
<p>See also <a class="reference internal" href="#test.support.INTERNET_TIMEOUT" title="test.support.INTERNET_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">INTERNET_TIMEOUT</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.INTERNET_TIMEOUT">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">INTERNET_TIMEOUT</span></span><a class="headerlink" href="#test.support.INTERNET_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Timeout in seconds for network requests going to the internet.</p>
<p>The timeout is short enough to prevent a test to wait for too long if the
internet request is blocked for whatever reason.</p>
<p>Usually, a timeout using <a class="reference internal" href="#test.support.INTERNET_TIMEOUT" title="test.support.INTERNET_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">INTERNET_TIMEOUT</span></code></a> should not mark a test as
failed, but skip the test instead: see
<a class="reference internal" href="#test.support.socket_helper.transient_internet" title="test.support.socket_helper.transient_internet"><code class="xref py py-func docutils literal notranslate"><span class="pre">transient_internet()</span></code></a>.</p>
<p>Its default value is 1 minute.</p>
<p>See also <a class="reference internal" href="#test.support.LOOPBACK_TIMEOUT" title="test.support.LOOPBACK_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">LOOPBACK_TIMEOUT</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.SHORT_TIMEOUT">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">SHORT_TIMEOUT</span></span><a class="headerlink" href="#test.support.SHORT_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Timeout in seconds to mark a test as failed if the test takes “too long”.</p>
<p>The timeout value depends on the regrtest <code class="docutils literal notranslate"><span class="pre">--timeout</span></code> command line option.</p>
<p>If a test using <a class="reference internal" href="#test.support.SHORT_TIMEOUT" title="test.support.SHORT_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">SHORT_TIMEOUT</span></code></a> starts to fail randomly on slow
buildbots, use <a class="reference internal" href="#test.support.LONG_TIMEOUT" title="test.support.LONG_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">LONG_TIMEOUT</span></code></a> instead.</p>
<p>Its default value is 30 seconds.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.LONG_TIMEOUT">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">LONG_TIMEOUT</span></span><a class="headerlink" href="#test.support.LONG_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Timeout in seconds to detect when a test hangs.</p>
<p>It is long enough to reduce the risk of test failure on the slowest Python
buildbots. It should not be used to mark a test as failed if the test takes
“too long”.  The timeout value depends on the regrtest <code class="docutils literal notranslate"><span class="pre">--timeout</span></code> command
line option.</p>
<p>Its default value is 5 minutes.</p>
<p>See also <a class="reference internal" href="#test.support.LOOPBACK_TIMEOUT" title="test.support.LOOPBACK_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">LOOPBACK_TIMEOUT</span></code></a>, <a class="reference internal" href="#test.support.INTERNET_TIMEOUT" title="test.support.INTERNET_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">INTERNET_TIMEOUT</span></code></a> and
<a class="reference internal" href="#test.support.SHORT_TIMEOUT" title="test.support.SHORT_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">SHORT_TIMEOUT</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.PGO">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">PGO</span></span><a class="headerlink" href="#test.support.PGO" title="Link to this definition">¶</a></dt>
<dd><p>Set when tests can be skipped when they are not useful for PGO.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.PIPE_MAX_SIZE">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">PIPE_MAX_SIZE</span></span><a class="headerlink" href="#test.support.PIPE_MAX_SIZE" title="Link to this definition">¶</a></dt>
<dd><p>A constant that is likely larger than the underlying OS pipe buffer size,
to make writes blocking.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.Py_DEBUG">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">Py_DEBUG</span></span><a class="headerlink" href="#test.support.Py_DEBUG" title="Link to this definition">¶</a></dt>
<dd><p>True if Python was built with the <a class="reference internal" href="../c-api/intro.html#c.Py_DEBUG" title="Py_DEBUG"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_DEBUG</span></code></a> macro
defined, that is, if
Python was <a class="reference internal" href="../using/configure.html#debug-build"><span class="std std-ref">built in debug mode</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.SOCK_MAX_SIZE">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">SOCK_MAX_SIZE</span></span><a class="headerlink" href="#test.support.SOCK_MAX_SIZE" title="Link to this definition">¶</a></dt>
<dd><p>A constant that is likely larger than the underlying OS socket buffer size,
to make writes blocking.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.TEST_SUPPORT_DIR">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">TEST_SUPPORT_DIR</span></span><a class="headerlink" href="#test.support.TEST_SUPPORT_DIR" title="Link to this definition">¶</a></dt>
<dd><p>Set to the top level directory that contains <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.TEST_HOME_DIR">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">TEST_HOME_DIR</span></span><a class="headerlink" href="#test.support.TEST_HOME_DIR" title="Link to this definition">¶</a></dt>
<dd><p>Set to the top level directory for the test package.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.TEST_DATA_DIR">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">TEST_DATA_DIR</span></span><a class="headerlink" href="#test.support.TEST_DATA_DIR" title="Link to this definition">¶</a></dt>
<dd><p>Set to the <code class="docutils literal notranslate"><span class="pre">data</span></code> directory within the test package.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.MAX_Py_ssize_t">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">MAX_Py_ssize_t</span></span><a class="headerlink" href="#test.support.MAX_Py_ssize_t" title="Link to this definition">¶</a></dt>
<dd><p>Set to <a class="reference internal" href="sys.html#sys.maxsize" title="sys.maxsize"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.maxsize</span></code></a> for big memory tests.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.max_memuse">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">max_memuse</span></span><a class="headerlink" href="#test.support.max_memuse" title="Link to this definition">¶</a></dt>
<dd><p>Set by <a class="reference internal" href="#test.support.set_memlimit" title="test.support.set_memlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_memlimit()</span></code></a> as the memory limit for big memory tests.
Limited by <a class="reference internal" href="#test.support.MAX_Py_ssize_t" title="test.support.MAX_Py_ssize_t"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAX_Py_ssize_t</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.real_max_memuse">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">real_max_memuse</span></span><a class="headerlink" href="#test.support.real_max_memuse" title="Link to this definition">¶</a></dt>
<dd><p>Set by <a class="reference internal" href="#test.support.set_memlimit" title="test.support.set_memlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_memlimit()</span></code></a> as the memory limit for big memory tests.  Not
limited by <a class="reference internal" href="#test.support.MAX_Py_ssize_t" title="test.support.MAX_Py_ssize_t"><code class="xref py py-data docutils literal notranslate"><span class="pre">MAX_Py_ssize_t</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.MISSING_C_DOCSTRINGS">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">MISSING_C_DOCSTRINGS</span></span><a class="headerlink" href="#test.support.MISSING_C_DOCSTRINGS" title="Link to this definition">¶</a></dt>
<dd><p>Set to <code class="docutils literal notranslate"><span class="pre">True</span></code> if Python is built without docstrings (the
<code class="xref c c-macro docutils literal notranslate"><span class="pre">WITH_DOC_STRINGS</span></code> macro is not defined).
See the <a class="reference internal" href="../using/configure.html#cmdoption-without-doc-strings"><code class="xref std std-option docutils literal notranslate"><span class="pre">configure</span> <span class="pre">--without-doc-strings</span></code></a> option.</p>
<p>See also the <a class="reference internal" href="#test.support.HAVE_DOCSTRINGS" title="test.support.HAVE_DOCSTRINGS"><code class="xref py py-data docutils literal notranslate"><span class="pre">HAVE_DOCSTRINGS</span></code></a> variable.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.HAVE_DOCSTRINGS">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">HAVE_DOCSTRINGS</span></span><a class="headerlink" href="#test.support.HAVE_DOCSTRINGS" title="Link to this definition">¶</a></dt>
<dd><p>Set to <code class="docutils literal notranslate"><span class="pre">True</span></code> if function docstrings are available.
See the <a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">python</span> <span class="pre">-OO</span></code></a> option, which strips docstrings of functions implemented in Python.</p>
<p>See also the <a class="reference internal" href="#test.support.MISSING_C_DOCSTRINGS" title="test.support.MISSING_C_DOCSTRINGS"><code class="xref py py-data docutils literal notranslate"><span class="pre">MISSING_C_DOCSTRINGS</span></code></a> variable.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.TEST_HTTP_URL">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">TEST_HTTP_URL</span></span><a class="headerlink" href="#test.support.TEST_HTTP_URL" title="Link to this definition">¶</a></dt>
<dd><p>Define the URL of a dedicated HTTP server for the network tests.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.ALWAYS_EQ">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">ALWAYS_EQ</span></span><a class="headerlink" href="#test.support.ALWAYS_EQ" title="Link to this definition">¶</a></dt>
<dd><p>Object that is equal to anything.  Used to test mixed type comparison.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.NEVER_EQ">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">NEVER_EQ</span></span><a class="headerlink" href="#test.support.NEVER_EQ" title="Link to this definition">¶</a></dt>
<dd><p>Object that is not equal to anything (even to <a class="reference internal" href="#test.support.ALWAYS_EQ" title="test.support.ALWAYS_EQ"><code class="xref py py-data docutils literal notranslate"><span class="pre">ALWAYS_EQ</span></code></a>).
Used to test mixed type comparison.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.LARGEST">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">LARGEST</span></span><a class="headerlink" href="#test.support.LARGEST" title="Link to this definition">¶</a></dt>
<dd><p>Object that is greater than anything (except itself).
Used to test mixed type comparison.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.SMALLEST">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">SMALLEST</span></span><a class="headerlink" href="#test.support.SMALLEST" title="Link to this definition">¶</a></dt>
<dd><p>Object that is less than anything (except itself).
Used to test mixed type comparison.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="test.support.busy_retry">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">busy_retry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">err_msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.busy_retry" title="Link to this definition">¶</a></dt>
<dd><p>Run the loop body until <code class="docutils literal notranslate"><span class="pre">break</span></code> stops the loop.</p>
<p>After <em>timeout</em> seconds, raise an <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> if <em>error</em> is true,
or just stop the loop if <em>error</em> is false.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">support</span><span class="o">.</span><span class="n">busy_retry</span><span class="p">(</span><span class="n">support</span><span class="o">.</span><span class="n">SHORT_TIMEOUT</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">check</span><span class="p">():</span>
        <span class="k">break</span>
</pre></div>
</div>
<p>Example of error=False usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">support</span><span class="o">.</span><span class="n">busy_retry</span><span class="p">(</span><span class="n">support</span><span class="o">.</span><span class="n">SHORT_TIMEOUT</span><span class="p">,</span> <span class="n">error</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">check</span><span class="p">():</span>
        <span class="k">break</span>
<span class="k">else</span><span class="p">:</span>
    <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s1">&#39;my custom error&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.sleeping_retry">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">sleeping_retry</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">err_msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">init_delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.010</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.sleeping_retry" title="Link to this definition">¶</a></dt>
<dd><p>Wait strategy that applies exponential backoff.</p>
<p>Run the loop body until <code class="docutils literal notranslate"><span class="pre">break</span></code> stops the loop. Sleep at each loop
iteration, but not at the first iteration. The sleep delay is doubled at
each iteration (up to <em>max_delay</em> seconds).</p>
<p>See <a class="reference internal" href="#test.support.busy_retry" title="test.support.busy_retry"><code class="xref py py-func docutils literal notranslate"><span class="pre">busy_retry()</span></code></a> documentation for the parameters usage.</p>
<p>Example raising an exception after SHORT_TIMEOUT seconds:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">support</span><span class="o">.</span><span class="n">sleeping_retry</span><span class="p">(</span><span class="n">support</span><span class="o">.</span><span class="n">SHORT_TIMEOUT</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">check</span><span class="p">():</span>
        <span class="k">break</span>
</pre></div>
</div>
<p>Example of error=False usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">support</span><span class="o">.</span><span class="n">sleeping_retry</span><span class="p">(</span><span class="n">support</span><span class="o">.</span><span class="n">SHORT_TIMEOUT</span><span class="p">,</span> <span class="n">error</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">check</span><span class="p">():</span>
        <span class="k">break</span>
<span class="k">else</span><span class="p">:</span>
    <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s1">&#39;my custom error&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.is_resource_enabled">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">is_resource_enabled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.is_resource_enabled" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>resource</em> is enabled and available. The list of
available resources is only set when <a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a> is executing the
tests.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.python_is_optimized">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">python_is_optimized</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.python_is_optimized" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if Python was not built with <code class="docutils literal notranslate"><span class="pre">-O0</span></code> or <code class="docutils literal notranslate"><span class="pre">-Og</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.with_pymalloc">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">with_pymalloc</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.with_pymalloc" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="xref py py-const docutils literal notranslate"><span class="pre">_testcapi.WITH_PYMALLOC</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.requires" title="Link to this definition">¶</a></dt>
<dd><p>Raise <a class="reference internal" href="#test.support.ResourceDenied" title="test.support.ResourceDenied"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceDenied</span></code></a> if <em>resource</em> is not available. <em>msg</em> is the
argument to <a class="reference internal" href="#test.support.ResourceDenied" title="test.support.ResourceDenied"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceDenied</span></code></a> if it is raised. Always returns
<code class="docutils literal notranslate"><span class="pre">True</span></code> if called by a function whose <code class="docutils literal notranslate"><span class="pre">__name__</span></code> is <code class="docutils literal notranslate"><span class="pre">'__main__'</span></code>.
Used when tests are executed by <a class="reference internal" href="#module-test.regrtest" title="test.regrtest: Drives the regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.regrtest</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.sortdict">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">sortdict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">dict</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.sortdict" title="Link to this definition">¶</a></dt>
<dd><p>Return a repr of <em>dict</em> with keys sorted.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.findfile">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">findfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subdir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.findfile" title="Link to this definition">¶</a></dt>
<dd><p>Return the path to the file named <em>filename</em>. If no match is found
<em>filename</em> is returned. This does not equal a failure since it could be the
path to the file.</p>
<p>Setting <em>subdir</em> indicates a relative path to use to find the file
rather than looking directly in the path directories.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.get_pagesize">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">get_pagesize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.get_pagesize" title="Link to this definition">¶</a></dt>
<dd><p>Get size of a page in bytes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.setswitchinterval">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">setswitchinterval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">interval</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.setswitchinterval" title="Link to this definition">¶</a></dt>
<dd><p>Set the <a class="reference internal" href="sys.html#sys.setswitchinterval" title="sys.setswitchinterval"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.setswitchinterval()</span></code></a> to the given <em>interval</em>.  Defines
a minimum interval for Android systems to prevent the system from hanging.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.check_impl_detail">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">check_impl_detail</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">guards</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.check_impl_detail" title="Link to this definition">¶</a></dt>
<dd><p>Use this check to guard CPython’s implementation-specific tests or to
run them only on the implementations guarded by the arguments.  This
function returns <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> depending on the host platform.
Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">check_impl_detail</span><span class="p">()</span>               <span class="c1"># Only on CPython (default).</span>
<span class="n">check_impl_detail</span><span class="p">(</span><span class="n">jython</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>    <span class="c1"># Only on Jython.</span>
<span class="n">check_impl_detail</span><span class="p">(</span><span class="n">cpython</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># Everywhere except CPython.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.set_memlimit">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">set_memlimit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">limit</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.set_memlimit" title="Link to this definition">¶</a></dt>
<dd><p>Set the values for <a class="reference internal" href="#test.support.max_memuse" title="test.support.max_memuse"><code class="xref py py-data docutils literal notranslate"><span class="pre">max_memuse</span></code></a> and <a class="reference internal" href="#test.support.real_max_memuse" title="test.support.real_max_memuse"><code class="xref py py-data docutils literal notranslate"><span class="pre">real_max_memuse</span></code></a> for big
memory tests.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.record_original_stdout">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">record_original_stdout</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stdout</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.record_original_stdout" title="Link to this definition">¶</a></dt>
<dd><p>Store the value from <em>stdout</em>.  It is meant to hold the stdout at the
time the regrtest began.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.get_original_stdout">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">get_original_stdout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.get_original_stdout" title="Link to this definition">¶</a></dt>
<dd><p>Return the original stdout set by <a class="reference internal" href="#test.support.record_original_stdout" title="test.support.record_original_stdout"><code class="xref py py-func docutils literal notranslate"><span class="pre">record_original_stdout()</span></code></a> or
<code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code> if it’s not set.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.args_from_interpreter_flags">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">args_from_interpreter_flags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.args_from_interpreter_flags" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of command line arguments reproducing the current settings
in <code class="docutils literal notranslate"><span class="pre">sys.flags</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.warnoptions</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.optim_args_from_interpreter_flags">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">optim_args_from_interpreter_flags</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.optim_args_from_interpreter_flags" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of command line arguments reproducing the current
optimization settings in <code class="docutils literal notranslate"><span class="pre">sys.flags</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.captured_stdin">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">captured_stdin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.captured_stdin" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="test.support.captured_stdout">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">captured_stdout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.captured_stdout" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="test.support.captured_stderr">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">captured_stderr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.captured_stderr" title="Link to this definition">¶</a></dt>
<dd><p>A context managers that temporarily replaces the named stream with
<a class="reference internal" href="io.html#io.StringIO" title="io.StringIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.StringIO</span></code></a> object.</p>
<p>Example use with output streams:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">captured_stdout</span><span class="p">()</span> <span class="k">as</span> <span class="n">stdout</span><span class="p">,</span> <span class="n">captured_stderr</span><span class="p">()</span> <span class="k">as</span> <span class="n">stderr</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;hello&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;error&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">sys</span><span class="o">.</span><span class="n">stderr</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">stdout</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span> <span class="o">==</span> <span class="s2">&quot;hello</span><span class="se">\n</span><span class="s2">&quot;</span>
<span class="k">assert</span> <span class="n">stderr</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span> <span class="o">==</span> <span class="s2">&quot;error</span><span class="se">\n</span><span class="s2">&quot;</span>
</pre></div>
</div>
<p>Example use with input stream:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">captured_stdin</span><span class="p">()</span> <span class="k">as</span> <span class="n">stdin</span><span class="p">:</span>
    <span class="n">stdin</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;hello</span><span class="se">\n</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">stdin</span><span class="o">.</span><span class="n">seek</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="c1"># call test code that consumes from sys.stdin</span>
    <span class="n">captured</span> <span class="o">=</span> <span class="nb">input</span><span class="p">()</span>
<span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">captured</span><span class="p">,</span> <span class="s2">&quot;hello&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.disable_faulthandler">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">disable_faulthandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.disable_faulthandler" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that temporary disables <a class="reference internal" href="faulthandler.html#module-faulthandler" title="faulthandler: Dump the Python traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.gc_collect">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">gc_collect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.gc_collect" title="Link to this definition">¶</a></dt>
<dd><p>Force as many objects as possible to be collected.  This is needed because
timely deallocation is not guaranteed by the garbage collector.  This means
that <code class="docutils literal notranslate"><span class="pre">__del__</span></code> methods may be called later than expected and weakrefs
may remain alive for longer than expected.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.disable_gc">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">disable_gc</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.disable_gc" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that disables the garbage collector on entry. On
exit, the garbage collector is restored to its prior state.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.swap_attr">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">swap_attr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.swap_attr" title="Link to this definition">¶</a></dt>
<dd><p>Context manager to swap out an attribute with a new object.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">swap_attr</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="s2">&quot;attr&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>This will set <code class="docutils literal notranslate"><span class="pre">obj.attr</span></code> to 5 for the duration of the <code class="docutils literal notranslate"><span class="pre">with</span></code> block,
restoring the old value at the end of the block.  If <code class="docutils literal notranslate"><span class="pre">attr</span></code> doesn’t
exist on <code class="docutils literal notranslate"><span class="pre">obj</span></code>, it will be created and then deleted at the end of the
block.</p>
<p>The old value (or <code class="docutils literal notranslate"><span class="pre">None</span></code> if it doesn’t exist) will be assigned to the
target of the “as” clause, if there is one.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.swap_item">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">swap_item</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attr</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.swap_item" title="Link to this definition">¶</a></dt>
<dd><p>Context manager to swap out an item with a new object.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">swap_item</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="s2">&quot;item&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">):</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>This will set <code class="docutils literal notranslate"><span class="pre">obj[&quot;item&quot;]</span></code> to 5 for the duration of the <code class="docutils literal notranslate"><span class="pre">with</span></code> block,
restoring the old value at the end of the block. If <code class="docutils literal notranslate"><span class="pre">item</span></code> doesn’t
exist on <code class="docutils literal notranslate"><span class="pre">obj</span></code>, it will be created and then deleted at the end of the
block.</p>
<p>The old value (or <code class="docutils literal notranslate"><span class="pre">None</span></code> if it doesn’t exist) will be assigned to the
target of the “as” clause, if there is one.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.flush_std_streams">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">flush_std_streams</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.flush_std_streams" title="Link to this definition">¶</a></dt>
<dd><p>Call the <code class="docutils literal notranslate"><span class="pre">flush()</span></code> method on <a class="reference internal" href="sys.html#sys.stdout" title="sys.stdout"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdout</span></code></a> and then on
<a class="reference internal" href="sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a>. It can be used to make sure that the logs order is
consistent before writing into stderr.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.print_warning">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">print_warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.print_warning" title="Link to this definition">¶</a></dt>
<dd><p>Print a warning into <a class="reference internal" href="sys.html#sys.__stderr__" title="sys.__stderr__"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.__stderr__</span></code></a>. Format the message as:
<code class="docutils literal notranslate"><span class="pre">f&quot;Warning</span> <span class="pre">--</span> <span class="pre">{msg}&quot;</span></code>. If <em>msg</em> is made of multiple lines, add
<code class="docutils literal notranslate"><span class="pre">&quot;Warning</span> <span class="pre">--</span> <span class="pre">&quot;</span></code> prefix to each line.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.wait_process">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">wait_process</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pid</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exitcode</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.wait_process" title="Link to this definition">¶</a></dt>
<dd><p>Wait until process <em>pid</em> completes and check that the process exit code is
<em>exitcode</em>.</p>
<p>Raise an <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> if the process exit code is not equal to
<em>exitcode</em>.</p>
<p>If the process runs longer than <em>timeout</em> seconds (<a class="reference internal" href="#test.support.SHORT_TIMEOUT" title="test.support.SHORT_TIMEOUT"><code class="xref py py-data docutils literal notranslate"><span class="pre">SHORT_TIMEOUT</span></code></a> by
default), kill the process and raise an <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a>. The timeout
feature is not available on Windows.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.calcobjsize">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">calcobjsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.calcobjsize" title="Link to this definition">¶</a></dt>
<dd><p>Return the size of the <a class="reference internal" href="../c-api/structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> whose structure members are
defined by <em>fmt</em>. The returned value includes the size of the Python object header and alignment.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.calcvobjsize">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">calcvobjsize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fmt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.calcvobjsize" title="Link to this definition">¶</a></dt>
<dd><p>Return the size of the <a class="reference internal" href="../c-api/structures.html#c.PyVarObject" title="PyVarObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyVarObject</span></code></a> whose structure members are
defined by <em>fmt</em>. The returned value includes the size of the Python object header and alignment.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.checksizeof">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">checksizeof</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">o</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.checksizeof" title="Link to this definition">¶</a></dt>
<dd><p>For testcase <em>test</em>, assert that the <code class="docutils literal notranslate"><span class="pre">sys.getsizeof</span></code> for <em>o</em> plus the GC
header size equals <em>size</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.anticipate_failure">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">anticipate_failure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">condition</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.anticipate_failure" title="Link to this definition">¶</a></dt>
<dd><p>A decorator to conditionally mark tests with
<a class="reference internal" href="unittest.html#unittest.expectedFailure" title="unittest.expectedFailure"><code class="xref py py-func docutils literal notranslate"><span class="pre">unittest.expectedFailure()</span></code></a>. Any use of this decorator should
have an associated comment identifying the relevant tracker issue.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.system_must_validate_cert">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">system_must_validate_cert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">f</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.system_must_validate_cert" title="Link to this definition">¶</a></dt>
<dd><p>A decorator that skips the decorated test on TLS certification validation failures.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.run_with_locale">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">run_with_locale</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">catstr</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">locales</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.run_with_locale" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for running a function in a different locale, correctly
resetting it after it has finished.  <em>catstr</em> is the locale category as
a string (for example <code class="docutils literal notranslate"><span class="pre">&quot;LC_ALL&quot;</span></code>).  The <em>locales</em> passed will be tried
sequentially, and the first valid locale will be used.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.run_with_tz">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">run_with_tz</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tz</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.run_with_tz" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for running a function in a specific timezone, correctly
resetting it after it has finished.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_freebsd_version">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_freebsd_version</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">min_version</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.requires_freebsd_version" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for the minimum version when running test on FreeBSD.  If the
FreeBSD version is less than the minimum, the test is skipped.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_linux_version">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_linux_version</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">min_version</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.requires_linux_version" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for the minimum version when running test on Linux.  If the
Linux version is less than the minimum, the test is skipped.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_mac_version">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_mac_version</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">min_version</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.requires_mac_version" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for the minimum version when running test on macOS.  If the
macOS version is less than the minimum, the test is skipped.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_IEEE_754">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_IEEE_754</span></span><a class="headerlink" href="#test.support.requires_IEEE_754" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests on non-IEEE 754 platforms.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_zlib">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_zlib</span></span><a class="headerlink" href="#test.support.requires_zlib" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests if <a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> doesn’t exist.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_gzip">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_gzip</span></span><a class="headerlink" href="#test.support.requires_gzip" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests if <a class="reference internal" href="gzip.html#module-gzip" title="gzip: Interfaces for gzip compression and decompression using file objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code></a> doesn’t exist.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_bz2">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_bz2</span></span><a class="headerlink" href="#test.support.requires_bz2" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests if <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> doesn’t exist.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_lzma">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_lzma</span></span><a class="headerlink" href="#test.support.requires_lzma" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests if <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> doesn’t exist.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_resource">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.requires_resource" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for skipping tests if <em>resource</em> is not available.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_docstrings">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_docstrings</span></span><a class="headerlink" href="#test.support.requires_docstrings" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for only running the test if <a class="reference internal" href="#test.support.HAVE_DOCSTRINGS" title="test.support.HAVE_DOCSTRINGS"><code class="xref py py-data docutils literal notranslate"><span class="pre">HAVE_DOCSTRINGS</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.requires_limited_api">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">requires_limited_api</span></span><a class="headerlink" href="#test.support.requires_limited_api" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for only running the test if <a class="reference internal" href="../c-api/stable.html#limited-c-api"><span class="std std-ref">Limited C API</span></a>
is available.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.cpython_only">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">cpython_only</span></span><a class="headerlink" href="#test.support.cpython_only" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for tests only applicable to CPython.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.impl_detail">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">impl_detail</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">msg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">guards</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.impl_detail" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for invoking <a class="reference internal" href="#test.support.check_impl_detail" title="test.support.check_impl_detail"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_impl_detail()</span></code></a> on <em>guards</em>.  If that
returns <code class="docutils literal notranslate"><span class="pre">False</span></code>, then uses <em>msg</em> as the reason for skipping the test.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.no_tracing">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">no_tracing</span></span><a class="headerlink" href="#test.support.no_tracing" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to temporarily turn off tracing for the duration of the test.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.refcount_test">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">refcount_test</span></span><a class="headerlink" href="#test.support.refcount_test" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for tests which involve reference counting.  The decorator does
not run the test if it is not run by CPython.  Any trace function is unset
for the duration of the test to prevent unexpected refcounts caused by
the trace function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.bigmemtest">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">bigmemtest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">memuse</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dry_run</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.bigmemtest" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for bigmem tests.</p>
<p><em>size</em> is a requested size for the test (in arbitrary, test-interpreted
units.)  <em>memuse</em> is the number of bytes per unit for the test, or a good
estimate of it.  For example, a test that needs two byte buffers, of 4 GiB
each, could be decorated with <code class="docutils literal notranslate"><span class="pre">&#64;bigmemtest(size=_4G,</span> <span class="pre">memuse=2)</span></code>.</p>
<p>The <em>size</em> argument is normally passed to the decorated test method as an
extra argument.  If <em>dry_run</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the value passed to the test
method may be less than the requested value.  If <em>dry_run</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, it
means the test doesn’t support dummy runs when <code class="docutils literal notranslate"><span class="pre">-M</span></code> is not specified.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.bigaddrspacetest">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">bigaddrspacetest</span></span><a class="headerlink" href="#test.support.bigaddrspacetest" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for tests that fill the address space.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.check_syntax_error">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">check_syntax_error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testcase</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">statement</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errtext</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.check_syntax_error" title="Link to this definition">¶</a></dt>
<dd><p>Test for syntax errors in <em>statement</em> by attempting to compile <em>statement</em>.
<em>testcase</em> is the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> instance for the test.  <em>errtext</em> is the
regular expression which should match the string representation of the
raised <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a>.  If <em>lineno</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, compares to
the line of the exception.  If <em>offset</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, compares to
the offset of the exception.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.open_urlresource">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">open_urlresource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.open_urlresource" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em>.  If open fails, raises <a class="reference internal" href="#test.support.TestFailed" title="test.support.TestFailed"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TestFailed</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.reap_children">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">reap_children</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.reap_children" title="Link to this definition">¶</a></dt>
<dd><p>Use this at the end of <code class="docutils literal notranslate"><span class="pre">test_main</span></code> whenever sub-processes are started.
This will help ensure that no extra children (zombies) stick around to
hog resources and create problems when looking for refleaks.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.get_attribute">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">get_attribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.get_attribute" title="Link to this definition">¶</a></dt>
<dd><p>Get an attribute, raising <a class="reference internal" href="unittest.html#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">unittest.SkipTest</span></code></a> if <a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a>
is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.catch_unraisable_exception">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">catch_unraisable_exception</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.catch_unraisable_exception" title="Link to this definition">¶</a></dt>
<dd><p>Context manager catching unraisable exception using
<a class="reference internal" href="sys.html#sys.unraisablehook" title="sys.unraisablehook"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.unraisablehook()</span></code></a>.</p>
<p>Storing the exception value (<code class="docutils literal notranslate"><span class="pre">cm.unraisable.exc_value</span></code>) creates a
reference cycle. The reference cycle is broken explicitly when the context
manager exits.</p>
<p>Storing the object (<code class="docutils literal notranslate"><span class="pre">cm.unraisable.object</span></code>) can resurrect it if it is set
to an object which is being finalized. Exiting the context manager clears
the stored object.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">support</span><span class="o">.</span><span class="n">catch_unraisable_exception</span><span class="p">()</span> <span class="k">as</span> <span class="n">cm</span><span class="p">:</span>
    <span class="c1"># code creating an &quot;unraisable exception&quot;</span>
    <span class="o">...</span>

    <span class="c1"># check the unraisable exception: use cm.unraisable</span>
    <span class="o">...</span>

<span class="c1"># cm.unraisable attribute no longer exists at this point</span>
<span class="c1"># (to break a reference cycle)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.load_package_tests">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">load_package_tests</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pkg_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">standard_tests</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.load_package_tests" title="Link to this definition">¶</a></dt>
<dd><p>Generic implementation of the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> <code class="docutils literal notranslate"><span class="pre">load_tests</span></code> protocol for
use in test packages.  <em>pkg_dir</em> is the root directory of the package;
<em>loader</em>, <em>standard_tests</em>, and <em>pattern</em> are the arguments expected by
<code class="docutils literal notranslate"><span class="pre">load_tests</span></code>.  In simple cases, the test package’s <code class="docutils literal notranslate"><span class="pre">__init__.py</span></code>
can be the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span>
<span class="kn">from</span> <span class="nn">test.support</span> <span class="kn">import</span> <span class="n">load_package_tests</span>

<span class="k">def</span> <span class="nf">load_tests</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">load_package_tests</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="vm">__file__</span><span class="p">),</span> <span class="o">*</span><span class="n">args</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.detect_api_mismatch">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">detect_api_mismatch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ref_api</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">other_api</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.detect_api_mismatch" title="Link to this definition">¶</a></dt>
<dd><p>Returns the set of attributes, functions or methods of <em>ref_api</em> not
found on <em>other_api</em>, except for a defined list of items to be
ignored in this check specified in <em>ignore</em>.</p>
<p>By default this skips private attributes beginning with ‘_’ but
includes all magic methods, i.e. those starting and ending in ‘__’.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.patch">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">patch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test_instance</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">object_to_patch</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attr_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.patch" title="Link to this definition">¶</a></dt>
<dd><p>Override <em>object_to_patch.attr_name</em> with <em>new_value</em>.  Also add
cleanup procedure to <em>test_instance</em> to restore <em>object_to_patch</em> for
<em>attr_name</em>.  The <em>attr_name</em> should be a valid attribute for
<em>object_to_patch</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.run_in_subinterp">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">run_in_subinterp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.run_in_subinterp" title="Link to this definition">¶</a></dt>
<dd><p>Run <em>code</em> in subinterpreter.  Raise <a class="reference internal" href="unittest.html#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">unittest.SkipTest</span></code></a> if
<a class="reference internal" href="tracemalloc.html#module-tracemalloc" title="tracemalloc: Trace memory allocations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tracemalloc</span></code></a> is enabled.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.check_free_after_iterating">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">check_free_after_iterating</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">iter</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.check_free_after_iterating" title="Link to this definition">¶</a></dt>
<dd><p>Assert instances of <em>cls</em> are deallocated after iterating.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.missing_compiler_executable">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">missing_compiler_executable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmd_names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">[]</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.missing_compiler_executable" title="Link to this definition">¶</a></dt>
<dd><p>Check for the existence of the compiler executables whose names are listed
in <em>cmd_names</em> or all the compiler executables when <em>cmd_names</em> is empty
and return the first missing executable or <code class="docutils literal notranslate"><span class="pre">None</span></code> when none is found
missing.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.check__all__">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">check__all__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test_case</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name_of_module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extra</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">not_exported</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.check__all__" title="Link to this definition">¶</a></dt>
<dd><p>Assert that the <code class="docutils literal notranslate"><span class="pre">__all__</span></code> variable of <em>module</em> contains all public names.</p>
<p>The module’s public names (its API) are detected automatically
based on whether they match the public name convention and were defined in
<em>module</em>.</p>
<p>The <em>name_of_module</em> argument can specify (as a string or tuple thereof) what
module(s) an API could be defined in order to be detected as a public
API. One case for this is when <em>module</em> imports part of its public API from
other modules, possibly a C backend (like <code class="docutils literal notranslate"><span class="pre">csv</span></code> and its <code class="docutils literal notranslate"><span class="pre">_csv</span></code>).</p>
<p>The <em>extra</em> argument can be a set of names that wouldn’t otherwise be automatically
detected as “public”, like objects without a proper <code class="docutils literal notranslate"><span class="pre">__module__</span></code>
attribute. If provided, it will be added to the automatically detected ones.</p>
<p>The <em>not_exported</em> argument can be a set of names that must not be treated
as part of the public API even though their names indicate otherwise.</p>
<p>Example use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">bar</span>
<span class="kn">import</span> <span class="nn">foo</span>
<span class="kn">import</span> <span class="nn">unittest</span>
<span class="kn">from</span> <span class="nn">test</span> <span class="kn">import</span> <span class="n">support</span>

<span class="k">class</span> <span class="nc">MiscTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">test__all__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">support</span><span class="o">.</span><span class="n">check__all__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">foo</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">OtherTestCase</span><span class="p">(</span><span class="n">unittest</span><span class="o">.</span><span class="n">TestCase</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">test__all__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">extra</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;BAR_CONST&#39;</span><span class="p">,</span> <span class="s1">&#39;FOO_CONST&#39;</span><span class="p">}</span>
        <span class="n">not_exported</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;baz&#39;</span><span class="p">}</span>  <span class="c1"># Undocumented name.</span>
        <span class="c1"># bar imports part of its API from _bar.</span>
        <span class="n">support</span><span class="o">.</span><span class="n">check__all__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">bar</span><span class="p">,</span> <span class="p">(</span><span class="s1">&#39;bar&#39;</span><span class="p">,</span> <span class="s1">&#39;_bar&#39;</span><span class="p">),</span>
                             <span class="n">extra</span><span class="o">=</span><span class="n">extra</span><span class="p">,</span> <span class="n">not_exported</span><span class="o">=</span><span class="n">not_exported</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.skip_if_broken_multiprocessing_synchronize">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">skip_if_broken_multiprocessing_synchronize</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.skip_if_broken_multiprocessing_synchronize" title="Link to this definition">¶</a></dt>
<dd><p>Skip tests if the <code class="xref py py-mod docutils literal notranslate"><span class="pre">multiprocessing.synchronize</span></code> module is missing, if
there is no available semaphore implementation, or if creating a lock raises
an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.check_disallow_instantiation">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">check_disallow_instantiation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">test_case</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tp</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.check_disallow_instantiation" title="Link to this definition">¶</a></dt>
<dd><p>Assert that type <em>tp</em> cannot be instantiated using <em>args</em> and <em>kwds</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.adjust_int_max_str_digits">
<span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">adjust_int_max_str_digits</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">max_digits</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.adjust_int_max_str_digits" title="Link to this definition">¶</a></dt>
<dd><p>This function returns a context manager that will change the global
<a class="reference internal" href="sys.html#sys.set_int_max_str_digits" title="sys.set_int_max_str_digits"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.set_int_max_str_digits()</span></code></a> setting for the duration of the
context to allow execution of test code that needs a different limit
on the number of digits when converting between an integer and string.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#module-test.support" title="test.support: Support for Python's regression test suite."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code></a> module defines the following classes:</p>
<dl class="py class">
<dt class="sig sig-object py" id="test.support.SuppressCrashReport">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">SuppressCrashReport</span></span><a class="headerlink" href="#test.support.SuppressCrashReport" title="Link to this definition">¶</a></dt>
<dd><p>A context manager used to try to prevent crash dialog popups on tests that
are expected to crash a subprocess.</p>
<p>On Windows, it disables Windows Error Reporting dialogs using
<a class="reference external" href="https://msdn.microsoft.com/en-us/library/windows/desktop/ms680621.aspx">SetErrorMode</a>.</p>
<p>On UNIX, <a class="reference internal" href="resource.html#resource.setrlimit" title="resource.setrlimit"><code class="xref py py-func docutils literal notranslate"><span class="pre">resource.setrlimit()</span></code></a> is used to set
<a class="reference internal" href="resource.html#resource.RLIMIT_CORE" title="resource.RLIMIT_CORE"><code class="xref py py-const docutils literal notranslate"><span class="pre">resource.RLIMIT_CORE</span></code></a>’s soft limit to 0 to prevent coredump file
creation.</p>
<p>On both platforms, the old value is restored by <a class="reference internal" href="../reference/datamodel.html#object.__exit__" title="object.__exit__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__exit__()</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.SaveSignals">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">SaveSignals</span></span><a class="headerlink" href="#test.support.SaveSignals" title="Link to this definition">¶</a></dt>
<dd><p>Class to save and restore signal handlers registered by the Python signal
handler.</p>
<dl class="py method">
<dt class="sig sig-object py" id="test.support.SaveSignals.save">
<span class="sig-name descname"><span class="pre">save</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.SaveSignals.save" title="Link to this definition">¶</a></dt>
<dd><p>Save the signal handlers to a dictionary mapping signal numbers to the
current signal handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.SaveSignals.restore">
<span class="sig-name descname"><span class="pre">restore</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.SaveSignals.restore" title="Link to this definition">¶</a></dt>
<dd><p>Set the signal numbers from the <a class="reference internal" href="#test.support.SaveSignals.save" title="test.support.SaveSignals.save"><code class="xref py py-meth docutils literal notranslate"><span class="pre">save()</span></code></a> dictionary to the saved
handler.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.Matcher">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.</span></span><span class="sig-name descname"><span class="pre">Matcher</span></span><a class="headerlink" href="#test.support.Matcher" title="Link to this definition">¶</a></dt>
<dd><dl class="py method">
<dt class="sig sig-object py" id="test.support.Matcher.matches">
<span class="sig-name descname"><span class="pre">matches</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">d</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.Matcher.matches" title="Link to this definition">¶</a></dt>
<dd><p>Try to match a single dict with the supplied arguments.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.Matcher.match_value">
<span class="sig-name descname"><span class="pre">match_value</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dv</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">v</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.Matcher.match_value" title="Link to this definition">¶</a></dt>
<dd><p>Try to match a single stored value (<em>dv</em>) with a supplied value (<em>v</em>).</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-test.support.socket_helper">
<span id="test-support-socket-helper-utilities-for-socket-tests"></span><h1><a class="reference internal" href="#module-test.support.socket_helper" title="test.support.socket_helper: Support for socket tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.socket_helper</span></code></a> — Utilities for socket tests<a class="headerlink" href="#module-test.support.socket_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.socket_helper" title="test.support.socket_helper: Support for socket tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.socket_helper</span></code></a> module provides support for socket tests.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<dl class="py data">
<dt class="sig sig-object py" id="test.support.socket_helper.IPV6_ENABLED">
<span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">IPV6_ENABLED</span></span><a class="headerlink" href="#test.support.socket_helper.IPV6_ENABLED" title="Link to this definition">¶</a></dt>
<dd><p>Set to <code class="docutils literal notranslate"><span class="pre">True</span></code> if IPv6 is enabled on this host, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.socket_helper.find_unused_port">
<span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">find_unused_port</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">family</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">socket.AF_INET</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">socktype</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">socket.SOCK_STREAM</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.socket_helper.find_unused_port" title="Link to this definition">¶</a></dt>
<dd><p>Returns an unused port that should be suitable for binding.  This is
achieved by creating a temporary socket with the same family and type as
the <code class="docutils literal notranslate"><span class="pre">sock</span></code> parameter (default is <a class="reference internal" href="socket.html#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a>,
<a class="reference internal" href="socket.html#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a>),
and binding it to the specified host address (defaults to <code class="docutils literal notranslate"><span class="pre">0.0.0.0</span></code>)
with the port set to 0, eliciting an unused ephemeral port from the OS.
The temporary socket is then closed and deleted, and the ephemeral port is
returned.</p>
<p>Either this method or <a class="reference internal" href="#test.support.socket_helper.bind_port" title="test.support.socket_helper.bind_port"><code class="xref py py-func docutils literal notranslate"><span class="pre">bind_port()</span></code></a> should be used for any tests
where a server socket needs to be bound to a particular port for the
duration of the test.
Which one to use depends on whether the calling code is creating a Python
socket, or if an unused port needs to be provided in a constructor
or passed to an external program (i.e. the <code class="docutils literal notranslate"><span class="pre">-accept</span></code> argument to
openssl’s s_server mode).  Always prefer <a class="reference internal" href="#test.support.socket_helper.bind_port" title="test.support.socket_helper.bind_port"><code class="xref py py-func docutils literal notranslate"><span class="pre">bind_port()</span></code></a> over
<a class="reference internal" href="#test.support.socket_helper.find_unused_port" title="test.support.socket_helper.find_unused_port"><code class="xref py py-func docutils literal notranslate"><span class="pre">find_unused_port()</span></code></a> where possible.  Using a hard coded port is
discouraged since it can make multiple instances of the test impossible to
run simultaneously, which is a problem for buildbots.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.socket_helper.bind_port">
<span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">bind_port</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">HOST</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.socket_helper.bind_port" title="Link to this definition">¶</a></dt>
<dd><p>Bind the socket to a free port and return the port number.  Relies on
ephemeral ports in order to ensure we are using an unbound port.  This is
important as many tests may be running simultaneously, especially in a
buildbot environment.  This method raises an exception if the
<code class="docutils literal notranslate"><span class="pre">sock.family</span></code> is <a class="reference internal" href="socket.html#socket.AF_INET" title="socket.AF_INET"><code class="xref py py-const docutils literal notranslate"><span class="pre">AF_INET</span></code></a> and <code class="docutils literal notranslate"><span class="pre">sock.type</span></code> is
<a class="reference internal" href="socket.html#socket.SOCK_STREAM" title="socket.SOCK_STREAM"><code class="xref py py-const docutils literal notranslate"><span class="pre">SOCK_STREAM</span></code></a>, and the socket has
<code class="xref py py-const docutils literal notranslate"><span class="pre">SO_REUSEADDR</span></code> or <code class="xref py py-const docutils literal notranslate"><span class="pre">SO_REUSEPORT</span></code> set on it.
Tests should never set these socket options for TCP/IP sockets.
The only case for setting these options is testing multicasting via
multiple UDP sockets.</p>
<p>Additionally, if the <code class="xref py py-const docutils literal notranslate"><span class="pre">SO_EXCLUSIVEADDRUSE</span></code> socket option is
available (i.e. on Windows), it will be set on the socket.  This will
prevent anyone else from binding to our host/port for the duration of the
test.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.socket_helper.bind_unix_socket">
<span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">bind_unix_socket</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sock</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">addr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.socket_helper.bind_unix_socket" title="Link to this definition">¶</a></dt>
<dd><p>Bind a Unix socket, raising <a class="reference internal" href="unittest.html#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">unittest.SkipTest</span></code></a> if
<a class="reference internal" href="exceptions.html#PermissionError" title="PermissionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PermissionError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.socket_helper.skip_unless_bind_unix_socket">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">skip_unless_bind_unix_socket</span></span><a class="headerlink" href="#test.support.socket_helper.skip_unless_bind_unix_socket" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for running tests that require a functional <code class="docutils literal notranslate"><span class="pre">bind()</span></code> for Unix
sockets.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.socket_helper.transient_internet">
<span class="sig-prename descclassname"><span class="pre">test.support.socket_helper.</span></span><span class="sig-name descname"><span class="pre">transient_internet</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource_name</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">30.0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errnos</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.socket_helper.transient_internet" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that raises <a class="reference internal" href="#test.support.ResourceDenied" title="test.support.ResourceDenied"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceDenied</span></code></a> when
various issues with the internet connection manifest themselves as
exceptions.</p>
</dd></dl>

</section>
<section id="module-test.support.script_helper">
<span id="test-support-script-helper-utilities-for-the-python-execution-tests"></span><h1><a class="reference internal" href="#module-test.support.script_helper" title="test.support.script_helper: Support for Python's script execution tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.script_helper</span></code></a> — Utilities for the Python execution tests<a class="headerlink" href="#module-test.support.script_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.script_helper" title="test.support.script_helper: Support for Python's script execution tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.script_helper</span></code></a> module provides support for Python’s
script execution tests.</p>
<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.interpreter_requires_environment">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">interpreter_requires_environment</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.interpreter_requires_environment" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <code class="docutils literal notranslate"><span class="pre">sys.executable</span> <span class="pre">interpreter</span></code> requires environment
variables in order to be able to run at all.</p>
<p>This is designed to be used with <code class="docutils literal notranslate"><span class="pre">&#64;unittest.skipIf()</span></code> to annotate tests
that need to use an <code class="docutils literal notranslate"><span class="pre">assert_python*()</span></code> function to launch an isolated
mode (<code class="docutils literal notranslate"><span class="pre">-I</span></code>) or no environment mode (<code class="docutils literal notranslate"><span class="pre">-E</span></code>) sub-interpreter process.</p>
<p>A normal build &amp; test does not run into this situation but it can happen
when trying to run the standard library test suite from an interpreter that
doesn’t have an obvious home with Python’s current home finding logic.</p>
<p>Setting <span class="target" id="index-0"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a> is one way to get most of the testsuite to run
in that situation.  <span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a> or <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUSERSITE</span></code> are
other common environment variables that might impact whether or not the
interpreter can start.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.run_python_until_end">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">run_python_until_end</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">env_vars</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.run_python_until_end" title="Link to this definition">¶</a></dt>
<dd><p>Set up the environment based on <em>env_vars</em> for running the interpreter
in a subprocess.  The values can include <code class="docutils literal notranslate"><span class="pre">__isolated</span></code>, <code class="docutils literal notranslate"><span class="pre">__cleanenv</span></code>,
<code class="docutils literal notranslate"><span class="pre">__cwd</span></code>, and <code class="docutils literal notranslate"><span class="pre">TERM</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The function no longer strips whitespaces from <em>stderr</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.assert_python_ok">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">assert_python_ok</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">env_vars</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.assert_python_ok" title="Link to this definition">¶</a></dt>
<dd><p>Assert that running the interpreter with <em>args</em> and optional environment
variables <em>env_vars</em> succeeds (<code class="docutils literal notranslate"><span class="pre">rc</span> <span class="pre">==</span> <span class="pre">0</span></code>) and return a <code class="docutils literal notranslate"><span class="pre">(return</span> <span class="pre">code,</span>
<span class="pre">stdout,</span> <span class="pre">stderr)</span></code> tuple.</p>
<p>If the <em>__cleanenv</em> keyword-only parameter is set, <em>env_vars</em> is used as a fresh
environment.</p>
<p>Python is started in isolated mode (command line option <code class="docutils literal notranslate"><span class="pre">-I</span></code>),
except if the <em>__isolated</em> keyword-only parameter is set to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The function no longer strips whitespaces from <em>stderr</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.assert_python_failure">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">assert_python_failure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">env_vars</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.assert_python_failure" title="Link to this definition">¶</a></dt>
<dd><p>Assert that running the interpreter with <em>args</em> and optional environment
variables <em>env_vars</em> fails (<code class="docutils literal notranslate"><span class="pre">rc</span> <span class="pre">!=</span> <span class="pre">0</span></code>) and return a <code class="docutils literal notranslate"><span class="pre">(return</span> <span class="pre">code,</span>
<span class="pre">stdout,</span> <span class="pre">stderr)</span></code> tuple.</p>
<p>See <a class="reference internal" href="#test.support.script_helper.assert_python_ok" title="test.support.script_helper.assert_python_ok"><code class="xref py py-func docutils literal notranslate"><span class="pre">assert_python_ok()</span></code></a> for more options.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The function no longer strips whitespaces from <em>stderr</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.spawn_python">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">spawn_python</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">subprocess.PIPE</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stderr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">subprocess.STDOUT</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.spawn_python" title="Link to this definition">¶</a></dt>
<dd><p>Run a Python subprocess with the given arguments.</p>
<p><em>kw</em> is extra keyword args to pass to <a class="reference internal" href="subprocess.html#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-func docutils literal notranslate"><span class="pre">subprocess.Popen()</span></code></a>. Returns a
<a class="reference internal" href="subprocess.html#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">subprocess.Popen</span></code></a> object.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.kill_python">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">kill_python</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">p</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.kill_python" title="Link to this definition">¶</a></dt>
<dd><p>Run the given <a class="reference internal" href="subprocess.html#subprocess.Popen" title="subprocess.Popen"><code class="xref py py-class docutils literal notranslate"><span class="pre">subprocess.Popen</span></code></a> process until completion and return
stdout.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.make_script">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">make_script</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">script_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_basename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">omit_suffix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.make_script" title="Link to this definition">¶</a></dt>
<dd><p>Create script containing <em>source</em> in path <em>script_dir</em> and <em>script_basename</em>.
If <em>omit_suffix</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, append <code class="docutils literal notranslate"><span class="pre">.py</span></code> to the name.  Return the full
script path.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.make_zip_script">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">make_zip_script</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">zip_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zip_basename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name_in_zip</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.make_zip_script" title="Link to this definition">¶</a></dt>
<dd><p>Create zip file at <em>zip_dir</em> and <em>zip_basename</em> with extension <code class="docutils literal notranslate"><span class="pre">zip</span></code> which
contains the files in <em>script_name</em>. <em>name_in_zip</em> is the archive name.
Return a tuple containing <code class="docutils literal notranslate"><span class="pre">(full</span> <span class="pre">path,</span> <span class="pre">full</span> <span class="pre">path</span> <span class="pre">of</span> <span class="pre">archive</span> <span class="pre">name)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.make_pkg">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">make_pkg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pkg_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">init_source</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.make_pkg" title="Link to this definition">¶</a></dt>
<dd><p>Create a directory named <em>pkg_dir</em> containing an <code class="docutils literal notranslate"><span class="pre">__init__</span></code> file with
<em>init_source</em> as its contents.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.script_helper.make_zip_pkg">
<span class="sig-prename descclassname"><span class="pre">test.support.script_helper.</span></span><span class="sig-name descname"><span class="pre">make_zip_pkg</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">zip_dir</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">zip_basename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pkg_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">script_basename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compiled</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.script_helper.make_zip_pkg" title="Link to this definition">¶</a></dt>
<dd><p>Create a zip package directory with a path of <em>zip_dir</em> and <em>zip_basename</em>
containing an empty <code class="docutils literal notranslate"><span class="pre">__init__</span></code> file and a file <em>script_basename</em>
containing the <em>source</em>.  If <em>compiled</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, both source files will
be compiled and added to the zip package.  Return a tuple of the full zip
path and the archive name for the zip file.</p>
</dd></dl>

</section>
<section id="module-test.support.bytecode_helper">
<span id="test-support-bytecode-helper-support-tools-for-testing-correct-bytecode-generation"></span><h1><a class="reference internal" href="#module-test.support.bytecode_helper" title="test.support.bytecode_helper: Support tools for testing correct bytecode generation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.bytecode_helper</span></code></a> — Support tools for testing correct bytecode generation<a class="headerlink" href="#module-test.support.bytecode_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.bytecode_helper" title="test.support.bytecode_helper: Support tools for testing correct bytecode generation."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.bytecode_helper</span></code></a> module provides support for testing
and inspecting bytecode generation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>The module defines the following class:</p>
<dl class="py class">
<dt class="sig sig-object py" id="test.support.bytecode_helper.BytecodeTestCase">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.bytecode_helper.</span></span><span class="sig-name descname"><span class="pre">BytecodeTestCase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unittest.TestCase</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.bytecode_helper.BytecodeTestCase" title="Link to this definition">¶</a></dt>
<dd><p>This class has custom assertion methods for inspecting bytecode.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.bytecode_helper.BytecodeTestCase.get_disassembly_as_string">
<span class="sig-prename descclassname"><span class="pre">BytecodeTestCase.</span></span><span class="sig-name descname"><span class="pre">get_disassembly_as_string</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">co</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.bytecode_helper.BytecodeTestCase.get_disassembly_as_string" title="Link to this definition">¶</a></dt>
<dd><p>Return the disassembly of <em>co</em> as string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.bytecode_helper.BytecodeTestCase.assertInBytecode">
<span class="sig-prename descclassname"><span class="pre">BytecodeTestCase.</span></span><span class="sig-name descname"><span class="pre">assertInBytecode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_UNSPECIFIED</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.bytecode_helper.BytecodeTestCase.assertInBytecode" title="Link to this definition">¶</a></dt>
<dd><p>Return instr if <em>opname</em> is found, otherwise throws <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.bytecode_helper.BytecodeTestCase.assertNotInBytecode">
<span class="sig-prename descclassname"><span class="pre">BytecodeTestCase.</span></span><span class="sig-name descname"><span class="pre">assertNotInBytecode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">opname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">argval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_UNSPECIFIED</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.bytecode_helper.BytecodeTestCase.assertNotInBytecode" title="Link to this definition">¶</a></dt>
<dd><p>Throws <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> if <em>opname</em> is found.</p>
</dd></dl>

</section>
<section id="module-test.support.threading_helper">
<span id="test-support-threading-helper-utilities-for-threading-tests"></span><h1><a class="reference internal" href="#module-test.support.threading_helper" title="test.support.threading_helper: Support for threading tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.threading_helper</span></code></a> — Utilities for threading tests<a class="headerlink" href="#module-test.support.threading_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.threading_helper" title="test.support.threading_helper: Support for threading tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.threading_helper</span></code></a> module provides support for threading tests.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.join_thread">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">join_thread</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">thread</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.join_thread" title="Link to this definition">¶</a></dt>
<dd><p>Join a <em>thread</em> within <em>timeout</em>.  Raise an <a class="reference internal" href="exceptions.html#AssertionError" title="AssertionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AssertionError</span></code></a> if thread
is still alive after <em>timeout</em> seconds.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.reap_threads">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">reap_threads</span></span><a class="headerlink" href="#test.support.threading_helper.reap_threads" title="Link to this definition">¶</a></dt>
<dd><p>Decorator to ensure the threads are cleaned up even if the test fails.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.start_threads">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">start_threads</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">threads</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unlock</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.start_threads" title="Link to this definition">¶</a></dt>
<dd><p>Context manager to start <em>threads</em>, which is a sequence of threads.
<em>unlock</em> is a function called after the threads are started, even if an
exception was raised; an example would be <a class="reference internal" href="threading.html#threading.Event.set" title="threading.Event.set"><code class="xref py py-meth docutils literal notranslate"><span class="pre">threading.Event.set()</span></code></a>.
<code class="docutils literal notranslate"><span class="pre">start_threads</span></code> will attempt to join the started threads upon exit.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.threading_cleanup">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">threading_cleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">original_values</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.threading_cleanup" title="Link to this definition">¶</a></dt>
<dd><p>Cleanup up threads not specified in <em>original_values</em>.  Designed to emit
a warning if a test leaves running threads in the background.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.threading_setup">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">threading_setup</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.threading_setup" title="Link to this definition">¶</a></dt>
<dd><p>Return current thread count and copy of dangling threads.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.wait_threads_exit">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">wait_threads_exit</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.wait_threads_exit" title="Link to this definition">¶</a></dt>
<dd><p>Context manager to wait until all threads created in the <code class="docutils literal notranslate"><span class="pre">with</span></code> statement
exit.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.threading_helper.catch_threading_exception">
<span class="sig-prename descclassname"><span class="pre">test.support.threading_helper.</span></span><span class="sig-name descname"><span class="pre">catch_threading_exception</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.threading_helper.catch_threading_exception" title="Link to this definition">¶</a></dt>
<dd><p>Context manager catching <a class="reference internal" href="threading.html#threading.Thread" title="threading.Thread"><code class="xref py py-class docutils literal notranslate"><span class="pre">threading.Thread</span></code></a> exception using
<a class="reference internal" href="threading.html#threading.excepthook" title="threading.excepthook"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.excepthook()</span></code></a>.</p>
<p>Attributes set when an exception is caught:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">exc_type</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">exc_value</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">exc_traceback</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">thread</span></code></p></li>
</ul>
<p>See <a class="reference internal" href="threading.html#threading.excepthook" title="threading.excepthook"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.excepthook()</span></code></a> documentation.</p>
<p>These attributes are deleted at the context manager exit.</p>
<p>Usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">threading_helper</span><span class="o">.</span><span class="n">catch_threading_exception</span><span class="p">()</span> <span class="k">as</span> <span class="n">cm</span><span class="p">:</span>
    <span class="c1"># code spawning a thread which raises an exception</span>
    <span class="o">...</span>

    <span class="c1"># check the thread exception, use cm attributes:</span>
    <span class="c1"># exc_type, exc_value, exc_traceback, thread</span>
    <span class="o">...</span>

<span class="c1"># exc_type, exc_value, exc_traceback, thread attributes of cm no longer</span>
<span class="c1"># exists at this point</span>
<span class="c1"># (to avoid reference cycles)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>
<section id="module-test.support.os_helper">
<span id="test-support-os-helper-utilities-for-os-tests"></span><h1><a class="reference internal" href="#module-test.support.os_helper" title="test.support.os_helper: Support for os tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.os_helper</span></code></a> — Utilities for os tests<a class="headerlink" href="#module-test.support.os_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.os_helper" title="test.support.os_helper: Support for os tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.os_helper</span></code></a> module provides support for os tests.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.FS_NONASCII">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">FS_NONASCII</span></span><a class="headerlink" href="#test.support.os_helper.FS_NONASCII" title="Link to this definition">¶</a></dt>
<dd><p>A non-ASCII character encodable by <a class="reference internal" href="os.html#os.fsencode" title="os.fsencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsencode()</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.SAVEDCWD">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">SAVEDCWD</span></span><a class="headerlink" href="#test.support.os_helper.SAVEDCWD" title="Link to this definition">¶</a></dt>
<dd><p>Set to <a class="reference internal" href="os.html#os.getcwd" title="os.getcwd"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.getcwd()</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.TESTFN">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">TESTFN</span></span><a class="headerlink" href="#test.support.os_helper.TESTFN" title="Link to this definition">¶</a></dt>
<dd><p>Set to a name that is safe to use as the name of a temporary file.  Any
temporary file that is created should be closed and unlinked (removed).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.TESTFN_NONASCII">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">TESTFN_NONASCII</span></span><a class="headerlink" href="#test.support.os_helper.TESTFN_NONASCII" title="Link to this definition">¶</a></dt>
<dd><p>Set to a filename containing the <a class="reference internal" href="#test.support.os_helper.FS_NONASCII" title="test.support.os_helper.FS_NONASCII"><code class="xref py py-data docutils literal notranslate"><span class="pre">FS_NONASCII</span></code></a> character, if it exists.
This guarantees that if the filename exists, it can be encoded and decoded
with the default filesystem encoding. This allows tests that require a
non-ASCII filename to be easily skipped on platforms where they can’t work.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.TESTFN_UNENCODABLE">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">TESTFN_UNENCODABLE</span></span><a class="headerlink" href="#test.support.os_helper.TESTFN_UNENCODABLE" title="Link to this definition">¶</a></dt>
<dd><p>Set to a filename (str type) that should not be able to be encoded by file
system encoding in strict mode.  It may be <code class="docutils literal notranslate"><span class="pre">None</span></code> if it’s not possible to
generate such a filename.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.TESTFN_UNDECODABLE">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">TESTFN_UNDECODABLE</span></span><a class="headerlink" href="#test.support.os_helper.TESTFN_UNDECODABLE" title="Link to this definition">¶</a></dt>
<dd><p>Set to a filename (bytes type) that should not be able to be decoded by
file system encoding in strict mode.  It may be <code class="docutils literal notranslate"><span class="pre">None</span></code> if it’s not
possible to generate such a filename.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="test.support.os_helper.TESTFN_UNICODE">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">TESTFN_UNICODE</span></span><a class="headerlink" href="#test.support.os_helper.TESTFN_UNICODE" title="Link to this definition">¶</a></dt>
<dd><p>Set to a non-ASCII name for a temporary file.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.os_helper.EnvironmentVarGuard">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">EnvironmentVarGuard</span></span><a class="headerlink" href="#test.support.os_helper.EnvironmentVarGuard" title="Link to this definition">¶</a></dt>
<dd><p>Class used to temporarily set or unset environment variables.  Instances can
be used as a context manager and have a complete dictionary interface for
querying/modifying the underlying <code class="docutils literal notranslate"><span class="pre">os.environ</span></code>. After exit from the
context manager all changes to environment variables done through this
instance will be rolled back.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added dictionary interface.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.os_helper.FakePath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">FakePath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.FakePath" title="Link to this definition">¶</a></dt>
<dd><p>Simple <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.  It implements the
<a class="reference internal" href="os.html#os.PathLike.__fspath__" title="os.PathLike.__fspath__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__fspath__()</span></code></a>
method which just returns the <em>path</em> argument.  If <em>path</em> is an exception,
it will be raised in <code class="xref py py-meth docutils literal notranslate"><span class="pre">__fspath__()</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.os_helper.EnvironmentVarGuard.set">
<span class="sig-prename descclassname"><span class="pre">EnvironmentVarGuard.</span></span><span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">envvar</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.EnvironmentVarGuard.set" title="Link to this definition">¶</a></dt>
<dd><p>Temporarily set the environment variable <code class="docutils literal notranslate"><span class="pre">envvar</span></code> to the value of
<code class="docutils literal notranslate"><span class="pre">value</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="test.support.os_helper.EnvironmentVarGuard.unset">
<span class="sig-prename descclassname"><span class="pre">EnvironmentVarGuard.</span></span><span class="sig-name descname"><span class="pre">unset</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">envvar</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.EnvironmentVarGuard.unset" title="Link to this definition">¶</a></dt>
<dd><p>Temporarily unset the environment variable <code class="docutils literal notranslate"><span class="pre">envvar</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.can_symlink">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">can_symlink</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.can_symlink" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the OS supports symbolic links, <code class="docutils literal notranslate"><span class="pre">False</span></code>
otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.can_xattr">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">can_xattr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.can_xattr" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the OS supports xattr, <code class="docutils literal notranslate"><span class="pre">False</span></code>
otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.change_cwd">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">change_cwd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quiet</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.change_cwd" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that temporarily changes the current working
directory to <em>path</em> and yields the directory.</p>
<p>If <em>quiet</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the context manager raises an exception
on error.  Otherwise, it issues only a warning and keeps the current
working directory the same.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.create_empty_file">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">create_empty_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.create_empty_file" title="Link to this definition">¶</a></dt>
<dd><p>Create an empty file with <em>filename</em>.  If it already exists, truncate it.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.fd_count">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">fd_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.fd_count" title="Link to this definition">¶</a></dt>
<dd><p>Count the number of open file descriptors.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.fs_is_case_insensitive">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">fs_is_case_insensitive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">directory</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.fs_is_case_insensitive" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the file system for <em>directory</em> is case-insensitive.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.make_bad_fd">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">make_bad_fd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.make_bad_fd" title="Link to this definition">¶</a></dt>
<dd><p>Create an invalid file descriptor by opening and closing a temporary file,
and returning its descriptor.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.rmdir">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">rmdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.rmdir" title="Link to this definition">¶</a></dt>
<dd><p>Call <a class="reference internal" href="os.html#os.rmdir" title="os.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rmdir()</span></code></a> on <em>filename</em>.  On Windows platforms, this is
wrapped with a wait loop that checks for the existence of the file,
which is needed due to antivirus programs that can hold files open and prevent
deletion.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.rmtree">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">rmtree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.rmtree" title="Link to this definition">¶</a></dt>
<dd><p>Call <a class="reference internal" href="shutil.html#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.rmtree()</span></code></a> on <em>path</em> or call <a class="reference internal" href="os.html#os.lstat" title="os.lstat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.lstat()</span></code></a> and
<a class="reference internal" href="os.html#os.rmdir" title="os.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rmdir()</span></code></a> to remove a path and its contents.  As with <a class="reference internal" href="#test.support.os_helper.rmdir" title="test.support.os_helper.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmdir()</span></code></a>,
on Windows platforms
this is wrapped with a wait loop that checks for the existence of the files.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.skip_unless_symlink">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">skip_unless_symlink</span></span><a class="headerlink" href="#test.support.os_helper.skip_unless_symlink" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for running tests that require support for symbolic links.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.skip_unless_xattr">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">skip_unless_xattr</span></span><a class="headerlink" href="#test.support.os_helper.skip_unless_xattr" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for running tests that require support for xattr.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.temp_cwd">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">temp_cwd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'tempcwd'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quiet</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.temp_cwd" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that temporarily creates a new directory and
changes the current working directory (CWD).</p>
<p>The context manager creates a temporary directory in the current
directory with name <em>name</em> before temporarily changing the current
working directory.  If <em>name</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the temporary directory is
created using <a class="reference internal" href="tempfile.html#tempfile.mkdtemp" title="tempfile.mkdtemp"><code class="xref py py-func docutils literal notranslate"><span class="pre">tempfile.mkdtemp()</span></code></a>.</p>
<p>If <em>quiet</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code> and it is not possible to create or change
the CWD, an error is raised.  Otherwise, only a warning is raised
and the original CWD is used.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.temp_dir">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">temp_dir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quiet</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.temp_dir" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that creates a temporary directory at <em>path</em> and
yields the directory.</p>
<p>If <em>path</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the temporary directory is created using
<a class="reference internal" href="tempfile.html#tempfile.mkdtemp" title="tempfile.mkdtemp"><code class="xref py py-func docutils literal notranslate"><span class="pre">tempfile.mkdtemp()</span></code></a>.  If <em>quiet</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the context manager
raises an exception on error.  Otherwise, if <em>path</em> is specified and
cannot be created, only a warning is issued.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.temp_umask">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">temp_umask</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">umask</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.temp_umask" title="Link to this definition">¶</a></dt>
<dd><p>A context manager that temporarily sets the process umask.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.os_helper.unlink">
<span class="sig-prename descclassname"><span class="pre">test.support.os_helper.</span></span><span class="sig-name descname"><span class="pre">unlink</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.os_helper.unlink" title="Link to this definition">¶</a></dt>
<dd><p>Call <a class="reference internal" href="os.html#os.unlink" title="os.unlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.unlink()</span></code></a> on <em>filename</em>.  As with <a class="reference internal" href="#test.support.os_helper.rmdir" title="test.support.os_helper.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmdir()</span></code></a>,
on Windows platforms, this is
wrapped with a wait loop that checks for the existence of the file.</p>
</dd></dl>

</section>
<section id="module-test.support.import_helper">
<span id="test-support-import-helper-utilities-for-import-tests"></span><h1><a class="reference internal" href="#module-test.support.import_helper" title="test.support.import_helper: Support for import tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.import_helper</span></code></a> — Utilities for import tests<a class="headerlink" href="#module-test.support.import_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.import_helper" title="test.support.import_helper: Support for import tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.import_helper</span></code></a> module provides support for import tests.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.forget">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">forget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.forget" title="Link to this definition">¶</a></dt>
<dd><p>Remove the module named <em>module_name</em> from <code class="docutils literal notranslate"><span class="pre">sys.modules</span></code> and delete any
byte-compiled files of the module.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.import_fresh_module">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">import_fresh_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fresh</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">blocked</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deprecated</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.import_fresh_module" title="Link to this definition">¶</a></dt>
<dd><p>This function imports and returns a fresh copy of the named Python module
by removing the named module from <code class="docutils literal notranslate"><span class="pre">sys.modules</span></code> before doing the import.
Note that unlike <code class="xref py py-func docutils literal notranslate"><span class="pre">reload()</span></code>, the original module is not affected by
this operation.</p>
<p><em>fresh</em> is an iterable of additional module names that are also removed
from the <code class="docutils literal notranslate"><span class="pre">sys.modules</span></code> cache before doing the import.</p>
<p><em>blocked</em> is an iterable of module names that are replaced with <code class="docutils literal notranslate"><span class="pre">None</span></code>
in the module cache during the import to ensure that attempts to import
them raise <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a>.</p>
<p>The named module and any modules named in the <em>fresh</em> and <em>blocked</em>
parameters are saved before starting the import and then reinserted into
<code class="docutils literal notranslate"><span class="pre">sys.modules</span></code> when the fresh import is complete.</p>
<p>Module and package deprecation messages are suppressed during this import
if <em>deprecated</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>This function will raise <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> if the named module cannot be
imported.</p>
<p>Example use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get copies of the warnings module for testing without affecting the</span>
<span class="c1"># version being used by the rest of the test suite. One copy uses the</span>
<span class="c1"># C implementation, the other is forced to use the pure Python fallback</span>
<span class="c1"># implementation</span>
<span class="n">py_warnings</span> <span class="o">=</span> <span class="n">import_fresh_module</span><span class="p">(</span><span class="s1">&#39;warnings&#39;</span><span class="p">,</span> <span class="n">blocked</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;_warnings&#39;</span><span class="p">])</span>
<span class="n">c_warnings</span> <span class="o">=</span> <span class="n">import_fresh_module</span><span class="p">(</span><span class="s1">&#39;warnings&#39;</span><span class="p">,</span> <span class="n">fresh</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;_warnings&#39;</span><span class="p">])</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.import_module">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">import_module</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deprecated</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">required_on</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.import_module" title="Link to this definition">¶</a></dt>
<dd><p>This function imports and returns the named module. Unlike a normal
import, this function raises <a class="reference internal" href="unittest.html#unittest.SkipTest" title="unittest.SkipTest"><code class="xref py py-exc docutils literal notranslate"><span class="pre">unittest.SkipTest</span></code></a> if the module
cannot be imported.</p>
<p>Module and package deprecation messages are suppressed during this import
if <em>deprecated</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>.  If a module is required on a platform but
optional for others, set <em>required_on</em> to an iterable of platform prefixes
which will be compared against <a class="reference internal" href="sys.html#sys.platform" title="sys.platform"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platform</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.modules_setup">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">modules_setup</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.modules_setup" title="Link to this definition">¶</a></dt>
<dd><p>Return a copy of <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.modules</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.modules_cleanup">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">modules_cleanup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">oldmodules</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.modules_cleanup" title="Link to this definition">¶</a></dt>
<dd><p>Remove modules except for <em>oldmodules</em> and <code class="docutils literal notranslate"><span class="pre">encodings</span></code> in order to
preserve internal cache.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.unload">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">unload</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.unload" title="Link to this definition">¶</a></dt>
<dd><p>Delete <em>name</em> from <code class="docutils literal notranslate"><span class="pre">sys.modules</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.import_helper.make_legacy_pyc">
<span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">make_legacy_pyc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.make_legacy_pyc" title="Link to this definition">¶</a></dt>
<dd><p>Move a <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a>/<span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0488/"><strong>PEP 488</strong></a> pyc file to its legacy pyc location and return the file
system path to the legacy pyc file.  The <em>source</em> value is the file system
path to the source file.  It does not need to exist, however the PEP
3147/488 pyc file must exist.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.import_helper.CleanImport">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">CleanImport</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">module_names</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.CleanImport" title="Link to this definition">¶</a></dt>
<dd><p>A context manager to force import to return a new module reference.  This
is useful for testing module-level behaviors, such as the emission of a
<a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a> on import.  Example usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">CleanImport</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">):</span>
    <span class="n">importlib</span><span class="o">.</span><span class="n">import_module</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span>  <span class="c1"># New reference.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.import_helper.DirsOnSysPath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.import_helper.</span></span><span class="sig-name descname"><span class="pre">DirsOnSysPath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">paths</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.import_helper.DirsOnSysPath" title="Link to this definition">¶</a></dt>
<dd><p>A context manager to temporarily add directories to <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>This makes a copy of <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>, appends any directories given
as positional arguments, then reverts <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> to the copied
settings when the context ends.</p>
<p>Note that <em>all</em> <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> modifications in the body of the
context manager, including replacement of the object,
will be reverted at the end of the block.</p>
</dd></dl>

</section>
<section id="module-test.support.warnings_helper">
<span id="test-support-warnings-helper-utilities-for-warnings-tests"></span><h1><a class="reference internal" href="#module-test.support.warnings_helper" title="test.support.warnings_helper: Support for warnings tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.warnings_helper</span></code></a> — Utilities for warnings tests<a class="headerlink" href="#module-test.support.warnings_helper" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-test.support.warnings_helper" title="test.support.warnings_helper: Support for warnings tests."><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.warnings_helper</span></code></a> module provides support for warnings tests.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="test.support.warnings_helper.ignore_warnings">
<span class="sig-prename descclassname"><span class="pre">test.support.warnings_helper.</span></span><span class="sig-name descname"><span class="pre">ignore_warnings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.warnings_helper.ignore_warnings" title="Link to this definition">¶</a></dt>
<dd><p>Suppress warnings that are instances of <em>category</em>,
which must be <a class="reference internal" href="exceptions.html#Warning" title="Warning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">Warning</span></code></a> or a subclass.
Roughly equivalent to <a class="reference internal" href="warnings.html#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">warnings.catch_warnings()</span></code></a>
with <a class="reference internal" href="warnings.html#warnings.simplefilter" title="warnings.simplefilter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">warnings.simplefilter('ignore',</span> <span class="pre">category=category)</span></code></a>.
For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@warning_helper</span><span class="o">.</span><span class="n">ignore_warnings</span><span class="p">(</span><span class="n">category</span><span class="o">=</span><span class="ne">DeprecationWarning</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">test_suppress_warning</span><span class="p">():</span>
    <span class="c1"># do something</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.warnings_helper.check_no_resource_warning">
<span class="sig-prename descclassname"><span class="pre">test.support.warnings_helper.</span></span><span class="sig-name descname"><span class="pre">check_no_resource_warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testcase</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.warnings_helper.check_no_resource_warning" title="Link to this definition">¶</a></dt>
<dd><p>Context manager to check that no <a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a> was raised.  You
must remove the object which may emit <a class="reference internal" href="exceptions.html#ResourceWarning" title="ResourceWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ResourceWarning</span></code></a> before the
end of the context manager.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.warnings_helper.check_syntax_warning">
<span class="sig-prename descclassname"><span class="pre">test.support.warnings_helper.</span></span><span class="sig-name descname"><span class="pre">check_syntax_warning</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">testcase</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">statement</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errtext</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lineno</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">offset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.warnings_helper.check_syntax_warning" title="Link to this definition">¶</a></dt>
<dd><p>Test for syntax warning in <em>statement</em> by attempting to compile <em>statement</em>.
Test also that the <a class="reference internal" href="exceptions.html#SyntaxWarning" title="SyntaxWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxWarning</span></code></a> is emitted only once, and that it
will be converted to a <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a> when turned into error.
<em>testcase</em> is the <a class="reference internal" href="unittest.html#module-unittest" title="unittest: Unit testing framework for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">unittest</span></code></a> instance for the test.  <em>errtext</em> is the
regular expression which should match the string representation of the
emitted <a class="reference internal" href="exceptions.html#SyntaxWarning" title="SyntaxWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxWarning</span></code></a> and raised <a class="reference internal" href="exceptions.html#SyntaxError" title="SyntaxError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">SyntaxError</span></code></a>.  If <em>lineno</em>
is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, compares to the line of the warning and exception.
If <em>offset</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, compares to the offset of the exception.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="test.support.warnings_helper.check_warnings">
<span class="sig-prename descclassname"><span class="pre">test.support.warnings_helper.</span></span><span class="sig-name descname"><span class="pre">check_warnings</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">filters</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quiet</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#test.support.warnings_helper.check_warnings" title="Link to this definition">¶</a></dt>
<dd><p>A convenience wrapper for <a class="reference internal" href="warnings.html#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">warnings.catch_warnings()</span></code></a> that makes it
easier to test that a warning was correctly raised.  It is approximately
equivalent to calling <code class="docutils literal notranslate"><span class="pre">warnings.catch_warnings(record=True)</span></code> with
<a class="reference internal" href="warnings.html#warnings.simplefilter" title="warnings.simplefilter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">warnings.simplefilter()</span></code></a> set to <code class="docutils literal notranslate"><span class="pre">always</span></code> and with the option to
automatically validate the results that are recorded.</p>
<p><code class="docutils literal notranslate"><span class="pre">check_warnings</span></code> accepts 2-tuples of the form <code class="docutils literal notranslate"><span class="pre">(&quot;message</span> <span class="pre">regexp&quot;,</span>
<span class="pre">WarningCategory)</span></code> as positional arguments. If one or more <em>filters</em> are
provided, or if the optional keyword argument <em>quiet</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>,
it checks to make sure the warnings are as expected:  each specified filter
must match at least one of the warnings raised by the enclosed code or the
test fails, and if any warnings are raised that do not match any of the
specified filters the test fails.  To disable the first of these checks,
set <em>quiet</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>If no arguments are specified, it defaults to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">check_warnings</span><span class="p">((</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="ne">Warning</span><span class="p">),</span> <span class="n">quiet</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
<p>In this case all warnings are caught and no errors are raised.</p>
<p>On entry to the context manager, a <code class="xref py py-class docutils literal notranslate"><span class="pre">WarningRecorder</span></code> instance is
returned. The underlying warnings list from
<a class="reference internal" href="warnings.html#warnings.catch_warnings" title="warnings.catch_warnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">catch_warnings()</span></code></a> is available via the recorder object’s
<a class="reference internal" href="warnings.html#module-warnings" title="warnings: Issue warning messages and control their disposition."><code class="xref py py-attr docutils literal notranslate"><span class="pre">warnings</span></code></a> attribute.  As a convenience, the attributes of the object
representing the most recent warning can also be accessed directly through
the recorder object (see example below).  If no warning has been raised,
then any of the attributes that would otherwise be expected on an object
representing a warning will return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>The recorder object also has a <code class="xref py py-meth docutils literal notranslate"><span class="pre">reset()</span></code> method, which clears the
warnings list.</p>
<p>The context manager is designed to be used like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">check_warnings</span><span class="p">((</span><span class="s2">&quot;assertion is always true&quot;</span><span class="p">,</span> <span class="ne">SyntaxWarning</span><span class="p">),</span>
                    <span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="ne">UserWarning</span><span class="p">)):</span>
    <span class="n">exec</span><span class="p">(</span><span class="s1">&#39;assert(False, &quot;Hey!&quot;)&#39;</span><span class="p">)</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="ne">UserWarning</span><span class="p">(</span><span class="s2">&quot;Hide me!&quot;</span><span class="p">))</span>
</pre></div>
</div>
<p>In this case if either warning was not raised, or some other warning was
raised, <a class="reference internal" href="#test.support.warnings_helper.check_warnings" title="test.support.warnings_helper.check_warnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_warnings()</span></code></a> would raise an error.</p>
<p>When a test needs to look more deeply into the warnings, rather than
just checking whether or not they occurred, code like this can be used:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">check_warnings</span><span class="p">(</span><span class="n">quiet</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span> <span class="k">as</span> <span class="n">w</span><span class="p">:</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">)</span>
    <span class="k">assert</span> <span class="nb">str</span><span class="p">(</span><span class="n">w</span><span class="o">.</span><span class="n">args</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span> <span class="o">==</span> <span class="s2">&quot;foo&quot;</span>
    <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;bar&quot;</span><span class="p">)</span>
    <span class="k">assert</span> <span class="nb">str</span><span class="p">(</span><span class="n">w</span><span class="o">.</span><span class="n">args</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span> <span class="o">==</span> <span class="s2">&quot;bar&quot;</span>
    <span class="k">assert</span> <span class="nb">str</span><span class="p">(</span><span class="n">w</span><span class="o">.</span><span class="n">warnings</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">args</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span> <span class="o">==</span> <span class="s2">&quot;foo&quot;</span>
    <span class="k">assert</span> <span class="nb">str</span><span class="p">(</span><span class="n">w</span><span class="o">.</span><span class="n">warnings</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">args</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span> <span class="o">==</span> <span class="s2">&quot;bar&quot;</span>
    <span class="n">w</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">w</span><span class="o">.</span><span class="n">warnings</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span>
</pre></div>
</div>
<p>Here all warnings will be caught, and the test code tests the captured
warnings directly.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>New optional arguments <em>filters</em> and <em>quiet</em>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="test.support.warnings_helper.WarningsRecorder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">test.support.warnings_helper.</span></span><span class="sig-name descname"><span class="pre">WarningsRecorder</span></span><a class="headerlink" href="#test.support.warnings_helper.WarningsRecorder" title="Link to this definition">¶</a></dt>
<dd><p>Class used to record warnings for unit tests. See documentation of
<a class="reference internal" href="#test.support.warnings_helper.check_warnings" title="test.support.warnings_helper.check_warnings"><code class="xref py py-func docutils literal notranslate"><span class="pre">check_warnings()</span></code></a> above for more details.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> — Regression tests package for Python</a><ul>
<li><a class="reference internal" href="#writing-unit-tests-for-the-test-package">Writing Unit Tests for the <code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> package</a></li>
<li><a class="reference internal" href="#module-test.regrtest">Running tests using the command-line interface</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-test.support"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support</span></code> — Utilities for the Python test suite</a></li>
<li><a class="reference internal" href="#module-test.support.socket_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.socket_helper</span></code> — Utilities for socket tests</a></li>
<li><a class="reference internal" href="#module-test.support.script_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.script_helper</span></code> — Utilities for the Python execution tests</a></li>
<li><a class="reference internal" href="#module-test.support.bytecode_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.bytecode_helper</span></code> — Support tools for testing correct bytecode generation</a></li>
<li><a class="reference internal" href="#module-test.support.threading_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.threading_helper</span></code> — Utilities for threading tests</a></li>
<li><a class="reference internal" href="#module-test.support.os_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.os_helper</span></code> — Utilities for os tests</a></li>
<li><a class="reference internal" href="#module-test.support.import_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.import_helper</span></code> — Utilities for import tests</a></li>
<li><a class="reference internal" href="#module-test.support.warnings_helper"><code class="xref py py-mod docutils literal notranslate"><span class="pre">test.support.warnings_helper</span></code> — Utilities for warnings tests</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="2to3.html"
                          title="previous chapter">2to3 — Automated Python 2 to 3 code translation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="debug.html"
                          title="next chapter">Debugging and Profiling</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/test.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="debug.html" title="Debugging and Profiling"
             >next</a> |</li>
        <li class="right" >
          <a href="2to3.html" title="2to3 — Automated Python 2 to 3 code translation"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="development.html" >Development Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">test</span></code> — Regression tests package for Python</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>