<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="enum — Support for enumerations" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/enum.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/enum.py Important: This page contains the API reference information. For tutorial information and discussion of more advanced topics, see Basic Tutorial, Advanced Tutorial, Enum Co..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/enum.py Important: This page contains the API reference information. For tutorial information and discussion of more advanced topics, see Basic Tutorial, Advanced Tutorial, Enum Co..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>enum — Support for enumerations &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="graphlib — Functionality to operate with graph-like structures" href="graphlib.html" />
    <link rel="prev" title="reprlib — Alternate repr() implementation" href="reprlib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/enum.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
<li><a class="reference internal" href="#data-types">Data Types</a><ul>
<li><a class="reference internal" href="#supported-dunder-names">Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names</a></li>
<li><a class="reference internal" href="#supported-sunder-names">Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names</a></li>
</ul>
</li>
<li><a class="reference internal" href="#utilities-and-decorators">Utilities and Decorators</a></li>
<li><a class="reference internal" href="#notes">Notes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="reprlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="graphlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/enum.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="graphlib.html" title="graphlib — Functionality to operate with graph-like structures"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="reprlib.html" title="reprlib — Alternate repr() implementation"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-enum">
<span id="enum-support-for-enumerations"></span><h1><a class="reference internal" href="#module-enum" title="enum: Implementation of an enumeration class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code></a> — Support for enumerations<a class="headerlink" href="#module-enum" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/enum.py">Lib/enum.py</a></p>
<aside class="sidebar">
<p class="sidebar-title">Important</p>
<p>This page contains the API reference information. For tutorial
information and discussion of more advanced topics, see</p>
<ul class="simple">
<li><p><a class="reference internal" href="../howto/enum.html#enum-basic-tutorial"><span class="std std-ref">Basic Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/enum.html#enum-advanced-tutorial"><span class="std std-ref">Advanced Tutorial</span></a></p></li>
<li><p><a class="reference internal" href="../howto/enum.html#enum-cookbook"><span class="std std-ref">Enum Cookbook</span></a></p></li>
</ul>
</aside>
<hr class="docutils" />
<p>An enumeration:</p>
<ul class="simple">
<li><p>is a set of symbolic names (members) bound to unique values</p></li>
<li><p>can be iterated over to return its canonical (i.e. non-alias) members in
definition order</p></li>
<li><p>uses <em>call</em> syntax to return members by value</p></li>
<li><p>uses <em>index</em> syntax to return members by name</p></li>
</ul>
<p>Enumerations are created either by using <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> syntax, or by
using function-call syntax:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># class syntax</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">3</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># functional syntax</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span> <span class="o">=</span> <span class="n">Enum</span><span class="p">(</span><span class="s1">&#39;Color&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;RED&#39;</span><span class="p">,</span> <span class="s1">&#39;GREEN&#39;</span><span class="p">,</span> <span class="s1">&#39;BLUE&#39;</span><span class="p">])</span>
</pre></div>
</div>
<p>Even though we can use <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> syntax to create Enums, Enums
are not normal Python classes.  See
<a class="reference internal" href="../howto/enum.html#enum-class-differences"><span class="std std-ref">How are Enums different?</span></a> for more details.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Nomenclature</p>
<ul class="simple">
<li><p>The class <code class="xref py py-class docutils literal notranslate"><span class="pre">Color</span></code> is an <em>enumeration</em> (or <em>enum</em>)</p></li>
<li><p>The attributes <code class="xref py py-attr docutils literal notranslate"><span class="pre">Color.RED</span></code>, <code class="xref py py-attr docutils literal notranslate"><span class="pre">Color.GREEN</span></code>, etc., are
<em>enumeration members</em> (or <em>members</em>) and are functionally constants.</p></li>
<li><p>The enum members have <em>names</em> and <em>values</em> (the name of
<code class="xref py py-attr docutils literal notranslate"><span class="pre">Color.RED</span></code> is <code class="docutils literal notranslate"><span class="pre">RED</span></code>, the value of <code class="xref py py-attr docutils literal notranslate"><span class="pre">Color.BLUE</span></code> is
<code class="docutils literal notranslate"><span class="pre">3</span></code>, etc.)</p></li>
</ul>
</div>
<hr class="docutils" />
<section id="module-contents">
<h2>Module Contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<blockquote>
<div><p><a class="reference internal" href="#enum.EnumType" title="enum.EnumType"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumType</span></code></a></p>
<blockquote>
<div><p>The <code class="docutils literal notranslate"><span class="pre">type</span></code> for Enum and its subclasses.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a></p>
<blockquote>
<div><p>Base class for creating enumerated constants.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a></p>
<blockquote>
<div><p>Base class for creating enumerated constants that are also
subclasses of <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>. (<a class="reference internal" href="#notes">Notes</a>)</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a></p>
<blockquote>
<div><p>Base class for creating enumerated constants that are also
subclasses of <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>. (<a class="reference internal" href="#notes">Notes</a>)</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a></p>
<blockquote>
<div><p>Base class for creating enumerated constants that can be combined using
the bitwise operations without losing their <a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> membership.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a></p>
<blockquote>
<div><p>Base class for creating enumerated constants that can be combined using
the bitwise operators without losing their <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> membership.
<a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> members are also subclasses of <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>. (<a class="reference internal" href="#notes">Notes</a>)</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.ReprEnum" title="enum.ReprEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">ReprEnum</span></code></a></p>
<blockquote>
<div><p>Used by <a class="reference internal" href="#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>, <a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a>, and <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>
to keep the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str()</span></code></a> of the mixed-in type.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.EnumCheck" title="enum.EnumCheck"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumCheck</span></code></a></p>
<blockquote>
<div><p>An enumeration with the values <code class="docutils literal notranslate"><span class="pre">CONTINUOUS</span></code>, <code class="docutils literal notranslate"><span class="pre">NAMED_FLAGS</span></code>, and
<code class="docutils literal notranslate"><span class="pre">UNIQUE</span></code>, for use with <a class="reference internal" href="#enum.verify" title="enum.verify"><code class="xref py py-func docutils literal notranslate"><span class="pre">verify()</span></code></a> to ensure various constraints
are met by a given enumeration.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.FlagBoundary" title="enum.FlagBoundary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlagBoundary</span></code></a></p>
<blockquote>
<div><p>An enumeration with the values <code class="docutils literal notranslate"><span class="pre">STRICT</span></code>, <code class="docutils literal notranslate"><span class="pre">CONFORM</span></code>, <code class="docutils literal notranslate"><span class="pre">EJECT</span></code>, and
<code class="docutils literal notranslate"><span class="pre">KEEP</span></code> which allows for more fine-grained control over how invalid values
are dealt with in an enumeration.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a></p>
<blockquote>
<div><p>Instances are replaced with an appropriate value for Enum members.
<a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a> defaults to the lower-cased version of the member name,
while other Enums default to 1 and increase from there.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.property" title="enum.property"><code class="xref py py-func docutils literal notranslate"><span class="pre">property()</span></code></a></p>
<blockquote>
<div><p>Allows <a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> members to have attributes without conflicting with
member names.  The <code class="docutils literal notranslate"><span class="pre">value</span></code> and <code class="docutils literal notranslate"><span class="pre">name</span></code> attributes are implemented this
way.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.unique" title="enum.unique"><code class="xref py py-func docutils literal notranslate"><span class="pre">unique()</span></code></a></p>
<blockquote>
<div><p>Enum class decorator that ensures only one name is bound to any one value.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.verify" title="enum.verify"><code class="xref py py-func docutils literal notranslate"><span class="pre">verify()</span></code></a></p>
<blockquote>
<div><p>Enum class decorator that checks user-selectable constraints on an
enumeration.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.member" title="enum.member"><code class="xref py py-func docutils literal notranslate"><span class="pre">member()</span></code></a></p>
<blockquote>
<div><p>Make <code class="docutils literal notranslate"><span class="pre">obj</span></code> a member.  Can be used as a decorator.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.nonmember" title="enum.nonmember"><code class="xref py py-func docutils literal notranslate"><span class="pre">nonmember()</span></code></a></p>
<blockquote>
<div><p>Do not make <code class="docutils literal notranslate"><span class="pre">obj</span></code> a member.  Can be used as a decorator.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.global_enum" title="enum.global_enum"><code class="xref py py-func docutils literal notranslate"><span class="pre">global_enum()</span></code></a></p>
<blockquote>
<div><p>Modify the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str()</span></code></a> and <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> of an enum
to show its members as belonging to the module instead of its class,
and export the enum members to the global namespace.</p>
</div></blockquote>
<p><a class="reference internal" href="#enum.show_flag_values" title="enum.show_flag_values"><code class="xref py py-func docutils literal notranslate"><span class="pre">show_flag_values()</span></code></a></p>
<blockquote>
<div><p>Return a list of all power-of-two integers contained in a flag.</p>
</div></blockquote>
</div></blockquote>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">Flag</span></code>, <code class="docutils literal notranslate"><span class="pre">IntFlag</span></code>, <code class="docutils literal notranslate"><span class="pre">auto</span></code></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span><code class="docutils literal notranslate"><span class="pre">StrEnum</span></code>, <code class="docutils literal notranslate"><span class="pre">EnumCheck</span></code>, <code class="docutils literal notranslate"><span class="pre">ReprEnum</span></code>, <code class="docutils literal notranslate"><span class="pre">FlagBoundary</span></code>, <code class="docutils literal notranslate"><span class="pre">property</span></code>, <code class="docutils literal notranslate"><span class="pre">member</span></code>, <code class="docutils literal notranslate"><span class="pre">nonmember</span></code>, <code class="docutils literal notranslate"><span class="pre">global_enum</span></code>, <code class="docutils literal notranslate"><span class="pre">show_flag_values</span></code></p>
</div>
</section>
<hr class="docutils" />
<section id="data-types">
<h2>Data Types<a class="headerlink" href="#data-types" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="enum.EnumType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">EnumType</span></span><a class="headerlink" href="#enum.EnumType" title="Link to this definition">¶</a></dt>
<dd><p><em>EnumType</em> is the <a class="reference internal" href="../glossary.html#term-metaclass"><span class="xref std std-term">metaclass</span></a> for <em>enum</em> enumerations.  It is possible
to subclass <em>EnumType</em> – see <a class="reference internal" href="../howto/enum.html#enumtype-examples"><span class="std std-ref">Subclassing EnumType</span></a>
for details.</p>
<p><em>EnumType</em> is responsible for setting the correct <code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code>, and <code class="xref py py-meth docutils literal notranslate"><span class="pre">__reduce__()</span></code> methods on the
final <em>enum</em>, as well as creating the enum members, properly handling
duplicates, providing iteration over the enum class, etc.</p>
<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__call__">
<span class="sig-name descname"><span class="pre">__call__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">module</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qualname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">boundary</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__call__" title="Link to this definition">¶</a></dt>
<dd><p>This method is called in two different ways:</p>
<ul>
<li><p>to look up an existing member:</p>
<blockquote>
<div><dl class="field-list simple">
<dt class="field-odd">cls<span class="colon">:</span></dt>
<dd class="field-odd"><p>The enum class being called.</p>
</dd>
<dt class="field-even">value<span class="colon">:</span></dt>
<dd class="field-even"><p>The value to lookup.</p>
</dd>
</dl>
</div></blockquote>
</li>
<li><p>to use the <code class="docutils literal notranslate"><span class="pre">cls</span></code> enum to create a new enum (only if the existing enum
does not have any members):</p>
<blockquote>
<div><dl class="field-list simple">
<dt class="field-odd">cls<span class="colon">:</span></dt>
<dd class="field-odd"><p>The enum class being called.</p>
</dd>
<dt class="field-even">value<span class="colon">:</span></dt>
<dd class="field-even"><p>The name of the new Enum to create.</p>
</dd>
<dt class="field-odd">names<span class="colon">:</span></dt>
<dd class="field-odd"><p>The names/values of the members for the new Enum.</p>
</dd>
<dt class="field-even">module<span class="colon">:</span></dt>
<dd class="field-even"><p>The name of the module the new Enum is created in.</p>
</dd>
<dt class="field-odd">qualname<span class="colon">:</span></dt>
<dd class="field-odd"><p>The actual location in the module where this Enum can be found.</p>
</dd>
<dt class="field-even">type<span class="colon">:</span></dt>
<dd class="field-even"><p>A mix-in type for the new Enum.</p>
</dd>
<dt class="field-odd">start<span class="colon">:</span></dt>
<dd class="field-odd"><p>The first integer value for the Enum (used by <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a>).</p>
</dd>
<dt class="field-even">boundary<span class="colon">:</span></dt>
<dd class="field-even"><p>How to handle out-of-range values from bit operations (<a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> only).</p>
</dd>
</dl>
</div></blockquote>
</li>
</ul>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__contains__">
<span class="sig-name descname"><span class="pre">__contains__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">member</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__contains__" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if member belongs to the <code class="docutils literal notranslate"><span class="pre">cls</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">some_var</span> <span class="o">=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">some_var</span> <span class="ow">in</span> <span class="n">Color</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span><span class="o">.</span><span class="n">value</span> <span class="ow">in</span> <span class="n">Color</span>
<span class="go">True</span>
</pre></div>
</div>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Before Python 3.12, a <code class="docutils literal notranslate"><span class="pre">TypeError</span></code> is raised if a
non-Enum-member is used in a containment check.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__dir__">
<span class="sig-name descname"><span class="pre">__dir__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__dir__" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">['__class__',</span> <span class="pre">'__doc__',</span> <span class="pre">'__members__',</span> <span class="pre">'__module__']</span></code> and the
names of the members in <em>cls</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">Color</span><span class="p">)</span>
<span class="go">[&#39;BLUE&#39;, &#39;GREEN&#39;, &#39;RED&#39;, &#39;__class__&#39;, &#39;__contains__&#39;, &#39;__doc__&#39;, &#39;__getitem__&#39;, &#39;__init_subclass__&#39;, &#39;__iter__&#39;, &#39;__len__&#39;, &#39;__members__&#39;, &#39;__module__&#39;, &#39;__name__&#39;, &#39;__qualname__&#39;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__getitem__">
<span class="sig-name descname"><span class="pre">__getitem__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__getitem__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the Enum member in <em>cls</em> matching <em>name</em>, or raises a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">[</span><span class="s1">&#39;BLUE&#39;</span><span class="p">]</span>
<span class="go">&lt;Color.BLUE: 3&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__iter__">
<span class="sig-name descname"><span class="pre">__iter__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__iter__" title="Link to this definition">¶</a></dt>
<dd><p>Returns each member in <em>cls</em> in definition order:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Color</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;, &lt;Color.GREEN: 2&gt;, &lt;Color.BLUE: 3&gt;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__len__">
<span class="sig-name descname"><span class="pre">__len__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__len__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the number of member in <em>cls</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">Color</span><span class="p">)</span>
<span class="go">3</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.EnumType.__members__">
<span class="sig-name descname"><span class="pre">__members__</span></span><a class="headerlink" href="#enum.EnumType.__members__" title="Link to this definition">¶</a></dt>
<dd><p>Returns a mapping of every enum name to its member, including aliases</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.EnumType.__reversed__">
<span class="sig-name descname"><span class="pre">__reversed__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.EnumType.__reversed__" title="Link to this definition">¶</a></dt>
<dd><p>Returns each member in <em>cls</em> in reverse definition order:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="nb">reversed</span><span class="p">(</span><span class="n">Color</span><span class="p">))</span>
<span class="go">[&lt;Color.BLUE: 3&gt;, &lt;Color.GREEN: 2&gt;, &lt;Color.RED: 1&gt;]</span>
</pre></div>
</div>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Before 3.11 <code class="docutils literal notranslate"><span class="pre">enum</span></code> used <code class="docutils literal notranslate"><span class="pre">EnumMeta</span></code> type, which is kept as an alias.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.Enum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">Enum</span></span><a class="headerlink" href="#enum.Enum" title="Link to this definition">¶</a></dt>
<dd><p><em>Enum</em> is the base class for all <em>enum</em> enumerations.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#enum.Enum.name" title="Link to this definition">¶</a></dt>
<dd><p>The name used to define the <code class="docutils literal notranslate"><span class="pre">Enum</span></code> member:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;BLUE&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum.value">
<span class="sig-name descname"><span class="pre">value</span></span><a class="headerlink" href="#enum.Enum.value" title="Link to this definition">¶</a></dt>
<dd><p>The value given to the <code class="docutils literal notranslate"><span class="pre">Enum</span></code> member:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span><span class="o">.</span><span class="n">value</span>
<span class="go">1</span>
</pre></div>
</div>
<p>Value of the member, can be set in <a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Enum member values</p>
<p>Member values can be anything: <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, etc.  If
the exact value is unimportant you may use <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> instances and an
appropriate value will be chosen for you.  See <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> for the
details.</p>
<p>While mutable/unhashable values, such as <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a>, <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> or
a mutable <a class="reference internal" href="dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-class docutils literal notranslate"><span class="pre">dataclass</span></code></a>, can be used, they will have a
quadratic performance impact during creation relative to the
total number of mutable/unhashable values in the enum.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum._name_">
<span class="sig-name descname"><span class="pre">_name_</span></span><a class="headerlink" href="#enum.Enum._name_" title="Link to this definition">¶</a></dt>
<dd><p>Name of the member.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum._value_">
<span class="sig-name descname"><span class="pre">_value_</span></span><a class="headerlink" href="#enum.Enum._value_" title="Link to this definition">¶</a></dt>
<dd><p>Value of the member, can be set in <a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum._order_">
<span class="sig-name descname"><span class="pre">_order_</span></span><a class="headerlink" href="#enum.Enum._order_" title="Link to this definition">¶</a></dt>
<dd><p>No longer used, kept for backward compatibility.
(class attribute, removed during class creation).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.Enum._ignore_">
<span class="sig-name descname"><span class="pre">_ignore_</span></span><a class="headerlink" href="#enum.Enum._ignore_" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">_ignore_</span></code> is only used during creation and is removed from the
enumeration once creation is complete.</p>
<p><code class="docutils literal notranslate"><span class="pre">_ignore_</span></code> is a list of names that will not become members, and whose
names will also be removed from the completed enumeration.  See
<a class="reference internal" href="../howto/enum.html#enum-time-period"><span class="std std-ref">TimePeriod</span></a> for an example.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__dir__">
<span class="sig-name descname"><span class="pre">__dir__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__dir__" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">['__class__',</span> <span class="pre">'__doc__',</span> <span class="pre">'__module__',</span> <span class="pre">'name',</span> <span class="pre">'value']</span></code> and
any public methods defined on <em>self.__class__</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">date</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TUESDAY</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">WEDNESDAY</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">THURSDAY</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">FRIDAY</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">... </span>    <span class="n">SATURDAY</span> <span class="o">=</span> <span class="mi">6</span>
<span class="gp">... </span>    <span class="n">SUNDAY</span> <span class="o">=</span> <span class="mi">7</span>
<span class="gp">... </span>    <span class="nd">@classmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">today</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="gp">... </span>        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;today is </span><span class="si">%s</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="bp">cls</span><span class="p">(</span><span class="n">date</span><span class="o">.</span><span class="n">today</span><span class="p">()</span><span class="o">.</span><span class="n">isoweekday</span><span class="p">())</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">Weekday</span><span class="o">.</span><span class="n">SATURDAY</span><span class="p">)</span>
<span class="go">[&#39;__class__&#39;, &#39;__doc__&#39;, &#39;__eq__&#39;, &#39;__hash__&#39;, &#39;__module__&#39;, &#39;name&#39;, &#39;today&#39;, &#39;value&#39;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum._generate_next_value_">
<span class="sig-name descname"><span class="pre">_generate_next_value_</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">count</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">last_values</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum._generate_next_value_" title="Link to this definition">¶</a></dt>
<dd><blockquote>
<div><dl class="field-list simple">
<dt class="field-odd">name<span class="colon">:</span></dt>
<dd class="field-odd"><p>The name of the member being defined (e.g. ‘RED’).</p>
</dd>
<dt class="field-even">start<span class="colon">:</span></dt>
<dd class="field-even"><p>The start value for the Enum; the default is 1.</p>
</dd>
<dt class="field-odd">count<span class="colon">:</span></dt>
<dd class="field-odd"><p>The number of members currently defined, not including this one.</p>
</dd>
<dt class="field-even">last_values<span class="colon">:</span></dt>
<dd class="field-even"><p>A list of the previous values.</p>
</dd>
</dl>
</div></blockquote>
<p>A <em>staticmethod</em> that is used to determine the next value returned by
<a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">PowersOfThree</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nd">@staticmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">_generate_next_value_</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="n">last_values</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="mi">3</span> <span class="o">**</span> <span class="p">(</span><span class="n">count</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">FIRST</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SECOND</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PowersOfThree</span><span class="o">.</span><span class="n">SECOND</span><span class="o">.</span><span class="n">value</span>
<span class="go">9</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__init__">
<span class="sig-name descname"><span class="pre">__init__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__init__" title="Link to this definition">¶</a></dt>
<dd><p>By default, does nothing.  If multiple values are given in the member
assignment, those values become separate arguments to <code class="docutils literal notranslate"><span class="pre">__init__</span></code>; e.g.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;Mon&#39;</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Weekday.__init__()</span></code> would be called as <code class="docutils literal notranslate"><span class="pre">Weekday.__init__(self,</span> <span class="pre">1,</span> <span class="pre">'Mon')</span></code></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__init_subclass__">
<span class="sig-name descname"><span class="pre">__init_subclass__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__init_subclass__" title="Link to this definition">¶</a></dt>
<dd><p>A <em>classmethod</em> that is used to further configure subsequent subclasses.
By default, does nothing.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum._missing_">
<span class="sig-name descname"><span class="pre">_missing_</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum._missing_" title="Link to this definition">¶</a></dt>
<dd><p>A <em>classmethod</em> for looking up values not found in <em>cls</em>.  By default it
does nothing, but can be overridden to implement custom search behavior:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">StrEnum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Build</span><span class="p">(</span><span class="n">StrEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">DEBUG</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">OPTIMIZED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="nd">@classmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">_missing_</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">... </span>        <span class="n">value</span> <span class="o">=</span> <span class="n">value</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
<span class="gp">... </span>        <span class="k">for</span> <span class="n">member</span> <span class="ow">in</span> <span class="bp">cls</span><span class="p">:</span>
<span class="gp">... </span>            <span class="k">if</span> <span class="n">member</span><span class="o">.</span><span class="n">value</span> <span class="o">==</span> <span class="n">value</span><span class="p">:</span>
<span class="gp">... </span>                <span class="k">return</span> <span class="n">member</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="kc">None</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Build</span><span class="o">.</span><span class="n">DEBUG</span><span class="o">.</span><span class="n">value</span>
<span class="go">&#39;debug&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Build</span><span class="p">(</span><span class="s1">&#39;deBUG&#39;</span><span class="p">)</span>
<span class="go">&lt;Build.DEBUG: &#39;debug&#39;&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__new__">
<span class="sig-name descname"><span class="pre">__new__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__new__" title="Link to this definition">¶</a></dt>
<dd><p>By default, doesn’t exist.  If specified, either in the enum class
definition or in a mixin class (such as <code class="docutils literal notranslate"><span class="pre">int</span></code>), all values given
in the member assignment will be passed; e.g.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyIntEnum</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">SEVENTEEN</span> <span class="o">=</span> <span class="s1">&#39;1a&#39;</span><span class="p">,</span> <span class="mi">16</span>
</pre></div>
</div>
<p>results in the call <code class="docutils literal notranslate"><span class="pre">int('1a',</span> <span class="pre">16)</span></code> and a value of <code class="docutils literal notranslate"><span class="pre">17</span></code> for the member.</p>
<dl class="simple">
<dt>..note:: When writing a custom <code class="docutils literal notranslate"><span class="pre">__new__</span></code>, do not use <code class="docutils literal notranslate"><span class="pre">super().__new__</span></code> –</dt><dd><p>call the appropriate <code class="docutils literal notranslate"><span class="pre">__new__</span></code> instead.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__repr__">
<span class="sig-name descname"><span class="pre">__repr__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__repr__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the string used for <em>repr()</em> calls.  By default, returns the
<em>Enum</em> name, member name, and value, but can be overridden:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">OtherStyle</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ALTERNATE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">OTHER</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SOMETHING_ELSE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="n">cls_name</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span><span class="o">.</span><span class="vm">__name__</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="n">cls_name</span><span class="si">}</span><span class="s1">.</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">),</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="go">(OtherStyle.ALTERNATE, &#39;OtherStyle.ALTERNATE&#39;, &#39;OtherStyle.ALTERNATE&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__str__">
<span class="sig-name descname"><span class="pre">__str__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__str__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the string used for <em>str()</em> calls.  By default, returns the
<em>Enum</em> name and member name, but can be overridden:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">OtherStyle</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ALTERNATE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">OTHER</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SOMETHING_ELSE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">),</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="go">(&lt;OtherStyle.ALTERNATE: 1&gt;, &#39;ALTERNATE&#39;, &#39;ALTERNATE&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Enum.__format__">
<span class="sig-name descname"><span class="pre">__format__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Enum.__format__" title="Link to this definition">¶</a></dt>
<dd><p>Returns the string used for <em>format()</em> and <em>f-string</em> calls.  By default,
returns <a class="reference internal" href="#enum.Enum.__str__" title="enum.Enum.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a> return value, but can be overridden:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">OtherStyle</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ALTERNATE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">OTHER</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SOMETHING_ELSE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__format__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">spec</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s1">&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="p">),</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">OtherStyle</span><span class="o">.</span><span class="n">ALTERNATE</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="go">(&lt;OtherStyle.ALTERNATE: 1&gt;, &#39;OtherStyle.ALTERNATE&#39;, &#39;ALTERNATE&#39;)</span>
</pre></div>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> with <a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> results in integers of increasing value,
starting with <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added <a class="reference internal" href="../howto/enum.html#enum-dataclass-support"><span class="std std-ref">Dataclass support</span></a></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.IntEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">IntEnum</span></span><a class="headerlink" href="#enum.IntEnum" title="Link to this definition">¶</a></dt>
<dd><p><em>IntEnum</em> is the same as <em>Enum</em>, but its members are also integers and can be
used anywhere that an integer can be used.  If any integer operation is performed
with an <em>IntEnum</em> member, the resulting value loses its enumeration status.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">IntEnum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Number</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ONE</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TWO</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">THREE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Number</span><span class="o">.</span><span class="n">THREE</span>
<span class="go">&lt;Number.THREE: 3&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Number</span><span class="o">.</span><span class="n">ONE</span> <span class="o">+</span> <span class="n">Number</span><span class="o">.</span><span class="n">TWO</span>
<span class="go">3</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Number</span><span class="o">.</span><span class="n">THREE</span> <span class="o">+</span> <span class="mi">5</span>
<span class="go">8</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Number</span><span class="o">.</span><span class="n">THREE</span> <span class="o">==</span> <span class="mi">3</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> with <a class="reference internal" href="#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> results in integers of increasing
value, starting with <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="../reference/datamodel.html#object.__str__" title="object.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a> is now <code class="xref py py-meth docutils literal notranslate"><span class="pre">int.__str__()</span></code> to
better support the <em>replacement of existing constants</em> use-case.
<a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a> was already <code class="xref py py-meth docutils literal notranslate"><span class="pre">int.__format__()</span></code> for that same reason.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.StrEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">StrEnum</span></span><a class="headerlink" href="#enum.StrEnum" title="Link to this definition">¶</a></dt>
<dd><p><em>StrEnum</em> is the same as <em>Enum</em>, but its members are also strings and can be used
in most of the same places that a string can be used.  The result of any string
operation performed on or with a <em>StrEnum</em> member is not part of the enumeration.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>There are places in the stdlib that check for an exact <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>
instead of a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> subclass (i.e. <code class="docutils literal notranslate"><span class="pre">type(unknown)</span> <span class="pre">==</span> <span class="pre">str</span></code>
instead of <code class="docutils literal notranslate"><span class="pre">isinstance(unknown,</span> <span class="pre">str)</span></code>), and in those locations you
will need to use <code class="docutils literal notranslate"><span class="pre">str(StrEnum.member)</span></code>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> with <a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a> results in the lower-cased member
name as the value.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="../reference/datamodel.html#object.__str__" title="object.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a> is <code class="xref py py-meth docutils literal notranslate"><span class="pre">str.__str__()</span></code> to better support the
<em>replacement of existing constants</em> use-case.  <a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a> is likewise
<code class="xref py py-meth docutils literal notranslate"><span class="pre">str.__format__()</span></code> for that same reason.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.Flag">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">Flag</span></span><a class="headerlink" href="#enum.Flag" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">Flag</span></code> is the same as <a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>, but its members support the bitwise
operators <code class="docutils literal notranslate"><span class="pre">&amp;</span></code> (<em>AND</em>), <code class="docutils literal notranslate"><span class="pre">|</span></code> (<em>OR</em>), <code class="docutils literal notranslate"><span class="pre">^</span></code> (<em>XOR</em>), and <code class="docutils literal notranslate"><span class="pre">~</span></code> (<em>INVERT</em>);
the results of those operators are members of the enumeration.</p>
<dl class="py method">
<dt class="sig sig-object py" id="enum.Flag.__contains__">
<span class="sig-name descname"><span class="pre">__contains__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Flag.__contains__" title="Link to this definition">¶</a></dt>
<dd><p>Returns <em>True</em> if value is in self:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">white</span> <span class="o">=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span> <span class="ow">in</span> <span class="n">purple</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span> <span class="ow">in</span> <span class="n">white</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="ow">in</span> <span class="n">white</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">white</span> <span class="ow">in</span> <span class="n">purple</span>
<span class="go">False</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">__iter__(self):</span></span></dt>
<dd><p>Returns all contained non-alias members:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">purple</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;, &lt;Color.BLUE: 4&gt;]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">__len__(self):</span></span></dt>
<dd><p>Returns number of members in flag:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span><span class="p">)</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">white</span><span class="p">)</span>
<span class="go">3</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">__bool__(self):</span></span></dt>
<dd><p>Returns <em>True</em> if any members in flag, <em>False</em> otherwise:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">white</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">black</span> <span class="o">=</span> <span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">black</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Flag.__or__">
<span class="sig-name descname"><span class="pre">__or__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Flag.__or__" title="Link to this definition">¶</a></dt>
<dd><p>Returns current flag binary or’ed with other:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.RED|GREEN: 3&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Flag.__and__">
<span class="sig-name descname"><span class="pre">__and__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Flag.__and__" title="Link to this definition">¶</a></dt>
<dd><p>Returns current flag binary and’ed with other:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">&amp;</span> <span class="n">white</span>
<span class="go">&lt;Color.RED|BLUE: 5&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">&amp;</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color: 0&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Flag.__xor__">
<span class="sig-name descname"><span class="pre">__xor__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.Flag.__xor__" title="Link to this definition">¶</a></dt>
<dd><p>Returns current flag binary xor’ed with other:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">^</span> <span class="n">white</span>
<span class="go">&lt;Color.GREEN: 2&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">^</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.RED|GREEN|BLUE: 7&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">__invert__(self):</span></span></dt>
<dd><p>Returns all the flags in <em>type(self)</em> that are not in self:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">white</span>
<span class="go">&lt;Color: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">purple</span>
<span class="go">&lt;Color.GREEN: 2&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">&lt;Color.GREEN|BLUE: 6&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="enum.Flag._numeric_repr_">
<span class="sig-name descname"><span class="pre">_numeric_repr_</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#enum.Flag._numeric_repr_" title="Link to this definition">¶</a></dt>
<dd><p>Function used to format any remaining unnamed numeric values.  Default is
the value’s repr; common choices are <a class="reference internal" href="functions.html#hex" title="hex"><code class="xref py py-func docutils literal notranslate"><span class="pre">hex()</span></code></a> and <a class="reference internal" href="functions.html#oct" title="oct"><code class="xref py py-func docutils literal notranslate"><span class="pre">oct()</span></code></a>.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> with <a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> results in integers that are powers
of two, starting with <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <em>repr()</em> of zero-valued flags has changed.  It
is now::</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span> 
<span class="go">&lt;Color: 0&gt;</span>
</pre></div>
</div>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.IntFlag">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">IntFlag</span></span><a class="headerlink" href="#enum.IntFlag" title="Link to this definition">¶</a></dt>
<dd><p><em>IntFlag</em> is the same as <em>Flag</em>, but its members are also integers and can be
used anywhere that an integer can be used.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">IntFlag</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">IntFlag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">&amp;</span> <span class="mi">2</span>
<span class="go">&lt;Color: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="mi">2</span>
<span class="go">&lt;Color.RED|GREEN: 3&gt;</span>
</pre></div>
</div>
<p>If any integer operation is performed with an <em>IntFlag</em> member, the result is
not an <em>IntFlag</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">+</span> <span class="mi">2</span>
<span class="go">3</span>
</pre></div>
</div>
<p>If a <em>Flag</em> operation is performed with an <em>IntFlag</em> member and:</p>
<ul class="simple">
<li><p>the result is a valid <em>IntFlag</em>: an <em>IntFlag</em> is returned</p></li>
<li><p>the result is not a valid <em>IntFlag</em>: the result depends on the <em>FlagBoundary</em> setting</p></li>
</ul>
<p>The <em>repr()</em> of unnamed zero-valued flags has changed.  It is now:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="go">&lt;Color: 0&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> with <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> results in integers that are powers
of two, starting with <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><a class="reference internal" href="../reference/datamodel.html#object.__str__" title="object.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code></a> is now <code class="xref py py-meth docutils literal notranslate"><span class="pre">int.__str__()</span></code> to better support the
<em>replacement of existing constants</em> use-case.  <a class="reference internal" href="../reference/datamodel.html#object.__format__" title="object.__format__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__format__()</span></code></a> was
already <code class="xref py py-meth docutils literal notranslate"><span class="pre">int.__format__()</span></code> for that same reason.</p>
<p>Inversion of an <code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code> now returns a positive value that is the
union of all flags not in the given flag, rather than a negative value.
This matches the existing <a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> behavior.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.ReprEnum">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">ReprEnum</span></span><a class="headerlink" href="#enum.ReprEnum" title="Link to this definition">¶</a></dt>
<dd><p><code class="xref py py-class docutils literal notranslate"><span class="pre">ReprEnum</span></code> uses the <a class="reference internal" href="#enum.Enum.__repr__" title="enum.Enum.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">repr()</span></code></a> of <a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>,
but the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str()</span></code></a> of the mixed-in data type:</p>
<ul class="simple">
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">int.__str__()</span></code> for <a class="reference internal" href="#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> and <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a></p></li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.__str__()</span></code> for <a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a></p></li>
</ul>
<p>Inherit from <code class="xref py py-class docutils literal notranslate"><span class="pre">ReprEnum</span></code> to keep the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str()</span></code></a> / <a class="reference internal" href="functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a>
of the mixed-in data type instead of using the
<a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>-default <a class="reference internal" href="#enum.Enum.__str__" title="enum.Enum.__str__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.EnumCheck">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">EnumCheck</span></span><a class="headerlink" href="#enum.EnumCheck" title="Link to this definition">¶</a></dt>
<dd><p><em>EnumCheck</em> contains the options used by the <a class="reference internal" href="#enum.verify" title="enum.verify"><code class="xref py py-func docutils literal notranslate"><span class="pre">verify()</span></code></a> decorator to ensure
various constraints; failed constraints result in a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="enum.EnumCheck.UNIQUE">
<span class="sig-name descname"><span class="pre">UNIQUE</span></span><a class="headerlink" href="#enum.EnumCheck.UNIQUE" title="Link to this definition">¶</a></dt>
<dd><p>Ensure that each value has only one name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">verify</span><span class="p">,</span> <span class="n">UNIQUE</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@verify</span><span class="p">(</span><span class="n">UNIQUE</span><span class="p">)</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">CRIMSON</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">aliases found in &lt;enum &#39;Color&#39;&gt;: CRIMSON -&gt; RED</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.EnumCheck.CONTINUOUS">
<span class="sig-name descname"><span class="pre">CONTINUOUS</span></span><a class="headerlink" href="#enum.EnumCheck.CONTINUOUS" title="Link to this definition">¶</a></dt>
<dd><p>Ensure that there are no missing values between the lowest-valued member
and the highest-valued member:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">verify</span><span class="p">,</span> <span class="n">CONTINUOUS</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@verify</span><span class="p">(</span><span class="n">CONTINUOUS</span><span class="p">)</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">invalid enum &#39;Color&#39;: missing values 3, 4</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.EnumCheck.NAMED_FLAGS">
<span class="sig-name descname"><span class="pre">NAMED_FLAGS</span></span><a class="headerlink" href="#enum.EnumCheck.NAMED_FLAGS" title="Link to this definition">¶</a></dt>
<dd><p>Ensure that any flag groups/masks contain only named flags – useful when
values are specified instead of being generated by <a class="reference internal" href="#enum.auto" title="enum.auto"><code class="xref py py-func docutils literal notranslate"><span class="pre">auto()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">verify</span><span class="p">,</span> <span class="n">NAMED_FLAGS</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@verify</span><span class="p">(</span><span class="n">NAMED_FLAGS</span><span class="p">)</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">WHITE</span> <span class="o">=</span> <span class="mi">15</span>
<span class="gp">... </span>    <span class="n">NEON</span> <span class="o">=</span> <span class="mi">31</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">invalid Flag &#39;Color&#39;: aliases WHITE and NEON are missing combined values of 0x18 [use enum.show_flag_values(value) for details]</span>
</pre></div>
</div>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>CONTINUOUS and NAMED_FLAGS are designed to work with integer-valued members.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="enum.FlagBoundary">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">FlagBoundary</span></span><a class="headerlink" href="#enum.FlagBoundary" title="Link to this definition">¶</a></dt>
<dd><p><em>FlagBoundary</em> controls how out-of-range values are handled in <em>Flag</em> and its
subclasses.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="enum.FlagBoundary.STRICT">
<span class="sig-name descname"><span class="pre">STRICT</span></span><a class="headerlink" href="#enum.FlagBoundary.STRICT" title="Link to this definition">¶</a></dt>
<dd><p>Out-of-range values cause a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> to be raised. This is the
default for <a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">STRICT</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">StrictFlag</span><span class="p">(</span><span class="n">Flag</span><span class="p">,</span> <span class="n">boundary</span><span class="o">=</span><span class="n">STRICT</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">StrictFlag</span><span class="p">(</span><span class="mi">2</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">2</span><span class="o">**</span><span class="mi">4</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">&lt;flag &#39;StrictFlag&#39;&gt; invalid value 20</span>
<span class="x">    given 0b0 10100</span>
<span class="x">  allowed 0b0 00111</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.FlagBoundary.CONFORM">
<span class="sig-name descname"><span class="pre">CONFORM</span></span><a class="headerlink" href="#enum.FlagBoundary.CONFORM" title="Link to this definition">¶</a></dt>
<dd><p>Out-of-range values have invalid values removed, leaving a valid <em>Flag</em>
value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">CONFORM</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">ConformFlag</span><span class="p">(</span><span class="n">Flag</span><span class="p">,</span> <span class="n">boundary</span><span class="o">=</span><span class="n">CONFORM</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ConformFlag</span><span class="p">(</span><span class="mi">2</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">2</span><span class="o">**</span><span class="mi">4</span><span class="p">)</span>
<span class="go">&lt;ConformFlag.BLUE: 4&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.FlagBoundary.EJECT">
<span class="sig-name descname"><span class="pre">EJECT</span></span><a class="headerlink" href="#enum.FlagBoundary.EJECT" title="Link to this definition">¶</a></dt>
<dd><p>Out-of-range values lose their <em>Flag</em> membership and revert to <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">EJECT</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">EjectFlag</span><span class="p">(</span><span class="n">Flag</span><span class="p">,</span> <span class="n">boundary</span><span class="o">=</span><span class="n">EJECT</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">EjectFlag</span><span class="p">(</span><span class="mi">2</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">2</span><span class="o">**</span><span class="mi">4</span><span class="p">)</span>
<span class="go">20</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="enum.FlagBoundary.KEEP">
<span class="sig-name descname"><span class="pre">KEEP</span></span><a class="headerlink" href="#enum.FlagBoundary.KEEP" title="Link to this definition">¶</a></dt>
<dd><p>Out-of-range values are kept, and the <em>Flag</em> membership is kept.
This is the default for <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">KEEP</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">KeepFlag</span><span class="p">(</span><span class="n">Flag</span><span class="p">,</span> <span class="n">boundary</span><span class="o">=</span><span class="n">KEEP</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">KeepFlag</span><span class="p">(</span><span class="mi">2</span><span class="o">**</span><span class="mi">2</span> <span class="o">+</span> <span class="mi">2</span><span class="o">**</span><span class="mi">4</span><span class="p">)</span>
<span class="go">&lt;KeepFlag.BLUE|16: 20&gt;</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<hr class="docutils" />
<section id="supported-dunder-names">
<h3>Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names<a class="headerlink" href="#supported-dunder-names" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#enum.EnumType.__members__" title="enum.EnumType.__members__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__members__</span></code></a> is a read-only ordered mapping of <code class="docutils literal notranslate"><span class="pre">member_name</span></code>:<code class="docutils literal notranslate"><span class="pre">member</span></code>
items.  It is only available on the class.</p>
<p><a class="reference internal" href="../reference/datamodel.html#object.__new__" title="object.__new__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a>, if specified, must create and return the enum members; it is
also a very good idea to set the member’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">_value_</span></code> appropriately.  Once
all the members are created it is no longer used.</p>
</section>
<section id="supported-sunder-names">
<h3>Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names<a class="headerlink" href="#supported-sunder-names" title="Link to this heading">¶</a></h3>
<ul>
<li><p><a class="reference internal" href="#enum.Enum._name_" title="enum.Enum._name_"><code class="xref py py-attr docutils literal notranslate"><span class="pre">_name_</span></code></a> – name of the member</p></li>
<li><p><a class="reference internal" href="#enum.Enum._value_" title="enum.Enum._value_"><code class="xref py py-attr docutils literal notranslate"><span class="pre">_value_</span></code></a> – value of the member; can be set in <code class="docutils literal notranslate"><span class="pre">__new__</span></code></p></li>
<li><p><a class="reference internal" href="#enum.Enum._missing_" title="enum.Enum._missing_"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_missing_()</span></code></a> – a lookup function used when a value is not found;
may be overridden</p></li>
<li><p><a class="reference internal" href="#enum.Enum._ignore_" title="enum.Enum._ignore_"><code class="xref py py-attr docutils literal notranslate"><span class="pre">_ignore_</span></code></a> – a list of names, either as a <a class="reference internal" href="stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> or a
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, that will not be transformed into members, and will be removed
from the final class</p></li>
<li><p><a class="reference internal" href="#enum.Enum._order_" title="enum.Enum._order_"><code class="xref py py-attr docutils literal notranslate"><span class="pre">_order_</span></code></a> – no longer used, kept for backward
compatibility (class attribute, removed during class creation)</p></li>
<li><p><a class="reference internal" href="#enum.Enum._generate_next_value_" title="enum.Enum._generate_next_value_"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_generate_next_value_()</span></code></a> – used to get an appropriate value for
an enum member; may be overridden</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For standard <a class="reference internal" href="#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> classes the next value chosen is the last value seen
incremented by one.</p>
<p>For <a class="reference internal" href="#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> classes the next value chosen will be the next highest
power-of-two, regardless of the last value seen.</p>
</div>
</li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">_missing_</span></code>, <code class="docutils literal notranslate"><span class="pre">_order_</span></code>, <code class="docutils literal notranslate"><span class="pre">_generate_next_value_</span></code></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span><code class="docutils literal notranslate"><span class="pre">_ignore_</span></code></p>
</div>
</section>
</section>
<hr class="docutils" />
<section id="utilities-and-decorators">
<h2>Utilities and Decorators<a class="headerlink" href="#utilities-and-decorators" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="enum.auto">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">auto</span></span><a class="headerlink" href="#enum.auto" title="Link to this definition">¶</a></dt>
<dd><p><em>auto</em> can be used in place of a value.  If used, the <em>Enum</em> machinery will
call an <em>Enum</em>’s <a class="reference internal" href="#enum.Enum._generate_next_value_" title="enum.Enum._generate_next_value_"><code class="xref py py-meth docutils literal notranslate"><span class="pre">_generate_next_value_()</span></code></a> to get an appropriate value.
For <em>Enum</em> and <em>IntEnum</em> that appropriate value will be the last value plus
one; for <em>Flag</em> and <em>IntFlag</em> it will be the first power-of-two greater
than the highest value; for <em>StrEnum</em> it will be the lower-cased version of
the member’s name.  Care must be taken if mixing <em>auto()</em> with manually
specified values.</p>
<p><em>auto</em> instances are only resolved when at the top level of an assignment:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">FIRST</span> <span class="pre">=</span> <span class="pre">auto()</span></code> will work (auto() is replaced with <code class="docutils literal notranslate"><span class="pre">1</span></code>);</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">SECOND</span> <span class="pre">=</span> <span class="pre">auto(),</span> <span class="pre">-2</span></code> will work (auto is replaced with <code class="docutils literal notranslate"><span class="pre">2</span></code>, so <code class="docutils literal notranslate"><span class="pre">2,</span> <span class="pre">-2</span></code> is
used to create the <code class="docutils literal notranslate"><span class="pre">SECOND</span></code> enum member;</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">THREE</span> <span class="pre">=</span> <span class="pre">[auto(),</span> <span class="pre">-3]</span></code> will <em>not</em> work (<code class="docutils literal notranslate"><span class="pre">&lt;auto</span> <span class="pre">instance&gt;,</span> <span class="pre">-3</span></code> is used to
create the <code class="docutils literal notranslate"><span class="pre">THREE</span></code> enum member)</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.1: </span>In prior versions, <code class="docutils literal notranslate"><span class="pre">auto()</span></code> had to be the only thing
on the assignment line to work properly.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">_generate_next_value_</span></code> can be overridden to customize the values used by
<em>auto</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>in 3.13 the default <code class="docutils literal notranslate"><span class="pre">_generate_next_value_</span></code> will always return
the highest member value incremented by 1, and will fail if any
member is an incompatible type.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.property">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">property</span></span><a class="headerlink" href="#enum.property" title="Link to this definition">¶</a></dt>
<dd><p>A decorator similar to the built-in <em>property</em>, but specifically for
enumerations.  It allows member attributes to have the same names as members
themselves.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>the <em>property</em> and the member must be defined in separate classes;
for example, the <em>value</em> and <em>name</em> attributes are defined in the
<em>Enum</em> class, and <em>Enum</em> subclasses can define members with the
names <code class="docutils literal notranslate"><span class="pre">value</span></code> and <code class="docutils literal notranslate"><span class="pre">name</span></code>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.unique">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">unique</span></span><a class="headerlink" href="#enum.unique" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> decorator specifically for enumerations.  It searches an
enumeration’s <a class="reference internal" href="#enum.EnumType.__members__" title="enum.EnumType.__members__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__members__</span></code></a>, gathering any aliases it finds; if any are
found <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised with the details:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">unique</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@unique</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">Mistake</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ONE</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TWO</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">THREE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">FOUR</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">duplicate values found in &lt;enum &#39;Mistake&#39;&gt;: FOUR -&gt; THREE</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.verify">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">verify</span></span><a class="headerlink" href="#enum.verify" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="../reference/compound_stmts.html#class"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">class</span></code></a> decorator specifically for enumerations.  Members from
<a class="reference internal" href="#enum.EnumCheck" title="enum.EnumCheck"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumCheck</span></code></a> are used to specify which constraints should be checked
on the decorated enumeration.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.member">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">member</span></span><a class="headerlink" href="#enum.member" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for use in enums: its target will become a member.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.nonmember">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">nonmember</span></span><a class="headerlink" href="#enum.nonmember" title="Link to this definition">¶</a></dt>
<dd><p>A decorator for use in enums: its target will not become a member.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.global_enum">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">global_enum</span></span><a class="headerlink" href="#enum.global_enum" title="Link to this definition">¶</a></dt>
<dd><p>A decorator to change the <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str()</span></code></a> and <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> of an enum
to show its members as belonging to the module instead of its class.
Should only be used when the enum members are exported
to the module global namespace (see <a class="reference internal" href="re.html#re.RegexFlag" title="re.RegexFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">re.RegexFlag</span></code></a> for an example).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="enum.show_flag_values">
<span class="sig-prename descclassname"><span class="pre">enum.</span></span><span class="sig-name descname"><span class="pre">show_flag_values</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#enum.show_flag_values" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of all power-of-two integers contained in a flag <em>value</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
<hr class="docutils" />
<section id="notes">
<h2>Notes<a class="headerlink" href="#notes" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>, <a class="reference internal" href="#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a>, and <a class="reference internal" href="#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a></p>
<blockquote>
<div><p>These three enum types are designed to be drop-in replacements for existing
integer- and string-based values; as such, they have extra limitations:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">__str__</span></code> uses the value and not the name of the enum member</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">__format__</span></code>, because it uses <code class="docutils literal notranslate"><span class="pre">__str__</span></code>, will also use the value of
the enum member instead of its name</p></li>
</ul>
<p>If you do not need/want those limitations, you can either create your own
base class by mixing in the <code class="docutils literal notranslate"><span class="pre">int</span></code> or <code class="docutils literal notranslate"><span class="pre">str</span></code> type yourself:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyIntEnum</span><span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">pass</span>
</pre></div>
</div>
<p>or you can reassign the appropriate <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str()</span></code></a>, etc., in your enum:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">IntEnum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyIntEnum</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="fm">__str__</span> <span class="o">=</span> <span class="n">Enum</span><span class="o">.</span><span class="fm">__str__</span>
</pre></div>
</div>
</div></blockquote>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
<li><a class="reference internal" href="#data-types">Data Types</a><ul>
<li><a class="reference internal" href="#supported-dunder-names">Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names</a></li>
<li><a class="reference internal" href="#supported-sunder-names">Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names</a></li>
</ul>
</li>
<li><a class="reference internal" href="#utilities-and-decorators">Utilities and Decorators</a></li>
<li><a class="reference internal" href="#notes">Notes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="reprlib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="graphlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/enum.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="graphlib.html" title="graphlib — Functionality to operate with graph-like structures"
             >next</a> |</li>
        <li class="right" >
          <a href="reprlib.html" title="reprlib — Alternate repr() implementation"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>