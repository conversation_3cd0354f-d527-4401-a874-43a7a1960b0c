<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="API and ABI Versioning" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/apiabiversion.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="CPython exposes its version number in the following macros. Note that these correspond to the version code is built with, not necessarily the version used at run time. See C API Stability for a dis..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="CPython exposes its version number in the following macros. Note that these correspond to the version code is built with, not necessarily the version used at run time. See C API Stability for a dis..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>API and ABI Versioning &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Installing Python Modules" href="../installing/index.html" />
    <link rel="prev" title="Supporting Cyclic Garbage Collection" href="gcsupport.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/apiabiversion.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gcsupport.html"
                          title="previous chapter">Supporting Cyclic Garbage Collection</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../installing/index.html"
                          title="next chapter">Installing Python Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/apiabiversion.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../installing/index.html" title="Installing Python Modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="gcsupport.html" title="Supporting Cyclic Garbage Collection"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API and ABI Versioning</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="api-and-abi-versioning">
<span id="apiabiversion"></span><h1>API and ABI Versioning<a class="headerlink" href="#api-and-abi-versioning" title="Link to this heading">¶</a></h1>
<p>CPython exposes its version number in the following macros.
Note that these correspond to the version code is <strong>built</strong> with,
not necessarily the version used at <strong>run time</strong>.</p>
<p>See <a class="reference internal" href="stable.html#stable"><span class="std std-ref">C API Stability</span></a> for a discussion of API and ABI stability across versions.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MAJOR_VERSION">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MAJOR_VERSION</span></span></span><a class="headerlink" href="#c.PY_MAJOR_VERSION" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">3</span></code> in <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MINOR_VERSION">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MINOR_VERSION</span></span></span><a class="headerlink" href="#c.PY_MINOR_VERSION" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">4</span></code> in <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_MICRO_VERSION">
<span class="sig-name descname"><span class="n"><span class="pre">PY_MICRO_VERSION</span></span></span><a class="headerlink" href="#c.PY_MICRO_VERSION" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">1</span></code> in <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code>.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_RELEASE_LEVEL">
<span class="sig-name descname"><span class="n"><span class="pre">PY_RELEASE_LEVEL</span></span></span><a class="headerlink" href="#c.PY_RELEASE_LEVEL" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">a</span></code> in <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code>.
This can be <code class="docutils literal notranslate"><span class="pre">0xA</span></code> for alpha, <code class="docutils literal notranslate"><span class="pre">0xB</span></code> for beta, <code class="docutils literal notranslate"><span class="pre">0xC</span></code> for release
candidate or <code class="docutils literal notranslate"><span class="pre">0xF</span></code> for final.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_RELEASE_SERIAL">
<span class="sig-name descname"><span class="n"><span class="pre">PY_RELEASE_SERIAL</span></span></span><a class="headerlink" href="#c.PY_RELEASE_SERIAL" title="Link to this definition">¶</a><br /></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">2</span></code> in <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code>. Zero for final releases.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PY_VERSION_HEX">
<span class="sig-name descname"><span class="n"><span class="pre">PY_VERSION_HEX</span></span></span><a class="headerlink" href="#c.PY_VERSION_HEX" title="Link to this definition">¶</a><br /></dt>
<dd><p>The Python version number encoded in a single integer.</p>
<p>The underlying version information can be found by treating it as a 32 bit
number in the following manner:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Bytes</p></th>
<th class="head"><p>Bits (big endian order)</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Value for <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code></p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>1</p></td>
<td><p>1-8</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PY_MAJOR_VERSION</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0x03</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>2</p></td>
<td><p>9-16</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PY_MINOR_VERSION</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0x04</span></code></p></td>
</tr>
<tr class="row-even"><td><p>3</p></td>
<td><p>17-24</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PY_MICRO_VERSION</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0x01</span></code></p></td>
</tr>
<tr class="row-odd"><td rowspan="2"><p>4</p></td>
<td><p>25-28</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PY_RELEASE_LEVEL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0xA</span></code></p></td>
</tr>
<tr class="row-even"><td><p>29-32</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">PY_RELEASE_SERIAL</span></code></p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">0x2</span></code></p></td>
</tr>
</tbody>
</table>
<p>Thus <code class="docutils literal notranslate"><span class="pre">3.4.1a2</span></code> is hexversion <code class="docutils literal notranslate"><span class="pre">0x030401a2</span></code> and <code class="docutils literal notranslate"><span class="pre">3.10.0</span></code> is
hexversion <code class="docutils literal notranslate"><span class="pre">0x030a00f0</span></code>.</p>
<p>Use this for numeric comparisons, e.g. <code class="docutils literal notranslate"><span class="pre">#if</span> <span class="pre">PY_VERSION_HEX</span> <span class="pre">&gt;=</span> <span class="pre">...</span></code>.</p>
<p>This version is also available via the symbol <a class="reference internal" href="#c.Py_Version" title="Py_Version"><code class="xref c c-var docutils literal notranslate"><span class="pre">Py_Version</span></code></a>.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_Version">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_Version</span></span></span><a class="headerlink" href="#c.Py_Version" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.11.</em><p>The Python runtime version number encoded in a single constant integer, with
the same format as the <a class="reference internal" href="#c.PY_VERSION_HEX" title="PY_VERSION_HEX"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PY_VERSION_HEX</span></code></a> macro.
This contains the Python version used at run time.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<p>All the given macros are defined in <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Include/patchlevel.h">Include/patchlevel.h</a>.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gcsupport.html"
                          title="previous chapter">Supporting Cyclic Garbage Collection</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../installing/index.html"
                          title="next chapter">Installing Python Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/apiabiversion.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="../installing/index.html" title="Installing Python Modules"
             >next</a> |</li>
        <li class="right" >
          <a href="gcsupport.html" title="Supporting Cyclic Garbage Collection"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API and ABI Versioning</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>