# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_profile
# 
# Translators:
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"!<br/>\n"
"            Did not receive it?"
msgstr ""
"!<br/>\n"
"            Etkö saanut sitä?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "(not verified)"
msgstr "(ei vahvistettu)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
". Collect points on the forum or on the eLearning platform. Those points "
"will make you reach new ranks."
msgstr ""
". Kerää pisteitä foorumilla tai verkko-oppimisen alustalla. Nällä pisteillä "
"saavutat uusia sijoituksia."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid ". Try another search."
msgstr ". Kokeile toista hakua."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right\"/> Get Badges"
msgstr "<i class=\"fa fa-arrow-right\"/> Hanki merkit"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-close me-1\"/>Cancel"
msgstr "<i class=\"fa fa-close me-1\"/>Peruuta"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"
msgstr ""
"<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" "
"title=\"Muokkaa\"/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-1\"/>EDIT"
msgstr "<i class=\"fa fa-pencil me-1\"/>MUOKKAA"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Muokkaa"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-2\"/>EDIT PROFILE"
msgstr "<i class=\"fa fa-pencil me-2\"/>MUOKKAA PROFIILIA"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Badges"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Kaikki merkit"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right\"/> All Badges"
msgstr "<i class=\"oi oi-arrow-right\"/> Kaikki merkit"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> palkitut käyttäjät</i>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Maa...</option>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold me-2\">Current rank:</small>"
msgstr "<small class=\"fw-bold me-2\">Nykyinen sijoitus:</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Badges</small>"
msgstr "<small class=\"fw-bold\">Merkit</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Joined</small>"
msgstr "<small class=\"fw-bold\">Liittynyt</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Biography</span>"
msgstr "<span class=\"fw-bold\">Elämäkerta</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">City</span>"
msgstr "<span class=\"fw-bold\">Kaupunki</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Country</span>"
msgstr "<span class=\"fw-bold\">Maa</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Email</span>"
msgstr "<span class=\"fw-bold\">Sähköposti</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Name</span>"
msgstr "<span class=\"fw-bold\">Nimi</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Public Profile</span>"
msgstr "<span class=\"fw-bold\">Julkinen profiili</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Website</span>"
msgstr "<span class=\"fw-bold\">Verkkosivusto</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Badges</span>"
msgstr "<span class=\"text-muted small fw-bold\">Merkit</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">XP</span>"
msgstr "<span class=\"text-muted small fw-bold\">XP</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">Badges</span>"
msgstr "<span class=\"text-muted\">Merkit</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">XP</span>"
msgstr "<span class=\"text-muted\">Kokemuspisteet</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"<span id=\"email_validated_message\">Congratulations! Your email has just "
"been validated.</span>"
msgstr ""
"<span id=\"email_validated_message\">Onnittelut! Sähköpostisi on juuri "
"vahvistettu.</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "<strong class=\"mb-3 text-white me-2\">Rank by:</strong>"
msgstr "<strong class=\"mb-3 text-white me-2\">Järjestä:</strong>"

#. module: website_profile
#: model:mail.template,body_html:website_profile.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profile validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            You have been invited to validate your email in order to get access to \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" website.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profiilin vahvistus\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hei <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            Pyydämme sinua vahvistamaan tilisi saadaksesi pääsyn \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" -sivustolle.\n"
"                            Vahvistaaksesi sähköpostin, klikkaa linkkiä:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Vahvista tilini\n"
"                                </a>\n"
"                            </div>\n"
"                            Kiitos osallistumisestasi!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Ohjelmistoa pyörittää <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Correct your email address</u>"
msgstr "<u>Korjaa sähköpostiosoitteesi</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Send Again</u>"
msgstr "<u>Lähetä uudelleen</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>here</u>"
msgstr "<u>täällä</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "About"
msgstr "Tietoja"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All Users"
msgstr "Kaikki käyttäjät"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All time"
msgstr "Kautta aikojen"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Badges"
msgstr "Ansiomerkit"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "Badges are your collection of achievements. Wear them proudly! <br/>"
msgstr "Merkit ovat kokoelma saavutuksiasi. Käytä niitä ylpeästi! <br/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"                        you receive badges for being especially helpful.<br class=\"d-none d-lg-inline-block\"/>Badges\n"
"                        appear on your profile page, and your posts."
msgstr ""
"Sen lisäksi, että saat mainetta kysymyksilläsi ja vastauksillasi,\n"
"                        saat merkkejä siitä, että olet erityisen avulias.<br class=\"d-none d-lg-inline-block\"/>Merkit\n"
"                        näkyvät profiilisivullasi ja viesteissäsi."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Biography"
msgstr "Kuvaus itsestäsi"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__can_publish
msgid "Can Publish"
msgstr "Voi julkaista"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Clear"
msgstr "Pyyhi"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Close"
msgstr "Sulje"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit"
msgstr "Muokkaa"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit Profile"
msgstr "Profiilin muokkaus"

#. module: website_profile
#: model:mail.template,name:website_profile.validation_email
msgid "Forum: Email Verification"
msgstr "Foorumi: Sähköpostivarmennus"

#. module: website_profile
#: model:ir.model,name:website_profile.model_gamification_badge
msgid "Gamification Badge"
msgstr "Pelillistämisen ansiomerkki"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "Get"
msgstr "Hae"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Home"
msgstr "Etusivu"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I earn badges?"
msgstr "Miten ansaitsen merkkejä?"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I score more points?"
msgstr "Miten saan enemmän pisteitä?"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__is_published
msgid "Is Published"
msgstr "On julkaistu"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Keep learning with"
msgstr "Jatka oppimista"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_website__karma_profile_min
msgid "Minimal karma to see other user's profile"
msgstr "Minimaalinen karma nähdäksesi muiden käyttäjien profiilin"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Mobile sub-nav"
msgstr "Mobiilinäkymän ala-navigaatio"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "More info"
msgstr "Lisätiedot"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Nav"
msgstr "Uusi"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Next rank:"
msgstr "Seuraava taso:"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_main
msgid "No Leaderboard Yet :("
msgstr "Ei tulostaulukkoa vielä :("

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "No badges yet!"
msgstr "Ei vielä merkkejä!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid "No user found for"
msgstr "Käyttäjää ei löydy kohteelle"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "Not have enough karma to view other users' profile."
msgstr ""
"Sinulla ei ole tarpeeksi karmaa nähdäksesi muiden käyttäjien profiileja."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""
"Anna toimiva sähköpostiosoite vastaanottaaksesi päivityksiä uusista "
"vastauksista ja kommenteista."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Ranks"
msgstr "Sijat"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_access_denied
msgid "Return to the website."
msgstr "Palaa verkkosivulle."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search"
msgstr "Hae"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search users"
msgstr "Etsi käyttäjiä"

#. module: website_profile
#: model:mail.template,description:website_profile.validation_email
msgid "Sent to forum visitors to confirm their mail address"
msgstr ""
"Lähetetään foorumin kävijöille heidän sähköpostiosoitteensa vahvistamiseksi"

#. module: website_profile
#: model:ir.model.fields,help:website_profile.field_gamification_badge__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumentin URL-osoite verkkosivustolla."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This month"
msgstr "Tässä kuussa"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "This profile is private!"
msgstr "Tämä profiili on yksityinen!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This week"
msgstr "Tällä viikolla"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "Unpublished"
msgstr "Julkaisematon"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Update"
msgstr "Päivitä"

#. module: website_profile
#: model:ir.model,name:website_profile.model_res_users
msgid "User"
msgstr "Käyttäjä"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "User rank"
msgstr "Käyttäjän paikka pistekisassa"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Users"
msgstr "Käyttäjät"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Verification Email sent to"
msgstr "Vahvistussähköposti lähetetään osoitteeseen"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_published
msgid "Visible on current website"
msgstr "Näkyy nykysellä verkkosivulla"

#. module: website_profile
#: model:ir.model,name:website_profile.model_website
msgid "Website"
msgstr "Verkkosivu"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_url
msgid "Website URL"
msgstr "Verkkosivuston osoite"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "When you finish a course or reach milestones, you're awarded badges."
msgstr ""
"Kun suoritat kurssin loppuun tai saavutat virstanpylväitä, saat merkkejä."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Write a few words about yourself..."
msgstr "Kirjoita muutama sana itsestäsi..."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "XP"
msgstr "XP"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
"You can score more points by answering quizzes at the end of each course "
"content. Points can also be earned on the forum. Follow this link to the "
"guidelines of the forum."
msgstr ""
"Voit saada lisää pisteitä vastaamalla kunkin kurssin sisällön lopussa "
"oleviin tietokilpailuihin. Pisteitä voi ansaita myös foorumilla. Seuraa tätä"
" linkkiä foorumin ohjeisiin."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"Your Account has not yet been verified.<br/>\n"
"            Click"
msgstr ""
"Tiliäsi ei ole vielä vahvistettu.<br/>\n"
"            Klikkaa"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Your account does not have an email set up. Please set it up on"
msgstr "Tilillesi ei ole määritetty sähköpostia. Ole hyvä ja määritä se"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "breadcrumb"
msgstr "murupolku"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "or"
msgstr "tai"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "point"
msgstr "piste"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this month"
msgstr "tämä kuukausi"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this week"
msgstr "tämä viikko"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "to receive a verification email"
msgstr "saadaksesi vahvistussähköpostin"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "xp"
msgstr "xp"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"xp\n"
"                        to level up!"
msgstr ""
"xp\n"
"                        tason nousuun!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "your account settings"
msgstr "tilisi asetukset"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"
msgstr ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"

#. module: website_profile
#: model:mail.template,subject:website_profile.validation_email
msgid "{{ object.company_id.name }} Profile validation"
msgstr "{{ object.company_id.name }} Profiilin validointi"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "└ Users"
msgstr "└ Käyttäjät"
