<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="mail_test_rating_rule_mc" model="ir.rule">
        <field name="name">TestRating: Multi Company</field>
        <field name="model_id" ref="test_mail_full.model_mail_test_rating"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>
    <record id="mail_test_rating_rule_portal" model="ir.rule">
        <field name="name">TestRating: Portal should follow</field>
        <field name="model_id" ref="test_mail_full.model_mail_test_rating"/>
        <field name="domain_force">[('message_partner_ids', 'in', [user.partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
    </record>

</odoo>
