<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="reprlib — Alternate repr() implementation" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/reprlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/reprlib.py The reprlib module provides a means for producing object representations with limits on the size of the resulting strings. This is used in the Python debugger and may be..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/reprlib.py The reprlib module provides a means for producing object representations with limits on the size of the resulting strings. This is used in the Python debugger and may be..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>reprlib — Alternate repr() implementation &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="enum — Support for enumerations" href="enum.html" />
    <link rel="prev" title="pprint — Data pretty printer" href="pprint.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/reprlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a><ul>
<li><a class="reference internal" href="#repr-objects">Repr Objects</a></li>
<li><a class="reference internal" href="#subclassing-repr-objects">Subclassing Repr Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pprint.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="enum.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/reprlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="enum.html" title="enum — Support for enumerations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pprint.html" title="pprint — Data pretty printer"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-reprlib">
<span id="reprlib-alternate-repr-implementation"></span><h1><a class="reference internal" href="#module-reprlib" title="reprlib: Alternate repr() implementation with size limits."><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code></a> — Alternate <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> implementation<a class="headerlink" href="#module-reprlib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/reprlib.py">Lib/reprlib.py</a></p>
<hr class="docutils" />
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> module provides a means for producing object representations
with limits on the size of the resulting strings. This is used in the Python
debugger and may be useful in other contexts as well.</p>
<p>This module provides a class, an instance, and a function:</p>
<dl class="py class">
<dt class="sig sig-object py" id="reprlib.Repr">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">reprlib.</span></span><span class="sig-name descname"><span class="pre">Repr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxlevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxtuple</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxlist</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxarray</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">5</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxdict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">4</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxfrozenset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxdeque</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxstring</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">30</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxlong</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">40</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxother</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">30</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fillvalue</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'...'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reprlib.Repr" title="Link to this definition">¶</a></dt>
<dd><p>Class which provides formatting services useful in implementing functions
similar to the built-in <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>; size limits for  different object types
are added to avoid the generation of representations which are excessively long.</p>
<p>The keyword arguments of the constructor can be used as a shortcut to set the
attributes of the <a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a> instance. Which means that the following
initialization:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">aRepr</span> <span class="o">=</span> <span class="n">reprlib</span><span class="o">.</span><span class="n">Repr</span><span class="p">(</span><span class="n">maxlevel</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
<p>Is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">aRepr</span> <span class="o">=</span> <span class="n">reprlib</span><span class="o">.</span><span class="n">Repr</span><span class="p">()</span>
<span class="n">aRepr</span><span class="o">.</span><span class="n">maxlevel</span> <span class="o">=</span> <span class="mi">3</span>
</pre></div>
</div>
<p>See section <a class="reference internal" href="#id1">Repr Objects</a> for more information about <a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a>
attributes.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Allow attributes to be set via keyword arguments.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="reprlib.aRepr">
<span class="sig-prename descclassname"><span class="pre">reprlib.</span></span><span class="sig-name descname"><span class="pre">aRepr</span></span><a class="headerlink" href="#reprlib.aRepr" title="Link to this definition">¶</a></dt>
<dd><p>This is an instance of <a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a> which is used to provide the
<a class="reference internal" href="#reprlib.repr" title="reprlib.repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> function described below.  Changing the attributes of this
object will affect the size limits used by <a class="reference internal" href="#reprlib.repr" title="reprlib.repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> and the Python
debugger.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="reprlib.repr">
<span class="sig-prename descclassname"><span class="pre">reprlib.</span></span><span class="sig-name descname"><span class="pre">repr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reprlib.repr" title="Link to this definition">¶</a></dt>
<dd><p>This is the <a class="reference internal" href="#reprlib.Repr.repr" title="reprlib.Repr.repr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">repr()</span></code></a> method of <code class="docutils literal notranslate"><span class="pre">aRepr</span></code>.  It returns a string
similar to that returned by the built-in function of the same name, but with
limits on most sizes.</p>
</dd></dl>

<p>In addition to size-limiting tools, the module also provides a decorator for
detecting recursive calls to <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a> and substituting a
placeholder string instead.</p>
<dl class="py function" id="index-0">
<dt class="sig sig-object py" id="reprlib.recursive_repr">
<span class="sig-prename descclassname"><span class="pre">&#64;</span></span><span class="sig-prename descclassname"><span class="pre">reprlib.</span></span><span class="sig-name descname"><span class="pre">recursive_repr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fillvalue</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'...'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reprlib.recursive_repr" title="Link to this definition">¶</a></dt>
<dd><p>Decorator for <a class="reference internal" href="../reference/datamodel.html#object.__repr__" title="object.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a> methods to detect recursive calls within the
same thread.  If a recursive call is made, the <em>fillvalue</em> is returned,
otherwise, the usual <code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code> call is made.  For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">reprlib</span> <span class="kn">import</span> <span class="n">recursive_repr</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyList</span><span class="p">(</span><span class="nb">list</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nd">@recursive_repr</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="s1">&#39;&lt;&#39;</span> <span class="o">+</span> <span class="s1">&#39;|&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="nb">map</span><span class="p">(</span><span class="nb">repr</span><span class="p">,</span> <span class="bp">self</span><span class="p">))</span> <span class="o">+</span> <span class="s1">&#39;&gt;&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span> <span class="o">=</span> <span class="n">MyList</span><span class="p">(</span><span class="s1">&#39;abc&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">m</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">m</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s1">&#39;x&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">m</span><span class="p">)</span>
<span class="go">&lt;&#39;a&#39;|&#39;b&#39;|&#39;c&#39;|...|&#39;x&#39;&gt;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<section id="repr-objects">
<span id="id1"></span><h2>Repr Objects<a class="headerlink" href="#repr-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a> instances provide several attributes which can be used to provide
size limits for the representations of different object types,  and methods
which format specific object types.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.fillvalue">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">fillvalue</span></span><a class="headerlink" href="#reprlib.Repr.fillvalue" title="Link to this definition">¶</a></dt>
<dd><p>This string is displayed for recursive references. It defaults to
<code class="docutils literal notranslate"><span class="pre">...</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.maxlevel">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxlevel</span></span><a class="headerlink" href="#reprlib.Repr.maxlevel" title="Link to this definition">¶</a></dt>
<dd><p>Depth limit on the creation of recursive representations.  The default is <code class="docutils literal notranslate"><span class="pre">6</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.maxdict">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxdict</span></span><a class="headerlink" href="#reprlib.Repr.maxdict" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxlist">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxlist</span></span><a class="headerlink" href="#reprlib.Repr.maxlist" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxtuple">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxtuple</span></span><a class="headerlink" href="#reprlib.Repr.maxtuple" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxset">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxset</span></span><a class="headerlink" href="#reprlib.Repr.maxset" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxfrozenset">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxfrozenset</span></span><a class="headerlink" href="#reprlib.Repr.maxfrozenset" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxdeque">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxdeque</span></span><a class="headerlink" href="#reprlib.Repr.maxdeque" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="reprlib.Repr.maxarray">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxarray</span></span><a class="headerlink" href="#reprlib.Repr.maxarray" title="Link to this definition">¶</a></dt>
<dd><p>Limits on the number of entries represented for the named object type.  The
default is <code class="docutils literal notranslate"><span class="pre">4</span></code> for <a class="reference internal" href="#reprlib.Repr.maxdict" title="reprlib.Repr.maxdict"><code class="xref py py-attr docutils literal notranslate"><span class="pre">maxdict</span></code></a>, <code class="docutils literal notranslate"><span class="pre">5</span></code> for <a class="reference internal" href="#reprlib.Repr.maxarray" title="reprlib.Repr.maxarray"><code class="xref py py-attr docutils literal notranslate"><span class="pre">maxarray</span></code></a>, and  <code class="docutils literal notranslate"><span class="pre">6</span></code> for
the others.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.maxlong">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxlong</span></span><a class="headerlink" href="#reprlib.Repr.maxlong" title="Link to this definition">¶</a></dt>
<dd><p>Maximum number of characters in the representation for an integer.  Digits
are dropped from the middle.  The default is <code class="docutils literal notranslate"><span class="pre">40</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.maxstring">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxstring</span></span><a class="headerlink" href="#reprlib.Repr.maxstring" title="Link to this definition">¶</a></dt>
<dd><p>Limit on the number of characters in the representation of the string.  Note
that the “normal” representation of the string is used as the character source:
if escape sequences are needed in the representation, these may be mangled when
the representation is shortened.  The default is <code class="docutils literal notranslate"><span class="pre">30</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.maxother">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">maxother</span></span><a class="headerlink" href="#reprlib.Repr.maxother" title="Link to this definition">¶</a></dt>
<dd><p>This limit is used to control the size of object types for which no specific
formatting method is available on the <a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a> object. It is applied in a
similar manner as <a class="reference internal" href="#reprlib.Repr.maxstring" title="reprlib.Repr.maxstring"><code class="xref py py-attr docutils literal notranslate"><span class="pre">maxstring</span></code></a>.  The default is <code class="docutils literal notranslate"><span class="pre">20</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="reprlib.Repr.indent">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">indent</span></span><a class="headerlink" href="#reprlib.Repr.indent" title="Link to this definition">¶</a></dt>
<dd><p>If this attribute is set to <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default), the output is formatted
with no line breaks or indentation, like the standard <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>.
For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">example</span> <span class="o">=</span> <span class="p">[</span>
<span class="gp">... </span>    <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="p">{</span><span class="s1">&#39;a&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">:</span> <span class="s1">&#39;spam eggs&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">:</span> <span class="p">{</span><span class="mi">3</span><span class="p">:</span> <span class="mf">4.5</span><span class="p">,</span> <span class="mi">6</span><span class="p">:</span> <span class="p">[]}},</span> <span class="s1">&#39;ham&#39;</span><span class="p">]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">reprlib</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">aRepr</span> <span class="o">=</span> <span class="n">reprlib</span><span class="o">.</span><span class="n">Repr</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">aRepr</span><span class="o">.</span><span class="n">repr</span><span class="p">(</span><span class="n">example</span><span class="p">))</span>
<span class="go">[1, &#39;spam&#39;, {&#39;a&#39;: 2, &#39;b&#39;: &#39;spam eggs&#39;, &#39;c&#39;: {3: 4.5, 6: []}}, &#39;ham&#39;]</span>
</pre></div>
</div>
<p>If <a class="reference internal" href="#reprlib.Repr.indent" title="reprlib.Repr.indent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">indent</span></code></a> is set to a string, each recursion level
is placed on its own line, indented by that string:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">aRepr</span><span class="o">.</span><span class="n">indent</span> <span class="o">=</span> <span class="s1">&#39;--&gt;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">aRepr</span><span class="o">.</span><span class="n">repr</span><span class="p">(</span><span class="n">example</span><span class="p">))</span>
<span class="go">[</span>
<span class="go">--&gt;1,</span>
<span class="go">--&gt;&#39;spam&#39;,</span>
<span class="go">--&gt;{</span>
<span class="go">--&gt;--&gt;&#39;a&#39;: 2,</span>
<span class="go">--&gt;--&gt;&#39;b&#39;: &#39;spam eggs&#39;,</span>
<span class="go">--&gt;--&gt;&#39;c&#39;: {</span>
<span class="go">--&gt;--&gt;--&gt;3: 4.5,</span>
<span class="go">--&gt;--&gt;--&gt;6: [],</span>
<span class="go">--&gt;--&gt;},</span>
<span class="go">--&gt;},</span>
<span class="go">--&gt;&#39;ham&#39;,</span>
<span class="go">]</span>
</pre></div>
</div>
<p>Setting <a class="reference internal" href="#reprlib.Repr.indent" title="reprlib.Repr.indent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">indent</span></code></a> to a positive integer value behaves as if it
was set to a string with that number of spaces:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">aRepr</span><span class="o">.</span><span class="n">indent</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">aRepr</span><span class="o">.</span><span class="n">repr</span><span class="p">(</span><span class="n">example</span><span class="p">))</span>
<span class="go">[</span>
<span class="go">    1,</span>
<span class="go">    &#39;spam&#39;,</span>
<span class="go">    {</span>
<span class="go">        &#39;a&#39;: 2,</span>
<span class="go">        &#39;b&#39;: &#39;spam eggs&#39;,</span>
<span class="go">        &#39;c&#39;: {</span>
<span class="go">            3: 4.5,</span>
<span class="go">            6: [],</span>
<span class="go">        },</span>
<span class="go">    },</span>
<span class="go">    &#39;ham&#39;,</span>
<span class="go">]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="reprlib.Repr.repr">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">repr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reprlib.Repr.repr" title="Link to this definition">¶</a></dt>
<dd><p>The equivalent to the built-in <a class="reference internal" href="functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> that uses the formatting imposed by
the instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="reprlib.Repr.repr1">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">repr1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#reprlib.Repr.repr1" title="Link to this definition">¶</a></dt>
<dd><p>Recursive implementation used by <a class="reference internal" href="#reprlib.Repr.repr" title="reprlib.Repr.repr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">repr()</span></code></a>.  This uses the type of <em>obj</em> to
determine which formatting method to call, passing it <em>obj</em> and <em>level</em>.  The
type-specific methods should call <a class="reference internal" href="#reprlib.Repr.repr1" title="reprlib.Repr.repr1"><code class="xref py py-meth docutils literal notranslate"><span class="pre">repr1()</span></code></a> to perform recursive formatting,
with <code class="docutils literal notranslate"><span class="pre">level</span> <span class="pre">-</span> <span class="pre">1</span></code> for the value of <em>level</em> in the recursive  call.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">Repr.</span></span><span class="sig-name descname"><span class="pre">repr_TYPE</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">obj</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Formatting methods for specific types are implemented as methods with a name
based on the type name.  In the method name, <strong>TYPE</strong> is replaced by
<code class="docutils literal notranslate"><span class="pre">'_'.join(type(obj).__name__.split())</span></code>. Dispatch to these methods is
handled by <a class="reference internal" href="#reprlib.Repr.repr1" title="reprlib.Repr.repr1"><code class="xref py py-meth docutils literal notranslate"><span class="pre">repr1()</span></code></a>. Type-specific methods which need to recursively
format a value should call <code class="docutils literal notranslate"><span class="pre">self.repr1(subobj,</span> <span class="pre">level</span> <span class="pre">-</span> <span class="pre">1)</span></code>.</p>
</dd></dl>

</section>
<section id="subclassing-repr-objects">
<span id="subclassing-reprs"></span><h2>Subclassing Repr Objects<a class="headerlink" href="#subclassing-repr-objects" title="Link to this heading">¶</a></h2>
<p>The use of dynamic dispatching by <a class="reference internal" href="#reprlib.Repr.repr1" title="reprlib.Repr.repr1"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Repr.repr1()</span></code></a> allows subclasses of
<a class="reference internal" href="#reprlib.Repr" title="reprlib.Repr"><code class="xref py py-class docutils literal notranslate"><span class="pre">Repr</span></code></a> to add support for additional built-in object types or to modify
the handling of types already supported. This example shows how special support
for file objects could be added:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">reprlib</span>
<span class="kn">import</span> <span class="nn">sys</span>

<span class="k">class</span> <span class="nc">MyRepr</span><span class="p">(</span><span class="n">reprlib</span><span class="o">.</span><span class="n">Repr</span><span class="p">):</span>

    <span class="k">def</span> <span class="nf">repr_TextIOWrapper</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">,</span> <span class="n">level</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">obj</span><span class="o">.</span><span class="n">name</span> <span class="ow">in</span> <span class="p">{</span><span class="s1">&#39;&lt;stdin&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;stdout&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;stderr&gt;&#39;</span><span class="p">}:</span>
            <span class="k">return</span> <span class="n">obj</span><span class="o">.</span><span class="n">name</span>
        <span class="k">return</span> <span class="nb">repr</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>

<span class="n">aRepr</span> <span class="o">=</span> <span class="n">MyRepr</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">aRepr</span><span class="o">.</span><span class="n">repr</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="p">))</span>         <span class="c1"># prints &#39;&lt;stdin&gt;&#39;</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;stdin&gt;
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a><ul>
<li><a class="reference internal" href="#repr-objects">Repr Objects</a></li>
<li><a class="reference internal" href="#subclassing-repr-objects">Subclassing Repr Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pprint.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code> — Data pretty printer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="enum.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/reprlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="enum.html" title="enum — Support for enumerations"
             >next</a> |</li>
        <li class="right" >
          <a href="pprint.html" title="pprint — Data pretty printer"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">reprlib</span></code> — Alternate <code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code> implementation</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>