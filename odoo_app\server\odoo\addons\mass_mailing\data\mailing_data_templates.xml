<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">
    <!--
    Reference: https://litmus.com/community/learning/24-how-to-code-a-responsive-email-from-scratch
    https://www.campaignmonitor.com/css/link-element/link-in-head/
    -->
    <template id="mass_mailing_mail_layout">
        &lt;!DOCTYPE html&gt;
        <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
                <meta name="format-detection" content="telephone=no"/>
                <meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=no;"/>
                <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />

                <t t-out="mailing_style"/>

                <!--
                    Prevent Outlook from distorting images with DPI scaling (see
                    https://www.htmlemailcheck.com/knowledge-base/dpi-scaling-in-outlook-2007-2013/):
                -->
                <!--[if mso]>
                    <xml>
                        <o:OfficeDocumentSettings>
                            <o:AllowPNG/>
                            <o:PixelsPerInch>96</o:PixelsPerInch>
                        </o:OfficeDocumentSettings>
                    </xml>
                <![endif]-->
            </head>
            <body>
                <t t-out="body"/>
            </body>
        </html>
    </template>
</data>

<data noupdate="0">
    <template id="ab_testing_description" name="Mass Mailing: A/B Test Description">
        <div name="ab_testing_description" class="mb-2">
            <t t-if="mailing.state == 'done' and mailing.sent == 0 and mailing.failed == 0 and mailing.canceled == 0">
                <span>There wasn't enough recipients left for this mailing</span>
            </t>
            <t t-if="mailing.ab_testing_completed">
                <p t-if="mailing.ab_testing_is_winner_mailing">
                    This <t t-out="mailing.mailing_type_description"/> is the winner of the A/B testing campaign and has been sent to all remaining recipients.
                </p>
                <p t-else="">The winner has already been sent. Use <b>Compare Version</b> to get an overview of this A/B testing campaign.</p>
            </t>
            <t t-elif="ab_testing_count >= 2">
                <p>
                    A sample of <b><t t-out="mailing.ab_testing_pc"/>% of recipients</b> will receive this version.<br/>
                    <t t-if="total_ab_testing_pc > 100 and mailing.active">
                        <i class="fa fa-exclamation-triangle text-danger" aria-hidden="true"/>
                        The sum of all percentages for this A/B campaign totals <b class="text-danger"><t t-out="total_ab_testing_pc"/>%</b> of all potential recipients.<br/>
                        <b class="text-danger">Some of the mailings will not be sent</b>, as only 1 email will be sent for each unique recipient in this campaign.
                    </t>
                </p>
                <p>
		    <t t-if="mailing.ab_testing_winner_selection == 'manual'">Don't forget to send your preferred version</t>
		    <t t-elif="not mailing.ab_testing_schedule_datetime">Since the date and time for this test has not been scheduled, don't forget to manually send your preferred version.</t>
                    <t t-else="">
                        Then on <b><t t-out="mailing.ab_testing_schedule_datetime.strftime('%b %d, %Y')"/></b> the <t t-out="mailing.mailing_type_description"/> having the <b><t t-out="ab_testing_winner_selection_description"/></b> will be sent
                    </t> to the remaining recipients.
                </p>
            </t>
            <t t-else="">
                <p>
                    A sample of <b><t t-out="mailing.ab_testing_pc"/>% of recipients</b> will receive this version.
                </p>
                <t t-if="mailing.ab_testing_winner_selection != 'manual'">
                    <p>Try different variations in the campaign to compare their <t t-out="ab_testing_winner_selection_description"/>.</p>
                    <p>Once the best version is identified, we will send the best one to the remaining recipients.</p>
                </t>
                <p t-else="">Use alternative versions to be able to select the winner.</p>
            </t>
        </div>
    </template>

    <template id="mass_mailing.mass_mailing_kpi_link_trackers" name="Marketing: mailing link trackers statistic">
        <table t-if="link_trackers" class="global_table_layout" bgcolor="#ffffff" cellspacing="0" cellpadding="0" align="center" border="0" style="width: 100%;">
            <tr>
                <td style="width: 100%;">
                    <table cellspacing="0" cellpadding="0" border="0" align="center" style="width:100%;">
                        <tr>
                            <td align="left">
                                <span style="color:#282f33; font-size: 15px; font-weight: bold; line-height: 30px">
                                    <t t-esc="'Click Rate Report on %i %s Sent' % (object.expected, mailing_type)"/>
                                </span>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="margin: 0; padding:0;">
                    <table cellspacing="0" cellpadding="0" border="0" align="center" style="width:100%; border-collapse: collapse;">
                        <tr t-att-style="'color: ' + (company.email_secondary_color or '#875a7b') + '; font-size: 16px; font-weight: 500;'">
                            <td style="width: 70%;padding: 10px 0; text-align: center; border: 1px solid #e7e7e7;">Button Label</td>
                            <td style="width: 30%;padding: 10px 0; text-align: center; border: 1px solid #e7e7e7;">%Click (Total)</td>
                        </tr>
                        <tr t-foreach="link_trackers" t-as="link_tracker" style="color: #888888; font-size: 15px; font-weight: 300;">
                            <td style="width: 70%;padding: 10px 0; border: 1px solid #e7e7e7;">
                                <a t-att-href="link_tracker.absolute_url" target="_blank" style="color: #56b3b5; text-decoration: none;" t-esc="link_tracker.label or link_tracker.url"/>
                            </td>
                            <td style="width: 30%;padding: 10px 0; text-align: center;  border: 1px solid #e7e7e7;">
                                <t t-esc="int(link_tracker.count * 100 / object.sent) if object.sent else 0"/>% (<t t-esc="link_tracker.count"/>)
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </template>
</data>
</odoo>
