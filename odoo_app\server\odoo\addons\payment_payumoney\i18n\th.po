# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rasar<PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__code
msgid "Code"
msgstr "โค้ด"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "Merchant Key"
msgstr "คีย์ผู้ขาย"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_provider__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr "ผู้ขาย Salt"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "ไม่พบธุรกรรมที่ตรงกับการอ้างอิง %s"

#. module: payment_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_provider__code__payumoney
#: model:payment.provider,name:payment_payumoney.payment_provider_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_provider
msgid "Payment Provider"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "ธุรกรรมสำหรับการชำระเงิน"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%s)"
msgstr "ข้อมูลที่ได้รับโดยไม่มีการอ้างอิง (%s)"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr "คีย์ที่ใช้ในการระบุบัญชีด้วยเงิน PayU เท่านั้น"

#. module: payment_payumoney
#. odoo-python
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr "การชำระเงินพบข้อผิดพลาดกับรหัส %s"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "รหัสทางเทคนิคของผู้ให้บริการชำระเงินรายนี้"

#. module: payment_payumoney
#: model_terms:ir.ui.view,arch_db:payment_payumoney.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Razorpay</strong>."
msgstr ""
"ผู้ให้บริการรายนี้เลิกใช้แล้ว\n"
"                    ให้พิจารณาปิดการใช้งานและเปลี่ยนไปใช้ <strong>Razorpay</strong>"

#. module: payment_payumoney
#: model_terms:payment.provider,auth_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been authorized."
msgstr "การชำระเงินของคุณได้รับการอนุมัติแล้ว"

#. module: payment_payumoney
#: model_terms:payment.provider,cancel_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been cancelled."
msgstr "การชำระเงินของคุณถูกยกเลิก"

#. module: payment_payumoney
#: model_terms:payment.provider,pending_msg:payment_payumoney.payment_provider_payumoney
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว แต่กำลังรอการอนุมัติ"

#. module: payment_payumoney
#: model_terms:payment.provider,done_msg:payment_payumoney.payment_provider_payumoney
msgid "Your payment has been successfully processed."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว"
