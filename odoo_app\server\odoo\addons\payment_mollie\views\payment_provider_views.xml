<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">Mollie Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position="inside">
                <group invisible="code != 'mollie'">
                    <field name="mollie_api_key" string="API Key" required="code == 'mollie' and state != 'disabled'" password="True"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
