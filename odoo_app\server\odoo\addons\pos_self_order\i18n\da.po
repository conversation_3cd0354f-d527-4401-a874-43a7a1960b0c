# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_self_order
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_self_order
#: model:ir.actions.report,print_report_name:pos_self_order.report_self_order_qr_codes_page
msgid "\"QR codes\""
msgstr "\"QR koder\""

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid ""
"%s is not available anymore, it has thus been removed from your order. "
"Please review your order and validate it again."
msgstr ""
"%s er ikke længere tilgængelig, det er derfor blevet fjernet fra din ordre. "
"Gennemgå venligst din ordre og valider den igen."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "/ Tracker number:"
msgstr "/ Tracker nummer:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"<br/>\n"
"                    URL:"
msgstr ""
"<br/>\n"
"                    URL:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"<span>Please note that the kiosk only works with Adyen &amp; Stripe "
"terminals</span>"
msgstr ""
"<span>Bemærk venligst, at kiosken kun fungerer med Adyen &amp; Stripe "
"terminaler</span>"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
msgid ""
"Access rights of this user will be used when visiting self order website "
"when no session is open."
msgstr ""
"Adgangsrettighederne for denne bruger vil blive brugt, når der besøges "
"selvbestillings hjemmesiden, når der ikke er åbnet en session."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__active
msgid "Active"
msgstr "Aktiv"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add Languages"
msgstr "Tilføj sprog"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Add an image to brand your header."
msgstr "Tilføj et billede for at forbedre din overskrift."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Add images"
msgstr "Tilføj billeder"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Add to Cart"
msgstr "Tilføj til kurv"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Add to cart"
msgstr "Tilføj til kurv"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Adjust the tax rate based on whether customers are dining in or opting for "
"takeout."
msgstr ""
"Juster momssatsen baseret på om kunderne spiser på stedet eller vælger "
"takeaway."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "All the items will be removed from the cart."
msgstr "Alle varer vil blive fjernet fra indkøbskurven."

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Alternative Fiscal Position"
msgstr "Alternativ bogføringsgruppe"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "An error has occurred"
msgstr "Der opstod en fejl"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Any items already sent will not be canceled"
msgstr "Alle varer, der allerede er sendt, vil ikke blive annulleret"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Are you sure you want to cancel this order?"
msgstr "Er du sikker på, at du vil annullere denne ordre?"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
#, python-format
msgid "Available"
msgstr "Til rådighed"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Available Languages"
msgstr "Tilgængelige sprog"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Available in Self"
msgstr "Tilgængelig i Self"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__self_order_available
msgid "Available in Self Order"
msgstr "Tilgængelig i Selvbestilling"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Available interface languages"
msgstr "Tilgængelige grænsefladesprog"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Back"
msgstr "Tilbage"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Cancel"
msgstr "Annullér"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#, python-format
msgid "Cancel Order"
msgstr "Annuller ordre"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.js:0
#, python-format
msgid "Cancel order"
msgstr "Annuller ordre"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Choose the kiosk mode"
msgstr "Vælg kiosktilstand"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Choose the self ordering mode"
msgstr "Vælg selvbestillingstilstand"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Choose when the customer will pay"
msgstr "Vælg, hvornår kunden skal betale"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#, python-format
msgid "Choose your"
msgstr "Vælg din"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Choose your eating location"
msgstr "Vælg dit spisested"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
#, python-format
msgid "Close"
msgstr "Luk"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Close Session"
msgstr "Luk session"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Combo"
msgstr "Kombination"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "Kombinations linjer"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "Kombinations forælder"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order_line__combo_id
msgid "Combo line reference"
msgstr "Kombinationslinje reference"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Confirm"
msgstr "Bekræft"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Connection lost, please try again later"
msgstr "Forbindelsen blev tabt, prøv venligst igen senere"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Could you please confirm your table number?"
msgstr "Kan du venligst bekræfte dit bordnummer?"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.js:0
#, python-format
msgid "Current"
msgstr "Aktuel"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "Custom Links"
msgstr "Tilpassede links"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_self_order_custom_link
msgid ""
"Custom links that the restaurant can configure to be displayed on the self "
"order screen"
msgstr ""
"Tilpassede links, som restauranten kan konfigurere til at blive vist på "
"selvbestillingsskærmen"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Customize Header"
msgstr "Tilpas header"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__danger
msgid "Danger"
msgstr "Fare"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__dark
msgid "Dark"
msgstr "Mørk"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default"
msgstr "Standard"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default Language"
msgstr "Standardsprog"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_default_user_id
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_default_user_id
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Default User"
msgstr "Standardbruger"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_default_language_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_default_language_id
msgid "Default language for the kiosk mode"
msgstr "Standard sprog for kiosktilstand"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__nothing
msgid "Disable"
msgstr "Deaktivér"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/cancel_popup/cancel_popup.xml:0
#: code:addons/pos_self_order/static/src/app/components/language_popup/language_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Discard"
msgstr "Kassér"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Download QR Codes"
msgstr "Download QR-koder"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Each Order"
msgstr "Hver ordre"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,\n"
"                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:."
msgstr ""
"Hvert bord din gulvplan er tildelt en unik QR-kode baseret på din konfiguration. Af sikkerhedsmæssige årsager,\n"
"er både point of sale og bordnavnene krypteret i den genererede URL, som vist i eksemplet nedenfor:."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Eat In"
msgstr "Spis på stedet"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Eat in / Take out"
msgstr "Spis på stedet / Tag med"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Edit"
msgstr "Rediger"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/attribute_selection/attribute_selection.xml:0
#, python-format
msgid "Enter your custom value"
msgstr "Indtast din tilpassede værdi"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Feel free to use and print this QR code as many times as needed according to"
" your requirements."
msgstr ""
"Du er velkommen til at bruge og udskrive denne QR-kode så mange gange som "
"nødvendigt i henhold til dine krav."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Fiscal Positions"
msgstr "Bogføringsgrupper"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Follow instructions on the terminal"
msgstr "Følg instruktionerne på terminalen"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "From your Kiosk, open this URL:"
msgstr "Fra din Kiosk, åbn denne URL:"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Generic"
msgstr "Generisk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Get a tracker and enter its number here"
msgstr "Få en tracker og indtast dens nummer her"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid ""
"Hey, looks like you forgot to create products or add them to pos_config. "
"Please add them before using the Self Order"
msgstr ""
"Hej, det ser ud til, at du har glemt at oprette produkter eller tilføje dem "
"til pos_config. Tilføj dem, før du bruger selvbestillingen"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Home buttons"
msgstr "Hjem knapper"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Hope you enjoyed your meal!"
msgstr "Håber du nød dit måltid!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to customize"
msgstr "Hvordan tilpasses"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "How to use"
msgstr "Hvordan man bruger"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__id
msgid "ID"
msgstr "ID"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_product_product__self_order_available
#: model:ir.model.fields,help:pos_self_order.field_product_template__self_order_available
msgid "If this product is available in the Self Order screens"
msgstr "Hvis dette produkt er tilgængeligt på selvbestillingsskærmene"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make\n"
"                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com"
msgstr ""
"Hvis du har brug for tilpassede QR-koder, start med at scanne den relevante QR-kode for at få URL'en. Så gør\n"
"brug af en QR-kodegenerator som https://www.qrcode-monkey.com eller https://www.qr-code-generator.com"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_home_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_home_ids
msgid "Image to display on the self order screen"
msgstr "Billede til visning på selvbestillingsskærmen"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid ""
"In Self-Order mode, you must have at least one table to generate QR codes"
msgstr ""
"I selvbestillingstilstand, skal du have mindst et bord for at generere QR-"
"koder"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__status__inactive
msgid "Inactive"
msgstr "Inaktiv"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__info
msgid "Info"
msgstr "Information"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Information about your product for Self Order and Kiosk"
msgstr "Information om dit produkt til Selvbestilling og Kiosk"

#. module: pos_self_order
#: model:ir.actions.act_window,name:pos_self_order.action_pos_self_order_search_view
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__kiosk
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_search_view
msgid "Kiosk"
msgstr "Kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__name
msgid "Label"
msgstr "Tekst"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Language"
msgstr "Sprog"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_available_language_ids
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_available_language_ids
msgid "Languages available for the kiosk mode"
msgstr "Tilgængelige sprog for kiosktilstand"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Let your customers order using their mobile or a kiosk."
msgstr "Lad dine kunder bestille via deres mobil eller en kiosk."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__light
msgid "Light"
msgstr "Lys"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Loading..."
msgstr "Indlæser..."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online or order with the QR codes on your tables"
msgstr ""
"Gør det nemt for dine kunder at udforske din menu\n"
"online eller bestil med QR-koderne på dine borde"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid ""
"Make it easy for your customers to explore your menu\n"
"                online with the QR codes on your tables"
msgstr ""
"Gør det nemt for dine kunder at udforske din menu\n"
"online med QR-koderne på dine borde"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Meal"
msgstr "Måltid"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Mobile Menu"
msgstr "Mobil menu"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Mobile self-order & Kiosk"
msgstr "Mobile selvbestilling og Kiosk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Order"
msgstr "Min ordre"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/landing_page/landing_page.xml:0
#, python-format
msgid "My Orders"
msgstr "Mine ordre"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "NY"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTAT"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Name of the image to display on the self order screen"
msgstr "Navn på billedet, der skal vises på selvbestillingsskærmen"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Next"
msgstr "Næste"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "No"
msgstr "Nej"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "No order found"
msgstr "Ingen ordre fundet"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "No products found"
msgstr "Ingen produkter fundet"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Not available"
msgstr "Ikke tilgængelig"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_search_view_pos
msgid "Not available in Self"
msgstr "Ikke tilgængelig i Self"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "Only pay after each is available with kiosk mode."
msgstr "Kun betal efter hver er tilgængelig med kiosktilstand."

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Open Kiosk"
msgstr "Åben Kiosk"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_kiosk_read_only_form_dialog
msgid "Open in New Tab"
msgstr "Åbn i ny fane"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#, python-format
msgid "Order"
msgstr "Ordre"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: model:pos_self_order.custom_link,name:pos_self_order.default_custom_link
#, python-format
msgid "Order Now"
msgstr "Bestil nu"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Order number:"
msgstr "Ordrenummer:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Order to pick-up at the counter"
msgstr "Bestilling til afhentning ved skranken"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Orders not found on server"
msgstr "Ordrer ikke fundet på serveren"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Out of stock"
msgstr "Ikke på lager"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.js:0
#: code:addons/pos_self_order/static/src/app/pages/stand_number_page/stand_number_page.xml:0
#, python-format
msgid "Pay"
msgstr "Betal"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_pay_after
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_pay_after
msgid "Pay After:"
msgstr "Betal efter:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Pay after"
msgstr "Betal efter"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Pay at the cashier"
msgstr "Betal ved kassen"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid ""
"Personalize your splash screen by adding one or multiple images to create a "
"slideshow"
msgstr ""
"Tilpas din startskærm ved at tilføje et eller flere billeder for at oprette "
"et diasshow"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Pickup At Counter"
msgstr "Afhentning ved skranken"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__counter
msgid "Pickup zone"
msgstr "Afhentningszone"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Please wait until the price is loaded"
msgstr ""

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_session.py:0
#, python-format
msgid "PoS Order by Session"
msgstr "PoS Ordre efter Session"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS konfiguration"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Point of Sale linjer"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS ordrer"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Point of Sale betalingsmetoder"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_pos_session
msgid "Point of Sale Session"
msgstr "PoS session"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Point of sale:"
msgstr "Point of sale:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid "Points of Sale"
msgstr "Points of Sale"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__link_html
msgid "Preview"
msgstr "Eksempel"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Preview Web interface"
msgstr "Forhåndsvisning af hjemmesidensgrænseflade"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__primary
msgid "Primary"
msgstr "Primære"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Print QR Codes"
msgstr "Print QR koder"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_product__description_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_product_template__description_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.product_template_form_view
msgid "Product Description for Self Order"
msgstr "Produktbeskrivelse til selvbestilling"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "Produktinformation"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_product_product
msgid "Product Variant"
msgstr "Varevariant"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/combo_selection/combo_selection.xml:0
#: code:addons/pos_self_order/static/src/app/components/product_card/product_card.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Product image"
msgstr "Produktbillede"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product is not available"
msgstr "Produktet er ikke tilgængeligt"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Product not found"
msgstr "Produktet er ikke fundet"

#. module: pos_self_order
#: model:ir.actions.report,name:pos_self_order.report_self_order_qr_codes_page
msgid "QR Codes"
msgstr "QR Koder"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "QR codes can only be generated in mobile or consultation mode."
msgstr "QR koder kan kun genereres i mobil- eller konsultationstilstand."

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__consultation
msgid "QR menu"
msgstr "QR menu"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "QR menu & Kiosk activation"
msgstr "QR menu og Kiosk aktivering"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_mode__mobile
msgid "QR menu + Ordering"
msgstr "QR menu + Bestilling"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/product_info_popup/product_info_popup.xml:0
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "Quantity select"
msgstr "Antal vælg"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Reset QR Codes"
msgstr "Nulstil QR koder"

#. module: pos_self_order
#: model:ir.model,name:pos_self_order.model_restaurant_table
msgid "Restaurant Table"
msgstr "Restaurant bord"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/payment_page/payment_page.xml:0
#, python-format
msgid "Retry"
msgstr "Forsøg igen"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_list_page/product_list_page.xml:0
#, python-format
msgid "Search product"
msgstr "Søg produkt"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__secondary
msgid "Secondary"
msgstr "Anden"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__access_token
#: model:ir.model.fields,field_description:pos_self_order.field_restaurant_table__identifier
msgid "Security Token"
msgstr "Sikkerhedstoken"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_self_order_custom_link__pos_config_ids
msgid ""
"Select for which points of sale you want to display this link. Leave empty "
"to display it for all points of sale. You have to select among the points of"
" sale that have the 'QR Code Menu' feature enabled."
msgstr ""
"Vælg for hvilke points of sale du vil vise dette link. Lad det være tomt for"
" at vise det for alle points of sale. Du skal vælge blandt points of sale, "
"der har funktionen 'QR Kode Menu' aktiveret."

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "Self Kiosk"
msgstr "Selv Kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand
msgid "Self Order Kiosk Image Brand"
msgstr "Selvbestilling Kiosk Image Brand"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_image_brand_name
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_image_brand_name
msgid "Self Order Kiosk Image Brand Name"
msgstr "Selvbestilling Kiosk Image Brand Navn"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Self Order:"
msgstr "Selvbestilling:"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Self Ordering"
msgstr "Selvbestilling"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Self Ordering Enabled"
msgstr "Selvbestillings aktiveret"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_mode
msgid "Self Ordering Mode"
msgstr "Selvbestillings funktion"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_url
msgid "Self Ordering Url"
msgstr "Selvbestillings URL"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Self-ordering availability:"
msgstr "Selvbestillings tilgængelighed:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_service_mode
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_service_mode
msgid "Service"
msgstr "Ydelse"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Service at"
msgstr "Service på"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Service at Table"
msgstr "Service ved bordet"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Service at table"
msgstr "Service ved bordet"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.res_config_settings_view_form_menu
msgid "Splash screens"
msgstr "Splash skærme"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.pos_self_order_menu_item
msgid "Start Kiosk"
msgstr "Start Kiosk"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__status
msgid "Status"
msgstr "Status"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__style
msgid "Style"
msgstr "Style"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__success
msgid "Success"
msgstr "Succes"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_config__self_ordering_service_mode__table
msgid "Table"
msgstr "Tabel"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__table_stand_number
msgid "Table Stand Number"
msgstr "Bord standnummer"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/overrides/components/receipt_header/receipt_header.xml:0
#, python-format
msgid "Table Tracker:"
msgstr "Brod Tracker:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Table detective time!"
msgstr "Bord detektiv tid!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Table:"
msgstr "Bord:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_order__take_away
msgid "Take Away"
msgstr "Tag væk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/eating_location_page/eating_location_page.xml:0
#, python-format
msgid "Take Out"
msgstr "Tag ud"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_config__self_ordering_takeaway
#: model:ir.model.fields,field_description:pos_self_order.field_res_config_settings__pos_self_ordering_takeaway
msgid "Takeaway"
msgstr "Tag væk"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Tax:"
msgstr "Moms:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Taxes:"
msgstr "Moms"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "Thanks a lot!"
msgstr "Mange tak!"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/pos_config.py:0
#, python-format
msgid "The Self-Order default user must be a POS user"
msgstr "Standardbrugeren for selvbestilling skal være en POS-bruger"

#. module: pos_self_order
#. odoo-python
#: code:addons/pos_self_order/models/res_config_settings.py:0
#, python-format
msgid "The user must be a POS user"
msgstr "Brugeren skal være en POS-bruger"

#. module: pos_self_order
#: model:ir.model.fields,help:pos_self_order.field_pos_config__self_ordering_alternative_fp_id
#: model:ir.model.fields,help:pos_self_order.field_res_config_settings__pos_self_ordering_alternative_fp_id
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Dette er nyttigt for restauranter med afhentning eller take-away tjenester "
"der indbefatter specifikke afgifter."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#: code:addons/pos_self_order/static/src/app/pages/order_history_page/order_history_page.xml:0
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: pos_self_order
#: model:ir.model.fields,field_description:pos_self_order.field_pos_self_order_custom_link__url
msgid "URL"
msgstr "URL"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "URL:"
msgstr "URL:"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Uncategorised"
msgstr "Ikke kategoriseret"

#. module: pos_self_order
#: model:ir.model.fields.selection,name:pos_self_order.selection__pos_self_order_custom_link__style__warning
msgid "Warning"
msgstr "Advarsel"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "We're currently closed"
msgstr "Vi holder lukket i øjeblikket"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_index.xml:0
#, python-format
msgid "We're currently closed."
msgstr "Vi holder lukket i øjeblikket."

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "We're preparing your order!"
msgstr "Vi er ved at forberede din ordre!"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.qr_codes_page
msgid "Yes"
msgstr "Ja"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.js:0
#, python-format
msgid "You cannot edit a posted orderline !"
msgstr "Du kan ikke redigere en bogført ordrelinje!"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "You're not authorized to perform this action"
msgstr "Du er ikke autoriseret til at udføre denne handling"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/order_widget/order_widget.xml:0
#: code:addons/pos_self_order/static/src/app/pages/cart_page/cart_page.xml:0
#, python-format
msgid "Your Order"
msgstr "Din bestilling"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/combo_page/combo_page.xml:0
#, python-format
msgid "Your Selection"
msgstr "Dit valg"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been canceled"
msgstr "Din ordre er blevet annulleret"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order has been paid"
msgstr "Din ordre er blevet betalt"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/confirmation_page/confirmation_page.xml:0
#, python-format
msgid "Your order number"
msgstr "Dit ordrenummer"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/self_order_service.js:0
#, python-format
msgid "Your order status has been changed"
msgstr "Din ordrestatus er blevet ændret"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "empty = all points of sale"
msgstr "Tom = alle points of sale"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/components/popup_table/popup_table.xml:0
#, python-format
msgid "floor"
msgstr ""

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "https://odoo.com"
msgstr "https://odoo.com"

#. module: pos_self_order
#: model_terms:ir.ui.view,arch_db:pos_self_order.custom_link_tree
msgid "odoo"
msgstr "odoo"

#. module: pos_self_order
#. odoo-javascript
#: code:addons/pos_self_order/static/src/app/pages/product_page/product_page.xml:0
#, python-format
msgid "options"
msgstr "Valgmuligheder"
