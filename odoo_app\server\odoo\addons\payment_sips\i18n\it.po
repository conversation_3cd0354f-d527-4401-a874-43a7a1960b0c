# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__code
msgid "Code"
msgstr "Codice"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_version
msgid "Interface Version"
msgstr "Versione interfaccia"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_merchant_id
msgid "Merchant ID"
msgstr "ID commerciante"

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nessuna transazione trovata corrispondente al riferimento %s."

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_provider
msgid "Payment Provider"
msgstr "Fornitore di pagamenti"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_prod_url
msgid "Production URL"
msgstr "URL di produzione"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_secret
msgid "SIPS Secret Key"
msgstr "SIPS Secret Key"

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_provider_form
msgid "Secret Key"
msgstr "Chiave segreta"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_key_version
msgid "Secret Key Version"
msgstr "Versione chiave segreta"

#. module: payment_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_provider__code__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_provider__sips_test_url
msgid "Test URL"
msgstr "Test URL"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr ""
"L'ID utilizzato esclusivamente per identificare il conto commerciante con "
"Sips"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Codice tecnico del fornitore di pagamenti."

#. module: payment_sips
#. odoo-python
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr "Risposta non riconosciuta ricevuta dal fornitore di pagamenti."
