<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Cron that processes the mass mailing queue -->
        <record id="ir_cron_mass_mailing_queue" model="ir.cron">
            <field name="name">Mail Marketing: Process queue</field>
            <field name="model_id" ref="model_mailing_mailing"/>
            <field name="state">code</field>
            <field name="code">model._process_mass_mailing_queue()</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field eval="False" name="doall" />
        </record>
        <!-- Cron that processes the a/b testing -->
        <record id="ir_cron_mass_mailing_ab_testing" model="ir.cron">
            <field name="name">Mail Marketing: A/B Testing</field>
            <field name="model_id" ref="model_utm_campaign"/>
            <field name="state">code</field>
            <field name="code">model._cron_process_mass_mailing_ab_testing()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="active" eval="False"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
        </record>
    </data>
</odoo>
