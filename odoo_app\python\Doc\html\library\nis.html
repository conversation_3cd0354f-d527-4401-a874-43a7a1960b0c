<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="nis — Interface to Sun’s NIS (Yellow Pages)" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/nis.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The nis module gives a thin wrapper around the NIS library, useful for central administration of several hosts. Because NIS exists only on Unix systems, this module is only available for Unix. Avai..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The nis module gives a thin wrapper around the NIS library, useful for central administration of several hosts. Because NIS exists only on Unix systems, this module is only available for Unix. Avai..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>nis — Interface to Sun’s NIS (Yellow Pages) &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="nntplib — NNTP protocol client" href="nntplib.html" />
    <link rel="prev" title="msilib — Read and write Microsoft Installer files" href="msilib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/nis.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="msilib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msilib</span></code> — Read and write Microsoft Installer files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="nntplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/nis.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="nntplib.html" title="nntplib — NNTP protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="msilib.html" title="msilib — Read and write Microsoft Installer files"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code> — Interface to Sun’s NIS (Yellow Pages)</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-nis">
<span id="nis-interface-to-sun-s-nis-yellow-pages"></span><h1><a class="reference internal" href="#module-nis" title="nis: Interface to Sun's NIS (Yellow Pages) library. (deprecated) (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code></a> — Interface to Sun’s NIS (Yellow Pages)<a class="headerlink" href="#module-nis" title="Link to this heading">¶</a></h1>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-nis" title="nis: Interface to Sun's NIS (Yellow Pages) library. (deprecated) (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code></a> module is deprecated
(see <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#nis"><strong>PEP 594</strong></a> for details).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-nis" title="nis: Interface to Sun's NIS (Yellow Pages) library. (deprecated) (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code></a> module gives a thin wrapper around the NIS library, useful for
central administration of several hosts.</p>
<p>Because NIS exists only on Unix systems, this module is only available for Unix.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>The <a class="reference internal" href="#module-nis" title="nis: Interface to Sun's NIS (Yellow Pages) library. (deprecated) (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="nis.match">
<span class="sig-prename descclassname"><span class="pre">nis.</span></span><span class="sig-name descname"><span class="pre">match</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mapname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">default_domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nis.match" title="Link to this definition">¶</a></dt>
<dd><p>Return the match for <em>key</em> in map <em>mapname</em>, or raise an error
(<a class="reference internal" href="#nis.error" title="nis.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">nis.error</span></code></a>) if there is none. Both should be strings, <em>key</em> is 8-bit
clean. Return value is an arbitrary array of bytes (may contain <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and
other joys).</p>
<p>Note that <em>mapname</em> is first checked if it is an alias to another name.</p>
<p>The <em>domain</em> argument allows overriding the NIS domain used for the lookup. If
unspecified, lookup is in the default NIS domain.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nis.cat">
<span class="sig-prename descclassname"><span class="pre">nis.</span></span><span class="sig-name descname"><span class="pre">cat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mapname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">default_domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nis.cat" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary mapping <em>key</em> to <em>value</em> such that <code class="docutils literal notranslate"><span class="pre">match(key,</span>
<span class="pre">mapname)==value</span></code>. Note that both keys and values of the dictionary are
arbitrary arrays of bytes.</p>
<p>Note that <em>mapname</em> is first checked if it is an alias to another name.</p>
<p>The <em>domain</em> argument allows overriding the NIS domain used for the lookup. If
unspecified, lookup is in the default NIS domain.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nis.maps">
<span class="sig-prename descclassname"><span class="pre">nis.</span></span><span class="sig-name descname"><span class="pre">maps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">default_domain</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#nis.maps" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of all valid maps.</p>
<p>The <em>domain</em> argument allows overriding the NIS domain used for the lookup. If
unspecified, lookup is in the default NIS domain.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="nis.get_default_domain">
<span class="sig-prename descclassname"><span class="pre">nis.</span></span><span class="sig-name descname"><span class="pre">get_default_domain</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#nis.get_default_domain" title="Link to this definition">¶</a></dt>
<dd><p>Return the system default NIS domain.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-nis" title="nis: Interface to Sun's NIS (Yellow Pages) library. (deprecated) (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code></a> module defines the following exception:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="nis.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">nis.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#nis.error" title="Link to this definition">¶</a></dt>
<dd><p>An error raised when a NIS function returns an error code.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="msilib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msilib</span></code> — Read and write Microsoft Installer files</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="nntplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">nntplib</span></code> — NNTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/nis.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="nntplib.html" title="nntplib — NNTP protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="msilib.html" title="msilib — Read and write Microsoft Installer files"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">nis</span></code> — Interface to Sun’s NIS (Yellow Pages)</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>