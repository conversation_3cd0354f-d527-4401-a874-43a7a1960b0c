# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_provider_form
msgid "API Key"
msgstr "API-Schl<PERSON>ssel"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr "Abgebrochene Zahlung mit Status: %s"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Verbindung mit API konnte nicht hergestellt werden."

#. module: payment_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_provider__code__mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__mollie_api_key
msgid "Mollie API Key"
msgstr "API-Schlüssel von Mollie"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Keine Transaktion gefunden, die der Referenz %s entspricht."

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_provider
msgid "Payment Provider"
msgstr "Zahlungsanbieter"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Zahlungstransaktion"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Erhaltene Daten mit ungültigem Zahlungsstatus: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the provider"
msgstr "Die Test- oder Live-API-Schlüssel je nach Konfiguration des Anbieters"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
#, python-format
msgid ""
"The communication with the API failed. Mollie gave us the following "
"information: %s"
msgstr ""
"Die Kommunikation mit der API ist fehlgeschlagen. Mollie hat uns folgende "
"Informationen übermittelt: %s"

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Der technische Code dieses Zahlungsanbieters."
