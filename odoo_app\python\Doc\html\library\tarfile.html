<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tarfile — Read and write tar archive files" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tarfile.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tarfile.py The tarfile module makes it possible to read and write tar archives, including those using gzip, bz2 and lzma compression. Use the zipfile module to read or write.zip fi..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tarfile.py The tarfile module makes it possible to read and write tar archives, including those using gzip, bz2 and lzma compression. Use the zipfile module to read or write.zip fi..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tarfile — Read and write tar archive files &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="File Formats" href="fileformats.html" />
    <link rel="prev" title="zipfile — Work with ZIP archives" href="zipfile.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tarfile.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a><ul>
<li><a class="reference internal" href="#tarfile-objects">TarFile Objects</a></li>
<li><a class="reference internal" href="#tarinfo-objects">TarInfo Objects</a></li>
<li><a class="reference internal" href="#extraction-filters">Extraction filters</a><ul>
<li><a class="reference internal" href="#default-named-filters">Default named filters</a></li>
<li><a class="reference internal" href="#filter-errors">Filter errors</a></li>
<li><a class="reference internal" href="#hints-for-further-verification">Hints for further verification</a></li>
<li><a class="reference internal" href="#supporting-older-python-versions">Supporting older Python versions</a></li>
<li><a class="reference internal" href="#stateful-extraction-filter-example">Stateful extraction filter example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#supported-tar-formats">Supported tar formats</a></li>
<li><a class="reference internal" href="#unicode-issues">Unicode issues</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipfile.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fileformats.html"
                          title="next chapter">File Formats</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tarfile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fileformats.html" title="File Formats"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="zipfile.html" title="zipfile — Work with ZIP archives"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" accesskey="U">Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tarfile">
<span id="tarfile-read-and-write-tar-archive-files"></span><h1><a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> — Read and write tar archive files<a class="headerlink" href="#module-tarfile" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tarfile.py">Lib/tarfile.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module makes it possible to read and write tar
archives, including those using gzip, bz2 and lzma compression.
Use the <a class="reference internal" href="zipfile.html#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> module to read or write <code class="file docutils literal notranslate"><span class="pre">.zip</span></code> files, or the
higher-level functions in <a class="reference internal" href="shutil.html#archiving-operations"><span class="std std-ref">shutil</span></a>.</p>
<p>Some facts and figures:</p>
<ul class="simple">
<li><p>reads and writes <a class="reference internal" href="gzip.html#module-gzip" title="gzip: Interfaces for gzip compression and decompression using file objects."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gzip</span></code></a>, <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> and <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> compressed archives
if the respective modules are available.</p></li>
<li><p>read/write support for the POSIX.1-1988 (ustar) format.</p></li>
<li><p>read/write support for the GNU tar format including <em>longname</em> and <em>longlink</em>
extensions, read-only support for all variants of the <em>sparse</em> extension
including restoration of sparse files.</p></li>
<li><p>read/write support for the POSIX.1-2001 (pax) format.</p></li>
<li><p>handles directories, regular files, hardlinks, symbolic links, fifos,
character devices and block devices and is able to acquire and restore file
information like timestamp, access permissions and owner.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added support for <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> compression.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Archives are extracted using a <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">filter</span></a>,
which makes it possible to either limit surprising/dangerous features,
or to acknowledge that they are expected and the archive is fully trusted.
By default, archives are fully trusted, but this default is deprecated
and slated to change in Python 3.14.</p>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="tarfile.open">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fileobj</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bufsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">10240</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.open" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> object for the pathname <em>name</em>. For detailed
information on <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> objects and the keyword arguments that are
allowed, see <a class="reference internal" href="#tarfile-objects"><span class="std std-ref">TarFile Objects</span></a>.</p>
<p><em>mode</em> has to be a string of the form <code class="docutils literal notranslate"><span class="pre">'filemode[:compression]'</span></code>, it defaults
to <code class="docutils literal notranslate"><span class="pre">'r'</span></code>. Here is a full list of mode combinations:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>mode</p></th>
<th class="head"><p>action</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r'</span> <span class="pre">or</span> <span class="pre">'r:*'</span></code></p></td>
<td><p>Open for reading with transparent
compression (recommended).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'r:'</span></code></p></td>
<td><p>Open for reading exclusively without
compression.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r:gz'</span></code></p></td>
<td><p>Open for reading with gzip compression.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'r:bz2'</span></code></p></td>
<td><p>Open for reading with bzip2 compression.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r:xz'</span></code></p></td>
<td><p>Open for reading with lzma compression.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'x'</span></code> or
<code class="docutils literal notranslate"><span class="pre">'x:'</span></code></p></td>
<td><p>Create a tarfile exclusively without
compression.
Raise a <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> exception
if it already exists.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'x:gz'</span></code></p></td>
<td><p>Create a tarfile with gzip compression.
Raise a <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> exception
if it already exists.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'x:bz2'</span></code></p></td>
<td><p>Create a tarfile with bzip2 compression.
Raise a <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> exception
if it already exists.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'x:xz'</span></code></p></td>
<td><p>Create a tarfile with lzma compression.
Raise a <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> exception
if it already exists.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'a'</span> <span class="pre">or</span> <span class="pre">'a:'</span></code></p></td>
<td><p>Open for appending with no compression. The
file is created if it does not exist.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'w'</span> <span class="pre">or</span> <span class="pre">'w:'</span></code></p></td>
<td><p>Open for uncompressed writing.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'w:gz'</span></code></p></td>
<td><p>Open for gzip compressed writing.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'w:bz2'</span></code></p></td>
<td><p>Open for bzip2 compressed writing.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'w:xz'</span></code></p></td>
<td><p>Open for lzma compressed writing.</p></td>
</tr>
</tbody>
</table>
<p>Note that <code class="docutils literal notranslate"><span class="pre">'a:gz'</span></code>, <code class="docutils literal notranslate"><span class="pre">'a:bz2'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a:xz'</span></code> is not possible. If <em>mode</em>
is not suitable to open a certain (compressed) file for reading,
<a class="reference internal" href="#tarfile.ReadError" title="tarfile.ReadError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ReadError</span></code></a> is raised. Use <em>mode</em> <code class="docutils literal notranslate"><span class="pre">'r'</span></code> to avoid this.  If a
compression method is not supported, <a class="reference internal" href="#tarfile.CompressionError" title="tarfile.CompressionError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CompressionError</span></code></a> is raised.</p>
<p>If <em>fileobj</em> is specified, it is used as an alternative to a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>
opened in binary mode for <em>name</em>. It is supposed to be at position 0.</p>
<p>For modes <code class="docutils literal notranslate"><span class="pre">'w:gz'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x:gz'</span></code>, <code class="docutils literal notranslate"><span class="pre">'w|gz'</span></code>, <code class="docutils literal notranslate"><span class="pre">'w:bz2'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x:bz2'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'w|bz2'</span></code>, <a class="reference internal" href="#tarfile.open" title="tarfile.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">tarfile.open()</span></code></a> accepts the keyword argument
<em>compresslevel</em> (default <code class="docutils literal notranslate"><span class="pre">9</span></code>) to specify the compression level of the file.</p>
<p>For modes <code class="docutils literal notranslate"><span class="pre">'w:xz'</span></code> and <code class="docutils literal notranslate"><span class="pre">'x:xz'</span></code>, <a class="reference internal" href="#tarfile.open" title="tarfile.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">tarfile.open()</span></code></a> accepts the
keyword argument <em>preset</em> to specify the compression level of the file.</p>
<p>For special purposes, there is a second format for <em>mode</em>:
<code class="docutils literal notranslate"><span class="pre">'filemode|[compression]'</span></code>.  <a class="reference internal" href="#tarfile.open" title="tarfile.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">tarfile.open()</span></code></a> will return a <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a>
object that processes its data as a stream of blocks.  No random seeking will
be done on the file. If given, <em>fileobj</em> may be any object that has a
<a class="reference internal" href="io.html#io.RawIOBase.read" title="io.RawIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> or <a class="reference internal" href="io.html#io.RawIOBase.write" title="io.RawIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> method
(depending on the <em>mode</em>) that works with bytes.
<em>bufsize</em> specifies the blocksize and defaults to <code class="docutils literal notranslate"><span class="pre">20</span> <span class="pre">*</span> <span class="pre">512</span></code> bytes.
Use this variant in combination with e.g. <code class="docutils literal notranslate"><span class="pre">sys.stdin.buffer</span></code>, a socket
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> or a tape device.
However, such a <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> object is limited in that it does
not allow random access, see <a class="reference internal" href="#tar-examples"><span class="std std-ref">Examples</span></a>.  The currently
possible modes:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Mode</p></th>
<th class="head"><p>Action</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r|*'</span></code></p></td>
<td><p>Open a <em>stream</em> of tar blocks for reading
with transparent compression.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'r|'</span></code></p></td>
<td><p>Open a <em>stream</em> of uncompressed tar blocks
for reading.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r|gz'</span></code></p></td>
<td><p>Open a gzip compressed <em>stream</em> for
reading.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'r|bz2'</span></code></p></td>
<td><p>Open a bzip2 compressed <em>stream</em> for
reading.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'r|xz'</span></code></p></td>
<td><p>Open an lzma compressed <em>stream</em> for
reading.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'w|'</span></code></p></td>
<td><p>Open an uncompressed <em>stream</em> for writing.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'w|gz'</span></code></p></td>
<td><p>Open a gzip compressed <em>stream</em> for
writing.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'w|bz2'</span></code></p></td>
<td><p>Open a bzip2 compressed <em>stream</em> for
writing.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'w|xz'</span></code></p></td>
<td><p>Open an lzma compressed <em>stream</em> for
writing.</p></td>
</tr>
</tbody>
</table>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="docutils literal notranslate"><span class="pre">'x'</span></code> (exclusive creation) mode was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>name</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>compresslevel</em> keyword argument also works for streams.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">TarFile</span></span></dt>
<dd><p>Class for reading and writing tar archives. Do not use this class directly:
use <a class="reference internal" href="#tarfile.open" title="tarfile.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">tarfile.open()</span></code></a> instead. See <a class="reference internal" href="#tarfile-objects"><span class="std std-ref">TarFile Objects</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tarfile.is_tarfile">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">is_tarfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.is_tarfile" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if <em>name</em> is a tar archive file, that the <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a>
module can read. <em>name</em> may be a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, file, or file-like object.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Support for file and file-like objects.</p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module defines the following exceptions:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.TarError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">TarError</span></span><a class="headerlink" href="#tarfile.TarError" title="Link to this definition">¶</a></dt>
<dd><p>Base class for all <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> exceptions.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.ReadError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">ReadError</span></span><a class="headerlink" href="#tarfile.ReadError" title="Link to this definition">¶</a></dt>
<dd><p>Is raised when a tar archive is opened, that either cannot be handled by the
<a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module or is somehow invalid.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.CompressionError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">CompressionError</span></span><a class="headerlink" href="#tarfile.CompressionError" title="Link to this definition">¶</a></dt>
<dd><p>Is raised when a compression method is not supported or when the data cannot be
decoded properly.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.StreamError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">StreamError</span></span><a class="headerlink" href="#tarfile.StreamError" title="Link to this definition">¶</a></dt>
<dd><p>Is raised for the limitations that are typical for stream-like <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a>
objects.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.ExtractError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">ExtractError</span></span><a class="headerlink" href="#tarfile.ExtractError" title="Link to this definition">¶</a></dt>
<dd><p>Is raised for <em>non-fatal</em> errors when using <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.extract()</span></code></a>, but only if
<a class="reference internal" href="#tarfile.TarFile.errorlevel" title="tarfile.TarFile.errorlevel"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TarFile.errorlevel</span></code></a><code class="docutils literal notranslate"><span class="pre">==</span> <span class="pre">2</span></code>.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.HeaderError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">HeaderError</span></span><a class="headerlink" href="#tarfile.HeaderError" title="Link to this definition">¶</a></dt>
<dd><p>Is raised by <a class="reference internal" href="#tarfile.TarInfo.frombuf" title="tarfile.TarInfo.frombuf"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarInfo.frombuf()</span></code></a> if the buffer it gets is invalid.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.FilterError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">FilterError</span></span><a class="headerlink" href="#tarfile.FilterError" title="Link to this definition">¶</a></dt>
<dd><p>Base class for members <a class="reference internal" href="#tarfile-extraction-refuse"><span class="std std-ref">refused</span></a> by
filters.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.FilterError.tarinfo">
<span class="sig-name descname"><span class="pre">tarinfo</span></span><a class="headerlink" href="#tarfile.FilterError.tarinfo" title="Link to this definition">¶</a></dt>
<dd><p>Information about the member that the filter refused to extract,
as <a class="reference internal" href="#tarinfo-objects"><span class="std std-ref">TarInfo</span></a>.</p>
</dd></dl>

</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.AbsolutePathError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">AbsolutePathError</span></span><a class="headerlink" href="#tarfile.AbsolutePathError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to refuse extracting a member with an absolute path.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.OutsideDestinationError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">OutsideDestinationError</span></span><a class="headerlink" href="#tarfile.OutsideDestinationError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to refuse extracting a member outside the destination directory.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.SpecialFileError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">SpecialFileError</span></span><a class="headerlink" href="#tarfile.SpecialFileError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to refuse extracting a special file (e.g. a device or pipe).</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.AbsoluteLinkError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">AbsoluteLinkError</span></span><a class="headerlink" href="#tarfile.AbsoluteLinkError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to refuse extracting a symbolic link with an absolute path.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="tarfile.LinkOutsideDestinationError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">LinkOutsideDestinationError</span></span><a class="headerlink" href="#tarfile.LinkOutsideDestinationError" title="Link to this definition">¶</a></dt>
<dd><p>Raised to refuse extracting a symbolic link pointing outside the destination
directory.</p>
</dd></dl>

<p>The following constants are available at the module level:</p>
<dl class="py data">
<dt class="sig sig-object py" id="tarfile.ENCODING">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">ENCODING</span></span><a class="headerlink" href="#tarfile.ENCODING" title="Link to this definition">¶</a></dt>
<dd><p>The default character encoding: <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code> on Windows, the value returned by
<a class="reference internal" href="sys.html#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencoding()</span></code></a> otherwise.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.REGTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">REGTYPE</span></span><a class="headerlink" href="#tarfile.REGTYPE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tarfile.AREGTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">AREGTYPE</span></span><a class="headerlink" href="#tarfile.AREGTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A regular file <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.LNKTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">LNKTYPE</span></span><a class="headerlink" href="#tarfile.LNKTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A link (inside tarfile) <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.SYMTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">SYMTYPE</span></span><a class="headerlink" href="#tarfile.SYMTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A symbolic link <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.CHRTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">CHRTYPE</span></span><a class="headerlink" href="#tarfile.CHRTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A character special device <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.BLKTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">BLKTYPE</span></span><a class="headerlink" href="#tarfile.BLKTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A block special device <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.DIRTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">DIRTYPE</span></span><a class="headerlink" href="#tarfile.DIRTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A directory <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.FIFOTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">FIFOTYPE</span></span><a class="headerlink" href="#tarfile.FIFOTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A FIFO special device <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.CONTTYPE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">CONTTYPE</span></span><a class="headerlink" href="#tarfile.CONTTYPE" title="Link to this definition">¶</a></dt>
<dd><p>A contiguous file <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.GNUTYPE_LONGNAME">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">GNUTYPE_LONGNAME</span></span><a class="headerlink" href="#tarfile.GNUTYPE_LONGNAME" title="Link to this definition">¶</a></dt>
<dd><p>A GNU tar longname <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.GNUTYPE_LONGLINK">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">GNUTYPE_LONGLINK</span></span><a class="headerlink" href="#tarfile.GNUTYPE_LONGLINK" title="Link to this definition">¶</a></dt>
<dd><p>A GNU tar longlink <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.GNUTYPE_SPARSE">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">GNUTYPE_SPARSE</span></span><a class="headerlink" href="#tarfile.GNUTYPE_SPARSE" title="Link to this definition">¶</a></dt>
<dd><p>A GNU tar sparse file <a class="reference internal" href="#tarfile.TarInfo.type" title="tarfile.TarInfo.type"><code class="xref py py-attr docutils literal notranslate"><span class="pre">type</span></code></a>.</p>
</dd></dl>

<p>Each of the following constants defines a tar archive format that the
<a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module is able to create. See section <a class="reference internal" href="#tar-formats"><span class="std std-ref">Supported tar formats</span></a> for
details.</p>
<dl class="py data">
<dt class="sig sig-object py" id="tarfile.USTAR_FORMAT">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">USTAR_FORMAT</span></span><a class="headerlink" href="#tarfile.USTAR_FORMAT" title="Link to this definition">¶</a></dt>
<dd><p>POSIX.1-1988 (ustar) format.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.GNU_FORMAT">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">GNU_FORMAT</span></span><a class="headerlink" href="#tarfile.GNU_FORMAT" title="Link to this definition">¶</a></dt>
<dd><p>GNU tar format.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.PAX_FORMAT">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">PAX_FORMAT</span></span><a class="headerlink" href="#tarfile.PAX_FORMAT" title="Link to this definition">¶</a></dt>
<dd><p>POSIX.1-2001 (pax) format.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tarfile.DEFAULT_FORMAT">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">DEFAULT_FORMAT</span></span><a class="headerlink" href="#tarfile.DEFAULT_FORMAT" title="Link to this definition">¶</a></dt>
<dd><p>The default format for creating archives. This is currently <a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The default format for new archives was changed to
<a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a> from <a class="reference internal" href="#tarfile.GNU_FORMAT" title="tarfile.GNU_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">GNU_FORMAT</span></code></a>.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="zipfile.html#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a></dt><dd><p>Documentation of the <a class="reference internal" href="zipfile.html#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> standard module.</p>
</dd>
<dt><a class="reference internal" href="shutil.html#archiving-operations"><span class="std std-ref">Archiving operations</span></a></dt><dd><p>Documentation of the higher-level archiving facilities provided by the
standard <a class="reference internal" href="shutil.html#module-shutil" title="shutil: High-level file operations, including copying."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code></a> module.</p>
</dd>
<dt><a class="reference external" href="https://www.gnu.org/software/tar/manual/html_node/Standard.html">GNU tar manual, Basic Tar Format</a></dt><dd><p>Documentation for tar archive files, including GNU tar extensions.</p>
</dd>
</dl>
</div>
<section id="tarfile-objects">
<span id="id1"></span><h2>TarFile Objects<a class="headerlink" href="#tarfile-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> object provides an interface to a tar archive. A tar
archive is a sequence of blocks. An archive member (a stored file) is made up of
a header block followed by data blocks. It is possible to store a file in a tar
archive several times. Each archive member is represented by a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a>
object, see <a class="reference internal" href="#tarinfo-objects"><span class="std std-ref">TarInfo Objects</span></a> for details.</p>
<p>A <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> object can be used as a context manager in a <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a>
statement. It will automatically be closed when the block is completed. Please
note that in the event of an exception an archive opened for writing will not
be finalized; only the internally used file object will be closed. See the
<a class="reference internal" href="#tar-examples"><span class="std std-ref">Examples</span></a> section for a use case.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2: </span>Added support for the context management protocol.</p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="tarfile.TarFile">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">TarFile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fileobj</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_FORMAT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tarinfo</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">TarInfo</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dereference</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ignore_zeros</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">ENCODING</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'surrogateescape'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pax_headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">debug</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errorlevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile" title="Link to this definition">¶</a></dt>
<dd><p>All following arguments are optional and can be accessed as instance attributes
as well.</p>
<p><em>name</em> is the pathname of the archive. <em>name</em> may be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.
It can be omitted if <em>fileobj</em> is given.
In this case, the file object’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> attribute is used if it exists.</p>
<p><em>mode</em> is either <code class="docutils literal notranslate"><span class="pre">'r'</span></code> to read from an existing archive, <code class="docutils literal notranslate"><span class="pre">'a'</span></code> to append
data to an existing file, <code class="docutils literal notranslate"><span class="pre">'w'</span></code> to create a new file overwriting an existing
one, or <code class="docutils literal notranslate"><span class="pre">'x'</span></code> to create a new file only if it does not already exist.</p>
<p>If <em>fileobj</em> is given, it is used for reading or writing data. If it can be
determined, <em>mode</em> is overridden by <em>fileobj</em>’s mode. <em>fileobj</em> will be used
from position 0.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><em>fileobj</em> is not closed, when <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> is closed.</p>
</div>
<p><em>format</em> controls the archive format for writing. It must be one of the constants
<a class="reference internal" href="#tarfile.USTAR_FORMAT" title="tarfile.USTAR_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">USTAR_FORMAT</span></code></a>, <a class="reference internal" href="#tarfile.GNU_FORMAT" title="tarfile.GNU_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">GNU_FORMAT</span></code></a> or <a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a> that are
defined at module level. When reading, format will be automatically detected, even
if different formats are present in a single archive.</p>
<p>The <em>tarinfo</em> argument can be used to replace the default <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> class
with a different one.</p>
<p>If <em>dereference</em> is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>, add symbolic and hard links to the archive. If it
is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, add the content of the target files to the archive. This has no
effect on systems that do not support symbolic links.</p>
<p>If <em>ignore_zeros</em> is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>, treat an empty block as the end of the archive.
If it is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, skip empty (and invalid) blocks and try to get as many members
as possible. This is only useful for reading concatenated or damaged archives.</p>
<p><em>debug</em> can be set from <code class="docutils literal notranslate"><span class="pre">0</span></code> (no debug messages) up to <code class="docutils literal notranslate"><span class="pre">3</span></code> (all debug
messages). The messages are written to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>.</p>
<p><em>errorlevel</em> controls how extraction errors are handled,
see <a class="reference internal" href="#tarfile.TarFile.errorlevel" title="tarfile.TarFile.errorlevel"><code class="xref py py-attr docutils literal notranslate"><span class="pre">the</span> <span class="pre">corresponding</span> <span class="pre">attribute</span></code></a>.</p>
<p>The <em>encoding</em> and <em>errors</em> arguments define the character encoding to be
used for reading or writing the archive and how conversion errors are going
to be handled. The default settings will work for most users.
See section <a class="reference internal" href="#tar-unicode"><span class="std std-ref">Unicode issues</span></a> for in-depth information.</p>
<p>The <em>pax_headers</em> argument is an optional dictionary of strings which
will be added as a pax global header if <em>format</em> is <a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Use <code class="docutils literal notranslate"><span class="pre">'surrogateescape'</span></code> as the default for the <em>errors</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <code class="docutils literal notranslate"><span class="pre">'x'</span></code> (exclusive creation) mode was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>name</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.open">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.open" title="Link to this definition">¶</a></dt>
<dd><p>Alternative constructor. The <a class="reference internal" href="#tarfile.open" title="tarfile.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">tarfile.open()</span></code></a> function is actually a
shortcut to this classmethod.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.getmember">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">getmember</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.getmember" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object for member <em>name</em>. If <em>name</em> can not be found
in the archive, <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> is raised.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If a member occurs more than once in the archive, its last occurrence is assumed
to be the most up-to-date version.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.getmembers">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">getmembers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.getmembers" title="Link to this definition">¶</a></dt>
<dd><p>Return the members of the archive as a list of <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> objects. The
list has the same order as the members in the archive.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.getnames">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">getnames</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.getnames" title="Link to this definition">¶</a></dt>
<dd><p>Return the members as a list of their names. It has the same order as the list
returned by <a class="reference internal" href="#tarfile.TarFile.getmembers" title="tarfile.TarFile.getmembers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmembers()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.list">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">list</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">verbose</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">members</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.list" title="Link to this definition">¶</a></dt>
<dd><p>Print a table of contents to <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>. If <em>verbose</em> is <a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>,
only the names of the members are printed. If it is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, output
similar to that of <strong class="program">ls -l</strong> is produced. If optional <em>members</em> is
given, it must be a subset of the list returned by <a class="reference internal" href="#tarfile.TarFile.getmembers" title="tarfile.TarFile.getmembers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmembers()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>members</em> parameter.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.next">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">next</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.next" title="Link to this definition">¶</a></dt>
<dd><p>Return the next member of the archive as a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object, when
<a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> is opened for reading. Return <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> if there is no more
available.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.extractall">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">extractall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'.'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">members</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">numeric_owner</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.extractall" title="Link to this definition">¶</a></dt>
<dd><p>Extract all members from the archive to the current working directory or
directory <em>path</em>. If optional <em>members</em> is given, it must be a subset of the
list returned by <a class="reference internal" href="#tarfile.TarFile.getmembers" title="tarfile.TarFile.getmembers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmembers()</span></code></a>. Directory information like owner,
modification time and permissions are set after all members have been extracted.
This is done to work around two problems: A directory’s modification time is
reset each time a file is created in it. And, if a directory’s permissions do
not allow writing, extracting files to it will fail.</p>
<p>If <em>numeric_owner</em> is <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a>, the uid and gid numbers from the tarfile
are used to set the owner/group for the extracted files. Otherwise, the named
values from the tarfile are used.</p>
<p>The <em>filter</em> argument specifies how <code class="docutils literal notranslate"><span class="pre">members</span></code> are modified or rejected
before extraction.
See <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">Extraction filters</span></a> for details.
It is recommended to set this explicitly depending on which <em>tar</em> features
you need to support.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Never extract archives from untrusted sources without prior inspection.
It is possible that files are created outside of <em>path</em>, e.g. members
that have absolute filenames starting with <code class="docutils literal notranslate"><span class="pre">&quot;/&quot;</span></code> or filenames with two
dots <code class="docutils literal notranslate"><span class="pre">&quot;..&quot;</span></code>.</p>
<p>Set <code class="docutils literal notranslate"><span class="pre">filter='data'</span></code> to prevent the most dangerous security issues,
and read the <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">Extraction filters</span></a> section for details.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>numeric_owner</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>path</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>filter</em> parameter.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.extract">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">set_attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">numeric_owner</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.extract" title="Link to this definition">¶</a></dt>
<dd><p>Extract a member from the archive to the current working directory, using its
full name. Its file information is extracted as accurately as possible. <em>member</em>
may be a filename or a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object. You can specify a different
directory using <em>path</em>. <em>path</em> may be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.
File attributes (owner, mtime, mode) are set unless <em>set_attrs</em> is false.</p>
<p>The <em>numeric_owner</em> and <em>filter</em> arguments are the same as
for <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> method does not take care of several extraction issues.
In most cases you should consider using the <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a> method.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>See the warning for <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>.</p>
<p>Set <code class="docutils literal notranslate"><span class="pre">filter='data'</span></code> to prevent the most dangerous security issues,
and read the <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">Extraction filters</span></a> section for details.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>set_attrs</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added the <em>numeric_owner</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>path</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>filter</em> parameter.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.extractfile">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">extractfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.extractfile" title="Link to this definition">¶</a></dt>
<dd><p>Extract a member from the archive as a file object. <em>member</em> may be
a filename or a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object. If <em>member</em> is a regular file or
a link, an <a class="reference internal" href="io.html#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedReader</span></code></a> object is returned. For all other
existing members, <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> is returned. If <em>member</em> does not appear
in the archive, <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Return an <a class="reference internal" href="io.html#io.BufferedReader" title="io.BufferedReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.BufferedReader</span></code></a> object.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarFile.errorlevel">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">errorlevel</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarFile.errorlevel" title="Link to this definition">¶</a></dt>
<dd><p>If <em>errorlevel</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code>, errors are ignored when using <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.extract()</span></code></a>
and <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.extractall()</span></code></a>.
Nevertheless, they appear as error messages in the debug output when
<em>debug</em> is greater than 0.
If <code class="docutils literal notranslate"><span class="pre">1</span></code> (the default), all <em>fatal</em> errors are raised as <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> or
<a class="reference internal" href="#tarfile.FilterError" title="tarfile.FilterError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FilterError</span></code></a> exceptions. If <code class="docutils literal notranslate"><span class="pre">2</span></code>, all <em>non-fatal</em> errors are raised
as <a class="reference internal" href="#tarfile.TarError" title="tarfile.TarError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TarError</span></code></a> exceptions as well.</p>
<p>Some exceptions, e.g. ones caused by wrong argument types or data
corruption, are always raised.</p>
<p>Custom <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">extraction filters</span></a>
should raise <a class="reference internal" href="#tarfile.FilterError" title="tarfile.FilterError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FilterError</span></code></a> for <em>fatal</em> errors
and <a class="reference internal" href="#tarfile.ExtractError" title="tarfile.ExtractError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExtractError</span></code></a> for <em>non-fatal</em> ones.</p>
<p>Note that when an exception is raised, the archive may be partially
extracted. It is the user’s responsibility to clean up.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarFile.extraction_filter">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">extraction_filter</span></span><a class="headerlink" href="#tarfile.TarFile.extraction_filter" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>The <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">extraction filter</span></a> used
as a default for the <em>filter</em> argument of <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a>
and <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>.</p>
<p>The attribute may be <code class="docutils literal notranslate"><span class="pre">None</span></code> or a callable.
String names are not allowed for this attribute, unlike the <em>filter</em>
argument to <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">extraction_filter</span></code> is <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default),
calling an extraction method without a <em>filter</em> argument will raise a
<code class="docutils literal notranslate"><span class="pre">DeprecationWarning</span></code>,
and fall back to the <a class="reference internal" href="#tarfile.fully_trusted_filter" title="tarfile.fully_trusted_filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">fully_trusted</span></code></a> filter,
whose dangerous behavior matches previous versions of Python.</p>
<p>In Python 3.14+, leaving <code class="docutils literal notranslate"><span class="pre">extraction_filter=None</span></code> will cause
extraction methods to use the <a class="reference internal" href="#tarfile.data_filter" title="tarfile.data_filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">data</span></code></a> filter by default.</p>
<p>The attribute may be set on instances or overridden in subclasses.
It also is possible to set it on the <code class="docutils literal notranslate"><span class="pre">TarFile</span></code> class itself to set a
global default, although, since it affects all uses of <em>tarfile</em>,
it is best practice to only do so in top-level applications or
<a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span> <span class="pre">configuration</span></code></a>.
To set a global default this way, a filter function needs to be wrapped in
<a class="reference internal" href="functions.html#staticmethod" title="staticmethod"><code class="xref py py-func docutils literal notranslate"><span class="pre">staticmethod()</span></code></a> to prevent injection of a <code class="docutils literal notranslate"><span class="pre">self</span></code> argument.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.add">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arcname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">recursive</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filter</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.add" title="Link to this definition">¶</a></dt>
<dd><p>Add the file <em>name</em> to the archive. <em>name</em> may be any type of file
(directory, fifo, symbolic link, etc.). If given, <em>arcname</em> specifies an
alternative name for the file in the archive. Directories are added
recursively by default. This can be avoided by setting <em>recursive</em> to
<a class="reference internal" href="constants.html#False" title="False"><code class="xref py py-const docutils literal notranslate"><span class="pre">False</span></code></a>. Recursion adds entries in sorted order.
If <em>filter</em> is given, it
should be a function that takes a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object argument and
returns the changed <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object. If it instead returns
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> the <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object will be excluded from the
archive. See <a class="reference internal" href="#tar-examples"><span class="std std-ref">Examples</span></a> for an example.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>filter</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Recursion adds entries in sorted order.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.addfile">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">addfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tarinfo</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fileobj</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.addfile" title="Link to this definition">¶</a></dt>
<dd><p>Add the <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object <em>tarinfo</em> to the archive. If <em>fileobj</em> is given,
it should be a <a class="reference internal" href="../glossary.html#term-binary-file"><span class="xref std std-term">binary file</span></a>, and
<code class="docutils literal notranslate"><span class="pre">tarinfo.size</span></code> bytes are read from it and added to the archive.  You can
create <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> objects directly, or by using <a class="reference internal" href="#tarfile.TarFile.gettarinfo" title="tarfile.TarFile.gettarinfo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">gettarinfo()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.gettarinfo">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">gettarinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arcname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fileobj</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.gettarinfo" title="Link to this definition">¶</a></dt>
<dd><p>Create a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object from the result of <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a> or
equivalent on an existing file.  The file is either named by <em>name</em>, or
specified as a <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> <em>fileobj</em> with a file descriptor.
<em>name</em> may be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.  If
given, <em>arcname</em> specifies an alternative name for the file in the
archive, otherwise, the name is taken from <em>fileobj</em>’s
<a class="reference internal" href="io.html#io.FileIO.name" title="io.FileIO.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a> attribute, or the <em>name</em> argument.  The name
should be a text string.</p>
<p>You can modify
some of the <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a>’s attributes before you add it using <a class="reference internal" href="#tarfile.TarFile.addfile" title="tarfile.TarFile.addfile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addfile()</span></code></a>.
If the file object is not an ordinary file object positioned at the
beginning of the file, attributes such as <a class="reference internal" href="#tarfile.TarInfo.size" title="tarfile.TarInfo.size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">size</span></code></a> may need
modifying.  This is the case for objects such as <a class="reference internal" href="gzip.html#gzip.GzipFile" title="gzip.GzipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">GzipFile</span></code></a>.
The <a class="reference internal" href="#tarfile.TarInfo.name" title="tarfile.TarInfo.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a> may also be modified, in which case <em>arcname</em>
could be a dummy string.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>name</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarFile.close">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarFile.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a>. In write mode, two finishing zero blocks are
appended to the archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarFile.pax_headers">
<span class="sig-prename descclassname"><span class="pre">TarFile.</span></span><span class="sig-name descname"><span class="pre">pax_headers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#dict" title="dict"><span class="pre">dict</span></a></em><a class="headerlink" href="#tarfile.TarFile.pax_headers" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary containing key-value pairs of pax global headers.</p>
</dd></dl>

</section>
<section id="tarinfo-objects">
<span id="id2"></span><h2>TarInfo Objects<a class="headerlink" href="#tarinfo-objects" title="Link to this heading">¶</a></h2>
<p>A <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object represents one member in a <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a>. Aside
from storing all required attributes of a file (like file type, size, time,
permissions, owner etc.), it provides some useful methods to determine its type.
It does <em>not</em> contain the file’s data itself.</p>
<p><a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> objects are returned by <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a>’s methods
<a class="reference internal" href="#tarfile.TarFile.getmember" title="tarfile.TarFile.getmember"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmember()</span></code></a>, <a class="reference internal" href="#tarfile.TarFile.getmembers" title="tarfile.TarFile.getmembers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmembers()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.gettarinfo" title="tarfile.TarFile.gettarinfo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">gettarinfo()</span></code></a>.</p>
<p>Modifying the objects returned by <a class="reference internal" href="#tarfile.TarFile.getmember" title="tarfile.TarFile.getmember"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmember()</span></code></a> or
<a class="reference internal" href="#tarfile.TarFile.getmembers" title="tarfile.TarFile.getmembers"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getmembers()</span></code></a> will affect all subsequent
operations on the archive.
For cases where this is unwanted, you can use <a class="reference internal" href="copy.html#module-copy" title="copy: Shallow and deep copy operations."><code class="xref py py-mod docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> or
call the <a class="reference internal" href="#tarfile.TarInfo.replace" title="tarfile.TarInfo.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">replace()</span></code></a> method to create a modified copy in one step.</p>
<p>Several attributes can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> to indicate that a piece of metadata
is unused or unknown.
Different <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> methods handle <code class="docutils literal notranslate"><span class="pre">None</span></code> differently:</p>
<ul class="simple">
<li><p>The <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> or <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a> methods will
ignore the corresponding metadata, leaving it set to a default.</p></li>
<li><p><a class="reference internal" href="#tarfile.TarFile.addfile" title="tarfile.TarFile.addfile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addfile()</span></code></a> will fail.</p></li>
<li><p><a class="reference internal" href="#tarfile.TarFile.list" title="tarfile.TarFile.list"><code class="xref py py-meth docutils literal notranslate"><span class="pre">list()</span></code></a> will print a placeholder string.</p></li>
</ul>
<dl class="py class">
<dt class="sig sig-object py" id="tarfile.TarInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">TarInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo" title="Link to this definition">¶</a></dt>
<dd><p>Create a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.frombuf">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">frombuf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">buf</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.frombuf" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object from string buffer <em>buf</em>.</p>
<p>Raises <a class="reference internal" href="#tarfile.HeaderError" title="tarfile.HeaderError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HeaderError</span></code></a> if the buffer is invalid.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.fromtarfile">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">fromtarfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tarfile</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.fromtarfile" title="Link to this definition">¶</a></dt>
<dd><p>Read the next member from the <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> object <em>tarfile</em> and return it as
a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.tobuf">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">tobuf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">DEFAULT_FORMAT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">ENCODING</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'surrogateescape'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.tobuf" title="Link to this definition">¶</a></dt>
<dd><p>Create a string buffer from a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object. For information on the
arguments see the constructor of the <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> class.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Use <code class="docutils literal notranslate"><span class="pre">'surrogateescape'</span></code> as the default for the <em>errors</em> argument.</p>
</div>
</dd></dl>

<p>A <code class="docutils literal notranslate"><span class="pre">TarInfo</span></code> object has the following public data attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.name">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">name</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></em><a class="headerlink" href="#tarfile.TarInfo.name" title="Link to this definition">¶</a></dt>
<dd><p>Name of the archive member.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.size">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">size</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.size" title="Link to this definition">¶</a></dt>
<dd><p>Size in bytes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.mtime">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">mtime</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></em><a class="headerlink" href="#tarfile.TarInfo.mtime" title="Link to this definition">¶</a></dt>
<dd><p>Time of last modification in seconds since the <a class="reference internal" href="time.html#epoch"><span class="std std-ref">epoch</span></a>,
as in <a class="reference internal" href="os.html#os.stat_result.st_mtime" title="os.stat_result.st_mtime"><code class="xref py py-attr docutils literal notranslate"><span class="pre">os.stat_result.st_mtime</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.mode">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">mode</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.mode" title="Link to this definition">¶</a></dt>
<dd><p>Permission bits, as for <a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.type">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">type</span></span><a class="headerlink" href="#tarfile.TarInfo.type" title="Link to this definition">¶</a></dt>
<dd><p>File type.  <em>type</em> is usually one of these constants: <a class="reference internal" href="#tarfile.REGTYPE" title="tarfile.REGTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">REGTYPE</span></code></a>,
<a class="reference internal" href="#tarfile.AREGTYPE" title="tarfile.AREGTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">AREGTYPE</span></code></a>, <a class="reference internal" href="#tarfile.LNKTYPE" title="tarfile.LNKTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LNKTYPE</span></code></a>, <a class="reference internal" href="#tarfile.SYMTYPE" title="tarfile.SYMTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">SYMTYPE</span></code></a>, <a class="reference internal" href="#tarfile.DIRTYPE" title="tarfile.DIRTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">DIRTYPE</span></code></a>,
<a class="reference internal" href="#tarfile.FIFOTYPE" title="tarfile.FIFOTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">FIFOTYPE</span></code></a>, <a class="reference internal" href="#tarfile.CONTTYPE" title="tarfile.CONTTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">CONTTYPE</span></code></a>, <a class="reference internal" href="#tarfile.CHRTYPE" title="tarfile.CHRTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">CHRTYPE</span></code></a>, <a class="reference internal" href="#tarfile.BLKTYPE" title="tarfile.BLKTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">BLKTYPE</span></code></a>,
<a class="reference internal" href="#tarfile.GNUTYPE_SPARSE" title="tarfile.GNUTYPE_SPARSE"><code class="xref py py-const docutils literal notranslate"><span class="pre">GNUTYPE_SPARSE</span></code></a>.  To determine the type of a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object
more conveniently, use the <code class="docutils literal notranslate"><span class="pre">is*()</span></code> methods below.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.linkname">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">linkname</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></em><a class="headerlink" href="#tarfile.TarInfo.linkname" title="Link to this definition">¶</a></dt>
<dd><p>Name of the target file name, which is only present in <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> objects
of type <a class="reference internal" href="#tarfile.LNKTYPE" title="tarfile.LNKTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">LNKTYPE</span></code></a> and <a class="reference internal" href="#tarfile.SYMTYPE" title="tarfile.SYMTYPE"><code class="xref py py-const docutils literal notranslate"><span class="pre">SYMTYPE</span></code></a>.</p>
<p>For symbolic links (<code class="docutils literal notranslate"><span class="pre">SYMTYPE</span></code>), the <em>linkname</em> is relative to the directory
that contains the link.
For hard links (<code class="docutils literal notranslate"><span class="pre">LNKTYPE</span></code>), the <em>linkname</em> is relative to the root of
the archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.uid">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">uid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.uid" title="Link to this definition">¶</a></dt>
<dd><p>User ID of the user who originally stored this member.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.gid">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">gid</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.gid" title="Link to this definition">¶</a></dt>
<dd><p>Group ID of the user who originally stored this member.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.uname">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">uname</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></em><a class="headerlink" href="#tarfile.TarInfo.uname" title="Link to this definition">¶</a></dt>
<dd><p>User name.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.gname">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">gname</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#str" title="str"><span class="pre">str</span></a></em><a class="headerlink" href="#tarfile.TarInfo.gname" title="Link to this definition">¶</a></dt>
<dd><p>Group name.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Can be set to <code class="docutils literal notranslate"><span class="pre">None</span></code> for <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> and
<a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>, causing extraction to skip applying this
attribute.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.chksum">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">chksum</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.chksum" title="Link to this definition">¶</a></dt>
<dd><p>Header checksum.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.devmajor">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">devmajor</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.devmajor" title="Link to this definition">¶</a></dt>
<dd><p>Device major number.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.devminor">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">devminor</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.devminor" title="Link to this definition">¶</a></dt>
<dd><p>Device minor number.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.offset">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">offset</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.offset" title="Link to this definition">¶</a></dt>
<dd><p>The tar header starts here.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.offset_data">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">offset_data</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></em><a class="headerlink" href="#tarfile.TarInfo.offset_data" title="Link to this definition">¶</a></dt>
<dd><p>The file’s data starts here.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.sparse">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">sparse</span></span><a class="headerlink" href="#tarfile.TarInfo.sparse" title="Link to this definition">¶</a></dt>
<dd><p>Sparse member information.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tarfile.TarInfo.pax_headers">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">pax_headers</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><a class="reference internal" href="stdtypes.html#dict" title="dict"><span class="pre">dict</span></a></em><a class="headerlink" href="#tarfile.TarInfo.pax_headers" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary containing key-value pairs of an associated pax extended header.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.replace">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mtime</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">linkname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">gname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">...</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">deep</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.replace" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Return a <em>new</em> copy of the <code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code> object with the given attributes
changed. For example, to return a <code class="docutils literal notranslate"><span class="pre">TarInfo</span></code> with the group name set to
<code class="docutils literal notranslate"><span class="pre">'staff'</span></code>, use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">new_tarinfo</span> <span class="o">=</span> <span class="n">old_tarinfo</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="n">gname</span><span class="o">=</span><span class="s1">&#39;staff&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>By default, a deep copy is made.
If <em>deep</em> is false, the copy is shallow, i.e. <code class="docutils literal notranslate"><span class="pre">pax_headers</span></code>
and any custom attributes are shared with the original <code class="docutils literal notranslate"><span class="pre">TarInfo</span></code> object.</p>
</dd></dl>

<p>A <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object also provides some convenient query methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isfile">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isfile</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isfile" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if the <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object is a regular file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isreg">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isreg</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isreg" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#tarfile.TarInfo.isfile" title="tarfile.TarInfo.isfile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">isfile()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isdir">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isdir" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a directory.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.issym">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">issym</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.issym" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a symbolic link.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.islnk">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">islnk</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.islnk" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a hard link.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.ischr">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">ischr</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.ischr" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a character device.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isblk">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isblk</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isblk" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a block device.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isfifo">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isfifo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isfifo" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is a FIFO.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tarfile.TarInfo.isdev">
<span class="sig-prename descclassname"><span class="pre">TarInfo.</span></span><span class="sig-name descname"><span class="pre">isdev</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.TarInfo.isdev" title="Link to this definition">¶</a></dt>
<dd><p>Return <a class="reference internal" href="constants.html#True" title="True"><code class="xref py py-const docutils literal notranslate"><span class="pre">True</span></code></a> if it is one of character device, block device or FIFO.</p>
</dd></dl>

</section>
<section id="extraction-filters">
<span id="tarfile-extraction-filter"></span><h2>Extraction filters<a class="headerlink" href="#extraction-filters" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>The <em>tar</em> format is designed to capture all details of a UNIX-like filesystem,
which makes it very powerful.
Unfortunately, the features make it easy to create tar files that have
unintended – and possibly malicious – effects when extracted.
For example, extracting a tar file can overwrite arbitrary files in various
ways (e.g.  by using absolute paths, <code class="docutils literal notranslate"><span class="pre">..</span></code> path components, or symlinks that
affect later members).</p>
<p>In most cases, the full functionality is not needed.
Therefore, <em>tarfile</em> supports extraction filters: a mechanism to limit
functionality, and thus mitigate some of the security issues.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0706/"><strong>PEP 706</strong></a></dt><dd><p>Contains further motivation and rationale behind the design.</p>
</dd>
</dl>
</div>
<p>The <em>filter</em> argument to <a class="reference internal" href="#tarfile.TarFile.extract" title="tarfile.TarFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.extract()</span></code></a> or <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a>
can be:</p>
<ul>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">'fully_trusted'</span></code>: Honor all metadata as specified in the
archive.
Should be used if the user trusts the archive completely, or implements
their own complex verification.</p></li>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">'tar'</span></code>: Honor most <em>tar</em>-specific features (i.e. features of
UNIX-like filesystems), but block features that are very likely to be
surprising or malicious. See <a class="reference internal" href="#tarfile.tar_filter" title="tarfile.tar_filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">tar_filter()</span></code></a> for details.</p></li>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">'data'</span></code>: Ignore or block most features specific to UNIX-like
filesystems. Intended for extracting cross-platform data archives.
See <a class="reference internal" href="#tarfile.data_filter" title="tarfile.data_filter"><code class="xref py py-func docutils literal notranslate"><span class="pre">data_filter()</span></code></a> for details.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">None</span></code> (default): Use <a class="reference internal" href="#tarfile.TarFile.extraction_filter" title="tarfile.TarFile.extraction_filter"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TarFile.extraction_filter</span></code></a>.</p>
<p>If that is also <code class="docutils literal notranslate"><span class="pre">None</span></code> (the default), raise a <code class="docutils literal notranslate"><span class="pre">DeprecationWarning</span></code>,
and fall back to the <code class="docutils literal notranslate"><span class="pre">'fully_trusted'</span></code> filter, whose dangerous behavior
matches previous versions of Python.</p>
<p>In Python 3.14, the <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter will become the default instead.
It’s possible to switch earlier; see <a class="reference internal" href="#tarfile.TarFile.extraction_filter" title="tarfile.TarFile.extraction_filter"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TarFile.extraction_filter</span></code></a>.</p>
</li>
<li><p>A callable which will be called for each extracted member with a
<a class="reference internal" href="#tarinfo-objects"><span class="std std-ref">TarInfo</span></a> describing the member and the destination
path to where the archive is extracted (i.e. the same path is used for all
members):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">filter</span><span class="p">(</span><span class="n">member</span><span class="p">:</span> <span class="n">TarInfo</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="o">/</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">TarInfo</span> <span class="o">|</span> <span class="kc">None</span>
</pre></div>
</div>
<p>The callable is called just before each member is extracted, so it can
take the current state of the disk into account.
It can:</p>
<ul class="simple">
<li><p>return a <a class="reference internal" href="#tarfile.TarInfo" title="tarfile.TarInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarInfo</span></code></a> object which will be used instead of the metadata
in the archive, or</p></li>
<li><p>return <code class="docutils literal notranslate"><span class="pre">None</span></code>, in which case the member will be skipped, or</p></li>
<li><p>raise an exception to abort the operation or skip the member,
depending on <a class="reference internal" href="#tarfile.TarFile.errorlevel" title="tarfile.TarFile.errorlevel"><code class="xref py py-attr docutils literal notranslate"><span class="pre">errorlevel</span></code></a>.
Note that when extraction is aborted, <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a> may leave
the archive partially extracted. It does not attempt to clean up.</p></li>
</ul>
</li>
</ul>
<section id="default-named-filters">
<h3>Default named filters<a class="headerlink" href="#default-named-filters" title="Link to this heading">¶</a></h3>
<p>The pre-defined, named filters are available as functions, so they can be
reused in custom filters:</p>
<dl class="py function">
<dt class="sig sig-object py" id="tarfile.fully_trusted_filter">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">fully_trusted_filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.fully_trusted_filter" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>member</em> unchanged.</p>
<p>This implements the <code class="docutils literal notranslate"><span class="pre">'fully_trusted'</span></code> filter.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tarfile.tar_filter">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">tar_filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.tar_filter" title="Link to this definition">¶</a></dt>
<dd><p>Implements the <code class="docutils literal notranslate"><span class="pre">'tar'</span></code> filter.</p>
<ul class="simple">
<li><p>Strip leading slashes (<code class="docutils literal notranslate"><span class="pre">/</span></code> and <a class="reference internal" href="os.html#os.sep" title="os.sep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.sep</span></code></a>) from filenames.</p></li>
<li><p><a class="reference internal" href="#tarfile-extraction-refuse"><span class="std std-ref">Refuse</span></a> to extract files with absolute
paths (in case the name is absolute
even after stripping slashes, e.g. <code class="docutils literal notranslate"><span class="pre">C:/foo</span></code> on Windows).
This raises <a class="reference internal" href="#tarfile.AbsolutePathError" title="tarfile.AbsolutePathError"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbsolutePathError</span></code></a>.</p></li>
<li><p><a class="reference internal" href="#tarfile-extraction-refuse"><span class="std std-ref">Refuse</span></a> to extract files whose absolute
path (after following symlinks) would end up outside the destination.
This raises <a class="reference internal" href="#tarfile.OutsideDestinationError" title="tarfile.OutsideDestinationError"><code class="xref py py-class docutils literal notranslate"><span class="pre">OutsideDestinationError</span></code></a>.</p></li>
<li><p>Clear high mode bits (setuid, setgid, sticky) and group/other write bits
(<a class="reference internal" href="stat.html#stat.S_IWGRP" title="stat.S_IWGRP"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IWGRP</span></code></a> | <a class="reference internal" href="stat.html#stat.S_IWOTH" title="stat.S_IWOTH"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IWOTH</span></code></a>).</p></li>
</ul>
<p>Return the modified <code class="docutils literal notranslate"><span class="pre">TarInfo</span></code> member.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tarfile.data_filter">
<span class="sig-prename descclassname"><span class="pre">tarfile.</span></span><span class="sig-name descname"><span class="pre">data_filter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tarfile.data_filter" title="Link to this definition">¶</a></dt>
<dd><p>Implements the <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter.
In addition to what <code class="docutils literal notranslate"><span class="pre">tar_filter</span></code> does:</p>
<ul>
<li><p><a class="reference internal" href="#tarfile-extraction-refuse"><span class="std std-ref">Refuse</span></a> to extract links (hard or soft)
that link to absolute paths, or ones that link outside the destination.</p>
<p>This raises <a class="reference internal" href="#tarfile.AbsoluteLinkError" title="tarfile.AbsoluteLinkError"><code class="xref py py-class docutils literal notranslate"><span class="pre">AbsoluteLinkError</span></code></a> or
<a class="reference internal" href="#tarfile.LinkOutsideDestinationError" title="tarfile.LinkOutsideDestinationError"><code class="xref py py-class docutils literal notranslate"><span class="pre">LinkOutsideDestinationError</span></code></a>.</p>
<p>Note that such files are refused even on platforms that do not support
symbolic links.</p>
</li>
<li><p><a class="reference internal" href="#tarfile-extraction-refuse"><span class="std std-ref">Refuse</span></a> to extract device files
(including pipes).
This raises <a class="reference internal" href="#tarfile.SpecialFileError" title="tarfile.SpecialFileError"><code class="xref py py-class docutils literal notranslate"><span class="pre">SpecialFileError</span></code></a>.</p></li>
<li><p>For regular files, including hard links:</p>
<ul class="simple">
<li><p>Set the owner read and write permissions
(<a class="reference internal" href="stat.html#stat.S_IRUSR" title="stat.S_IRUSR"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IRUSR</span></code></a> | <a class="reference internal" href="stat.html#stat.S_IWUSR" title="stat.S_IWUSR"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IWUSR</span></code></a>).</p></li>
<li><p>Remove the group &amp; other executable permission
(<a class="reference internal" href="stat.html#stat.S_IXGRP" title="stat.S_IXGRP"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IXGRP</span></code></a> | <a class="reference internal" href="stat.html#stat.S_IXOTH" title="stat.S_IXOTH"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IXOTH</span></code></a>)
if the owner doesn’t have it (<a class="reference internal" href="stat.html#stat.S_IXUSR" title="stat.S_IXUSR"><code class="xref py py-const docutils literal notranslate"><span class="pre">S_IXUSR</span></code></a>).</p></li>
</ul>
</li>
<li><p>For other files (directories), set <code class="docutils literal notranslate"><span class="pre">mode</span></code> to <code class="docutils literal notranslate"><span class="pre">None</span></code>, so
that extraction methods skip applying permission bits.</p></li>
<li><p>Set user and group info (<code class="docutils literal notranslate"><span class="pre">uid</span></code>, <code class="docutils literal notranslate"><span class="pre">gid</span></code>, <code class="docutils literal notranslate"><span class="pre">uname</span></code>, <code class="docutils literal notranslate"><span class="pre">gname</span></code>)
to <code class="docutils literal notranslate"><span class="pre">None</span></code>, so that extraction methods skip setting it.</p></li>
</ul>
<p>Return the modified <code class="docutils literal notranslate"><span class="pre">TarInfo</span></code> member.</p>
</dd></dl>

</section>
<section id="filter-errors">
<span id="tarfile-extraction-refuse"></span><h3>Filter errors<a class="headerlink" href="#filter-errors" title="Link to this heading">¶</a></h3>
<p>When a filter refuses to extract a file, it will raise an appropriate exception,
a subclass of <a class="reference internal" href="#tarfile.FilterError" title="tarfile.FilterError"><code class="xref py py-class docutils literal notranslate"><span class="pre">FilterError</span></code></a>.
This will abort the extraction if <a class="reference internal" href="#tarfile.TarFile.errorlevel" title="tarfile.TarFile.errorlevel"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TarFile.errorlevel</span></code></a> is 1 or more.
With <code class="docutils literal notranslate"><span class="pre">errorlevel=0</span></code> the error will be logged and the member will be skipped,
but extraction will continue.</p>
</section>
<section id="hints-for-further-verification">
<h3>Hints for further verification<a class="headerlink" href="#hints-for-further-verification" title="Link to this heading">¶</a></h3>
<p>Even with <code class="docutils literal notranslate"><span class="pre">filter='data'</span></code>, <em>tarfile</em> is not suited for extracting untrusted
files without prior inspection.
Among other issues, the pre-defined filters do not prevent denial-of-service
attacks. Users should do additional checks.</p>
<p>Here is an incomplete list of things to consider:</p>
<ul class="simple">
<li><p>Extract to a <a class="reference internal" href="tempfile.html#tempfile.mkdtemp" title="tempfile.mkdtemp"><code class="xref py py-func docutils literal notranslate"><span class="pre">new</span> <span class="pre">temporary</span> <span class="pre">directory</span></code></a>
to prevent e.g. exploiting pre-existing links, and to make it easier to
clean up after a failed extraction.</p></li>
<li><p>When working with untrusted data, use external (e.g. OS-level) limits on
disk, memory and CPU usage.</p></li>
<li><p>Check filenames against an allow-list of characters
(to filter out control characters, confusables, foreign path separators,
etc.).</p></li>
<li><p>Check that filenames have expected extensions (discouraging files that
execute when you “click on them”, or extension-less files like Windows special device names).</p></li>
<li><p>Limit the number of extracted files, total size of extracted data,
filename length (including symlink length), and size of individual files.</p></li>
<li><p>Check for files that would be shadowed on case-insensitive filesystems.</p></li>
</ul>
<p>Also note that:</p>
<ul class="simple">
<li><p>Tar files may contain multiple versions of the same file.
Later ones are expected to overwrite any earlier ones.
This feature is crucial to allow updating tape archives, but can be abused
maliciously.</p></li>
<li><p><em>tarfile</em> does not protect against issues with “live” data,
e.g. an attacker tinkering with the destination (or source) directory while
extraction (or archiving) is in progress.</p></li>
</ul>
</section>
<section id="supporting-older-python-versions">
<h3>Supporting older Python versions<a class="headerlink" href="#supporting-older-python-versions" title="Link to this heading">¶</a></h3>
<p>Extraction filters were added to Python 3.12, but may be backported to older
versions as security updates.
To check whether the feature is available, use e.g.
<code class="docutils literal notranslate"><span class="pre">hasattr(tarfile,</span> <span class="pre">'data_filter')</span></code> rather than checking the Python version.</p>
<p>The following examples show how to support Python versions with and without
the feature.
Note that setting <code class="docutils literal notranslate"><span class="pre">extraction_filter</span></code> will affect any subsequent operations.</p>
<ul>
<li><p>Fully trusted archive:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">my_tarfile</span><span class="o">.</span><span class="n">extraction_filter</span> <span class="o">=</span> <span class="p">(</span><span class="k">lambda</span> <span class="n">member</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="n">member</span><span class="p">)</span>
<span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">()</span>
</pre></div>
</div>
</li>
<li><p>Use the <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter if available, but revert to Python 3.11 behavior
(<code class="docutils literal notranslate"><span class="pre">'fully_trusted'</span></code>) if this feature is not available:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">my_tarfile</span><span class="o">.</span><span class="n">extraction_filter</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">tarfile</span><span class="p">,</span> <span class="s1">&#39;data_filter&#39;</span><span class="p">,</span>
                                       <span class="p">(</span><span class="k">lambda</span> <span class="n">member</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="n">member</span><span class="p">))</span>
<span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">()</span>
</pre></div>
</div>
</li>
<li><p>Use the <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter; <em>fail</em> if it is not available:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">(</span><span class="nb">filter</span><span class="o">=</span><span class="n">tarfile</span><span class="o">.</span><span class="n">data_filter</span><span class="p">)</span>
</pre></div>
</div>
<p>or:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">my_tarfile</span><span class="o">.</span><span class="n">extraction_filter</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">data_filter</span>
<span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">()</span>
</pre></div>
</div>
</li>
<li><p>Use the <code class="docutils literal notranslate"><span class="pre">'data'</span></code> filter; <em>warn</em> if it is not available:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">tarfile</span><span class="p">,</span> <span class="s1">&#39;data_filter&#39;</span><span class="p">):</span>
    <span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">(</span><span class="nb">filter</span><span class="o">=</span><span class="s1">&#39;data&#39;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="c1"># remove this when no longer needed</span>
    <span class="n">warn_the_user</span><span class="p">(</span><span class="s1">&#39;Extracting may be unsafe; consider updating Python&#39;</span><span class="p">)</span>
    <span class="n">my_tarfile</span><span class="o">.</span><span class="n">extractall</span><span class="p">()</span>
</pre></div>
</div>
</li>
</ul>
</section>
<section id="stateful-extraction-filter-example">
<h3>Stateful extraction filter example<a class="headerlink" href="#stateful-extraction-filter-example" title="Link to this heading">¶</a></h3>
<p>While <em>tarfile</em>’s extraction methods take a simple <em>filter</em> callable,
custom filters may be more complex objects with an internal state.
It may be useful to write these as context managers, to be used like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">StatefulFilter</span><span class="p">()</span> <span class="k">as</span> <span class="n">filter_func</span><span class="p">:</span>
    <span class="n">tar</span><span class="o">.</span><span class="n">extractall</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="nb">filter</span><span class="o">=</span><span class="n">filter_func</span><span class="p">)</span>
</pre></div>
</div>
<p>Such a filter can be written as, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">StatefulFilter</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">file_count</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span> <span class="fm">__enter__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span>

    <span class="k">def</span> <span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">member</span><span class="p">,</span> <span class="n">path</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">file_count</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="k">return</span> <span class="n">member</span>

    <span class="k">def</span> <span class="fm">__exit__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">exc_info</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">file_count</span><span class="si">}</span><span class="s1"> files extracted&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="command-line-interface">
<span id="tarfile-commandline"></span><h2>Command-Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>The <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module provides a simple command-line interface to interact
with tar archives.</p>
<p>If you want to create a new tar archive, specify its name after the <a class="reference internal" href="#cmdoption-tarfile-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a>
option and then list the filename(s) that should be included:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-c<span class="w"> </span>monty.tar<span class="w">  </span>spam.txt<span class="w"> </span>eggs.txt
</pre></div>
</div>
<p>Passing a directory is also acceptable:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-c<span class="w"> </span>monty.tar<span class="w"> </span>life-of-brian_1979/
</pre></div>
</div>
<p>If you want to extract a tar archive into the current directory, use
the <a class="reference internal" href="#cmdoption-tarfile-e"><code class="xref std std-option docutils literal notranslate"><span class="pre">-e</span></code></a> option:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-e<span class="w"> </span>monty.tar
</pre></div>
</div>
<p>You can also extract a tar archive into a different directory by passing the
directory’s name:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-e<span class="w"> </span>monty.tar<span class="w">  </span>other-dir/
</pre></div>
</div>
<p>For a list of the files in a tar archive, use the <a class="reference internal" href="#cmdoption-tarfile-l"><code class="xref std std-option docutils literal notranslate"><span class="pre">-l</span></code></a> option:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>tarfile<span class="w"> </span>-l<span class="w"> </span>monty.tar
</pre></div>
</div>
<section id="command-line-options">
<h3>Command-line options<a class="headerlink" href="#command-line-options" title="Link to this heading">¶</a></h3>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-l">
<span class="sig-name descname"><span class="pre">-l</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-l" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-tarfile-list">
<span class="sig-name descname"><span class="pre">--list</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-list" title="Link to this definition">¶</a></dt>
<dd><p>List files in a tarfile.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-c">
<span class="sig-name descname"><span class="pre">-c</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span> <span class="pre">&lt;source1&gt;</span> <span class="pre">...</span> <span class="pre">&lt;sourceN&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-c" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-tarfile-create">
<span class="sig-name descname"><span class="pre">--create</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span> <span class="pre">&lt;source1&gt;</span> <span class="pre">...</span> <span class="pre">&lt;sourceN&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-create" title="Link to this definition">¶</a></dt>
<dd><p>Create tarfile from source files.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-e">
<span class="sig-name descname"><span class="pre">-e</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span> <span class="pre">[&lt;output_dir&gt;]</span></span><a class="headerlink" href="#cmdoption-tarfile-e" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-tarfile-extract">
<span class="sig-name descname"><span class="pre">--extract</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span> <span class="pre">[&lt;output_dir&gt;]</span></span><a class="headerlink" href="#cmdoption-tarfile-extract" title="Link to this definition">¶</a></dt>
<dd><p>Extract tarfile into the current directory if <em>output_dir</em> is not specified.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-t">
<span class="sig-name descname"><span class="pre">-t</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-t" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-tarfile-test">
<span class="sig-name descname"><span class="pre">--test</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;tarfile&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-test" title="Link to this definition">¶</a></dt>
<dd><p>Test whether the tarfile is valid or not.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-v">
<span id="cmdoption-tarfile-verbose"></span><span class="sig-name descname"><span class="pre">-v</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--verbose</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-tarfile-v" title="Link to this definition">¶</a></dt>
<dd><p>Verbose output.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-tarfile-filter">
<span class="sig-name descname"><span class="pre">--filter</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;filtername&gt;</span></span><a class="headerlink" href="#cmdoption-tarfile-filter" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the <em>filter</em> for <code class="docutils literal notranslate"><span class="pre">--extract</span></code>.
See <a class="reference internal" href="#tarfile-extraction-filter"><span class="std std-ref">Extraction filters</span></a> for details.
Only string names are accepted (that is, <code class="docutils literal notranslate"><span class="pre">fully_trusted</span></code>, <code class="docutils literal notranslate"><span class="pre">tar</span></code>,
and <code class="docutils literal notranslate"><span class="pre">data</span></code>).</p>
</dd></dl>

</section>
</section>
<section id="examples">
<span id="tar-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>How to extract an entire tar archive to the current working directory:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tarfile</span>
<span class="n">tar</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar.gz&quot;</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">extractall</span><span class="p">(</span><span class="nb">filter</span><span class="o">=</span><span class="s1">&#39;data&#39;</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>How to extract a subset of a tar archive with <a class="reference internal" href="#tarfile.TarFile.extractall" title="tarfile.TarFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.extractall()</span></code></a> using
a generator function instead of a list:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">os</span>
<span class="kn">import</span> <span class="nn">tarfile</span>

<span class="k">def</span> <span class="nf">py_files</span><span class="p">(</span><span class="n">members</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">tarinfo</span> <span class="ow">in</span> <span class="n">members</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">splitext</span><span class="p">(</span><span class="n">tarinfo</span><span class="o">.</span><span class="n">name</span><span class="p">)[</span><span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;.py&quot;</span><span class="p">:</span>
            <span class="k">yield</span> <span class="n">tarinfo</span>

<span class="n">tar</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar.gz&quot;</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">extractall</span><span class="p">(</span><span class="n">members</span><span class="o">=</span><span class="n">py_files</span><span class="p">(</span><span class="n">tar</span><span class="p">))</span>
<span class="n">tar</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>How to create an uncompressed tar archive from a list of filenames:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tarfile</span>
<span class="n">tar</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">,</span> <span class="s2">&quot;quux&quot;</span><span class="p">]:</span>
    <span class="n">tar</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">name</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>The same example using the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tarfile</span>
<span class="k">with</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">tar</span><span class="p">:</span>
    <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="s2">&quot;bar&quot;</span><span class="p">,</span> <span class="s2">&quot;quux&quot;</span><span class="p">]:</span>
        <span class="n">tar</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">name</span><span class="p">)</span>
</pre></div>
</div>
<p>How to read a gzip compressed tar archive and display some member information:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tarfile</span>
<span class="n">tar</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar.gz&quot;</span><span class="p">,</span> <span class="s2">&quot;r:gz&quot;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">tarinfo</span> <span class="ow">in</span> <span class="n">tar</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">tarinfo</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="s2">&quot;is&quot;</span><span class="p">,</span> <span class="n">tarinfo</span><span class="o">.</span><span class="n">size</span><span class="p">,</span> <span class="s2">&quot;bytes in size and is &quot;</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">tarinfo</span><span class="o">.</span><span class="n">isreg</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;a regular file.&quot;</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">tarinfo</span><span class="o">.</span><span class="n">isdir</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;a directory.&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;something else.&quot;</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>How to create an archive and reset the user information using the <em>filter</em>
parameter in <a class="reference internal" href="#tarfile.TarFile.add" title="tarfile.TarFile.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TarFile.add()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tarfile</span>
<span class="k">def</span> <span class="nf">reset</span><span class="p">(</span><span class="n">tarinfo</span><span class="p">):</span>
    <span class="n">tarinfo</span><span class="o">.</span><span class="n">uid</span> <span class="o">=</span> <span class="n">tarinfo</span><span class="o">.</span><span class="n">gid</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">tarinfo</span><span class="o">.</span><span class="n">uname</span> <span class="o">=</span> <span class="n">tarinfo</span><span class="o">.</span><span class="n">gname</span> <span class="o">=</span> <span class="s2">&quot;root&quot;</span>
    <span class="k">return</span> <span class="n">tarinfo</span>
<span class="n">tar</span> <span class="o">=</span> <span class="n">tarfile</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;sample.tar.gz&quot;</span><span class="p">,</span> <span class="s2">&quot;w:gz&quot;</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="s2">&quot;foo&quot;</span><span class="p">,</span> <span class="nb">filter</span><span class="o">=</span><span class="n">reset</span><span class="p">)</span>
<span class="n">tar</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="supported-tar-formats">
<span id="tar-formats"></span><h2>Supported tar formats<a class="headerlink" href="#supported-tar-formats" title="Link to this heading">¶</a></h2>
<p>There are three tar formats that can be created with the <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> module:</p>
<ul>
<li><p>The POSIX.1-1988 ustar format (<a class="reference internal" href="#tarfile.USTAR_FORMAT" title="tarfile.USTAR_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">USTAR_FORMAT</span></code></a>). It supports filenames
up to a length of at best 256 characters and linknames up to 100 characters.
The maximum file size is 8 GiB. This is an old and limited but widely
supported format.</p></li>
<li><p>The GNU tar format (<a class="reference internal" href="#tarfile.GNU_FORMAT" title="tarfile.GNU_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">GNU_FORMAT</span></code></a>). It supports long filenames and
linknames, files bigger than 8 GiB and sparse files. It is the de facto
standard on GNU/Linux systems. <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> fully supports the GNU tar
extensions for long names, sparse file support is read-only.</p></li>
<li><p>The POSIX.1-2001 pax format (<a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a>). It is the most flexible
format with virtually no limits. It supports long filenames and linknames, large
files and stores pathnames in a portable way. Modern tar implementations,
including GNU tar, bsdtar/libarchive and star, fully support extended <em>pax</em>
features; some old or unmaintained libraries may not, but should treat
<em>pax</em> archives as if they were in the universally supported <em>ustar</em> format.
It is the current default format for new archives.</p>
<p>It extends the existing <em>ustar</em> format with extra headers for information
that cannot be stored otherwise. There are two flavours of pax headers:
Extended headers only affect the subsequent file header, global
headers are valid for the complete archive and affect all following files.
All the data in a pax header is encoded in <em>UTF-8</em> for portability reasons.</p>
</li>
</ul>
<p>There are some more variants of the tar format which can be read, but not
created:</p>
<ul class="simple">
<li><p>The ancient V7 format. This is the first tar format from Unix Seventh Edition,
storing only regular files and directories. Names must not be longer than 100
characters, there is no user/group name information. Some archives have
miscalculated header checksums in case of fields with non-ASCII characters.</p></li>
<li><p>The SunOS tar extended format. This format is a variant of the POSIX.1-2001
pax format, but is not compatible.</p></li>
</ul>
</section>
<section id="unicode-issues">
<span id="tar-unicode"></span><h2>Unicode issues<a class="headerlink" href="#unicode-issues" title="Link to this heading">¶</a></h2>
<p>The tar format was originally conceived to make backups on tape drives with the
main focus on preserving file system information. Nowadays tar archives are
commonly used for file distribution and exchanging archives over networks. One
problem of the original format (which is the basis of all other formats) is
that there is no concept of supporting different character encodings. For
example, an ordinary tar archive created on a <em>UTF-8</em> system cannot be read
correctly on a <em>Latin-1</em> system if it contains non-<em>ASCII</em> characters. Textual
metadata (like filenames, linknames, user/group names) will appear damaged.
Unfortunately, there is no way to autodetect the encoding of an archive. The
pax format was designed to solve this problem. It stores non-ASCII metadata
using the universal character encoding <em>UTF-8</em>.</p>
<p>The details of character conversion in <a class="reference internal" href="#module-tarfile" title="tarfile: Read and write tar-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code></a> are controlled by the
<em>encoding</em> and <em>errors</em> keyword arguments of the <a class="reference internal" href="#tarfile.TarFile" title="tarfile.TarFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">TarFile</span></code></a> class.</p>
<p><em>encoding</em> defines the character encoding to use for the metadata in the
archive. The default value is <a class="reference internal" href="sys.html#sys.getfilesystemencoding" title="sys.getfilesystemencoding"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.getfilesystemencoding()</span></code></a> or <code class="docutils literal notranslate"><span class="pre">'ascii'</span></code>
as a fallback. Depending on whether the archive is read or written, the
metadata must be either decoded or encoded. If <em>encoding</em> is not set
appropriately, this conversion may fail.</p>
<p>The <em>errors</em> argument defines how characters are treated that cannot be
converted. Possible values are listed in section <a class="reference internal" href="codecs.html#error-handlers"><span class="std std-ref">Error Handlers</span></a>.
The default scheme is <code class="docutils literal notranslate"><span class="pre">'surrogateescape'</span></code> which Python also uses for its
file system calls, see <a class="reference internal" href="os.html#os-filenames"><span class="std std-ref">File Names, Command Line Arguments, and Environment Variables</span></a>.</p>
<p>For <a class="reference internal" href="#tarfile.PAX_FORMAT" title="tarfile.PAX_FORMAT"><code class="xref py py-const docutils literal notranslate"><span class="pre">PAX_FORMAT</span></code></a> archives (the default), <em>encoding</em> is generally not needed
because all the metadata is stored using <em>UTF-8</em>. <em>encoding</em> is only used in
the rare cases when binary pax headers are decoded or when strings with
surrogate characters are stored.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a><ul>
<li><a class="reference internal" href="#tarfile-objects">TarFile Objects</a></li>
<li><a class="reference internal" href="#tarinfo-objects">TarInfo Objects</a></li>
<li><a class="reference internal" href="#extraction-filters">Extraction filters</a><ul>
<li><a class="reference internal" href="#default-named-filters">Default named filters</a></li>
<li><a class="reference internal" href="#filter-errors">Filter errors</a></li>
<li><a class="reference internal" href="#hints-for-further-verification">Hints for further verification</a></li>
<li><a class="reference internal" href="#supporting-older-python-versions">Supporting older Python versions</a></li>
<li><a class="reference internal" href="#stateful-extraction-filter-example">Stateful extraction filter example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#supported-tar-formats">Supported tar formats</a></li>
<li><a class="reference internal" href="#unicode-issues">Unicode issues</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipfile.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="fileformats.html"
                          title="next chapter">File Formats</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tarfile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="fileformats.html" title="File Formats"
             >next</a> |</li>
        <li class="right" >
          <a href="zipfile.html" title="zipfile — Work with ZIP archives"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" >Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>