/** @odoo-module */

import { Component, useEffect, useRef } from "@odoo/owl";
import { CenteredIcon } from "@point_of_sale/app/generic_components/centered_icon/centered_icon";
import { _t } from "@web/core/l10n/translation";

export class OrderWidget extends Component {
    static template = "point_of_sale.OrderWidget";
    static props = {
        lines: { type: Array, element: Object },
        slots: { type: Object },
        total: { type: String, optional: true },
        tax: { type: String, optional: true },
        editable: { type: Boolean, optional: true },
    };
    static defaultProps = {
        editable: true,
    }
    static components = { CenteredIcon };
    setup() {
        this.scrollableRef = useRef("scrollable");
        useEffect(() => {
            this.scrollableRef.el
                ?.querySelector(".orderline.selected")
                ?.scrollIntoView({ behavior: "smooth", block: "start" });
        });
    }
    emptyCartText() {
        return _t("Start adding products");
    }
}
