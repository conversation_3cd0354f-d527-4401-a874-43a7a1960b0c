# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_purchase
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: project_purchase
#: model:ir.model.fields,field_description:project_purchase.field_project_project__purchase_orders_count
msgid "# Purchase Orders"
msgstr "Pirkimo užsakymo nr."

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_project_project
msgid "Project"
msgstr "Projektas"

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
#, python-format
msgid "Purchase Order Items"
msgstr ""

#. module: project_purchase
#: model:ir.model,name:project_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Pirkimo užsakymo eilutė "

#. module: project_purchase
#. odoo-python
#: code:addons/project_purchase/models/project_project.py:0
#: code:addons/project_purchase/models/project_project.py:0
#: code:addons/project_purchase/models/project_project.py:0
#, python-format
msgid "Purchase Orders"
msgstr "Pirkimų užsakymai"
