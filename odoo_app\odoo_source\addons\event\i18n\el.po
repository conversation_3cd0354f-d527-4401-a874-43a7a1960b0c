# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event
# 
# Translators:
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2018
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "${object.event_id.name}: ${object.get_date_range_str()}"
msgstr "${object.event_id.name}: ${object.get_date_range_str()}"

#. module: event
#: code:addons/event/models/event.py:303
#, python-format
msgid "%s (copy)"
msgstr "%s (αντίγραφο)"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "5 Intensive Days"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid ""
"<b><i class=\"fa fa-clock-o\"/>\n"
"                                        To</b>"
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<div>\n"
"    Dear ${object.name},<br/>\n"
"    Thank you for your inquiry.<br/>\n"
"    Here is your badge for the event ${object.event_id.name}.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,<br/>\n"
"    % if object.event_id.user_id and object.event_id.user_id.signature:\n"
"        ${object.event_id.user_id.signature | safe}\n"
"    % endif\n"
"</div>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<em>(Chamber Works reserves the right to cancel, re-name or re-locate<br>the"
" event or change the dates on which it is held.)</em>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<font style=\"color: rgb(255, 255, 255); font-size: 24px;\">to learn <i>Odoo"
" Programming</i></font>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i>You will be able to create dynamic page interacting with the ORM.</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i>You will be able to develop a full application with backend and user "
"interface.</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i>You will be able to develop simple dynamic components in HTML pages.</i>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "<i>to</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<small>John Doe, CEO</small>"
msgstr "<small>John Doe, Διευθύνων Σύμβουλος</small>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" class=\"oe_read_only\">\n"
"                                        to\n"
"                                    </span>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid ""
"<span class=\"o_form_label\">Warning: This Event has not reached its Minimum"
" Registration Limit. Are you sure you want to confirm it?</span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<span style=\"text-align: -webkit-center; \">This course is dedicated to "
"developers who need to grasp knowledge of the <strong>business applications "
"development </strong>process. This course is for new developers or for IT "
"professionals eager to learn more about technical aspects.</span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Conference for Architects</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<strong>Having attended this course, participants should be able "
"to:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "<strong>Objectives:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<strong>Our prices include:</strong>"
msgstr "<strong>Οι τιμές μας περιλαμβάνουν:</strong>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Program:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<strong>Requirements:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Where to find us:</strong>"
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"% set date_end = format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name or ''},<br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        for attendee ${object.name}.\n"
"                    </div>\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"https://bay02.calendar.live.com/calendar/calendar.aspx?rru=addevent&amp;summary=${object.event_id.name}&amp;dtstart=${date_begin}&amp;dtend=${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Outlook</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%S')}&amp;et=${format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%S')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        % if object.company_id\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"% set date_begin = format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"% set date_end = format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%SZ')\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"${'/logo.png?company=%s' % object.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${'%s' % object.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello ${object.name},<br/>\n"
"                        We are excited to remind you that the event\n"
"                        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                            <a href=\"${object.event_id.website_url}\" style=\"color:#875A7B;text-decoration:none;\">${object.event_id.name}</a>\n"
"                        % else:\n"
"                            <strong>${object.event_id.name}</strong>\n"
"                        % endif\n"
"                        is starting <strong>${object.get_date_range_str()}</strong>.\n"
"                    </div>\n"
"                    % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"                    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                        <a href=\"${object.event_id.website_url}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">View Event</a><br/>\n"
"                    </div>\n"
"                    % endif\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        % if object.event_id.organizer_id:\n"
"                            ${object.event_id.organizer_id.name}\n"
"                        % else:\n"
"                            The organizers.\n"
"                        % endif\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> ${object.event_id.date_begin_located}</div>\n"
"                                <div><strong>To</strong> ${object.event_id.date_end_located}</div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> ${object.event_id.date_tz}</i></div>\n"
"                            </td>\n"
"                            % if object.event_id.address_id.country_id.name:\n"
"                                <td style=\"vertical-align:top;\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </td>\n"
"                                <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                    % set location = ''\n"
"                                    % if object.event_id.address_id.name:\n"
"                                        <div>${object.event_id.address_id.name}</div>\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street:\n"
"                                        <div>${object.event_id.address_id.street}</div>\n"
"                                        % set location = object.event_id.address_id.street\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.street2:\n"
"                                        <div>${object.event_id.address_id.street2}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.street2)\n"
"                                    % endif\n"
"                                    <div>\n"
"                                    % if object.event_id.address_id.city:\n"
"                                        ${object.event_id.address_id.city},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.city)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.state_id.name:\n"
"                                        ${object.event_id.address_id.state_id.name},\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.state_id.name)\n"
"                                    % endif\n"
"                                    % if object.event_id.address_id.zip:\n"
"                                        ${object.event_id.address_id.zip}\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.zip)\n"
"                                    % endif\n"
"                                    </div>\n"
"                                    % if object.event_id.address_id.country_id.name:\n"
"                                        <div>${object.event_id.address_id.country_id.name}</div>\n"
"                                        % set location = '%s, %s' % (location, object.event_id.address_id.country_id.name)\n"
"                                    % endif\n"
"                                </td>\n"
"                            % endif\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.organizer_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                        <div>Please contact the organizer:</div>\n"
"                        <ul>\n"
"                            <li>${object.event_id.organizer_id.name}</li>\n"
"                            % if object.event_id.organizer_id.email\n"
"                                <li>Mail: <a href=\"mailto:${object.event_id.organizer_id.email}\" style=\"text-decoration:none;color:#875A7B;\">${object.event_id.organizer_id.email}</a></li>\n"
"                            % endif\n"
"                            % if object.event_id.organizer_id.phone\n"
"                                <li>Phone: ${object.event_id.organizer_id.phone}</li>\n"
"                            % endif\n"
"                        </ul>\n"
"                    </div>\n"
"                </td></tr>\n"
"                % endif\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                        <tr>\n"
"                            <td style=\"padding:25px 0px;\">\n"
"                                <strong>Add this event to your calendar</strong>\n"
"                                <a href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text=${object.event_id.name}&amp;dates=${date_begin}/${date_end}&amp;location=${location}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                                <a href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title=${object.event_id.name}&amp;in_loc=${location}&amp;st=${format_tz(object.event_id.date_begin, tz='UTC', format='%Y%m%dT%H%M%S')}&amp;et=${format_tz(object.event_id.date_end, tz='UTC', format='%Y%m%dT%H%M%S')}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                                    <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo</a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"                % if object.event_id.address_id:\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                                <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"                % endif\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        % if object.company_id:\n"
"        Sent by <a target=\"_blank\" href=\"${object.company_id.website}\" style=\"color: #875A7B;\">${object.company_id.name}</a>\n"
"        % if 'website_url' in object.event_id and object.event_id.website_url:\n"
"        <br/>\n"
"        Discover <a href=\"/event\" style=\"text-decoration:none;color:#717188;\">all our events</a>.\n"
"        % endif\n"
"        % endif\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "Απαιτείται ενέργεια"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
msgid "Active"
msgstr "Σε Ισχύ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
msgid "Add a new attendee"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Add a new event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Advanced JQuery"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "After each registration"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "After the event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr "Αρχειοθετημένα"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"Are you sure you want to cancel this event? All the linked attendees will be"
" cancelled as well."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Arrays"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "Παρουσίες"

#. module: event
#: selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "Συμμετοχή"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "Ημερομηνία Συμμετοχής"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Attended the Event"
msgstr "Συμμετοχή στην Εκδήλωση"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Συμμετέχων"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
msgid "Attendee Name"
msgstr "Όνομα Συμμετέχοντα"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "Συμμετέχοντες"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__auto_confirm
msgid "Autoconfirm Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__auto_confirm
msgid "Automatically Confirm Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_mail_schedule
msgid "Automatically Send Emails"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
msgid "Available Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_back
msgid "Badge Back"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_front
msgid "Badge Front"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerleft
msgid "Badge Inner Left"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__badge_innerright
msgid "Badge Inner Right"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_barcode
msgid "Barcode"
msgstr "Barcode"

#. module: event
#: selection:event.mail,interval_type:0
#: selection:event.type.mail,interval_type:0
msgid "Before the event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Best regards,"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Bootstrap CSS"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Bring your own laptop."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Building a Full Application"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Calling the ORM"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Cancel"
msgstr "Ακύρωση"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Cancel Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel Registration"
msgstr "Ακύρωση Συμμετοχής"

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
msgid "Cancelled"
msgstr "Ακυρώθηκε"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
msgid "Category"
msgstr "Κατηγορία"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Chamber Works 60, Rosewood Court Detroit, MI 48212 (United States)"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "Επικοινωνία"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
msgid "Company"
msgstr "Εταιρία"

#. module: event
#: code:addons/event/models/event.py:504
#, python-format
msgid "Compose Email"
msgstr "Σύνταξη Email"

#. module: event
#: model:event.type,name:event.event_type_2
msgid "Conference"
msgstr ""

#. module: event
#: model:event.event,name:event.event_2
msgid "Conference for Architects"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "Διαμόρφωση"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Confirm"
msgstr "Επιβεβαίωση"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Confirm Anyway"
msgstr "Επιβεβαίωση ούτως ή άλλως"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Confirm Event"
msgstr "Επιβεβαίωση Εκδήλωσης"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm Registration"
msgstr "Επιβεβαίωση Καταχώρισης"

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Confirmed"
msgstr "Επιβεβαιώθηκε"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Confirmed attendees"
msgstr "Επιβεβαιωμένες συμμετοχές"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed events"
msgstr "Επιβεβαιωμένες εκδηλώσεις"

#. module: event
#: model:ir.model,name:event.model_res_partner
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
msgid "Contact"
msgstr "Επαφή"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Controllers and Views"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "Χώρα"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Course Summary"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_uid
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__create_date
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: event
#: code:addons/event/models/event.py:455
#, python-format
msgid "Customer"
msgstr "Πελάτης"

#. module: event
#: code:addons/event/models/event.py:457
#, python-format
msgid "Customer Email"
msgstr "Email Πελάτη"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Day 1"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Day 2"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Day 3"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Day(s)"
msgstr "Ημέρα(ες)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Delete"
msgstr "Διαγραφή"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
msgid "Description"
msgstr "Περιγραφή"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Develop a new module for a particular application."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__display_name
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "Σειρά εμφάνισης"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Dive into Strings"
msgstr ""

#. module: event
#: selection:event.event,state:0
msgid "Done"
msgstr "Ολοκληρωμένη"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Dropdown menu"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "Email"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Email Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_id
msgid "Email Template"
msgstr "Πρότυπο email"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "Ημερ. Λήξης"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_pivot
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "Συμβάν"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.report_event_event_badge
msgid "Event Badge"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Categories"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Category"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_confirm
#: model:ir.model,name:event.model_event_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Event Confirmation"
msgstr "Επιβεβαίωση εκδήλωσης"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "Ημερ. Λήξης Εκδήλωσης"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "Πληροφορίες Εκδήλωσης"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_logo
msgid "Event Logo"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "Χρονοπρογραμματιστής email Εκδήλωσης"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "Χρονοπρογραμματιστές email Εκδήλωσης"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr ""

#. module: event
#: code:addons/event/models/event_mail.py:143
#, python-format
msgid ""
"Event Scheduler for:\n"
"                              - Event: %s (%s)\n"
"                              - Scheduled: %s\n"
"                              - Template: %s (%s)\n"
"\n"
"                            Failed with error:\n"
"                              - %s\n"
"\n"
"                            You receive this email because you are:\n"
"                              - the organizer of the event,\n"
"                              - or the responsible of the event,\n"
"                              - or the last writer of the template."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "Τύπος Εκδήλωσης"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
#: model:ir.cron,name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr "Εκδήλωση: Χρονοπρογραμματιστής email"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.ui.menu,name:event.event_event_menu_pivot_report
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "Εκδηλώσεις"

#. module: event
#: model:ir.actions.act_window,name:event.event_event_action_pivot
msgid "Events Analysis"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "Εκδηλώσεις: Χρονοπρογραμματιστές email"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Events and registrations will automatically be confirmed\n"
"                                            upon creation, easing the flow for simple events."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__auto_confirm
msgid ""
"Events and registrations will automatically be confirmed upon creation, "
"easing the flow for simple events."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Events in New state"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Exhibition"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr "Αναμένεται"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr "Αναμενόμενοι συμμετέχοντες"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Finish Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "Ακόλουθοι"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_channel_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_channel_ids
msgid "Followers (Channels)"
msgstr "Ακόλουθοι (Κανάλια)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "Ακόλουθοι (Συνεργάτες)"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "For any additional information, please contact us at"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_min
msgid ""
"For each event you can define a minimum reserved seats (number of "
"attendees), if it does not reach the mentioned registrations the event can "
"not be confirmed (keep 0 to ignore this rule)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Functions"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "Ομαδοποίηση κατά"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Hello World"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Hour(s)"
msgstr "Ώρα"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "How to Debug"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"I did not expect such a great learning experience. I feel like I can develop"
" anything now."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__id
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
msgid "ID"
msgstr "Κωδικός"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_unread
#: model:ir.model.fields,help:event.field_event_registration__message_unread
msgid "If checked new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Εάν επιλεγεί τα νέα μηνύματα χρειάζονται την προσοχή σας."

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__state
msgid ""
"If event is created, the status is 'Draft'. If event is confirmed for the "
"particular dates the status is set to 'Confirmed'. If the event is over, the"
" status is set to 'Done'. If event is cancelled the status is set to "
"'Cancelled'."
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Immediately"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Install and administer your own server;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Integrated Help"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "Διάστημα"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Introduction to JQuery"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Introduction to Javascript"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Introduction to QWeb"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "Είναι Ακόλουθος"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__default_registration_min
msgid "It will select this default minimum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__color
msgid "Kanban Color Index"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm____last_update
#: model:ir.model.fields,field_description:event.field_event_event____last_update
#: model:ir.model.fields,field_description:event.field_event_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_type____last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail____last_update
msgid "Last Modified on"
msgstr "Τελευταία τροποποίηση στις"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_uid
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_confirm__write_date
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: event
#: selection:event.event,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Location"
msgstr "Τοποθεσία"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "Logo"
msgstr "Λογότυπο"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Loops"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Luigi Roni, Senior Event Manager"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "Χρονοπρογραμματιστής email"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "Προγραμματιστές email"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_sent
msgid "Mail Sent on Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_main_attachment_id
#: model:ir.model.fields,field_description:event.field_event_registration__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Manager"
msgstr "Διευθυντής"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_availability
msgid "Maximum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
msgid "Maximum Attendees Number"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_max
msgid "Maximum Registrations"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:258
#, python-format
msgid ""
"Maximum attendees number should be greater than minimum attendees number."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "Μηνύματα"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_min
msgid "Minimum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__default_registration_min
msgid "Minimum Registrations"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Modules"
msgstr "Αρθρώματα"

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Month(s)"
msgstr "Μήνας(ες)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Οι Εκδηλώσεις Μου"

#. module: event
#: code:addons/event/models/event.py:263
#, python-format
msgid "No more available seats."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:369
#, python-format
msgid "No more seats available for this event."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "Πλήθος ενεργειών"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_expected
msgid "Number of Expected Attendees"
msgstr "Πλήθος των αναμενόμενων συμμετεχόντων"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Participants"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_res_partner__event_count
msgid "Number of events the partner has participated."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Πλήθος μηνυμάτων που απαιτούν ενέργεια"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_unread_counter
#: model:ir.model.fields,help:event.field_event_registration__message_unread_counter
msgid "Number of unread messages"
msgstr "Πλήθος μη αναγνωσμένων μηνυμάτων"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Odoo Official Website"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Odoo Web Client"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_data_online
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Online"
msgstr "Online"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_online
#: model:ir.model.fields,field_description:event.field_event_type__is_online
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Online Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Online Events"
msgstr "Εκδηλώσεις OnLine"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid ""
"Online events like webinars do not require a specific location\n"
"                                            and are hosted online."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type__is_online
msgid ""
"Online events like webinars do not require a specific location and are "
"hosted online."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
msgid "Organizer"
msgstr "Ατζέντα"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "Συμμετέχων"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Participants are expected to have some knowledge in programming. A basic "
"knowledge of the Python programming is recommended."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Participants preferably have a functional knowledge of our software (see "
"Functional Training)."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Συναλλασόμενος"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "Τηλέφωνο"

#. module: event
#: model:event.type,name:event.event_type_data_physical
msgid "Physical Event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Python Objects"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Read Great Contents"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_registration__origin
msgid ""
"Reference of the document that created the registration, for example a sales"
" order"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Register with this event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr ""

#. module: event
#: model:ir.actions.report,name:event.report_event_registration_badge
msgid "Registration Badge"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_open
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_questions
msgid "Registration Survey"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Report Engine"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "Αναφορές"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Requirements"
msgstr "Απαιτήσεις"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
msgid "Reserved Seats"
msgstr "Κρατημένες Θέσεις"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr "Υπεύθυνοι"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events efficiently:\n"
"                track registrations and participations, automate the confirmation emails, sell tickets, etc."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Scheduled Sent Mail"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Seminar"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "Αποστολή με Email"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__done
msgid "Sent"
msgstr "Εστάλη"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Set To Draft"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr "Ορισμός ως μη επιβεβαιωμένες"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "Ρυθμίσεις"

#. module: event
#: model:event.type,name:event.event_type_3
msgid "Show"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__origin
msgid "Source Document"
msgstr "Έγγραφο Πηγή"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "Ημερομηνία Έναρξης"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__state
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Κατάσταση"

#. module: event
#: code:addons/event/models/event.py:269
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:313
#, python-format
msgid ""
"There are already attendees who attended this event. Please reset it to "
"draft if you want to cancel this event."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This course help me build my first application within a month. Definetly "
"worth its price."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__template_id
#: model:ir.model.fields,help:event.field_event_type_mail__template_id
msgid ""
"This field contains the template of the mail that will be automatically sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
msgid "Tickets"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "Ζώνη Ώρας"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "To get more information, visit the"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_4
msgid "Training"
msgstr "Εκπαίδευση"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Training Center Module"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "Έναυσμα"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__twitter_hashtag
#: model:ir.model.fields,field_description:event.field_event_type__default_hashtag
msgid "Twitter Hashtag"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "Μη Επιβεβαιωμένη"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr "Ανεπιβεβαίωτες Κρατήσεις Θέσεων"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Underscore"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Understand the development concepts and architecture;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "Μονάδα"

#. module: event
#: selection:event.event,seats_availability:0
msgid "Unlimited"
msgstr "Απεριόριστο"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "Μη αναγνωσμένα μηνύματα"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Μετρητής μη αναγνωσμένων μηνυμάτων"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_hashtag
msgid "Use Default Hashtag"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__use_timezone
msgid "Use Default Timezone"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Χρήστης"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Variables &amp; Operators"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Visibility"
msgstr "Ορατότητα"

#. module: event
#: code:addons/event/models/event_mail.py:130
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
#: selection:event.type.mail,interval_unit:0
msgid "Week(s)"
msgstr "Εβδομάδα(ες)"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What do people say about this course?"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What you will learn?"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Workflows"
msgstr "Ροές Εργασίας"

#. module: event
#: code:addons/event/models/event.py:426
#, python-format
msgid "You must wait the event confirmation before doing this action."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:428
#, python-format
msgid "You must wait the event starting day before doing this action."
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid "badge_of_${(object.event_id.name or '').replace('/','_')}"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "drinks and lunch;"
msgstr "Ποτά και γεύμα;"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: code:addons/event/models/event.py:526
#, python-format
msgid "in %d days"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:530
#, python-format
msgid "next month"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:528
#, python-format
msgid "next week"
msgstr "επόμενη εβδομάδα"

#. module: event
#: code:addons/event/models/event.py:532
#, python-format
msgid "on "
msgstr ""

#. module: event
#: code:addons/event/models/event.py:522
#, python-format
msgid "today"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:524
#, python-format
msgid "tomorrow"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "training material."
msgstr ""
