<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Initialization, Finalization, and Threads" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/init.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="See also Python Initialization Configuration. Before Python Initialization: In an application embedding Python, the Py_Initialize() function must be called before using any other Python/C API funct..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="See also Python Initialization Configuration. Before Python Initialization: In an application embedding Python, the Py_Initialize() function must be called before using any other Python/C API funct..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Initialization, Finalization, and Threads &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Python Initialization Configuration" href="init_config.html" />
    <link rel="prev" title="Objects for Type Hinting" href="typehints.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/init.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Initialization, Finalization, and Threads</a><ul>
<li><a class="reference internal" href="#before-python-initialization">Before Python Initialization</a></li>
<li><a class="reference internal" href="#global-configuration-variables">Global configuration variables</a></li>
<li><a class="reference internal" href="#initializing-and-finalizing-the-interpreter">Initializing and finalizing the interpreter</a></li>
<li><a class="reference internal" href="#process-wide-parameters">Process-wide parameters</a></li>
<li><a class="reference internal" href="#thread-state-and-the-global-interpreter-lock">Thread State and the Global Interpreter Lock</a><ul>
<li><a class="reference internal" href="#releasing-the-gil-from-extension-code">Releasing the GIL from extension code</a></li>
<li><a class="reference internal" href="#non-python-created-threads">Non-Python created threads</a></li>
<li><a class="reference internal" href="#cautions-about-fork">Cautions about fork()</a></li>
<li><a class="reference internal" href="#high-level-api">High-level API</a></li>
<li><a class="reference internal" href="#low-level-api">Low-level API</a></li>
</ul>
</li>
<li><a class="reference internal" href="#sub-interpreter-support">Sub-interpreter support</a><ul>
<li><a class="reference internal" href="#a-per-interpreter-gil">A Per-Interpreter GIL</a></li>
<li><a class="reference internal" href="#bugs-and-caveats">Bugs and caveats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#asynchronous-notifications">Asynchronous Notifications</a></li>
<li><a class="reference internal" href="#profiling-and-tracing">Profiling and Tracing</a></li>
<li><a class="reference internal" href="#advanced-debugger-support">Advanced Debugger Support</a></li>
<li><a class="reference internal" href="#thread-local-storage-support">Thread Local Storage Support</a><ul>
<li><a class="reference internal" href="#thread-specific-storage-tss-api">Thread Specific Storage (TSS) API</a><ul>
<li><a class="reference internal" href="#dynamic-allocation">Dynamic Allocation</a></li>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#thread-local-storage-tls-api">Thread Local Storage (TLS) API</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="typehints.html"
                          title="previous chapter">Objects for Type Hinting</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="init_config.html"
                          title="next chapter">Python Initialization Configuration</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/init.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="init_config.html" title="Python Initialization Configuration"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="typehints.html" title="Objects for Type Hinting"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Initialization, Finalization, and Threads</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="initialization-finalization-and-threads">
<span id="initialization"></span><h1>Initialization, Finalization, and Threads<a class="headerlink" href="#initialization-finalization-and-threads" title="Link to this heading">¶</a></h1>
<p>See also <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<section id="before-python-initialization">
<span id="pre-init-safe"></span><h2>Before Python Initialization<a class="headerlink" href="#before-python-initialization" title="Link to this heading">¶</a></h2>
<p>In an application embedding  Python, the <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> function must
be called before using any other Python/C API functions; with the exception of
a few functions and the <a class="reference internal" href="#global-conf-vars"><span class="std std-ref">global configuration variables</span></a>.</p>
<p>The following functions can be safely called before Python is initialized:</p>
<ul class="simple">
<li><p>Configuration functions:</p>
<ul>
<li><p><a class="reference internal" href="import.html#c.PyImport_AppendInittab" title="PyImport_AppendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_AppendInittab()</span></code></a></p></li>
<li><p><a class="reference internal" href="import.html#c.PyImport_ExtendInittab" title="PyImport_ExtendInittab"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyImport_ExtendInittab()</span></code></a></p></li>
<li><p><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInitFrozenExtensions()</span></code></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_SetAllocator" title="PyMem_SetAllocator"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_SetAllocator()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_SetupDebugHooks" title="PyMem_SetupDebugHooks"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_SetupDebugHooks()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_SetArenaAllocator" title="PyObject_SetArenaAllocator"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_SetArenaAllocator()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_SetPath" title="Py_SetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPath()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_SetPythonHome" title="Py_SetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPythonHome()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_SetStandardStreamEncoding" title="Py_SetStandardStreamEncoding"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetStandardStreamEncoding()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_AddWarnOption" title="PySys_AddWarnOption"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddWarnOption()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_AddXOption" title="PySys_AddXOption"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_AddXOption()</span></code></a></p></li>
<li><p><a class="reference internal" href="sys.html#c.PySys_ResetWarnOptions" title="PySys_ResetWarnOptions"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_ResetWarnOptions()</span></code></a></p></li>
</ul>
</li>
<li><p>Informative functions:</p>
<ul>
<li><p><a class="reference internal" href="#c.Py_IsInitialized" title="Py_IsInitialized"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_IsInitialized()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_GetAllocator" title="PyMem_GetAllocator"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_GetAllocator()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyObject_GetArenaAllocator" title="PyObject_GetArenaAllocator"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyObject_GetArenaAllocator()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_GetBuildInfo" title="Py_GetBuildInfo"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetBuildInfo()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_GetCompiler" title="Py_GetCompiler"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetCompiler()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_GetCopyright" title="Py_GetCopyright"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetCopyright()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_GetPlatform" title="Py_GetPlatform"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPlatform()</span></code></a></p></li>
<li><p><a class="reference internal" href="#c.Py_GetVersion" title="Py_GetVersion"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetVersion()</span></code></a></p></li>
</ul>
</li>
<li><p>Utilities:</p>
<ul>
<li><p><a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a></p></li>
</ul>
</li>
<li><p>Memory allocators:</p>
<ul>
<li><p><a class="reference internal" href="memory.html#c.PyMem_RawMalloc" title="PyMem_RawMalloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawMalloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_RawRealloc" title="PyMem_RawRealloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawRealloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_RawCalloc" title="PyMem_RawCalloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawCalloc()</span></code></a></p></li>
<li><p><a class="reference internal" href="memory.html#c.PyMem_RawFree" title="PyMem_RawFree"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_RawFree()</span></code></a></p></li>
</ul>
</li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The following functions <strong>should not be called</strong> before
<a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>: <a class="reference internal" href="sys.html#c.Py_EncodeLocale" title="Py_EncodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_EncodeLocale()</span></code></a>, <a class="reference internal" href="#c.Py_GetPath" title="Py_GetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPath()</span></code></a>,
<a class="reference internal" href="#c.Py_GetPrefix" title="Py_GetPrefix"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPrefix()</span></code></a>, <a class="reference internal" href="#c.Py_GetExecPrefix" title="Py_GetExecPrefix"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetExecPrefix()</span></code></a>,
<a class="reference internal" href="#c.Py_GetProgramFullPath" title="Py_GetProgramFullPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetProgramFullPath()</span></code></a>, <a class="reference internal" href="#c.Py_GetPythonHome" title="Py_GetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPythonHome()</span></code></a>,
<a class="reference internal" href="#c.Py_GetProgramName" title="Py_GetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetProgramName()</span></code></a> and <a class="reference internal" href="#c.PyEval_InitThreads" title="PyEval_InitThreads"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_InitThreads()</span></code></a>.</p>
</div>
</section>
<section id="global-configuration-variables">
<span id="global-conf-vars"></span><h2>Global configuration variables<a class="headerlink" href="#global-configuration-variables" title="Link to this heading">¶</a></h2>
<p>Python has variables for the global configuration to control different features
and options. By default, these flags are controlled by <a class="reference internal" href="../using/cmdline.html#using-on-interface-options"><span class="std std-ref">command line
options</span></a>.</p>
<p>When a flag is set by an option, the value of the flag is the number of times
that the option was set. For example, <code class="docutils literal notranslate"><span class="pre">-b</span></code> sets <a class="reference internal" href="#c.Py_BytesWarningFlag" title="Py_BytesWarningFlag"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_BytesWarningFlag</span></code></a>
to 1 and <code class="docutils literal notranslate"><span class="pre">-bb</span></code> sets <a class="reference internal" href="#c.Py_BytesWarningFlag" title="Py_BytesWarningFlag"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_BytesWarningFlag</span></code></a> to 2.</p>
<dl class="c var">
<dt class="sig sig-object c" id="c.Py_BytesWarningFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_BytesWarningFlag</span></span></span><a class="headerlink" href="#c.Py_BytesWarningFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.bytes_warning" title="PyConfig.bytes_warning"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.bytes_warning</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Issue a warning when comparing <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> or <a class="reference internal" href="../library/stdtypes.html#bytearray" title="bytearray"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytearray</span></code></a> with
<a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> or <a class="reference internal" href="../library/stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> with <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.  Issue an error if greater
or equal to <code class="docutils literal notranslate"><span class="pre">2</span></code>.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-b"><code class="xref std std-option docutils literal notranslate"><span class="pre">-b</span></code></a> option.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_DebugFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_DebugFlag</span></span></span><a class="headerlink" href="#c.Py_DebugFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.parser_debug" title="PyConfig.parser_debug"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parser_debug</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Turn on parser debugging output (for expert only, depending on compilation
options).</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-d"><code class="xref std std-option docutils literal notranslate"><span class="pre">-d</span></code></a> option and the <span class="target" id="index-0"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDEBUG"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDEBUG</span></code></a> environment
variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_DontWriteBytecodeFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_DontWriteBytecodeFlag</span></span></span><a class="headerlink" href="#c.Py_DontWriteBytecodeFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.write_bytecode" title="PyConfig.write_bytecode"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.write_bytecode</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>If set to non-zero, Python won’t try to write <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files on the
import of source modules.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-B"><code class="xref std std-option docutils literal notranslate"><span class="pre">-B</span></code></a> option and the <span class="target" id="index-1"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONDONTWRITEBYTECODE</span></code></a>
environment variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_FrozenFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_FrozenFlag</span></span></span><a class="headerlink" href="#c.Py_FrozenFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.pathconfig_warnings" title="PyConfig.pathconfig_warnings"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.pathconfig_warnings</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Suppress error messages when calculating the module search path in
<a class="reference internal" href="#c.Py_GetPath" title="Py_GetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPath()</span></code></a>.</p>
<p>Private flag used by <code class="docutils literal notranslate"><span class="pre">_freeze_module</span></code> and <code class="docutils literal notranslate"><span class="pre">frozenmain</span></code> programs.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_HashRandomizationFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_HashRandomizationFlag</span></span></span><a class="headerlink" href="#c.Py_HashRandomizationFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.hash_seed" title="PyConfig.hash_seed"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.hash_seed</span></code></a> and <a class="reference internal" href="init_config.html#c.PyConfig.use_hash_seed" title="PyConfig.use_hash_seed"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.use_hash_seed</span></code></a> should
be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the <span class="target" id="index-2"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHASHSEED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHASHSEED</span></code></a> environment variable is set to
a non-empty string.</p>
<p>If the flag is non-zero, read the <span class="target" id="index-3"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHASHSEED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHASHSEED</span></code></a> environment
variable to initialize the secret hash seed.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_IgnoreEnvironmentFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IgnoreEnvironmentFlag</span></span></span><a class="headerlink" href="#c.Py_IgnoreEnvironmentFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.use_environment" title="PyConfig.use_environment"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.use_environment</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Ignore all <code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHON*</span></code> environment variables, e.g.
<span class="target" id="index-4"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONPATH"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONPATH</span></code></a> and <span class="target" id="index-5"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a>, that might be set.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">-E</span></code></a> and <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> options.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_InspectFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_InspectFlag</span></span></span><a class="headerlink" href="#c.Py_InspectFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.inspect" title="PyConfig.inspect"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.inspect</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>When a script is passed as first argument or the <a class="reference internal" href="../using/cmdline.html#cmdoption-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a> option is used,
enter interactive mode after executing the script or the command, even when
<a class="reference internal" href="../library/sys.html#sys.stdin" title="sys.stdin"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stdin</span></code></a> does not appear to be a terminal.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a> option and the <span class="target" id="index-6"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONINSPECT"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONINSPECT</span></code></a> environment
variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_InteractiveFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_InteractiveFlag</span></span></span><a class="headerlink" href="#c.Py_InteractiveFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.interactive" title="PyConfig.interactive"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.interactive</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-i"><code class="xref std std-option docutils literal notranslate"><span class="pre">-i</span></code></a> option.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_IsolatedFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IsolatedFlag</span></span></span><a class="headerlink" href="#c.Py_IsolatedFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.isolated" title="PyConfig.isolated"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.isolated</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Run Python in isolated mode. In isolated mode <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> contains
neither the script’s directory nor the user’s site-packages directory.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> option.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_LegacyWindowsFSEncodingFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_LegacyWindowsFSEncodingFlag</span></span></span><a class="headerlink" href="#c.Py_LegacyWindowsFSEncodingFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyPreConfig.legacy_windows_fs_encoding" title="PyPreConfig.legacy_windows_fs_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyPreConfig.legacy_windows_fs_encoding</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>If the flag is non-zero, use the <code class="docutils literal notranslate"><span class="pre">mbcs</span></code> encoding with <code class="docutils literal notranslate"><span class="pre">replace</span></code> error
handler, instead of the UTF-8 encoding with <code class="docutils literal notranslate"><span class="pre">surrogatepass</span></code> error handler,
for the <a class="reference internal" href="../glossary.html#term-filesystem-encoding-and-error-handler"><span class="xref std std-term">filesystem encoding and error handler</span></a>.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the <span class="target" id="index-7"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSFSENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSFSENCODING</span></code></a> environment
variable is set to a non-empty string.</p>
<p>See <span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0529/"><strong>PEP 529</strong></a> for more details.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="../library/intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_LegacyWindowsStdioFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_LegacyWindowsStdioFlag</span></span></span><a class="headerlink" href="#c.Py_LegacyWindowsStdioFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.legacy_windows_stdio" title="PyConfig.legacy_windows_stdio"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.legacy_windows_stdio</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>If the flag is non-zero, use <a class="reference internal" href="../library/io.html#io.FileIO" title="io.FileIO"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.FileIO</span></code></a> instead of
<code class="xref py py-class docutils literal notranslate"><span class="pre">io._WindowsConsoleIO</span></code> for <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a> standard streams.</p>
<p>Set to <code class="docutils literal notranslate"><span class="pre">1</span></code> if the <span class="target" id="index-9"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONLEGACYWINDOWSSTDIO"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONLEGACYWINDOWSSTDIO</span></code></a> environment
variable is set to a non-empty string.</p>
<p>See <span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0528/"><strong>PEP 528</strong></a> for more details.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="../library/intro.html#availability"><span class="std std-ref">Availability</span></a>: Windows.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_NoSiteFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_NoSiteFlag</span></span></span><a class="headerlink" href="#c.Py_NoSiteFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.site_import" title="PyConfig.site_import"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.site_import</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Disable the import of the module <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> and the site-dependent
manipulations of <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> that it entails.  Also disable these
manipulations if <a class="reference internal" href="../library/site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> is explicitly imported later (call
<a class="reference internal" href="../library/site.html#site.main" title="site.main"><code class="xref py py-func docutils literal notranslate"><span class="pre">site.main()</span></code></a> if you want them to be triggered).</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-S"><code class="xref std std-option docutils literal notranslate"><span class="pre">-S</span></code></a> option.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_NoUserSiteDirectory">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_NoUserSiteDirectory</span></span></span><a class="headerlink" href="#c.Py_NoUserSiteDirectory" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.user_site_directory" title="PyConfig.user_site_directory"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.user_site_directory</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Don’t add the <a class="reference internal" href="../library/site.html#site.USER_SITE" title="site.USER_SITE"><code class="xref py py-data docutils literal notranslate"><span class="pre">user</span> <span class="pre">site-packages</span> <span class="pre">directory</span></code></a> to
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-s"><code class="xref std std-option docutils literal notranslate"><span class="pre">-s</span></code></a> and <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a> options, and the
<span class="target" id="index-11"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONNOUSERSITE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONNOUSERSITE</span></code></a> environment variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_OptimizeFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_OptimizeFlag</span></span></span><a class="headerlink" href="#c.Py_OptimizeFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.optimization_level" title="PyConfig.optimization_level"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.optimization_level</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-O"><code class="xref std std-option docutils literal notranslate"><span class="pre">-O</span></code></a> option and the <span class="target" id="index-12"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONOPTIMIZE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONOPTIMIZE</span></code></a> environment
variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_QuietFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_QuietFlag</span></span></span><a class="headerlink" href="#c.Py_QuietFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.quiet" title="PyConfig.quiet"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.quiet</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Don’t display the copyright and version messages even in interactive mode.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-q"><code class="xref std std-option docutils literal notranslate"><span class="pre">-q</span></code></a> option.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_UnbufferedStdioFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_UnbufferedStdioFlag</span></span></span><a class="headerlink" href="#c.Py_UnbufferedStdioFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.buffered_stdio" title="PyConfig.buffered_stdio"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.buffered_stdio</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Force the stdout and stderr streams to be unbuffered.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-u"><code class="xref std std-option docutils literal notranslate"><span class="pre">-u</span></code></a> option and the <span class="target" id="index-13"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONUNBUFFERED"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONUNBUFFERED</span></code></a>
environment variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.Py_VerboseFlag">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_VerboseFlag</span></span></span><a class="headerlink" href="#c.Py_VerboseFlag" title="Link to this definition">¶</a><br /></dt>
<dd><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.verbose" title="PyConfig.verbose"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.verbose</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Print a message each time a module is initialized, showing the place
(filename or built-in module) from which it is loaded.  If greater or equal
to <code class="docutils literal notranslate"><span class="pre">2</span></code>, print a message for each file that is checked for when
searching for a module. Also provides information on module cleanup at exit.</p>
<p>Set by the <a class="reference internal" href="../using/cmdline.html#cmdoption-v"><code class="xref std std-option docutils literal notranslate"><span class="pre">-v</span></code></a> option and the <span class="target" id="index-14"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONVERBOSE"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONVERBOSE</span></code></a> environment
variable.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.12.</span></p>
</div>
</dd></dl>

</section>
<section id="initializing-and-finalizing-the-interpreter">
<h2>Initializing and finalizing the interpreter<a class="headerlink" href="#initializing-and-finalizing-the-interpreter" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_Initialize">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_Initialize</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_Initialize" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-15">Initialize the Python interpreter.  In an application embedding  Python,
this should be called before using any other Python/C API functions; see
<a class="reference internal" href="#pre-init-safe"><span class="std std-ref">Before Python Initialization</span></a> for the few exceptions.</p>
<p>This initializes
the table of loaded modules (<code class="docutils literal notranslate"><span class="pre">sys.modules</span></code>), and creates the fundamental
modules <a class="reference internal" href="../library/builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a>, <a class="reference internal" href="../library/__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> and <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a>.  It also initializes
the module search path (<code class="docutils literal notranslate"><span class="pre">sys.path</span></code>). It does not set <code class="docutils literal notranslate"><span class="pre">sys.argv</span></code>; use
<a class="reference internal" href="#c.PySys_SetArgvEx" title="PySys_SetArgvEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetArgvEx()</span></code></a> for that.  This is a no-op when called for a second time
(without calling <a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> first).  There is no return value; it is a
fatal error if the initialization fails.</p>
<p>Use the <a class="reference internal" href="init_config.html#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> function to customize the
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>On Windows, changes the console mode from <code class="docutils literal notranslate"><span class="pre">O_TEXT</span></code> to <code class="docutils literal notranslate"><span class="pre">O_BINARY</span></code>, which will
also affect non-Python uses of the console using the C Runtime.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_InitializeEx">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_InitializeEx</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">initsigs</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_InitializeEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This function works like <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> if <em>initsigs</em> is <code class="docutils literal notranslate"><span class="pre">1</span></code>. If
<em>initsigs</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code>, it skips initialization registration of signal handlers, which
might be useful when Python is embedded.</p>
<p>Use the <a class="reference internal" href="init_config.html#c.Py_InitializeFromConfig" title="Py_InitializeFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_InitializeFromConfig()</span></code></a> function to customize the
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_IsInitialized">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_IsInitialized</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_IsInitialized" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return true (nonzero) when the Python interpreter has been initialized, false
(zero) if not.  After <a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> is called, this returns false until
<a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> is called again.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_FinalizeEx">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_FinalizeEx</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_FinalizeEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.6.</em><p>Undo all initializations made by <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> and subsequent use of
Python/C API functions, and destroy all sub-interpreters (see
<a class="reference internal" href="#c.Py_NewInterpreter" title="Py_NewInterpreter"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreter()</span></code></a> below) that were created and not yet destroyed since
the last call to <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.  Ideally, this frees all memory
allocated by the Python interpreter.  This is a no-op when called for a second
time (without calling <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> again first).  Normally the
return value is <code class="docutils literal notranslate"><span class="pre">0</span></code>.  If there were errors during finalization
(flushing buffered data), <code class="docutils literal notranslate"><span class="pre">-1</span></code> is returned.</p>
<p>This function is provided for a number of reasons.  An embedding application
might want to restart Python without having to restart the application itself.
An application that has loaded the Python interpreter from a dynamically
loadable library (or DLL) might want to free all memory allocated by Python
before unloading the DLL. During a hunt for memory leaks in an application a
developer might want to free all memory allocated by Python before exiting from
the application.</p>
<p><strong>Bugs and caveats:</strong> The destruction of modules and objects in modules is done
in random order; this may cause destructors (<a class="reference internal" href="../reference/datamodel.html#object.__del__" title="object.__del__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__del__()</span></code></a> methods) to fail
when they depend on other objects (even functions) or modules.  Dynamically
loaded extension modules loaded by Python are not unloaded.  Small amounts of
memory allocated by the Python interpreter may not be freed (if you find a leak,
please report it).  Memory tied up in circular references between objects is not
freed.  Some memory allocated by extension modules may not be freed.  Some
extensions may not work properly if their initialization routine is called more
than once; this can happen if an application calls <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> and
<a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> more than once.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="../library/sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">cpython._PySys_ClearAuditHooks</span></code> with no arguments.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_Finalize">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_Finalize</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_Finalize" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This is a backwards-compatible version of <a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> that
disregards the return value.</p>
</dd></dl>

</section>
<section id="process-wide-parameters">
<h2>Process-wide parameters<a class="headerlink" href="#process-wide-parameters" title="Link to this heading">¶</a></h2>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SetStandardStreamEncoding">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SetStandardStreamEncoding</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">encoding</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">errors</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SetStandardStreamEncoding" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-16">This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.stdio_encoding" title="PyConfig.stdio_encoding"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.stdio_encoding</span></code></a> and <a class="reference internal" href="init_config.html#c.PyConfig.stdio_errors" title="PyConfig.stdio_errors"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.stdio_errors</span></code></a>
should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>This function should be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, if it is
called at all. It specifies which encoding and error handling to use
with standard IO, with the same meanings as in <a class="reference internal" href="../library/stdtypes.html#str.encode" title="str.encode"><code class="xref py py-func docutils literal notranslate"><span class="pre">str.encode()</span></code></a>.</p>
<p>It overrides <span class="target" id="index-17"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONIOENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONIOENCODING</span></code></a> values, and allows embedding code
to control IO encoding when the environment variable does not work.</p>
<p><em>encoding</em> and/or <em>errors</em> may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code> to use
<span class="target" id="index-18"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONIOENCODING"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONIOENCODING</span></code></a> and/or default values (depending on other
settings).</p>
<p>Note that <a class="reference internal" href="../library/sys.html#sys.stderr" title="sys.stderr"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.stderr</span></code></a> always uses the “backslashreplace” error
handler, regardless of this (or any other) setting.</p>
<p>If <a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> is called, this function will need to be called
again in order to affect subsequent calls to <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
<p>Returns <code class="docutils literal notranslate"><span class="pre">0</span></code> if successful, a nonzero value on error (e.g. calling after the
interpreter has already been initialized).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SetProgramName">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SetProgramName</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">name</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SetProgramName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-19">This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.program_name" title="PyConfig.program_name"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.program_name</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>This function should be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> is called for
the first time, if it is called at all.  It tells the interpreter the value
of the <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> argument to the <code class="xref c c-func docutils literal notranslate"><span class="pre">main()</span></code> function of the program
(converted to wide characters).
This is used by <a class="reference internal" href="#c.Py_GetPath" title="Py_GetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPath()</span></code></a> and some other functions below to find
the Python run-time libraries relative to the interpreter executable.  The
default value is <code class="docutils literal notranslate"><span class="pre">'python'</span></code>.  The argument should point to a
zero-terminated wide character string in static storage whose contents will not
change for the duration of the program’s execution.  No code in the Python
interpreter will change the contents of this storage.</p>
<p>Use <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> to decode a bytes string to get a
<span class="c-expr sig sig-inline c"><span class="n">wchar_t</span><span class="p">*</span></span> string.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetProgramName">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetProgramName</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetProgramName" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-20">Return the program name set with <a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a>, or the default.
The returned string points into static storage; the caller should not modify its
value.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetPrefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetPrefix</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetPrefix" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the <em>prefix</em> for installed platform-independent files. This is derived
through a number of complicated rules from the program name set with
<a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a> and some environment variables; for example, if the
program name is <code class="docutils literal notranslate"><span class="pre">'/usr/local/bin/python'</span></code>, the prefix is <code class="docutils literal notranslate"><span class="pre">'/usr/local'</span></code>. The
returned string points into static storage; the caller should not modify its
value.  This corresponds to the <strong class="makevar">prefix</strong> variable in the top-level
<code class="file docutils literal notranslate"><span class="pre">Makefile</span></code> and the <a class="reference internal" href="../using/configure.html#cmdoption-prefix"><code class="xref std std-option docutils literal notranslate"><span class="pre">--prefix</span></code></a> argument to the <strong class="program">configure</strong>
script at build time.  The value is available to Python code as <code class="docutils literal notranslate"><span class="pre">sys.prefix</span></code>.
It is only useful on Unix.  See also the next function.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetExecPrefix">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetExecPrefix</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetExecPrefix" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the <em>exec-prefix</em> for installed platform-<em>dependent</em> files.  This is
derived through a number of complicated rules from the program name set with
<a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a> and some environment variables; for example, if the
program name is <code class="docutils literal notranslate"><span class="pre">'/usr/local/bin/python'</span></code>, the exec-prefix is
<code class="docutils literal notranslate"><span class="pre">'/usr/local'</span></code>.  The returned string points into static storage; the caller
should not modify its value.  This corresponds to the <strong class="makevar">exec_prefix</strong>
variable in the top-level <code class="file docutils literal notranslate"><span class="pre">Makefile</span></code> and the <code class="docutils literal notranslate"><span class="pre">--exec-prefix</span></code>
argument to the <strong class="program">configure</strong> script at build  time.  The value is
available to Python code as <code class="docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code>.  It is only useful on Unix.</p>
<p>Background: The exec-prefix differs from the prefix when platform dependent
files (such as executables and shared libraries) are installed in a different
directory tree.  In a typical installation, platform dependent files may be
installed in the <code class="file docutils literal notranslate"><span class="pre">/usr/local/plat</span></code> subtree while platform independent may
be installed in <code class="file docutils literal notranslate"><span class="pre">/usr/local</span></code>.</p>
<p>Generally speaking, a platform is a combination of hardware and software
families, e.g.  Sparc machines running the Solaris 2.x operating system are
considered the same platform, but Intel machines running Solaris 2.x are another
platform, and Intel machines running Linux are yet another platform.  Different
major revisions of the same operating system generally also form different
platforms.  Non-Unix operating systems are a different story; the installation
strategies on those systems are so different that the prefix and exec-prefix are
meaningless, and set to the empty string. Note that compiled Python bytecode
files are platform independent (but not independent from the Python version by
which they were compiled!).</p>
<p>System administrators will know how to configure the <strong class="program">mount</strong> or
<strong class="program">automount</strong> programs to share <code class="file docutils literal notranslate"><span class="pre">/usr/local</span></code> between platforms
while having <code class="file docutils literal notranslate"><span class="pre">/usr/local/plat</span></code> be a different filesystem for each
platform.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetProgramFullPath">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetProgramFullPath</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetProgramFullPath" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-21">Return the full program name of the Python executable; this is  computed as a
side-effect of deriving the default module search path  from the program name
(set by <a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a> above). The returned string points into
static storage; the caller should not modify its value.  The value is available
to Python code as <code class="docutils literal notranslate"><span class="pre">sys.executable</span></code>.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetPath">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetPath</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetPath" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-22">Return the default module search path; this is computed from the program name
(set by <a class="reference internal" href="#c.Py_SetProgramName" title="Py_SetProgramName"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetProgramName()</span></code></a> above) and some environment variables.
The returned string consists of a series of directory names separated by a
platform dependent delimiter character.  The delimiter character is <code class="docutils literal notranslate"><span class="pre">':'</span></code>
on Unix and macOS, <code class="docutils literal notranslate"><span class="pre">';'</span></code> on Windows.  The returned string points into
static storage; the caller should not modify its value.  The list
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> is initialized with this value on interpreter startup; it
can be (and usually is) modified later to change the search path for loading
modules.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SetPath">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SetPath</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="p"><span class="pre">*</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SetPath" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p id="index-23">This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.module_search_paths" title="PyConfig.module_search_paths"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.module_search_paths</span></code></a> and
<a class="reference internal" href="init_config.html#c.PyConfig.module_search_paths_set" title="PyConfig.module_search_paths_set"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.module_search_paths_set</span></code></a> should be used instead, see
<a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>Set the default module search path.  If this function is called before
<a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, then <a class="reference internal" href="#c.Py_GetPath" title="Py_GetPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetPath()</span></code></a> won’t attempt to compute a
default search path but uses the one provided instead.  This is useful if
Python is embedded by an application that has full knowledge of the location
of all modules.  The path components should be separated by the platform
dependent delimiter character, which is <code class="docutils literal notranslate"><span class="pre">':'</span></code> on Unix and macOS, <code class="docutils literal notranslate"><span class="pre">';'</span></code>
on Windows.</p>
<p>This also causes <a class="reference internal" href="../library/sys.html#sys.executable" title="sys.executable"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.executable</span></code></a> to be set to the program
full path (see <a class="reference internal" href="#c.Py_GetProgramFullPath" title="Py_GetProgramFullPath"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_GetProgramFullPath()</span></code></a>) and for <a class="reference internal" href="../library/sys.html#sys.prefix" title="sys.prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.prefix</span></code></a> and
<a class="reference internal" href="../library/sys.html#sys.exec_prefix" title="sys.exec_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.exec_prefix</span></code></a> to be empty.  It is up to the caller to modify these
if required after calling <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
<p>Use <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> to decode a bytes string to get a
<span class="c-expr sig sig-inline c"><span class="n">wchar_</span><span class="p">*</span></span> string.</p>
<p>The path argument is copied internally, so the caller may free it after the
call completes.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The program full path is now used for <a class="reference internal" href="../library/sys.html#sys.executable" title="sys.executable"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.executable</span></code></a>, instead
of the program name.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetVersion">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetVersion</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetVersion" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the version of this Python interpreter.  This is a string that looks
something like</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="s">&quot;3.0a5+ (py3k:63103M, May 12 2008, 00:53:55) </span><span class="se">\n</span><span class="s">[GCC 4.2.3]&quot;</span>
</pre></div>
</div>
<p id="index-24">The first word (up to the first space character) is the current Python version;
the first characters are the major and minor version separated by a
period.  The returned string points into static storage; the caller should not
modify its value.  The value is available to Python code as <a class="reference internal" href="../library/sys.html#sys.version" title="sys.version"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version</span></code></a>.</p>
<p>See also the <a class="reference internal" href="apiabiversion.html#c.Py_Version" title="Py_Version"><code class="xref c c-var docutils literal notranslate"><span class="pre">Py_Version</span></code></a> constant.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetPlatform">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetPlatform</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetPlatform" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-25">Return the platform identifier for the current platform.  On Unix, this is
formed from the “official” name of the operating system, converted to lower
case, followed by the major revision number; e.g., for Solaris 2.x, which is
also known as SunOS 5.x, the value is <code class="docutils literal notranslate"><span class="pre">'sunos5'</span></code>.  On macOS, it is
<code class="docutils literal notranslate"><span class="pre">'darwin'</span></code>.  On Windows, it is <code class="docutils literal notranslate"><span class="pre">'win'</span></code>.  The returned string points into
static storage; the caller should not modify its value.  The value is available
to Python code as <code class="docutils literal notranslate"><span class="pre">sys.platform</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetCopyright">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetCopyright</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetCopyright" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the official copyright string for the current Python version, for example</p>
<p><code class="docutils literal notranslate"><span class="pre">'Copyright</span> <span class="pre">1991-1995</span> <span class="pre">Stichting</span> <span class="pre">Mathematisch</span> <span class="pre">Centrum,</span> <span class="pre">Amsterdam'</span></code></p>
<p id="index-26">The returned string points into static storage; the caller should not modify its
value.  The value is available to Python code as <code class="docutils literal notranslate"><span class="pre">sys.copyright</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetCompiler">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetCompiler</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetCompiler" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return an indication of the compiler used to build the current Python version,
in square brackets, for example:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="s">&quot;[GCC 2.7.2.2]&quot;</span>
</pre></div>
</div>
<p id="index-27">The returned string points into static storage; the caller should not modify its
value.  The value is available to Python code as part of the variable
<code class="docutils literal notranslate"><span class="pre">sys.version</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetBuildInfo">
<span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetBuildInfo</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetBuildInfo" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return information about the sequence number and build date and time  of the
current Python interpreter instance, for example</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="s">&quot;#67, Aug  1 1997, 22:34:28&quot;</span>
</pre></div>
</div>
<p id="index-28">The returned string points into static storage; the caller should not modify its
value.  The value is available to Python code as part of the variable
<code class="docutils literal notranslate"><span class="pre">sys.version</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySys_SetArgvEx">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySys_SetArgvEx</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">updatepath</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySys_SetArgvEx" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-29">This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a>, <a class="reference internal" href="init_config.html#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a> and
<a class="reference internal" href="init_config.html#c.PyConfig.safe_path" title="PyConfig.safe_path"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.safe_path</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Set <a class="reference internal" href="../library/sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a> based on <em>argc</em> and <em>argv</em>.  These parameters are
similar to those passed to the program’s <code class="xref c c-func docutils literal notranslate"><span class="pre">main()</span></code> function with the
difference that the first entry should refer to the script file to be
executed rather than the executable hosting the Python interpreter.  If there
isn’t a script that will be run, the first entry in <em>argv</em> can be an empty
string.  If this function fails to initialize <a class="reference internal" href="../library/sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a>, a fatal
condition is signalled using <a class="reference internal" href="sys.html#c.Py_FatalError" title="Py_FatalError"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FatalError()</span></code></a>.</p>
<p>If <em>updatepath</em> is zero, this is all the function does.  If <em>updatepath</em>
is non-zero, the function also modifies <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> according to the
following algorithm:</p>
<ul class="simple">
<li><p>If the name of an existing script is passed in <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code>, the absolute
path of the directory where the script is located is prepended to
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p></li>
<li><p>Otherwise (that is, if <em>argc</em> is <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">argv[0]</span></code> doesn’t point
to an existing file name), an empty string is prepended to
<a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>, which is the same as prepending the current working
directory (<code class="docutils literal notranslate"><span class="pre">&quot;.&quot;</span></code>).</p></li>
</ul>
<p>Use <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> to decode a bytes string to get a
<span class="c-expr sig sig-inline c"><span class="n">wchar_</span><span class="p">*</span></span> string.</p>
<p>See also <a class="reference internal" href="init_config.html#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.orig_argv</span></code></a> and <a class="reference internal" href="init_config.html#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a>
members of the <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It is recommended that applications embedding the Python interpreter
for purposes other than executing a single script pass <code class="docutils literal notranslate"><span class="pre">0</span></code> as <em>updatepath</em>,
and update <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> themselves if desired.
See <a class="reference external" href="https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2008-5983">CVE-2008-5983</a>.</p>
<p>On versions before 3.1.3, you can achieve the same effect by manually
popping the first <a class="reference internal" href="../library/sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> element after having called
<a class="reference internal" href="#c.PySys_SetArgv" title="PySys_SetArgv"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetArgv()</span></code></a>, for example using:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyRun_SimpleString</span><span class="p">(</span><span class="s">&quot;import sys; sys.path.pop(0)</span><span class="se">\n</span><span class="s">&quot;</span><span class="p">);</span>
</pre></div>
</div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.3.</span></p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PySys_SetArgv">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PySys_SetArgv</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">argc</span></span>, <span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">argv</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PySys_SetArgv" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a> and <a class="reference internal" href="init_config.html#c.PyConfig.parse_argv" title="PyConfig.parse_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.parse_argv</span></code></a> should be used
instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<p>This function works like <a class="reference internal" href="#c.PySys_SetArgvEx" title="PySys_SetArgvEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">PySys_SetArgvEx()</span></code></a> with <em>updatepath</em> set
to <code class="docutils literal notranslate"><span class="pre">1</span></code> unless the <strong class="program">python</strong> interpreter was started with the
<a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a>.</p>
<p>Use <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> to decode a bytes string to get a
<span class="c-expr sig sig-inline c"><span class="n">wchar_</span><span class="p">*</span></span> string.</p>
<p>See also <a class="reference internal" href="init_config.html#c.PyConfig.orig_argv" title="PyConfig.orig_argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.orig_argv</span></code></a> and <a class="reference internal" href="init_config.html#c.PyConfig.argv" title="PyConfig.argv"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.argv</span></code></a>
members of the <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python Initialization Configuration</span></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The <em>updatepath</em> value depends on <a class="reference internal" href="../using/cmdline.html#cmdoption-I"><code class="xref std std-option docutils literal notranslate"><span class="pre">-I</span></code></a>.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_SetPythonHome">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_SetPythonHome</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">home</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_SetPythonHome" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This API is kept for backward compatibility: setting
<a class="reference internal" href="init_config.html#c.PyConfig.home" title="PyConfig.home"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyConfig.home</span></code></a> should be used instead, see <a class="reference internal" href="init_config.html#init-config"><span class="std std-ref">Python
Initialization Configuration</span></a>.</p>
<p>Set the default “home” directory, that is, the location of the standard
Python libraries.  See <span class="target" id="index-30"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a> for the meaning of the
argument string.</p>
<p>The argument should point to a zero-terminated character string in static
storage whose contents will not change for the duration of the program’s
execution.  No code in the Python interpreter will change the contents of
this storage.</p>
<p>Use <a class="reference internal" href="sys.html#c.Py_DecodeLocale" title="Py_DecodeLocale"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_DecodeLocale()</span></code></a> to decode a bytes string to get a
<span class="c-expr sig sig-inline c"><span class="n">wchar_</span><span class="p">*</span></span> string.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_GetPythonHome">
<span class="n"><span class="pre">wchar_t</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_GetPythonHome</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_GetPythonHome" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the default “home”, that is, the value set by a previous call to
<a class="reference internal" href="#c.Py_SetPythonHome" title="Py_SetPythonHome"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_SetPythonHome()</span></code></a>, or the value of the <span class="target" id="index-31"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONHOME"><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PYTHONHOME</span></code></a>
environment variable if it is set.</p>
<p>This function should not be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, otherwise
it returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>It now returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
</dd></dl>

</section>
<section id="thread-state-and-the-global-interpreter-lock">
<span id="threads"></span><h2>Thread State and the Global Interpreter Lock<a class="headerlink" href="#thread-state-and-the-global-interpreter-lock" title="Link to this heading">¶</a></h2>
<p id="index-32">The Python interpreter is not fully thread-safe.  In order to support
multi-threaded Python programs, there’s a global lock, called the <a class="reference internal" href="../glossary.html#term-global-interpreter-lock"><span class="xref std std-term">global
interpreter lock</span></a> or <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>, that must be held by the current thread before
it can safely access Python objects. Without the lock, even the simplest
operations could cause problems in a multi-threaded program: for example, when
two threads simultaneously increment the reference count of the same object, the
reference count could end up being incremented only once instead of twice.</p>
<p id="index-33">Therefore, the rule exists that only the thread that has acquired the
<a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a> may operate on Python objects or call Python/C API functions.
In order to emulate concurrency of execution, the interpreter regularly
tries to switch threads (see <a class="reference internal" href="../library/sys.html#sys.setswitchinterval" title="sys.setswitchinterval"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.setswitchinterval()</span></code></a>).  The lock is also
released around potentially blocking I/O operations like reading or writing
a file, so that other Python threads can run in the meantime.</p>
<p id="index-34">The Python interpreter keeps some thread-specific bookkeeping information
inside a data structure called <a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyThreadState</span></code></a>.  There’s also one
global variable pointing to the current <a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyThreadState</span></code></a>: it can
be retrieved using <a class="reference internal" href="#c.PyThreadState_Get" title="PyThreadState_Get"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Get()</span></code></a>.</p>
<section id="releasing-the-gil-from-extension-code">
<h3>Releasing the GIL from extension code<a class="headerlink" href="#releasing-the-gil-from-extension-code" title="Link to this heading">¶</a></h3>
<p>Most extension code manipulating the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a> has the following simple
structure:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Save</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="kr">thread</span><span class="w"> </span><span class="n">state</span><span class="w"> </span><span class="n">in</span><span class="w"> </span><span class="n">a</span><span class="w"> </span><span class="n">local</span><span class="w"> </span><span class="n">variable</span><span class="p">.</span>
<span class="n">Release</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">global</span><span class="w"> </span><span class="n">interpreter</span><span class="w"> </span><span class="n">lock</span><span class="p">.</span>
<span class="p">...</span><span class="w"> </span><span class="n">Do</span><span class="w"> </span><span class="n">some</span><span class="w"> </span><span class="n">blocking</span><span class="w"> </span><span class="n">I</span><span class="o">/</span><span class="n">O</span><span class="w"> </span><span class="n">operation</span><span class="w"> </span><span class="p">...</span>
<span class="n">Reacquire</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">global</span><span class="w"> </span><span class="n">interpreter</span><span class="w"> </span><span class="n">lock</span><span class="p">.</span>
<span class="n">Restore</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="kr">thread</span><span class="w"> </span><span class="n">state</span><span class="w"> </span><span class="n">from</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">local</span><span class="w"> </span><span class="n">variable</span><span class="p">.</span>
</pre></div>
</div>
<p>This is so common that a pair of macros exists to simplify it:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">Py_BEGIN_ALLOW_THREADS</span>
<span class="p">...</span><span class="w"> </span><span class="n">Do</span><span class="w"> </span><span class="n">some</span><span class="w"> </span><span class="n">blocking</span><span class="w"> </span><span class="n">I</span><span class="o">/</span><span class="n">O</span><span class="w"> </span><span class="n">operation</span><span class="w"> </span><span class="p">...</span>
<span class="n">Py_END_ALLOW_THREADS</span>
</pre></div>
</div>
<p id="index-35">The <a class="reference internal" href="#c.Py_BEGIN_ALLOW_THREADS" title="Py_BEGIN_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></code></a> macro opens a new block and declares a
hidden local variable; the <a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS</span></code></a> macro closes the
block.</p>
<p>The block above expands to the following code:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyThreadState</span><span class="w"> </span><span class="o">*</span><span class="n">_save</span><span class="p">;</span>

<span class="n">_save</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyEval_SaveThread</span><span class="p">();</span>
<span class="p">...</span><span class="w"> </span><span class="n">Do</span><span class="w"> </span><span class="n">some</span><span class="w"> </span><span class="n">blocking</span><span class="w"> </span><span class="n">I</span><span class="o">/</span><span class="n">O</span><span class="w"> </span><span class="n">operation</span><span class="w"> </span><span class="p">...</span>
<span class="n">PyEval_RestoreThread</span><span class="p">(</span><span class="n">_save</span><span class="p">);</span>
</pre></div>
</div>
<p id="index-36">Here is how these functions work: the global interpreter lock is used to protect the pointer to the
current thread state.  When releasing the lock and saving the thread state,
the current thread state pointer must be retrieved before the lock is released
(since another thread could immediately acquire the lock and store its own thread
state in the global variable). Conversely, when acquiring the lock and restoring
the thread state, the lock must be acquired before storing the thread state
pointer.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling system I/O functions is the most common use case for releasing
the GIL, but it can also be useful before calling long-running computations
which don’t need access to Python objects, such as compression or
cryptographic functions operating over memory buffers.  For example, the
standard <a class="reference internal" href="../library/zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> and <a class="reference internal" href="../library/hashlib.html#module-hashlib" title="hashlib: Secure hash and message digest algorithms."><code class="xref py py-mod docutils literal notranslate"><span class="pre">hashlib</span></code></a> modules release the GIL when
compressing or hashing data.</p>
</div>
</section>
<section id="non-python-created-threads">
<span id="gilstate"></span><h3>Non-Python created threads<a class="headerlink" href="#non-python-created-threads" title="Link to this heading">¶</a></h3>
<p>When threads are created using the dedicated Python APIs (such as the
<a class="reference internal" href="../library/threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module), a thread state is automatically associated to them
and the code showed above is therefore correct.  However, when threads are
created from C (for example by a third-party library with its own thread
management), they don’t hold the GIL, nor is there a thread state structure
for them.</p>
<p>If you need to call Python code from these threads (often this will be part
of a callback API provided by the aforementioned third-party library),
you must first register these threads with the interpreter by
creating a thread state data structure, then acquiring the GIL, and finally
storing their thread state pointer, before you can start using the Python/C
API.  When you are done, you should reset the thread state pointer, release
the GIL, and finally free the thread state data structure.</p>
<p>The <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> and <a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a> functions do
all of the above automatically.  The typical idiom for calling into Python
from a C thread is:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyGILState_STATE</span><span class="w"> </span><span class="n">gstate</span><span class="p">;</span>
<span class="n">gstate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyGILState_Ensure</span><span class="p">();</span>

<span class="cm">/* Perform Python actions here. */</span>
<span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">CallSomeFunction</span><span class="p">();</span>
<span class="cm">/* evaluate result or handle exception */</span>

<span class="cm">/* Release the thread. No Python API allowed beyond this point. */</span>
<span class="n">PyGILState_Release</span><span class="p">(</span><span class="n">gstate</span><span class="p">);</span>
</pre></div>
</div>
<p>Note that the <code class="docutils literal notranslate"><span class="pre">PyGILState_*</span></code> functions assume there is only one global
interpreter (created automatically by <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>).  Python
supports the creation of additional interpreters (using
<a class="reference internal" href="#c.Py_NewInterpreter" title="Py_NewInterpreter"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreter()</span></code></a>), but mixing multiple interpreters and the
<code class="docutils literal notranslate"><span class="pre">PyGILState_*</span></code> API is unsupported.</p>
</section>
<section id="cautions-about-fork">
<span id="fork-and-threads"></span><h3>Cautions about fork()<a class="headerlink" href="#cautions-about-fork" title="Link to this heading">¶</a></h3>
<p>Another important thing to note about threads is their behaviour in the face
of the C <code class="xref c c-func docutils literal notranslate"><span class="pre">fork()</span></code> call. On most systems with <code class="xref c c-func docutils literal notranslate"><span class="pre">fork()</span></code>, after a
process forks only the thread that issued the fork will exist.  This has a
concrete impact both on how locks must be handled and on all stored state
in CPython’s runtime.</p>
<p>The fact that only the “current” thread remains
means any locks held by other threads will never be released. Python solves
this for <a class="reference internal" href="../library/os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a> by acquiring the locks it uses internally before
the fork, and releasing them afterwards. In addition, it resets any
<a class="reference internal" href="../library/threading.html#lock-objects"><span class="std std-ref">Lock Objects</span></a> in the child. When extending or embedding Python, there
is no way to inform Python of additional (non-Python) locks that need to be
acquired before or reset after a fork. OS facilities such as
<code class="xref c c-func docutils literal notranslate"><span class="pre">pthread_atfork()</span></code> would need to be used to accomplish the same thing.
Additionally, when extending or embedding Python, calling <code class="xref c c-func docutils literal notranslate"><span class="pre">fork()</span></code>
directly rather than through <a class="reference internal" href="../library/os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a> (and returning to or calling
into Python) may result in a deadlock by one of Python’s internal locks
being held by a thread that is defunct after the fork.
<a class="reference internal" href="sys.html#c.PyOS_AfterFork_Child" title="PyOS_AfterFork_Child"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_AfterFork_Child()</span></code></a> tries to reset the necessary locks, but is not
always able to.</p>
<p>The fact that all other threads go away also means that CPython’s
runtime state there must be cleaned up properly, which <a class="reference internal" href="../library/os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a>
does.  This means finalizing all other <a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyThreadState</span></code></a> objects
belonging to the current interpreter and all other
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyInterpreterState</span></code></a> objects.  Due to this and the special
nature of the <a class="reference internal" href="#sub-interpreter-support"><span class="std std-ref">“main” interpreter</span></a>,
<code class="xref c c-func docutils literal notranslate"><span class="pre">fork()</span></code> should only be called in that interpreter’s “main”
thread, where the CPython global runtime was originally initialized.
The only exception is if <code class="xref c c-func docutils literal notranslate"><span class="pre">exec()</span></code> will be called immediately
after.</p>
</section>
<section id="high-level-api">
<h3>High-level API<a class="headerlink" href="#high-level-api" title="Link to this heading">¶</a></h3>
<p>These are the most commonly used types and functions when writing C extension
code, or when embedding the Python interpreter:</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyInterpreterState">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState</span></span></span><a class="headerlink" href="#c.PyInterpreterState" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Limited API</span></a> (as an opaque struct).</em><p>This data structure represents the state shared by a number of cooperating
threads.  Threads belonging to the same interpreter share their module
administration and a few other internal items. There are no public members in
this structure.</p>
<p>Threads belonging to different interpreters initially share nothing, except
process state like available memory, open file descriptors and such.  The global
interpreter lock is also shared by all threads, regardless of to which
interpreter they belong.</p>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c.PyThreadState">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState</span></span></span><a class="headerlink" href="#c.PyThreadState" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Limited API</span></a> (as an opaque struct).</em><p>This data structure represents the state of a single thread.  The only public
data member is:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyThreadState.interp">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">interp</span></span></span><a class="headerlink" href="#c.PyThreadState.interp" title="Link to this definition">¶</a><br /></dt>
<dd><p>This thread’s interpreter state.</p>
</dd></dl>

</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_InitThreads">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_InitThreads</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_InitThreads" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-37">Deprecated function which does nothing.</p>
<p>In Python 3.6 and older, this function created the GIL if it didn’t exist.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The function now does nothing.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>This function is now called by <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>, so you don’t
have to call it yourself anymore.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>This function cannot be called before <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> anymore.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9.</span></p>
</div>
<span class="target" id="index-38"></span></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_ThreadsInitialized">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_ThreadsInitialized</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_ThreadsInitialized" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Returns a non-zero value if <a class="reference internal" href="#c.PyEval_InitThreads" title="PyEval_InitThreads"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_InitThreads()</span></code></a> has been called.  This
function can be called without holding the GIL, and therefore can be used to
avoid calls to the locking API when running single-threaded.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a> is now initialized by <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_SaveThread">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_SaveThread</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_SaveThread" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Release the global interpreter lock (if it has been created) and reset the
thread state to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, returning the previous thread state (which is not
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>).  If the lock has been created, the current thread must have
acquired it.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_RestoreThread">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_RestoreThread</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_RestoreThread" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Acquire the global interpreter lock (if it has been created) and set the
thread state to <em>tstate</em>, which must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  If the lock has been
created, the current thread must not have acquired it, otherwise deadlock
ensues.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling this function from a thread when the runtime is finalizing
will terminate the thread, even if the thread was not created by Python.
You can use <code class="xref c c-func docutils literal notranslate"><span class="pre">_Py_IsFinalizing()</span></code> or <a class="reference internal" href="../library/sys.html#sys.is_finalizing" title="sys.is_finalizing"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.is_finalizing()</span></code></a> to
check if the interpreter is in process of being finalized before calling
this function to avoid unwanted termination.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_Get">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_Get</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_Get" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the current thread state.  The global interpreter lock must be held.
When the current thread state is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, this issues a fatal error (so that
the caller needn’t check for <code class="docutils literal notranslate"><span class="pre">NULL</span></code>).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_Swap">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_Swap</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_Swap" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Swap the current thread state with the thread state given by the argument
<em>tstate</em>, which may be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  The global interpreter lock must be held
and is not released.</p>
</dd></dl>

<p>The following functions use thread-local storage, and are not compatible
with sub-interpreters:</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyGILState_Ensure">
<span class="n"><span class="pre">PyGILState_STATE</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyGILState_Ensure</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyGILState_Ensure" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Ensure that the current thread is ready to call the Python C API regardless
of the current state of Python, or of the global interpreter lock. This may
be called as many times as desired by a thread as long as each call is
matched with a call to <a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a>. In general, other
thread-related APIs may be used between <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> and
<a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a> calls as long as the thread state is restored to
its previous state before the Release().  For example, normal usage of the
<a class="reference internal" href="#c.Py_BEGIN_ALLOW_THREADS" title="Py_BEGIN_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></code></a> and <a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS</span></code></a> macros is
acceptable.</p>
<p>The return value is an opaque “handle” to the thread state when
<a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> was called, and must be passed to
<a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a> to ensure Python is left in the same state. Even
though recursive calls are allowed, these handles <em>cannot</em> be shared - each
unique call to <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> must save the handle for its call
to <a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a>.</p>
<p>When the function returns, the current thread will hold the GIL and be able
to call arbitrary Python code.  Failure is a fatal error.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling this function from a thread when the runtime is finalizing
will terminate the thread, even if the thread was not created by Python.
You can use <code class="xref c c-func docutils literal notranslate"><span class="pre">_Py_IsFinalizing()</span></code> or <a class="reference internal" href="../library/sys.html#sys.is_finalizing" title="sys.is_finalizing"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.is_finalizing()</span></code></a> to
check if the interpreter is in process of being finalized before calling
this function to avoid unwanted termination.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyGILState_Release">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyGILState_Release</span></span></span><span class="sig-paren">(</span><span class="n"><span class="pre">PyGILState_STATE</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyGILState_Release" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Release any resources previously acquired.  After this call, Python’s state will
be the same as it was prior to the corresponding <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> call
(but generally this state will be unknown to the caller, hence the use of the
GILState API).</p>
<p>Every call to <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> must be matched by a call to
<a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a> on the same thread.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyGILState_GetThisThreadState">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyGILState_GetThisThreadState</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyGILState_GetThisThreadState" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Get the current thread state for this thread.  May return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if no
GILState API has been used on the current thread.  Note that the main thread
always has such a thread-state, even if no auto-thread-state call has been
made on the main thread.  This is mainly a helper/diagnostic function.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyGILState_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyGILState_Check</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyGILState_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">1</span></code> if the current thread is holding the GIL and <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.
This function can be called from any thread at any time.
Only if it has had its Python thread state initialized and currently is
holding the GIL will it return <code class="docutils literal notranslate"><span class="pre">1</span></code>.
This is mainly a helper/diagnostic function.  It can be useful
for example in callback contexts or memory allocation functions when
knowing that the GIL is locked can allow the caller to perform sensitive
actions or otherwise behave differently.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p>The following macros are normally used without a trailing semicolon; look for
example usage in the Python source distribution.</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_BEGIN_ALLOW_THREADS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></span></span><a class="headerlink" href="#c.Py_BEGIN_ALLOW_THREADS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This macro expands to <code class="docutils literal notranslate"><span class="pre">{</span> <span class="pre">PyThreadState</span> <span class="pre">*_save;</span> <span class="pre">_save</span> <span class="pre">=</span> <span class="pre">PyEval_SaveThread();</span></code>.
Note that it contains an opening brace; it must be matched with a following
<a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS</span></code></a> macro.  See above for further discussion of this
macro.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_END_ALLOW_THREADS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_END_ALLOW_THREADS</span></span></span><a class="headerlink" href="#c.Py_END_ALLOW_THREADS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This macro expands to <code class="docutils literal notranslate"><span class="pre">PyEval_RestoreThread(_save);</span> <span class="pre">}</span></code>. Note that it contains
a closing brace; it must be matched with an earlier
<a class="reference internal" href="#c.Py_BEGIN_ALLOW_THREADS" title="Py_BEGIN_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></code></a> macro.  See above for further discussion of
this macro.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_BLOCK_THREADS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_BLOCK_THREADS</span></span></span><a class="headerlink" href="#c.Py_BLOCK_THREADS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This macro expands to <code class="docutils literal notranslate"><span class="pre">PyEval_RestoreThread(_save);</span></code>: it is equivalent to
<a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS</span></code></a> without the closing brace.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_UNBLOCK_THREADS">
<span class="sig-name descname"><span class="n"><span class="pre">Py_UNBLOCK_THREADS</span></span></span><a class="headerlink" href="#c.Py_UNBLOCK_THREADS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This macro expands to <code class="docutils literal notranslate"><span class="pre">_save</span> <span class="pre">=</span> <span class="pre">PyEval_SaveThread();</span></code>: it is equivalent to
<a class="reference internal" href="#c.Py_BEGIN_ALLOW_THREADS" title="Py_BEGIN_ALLOW_THREADS"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_BEGIN_ALLOW_THREADS</span></code></a> without the opening brace and variable
declaration.</p>
</dd></dl>

</section>
<section id="low-level-api">
<h3>Low-level API<a class="headerlink" href="#low-level-api" title="Link to this heading">¶</a></h3>
<p>All of the following functions must be called after <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a> now initializes the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>.</p>
</div>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_New">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_New</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Create a new interpreter state object.  The global interpreter lock need not
be held, but may be held if it is necessary to serialize calls to this
function.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="../library/sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">cpython.PyInterpreterState_New</span></code> with no arguments.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Clear">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Clear</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Clear" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Reset all information in an interpreter state object.  The global interpreter
lock must be held.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="../library/sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">cpython.PyInterpreterState_Clear</span></code> with no arguments.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Delete">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Delete</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Delete" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Destroy an interpreter state object.  The global interpreter lock need not be
held.  The interpreter state must have been reset with a previous call to
<a class="reference internal" href="#c.PyInterpreterState_Clear" title="PyInterpreterState_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_Clear()</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_New">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_New</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_New" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Create a new thread state object belonging to the given interpreter object.
The global interpreter lock need not be held, but may be held if it is
necessary to serialize calls to this function.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_Clear">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_Clear</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_Clear" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Reset all information in a thread state object.  The global interpreter lock
must be held.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>This function now calls the <code class="xref c c-member docutils literal notranslate"><span class="pre">PyThreadState.on_delete</span></code> callback.
Previously, that happened in <a class="reference internal" href="#c.PyThreadState_Delete" title="PyThreadState_Delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Delete()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_Delete">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_Delete</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_Delete" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Destroy a thread state object.  The global interpreter lock need not be held.
The thread state must have been reset with a previous call to
<a class="reference internal" href="#c.PyThreadState_Clear" title="PyThreadState_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Clear()</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_DeleteCurrent">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_DeleteCurrent</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_DeleteCurrent" title="Link to this definition">¶</a><br /></dt>
<dd><p>Destroy the current thread state and release the global interpreter lock.
Like <a class="reference internal" href="#c.PyThreadState_Delete" title="PyThreadState_Delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Delete()</span></code></a>, the global interpreter lock need not
be held. The thread state must have been reset with a previous call
to <a class="reference internal" href="#c.PyThreadState_Clear" title="PyThreadState_Clear"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Clear()</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_GetFrame">
<a class="reference internal" href="frame.html#c.PyFrameObject" title="PyFrameObject"><span class="n"><span class="pre">PyFrameObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_GetFrame</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_GetFrame" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Get the current frame of the Python thread state <em>tstate</em>.</p>
<p>Return a <a class="reference internal" href="../glossary.html#term-strong-reference"><span class="xref std std-term">strong reference</span></a>. Return <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if no frame is currently
executing.</p>
<p>See also <a class="reference internal" href="reflection.html#c.PyEval_GetFrame" title="PyEval_GetFrame"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_GetFrame()</span></code></a>.</p>
<p><em>tstate</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_GetID">
<span class="n"><span class="pre">uint64_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_GetID</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_GetID" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Get the unique thread state identifier of the Python thread state <em>tstate</em>.</p>
<p><em>tstate</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_GetInterpreter">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_GetInterpreter</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_GetInterpreter" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.10.</em><p>Get the interpreter of the Python thread state <em>tstate</em>.</p>
<p><em>tstate</em> must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_EnterTracing">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_EnterTracing</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_EnterTracing" title="Link to this definition">¶</a><br /></dt>
<dd><p>Suspend tracing and profiling in the Python thread state <em>tstate</em>.</p>
<p>Resume them using the <a class="reference internal" href="#c.PyThreadState_LeaveTracing" title="PyThreadState_LeaveTracing"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_LeaveTracing()</span></code></a> function.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_LeaveTracing">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_LeaveTracing</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_LeaveTracing" title="Link to this definition">¶</a><br /></dt>
<dd><p>Resume tracing and profiling in the Python thread state <em>tstate</em> suspended
by the <a class="reference internal" href="#c.PyThreadState_EnterTracing" title="PyThreadState_EnterTracing"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_EnterTracing()</span></code></a> function.</p>
<p>See also <a class="reference internal" href="#c.PyEval_SetTrace" title="PyEval_SetTrace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetTrace()</span></code></a> and <a class="reference internal" href="#c.PyEval_SetProfile" title="PyEval_SetProfile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetProfile()</span></code></a>
functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Get">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Get</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Get" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.9.</em><p>Get the current interpreter.</p>
<p>Issue a fatal error if there no current Python thread state or no current
interpreter. It cannot return NULL.</p>
<p>The caller must hold the GIL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_GetID">
<span class="n"><span class="pre">int64_t</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_GetID</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_GetID" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return the interpreter’s unique ID.  If there was any error in doing
so then <code class="docutils literal notranslate"><span class="pre">-1</span></code> is returned and an error is set.</p>
<p>The caller must hold the GIL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_GetDict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_GetDict</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_GetDict" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.8.</em><p>Return a dictionary in which interpreter-specific data may be stored.
If this function returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> then no exception has been raised and
the caller should assume no interpreter-specific dict is available.</p>
<p>This is not a replacement for <a class="reference internal" href="module.html#c.PyModule_GetState" title="PyModule_GetState"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_GetState()</span></code></a>, which
extensions should use to store interpreter-specific state information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="c type">
<dt class="sig sig-object c" id="c._PyFrameEvalFunction">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">_PyFrameEvalFunction</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="frame.html#c._PyInterpreterFrame" title="_PyInterpreterFrame"><span class="n"><span class="pre">_PyInterpreterFrame</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">frame</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">throwflag</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c._PyFrameEvalFunction" title="Link to this definition">¶</a><br /></dt>
<dd><p>Type of a frame evaluation function.</p>
<p>The <em>throwflag</em> parameter is used by the <code class="docutils literal notranslate"><span class="pre">throw()</span></code> method of generators:
if non-zero, handle the current exception.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The function now takes a <em>tstate</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The <em>frame</em> parameter changed from <code class="docutils literal notranslate"><span class="pre">PyFrameObject*</span></code> to <code class="docutils literal notranslate"><span class="pre">_PyInterpreterFrame*</span></code>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._PyInterpreterState_GetEvalFrameFunc">
<a class="reference internal" href="#c._PyFrameEvalFunction" title="_PyFrameEvalFunction"><span class="n"><span class="pre">_PyFrameEvalFunction</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_PyInterpreterState_GetEvalFrameFunc</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._PyInterpreterState_GetEvalFrameFunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>Get the frame evaluation function.</p>
<p>See the <span class="target" id="index-39"></span><a class="pep reference external" href="https://peps.python.org/pep-0523/"><strong>PEP 523</strong></a> “Adding a frame evaluation API to CPython”.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._PyInterpreterState_SetEvalFrameFunc">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_PyInterpreterState_SetEvalFrameFunc</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span>, <a class="reference internal" href="#c._PyFrameEvalFunction" title="_PyFrameEvalFunction"><span class="n"><span class="pre">_PyFrameEvalFunction</span></span></a><span class="w"> </span><span class="n"><span class="pre">eval_frame</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._PyInterpreterState_SetEvalFrameFunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the frame evaluation function.</p>
<p>See the <span class="target" id="index-40"></span><a class="pep reference external" href="https://peps.python.org/pep-0523/"><strong>PEP 523</strong></a> “Adding a frame evaluation API to CPython”.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_GetDict">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_GetDict</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_GetDict" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: Borrowed reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a dictionary in which extensions can store thread-specific state
information.  Each extension should use a unique key to use to store state in
the dictionary.  It is okay to call this function when no current thread state
is available. If this function returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, no exception has been raised and
the caller should assume no current thread state is available.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_SetAsyncExc">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_SetAsyncExc</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="n"><span class="pre">id</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">exc</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_SetAsyncExc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Asynchronously raise an exception in a thread. The <em>id</em> argument is the thread
id of the target thread; <em>exc</em> is the exception object to be raised. This
function does not steal any references to <em>exc</em>. To prevent naive misuse, you
must write your own C extension to call this.  Must be called with the GIL held.
Returns the number of thread states modified; this is normally one, but will be
zero if the thread id isn’t found.  If <em>exc</em> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, the pending
exception (if any) for the thread is cleared. This raises no exceptions.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The type of the <em>id</em> parameter changed from <span class="c-expr sig sig-inline c"><span class="kt">long</span></span> to
<span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span></span>.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_AcquireThread">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_AcquireThread</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_AcquireThread" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Acquire the global interpreter lock and set the current thread state to
<em>tstate</em>, which must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  The lock must have been created earlier.
If this thread already has the lock, deadlock ensues.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling this function from a thread when the runtime is finalizing
will terminate the thread, even if the thread was not created by Python.
You can use <code class="xref c c-func docutils literal notranslate"><span class="pre">_Py_IsFinalizing()</span></code> or <a class="reference internal" href="../library/sys.html#sys.is_finalizing" title="sys.is_finalizing"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.is_finalizing()</span></code></a> to
check if the interpreter is in process of being finalized before calling
this function to avoid unwanted termination.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Updated to be consistent with <a class="reference internal" href="#c.PyEval_RestoreThread" title="PyEval_RestoreThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_RestoreThread()</span></code></a>,
<a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS()</span></code></a>, and <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a>,
and terminate the current thread if called while the interpreter is finalizing.</p>
</div>
<p><a class="reference internal" href="#c.PyEval_RestoreThread" title="PyEval_RestoreThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_RestoreThread()</span></code></a> is a higher-level function which is always
available (even when threads have not been initialized).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_ReleaseThread">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_ReleaseThread</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_ReleaseThread" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Reset the current thread state to <code class="docutils literal notranslate"><span class="pre">NULL</span></code> and release the global interpreter
lock.  The lock must have been created earlier and must be held by the current
thread.  The <em>tstate</em> argument, which must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, is only used to check
that it represents the current thread state — if it isn’t, a fatal error is
reported.</p>
<p><a class="reference internal" href="#c.PyEval_SaveThread" title="PyEval_SaveThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SaveThread()</span></code></a> is a higher-level function which is always
available (even when threads have not been initialized).</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_AcquireLock">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_AcquireLock</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_AcquireLock" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Acquire the global interpreter lock.  The lock must have been created earlier.
If this thread already has the lock, a deadlock ensues.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.2: </span>This function does not update the current thread state.  Please use
<a class="reference internal" href="#c.PyEval_RestoreThread" title="PyEval_RestoreThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_RestoreThread()</span></code></a> or <a class="reference internal" href="#c.PyEval_AcquireThread" title="PyEval_AcquireThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_AcquireThread()</span></code></a>
instead.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling this function from a thread when the runtime is finalizing
will terminate the thread, even if the thread was not created by Python.
You can use <code class="xref c c-func docutils literal notranslate"><span class="pre">_Py_IsFinalizing()</span></code> or <a class="reference internal" href="../library/sys.html#sys.is_finalizing" title="sys.is_finalizing"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.is_finalizing()</span></code></a> to
check if the interpreter is in process of being finalized before calling
this function to avoid unwanted termination.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Updated to be consistent with <a class="reference internal" href="#c.PyEval_RestoreThread" title="PyEval_RestoreThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_RestoreThread()</span></code></a>,
<a class="reference internal" href="#c.Py_END_ALLOW_THREADS" title="Py_END_ALLOW_THREADS"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_END_ALLOW_THREADS()</span></code></a>, and <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a>,
and terminate the current thread if called while the interpreter is finalizing.</p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_ReleaseLock">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_ReleaseLock</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_ReleaseLock" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Release the global interpreter lock.  The lock must have been created earlier.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.2: </span>This function does not update the current thread state.  Please use
<a class="reference internal" href="#c.PyEval_SaveThread" title="PyEval_SaveThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SaveThread()</span></code></a> or <a class="reference internal" href="#c.PyEval_ReleaseThread" title="PyEval_ReleaseThread"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_ReleaseThread()</span></code></a>
instead.</p>
</div>
</dd></dl>

</section>
</section>
<section id="sub-interpreter-support">
<span id="id1"></span><h2>Sub-interpreter support<a class="headerlink" href="#sub-interpreter-support" title="Link to this heading">¶</a></h2>
<p>While in most uses, you will only embed a single Python interpreter, there
are cases where you need to create several independent interpreters in the
same process and perhaps even in the same thread. Sub-interpreters allow
you to do that.</p>
<p>The “main” interpreter is the first one created when the runtime initializes.
It is usually the only Python interpreter in a process.  Unlike sub-interpreters,
the main interpreter has unique process-global responsibilities like signal
handling.  It is also responsible for execution during runtime initialization and
is usually the active interpreter during runtime finalization.  The
<a class="reference internal" href="#c.PyInterpreterState_Main" title="PyInterpreterState_Main"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyInterpreterState_Main()</span></code></a> function returns a pointer to its state.</p>
<p>You can switch between sub-interpreters using the <a class="reference internal" href="#c.PyThreadState_Swap" title="PyThreadState_Swap"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThreadState_Swap()</span></code></a>
function. You can create and destroy them using the following functions:</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyInterpreterConfig">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterConfig</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p>Structure containing most parameters to configure a sub-interpreter.
Its values are used only in <a class="reference internal" href="#c.Py_NewInterpreterFromConfig" title="Py_NewInterpreterFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreterFromConfig()</span></code></a> and
never modified by the runtime.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Structure fields:</p>
<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.use_main_obmalloc">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">use_main_obmalloc</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.use_main_obmalloc" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then the sub-interpreter will use its own
“object” allocator state.
Otherwise it will use (share) the main interpreter’s.</p>
<p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then
<a class="reference internal" href="#c.PyInterpreterConfig.check_multi_interp_extensions" title="PyInterpreterConfig.check_multi_interp_extensions"><code class="xref c c-member docutils literal notranslate"><span class="pre">check_multi_interp_extensions</span></code></a>
must be <code class="docutils literal notranslate"><span class="pre">1</span></code> (non-zero).
If this is <code class="docutils literal notranslate"><span class="pre">1</span></code> then <a class="reference internal" href="#c.PyInterpreterConfig.gil" title="PyInterpreterConfig.gil"><code class="xref c c-member docutils literal notranslate"><span class="pre">gil</span></code></a>
must not be <a class="reference internal" href="#c.PyInterpreterConfig_OWN_GIL" title="PyInterpreterConfig_OWN_GIL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyInterpreterConfig_OWN_GIL</span></code></a>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.allow_fork">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">allow_fork</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.allow_fork" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then the runtime will not support forking the
process in any thread where the sub-interpreter is currently active.
Otherwise fork is unrestricted.</p>
<p>Note that the <a class="reference internal" href="../library/subprocess.html#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module still works
when fork is disallowed.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.allow_exec">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">allow_exec</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.allow_exec" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then the runtime will not support replacing the
current process via exec (e.g. <a class="reference internal" href="../library/os.html#os.execv" title="os.execv"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.execv()</span></code></a>) in any thread
where the sub-interpreter is currently active.
Otherwise exec is unrestricted.</p>
<p>Note that the <a class="reference internal" href="../library/subprocess.html#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> module still works
when exec is disallowed.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.allow_threads">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">allow_threads</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.allow_threads" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then the sub-interpreter’s <a class="reference internal" href="../library/threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module
won’t create threads.
Otherwise threads are allowed.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.allow_daemon_threads">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">allow_daemon_threads</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.allow_daemon_threads" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then the sub-interpreter’s <a class="reference internal" href="../library/threading.html#module-threading" title="threading: Thread-based parallelism."><code class="xref py py-mod docutils literal notranslate"><span class="pre">threading</span></code></a> module
won’t create daemon threads.
Otherwise daemon threads are allowed (as long as
<a class="reference internal" href="#c.PyInterpreterConfig.allow_threads" title="PyInterpreterConfig.allow_threads"><code class="xref c c-member docutils literal notranslate"><span class="pre">allow_threads</span></code></a> is non-zero).</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.check_multi_interp_extensions">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">check_multi_interp_extensions</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.check_multi_interp_extensions" title="Link to this definition">¶</a><br /></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">0</span></code> then all extension modules may be imported,
including legacy (single-phase init) modules,
in any thread where the sub-interpreter is currently active.
Otherwise only multi-phase init extension modules
(see <span class="target" id="index-41"></span><a class="pep reference external" href="https://peps.python.org/pep-0489/"><strong>PEP 489</strong></a>) may be imported.
(Also see <a class="reference internal" href="module.html#c.Py_mod_multiple_interpreters" title="Py_mod_multiple_interpreters"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_mod_multiple_interpreters</span></code></a>.)</p>
<p>This must be <code class="docutils literal notranslate"><span class="pre">1</span></code> (non-zero) if
<a class="reference internal" href="#c.PyInterpreterConfig.use_main_obmalloc" title="PyInterpreterConfig.use_main_obmalloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">use_main_obmalloc</span></code></a> is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="c member">
<dt class="sig sig-object c" id="c.PyInterpreterConfig.gil">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">gil</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig.gil" title="Link to this definition">¶</a><br /></dt>
<dd><p>This determines the operation of the GIL for the sub-interpreter.
It may be one of the following:</p>
<dl class="c macro">
<dt class="sig sig-object c" id="c.PyInterpreterConfig_DEFAULT_GIL">
<span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterConfig_DEFAULT_GIL</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig_DEFAULT_GIL" title="Link to this definition">¶</a><br /></dt>
<dd><p>Use the default selection (<a class="reference internal" href="#c.PyInterpreterConfig_SHARED_GIL" title="PyInterpreterConfig_SHARED_GIL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyInterpreterConfig_SHARED_GIL</span></code></a>).</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyInterpreterConfig_SHARED_GIL">
<span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterConfig_SHARED_GIL</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig_SHARED_GIL" title="Link to this definition">¶</a><br /></dt>
<dd><p>Use (share) the main interpreter’s GIL.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.PyInterpreterConfig_OWN_GIL">
<span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterConfig_OWN_GIL</span></span></span><a class="headerlink" href="#c.PyInterpreterConfig_OWN_GIL" title="Link to this definition">¶</a><br /></dt>
<dd><p>Use the sub-interpreter’s own GIL.</p>
</dd></dl>

<p>If this is <a class="reference internal" href="#c.PyInterpreterConfig_OWN_GIL" title="PyInterpreterConfig_OWN_GIL"><code class="xref c c-macro docutils literal notranslate"><span class="pre">PyInterpreterConfig_OWN_GIL</span></code></a> then
<a class="reference internal" href="#c.PyInterpreterConfig.use_main_obmalloc" title="PyInterpreterConfig.use_main_obmalloc"><code class="xref c c-member docutils literal notranslate"><span class="pre">PyInterpreterConfig.use_main_obmalloc</span></code></a> must be <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_NewInterpreterFromConfig">
<a class="reference internal" href="init_config.html#c.PyStatus" title="PyStatus"><span class="n"><span class="pre">PyStatus</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_NewInterpreterFromConfig</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate_p</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><a class="reference internal" href="#c.PyInterpreterConfig" title="PyInterpreterConfig"><span class="n"><span class="pre">PyInterpreterConfig</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">config</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_NewInterpreterFromConfig" title="Link to this definition">¶</a><br /></dt>
<dd><p id="index-42">Create a new sub-interpreter.  This is an (almost) totally separate environment
for the execution of Python code.  In particular, the new interpreter has
separate, independent versions of all imported modules, including the
fundamental modules <a class="reference internal" href="../library/builtins.html#module-builtins" title="builtins: The module that provides the built-in namespace."><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code></a>, <a class="reference internal" href="../library/__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> and <a class="reference internal" href="../library/sys.html#module-sys" title="sys: Access system-specific parameters and functions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys</span></code></a>.  The
table of loaded modules (<code class="docutils literal notranslate"><span class="pre">sys.modules</span></code>) and the module search path
(<code class="docutils literal notranslate"><span class="pre">sys.path</span></code>) are also separate.  The new environment has no <code class="docutils literal notranslate"><span class="pre">sys.argv</span></code>
variable.  It has new standard I/O stream file objects <code class="docutils literal notranslate"><span class="pre">sys.stdin</span></code>,
<code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code> and <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code> (however these refer to the same underlying
file descriptors).</p>
<p>The given <em>config</em> controls the options with which the interpreter
is initialized.</p>
<p>Upon success, <em>tstate_p</em> will be set to the first thread state
created in the new
sub-interpreter.  This thread state is made in the current thread state.
Note that no actual thread is created; see the discussion of thread states
below.  If creation of the new interpreter is unsuccessful,
<em>tstate_p</em> is set to <code class="docutils literal notranslate"><span class="pre">NULL</span></code>;
no exception is set since the exception state is stored in the
current thread state and there may not be a current thread state.</p>
<p>Like all other Python/C API functions, the global interpreter lock
must be held before calling this function and is still held when it
returns.  Likewise a current thread state must be set on entry.  On
success, the returned thread state will be set as current.  If the
sub-interpreter is created with its own GIL then the GIL of the
calling interpreter will be released.  When the function returns,
the new interpreter’s GIL will be held by the current thread and
the previously interpreter’s GIL will remain released here.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Sub-interpreters are most effective when isolated from each other,
with certain functionality restricted:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="n">PyInterpreterConfig</span><span class="w"> </span><span class="n">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="p">.</span><span class="n">use_main_obmalloc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">allow_fork</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">allow_exec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">allow_threads</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">allow_daemon_threads</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">check_multi_interp_extensions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="p">.</span><span class="n">gil</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">PyInterpreterConfig_OWN_GIL</span><span class="p">,</span>
<span class="p">};</span>
<span class="n">PyThreadState</span><span class="w"> </span><span class="o">*</span><span class="n">tstate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Py_NewInterpreterFromConfig</span><span class="p">(</span><span class="o">&amp;</span><span class="n">config</span><span class="p">);</span>
</pre></div>
</div>
<p>Note that the config is used only briefly and does not get modified.
During initialization the config’s values are converted into various
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyInterpreterState</span></code></a> values.  A read-only copy of the config
may be stored internally on the <a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyInterpreterState</span></code></a>.</p>
<p id="index-43">Extension modules are shared between (sub-)interpreters as follows:</p>
<ul>
<li><p>For modules using multi-phase initialization,
e.g. <a class="reference internal" href="module.html#c.PyModule_FromDefAndSpec" title="PyModule_FromDefAndSpec"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_FromDefAndSpec()</span></code></a>, a separate module object is
created and initialized for each interpreter.
Only C-level static and global variables are shared between these
module objects.</p></li>
<li><p>For modules using single-phase initialization,
e.g. <a class="reference internal" href="module.html#c.PyModule_Create" title="PyModule_Create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyModule_Create()</span></code></a>, the first time a particular extension
is imported, it is initialized normally, and a (shallow) copy of its
module’s dictionary is squirreled away.
When the same extension is imported by another (sub-)interpreter, a new
module is initialized and filled with the contents of this copy; the
extension’s <code class="docutils literal notranslate"><span class="pre">init</span></code> function is not called.
Objects in the module’s dictionary thus end up shared across
(sub-)interpreters, which might cause unwanted behavior (see
<a class="reference internal" href="#bugs-and-caveats">Bugs and caveats</a> below).</p>
<p>Note that this is different from what happens when an extension is
imported after the interpreter has been completely re-initialized by
calling <a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> and <a class="reference internal" href="#c.Py_Initialize" title="Py_Initialize"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_Initialize()</span></code></a>; in that
case, the extension’s <code class="docutils literal notranslate"><span class="pre">initmodule</span></code> function <em>is</em> called again.
As with multi-phase initialization, this means that only C-level static
and global variables are shared between these modules.</p>
</li>
</ul>
<span class="target" id="index-44"></span></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_NewInterpreter">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_NewInterpreter</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">void</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_NewInterpreter" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-45">Create a new sub-interpreter.  This is essentially just a wrapper
around <a class="reference internal" href="#c.Py_NewInterpreterFromConfig" title="Py_NewInterpreterFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreterFromConfig()</span></code></a> with a config that
preserves the existing behavior.  The result is an unisolated
sub-interpreter that shares the main interpreter’s GIL, allows
fork/exec, allows daemon threads, and allows single-phase init
modules.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.Py_EndInterpreter">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_EndInterpreter</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_EndInterpreter" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p id="index-46">Destroy the (sub-)interpreter represented by the given thread state.
The given thread state must be the current thread state.  See the
discussion of thread states below.  When the call returns,
the current thread state is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  All thread states associated
with this interpreter are destroyed.  The global interpreter lock
used by the target interpreter must be held before calling this
function.  No GIL is held when it returns.</p>
<p><a class="reference internal" href="#c.Py_FinalizeEx" title="Py_FinalizeEx"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_FinalizeEx()</span></code></a> will destroy all sub-interpreters that
haven’t been explicitly destroyed at that point.</p>
</dd></dl>

<section id="a-per-interpreter-gil">
<h3>A Per-Interpreter GIL<a class="headerlink" href="#a-per-interpreter-gil" title="Link to this heading">¶</a></h3>
<p>Using <a class="reference internal" href="#c.Py_NewInterpreterFromConfig" title="Py_NewInterpreterFromConfig"><code class="xref c c-func docutils literal notranslate"><span class="pre">Py_NewInterpreterFromConfig()</span></code></a> you can create
a sub-interpreter that is completely isolated from other interpreters,
including having its own GIL.  The most important benefit of this
isolation is that such an interpreter can execute Python code without
being blocked by other interpreters or blocking any others.  Thus a
single Python process can truly take advantage of multiple CPU cores
when running Python code.  The isolation also encourages a different
approach to concurrency than that of just using threads.
(See <span class="target" id="index-47"></span><a class="pep reference external" href="https://peps.python.org/pep-0554/"><strong>PEP 554</strong></a>.)</p>
<p>Using an isolated interpreter requires vigilance in preserving that
isolation.  That especially means not sharing any objects or mutable
state without guarantees about thread-safety.  Even objects that are
otherwise immutable (e.g. <code class="docutils literal notranslate"><span class="pre">None</span></code>, <code class="docutils literal notranslate"><span class="pre">(1,</span> <span class="pre">5)</span></code>) can’t normally be shared
because of the refcount.  One simple but less-efficient approach around
this is to use a global lock around all use of some state (or object).
Alternately, effectively immutable objects (like integers or strings)
can be made safe in spite of their refcounts by making them “immortal”.
In fact, this has been done for the builtin singletons, small integers,
and a number of other builtin objects.</p>
<p>If you preserve isolation then you will have access to proper multi-core
computing without the complications that come with free-threading.
Failure to preserve isolation will expose you to the full consequences
of free-threading, including races and hard-to-debug crashes.</p>
<p>Aside from that, one of the main challenges of using multiple isolated
interpreters is how to communicate between them safely (not break
isolation) and efficiently.  The runtime and stdlib do not provide
any standard approach to this yet.  A future stdlib module would help
mitigate the effort of preserving isolation and expose effective tools
for communicating (and sharing) data between interpreters.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</section>
<section id="bugs-and-caveats">
<h3>Bugs and caveats<a class="headerlink" href="#bugs-and-caveats" title="Link to this heading">¶</a></h3>
<p>Because sub-interpreters (and the main interpreter) are part of the same
process, the insulation between them isn’t perfect — for example, using
low-level file operations like  <a class="reference internal" href="../library/os.html#os.close" title="os.close"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.close()</span></code></a> they can
(accidentally or maliciously) affect each other’s open files.  Because of the
way extensions are shared between (sub-)interpreters, some extensions may not
work properly; this is especially likely when using single-phase initialization
or (static) global variables.
It is possible to insert objects created in one sub-interpreter into
a namespace of another (sub-)interpreter; this should be avoided if possible.</p>
<p>Special care should be taken to avoid sharing user-defined functions,
methods, instances or classes between sub-interpreters, since import
operations executed by such objects may affect the wrong (sub-)interpreter’s
dictionary of loaded modules. It is equally important to avoid sharing
objects from which the above are reachable.</p>
<p>Also note that combining this functionality with <code class="docutils literal notranslate"><span class="pre">PyGILState_*</span></code> APIs
is delicate, because these APIs assume a bijection between Python thread states
and OS-level threads, an assumption broken by the presence of sub-interpreters.
It is highly recommended that you don’t switch sub-interpreters between a pair
of matching <a class="reference internal" href="#c.PyGILState_Ensure" title="PyGILState_Ensure"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Ensure()</span></code></a> and <a class="reference internal" href="#c.PyGILState_Release" title="PyGILState_Release"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyGILState_Release()</span></code></a> calls.
Furthermore, extensions (such as <a class="reference internal" href="../library/ctypes.html#module-ctypes" title="ctypes: A foreign function library for Python."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ctypes</span></code></a>) using these APIs to allow calling
of Python code from non-Python created threads will probably be broken when using
sub-interpreters.</p>
</section>
</section>
<section id="asynchronous-notifications">
<h2>Asynchronous Notifications<a class="headerlink" href="#asynchronous-notifications" title="Link to this heading">¶</a></h2>
<p>A mechanism is provided to make asynchronous notifications to the main
interpreter thread.  These notifications take the form of a function
pointer and a void pointer argument.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.Py_AddPendingCall">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_AddPendingCall</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">func</span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><span class="kt"><span class="pre">void</span></span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">)</span></span>, <span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">arg</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.Py_AddPendingCall" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Schedule a function to be called from the main interpreter thread.  On
success, <code class="docutils literal notranslate"><span class="pre">0</span></code> is returned and <em>func</em> is queued for being called in the
main thread.  On failure, <code class="docutils literal notranslate"><span class="pre">-1</span></code> is returned without setting any exception.</p>
<p>When successfully queued, <em>func</em> will be <em>eventually</em> called from the
main interpreter thread with the argument <em>arg</em>.  It will be called
asynchronously with respect to normally running Python code, but with
both these conditions met:</p>
<ul class="simple">
<li><p>on a <a class="reference internal" href="../glossary.html#term-bytecode"><span class="xref std std-term">bytecode</span></a> boundary;</p></li>
<li><p>with the main thread holding the <a class="reference internal" href="../glossary.html#term-global-interpreter-lock"><span class="xref std std-term">global interpreter lock</span></a>
(<em>func</em> can therefore use the full C API).</p></li>
</ul>
<p><em>func</em> must return <code class="docutils literal notranslate"><span class="pre">0</span></code> on success, or <code class="docutils literal notranslate"><span class="pre">-1</span></code> on failure with an exception
set.  <em>func</em> won’t be interrupted to perform another asynchronous
notification recursively, but it can still be interrupted to switch
threads if the global interpreter lock is released.</p>
<p>This function doesn’t need a current thread state to run, and it doesn’t
need the global interpreter lock.</p>
<p>To call this function in a subinterpreter, the caller must hold the GIL.
Otherwise, the function <em>func</em> can be scheduled to be called from the wrong
interpreter.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This is a low-level function, only useful for very special cases.
There is no guarantee that <em>func</em> will be called as quick as
possible.  If the main thread is busy executing a system call,
<em>func</em> won’t be called before the system call returns.  This
function is generally <strong>not</strong> suitable for calling Python code from
arbitrary C threads.  Instead, use the <a class="reference internal" href="#gilstate"><span class="std std-ref">PyGILState API</span></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>If this function is called in a subinterpreter, the function <em>func</em> is
now scheduled to be called from the subinterpreter, rather than being
called from the main interpreter. Each subinterpreter now has its own
list of scheduled calls.</p>
</div>
</dd></dl>

</section>
<section id="profiling-and-tracing">
<span id="profiling"></span><h2>Profiling and Tracing<a class="headerlink" href="#profiling-and-tracing" title="Link to this heading">¶</a></h2>
<p>The Python interpreter provides some low-level support for attaching profiling
and execution tracing facilities.  These are used for profiling, debugging, and
coverage analysis tools.</p>
<p>This C interface allows the profiling or tracing code to avoid the overhead of
calling through Python-level callable objects, making a direct C function call
instead.  The essential attributes of the facility have not changed; the
interface allows trace functions to be installed per-thread, and the basic
events reported to the trace function are the same as had been reported to the
Python-level trace functions in previous versions.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.Py_tracefunc">
<span class="k"><span class="pre">typedef</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">(</span></span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">Py_tracefunc</span></span></span><span class="p"><span class="pre">)</span></span><span class="p"><span class="pre">(</span></span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="frame.html#c.PyFrameObject" title="PyFrameObject"><span class="n"><span class="pre">PyFrameObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">frame</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">what</span></span><span class="p"><span class="pre">,</span></span><span class="w"> </span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">arg</span></span><span class="p"><span class="pre">)</span></span><a class="headerlink" href="#c.Py_tracefunc" title="Link to this definition">¶</a><br /></dt>
<dd><p>The type of the trace function registered using <a class="reference internal" href="#c.PyEval_SetProfile" title="PyEval_SetProfile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetProfile()</span></code></a> and
<a class="reference internal" href="#c.PyEval_SetTrace" title="PyEval_SetTrace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetTrace()</span></code></a>. The first parameter is the object passed to the
registration function as <em>obj</em>, <em>frame</em> is the frame object to which the event
pertains, <em>what</em> is one of the constants <a class="reference internal" href="#c.PyTrace_CALL" title="PyTrace_CALL"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_CALL</span></code></a>,
<a class="reference internal" href="#c.PyTrace_EXCEPTION" title="PyTrace_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_EXCEPTION</span></code></a>, <a class="reference internal" href="#c.PyTrace_LINE" title="PyTrace_LINE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_LINE</span></code></a>, <a class="reference internal" href="#c.PyTrace_RETURN" title="PyTrace_RETURN"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_RETURN</span></code></a>,
<a class="reference internal" href="#c.PyTrace_C_CALL" title="PyTrace_C_CALL"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_CALL</span></code></a>, <a class="reference internal" href="#c.PyTrace_C_EXCEPTION" title="PyTrace_C_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_EXCEPTION</span></code></a>, <a class="reference internal" href="#c.PyTrace_C_RETURN" title="PyTrace_C_RETURN"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_RETURN</span></code></a>,
or <a class="reference internal" href="#c.PyTrace_OPCODE" title="PyTrace_OPCODE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_OPCODE</span></code></a>, and <em>arg</em> depends on the value of <em>what</em>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Value of <em>what</em></p></th>
<th class="head"><p>Meaning of <em>arg</em></p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTrace_CALL" title="PyTrace_CALL"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_CALL</span></code></a></p></td>
<td><p>Always <a class="reference internal" href="none.html#c.Py_None" title="Py_None"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_None</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTrace_EXCEPTION" title="PyTrace_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_EXCEPTION</span></code></a></p></td>
<td><p>Exception information as returned by
<a class="reference internal" href="../library/sys.html#sys.exc_info" title="sys.exc_info"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exc_info()</span></code></a>.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTrace_LINE" title="PyTrace_LINE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_LINE</span></code></a></p></td>
<td><p>Always <a class="reference internal" href="none.html#c.Py_None" title="Py_None"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_None</span></code></a>.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTrace_RETURN" title="PyTrace_RETURN"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_RETURN</span></code></a></p></td>
<td><p>Value being returned to the caller,
or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if caused by an exception.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTrace_C_CALL" title="PyTrace_C_CALL"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_CALL</span></code></a></p></td>
<td><p>Function object being called.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTrace_C_EXCEPTION" title="PyTrace_C_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_EXCEPTION</span></code></a></p></td>
<td><p>Function object being called.</p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#c.PyTrace_C_RETURN" title="PyTrace_C_RETURN"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_RETURN</span></code></a></p></td>
<td><p>Function object being called.</p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#c.PyTrace_OPCODE" title="PyTrace_OPCODE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_OPCODE</span></code></a></p></td>
<td><p>Always <a class="reference internal" href="none.html#c.Py_None" title="Py_None"><code class="xref c c-data docutils literal notranslate"><span class="pre">Py_None</span></code></a>.</p></td>
</tr>
</tbody>
</table>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_CALL">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_CALL</span></span></span><a class="headerlink" href="#c.PyTrace_CALL" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value of the <em>what</em> parameter to a <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> function when a new
call to a function or method is being reported, or a new entry into a generator.
Note that the creation of the iterator for a generator function is not reported
as there is no control transfer to the Python bytecode in the corresponding
frame.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_EXCEPTION">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_EXCEPTION</span></span></span><a class="headerlink" href="#c.PyTrace_EXCEPTION" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value of the <em>what</em> parameter to a <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> function when an
exception has been raised.  The callback function is called with this value for
<em>what</em> when after any bytecode is processed after which the exception becomes
set within the frame being executed.  The effect of this is that as exception
propagation causes the Python stack to unwind, the callback is called upon
return to each frame as the exception propagates.  Only trace functions receives
these events; they are not needed by the profiler.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_LINE">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_LINE</span></span></span><a class="headerlink" href="#c.PyTrace_LINE" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value passed as the <em>what</em> parameter to a <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> function
(but not a profiling function) when a line-number event is being reported.
It may be disabled for a frame by setting <a class="reference internal" href="../reference/datamodel.html#frame.f_trace_lines" title="frame.f_trace_lines"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_lines</span></code></a> to
<em>0</em> on that frame.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_RETURN">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_RETURN</span></span></span><a class="headerlink" href="#c.PyTrace_RETURN" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value for the <em>what</em> parameter to <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> functions when a
call is about to return.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_C_CALL">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_C_CALL</span></span></span><a class="headerlink" href="#c.PyTrace_C_CALL" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value for the <em>what</em> parameter to <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> functions when a C
function is about to be called.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_C_EXCEPTION">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_C_EXCEPTION</span></span></span><a class="headerlink" href="#c.PyTrace_C_EXCEPTION" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value for the <em>what</em> parameter to <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> functions when a C
function has raised an exception.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_C_RETURN">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_C_RETURN</span></span></span><a class="headerlink" href="#c.PyTrace_C_RETURN" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value for the <em>what</em> parameter to <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> functions when a C
function has returned.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyTrace_OPCODE">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyTrace_OPCODE</span></span></span><a class="headerlink" href="#c.PyTrace_OPCODE" title="Link to this definition">¶</a><br /></dt>
<dd><p>The value for the <em>what</em> parameter to <a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tracefunc</span></code></a> functions (but not
profiling functions) when a new opcode is about to be executed.  This event is
not emitted by default: it must be explicitly requested by setting
<a class="reference internal" href="../reference/datamodel.html#frame.f_trace_opcodes" title="frame.f_trace_opcodes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">f_trace_opcodes</span></code></a> to <em>1</em> on the frame.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_SetProfile">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_SetProfile</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><span class="n"><span class="pre">Py_tracefunc</span></span></a><span class="w"> </span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_SetProfile" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the profiler function to <em>func</em>.  The <em>obj</em> parameter is passed to the
function as its first parameter, and may be any Python object, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  If
the profile function needs to maintain state, using a different value for <em>obj</em>
for each thread provides a convenient and thread-safe place to store it.  The
profile function is called for all monitored events except <a class="reference internal" href="#c.PyTrace_LINE" title="PyTrace_LINE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_LINE</span></code></a>
<a class="reference internal" href="#c.PyTrace_OPCODE" title="PyTrace_OPCODE"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_OPCODE</span></code></a> and <a class="reference internal" href="#c.PyTrace_EXCEPTION" title="PyTrace_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_EXCEPTION</span></code></a>.</p>
<p>See also the <a class="reference internal" href="../library/sys.html#sys.setprofile" title="sys.setprofile"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.setprofile()</span></code></a> function.</p>
<p>The caller must hold the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_SetProfileAllThreads">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_SetProfileAllThreads</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><span class="n"><span class="pre">Py_tracefunc</span></span></a><span class="w"> </span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_SetProfileAllThreads" title="Link to this definition">¶</a><br /></dt>
<dd><p>Like <a class="reference internal" href="#c.PyEval_SetProfile" title="PyEval_SetProfile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetProfile()</span></code></a> but sets the profile function in all running threads
belonging to the current interpreter instead of the setting it only on the current thread.</p>
<p>The caller must hold the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>.</p>
<p>As <a class="reference internal" href="#c.PyEval_SetProfile" title="PyEval_SetProfile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetProfile()</span></code></a>, this function ignores any exceptions raised while
setting the profile functions in all threads.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_SetTrace">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_SetTrace</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><span class="n"><span class="pre">Py_tracefunc</span></span></a><span class="w"> </span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_SetTrace" title="Link to this definition">¶</a><br /></dt>
<dd><p>Set the tracing function to <em>func</em>.  This is similar to
<a class="reference internal" href="#c.PyEval_SetProfile" title="PyEval_SetProfile"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetProfile()</span></code></a>, except the tracing function does receive line-number
events and per-opcode events, but does not receive any event related to C function
objects being called.  Any trace function registered using <a class="reference internal" href="#c.PyEval_SetTrace" title="PyEval_SetTrace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetTrace()</span></code></a>
will not receive <a class="reference internal" href="#c.PyTrace_C_CALL" title="PyTrace_C_CALL"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_CALL</span></code></a>, <a class="reference internal" href="#c.PyTrace_C_EXCEPTION" title="PyTrace_C_EXCEPTION"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_EXCEPTION</span></code></a> or
<a class="reference internal" href="#c.PyTrace_C_RETURN" title="PyTrace_C_RETURN"><code class="xref c c-data docutils literal notranslate"><span class="pre">PyTrace_C_RETURN</span></code></a> as a value for the <em>what</em> parameter.</p>
<p>See also the <a class="reference internal" href="../library/sys.html#sys.settrace" title="sys.settrace"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.settrace()</span></code></a> function.</p>
<p>The caller must hold the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyEval_SetTraceAllThreads">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyEval_SetTraceAllThreads</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tracefunc" title="Py_tracefunc"><span class="n"><span class="pre">Py_tracefunc</span></span></a><span class="w"> </span><span class="n"><span class="pre">func</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">obj</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyEval_SetTraceAllThreads" title="Link to this definition">¶</a><br /></dt>
<dd><p>Like <a class="reference internal" href="#c.PyEval_SetTrace" title="PyEval_SetTrace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetTrace()</span></code></a> but sets the tracing function in all running threads
belonging to the current interpreter instead of the setting it only on the current thread.</p>
<p>The caller must hold the <a class="reference internal" href="../glossary.html#term-GIL"><span class="xref std std-term">GIL</span></a>.</p>
<p>As <a class="reference internal" href="#c.PyEval_SetTrace" title="PyEval_SetTrace"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyEval_SetTrace()</span></code></a>, this function ignores any exceptions raised while
setting the trace functions in all threads.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</section>
<section id="advanced-debugger-support">
<span id="advanced-debugging"></span><h2>Advanced Debugger Support<a class="headerlink" href="#advanced-debugger-support" title="Link to this heading">¶</a></h2>
<p>These functions are only intended to be used by advanced debugging tools.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Head">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Head</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Head" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the interpreter state object at the head of the list of all such objects.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Main">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Main</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Main" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the main interpreter state object.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_Next">
<a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_Next</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_Next" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the next interpreter state object after <em>interp</em> from the list of all
such objects.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyInterpreterState_ThreadHead">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyInterpreterState_ThreadHead</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><span class="n"><span class="pre">PyInterpreterState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">interp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyInterpreterState_ThreadHead" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the pointer to the first <a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyThreadState</span></code></a> object in the list of
threads associated with the interpreter <em>interp</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThreadState_Next">
<a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThreadState_Next</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.PyThreadState" title="PyThreadState"><span class="n"><span class="pre">PyThreadState</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">tstate</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThreadState_Next" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the next thread state object after <em>tstate</em> from the list of all such
objects belonging to the same <a class="reference internal" href="#c.PyInterpreterState" title="PyInterpreterState"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyInterpreterState</span></code></a> object.</p>
</dd></dl>

</section>
<section id="thread-local-storage-support">
<span id="thread-local-storage"></span><h2>Thread Local Storage Support<a class="headerlink" href="#thread-local-storage-support" title="Link to this heading">¶</a></h2>
<p>The Python interpreter provides low-level support for thread-local storage
(TLS) which wraps the underlying native TLS implementation to support the
Python-level thread local storage API (<a class="reference internal" href="../library/threading.html#threading.local" title="threading.local"><code class="xref py py-class docutils literal notranslate"><span class="pre">threading.local</span></code></a>).  The
CPython C level APIs are similar to those offered by pthreads and Windows:
use a thread key and functions to associate a <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> value per
thread.</p>
<p>The GIL does <em>not</em> need to be held when calling these functions; they supply
their own locking.</p>
<p>Note that <code class="file docutils literal notranslate"><span class="pre">Python.h</span></code> does not include the declaration of the TLS APIs,
you need to include <code class="file docutils literal notranslate"><span class="pre">pythread.h</span></code> to use thread-local storage.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>None of these API functions handle memory management on behalf of the
<span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> values.  You need to allocate and deallocate them yourself.
If the <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> values happen to be <span class="c-expr sig sig-inline c"><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n">PyObject</span></a><span class="p">*</span></span>, these
functions don’t do refcount operations on them either.</p>
</div>
<section id="thread-specific-storage-tss-api">
<span id="thread-specific-storage-api"></span><h3>Thread Specific Storage (TSS) API<a class="headerlink" href="#thread-specific-storage-tss-api" title="Link to this heading">¶</a></h3>
<p>TSS API is introduced to supersede the use of the existing TLS API within the
CPython interpreter.  This API uses a new type <a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tss_t</span></code></a> instead of
<span class="c-expr sig sig-inline c"><span class="kt">int</span></span> to represent thread keys.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>“A New C-API for Thread-Local Storage in CPython” (<span class="target" id="index-48"></span><a class="pep reference external" href="https://peps.python.org/pep-0539/"><strong>PEP 539</strong></a>)</p>
</div>
<dl class="c type">
<dt class="sig sig-object c" id="c.Py_tss_t">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_tss_t</span></span></span><a class="headerlink" href="#c.Py_tss_t" title="Link to this definition">¶</a><br /></dt>
<dd><p>This data structure represents the state of a thread key, the definition of
which may depend on the underlying TLS implementation, and it has an
internal field representing the key’s initialization state.  There are no
public members in this structure.</p>
<p>When <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Py_LIMITED_API</span></a> is not defined, static allocation of
this type by <a class="reference internal" href="#c.Py_tss_NEEDS_INIT" title="Py_tss_NEEDS_INIT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_tss_NEEDS_INIT</span></code></a> is allowed.</p>
</dd></dl>

<dl class="c macro">
<dt class="sig sig-object c" id="c.Py_tss_NEEDS_INIT">
<span class="sig-name descname"><span class="n"><span class="pre">Py_tss_NEEDS_INIT</span></span></span><a class="headerlink" href="#c.Py_tss_NEEDS_INIT" title="Link to this definition">¶</a><br /></dt>
<dd><p>This macro expands to the initializer for <a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tss_t</span></code></a> variables.
Note that this macro won’t be defined with <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Py_LIMITED_API</span></a>.</p>
</dd></dl>

<section id="dynamic-allocation">
<h4>Dynamic Allocation<a class="headerlink" href="#dynamic-allocation" title="Link to this heading">¶</a></h4>
<p>Dynamic allocation of the <a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tss_t</span></code></a>, required in extension modules
built with <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Py_LIMITED_API</span></a>, where static allocation of this type
is not possible due to its implementation being opaque at build time.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_alloc">
<a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_alloc</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_alloc" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return a value which is the same state as a value initialized with
<a class="reference internal" href="#c.Py_tss_NEEDS_INIT" title="Py_tss_NEEDS_INIT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_tss_NEEDS_INIT</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">NULL</span></code> in the case of dynamic allocation
failure.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_free">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_free</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_free" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Free the given <em>key</em> allocated by <a class="reference internal" href="#c.PyThread_tss_alloc" title="PyThread_tss_alloc"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_alloc()</span></code></a>, after
first calling <a class="reference internal" href="#c.PyThread_tss_delete" title="PyThread_tss_delete"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_delete()</span></code></a> to ensure any associated
thread locals have been unassigned. This is a no-op if the <em>key</em>
argument is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A freed key becomes a dangling pointer. You should reset the key to
<code class="docutils literal notranslate"><span class="pre">NULL</span></code>.</p>
</div>
</dd></dl>

</section>
<section id="methods">
<h4>Methods<a class="headerlink" href="#methods" title="Link to this heading">¶</a></h4>
<p>The parameter <em>key</em> of these functions must not be <code class="docutils literal notranslate"><span class="pre">NULL</span></code>.  Moreover, the
behaviors of <a class="reference internal" href="#c.PyThread_tss_set" title="PyThread_tss_set"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_set()</span></code></a> and <a class="reference internal" href="#c.PyThread_tss_get" title="PyThread_tss_get"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_get()</span></code></a> are
undefined if the given <a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tss_t</span></code></a> has not been initialized by
<a class="reference internal" href="#c.PyThread_tss_create" title="PyThread_tss_create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_create()</span></code></a>.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_is_created">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_is_created</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_is_created" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return a non-zero value if the given <a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_tss_t</span></code></a> has been initialized
by <a class="reference internal" href="#c.PyThread_tss_create" title="PyThread_tss_create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_create()</span></code></a>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_create">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_create</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_create" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return a zero value on successful initialization of a TSS key.  The behavior
is undefined if the value pointed to by the <em>key</em> argument is not
initialized by <a class="reference internal" href="#c.Py_tss_NEEDS_INIT" title="Py_tss_NEEDS_INIT"><code class="xref c c-macro docutils literal notranslate"><span class="pre">Py_tss_NEEDS_INIT</span></code></a>.  This function can be called
repeatedly on the same key – calling it on an already initialized key is a
no-op and immediately returns success.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_delete">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_delete</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_delete" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Destroy a TSS key to forget the values associated with the key across all
threads, and change the key’s initialization state to uninitialized.  A
destroyed key is able to be initialized again by
<a class="reference internal" href="#c.PyThread_tss_create" title="PyThread_tss_create"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_tss_create()</span></code></a>. This function can be called repeatedly on
the same key – calling it on an already destroyed key is a no-op.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_set">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_set</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span>, <span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_set" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return a zero value to indicate successfully associating a <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span>
value with a TSS key in the current thread.  Each thread has a distinct
mapping of the key to a <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> value.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_tss_get">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_tss_get</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_tss_t" title="Py_tss_t"><span class="n"><span class="pre">Py_tss_t</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_tss_get" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a> since version 3.7.</em><p>Return the <span class="c-expr sig sig-inline c"><span class="kt">void</span><span class="p">*</span></span> value associated with a TSS key in the current
thread.  This returns <code class="docutils literal notranslate"><span class="pre">NULL</span></code> if no value is associated with the key in the
current thread.</p>
</dd></dl>

</section>
</section>
<section id="thread-local-storage-tls-api">
<span id="thread-local-storage-api"></span><h3>Thread Local Storage (TLS) API<a class="headerlink" href="#thread-local-storage-tls-api" title="Link to this heading">¶</a></h3>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.7: </span>This API is superseded by
<a class="reference internal" href="#thread-specific-storage-api"><span class="std std-ref">Thread Specific Storage (TSS) API</span></a>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This version of the API does not support platforms where the native TLS key
is defined in a way that cannot be safely cast to <code class="docutils literal notranslate"><span class="pre">int</span></code>.  On such platforms,
<a class="reference internal" href="#c.PyThread_create_key" title="PyThread_create_key"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyThread_create_key()</span></code></a> will return immediately with a failure status,
and the other TLS functions will all be no-ops on such platforms.</p>
</div>
<p>Due to the compatibility problem noted above, this version of the API should not
be used in new code.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_create_key">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_create_key</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_create_key" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_delete_key">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_delete_key</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_delete_key" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_set_key_value">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_set_key_value</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">key</span></span>, <span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">value</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_set_key_value" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_get_key_value">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_get_key_value</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_get_key_value" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_delete_key_value">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_delete_key_value</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">key</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_delete_key_value" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyThread_ReInitTLS">
<span class="kt"><span class="pre">void</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyThread_ReInitTLS</span></span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyThread_ReInitTLS" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em></dd></dl>

</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Initialization, Finalization, and Threads</a><ul>
<li><a class="reference internal" href="#before-python-initialization">Before Python Initialization</a></li>
<li><a class="reference internal" href="#global-configuration-variables">Global configuration variables</a></li>
<li><a class="reference internal" href="#initializing-and-finalizing-the-interpreter">Initializing and finalizing the interpreter</a></li>
<li><a class="reference internal" href="#process-wide-parameters">Process-wide parameters</a></li>
<li><a class="reference internal" href="#thread-state-and-the-global-interpreter-lock">Thread State and the Global Interpreter Lock</a><ul>
<li><a class="reference internal" href="#releasing-the-gil-from-extension-code">Releasing the GIL from extension code</a></li>
<li><a class="reference internal" href="#non-python-created-threads">Non-Python created threads</a></li>
<li><a class="reference internal" href="#cautions-about-fork">Cautions about fork()</a></li>
<li><a class="reference internal" href="#high-level-api">High-level API</a></li>
<li><a class="reference internal" href="#low-level-api">Low-level API</a></li>
</ul>
</li>
<li><a class="reference internal" href="#sub-interpreter-support">Sub-interpreter support</a><ul>
<li><a class="reference internal" href="#a-per-interpreter-gil">A Per-Interpreter GIL</a></li>
<li><a class="reference internal" href="#bugs-and-caveats">Bugs and caveats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#asynchronous-notifications">Asynchronous Notifications</a></li>
<li><a class="reference internal" href="#profiling-and-tracing">Profiling and Tracing</a></li>
<li><a class="reference internal" href="#advanced-debugger-support">Advanced Debugger Support</a></li>
<li><a class="reference internal" href="#thread-local-storage-support">Thread Local Storage Support</a><ul>
<li><a class="reference internal" href="#thread-specific-storage-tss-api">Thread Specific Storage (TSS) API</a><ul>
<li><a class="reference internal" href="#dynamic-allocation">Dynamic Allocation</a></li>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#thread-local-storage-tls-api">Thread Local Storage (TLS) API</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="typehints.html"
                          title="previous chapter">Objects for Type Hinting</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="init_config.html"
                          title="next chapter">Python Initialization Configuration</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/init.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="init_config.html" title="Python Initialization Configuration"
             >next</a> |</li>
        <li class="right" >
          <a href="typehints.html" title="Objects for Type Hinting"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Initialization, Finalization, and Threads</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>