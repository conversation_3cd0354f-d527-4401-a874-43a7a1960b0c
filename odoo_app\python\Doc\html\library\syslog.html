<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="syslog — Unix syslog library routines" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/syslog.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides an interface to the Unix syslog library routines. Refer to the Unix manual pages for a detailed description of the syslog facility. Availability: Unix, not Emscripten, not WASI..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides an interface to the Unix syslog library routines. Refer to the Unix manual pages for a detailed description of the syslog facility. Availability: Unix, not Emscripten, not WASI..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>syslog — Unix syslog library routines &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Modules command-line interface (CLI)" href="cmdline.html" />
    <link rel="prev" title="resource — Resource usage information" href="resource.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/syslog.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a><ul>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#simple-example">Simple example</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="resource.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmdline.html"
                          title="next chapter">Modules command-line interface (CLI)</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/syslog.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmdline.html" title="Modules command-line interface (CLI)"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="resource.html" title="resource — Resource usage information"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" accesskey="U">Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-syslog">
<span id="syslog-unix-syslog-library-routines"></span><h1><a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code></a> — Unix syslog library routines<a class="headerlink" href="#module-syslog" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides an interface to the Unix <code class="docutils literal notranslate"><span class="pre">syslog</span></code> library routines.
Refer to the Unix manual pages for a detailed description of the <code class="docutils literal notranslate"><span class="pre">syslog</span></code>
facility.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix, not Emscripten, not WASI.</p>
</div>
<p>This module wraps the system <code class="docutils literal notranslate"><span class="pre">syslog</span></code> family of routines.  A pure Python
library that can speak to a syslog server is available in the
<a class="reference internal" href="logging.handlers.html#module-logging.handlers" title="logging.handlers: Handlers for the logging module."><code class="xref py py-mod docutils literal notranslate"><span class="pre">logging.handlers</span></code></a> module as <code class="xref py py-class docutils literal notranslate"><span class="pre">SysLogHandler</span></code>.</p>
<p>The module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="syslog.syslog">
<span class="sig-prename descclassname"><span class="pre">syslog.</span></span><span class="sig-name descname"><span class="pre">syslog</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#syslog.syslog" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">syslog.</span></span><span class="sig-name descname"><span class="pre">syslog</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">priority</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span></dt>
<dd><p>Send the string <em>message</em> to the system logger.  A trailing newline is added
if necessary.  Each message is tagged with a priority composed of a
<em>facility</em> and a <em>level</em>.  The optional <em>priority</em> argument, which defaults
to <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_INFO</span></code>, determines the message priority.  If the facility is
not encoded in <em>priority</em> using logical-or (<code class="docutils literal notranslate"><span class="pre">LOG_INFO</span> <span class="pre">|</span> <span class="pre">LOG_USER</span></code>), the
value given in the <a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> call is used.</p>
<p>If <a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> has not been called prior to the call to <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a>,
<a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> will be called with no arguments.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">syslog.syslog</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">priority</span></code>, <code class="docutils literal notranslate"><span class="pre">message</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>In previous versions, <a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> would not be called automatically if
it wasn’t called prior to the call to <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a>, deferring to the syslog
implementation to call <code class="docutils literal notranslate"><span class="pre">openlog()</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This function is restricted in subinterpreters.
(Only code that runs in multiple interpreters is affected and
the restriction is not relevant for most users.)
<a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> must be called in the main interpreter before <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a> may be used
in a subinterpreter.  Otherwise it will raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="syslog.openlog">
<span class="sig-prename descclassname"><span class="pre">syslog.</span></span><span class="sig-name descname"><span class="pre">openlog</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">ident</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">logoption</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">facility</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#syslog.openlog" title="Link to this definition">¶</a></dt>
<dd><p>Logging options of subsequent <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a> calls can be set by calling
<a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a>.  <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a> will call <a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> with no arguments
if the log is not currently open.</p>
<p>The optional <em>ident</em> keyword argument is a string which is prepended to every
message, and defaults to <code class="docutils literal notranslate"><span class="pre">sys.argv[0]</span></code> with leading path components
stripped.  The optional <em>logoption</em> keyword argument (default is 0) is a bit
field – see below for possible values to combine.  The optional <em>facility</em>
keyword argument (default is <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_USER</span></code>) sets the default facility for
messages which do not have a facility explicitly encoded.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">syslog.openlog</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">ident</span></code>, <code class="docutils literal notranslate"><span class="pre">logoption</span></code>, <code class="docutils literal notranslate"><span class="pre">facility</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>In previous versions, keyword arguments were not allowed, and <em>ident</em> was
required.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This function is restricted in subinterpreters.
(Only code that runs in multiple interpreters is affected and
the restriction is not relevant for most users.)
This may only be called in the main interpreter.
It will raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> if called in a subinterpreter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="syslog.closelog">
<span class="sig-prename descclassname"><span class="pre">syslog.</span></span><span class="sig-name descname"><span class="pre">closelog</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#syslog.closelog" title="Link to this definition">¶</a></dt>
<dd><p>Reset the syslog module values and call the system library <code class="docutils literal notranslate"><span class="pre">closelog()</span></code>.</p>
<p>This causes the module to behave as it does when initially imported.  For
example, <a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> will be called on the first <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a> call (if
<a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> hasn’t already been called), and <em>ident</em> and other
<a class="reference internal" href="#syslog.openlog" title="syslog.openlog"><code class="xref py py-func docutils literal notranslate"><span class="pre">openlog()</span></code></a> parameters are reset to defaults.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">syslog.closelog</span></code> with no arguments.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>This function is restricted in subinterpreters.
(Only code that runs in multiple interpreters is affected and
the restriction is not relevant for most users.)
This may only be called in the main interpreter.
It will raise <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> if called in a subinterpreter.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="syslog.setlogmask">
<span class="sig-prename descclassname"><span class="pre">syslog.</span></span><span class="sig-name descname"><span class="pre">setlogmask</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">maskpri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#syslog.setlogmask" title="Link to this definition">¶</a></dt>
<dd><p>Set the priority mask to <em>maskpri</em> and return the previous mask value.  Calls
to <a class="reference internal" href="#module-syslog" title="syslog: An interface to the Unix syslog library routines. (Unix)"><code class="xref py py-func docutils literal notranslate"><span class="pre">syslog()</span></code></a> with a priority level not set in <em>maskpri</em> are ignored.
The default is to log all priorities.  The function <code class="docutils literal notranslate"><span class="pre">LOG_MASK(pri)</span></code>
calculates the mask for the individual priority <em>pri</em>.  The function
<code class="docutils literal notranslate"><span class="pre">LOG_UPTO(pri)</span></code> calculates the mask for all priorities up to and including
<em>pri</em>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">syslog.setlogmask</span></code> with argument <code class="docutils literal notranslate"><span class="pre">maskpri</span></code>.</p>
</dd></dl>

<p>The module defines the following constants:</p>
<dl class="simple">
<dt>Priority levels (high to low):</dt><dd><p><code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_EMERG</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_ALERT</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_CRIT</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_ERR</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_WARNING</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_NOTICE</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_INFO</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_DEBUG</span></code>.</p>
</dd>
<dt>Facilities:</dt><dd><p><code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_KERN</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_USER</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_MAIL</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_DAEMON</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_AUTH</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_LPR</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_NEWS</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_UUCP</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_CRON</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_SYSLOG</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_LOCAL0</span></code> to
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_LOCAL7</span></code>, and, if defined in <code class="docutils literal notranslate"><span class="pre">&lt;syslog.h&gt;</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_AUTHPRIV</span></code>.</p>
</dd>
<dt>Log options:</dt><dd><p><code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_PID</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_CONS</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_NDELAY</span></code>, and, if defined
in <code class="docutils literal notranslate"><span class="pre">&lt;syslog.h&gt;</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_ODELAY</span></code>, <code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_NOWAIT</span></code>, and
<code class="xref py py-const docutils literal notranslate"><span class="pre">LOG_PERROR</span></code>.</p>
</dd>
</dl>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<section id="simple-example">
<h3>Simple example<a class="headerlink" href="#simple-example" title="Link to this heading">¶</a></h3>
<p>A simple set of examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">syslog</span>

<span class="n">syslog</span><span class="o">.</span><span class="n">syslog</span><span class="p">(</span><span class="s1">&#39;Processing started&#39;</span><span class="p">)</span>
<span class="k">if</span> <span class="n">error</span><span class="p">:</span>
    <span class="n">syslog</span><span class="o">.</span><span class="n">syslog</span><span class="p">(</span><span class="n">syslog</span><span class="o">.</span><span class="n">LOG_ERR</span><span class="p">,</span> <span class="s1">&#39;Processing started&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>An example of setting some log options, these would include the process ID in
logged messages, and write the messages to the destination facility used for
mail logging:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">syslog</span><span class="o">.</span><span class="n">openlog</span><span class="p">(</span><span class="n">logoption</span><span class="o">=</span><span class="n">syslog</span><span class="o">.</span><span class="n">LOG_PID</span><span class="p">,</span> <span class="n">facility</span><span class="o">=</span><span class="n">syslog</span><span class="o">.</span><span class="n">LOG_MAIL</span><span class="p">)</span>
<span class="n">syslog</span><span class="o">.</span><span class="n">syslog</span><span class="p">(</span><span class="s1">&#39;E-mail processing initiated...&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a><ul>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#simple-example">Simple example</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="resource.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">resource</span></code> — Resource usage information</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmdline.html"
                          title="next chapter">Modules command-line interface (CLI)</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/syslog.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmdline.html" title="Modules command-line interface (CLI)"
             >next</a> |</li>
        <li class="right" >
          <a href="resource.html" title="resource — Resource usage information"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="unix.html" >Unix Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">syslog</span></code> — Unix syslog library routines</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>