<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Data Persistence" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/persistence.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The modules described in this chapter support storing Python data in a persistent form on disk. The pickle and marshal modules can turn many Python data types into a stream of bytes and then recrea..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The modules described in this chapter support storing Python data in a persistent form on disk. The pickle and marshal modules can turn many Python data types into a stream of bytes and then recrea..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Data Persistence &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="pickle — Python object serialization" href="pickle.html" />
    <link rel="prev" title="shutil — High-level file operations" href="shutil.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/persistence.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="shutil.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pickle.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code> — Python object serialization</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/persistence.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pickle.html" title="pickle — Python object serialization"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="shutil.html" title="shutil — High-level file operations"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Data Persistence</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="data-persistence">
<span id="persistence"></span><h1>Data Persistence<a class="headerlink" href="#data-persistence" title="Link to this heading">¶</a></h1>
<p>The modules described in this chapter support storing Python data in a
persistent form on disk.  The <a class="reference internal" href="pickle.html#module-pickle" title="pickle: Convert Python objects to streams of bytes and back."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code></a> and <a class="reference internal" href="marshal.html#module-marshal" title="marshal: Convert Python objects to streams of bytes and back (with different constraints)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">marshal</span></code></a> modules can turn
many Python data types into a stream of bytes and then recreate the objects from
the bytes.  The various DBM-related modules support a family of hash-based file
formats that store a mapping of strings to other strings.</p>
<p>The list of modules described in this chapter is:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="pickle.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code> — Python object serialization</a><ul>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#relationship-to-other-python-modules">Relationship to other Python modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#comparison-with-marshal">Comparison with <code class="docutils literal notranslate"><span class="pre">marshal</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#comparison-with-json">Comparison with <code class="docutils literal notranslate"><span class="pre">json</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#data-stream-format">Data stream format</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#module-interface">Module Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#what-can-be-pickled-and-unpickled">What can be pickled and unpickled?</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#pickling-class-instances">Pickling Class Instances</a><ul>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#persistence-of-external-objects">Persistence of External Objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#dispatch-tables">Dispatch Tables</a></li>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#handling-stateful-objects">Handling Stateful Objects</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#custom-reduction-for-types-functions-and-other-objects">Custom Reduction for Types, Functions, and Other Objects</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#out-of-band-buffers">Out-of-band Buffers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#provider-api">Provider API</a></li>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#consumer-api">Consumer API</a></li>
<li class="toctree-l3"><a class="reference internal" href="pickle.html#example">Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#restricting-globals">Restricting Globals</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#performance">Performance</a></li>
<li class="toctree-l2"><a class="reference internal" href="pickle.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="copyreg.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">copyreg</span></code> — Register <code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code> support functions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="copyreg.html#example">Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="shelve.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shelve</span></code> — Python object persistence</a><ul>
<li class="toctree-l2"><a class="reference internal" href="shelve.html#restrictions">Restrictions</a></li>
<li class="toctree-l2"><a class="reference internal" href="shelve.html#example">Example</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="marshal.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">marshal</span></code> — Internal Python object serialization</a></li>
<li class="toctree-l1"><a class="reference internal" href="dbm.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm</span></code> — Interfaces to Unix “databases”</a><ul>
<li class="toctree-l2"><a class="reference internal" href="dbm.html#module-dbm.gnu"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm.gnu</span></code> — GNU database manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="dbm.html#module-dbm.ndbm"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm.ndbm</span></code> — New Database Manager</a></li>
<li class="toctree-l2"><a class="reference internal" href="dbm.html#module-dbm.dumb"><code class="xref py py-mod docutils literal notranslate"><span class="pre">dbm.dumb</span></code> — Portable DBM implementation</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="sqlite3.html"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sqlite3</span></code> — DB-API 2.0 interface for SQLite databases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="sqlite3.html#tutorial">Tutorial</a></li>
<li class="toctree-l2"><a class="reference internal" href="sqlite3.html#reference">Reference</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#module-functions">Module functions</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#module-constants">Module constants</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#connection-objects">Connection objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#cursor-objects">Cursor objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#row-objects">Row objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#blob-objects">Blob objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#prepareprotocol-objects">PrepareProtocol objects</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#exceptions">Exceptions</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#sqlite-and-python-types">SQLite and Python types</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#default-adapters-and-converters-deprecated">Default adapters and converters (deprecated)</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#command-line-interface">Command-line interface</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sqlite3.html#how-to-guides">How-to guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-use-placeholders-to-bind-values-in-sql-queries">How to use placeholders to bind values in SQL queries</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-adapt-custom-python-types-to-sqlite-values">How to adapt custom Python types to SQLite values</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sqlite3.html#how-to-write-adaptable-objects">How to write adaptable objects</a></li>
<li class="toctree-l4"><a class="reference internal" href="sqlite3.html#how-to-register-adapter-callables">How to register adapter callables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-convert-sqlite-values-to-custom-python-types">How to convert SQLite values to custom Python types</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#adapter-and-converter-recipes">Adapter and converter recipes</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-use-connection-shortcut-methods">How to use connection shortcut methods</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-use-the-connection-context-manager">How to use the connection context manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-work-with-sqlite-uris">How to work with SQLite URIs</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-create-and-use-row-factories">How to create and use row factories</a></li>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#how-to-handle-non-utf-8-text-encodings">How to handle non-UTF-8 text encodings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="sqlite3.html#explanation">Explanation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="sqlite3.html#transaction-control">Transaction control</a><ul>
<li class="toctree-l4"><a class="reference internal" href="sqlite3.html#transaction-control-via-the-autocommit-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">autocommit</span></code> attribute</a></li>
<li class="toctree-l4"><a class="reference internal" href="sqlite3.html#transaction-control-via-the-isolation-level-attribute">Transaction control via the <code class="docutils literal notranslate"><span class="pre">isolation_level</span></code> attribute</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="shutil.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">shutil</span></code> — High-level file operations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="pickle.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pickle</span></code> — Python object serialization</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/persistence.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="pickle.html" title="pickle — Python object serialization"
             >next</a> |</li>
        <li class="right" >
          <a href="shutil.html" title="shutil — High-level file operations"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Data Persistence</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>