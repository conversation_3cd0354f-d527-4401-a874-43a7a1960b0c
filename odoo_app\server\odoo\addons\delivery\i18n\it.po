# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "%(carrier)s Error"
msgstr "%(carrier)s Errore"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "%(old_name)s (copy)"
msgstr "%(old_name)s (copia)"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Get rate"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Calcola tariffa"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    Per gestire più corrieri acquistare Odoo Enterprise.\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"o_stat_text o_warning_text fw-bold\">Test</span>\n"
"                                <span class=\"o_stat_text\">ambiente</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"o_stat_text text-danger\">No debug</span>"
msgstr "<span class=\"o_stat_text text-danger\">Nessun debug</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Richieste debug</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                <span class=\"o_stat_text\">Environment</span>"
msgstr ""
"<span class=\"text-success\">Ambiente di</span>\n"
"                                    <span class=\"o_stat_text\">produzione</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_report_saleorder_document
msgid "<strong>Shipping Description:</strong>"
msgstr "<strong>Descrizione spedizione:</strong>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__carrier_description
msgid ""
"A description of the delivery method that you want to communicate to your "
"customers on the Sales Order and sales confirmation email.E.g. instructions "
"for customers to follow."
msgstr ""
"Descrizione del metodo di consegna che vuoi fornire ai clienti nell'ordine "
"di vendita e nell'e-mail di conferma delle vendite. Ad es. Istruzioni da "
"seguire per il cliente."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Azione alla conferma degli ordini di consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Attivo"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Aggiungi"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Add a shipping method"
msgstr "Aggiungi metodo di spedizione"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Aggiungi spedizione"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Additional margin"
msgstr "Margine aggiuntivo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Importo"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Importo dell'ordine per beneficiare della spedizione gratuita, espresso "
"nella valuta dell'azienda"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "In archivio"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Vettori disponibili"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "Basato su regole"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Può generare un reso"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Vettore"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__carrier_description
msgid "Carrier Description"
msgstr "Descrizione vettore"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Azienda"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Condizione"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Costo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Nazioni"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__currency_id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Registrazione richieste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Metodo di consegna predefinito utilizzato negli ordini di vendita."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Definire un nuovo metodo di consegna"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Vettore di consegna"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Procedura di selezione vettore di consegna"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Costo di consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Messaggio per la consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "Metodo di consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Prezzo di consegna"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Regole per le tariffe di consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Prodotto «Consegna»"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr "Indice di successo consegne"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Gruppo di consegne"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_zip_prefix
msgid "Delivery Zip Prefix"
msgstr "Codice postale consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "Costi di spedizione da ricalcolare"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid ""
"Delivery zip prefixes are assigned to delivery carriers to restrict\n"
"            which zips it is available to."
msgstr ""
"I codici postali per la consegna vengono assegnati ai corrieri per ridurre il numero di\n"
"                codici per i quali sono disponibili."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Description"
msgstr "Descrizione"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr "Disponibilità destinazione"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Determina l'ordine di visualizzazione"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Discard"
msgstr "Abbandona"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"            UPS Express, UPS Standard) with a set of pricing rules attached\n"
"            to each method."
msgstr ""
"Ogni vettore (ad es. UPS) può avere diversi metodi di consegna \n"
"                (ad es UPS Express, UPS Standard) con un insieme di tariffe legate\n"
"                a ogni metodo."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Ambiente"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr ""
"Errore: questo metodo di consegna non è disponibile per questo indirizzo."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "Error: this delivery method is not available."
msgstr "Errore questo metodo di consegna non è disponibile."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Costo stimato: al cliente verrà fatturato il costo stimato della spedizione.\n"
"Costo reale: al cliente verrà fatturato il costo reale della spedizione, il costo della spedizione verrà aggiornato sull'ordine di acquisto in seguito alla consegna."

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Costo stimato"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""
"Compilare questo campo se la spedizione verrà fatturata in base al prelievo."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""
"La compilazione del modulo permette di filtrare i vettori in base "
"all'indirizzo di consegna del cliente."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_margin
msgid "Fixed Margin"
msgstr "Margine fisso"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Prezzo fisso"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping"
msgstr "Spedizione gratuita"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratuito se ordine con importo maggiore di"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Genera etichetta di reso"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Calcolare tariffa"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Calcolare tariffa e creare spedizione"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Se l'importo totale dell'ordine (spedizione esclusa) è maggiore o uguale a "
"questo valore, il cliente beneficia della spedizione gratuita"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Aggiungere altri corrieri"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__shipping_insurance
msgid "Insurance Percentage"
msgstr "Percentuale di assicurazione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Livello di integrazione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Messaggio per fatturazione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Politica di fatturazione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "È una consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_local_delivery
#: model:product.template,name:delivery.product_product_local_delivery_product_template
msgid "Local Delivery"
msgstr "Consegna locale"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Registrare le richieste per facilitare la risoluzione errori"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_zip_prefix_list
msgid "Manage delivery zip prefixes"
msgstr "Gestisci i codici postali di consegna"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Margine"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "Il margine non può essere inferiore a -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Margine sulla tariffa"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Valore massimo"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Nome"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "New Providers"
msgstr "Nuovi fornitori"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "Not available for current order"
msgstr "Non disponibile per l'ordine corrente"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operatore"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Ordine"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Please select a country before choosing a state or a zip prefix."
msgstr "Seleziona un Paese prima di scegliere un codice postale."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_zip_prefix__name
msgid "Prefix"
msgstr "Prefisso"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_zip_prefix_name_uniq
msgid "Prefix already exists!"
msgstr "Prefisso già esistente!"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__zip_prefix_ids
msgid ""
"Prefixes of zip codes that this carrier applies to. Note that regular "
"expressions can be used to support countries with varying zip code lengths, "
"i.e. '$' can be added to end of prefix to match the exact zip (e.g. '100$' "
"will only match '100' and not '1000')"
msgstr ""
"Codici postali a cui si applica il vettore. Nota che possono essere "
"utilizzate espressioni regolare per supportare le nazioni con codici postali"
" con varie lunghezze, ad es. \"$\" può essere aggiunto alla fine di un "
"prefisso per individuare il codice giusto (ad es. \"100$\" ndividuerà "
"\"100\" e non \"1000\")"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Prezzo"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Regole tariffarie"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Definizione prezzi"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Regole tariffarie"

#. module: delivery
#: model:ir.model,name:delivery.model_product_category
msgid "Product Category"
msgstr "Categoria prodotto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Q.tà Prodotto"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Corriere"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Quantità"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Etichetta di reso accessibile dal portale clienti"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Prezzo base di vendita"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
msgid "Sale Price"
msgstr "Prezzo di vendita"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Prodotto servizio"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Impostare a «True» se le credenziali sono certificate per la produzione."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Shipping Method"
msgstr "Metodo di spedizione"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Metodi di spedizione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__shipping_weight
msgid "Shipping Weight"
msgstr "Peso spedizione"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__shipping_insurance
msgid ""
"Shipping insurance is a service which may reimburse senders whose parcels "
"are lost, stolen, and/or damaged in transit."
msgstr ""
"L'assicurazione sulla spedizione è un servizio che può rimborsare i mittenti"
" i cui pacchi vengono persi, rubati e/o danneggiati durante il trasporto."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Shipping method details to be included at bottom sales orders and their "
"confirmation emails. E.g. Instructions for customers to follow."
msgstr ""
"Dettagli del metodo di spedizione da inserire nella parte finale degli "
"ordini di vendita ed e-mail di conferma. Ad es. istruzioni da seguire per il"
" cliente."

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Standard delivery"
msgstr "Consegna standard"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Stati/Province"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__supports_shipping_insurance
msgid "Supports Shipping Insurance"
msgstr "Supporta l'assicurazione sulla spedizione"

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "La Poste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr "Il cliente può scaricare l'etichetta di reso dal portale clienti."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "L'etichetta di reso viene generata automaticamente alla consegna."

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_shipping_insurance_is_percentage
msgid "The shipping insurance must be a percentage between 0 and 100."
msgstr ""
"L'assicurazione di spedizione deve essere una percentuale compresa tra 0 e "
"100."

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "Spedizione gratuita, l'importo dell'ordine è superiore a %.2f."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"            according to your settings; on the sales order (based on the\n"
"            quotation) or the invoice (based on the delivery orders)."
msgstr ""
"Questi metodi consentono il calcolo automatico del prezzo di consegna,\n"
"                secondo le impostazioni dell'ordine di vendita (basato sul\n"
"                preventivo) o della fattura (basato sugli ordini di consegna)."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__fixed_margin
msgid "This fixed amount will be added to the shipping price."
msgstr "Questo importo fisso verrà aggiunto al prezzo della spedizione."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Questa percentuale verrà aggiunta al prezzo di spedizione."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__total_weight
msgid "Total Order Weight"
msgstr "Peso totale ordine"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Aggiorna"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
#, python-format
msgid "Update shipping cost"
msgstr "Aggiorna costo di spedizione"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variabile"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Fattore variabile"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volume"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
msgid "Weight"
msgstr "Peso"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Peso x Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__weight_uom_name
msgid "Weight Uom Name"
msgstr "Nome UDM peso"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"Impossibile aggiornare i costi di spedizione di un ordine già fatturato.\n"
"\n"
"Le seguenti righe di spedizione (prodotto, quantità fatturata e prezzo) sono già state elaborate:\n"
"\n"

#. module: delivery
#. odoo-python
#: code:addons/delivery/models/product_category.py:0
#, python-format
msgid ""
"You cannot delete the deliveries product category as it is used on the "
"delivery carriers products."
msgstr ""
"Non è possibile eliminare la categoria di prodotto consegne perché "
"utilizzata per i prodotti dei corrieri."

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_zip_prefix_list
msgid "Zip Prefix"
msgstr "Codici postali"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_prefix_ids
msgid "Zip Prefixes"
msgstr "Codici postali"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "es. UPS Express"
