# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <mr.roya<PERSON>@gmail.com>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <y.shadman<PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Faraz <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <maheshm<PERSON><PERSON><PERSON>.<EMAIL>>, 2024
# Dynamic Business Group, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\"> هیچ نقطه عطفی یافت نشد. بیایید "
"یکی ایجاد کنیم! </p><p> پیشرفت‌های اصلی را پیگیری کنید که باید برای رسیدن به"
" موفقیت به دست آیند. </p>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid " - Tasks by Deadline"
msgstr "- وظایف براساس مهلت پایانی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "#همکاران"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "رتبه دهی"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "تعداد وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__done_task_count
msgid "# of Done Tasks"
msgstr "تعداد وظایف انجام شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
#, python-format
msgid "# of Tasks"
msgstr "تعداد کارها"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count_reached.value} نقطه عطف از "
"#{record.milestone_count.value} نقطه عطف به دست آمده است"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr "#{task.stage_id.name}"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s / %(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s"
msgstr "%(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Burndown Chart"
msgstr "نمودار سوختن %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Milestones"
msgstr "مراحل مهم %(name)s's"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Rating"
msgstr "رتبه‌بندی %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Tasks Analysis"
msgstr "تجزیه و تحلیل وظایف %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "%(name)s's Updates"
msgstr "به‌روزرسانی‌های %(name)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (کپی)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(تا"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(آخرین آپدیت پروژه),"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "دست یافته در - "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>بکشید &amp; رها کنید</b>کارت را برای تغییر مرحله."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""
"<b>ثبت یادداشت‌ها</b> برای ارتباطات داخلی <i>(افرادی که این وظیفه را دنبال "
"می‌کنند از یادداشتی که وارد می‌کنید مطلع نخواهند شد مگر اینکه به‌صورت خاص "
"آن‌ها را تگ کنید)</i>. از @ <b>منشن</b> برای جلب توجه یک همکار یا # "
"<b>منشن</b> برای رسیدن به تمامی اعضای تیم استفاده کنید."

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"></t>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"></t>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br><br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br><br>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\">\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\">\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div> <t t-set=\"access_token\" "
"t-value=\"object._rating_get_access_token()\"></t> <t t-set=\"partner\" "
"t-value=\"object._rating_get_partner()\"></t> <table border=\"0\" "
"cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; "
"margin:0px auto;\"> <tbody> <tr><td valign=\"top\" style=\"font-size: "
"13px;\"> <t t-if=\"partner.name\"> سلام <t t-out=\"partner.name or "
"''\">براندون فریمن</t>,<br><br> </t> <t t-else=\"\"> سلام,<br><br> </t> "
"لطفاً لحظه‌ای برای ارزیابی خدمات ما در رابطه با <strong t-out=\"object.name "
"or ''\">برنامه‌ریزی و بودجه</strong> بردارید <t "
"t-if=\"object._rating_get_operator().name\"> که به <strong "
"t-out=\"object._rating_get_operator().name or ''\">میتچل ادمین</strong> "
"اختصاص داده شده است.<br> </t> <t t-else=\"\"> .<br> </t> </td></tr> <tr><td "
"style=\"text-align: center;\"> <table border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" "
"style=\"width:100%; margin: 32px 0px 32px 0px;\"> <tr><td style=\"font-size:"
" 13px;\"> <strong>بگویید که چه احساسی نسبت به خدمات ما دارید</strong><br> "
"<span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(روی یکی از "
"این شکلک‌ها کلیک کنید)</span> </td></tr> <tr><td style=\"font-size: 13px;\">"
" <table style=\"width:100%;text-align:center;margin-top:2rem;\"> <tr> <td> "
"<a t-attf-href=\"/rate/{{ access_token }}/5\"> <img alt=\"راضی\" "
"src=\"/rating/static/src/img/rating_5.png\" title=\"راضی\"> </a> </td> <td> "
"<a t-attf-href=\"/rate/{{ access_token }}/3\"> <img alt=\"متوسط\" "
"src=\"/rating/static/src/img/rating_3.png\" title=\"متوسط\"> </a> </td> <td>"
" <a t-attf-href=\"/rate/{{ access_token }}/1\"> <img alt=\"ناراضی\" "
"src=\"/rating/static/src/img/rating_1.png\" title=\"ناراضی\"> </a> </td> "
"</tr> </table> </td></tr> </table> </td></tr> <tr><td valign=\"top\" "
"style=\"font-size: 13px;\"> ما از بازخورد شما قدردانی می‌کنیم. این به ما کمک"
" می‌کند تا به‌طور مداوم بهبود یابیم. <t "
"t-if=\"object.project_id.rating_status == 'stage'\"> <br><span "
"style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">این "
"پرسشنامه رضایت به دلیل انتقال وظیفه شما به مرحله <b "
"t-out=\"object.stage_id.name or ''\">در حال انجام</b> ارسال شده است</span> "
"</t> <t t-if=\"object.project_id.rating_status == 'periodic'\"> <br><span "
"style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">این "
"پرسشنامه رضایت <b t-out=\"object.project_id.rating_status_period or "
"''\">هفتگی</b> ارسال می‌شود تا زمانی که وظیفه در مرحله <b "
"t-out=\"object.stage_id.name or ''\">در حال انجام</b> باشد.</span> </t> "
"</td></tr> <tr><td><br>با احترام,</td></tr> <tr><td> <t "
"t-out=\"object.project_id.company_id.name or ''\">شرکت شما</t> </td></tr> "
"<tr><td style=\"opacity: 0.5;\"> <t "
"t-out=\"object.project_id.company_id.phone or ''\">**************</t> <t "
"t-if=\"object.project_id.company_id.email\"> | <a t-attf-href=\"mailto:{{ "
"object.project_id.company_id.email }}\" style=\"text-decoration:none; color:"
" #454748;\" t-out=\"object.project_id.company_id.email or "
"''\"><EMAIL></a> </t> <t "
"t-if=\"object.project_id.company_id.website\"> | <a t-attf-href=\"{{ "
"object.project_id.company_id.website }}\" style=\"text-decoration:none; "
"color: #454748;\" t-out=\"object.project_id.company_id.website or "
"''\">http://www.example.com</a> </t> </td></tr> </tbody> </table> </div>"

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div> <ت t-out=\"object.partner_id.name or 'customer'\">براندون فریمن</ت> "
"عزیز،<br> با کمال مسرت به اطلاع شما می‌رسانم که ما پروژه \"<strong "
"t-out=\"object.name or ''\">نوسازی</strong>\" را با موفقیت به اتمام رساندیم."
" <ت t-if=\"user.signature\"> <br> <ت t-out=\"user.signature or "
"''\">--<br>میتچل ادمین</ت> </ت> </div> <br><span style=\"margin: 0px 0px 0px"
" 0px; font-size: 12px; opacity: 0.5; color: #454748;\" "
"groups=\"project.group_project_stages\">شما این ایمیل را دریافت می‌کنید زیرا"
" پروژه شما به مرحله <b t-out=\"object.stage_id.name or ''\">انجام شده</b> "
"منتقل شده است.</span>"

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br>\n"
"    Thank you for your enquiry.<br>\n"
"    If you have any questions, please let us know.\n"
"    <br><br>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br>\n"
"        <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div> عزیز <t t-out=\"object.partner_id.name or 'customer'\">براندون "
"فریمن</t>,<br> از بابت استعلام شما سپاسگزاریم.<br> اگر سوالی دارید، لطفاً به"
" ما اطلاع دهید. <br><br> متشکرم, <t t-if=\"user.signature\"> <br> <t "
"t-out=\"user.signature or ''\">--<br>میتچل ادمین</t> </t> </div>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" "
"invisible=\"rating_avg &lt; 3.66\" title=\"راضی\"/> <i class=\"fa fa-fw "
"o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or "
"rating_avg &gt;= 3.66\" title=\"خوب\"/> <i class=\"fa fa-fw o_button_icon "
"fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" "
"title=\"ناراضی\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> خصوصی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"مشتری در پروژه‌ها غیرفعال است\"/><b> "
"رتبه‌بندی مشتری</b> در پروژه(های) زیر غیرفعال است : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>بازگشت به حالت ویرایش"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr "<i title=\"وظیفه خصوصی\" class=\"fa fa-lock\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">مرحله:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\">تخصیص‌گیرندگان</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">مشتری</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"تاریخ‌ها\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"نام مستعار دامنه\" "
"title=\"نام مستعار دامنه\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"شریک\" title=\"شریک\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> انجام شد</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\"> وظایف</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Closed</span>"
msgstr "<span class=\"o_stat_text order-2\">بسته شده</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text order-2\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text order-2\">زیر وظایف</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                     همکاران\n"
"                                 </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">آخرین امتیازدهی</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">وظیفه والد</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr ""
"<span class=\"text-muted o_row ps-1 pb-3\">ارسال درخواست رتبه‌بندی:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\"> <i class=\"fa fa-"
"lightbulb-o pe-2\"/> <span invisible=\"rating_status == 'periodic'\">به محض "
"اینکه کار به مرحله ای برسد که قالب ایمیل امتیازدهی تعریف شده باشد، درخواست "
"امتیازدهی ارسال خواهد شد.</span> <span invisible=\"rating_status == "
"'stage'\">درخواست‌های امتیازدهی تا زمانی که کار در مرحله‌ای باشد که قالب "
"ایمیل امتیازدهی تعریف شده است، ارسال می‌شوند.</span> </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>گزارش</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>نمایش</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">پیوستها</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>موعد نهایی:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>نقطه عطف:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>پروژه:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>موعد ها</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"یک دیکشنری پایتون که برای فراهم کردن مقادیر پیش‌فرض هنگام ایجاد رکوردهای "
"جدید برای این نام مستعار مورد  ارزیابی قرار می‌گیرد."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"یک همکار را نمی توان بیش از یک بار در دسترسی به اشتراک گذاری پروژه انتخاب "
"کرد. لطفاً موارد تکراری را حذف کنید و دوباره امتحان کنید"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"یک مرحله شخصی نمی‌تواند به یک پروژه مرتبط شود زیرا فقط برای کاربر مربوطه "
"قابل مشاهده است."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "یک وظیفه خصوصی نمی‌تواند یک والد داشته باشد."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "یک زیرکار نمی‌تواند تکراری باشد."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "یک برچسب با نام مشابه از قبل موجود است."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "یک کار فقط می تواند یک مرحله شخصی برای هر کاربر داشته باشد."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "قبول ایمیل دریافتی از طرف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_instruction_message
msgid "Access Instruction Message"
msgstr "دسترسی به پیام راهنما"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "حالت دسترسی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "هشدار دسترسی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "فعال"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "فعالیت ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "دکوراسیون استثنایی فعالیت"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "برنامه های فعالیت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "انواع فعالیت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Add Milestone"
msgstr "اضافه کردن مرحله"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_task_plan
msgid "Add a new plan"
msgstr "ثبت یک برنامه جدید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "افزودن یادداشت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"برای سازماندهی وظایف خود در <b>مراحل</b> <i>به عنوان مثال، ستون‌هایی اضافه "
"کنید. جدید - \"در حال انجام\" - انجام شد</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "افزودن مخاطبان برای اشتراک گذاری این سند..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "اضافه کردن جزئیات درباره این کار..."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "افزودن محتوای اضافی برای نمایش در ایمیل"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "پس از آماده شدن وظیفه خود را اضافه کنید."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "مدیر"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "اسکرام چابک"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "مستعار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "مستعار تماس امنیتی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "نام مستعار دامنه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "دامین مستعار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "ایمیل مستعار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "نام مستعار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "وضعیت ایمیل مستعار"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "وضعیت نام مستعار در آخرین پیام دریافتی ارزیابی شد."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "مدل استعاری"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "همه"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "همه وظایف"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "همه کاربران داخلی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "زمان اختصاص داده شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "زمان تخصیص یافته:"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_rating
msgid "Allow Customer Ratings"
msgstr "فعال کردن امتیازدهی مشتری"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "حساب تحلیلی"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__analytic_plan_id
msgid "Analytic Plan"
msgstr "طرح تحلیلی"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""
"حساب تحلیلی که این پروژه، وظایف و برگه‌های زمانی آن به آن متصل هستند.  \n"
"هزینه‌ها و درآمدهای پروژه خود را با تنظیم این حساب تحلیلی روی اسناد مرتبط خود دنبال کنید (به عنوان مثال، سفارش‌های فروش، فاکتورها، سفارش‌های خرید، صورتحساب‌های فروشنده، هزینه‌ها و غیره).  \n"
"در صورت لزوم می‌توان این حساب تحلیلی را به طور جداگانه روی هر وظیفه تغییر داد.  \n"
"برای استفاده از برگه‌های زمانی به یک حساب تحلیلی نیاز است."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task and its timesheets are linked.\n"
"Track the costs and revenues of your task by setting its analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"By default, the analytic account of the project is set. However, it can be changed on each task individually if necessary."
msgstr ""
"حساب تجزیه و تحلیلی که این وظیفه و برگه‌های زمانی آن به آن متصل هستند.\n"
"هزینه‌ها و درآمدهای وظیفه خود را با تنظیم حساب تجزیه و تحلیلی آن بر روی اسناد مرتبط خود (مانند سفارش‌های فروش، فاکتورها، سفارش‌های خرید، صورت‌حساب‌های فروشنده، هزینه‌ها و غیره) پیگیری کنید.\n"
"به طور پیش‌فرض، حساب تجزیه و تحلیلی پروژه تنظیم شده است. با این حال، می‌توان آن را بر روی هر وظیفه به صورت جداگانه در صورت نیاز تغییر داد."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "تحلیلی"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""
"ارزیابی کنید که تیم شما چقدر سریع وظایف پروژه را تکمیل می‌کند و بررسی کنید "
"که آیا همه چیز طبق برنامه پیش می‌رود یا خیر."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "ارزیابی پیشرفت پروژه‌های خود و عملکرد کارکنان‌تان."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "موافقت شد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "مراحل بایگانی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "بایگانی شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "آیا مطمئن هستید که میخواهید ادامه دهید؟"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "آیا مطمئن هستید که می‌خواهید این مراحل را حذف کنید؟"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "مطمئنید می‌خواهید این رکورد را حذف کنید؟"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "فلش"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "آیکون فلش"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "مونتاژ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Assign a responsible to your task"
msgstr "به وظیفه خود یک مسئول تعیین کنید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "واگذار شده"

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "وظایف محول شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "واگذار شده به"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignees"
msgstr "محول شده به"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "تاریخ تخصیص"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "تاریخ محول کردن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "تاریخ واگذاری"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "در ریسک"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr ""
"همه اسناد یا لینک‌ها را مستقیماً به وظیفه پیوست کنید تا تمام اطلاعات تحقیق "
"در یک مکان متمرکز شوند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "پیوست‌هایی که از یک پیام نیستند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "مولف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "تولید خودکار وظایف برای فعالیت‌های منظم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "وضعیت کانبان خودکار"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""
"به‌طور خودکار وضعیت را زمانی که مشتری به بازخورد این مرحله پاسخ می‌دهد، تغییر دهید.\n"
"* بازخورد خوب از مشتری، وضعیت را به \"تایید شده\" (نشان سبز) به‌روزرسانی می‌کند.\n"
"* بازخورد خنثی یا بد، وضعیت کانبان را به \"تغییرات درخواست شده\" (نشان نارنجی) تنظیم می‌کند."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Avatar"
msgstr "آواتار"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
#, python-format
msgid "Average Rating"
msgstr "رتبه‌ی متوسط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "رتبه‌بندی متوسط (%)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "امتیاز متوسط: ناراضی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "رتبهٔ میانگین: خوب"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "رتبه بندی میانگین: راضی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "کار ناتمام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "تراز"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Billed"
msgstr "صورتحساب شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "مسدود کردن"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "مسدود شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "مسدود شده توسط"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "مسدود کردن"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocking Tasks"
msgstr "مسدود کردن وظایف"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "ایده‌پردازی"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "باگ"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "نمودار Burndown"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__can_be_marked_as_done
msgid "Can Be Marked As Done"
msgstr "می‌تواند به عنوان انجام شده علامت‌گذاری شود"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
#, python-format
msgid "Canceled"
msgstr "لغو شده"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "تغییرات درخواست شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""
"انتخاب کنید <b>نام</b> برای پروژه خود. <i>می‌تواند هر چیزی باشد که شما "
"می‌خواهید: نام یک مشتری، محصول، تیم، محل ساخت‌وساز، و غیره.</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"یک وظیفه را انتخاب کنید <b>نام</b> <i>(به عنوان مثال طراحی وب سایت، خرید "
"کالا...)</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "نظرات مشتری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "بسته شده در"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_subtask_count
msgid "Closed Sub-tasks Count"
msgstr "تعداد وظایف فرعی بسته‌شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__closed_task_count
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_count
msgid "Closed Task Count"
msgstr "تعداد وظایف بسته‌شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__closed_task_percentage
msgid "Closed Task Percentage"
msgstr "درصد وظایف بسته‌شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "وظایف بسته شده"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid ""
"Collaborate efficiently with key stakeholders by sharing with them the "
"Kanban view of your tasks. Collaborators will be able to edit parts of tasks"
" and send messages."
msgstr ""
"با همکاری مؤثر با ذینفعان کلیدی با اشتراک گذاری نمای کانبان وظایف خود، "
"همکاری نمایید. همکاران قادر خواهند بود بخش‌های وظایف را ویرایش کرده و پیام "
"ارسال کنند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "همکار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
msgid "Collaborators"
msgstr "همکاران"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "همکاران در پروژه به اشتراک گذاشته شده"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"برای جمع‌آوری بازخورد از مشتریان خود، هنگامی که یک وظیفه وارد یک مرحله خاص می‌شود، یک درخواست امتیازدهی به آنها ارسال کنید. برای این کار، یک قالب ایمیل امتیازدهی را روی مراحل مربوطه تعریف کنید.\n"
"امتیازدهی هنگام تغییر مرحله: هنگامی که وظیفه به مرحله‌ای می‌رسد که قالب ایمیل امتیازدهی روی آن تنظیم شده است، یک ایمیل به‌طور خودکار ارسال خواهد شد.\n"
"امتیازدهی دوره‌ای: تا زمانی که وظیفه در مرحله‌ای که قالب ایمیل امتیازدهی روی آن تنظیم شده است باقی بماند، یک ایمیل به‌طور خودکار در فواصل منظم ارسال خواهد شد."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "رنگ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "رنگ پس زمینه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""
"ارتباط با مشتریان در مورد وظیفه با استفاده از دروازه ایمیل. طراحی‌های لوگو "
"را به وظیفه پیوست کنید تا اطلاعات از طراحان به کارگرانی که تی‌شرت را چاپ "
"می‌کنند انتقال یابد."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "تاریخچه ارتباطات"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "شرکت"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "پیکربندی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "پیکربندی مراحل"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "تایید کردن"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#, python-format
msgid "Confirmation"
msgstr "تاییدیه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Congratulations, you are now a master of project management."
msgstr "تبریک می‌گوییم، شما اکنون استاد مدیریت پروژه هستید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "مشاوره ای"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "مخاطب"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "تبدیل وظیفه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
#, python-format
msgid "Convert to Task/Sub-Task"
msgstr "تبدیل به وظیفه/زیر وظیفه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "کپی رایت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Costs"
msgstr "هزینه‌ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "تصویر رویی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr "ایجاد <b>فعالیت‌ها</b> برای تعیین کارهای شخصی یا برنامه‌ریزی جلسات."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "ایجاد تاریخ"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "ایجاد یک پروژه"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "یک مرحله جدید در فرصت‌های فروش ایجاد کنید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "ایجاد پروژه"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""
"ایجاد پروژه‌ها برای سازماندهی وظایف خود و تعریف جریان کاری متفاوت برای هر "
"پروژه."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""
"ایجاد پروژه‌ها برای سازماندهی وظایف خود. یک جریان کاری متفاوت برای هر پروژه "
"تعریف کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "ایجاد وظایف با ارسال یک ایمیل به"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "ایجاد وظایف با ارسال ایمیل به آدرس ایمیل پروژه شما."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "ایجادشده در"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "تاریخ ایجاد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "ارز"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "پروژه فعلی وظیفه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "وضعیت کنونی این کار"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"فعلاً برای همه کاربرانی که این سند را مشاهده می‌کنند، در دسترس است. برای "
"محدود کردن به کارمندان داخلی، کلیک کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"در حال حاضر به کارکنان داخلی محدود شده است، برای در دسترس قرار دادن آن برای "
"همه افرادی که این سند را مشاهده می‌کنند، کلیک کنید."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "پیام برگشتی سفارشی"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Customer"
msgstr "مشتری"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Customer Email"
msgstr "ایمیل مشتری"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "بازخورد مشتری"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "آدرس پرتال مشتری"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "رتبه بندی مشتریان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "وضعیت رتبه بندی مشتری"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""
"مشتریان بازخوردهای خود را از طریق ایمیل پیشنهاد می‌کنند؛ اودوو به‌طور خودکار"
" وظایف را ایجاد می‌کند، و شما می‌توانید به‌طور مستقیم بر روی وظیفه ارتباط "
"برقرار کنید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Customers will be added to the followers of their project and tasks."
msgstr "مشتریان به دنبال کنندگان پروژه‌ها و وظایفشان اضافه خواهند شد."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "روزانه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
#, python-format
msgid "Date"
msgstr "تاریخ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "Date and Stage"
msgstr "تاریخ و مرحله"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""
"تاریخی که وضعیت کار شما آخرین بار تغییر کرده است.  \n"
"براساس این اطلاعات شما می توانید کارهایی که به تعویق افتاده‌اند را شناسایی کنید و آماری درباره زمان معمول مورد نیاز برای انتقال کارها از یک مرحله/وضعیت به مرحله/وضعیت دیگر بدست آورید."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""
"تاریخی که این پروژه به پایان می‌رسد. بازه زمانی تعریف شده در پروژه هنگام "
"مشاهده برنامه‌ریزی آن در نظر گرفته می‌شود."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"تاریخی که این کار آخرین بار اختصاص داده شد (یا لغو اختصاص داده شد). بر اساس "
"این، می‌توانید آمار زمانی را که معمولاً برای اختصاص کارها صرف می‌شود، بدست "
"آورید."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "روز"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "روزهای باقی مانده تا مهلت نهایی"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
#, python-format
msgid "Deadline"
msgstr "آخرین مهلت"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "گرامی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "مقادیر پیش فرض"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"مراحلی را که از ایجاد وظیفه تا پایان کار یا موضوع در پروژه\n"
"                استفاده می شود را تعریف کنید.\n"
"                 شما از این مراحل برای پیگیری پیشرفت در\n"
"                حل یک وظیفه یا مسئله استفاده خواهید کرد."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr ""
"تعریف کنید پروژه‌هایتان از ایجاد تا تکمیل از طریق چه مراحلی عبور می‌کنند."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr "مراحل حرکت کارهای خود را از ایجاد تا تکمیل تعریف کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "حذف مرحله"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#, python-format
msgid "Delete Project Stage"
msgstr "حذف مرحله پروژه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
#, python-format
msgid "Delete Stage"
msgstr "حذف مرحله"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/delete_subtasks_confirmation_dialog/delete_subtasks_confirmation_dialog.js:0
#, python-format
msgid ""
"Deleting a task will also delete its associated sub-tasks. If you wish to "
"preserve the sub-tasks, make sure to unlink them from their parent task "
"beforehand. Are you sure you want to proceed?"
msgstr ""
"حذف یک وظیفه همچنین وظایف زیرمجموعه مرتبط با آن را نیز حذف خواهد کرد. اگر "
"می‌خواهید وظایف زیرمجموعه را حفظ کنید، اطمینان حاصل کنید که قبل از آن پیوند "
"آنها را از وظیفه والد قطع کنید. آیا مطمئن هستید که می‌خواهید ادامه دهید؟"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""
"در زمان رسیدن به نقطه عطف، خدمات خود را به‌طور خودکار ارائه کنید، با پیوند "
"دادن آن به یک آیتم سفارش فروش."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "تحویل داده شد"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "وظایف وابسته"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "توصیف"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "توضیحاتی برای ارائه اطلاعات و زمینه بیشتر درباره این پروژه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "طراحی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "ترتیب انجام وظایف را تعیین کنید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "توسعه"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "خلاصه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "بازاریابی دیجیتال"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "هشدار امتیاز غیرفعال"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "رها کردن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "نمایش نحوه دسترسی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_in_project
msgid "Display In Project"
msgstr "نمایش در پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_parent_task_button
msgid "Display Parent Task Button"
msgstr "نمایش دکمه وظیفه والد"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "دسترسی ندارید، از این داده ها برای ایمیل خلاصه کاربر صرفنظر کنید"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "انجام شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "پیش‌نویس"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""
"هر کاربر باید حداقل یک مرحله شخصی داشته باشد. یک مرحله جدید ایجاد کنید که "
"وظایف را بتوان به آن منتقل کرد پس از اینکه وظایف انتخاب شده حذف شدند."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
msgid "Edit"
msgstr "ویرایش"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "ویرایش"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_email
msgid "Email"
msgstr "پست الکترونیک"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "مستعار ایمیل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "قالب ایمیل"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""
"آدرس‌های ایمیلی که در بخش CC ایمیل‌های ورودی از این وظیفه بودند و در حال "
"حاضر به مشتری موجودی متصل نیستند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "ایمیل سی سی"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "دامنه‌ی ایمیل مانند 'example.com' در '<EMAIL>'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "ایمیل های ارسال شده به"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Employees Only"
msgstr "فقط کارمندان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "تاریخ پایان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "تاریخ پایان"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "خطا! شما نمی‌توانید یک سلسله مراتب بازگشتی از وظایف ایجاد کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr ""
"همه می‌توانند ایده‌ها را پیشنهاد دهند و ویرایشگر بهترین‌ها را به عنوان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Expected"
msgstr "مورد انتظار"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "آزمایش"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "تاریخ انقضا"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "خارجی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "اطلاعات اضافی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "علاقه‌مندی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "پروژه‌های مورد علاقه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "مدرک نهایی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "تاشده در کانبان"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "پیگیری و نظرات روی وظایف پروژه‌های شما"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "پیگیری پیشرفت پروژه‌های خود را داشته باشید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr ""
"برای ردیابی خودکار رویدادهای مرتبط با وظایف و مسائل این پروژه، این پروژه را "
"دنبال کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "دنبال‌شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "به‌روزرسانی‌های دنبال شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی به عبارتی fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "برای همیشه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "فرکانس"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "فعالیت های آینده"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""
"یک عکس فوری از وضعیت پروژه خود دریافت کنید و پیشرفت آن را با سهامداران کلیدی"
" به اشتراک بگذارید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr "بازخورد مشتریان را دریافت کنید و عملکرد کارکنان خود را ارزیابی کنید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "دادن دسترسی به پرتال"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""
"کارمندان را با افزودن به عنوان دنبال‌کنندگان به پروژه یا وظایف خود دسترسی "
"دهید. کارمندان به طور خودکار به وظایفی که به آن‌ها اختصاص داده شده است "
"دسترسی پیدا می‌کنند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Grant portal users access to your project or tasks by adding them as "
"followers. Customers automatically get access to their tasks in their "
"portal."
msgstr ""
"برای دسترسی کاربران پورتال به پروژه یا وظایف خود، آن‌ها را به عنوان "
"دنبال‌کننده اضافه کنید. مشتریان به صورت خودکار به وظایف خود در پورتال دسترسی"
" پیدا می‌کنند."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr ""
"مدیریت گردآوری ایده‌های خود را در داخل وظایف پروژه جدیدتان انجام دهید و "
"آن‌ها را در قسمت صحبت‌ها مورد بحث قرار دهید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "دست به دست کردن"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "چهره خندان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__has_late_and_unreached_milestone
msgid "Has Late And Unreached Milestone"
msgstr "دارای مایلستون های دیررس و نرسیده است"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "بالا"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "تاریخچه"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr "تغییر خانه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "ساعت"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "وضعیت پروژه چگونه است؟"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "شناسه"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"شناسه رکورد والد حاوی نام مستعار (مثال: پروژه دارای نام مستعار ایجاد وظیفه)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "شمایل"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنا."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "‌ایده‌ها"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"اگر فعال شود، این مرحله در نمای کانبان پروژه‌های شما به صورت جمع شده نمایش "
"داده می‌شود. پروژه‌هایی که در یک مرحله جمع شده هستند به عنوان بسته شده در "
"نظر گرفته می‌شوند."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"اگر تنظیم شود، درخواست رتبه‌بندی به‌صورت خودکار زمانی که کار به این مرحله می‌رسد، از طریق ایمیل به مشتری ارسال خواهد شد.\n"
"به‌صورت جایگزین، به صورت منظم و تا زمانی که کار در این مرحله باقی بماند، بر حسب تنظیمات پروژه شما ارسال خواهد شد.\n"
"برای استفاده از این ویژگی، اطمینان حاصل کنید که گزینه 'رتبه‌بندی مشتریان' بر روی پروژه شما فعال باشد."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""
"اگر تنظیم شده باشد، یک ایمیل به‌طور خودکار به مشتری ارسال خواهد شد وقتی که "
"پروژه به این مرحله برسد."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""
"اگر تنظیم شود، هنگامی که وظیفه به این مرحله برسد، ایمیلی به طور خودکار به "
"مشتری ارسال خواهد شد."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"در صورت تنظیم، این محتوا به‌جای پیام پیش‌فرض، به‌طور خودکار برای کاربران "
"غیرمجاز ارسال می‌شود."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"اگر فیلد فعال روی False تنظیم شود، به شما امکان می دهد پروژه را بدون حذف آن "
"پنهان کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "در جریان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "در حال توسعه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "صندوق پستی"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "داخلی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal Note"
msgstr "یادداشت داخلی"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"ایمیل داخلی مرتبط با این پروژه. ایمیل های دریافتی به طور خودکار با وظایف "
"همگام می شوند (یا در صورت نصب ماژول Issue Tracker به صورت اختیاری منتشر "
"می‌شوند)."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "یادداشت‌های داخلی تنها به کاربران داخلی نمایش داده می‌شوند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr "عملگر نامعتبر: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr "مقدار نامعتبر: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Invite Collaborators"
msgstr "دعوت از همکاران"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "دعوت افراد"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "دعوت شدگان کاربران داخلی (خصوصی)"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "کاربران دعوت شده به پورتال و تمامی کاربران داخلی (عمومی)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Invoiced"
msgstr "فاکتور شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "آیا مهلت سپری شده است"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "آیا مهلت زمانی در آینده است؟"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_milestone_exceeded
msgid "Is Milestone Exceeded"
msgstr "آیا نقطه عطف بیش از حد است"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Is toggle mode"
msgstr "حالت تغییر است"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "نسخه"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""
"JSON که شناسه‌ها از یک فیلد many2one را به ثانیه‌های صرف شده نگاشت می‌کند"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"پیگیری پیشرفت وظایف خود را از ایجاد تا تکمیل دنبال کنید.<br>\n"
"                    با چت کردن به صورت هم‌زمان یا از طریق ایمیل به طور موثر همکاری کنید."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"پیگیری پیشرفت وظایف خود را از ایجاد تا تکمیل حفظ کنید.<br>\n"
"                به صورت کارآمد هم‌کاری کنید با چت کردن در زمان واقعی یا از طریق ایمیل."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "مقدار باز شده وظیفه پروژه KPI"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "ماه گذشته"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Last Stage Update"
msgstr "آخرین بروزرسانی مرحله"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "اخرین بروزرسانی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "رنگ آخرین بروزرسانی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "وضعیت آخرین به‌روزرسانی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "آخرین بروز‌رسانی در"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "فعالیتهای اخیر"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "تکمیل پروژه با تأخیر"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "بعدا"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Leave a comment"
msgstr "کامنت بگذارید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "بیایید اولین <b>پروژه</b> شما را ایجاد کنیم."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>stage</b>."
msgstr "ایجاد <b>مرحله</b> اول خود را شروع کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "بیایید اولین <b>وظیفه</b> شما را ایجاد کنیم."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your second <b>stage</b>."
msgstr "ایجاد کنیم <b>مرحله</b> دوم شما را."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"بیایید به <b>نمای کانبان</b> برگردیم تا نمایی کلی از وظایف بعدی خود داشته "
"باشیم."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "بیایید کار را روی وظایف خود شروع کنیم."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "بیایید منتظر باشیم تا مشتریان خود را نشان دهند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Link"
msgstr "لینک"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "زنده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "تشخیص ورودی بر اساس بخش محلی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "طراحی لوگو"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Look for the"
msgstr "به دنبالِ"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "پایین"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "پیوستهای اصلی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"مدیریت چرخه حیات پروژه خود را با استفاده از نمای کانبان انجام دهید. پروژه‌های تازه کسب‌شده را اضافه کنید، \n"
"آن‌ها را تخصیص دهید و از"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "تولید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Margin"
msgstr "حاشیه سود"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "Mark as done"
msgstr "علامت به عنوان انجام شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "منبع یابی مواد"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""
"میزان رضایت مشتریان خود را با ارسال درخواست ارزیابی زمانی که وظایف شما به "
"مرحله خاصی می‌رسند، اندازه‌گیری کنید."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "اعضا"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "منو"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "پیام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "پیام ها"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Milestone"
msgstr "موعد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "تعداد مایلستون‌ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count_reached
msgid "Milestone Count Reached"
msgstr "تعداد نقطه عطف به حداکثر رسید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_project_task__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Milestones"
msgstr "موعد ها"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "مخلوط کردن"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "ماه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد نهای فعالیت من"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "آخرین مهلت من"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "علایق من"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "پروژه‌های من"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "وظایف من"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "به‌روزرسانی‌های من"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "نام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "نام کوتاه شده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "نام وظایف"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""
"نامی که برای اشاره به وظایف پروژه شما استفاده می‌شود، مانند وظایف، تیکت‌ها، "
"اسپرینت‌ها، و غیره..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "صورت خنثی"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#, python-format
msgid "New"
msgstr "جدید"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "ویژگی جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#, python-format
msgid "New Milestone"
msgstr "موعد جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "سفارشات جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
#, python-format
msgid "New Project"
msgstr "پروژه جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "پروژه‌های جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "درخواست جدید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
#, python-format
msgid "New Task"
msgstr "وظیفه جدید"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "جدیدترین"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Next"
msgstr "بعدی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "فعالیت بعدی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "بدون مشتری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "بدون نقطه عطف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr "بدون پروژه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "No Subject"
msgstr "بدون موضوع"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "نوع فعالیتی یافت نشد. بیایید یکی بسازیم!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_collaborator_action
msgid "No collaborators found"
msgstr "یافت نشد همکاران"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "هنوز رتبه بندی مشتری وجود ندارد"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "هنوز هیچ داده ای وجود ندارد!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "هیچ پروژه ای یافت نشد. بیایید یکی ایجاد کنیم!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "هیچ مرحله ای پیدا نشد. بیایید یکی ایجاد کنیم!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "هیچ برچسبی پیدا نشد. بیایید یکی ایجاد کنیم!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "هیچ وظیفه‌ای پیدا نشد. بیایید یکی ایجاد کنیم!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "به روز رسانی پیدا نشد بیایید یکی ایجاد کنیم!"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "هیچکدام"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Not Implemented."
msgstr "اجرا نشده است."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "یادداشت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__projects_count
msgid "Number of Projects"
msgstr "تعداد پروژه‌ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "تعداد وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "تعداد سندهای پیوست شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام هایی که نیاز به اقدام دارند"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "خارج از مسیر"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "طراحی دفتر کار"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "اسپرینت تکمیل شده قدیمی"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "در انتظار"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "در مسیر"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "یکبار در ماه"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr "فقط تصاویر jpeg، png، bmp و tiff به عنوان پیوست مجاز هستند."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "اوپس! چیزی درست نبود. صفحه را دوباره بارگزاری کن و دوباره تلاش کن."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__open_task_count
msgid "Open Task Count"
msgstr "تعداد وظایف باز"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "وظایف باز"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Operation not supported"
msgstr "عملیات پشتیبانی نمی شود"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"شناسه اختیاری یک موضوع (رکورد) که تمام پیام‌های دریافتی به آن پیوست می‌شود، "
"حتی اگر به آن پاسخ نداده باشند. اگر تنظیم شود، ایجاد رکوردهای جدید به طور "
"کامل غیرفعال می شود."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Organize priorities amongst orders using the"
msgstr "سامان‌دهی اولویت‌ها در میان سفارشات با استفاده از"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"ساماندهی وظایف خود را با تخصیص آن‌ها در سراسر خط لوله انجام دهید.<br>\n"
"                    با چت کردن به صورت زمان واقعی یا از طریق ایمیل به طور کارآمد همکاری کنید."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "دیگران"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "وظایف بیش از حد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "ایده ها را شماره گذاری کنید"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "مدل والد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "شناسه موضوع رکورد والد"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
#, python-format
msgid "Parent Task"
msgstr "کار والد"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"مدل والد دارای نام مستعار. مدلی که مرجع نام مستعار را نگه می دارد لزوماً "
"مدلی نیست که توسط alias_model_id (مثال: پروژه (parent_model) و task (model))"
" ارائه شده است."

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr "شرکت شریک نمی‌تواند با شرکت پروژه‌های تخصیص‌یافته آن متفاوت باشد"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
#, python-format
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr "شرکت شریک نمی‌تواند با شرکت وظایف اختصاص داده شده آن متفاوت باشد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr ""
"افرادی که برای همکاری در پروژه دعوت شده‌اند، حقوق دسترسی پورتال خواهند داشت."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"افرادی که این پروژه و وظایف آن برای آنها قابل مشاهده خواهد بود.\n"
"\n"
"- کاربران داخلی دعوت‌شده: وقتی یک پروژه را دنبال می‌کنند، کاربران داخلی به تمام وظایف آن بدون استثنا دسترسی خواهند داشت. در غیر این صورت، فقط به وظایف خاصی که دنبال می‌کنند دسترسی خواهند داشت. یک کاربر با سطح دسترسی > مدیر پروژه هنوز هم می‌تواند به این پروژه و وظایف آن دسترسی داشته باشد، حتی اگر به طور خاص جزء دنبال‌کنندگان نباشند.\n"
"\n"
"- همه کاربران داخلی: همه کاربران داخلی می‌توانند به پروژه و تمام وظایف آن بدون استثنا دسترسی داشته باشند.\n"
"\n"
"- کاربران پورتال دعوت‌شده و همه کاربران داخلی: همه کاربران داخلی می‌توانند به پروژه و تمام وظایف آن بدون استثنا دسترسی داشته باشند. وقتی یک پروژه را دنبال می‌کنند، کاربران پورتال به تمام وظایف آن بدون استثنا دسترسی خواهند داشت. در غیر این صورت، فقط به وظایف خاصی که دنبال می‌کنند دسترسی خواهند داشت.\n"
"\n"
"وقتی یک پروژه به صورت فقط-خوانده‌شده به اشتراک گذاشته می‌شود، کاربر پورتال به پورتال خود هدایت می‌شود. آنها می‌توانند وظایف را ببینند، اما نمی‌توانند آنها را ویرایش کنند. وقتی یک پروژه به صورت قابل ویرایش به اشتراک گذاشته می‌شود، کاربر پورتال به نمایش‌های کانبان و فهرست وظایف هدایت می‌شود. آنها می‌توانند تعدادی از فیلدها را روی وظایف ویرایش کنند.\n"
"\n"
"در هر صورت، یک کاربر داخلی بدون حقوق دسترسی به پروژه همچنان می‌تواند به یک وظیفه دسترسی داشته باشد، به شرطی که URL مربوطه در اختیارشان قرار گیرد (و اگر پروژه خصوصی است، بخشی از دنبال‌کنندگان باشند)."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "درصد رتبه‌های راضی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "مرحله شخصی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "وضعیت مرحله شخصی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
msgid "Personal Stages"
msgstr "<br>مراحل شخصی</br>"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "مرحله وظیفه شخصی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "تاریخ برنامه ریزی شده"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"لطفا وظایف موجود در پروژه مرتبط با حساب‌هایی را که می‌خواهید حذف کنید، پاک "
"کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "تولید پادکست و ویدئو"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"خط مشی ارسال پیام در سند با استفاده از mailgateway.\n"
"- همه: همه می توانند پست کنند\n"
"- شرکا: فقط شرکای تأیید شده\n"
"- دنبال کنندگان: فقط دنبال کنندگان سند مرتبط یا اعضای کانال های زیر\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "آدرس دسترسی پرتال"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "نام‌های کاربران پورتال"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "کاربران پورتال از دنبال‌کنندگان پروژه و وظایف آن حذف خواهند شد."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
#, python-format
msgid "Previous"
msgstr "قبلی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Prioritize your tasks by marking important ones using the"
msgstr "برنامه‌های خود را با علامت‌گذاری وظایف مهم در اولویت قرار دهید"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#, python-format
msgid "Priority"
msgstr "اولویت"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "اولویت: {{'مهم' اگر task.priority == '1' باشد وگرنه 'عادی'}}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility_warning
msgid "Privacy Visibility Warning"
msgstr "هشدار قابلیت مشاهده حریم خصوصی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
#, python-format
msgid "Private"
msgstr "خصوصی"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "وظایف خصوصی"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project for "
"the task to gain access to this feature."
msgstr ""
"وظایف خصوصی نمی‌توانند به وظایف فرعی تبدیل شوند. لطفاً برای دسترسی به این "
"ویژگی، یک پروژه برای وظیفه تعیین کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Profitability"
msgstr "سودآوری"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "پیشرفت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "درصد پیشرفت"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
#, python-format
msgid "Project"
msgstr "پروژه"

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "همکاران پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "تعداد پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "مدیر پروژه"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "پروژه مایل‌استون"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "نام پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "وضعیت امتیازدهی پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "پروژه به اشتراک گذاشته شده"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "اشتراک پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "اشتراک پروژه: وظیفه"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "مرحله پروژه"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "تغییر مرحله پروژه"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "ویزارد حذف مرحله پروژه"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "مراحل پروژه"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "برچسب‌های پروژه"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr "ویزارد حذف مرحله وظیفه پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "کارهای پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "عنوان پروژه"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "به‌روزرسانی پروژه"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "به روز رسانی پروژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "قابلیت دیدن پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "شرح پروژه..."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "وضعیت پروژه - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "کارهای مربوط به پروژه"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "پروژه: پروژه تکمیل شد"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "پروژه: تأیید درخواست"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "پروژه: ارسال امتیاز"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "پروژه: درخواست ارزیابی وظیفه"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_tags__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_res_partner__project_ids
#: model:ir.model.fields,field_description:project.field_res_users__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#, python-format
msgid "Projects"
msgstr "پروژه‌ها"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr ""
"پروژه‌ها شامل وظایف مرتبط با همان موضوع هستند و هر کدام داشبورد مخصوص به خود"
" را دارند."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"پروژه‌هایی که این مرحله در آن‌ها حضور دارد. اگر شما یک جریان کاری مشابه را "
"در چندین پروژه دنبال می‌کنید، می‌توانید این مرحله را بین آن‌ها به اشتراک "
"بگذارید و به این صورت اطلاعات تلفیقی دریافت کنید."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "مشخصات"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "منتشر شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Published on"
msgstr "منتشر شده در"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "انتشار"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "فصلی"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""
"به‌سرعت وضعیت وظایف را برای تاییدها یا درخواست‌های تغییر بررسی کنید و مواردی"
" را که تا حل وابستگی‌ها در انتظار هستند با آیکون ساعت شنی شناسایی کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "رتبه دهی"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "امتیاز (/۵)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "متن میانگین رتبه‌بندی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "قالب ایمیل رتبه بندی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "فرکانس رتبه بندی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "رتبه بندی آخرین بازخورد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "رتبه بندی آخرین تصویر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "رتبه‌بندی آخرین مقدار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "رتبه بندی آخرین مهلت درخواست"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "رتبه‌بندی رضایت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "متن رتبه‌بندی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#, python-format
msgid "Rating Value (/5)"
msgstr "ارزش‌گذاری (از ۵)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "تعداد رتبه بندی"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "رتبه‌ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "دست یافته"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "تاریخ دست یافته"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "فقط خواندنی"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "دریافت {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "گیرندگان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "شناسه موضوع رکورد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "ضبط‌کردن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "تکرار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "مکرر"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "وظایف تکرار شونده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "رد شده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "سند مرتبط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "شناسه مدرک مربوطه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "مدل مدرک مربوطه"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "نوسازی‌ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "تکرار هر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "واحد تکرار"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "گزارش"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "پژوهش"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "تحقیق و توسعه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "پروژه تحقیقاتی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "تحقیق"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "تخصیص منابع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Revenues"
msgstr "درآمد ها"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "صورت غمگین"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to drag images in description"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
#, python-format
msgid "Save the task to be able to paste images in description"
msgstr "ذخیره کردن وظیفه برای قرار دادن تصاویر در توضیحات"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Schedule your activity once it is ready."
msgstr "برنامه‌ریزی فعالیت خود را پس از آماده شدن انجام دهید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "اسکریپت"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "جستجو <span class=\"nolabel\"> (در محتوا)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "جستوجو پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "به‌روزرسانی جستجو"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "جستجو در همه"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "جستجو در تخصیص‌دهنده‌ها"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Customer"
msgstr "جستجو در مشتری"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "جستجو در پیامها"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Milestone"
msgstr "جستجو در نقطه‌عطف"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "جستجو در اولویت"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "جستجو در پروژه"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "جستجو در مرجع"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "جستجو در وضعیتها"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "جستجو در وضعیت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "توکن امنیتی"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#, python-format
msgid "Send"
msgstr "ارسال"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "ارسال ایمیل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "تعیین تصویر رویی"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
#, python-format
msgid "Set Status"
msgstr "تنظیم وضعیت"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "یک قالب ایمیل رتبه بندی در مراحل تنظیم کنید"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""
"تنظیم در مراحل پروژه برای اطلاع‌رسانی به مشتریان هنگامی که یک پروژه به آن "
"مرحله می‌رسد"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
#, python-format
msgid "Set priority as %s"
msgstr "اولویت را به %s تنظیم کنید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
#, python-format
msgid "Set state as..."
msgstr "تنظیم وضعیت به عنوان..."

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""
"این الگو را در یک مرحله پروژه تنظیم کنید تا از مشتریان خود درخواست بازخورد "
"کنید. ویژگی \"امتیازات مشتری\" را در پروژه فعال کنید."

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"قالب را در مرحله پروژه تنظیم کنید تا هنگام رسیدن وظایف به مراحل، ایمیل‌ها "
"به‌صورت خودکار ارسال شوند"

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "تنظیمات"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "اشتراک‌گذاری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "قابل ویرایش را به اشتراک بگذارید"

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "اشتراک پروژه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Read-only"
msgstr "اشتراک گذاری فقط خواندنی"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "نمایش پروژه در داشبورد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "تمام رکوردهایی که تاریخ اقدام بعدی آن قبل از امروز است را نشان بده"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr ""
"متاسفم، اما به نظر می‌رسد که به نظر می رسد شما متنی برای ترجمه ارائه "
"نداده‌اید. اگر متنی دارید که می‌خواهید ترجمه شود، لطفاً آن را ارسال کنید تا "
"من بتوانم در ترجمه آن به شما کمک کنم."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "توسعه نرم افزار"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "متاسفم. شما نمی توانید یک وظیفه را به عنوان وظیفه اصلی آن تعیین کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "مشخصات"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "بک لاگ اسپرینت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "اسپرینت تکمیل شده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "اسپرینت در حال پیشرفت"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Stage"
msgstr "مرحله"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "مرحله تغییر کرد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "مالک مرحله"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "مرحله تغییر کرد"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Stage: %s"
msgstr "مرحله: %s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stages_active
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "مراحل فعال"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__stage_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "مراحل برای حذف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr "وظایف ویژه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "تاریخ آغاز"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "استان"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "وضعیت"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Status Update - "
msgstr "به‌روزرسانی وضعیت -"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت ها\n"
"سررسید: تاریخ سررسید گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه ریزی شده: فعالیت های آینده."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "زمان وضعیت"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "تعداد زیرکارها"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "زیر-وظیفه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_allocated_hours
msgid "Sub-tasks Allocated Time"
msgstr "اختصاص زمان به زیر وظایف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "ارسال شده در"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"مجموع ساعت‌های تخصیص‌یافته برای همه زیرکارها (و زیرکارهای آن‌ها) که به این "
"کار مرتبط هستند. معمولاً کمتر یا مساوی با ساعت‌های تخصیص‌یافته به این کار "
"است."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "خلاصه"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "چاپ تی‌شرت"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "برچسب‌ها"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_rating_graph/project_rating_graph_view.js:0
#: code:addons/project/static/src/views/project_rating_pivot/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Task"
msgstr "وظیفه"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "فعالیتهای کار"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "تأیید شده وظیفه"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Canceled"
msgstr "وظیفه لغو شد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count
#: model:ir.model.fields,field_description:project.field_project_update__task_count
msgid "Task Count"
msgstr "تعداد وظیفه"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "وظیفه ایجاد شد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "وابستگی وظایف"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "کـــار انجـام شـــده"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "در حال انجام وظیفه"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "لاگ‌های وظیفه"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_task_plan
msgid "Task Plans"
msgstr "برنامه‌های وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "ویژگی‌های وظیفه"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "رتبه‌بندی وظیفه"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تکرار وظیفه"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "مرحله وظیفه"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "وضعیت کار لغو شده"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "مراحل فعالیت‌ها"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "عنوان کار"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "عنوان کار ..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "انتظار کار"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "وظیفه تأیید شد"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task canceled"
msgstr "کار لغو شد"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "وظیفه انجام شد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "وظیفه:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "وظیفه: درخواست رتبه‌بندی"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_project_milestone__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_project_tags__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Tasks"
msgstr "وظایف"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "تحلیل کارها"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "مدیریت وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "مراحل کارها"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
#, python-format
msgid "Tasks in Recurrence"
msgstr "وظایف در تکرار"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "آزمونها"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
#, python-format
msgid "The Burndown Chart must be grouped by"
msgstr "چارت سوخت باید بر اساس گروه بندی شود"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "مرحله شخصی کاربر فعلی."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "مرحله وظیفه شخصی کاربر فعلی."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "سند وجود ندارد یا شما دسترسی لازم برای مشاهده آن را ندارید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The end date should be in the future"
msgstr "تاریخ پایان باید در آینده باشد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "به‌روزرسانی زیر اضافه شده است:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "این مراحل کلان اضافه شدند:"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
#, python-format
msgid "The interval should be greater than 0"
msgstr "فاصله باید بزرگتر از ۰ باشد"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"مدلی (Odoo Document Kind) که این نام مستعار با آن مطابقت دارد. هر ایمیل "
"دریافتی که به سابقه موجود پاسخ نمی‌دهد باعث ایجاد یک رکورد جدید از این مدل "
"می‌شود (به عنوان مثال یک وظیفه پروژه)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"نام مستعار ایمیل، به عنوان مثال 'jobs'. اگر می خواهید ایمیل های "
"<<EMAIL>> را دریافت کنید"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr "پروژه و شریک مرتبط باید به همان شرکت متصل باشند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"پروژه را نمی توان با گیرنده(های) به اشتراک گذاشت زیرا حریم خصوصی پروژه بسیار"
" محدود شده است. حریم خصوصی را روی «قابل مشاهده برای مشتریان» تنظیم کنید تا "
"برای گیرنده یا گیرندگان قابل دسترسی باشد."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr ""
"نمی‌توان شرکت پروژه را تغییر داد اگر حساب تحلیلی آن دارای خطوط تحلیلی باشد "
"یا اگر بیش از یک پروژه به آن مرتبط باشد."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "تاریخ شروع پروژه باید قبل از تاریخ پایان آن باشد."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "The search does not support the %s operator or %s value."
msgstr "جستجو از عملگر %s یا مقدار %s پشتیبانی نمی‌کند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr "وظیفه و شریک مرتبط باید به همان شرکت متصل باشند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"وظیفه را نمی توان با گیرنده(ها) به اشتراک گذاشت زیرا حریم خصوصی پروژه بسیار "
"محدود شده است. حریم خصوصی پروژه را روی «قابل مشاهده برای مشتریان» تنظیم کنید"
" تا گیرنده (ها) به آن دسترسی داشته باشند."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
#, python-format
msgid "The view must be grouped by date and by stage_id"
msgstr "مشاهده باید بر اساس تاریخ و stage_id گروه‌بندی شود"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "فعلا کامنتی موجود نیست."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "هیچ پروژه‌ای نیست."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "در حال حاضر هیچ امتیازی برای این پروژه وجود ندارد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "هیچ کاری نیست."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "There is nothing to report."
msgstr "هیچ موردی برای گزارش وجود ندارد."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read-only "
"mode on your website. This includes leads/opportunities, quotations/sales "
"orders, purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""
"آنها می‌توانند وظایف پروژه‌های مشترک را ویرایش کرده و مستندات خاصی را در "
"حالت فقط خواندنی در وب‌سایت شما مشاهده کنند. این شامل سرنخ‌ها/فرصت‌ها، "
"پیشنهادها/سفارشات فروش، سفارشات خرید، فاکتورها و صورتحساب‌ها، برگه‌های زمان "
"و تیکت‌ها می‌باشد."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "این ماه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "این هفته"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr ""
"این یک پیش‌نمایش از چگونگی ظاهر پروژه هنگامی است که با مشتریان به اشتراک "
"گذاشته می‌شود و آنها دسترسی ویرایشی دارند."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is associated with %s, whereas the selected stage belongs to "
"%s. There are a couple of options to consider: either remove the company "
"designation from the project or from the stage. Alternatively, you can "
"update the company information for these records to align them under the "
"same company."
msgstr ""
"این پروژه با %s مرتبط است، در حالی که مرحله انتخابی به %s تعلق دارد. چند "
"گزینه برای بررسی وجود دارد: یا نام شرکت را از پروژه یا از مرحله حذف کنید. در"
" غیر این صورت، می‌توانید اطلاعات شرکت را برای این سوابق به‌روزرسانی کنید تا "
"آن‌ها را تحت همان شرکت هم‌راستا کنید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid ""
"This project is not associated to any company, while the stage is associated"
" to %s. There are a couple of options to consider: either change the "
"project's company to align with the stage's company or remove the company "
"designation from the stage"
msgstr ""
"این پروژه به هیچ شرکتی مرتبط نیست، در حالی که مرحله به %s مرتبط است. چند "
"گزینه برای در نظر گرفتن وجود دارد: یا شرکت پروژه را تغییر دهید تا با شرکت "
"مرحله همراستا شود یا تخصیص شرکت را از مرحله حذف کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#, python-format
msgid "This task is blocked by another unfinished task"
msgstr ""
"این وظیفه توسط وظیفه دیگری که هنوز به پایان نرسیده است، مسدود شده است."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"با این کار مراحل و تمام وظایفی که در پروژه‌های زیر وجود دارند بایگانی "
"می‌شوند:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"اینها دسته‌های مختلفی از کارهایی که باید انجام دهید را نشان می‌دهند (مثلاً "
"\"تماس\" یا \"ارسال ایمیل\")."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "مدیریت زمان"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "نکته: ایجاد وظایف از ایمیل‌های ورودی"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr "نکته: از وضعیت‌های وظیفه برای پیگیری پیشرفت وظایف خود استفاده کنید"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#, python-format
msgid "Title"
msgstr "عنوان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Bill"
msgstr "به صورت فاکتور"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "جهت اقدام"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "To Invoice"
msgstr "قابل فاکتور"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "برای چاپ"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"برای انجام کارها، از فعالیت ها و وضعیت در وظایف استفاده کنید.<br>\n"
"                     چت در زمان واقعی یا از طریق ایمیل برای همکاری موثر."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""
"برای تبدیل یک کار به زیرکار، یک کار اصلی را انتخاب کنید. در غیر این صورت، "
"فیلد کار اصلی را خالی بگذارید تا زیرکار به کار مستقل تبدیل شود."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "امروز"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "فعالیت های امروز"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
#, python-format
msgid "Total"
msgstr "مجموع"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "ردیابی رضایت مشتری از وظایف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr "رسیدن به نقاط پیشرفت عمده‌ای که باید برای موفقیت رسید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr ""
"ردیابی نقاط پیشرفت عمده ای که برای دستیابی به موفقیت باید به آنها رسید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr ""
"پیگیری هزینه‌های پروژه، درآمدها و حاشیه سود با تنظیم حساب تحلیلی مرتبط با "
"پروژه بر روی اسناد مربوطه."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your project with analytic accounting. Select the"
" analytic plan for new projects:"
msgstr ""
"ردیابی سودآوری پروژه خود با حسابداری تحلیلی. طرح تحلیلی را برای پروژه‌های "
"جدید انتخاب کنید:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Track the profitability of your projects. Any project, its tasks and "
"timesheets are linked to an analytic account and any analytic account "
"belongs to a plan."
msgstr ""
"کسب سودآوری پروژه‌های خود را دنبال کنید. هر پروژه، وظایف و برگه‌های زمانی آن"
" به یک حساب تحلیلی مرتبط شده‌اند و هر حساب تحلیلی متعلق به یک طرح است."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "پیشرفت پروژه های خود را پیگیری کنید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "ردیابی زمان صرف شده برای پروژه ها و وظایف"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr "تگ‌های شفاف در نمای کانبان پروژه‌ها و وظایف شما قابل مشاهده نیستند."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "دوبار در ماه"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "Two tasks cannot depend on each other."
msgstr "دو وظیفه نمی‌توانند به یکدیگر وابسته باشند."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی برای رکورد."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid "Unarchive Projects"
msgstr "‌بایگانی نکردن پروژه‌ها"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#, python-format
msgid "Unarchive Tasks"
msgstr "الغیرفعال کردن کارها"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
#, python-format
msgid "Unassigned"
msgstr "محول نشده"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "حساب تحلیلی ناشناخته"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "پیام های ناخوانده"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "تا"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "به روز رسانی"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "به‌روزرسانی ایجاد شد"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "کاربردپذیری"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "از نقاط عطف استفاده کنید"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "استفاده از رتبه بندی در پروژه"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "استفاده از وظایف تکراری"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "استفاده از مراحل در پروژه"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "استفاده از وابستگی وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "استفاده از کار به عنوان "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "استفاده از این برای پروژه من"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "از برچسب ها برای دسته بندی وظایف خود استفاده کنید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Use the"
msgstr "استفاده کنید از"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""
"از چتر برای <b>ارسال ایمیل‌ها</b> و برقراری ارتباط مؤثر با مشتریان خود "
"استفاده کنید. افراد جدیدی را به لیست دنبال‌کنندگان اضافه کنید تا آنها را از "
"تغییرات اصلی پیرامون این وظیفه مطلع کنید."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"استفاده از این کلمات کلیدی در عنوان برای تنظیم وظایف جدید:\n"
"\n"
"۳0h تخصیص ۳۰ ساعت به وظیفه\n"
"#tags تنظیم برچسب‌ها بر روی وظیفه\n"
"@user تخصیص وظیفه به یک کاربر\n"
"! تنظیم اولویت بالا برای وظیفه\n"
"\n"
"مطمئن شوید که از فرمت و ترتیب صحیح استفاده می‎کنید، مثلاً: بهبود صفحه پیکربندی ۵h #feature #v۱۶ @Mitchell !"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "کاربر"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "نما"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "مشاهده وظیفه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "مشاهده وظایف"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "قابلیت مشاهده"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "Visible"
msgstr "نمايان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
#, python-format
msgid "Waiting"
msgstr "در انتظار"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""
"راهی بهتر برای <b>مدیریت پروژه های خود</b> می خواهید؟ <i>از اینجا شروع می "
"شود.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "پیام های وب سایت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "بازطراحی وب‌سایت"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وبسایت"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "هفتگی"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "هفته"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in edit mode, you are giving people access to the kanban and list views of your tasks.\n"
"                    Collaborators will be able to view and modify part of the tasks' information."
msgstr ""
"هنگام به اشتراک‌گذاری یک پروژه در حالت ویرایش، شما به افراد اجازه دسترسی به "
"نماهای کانبان و لیست وظایف خود را می‌دهید. همکاران قادر خواهند بود بخشی از "
"اطلاعات وظایف را مشاهده و ویرایش کنند."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"When sharing a project in read-only mode, you are giving people access to the tasks in their portal.\n"
"                    These people will be able to view the tasks, but not edit them."
msgstr ""
"هنگام به اشتراک‌گذاری پروژه در حالت فقط خواندنی، شما به افراد امکان دسترسی به وظایف در پورتالشان را می‌دهید.\n"
"این افراد قادر خواهند بود وظایف را مشاهده کنند، اما نمی‌توانند آنها را ویرایش کنند."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "تخصیص روزهای کاری"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "روزهای کاری برای بستن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "ساعات کاری برای تخصیص"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "ساعات کاری تا بسته شدن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "ساعت کاری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "زمان کاری برای واگذاری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "زمان کاری برای بستن"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr ""
"آیا می‌خواهید همه پروژه‌های موجود در این مراحل را نیز از آرشیو خارج کنید؟"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr ""
"آیا می‌خواهید همه وظایف موجود در این مراحل را نیز از بایگانی خارج کنید؟"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "Write a message..."
msgstr "نوشتن یک پیام ..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "نوشتن"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "سالیانه"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "سال"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#, python-format
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""
"شما نمی‌توانید شرکت این مرحله را به %(company_name)s تغییر دهید زیرا در حال "
"حاضر شامل پروژه‌هایی است که با %(project_company_name)s مرتبط هستند. لطفاً "
"اطمینان حاصل کنید که این مرحله به طور اختصاصی شامل پروژه‌های مرتبط با "
"%(company_name)s باشد."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You can only set a personal stage on a private task."
msgstr "شما فقط می‌توانید یک مرحله شخصی را روی یک وظیفه خصوصی تنظیم کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""
"شما نمی‌توانید مراحلی که شامل پروژه‌ها هستند را حذف کنید. می‌توانید آنها را "
"بایگانی کنید یا ابتدا تمام پروژه‌های آنها را حذف کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr ""
"شما نمی‌توانید مرحله‌هایی که شامل پروژه‌ها هستند را حذف کنید. ابتدا باید "
"تمام پروژه‌های آن‌ها را حذف کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"شما نمی توانید مراحل حاوی وظایف را حذف کنید. می توانید آنها را بایگانی کنید "
"یا ابتدا همه وظایف آنها را حذف کنید."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""
"نمی توانید مراحل حاوی وظایف را حذف کنید. ابتدا باید تمام وظایف آنها را حذف "
"کنید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "شما نمی‌توانید فیلدهای %s را در وظیفه بخوانید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "شما نمی‌توانید روی فیلدهای %s در وظیفه بنویسید."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "شما به %s اختصاص داده شده‌اید"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "شما مسئول شدید برای"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr ""
"شما کنترل کامل دارید و می‌توانید دسترسی پورتال را در هر زمانی لغو کنید. آیا "
"آماده ادامه هستید؟"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "شما دسترسی نوشتن به فیلد %s ندارید."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "You must be"
msgstr "شما باید"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "Your managers decide which feedback is accepted"
msgstr "مدیران شما تصمیم می‌گیرند که چه بازخوردی پذیرفته شود"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "and"
msgstr "و"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr "و بازخورد به ستون \"رد شده\" منتقل شده است."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "گیرندگان"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
#, python-format
msgid "avatar"
msgstr "آواتار"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "button."
msgstr "دکمه."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
#, python-format
msgid "comments"
msgstr "کامنتها"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "به عنوان مثال بررسی ماهانه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "برای مثال مهمانی کاری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "مثلاً راه‌اندازی محصول"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "به عنوان مثال ارسال دعوت‌نامه‌ها"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr "مثلاً: وظایف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr "برای انجام دادن"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. domain.com"
msgstr "به طورمثال  domain.com"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "برای مثال مهمانی کاری"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "مثال: راه‌اندازی محصول"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon to organize your daily activities."
msgstr "icon برای سازماندهی فعالیت‌های روزانه شما."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or canceled, all of its dependencies will be unblocked."
msgstr ""
"icon برای دیدن وظایفی که منتظر دیگر وظایف هستند. هنگامی که یک وظیفه به عنوان"
" کامل شده یا لغو شده علامت زده شود، تمامی وابستگی‌های آن برطرف خواهند شد."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "icon."
msgstr "آیکون."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "logged in"
msgstr "وارد شده"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "منوی نمایش وظایف من"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "به صورت دوره‌ای"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "پروژه."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "آماده برای علامت‌گذاری به عنوان رسیدن"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr "حالتی برای نشان دادن درخواست تغییرات یا نیاز به بحث در مورد یک وظیفه."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr ""
"<p>وضعیت را جهت اطلاع همکارانتان که یک وظیفه برای مرحله بعد تایید شده است "
"اعلام کنید.</p>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as canceled."
msgstr "وضعیت برای علامت‌گذاری وظیفه به عنوان لغو شده."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "state to mark the task as complete."
msgstr "وضعیت برای علامت‌گذاری وظیفه به عنوان کامل."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "state.message"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
#, python-format
msgid "sub-tasks)"
msgstr "زیر-وظایف)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#, python-format
msgid "task"
msgstr "وظیفه"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "مهلت برای نقطه عطف زیر به‌روز شده است:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "ضرب‌الاجل برای نقاط عطف زیر به‌روزرسانی شده است:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr "برای تعیین اینکه آیا پروژه برای مرحله بعد آماده است یا نه."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
#, python-format
msgid "to post a comment."
msgstr "برای ارسال یک کامنت"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
#, python-format
msgid "to signalize what is the current status of your Idea."
msgstr "برای نشان دادن وضعیت فعلی ایده شما."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "وقتی به یک مرحله معین می‌رسید"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "ماژول به طور خودکار وظایف را در سیستم شما ایجاد خواهد کرد."

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"{{ record.closed_subtask_count.value }} sub-tasks closed out of {{ "
"record.subtask_count.value }}"
msgstr ""
"{{ record.closed_subtask_count.value }} زیرکار بسته شده از {{ "
"record.subtask_count.value }}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "👤 Unassigned"
msgstr "👤 اختصاص نیافته"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
#, python-format
msgid "🔒 Private"
msgstr "خصوصی 🔒"
