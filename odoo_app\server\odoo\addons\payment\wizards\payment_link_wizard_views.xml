<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_link_wizard_view_form" model="ir.ui.view">
        <field name="name">payment.link.wizard.form</field>
        <field name="model">payment.link.wizard</field>
        <field name="arch" type="xml">
            <form string="Generate Payment Link">
                <div class="alert alert-warning fw-bold"
                     role="alert"
                     invisible="partner_email">
                     This partner has no email, which may cause issues with some payment providers.
                     Setting an email for this partner is advised.
                </div>
                <group>
                    <group>
                        <field name="res_id" invisible="1"/>
                        <field name="res_model" invisible="1"/>
                        <field name="partner_id" invisible="1"/>
                        <field name="partner_email" invisible="1"/>
                        <field name="amount_max" invisible="1"/>
                        <field name="amount"/>
                        <field name="warning_message" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                    </group>
                </group>
                <div name="payment_link_warning_information"
                     class="alert alert-warning text-center fw-bold"
                     role="alert"
                     invisible="warning_message == ''">
                    <field name="warning_message"/>
                </div>
                <footer>
                    <field name="link"
                           string="Generate and Copy Payment Link"
                           readonly="1"
                           disabled="bool(warning_message)"
                           widget="PaymentWizardCopyClipboardButtonField"
                           data-hotkey="q"/>
                    <button string="Close"
                            class="btn btn-secondary rounded-2"
                            special="cancel"
                            data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
