<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.etree.ElementTree — The ElementTree XML API" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/xml.etree.elementtree.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/xml/etree/ElementTree.py The xml.etree.ElementTree module implements a simple and efficient API for parsing and creating XML data. Tutorial: This is a short tutorial for using xml...." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/xml/etree/ElementTree.py The xml.etree.ElementTree module implements a simple and efficient API for parsing and creating XML data. Tutorial: This is a short tutorial for using xml...." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.etree.ElementTree — The ElementTree XML API &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="xml.dom — The Document Object Model API" href="xml.dom.html" />
    <link rel="prev" title="XML Processing Modules" href="xml.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/xml.etree.elementtree.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a><ul>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#xml-tree-and-elements">XML tree and elements</a></li>
<li><a class="reference internal" href="#parsing-xml">Parsing XML</a></li>
<li><a class="reference internal" href="#pull-api-for-non-blocking-parsing">Pull API for non-blocking parsing</a></li>
<li><a class="reference internal" href="#finding-interesting-elements">Finding interesting elements</a></li>
<li><a class="reference internal" href="#modifying-an-xml-file">Modifying an XML File</a></li>
<li><a class="reference internal" href="#building-xml-documents">Building XML documents</a></li>
<li><a class="reference internal" href="#parsing-xml-with-namespaces">Parsing XML with Namespaces</a></li>
</ul>
</li>
<li><a class="reference internal" href="#xpath-support">XPath support</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#supported-xpath-syntax">Supported XPath syntax</a></li>
</ul>
</li>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#xinclude-support">XInclude support</a><ul>
<li><a class="reference internal" href="#id3">Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id4">Reference</a><ul>
<li><a class="reference internal" href="#elementinclude-functions">Functions</a></li>
<li><a class="reference internal" href="#element-objects">Element Objects</a></li>
<li><a class="reference internal" href="#elementtree-objects">ElementTree Objects</a></li>
<li><a class="reference internal" href="#qname-objects">QName Objects</a></li>
<li><a class="reference internal" href="#treebuilder-objects">TreeBuilder Objects</a></li>
<li><a class="reference internal" href="#xmlparser-objects">XMLParser Objects</a></li>
<li><a class="reference internal" href="#xmlpullparser-objects">XMLPullParser Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.html"
                          title="previous chapter">XML Processing Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.etree.elementtree.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.html" title="xml.dom — The Document Object Model API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.html" title="XML Processing Modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.etree.ElementTree">
<span id="xml-etree-elementtree-the-elementtree-xml-api"></span><h1><a class="reference internal" href="#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> — The ElementTree XML API<a class="headerlink" href="#module-xml.etree.ElementTree" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/xml/etree/ElementTree.py">Lib/xml/etree/ElementTree.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> module implements a simple and efficient API
for parsing and creating XML data.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>This module will use a fast implementation whenever available.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.3: </span>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.cElementTree</span></code> module is deprecated.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <a class="reference internal" href="#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> module is not secure against
maliciously constructed data.  If you need to parse untrusted or
unauthenticated data see <a class="reference internal" href="xml.html#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a>.</p>
</div>
<section id="tutorial">
<h2>Tutorial<a class="headerlink" href="#tutorial" title="Link to this heading">¶</a></h2>
<p>This is a short tutorial for using <a class="reference internal" href="#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> (<code class="docutils literal notranslate"><span class="pre">ET</span></code> in
short).  The goal is to demonstrate some of the building blocks and basic
concepts of the module.</p>
<section id="xml-tree-and-elements">
<h3>XML tree and elements<a class="headerlink" href="#xml-tree-and-elements" title="Link to this heading">¶</a></h3>
<p>XML is an inherently hierarchical data format, and the most natural way to
represent it is with a tree.  <code class="docutils literal notranslate"><span class="pre">ET</span></code> has two classes for this purpose -
<a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> represents the whole XML document as a tree, and
<a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> represents a single node in this tree.  Interactions with
the whole document (reading and writing to/from files) are usually done
on the <a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> level.  Interactions with a single XML element
and its sub-elements are done on the <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> level.</p>
</section>
<section id="parsing-xml">
<span id="elementtree-parsing-xml"></span><h3>Parsing XML<a class="headerlink" href="#parsing-xml" title="Link to this heading">¶</a></h3>
<p>We’ll be using the fictive <code class="file docutils literal notranslate"><span class="pre">country_data.xml</span></code> XML document as the sample data for this section:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;data&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Liechtenstein&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank&gt;</span>1<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2008<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>141100<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Austria&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;E&quot;</span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Switzerland&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;W&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Singapore&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank&gt;</span>4<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2011<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>59900<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Malaysia&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;N&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Panama&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank&gt;</span>68<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2011<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>13600<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Costa Rica&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;W&quot;</span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Colombia&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;E&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="nt">&lt;/data&gt;</span>
</pre></div>
</div>
<p>We can import this data by reading from a file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xml.etree.ElementTree</span> <span class="k">as</span> <span class="nn">ET</span>
<span class="n">tree</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="s1">&#39;country_data.xml&#39;</span><span class="p">)</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">tree</span><span class="o">.</span><span class="n">getroot</span><span class="p">()</span>
</pre></div>
</div>
<p>Or directly from a string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">root</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">fromstring</span><span class="p">(</span><span class="n">country_data_as_string</span><span class="p">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#xml.etree.ElementTree.fromstring" title="xml.etree.ElementTree.fromstring"><code class="xref py py-func docutils literal notranslate"><span class="pre">fromstring()</span></code></a> parses XML from a string directly into an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>,
which is the root element of the parsed tree.  Other parsing functions may
create an <a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a>.  Check the documentation to be sure.</p>
<p>As an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>, <code class="docutils literal notranslate"><span class="pre">root</span></code> has a tag and a dictionary of attributes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">root</span><span class="o">.</span><span class="n">tag</span>
<span class="go">&#39;data&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">root</span><span class="o">.</span><span class="n">attrib</span>
<span class="go">{}</span>
</pre></div>
</div>
<p>It also has children nodes over which we can iterate:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">root</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">child</span><span class="o">.</span><span class="n">tag</span><span class="p">,</span> <span class="n">child</span><span class="o">.</span><span class="n">attrib</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">country {&#39;name&#39;: &#39;Liechtenstein&#39;}</span>
<span class="go">country {&#39;name&#39;: &#39;Singapore&#39;}</span>
<span class="go">country {&#39;name&#39;: &#39;Panama&#39;}</span>
</pre></div>
</div>
<p>Children are nested, and we can access specific child nodes by index:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">root</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">text</span>
<span class="go">&#39;2008&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Not all elements of the XML input will end up as elements of the
parsed tree. Currently, this module skips over any XML comments,
processing instructions, and document type declarations in the
input. Nevertheless, trees built using this module’s API rather
than parsing from XML text can have comments and processing
instructions in them; they will be included when generating XML
output. A document type declaration may be accessed by passing a
custom <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> instance to the <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a>
constructor.</p>
</div>
</section>
<section id="pull-api-for-non-blocking-parsing">
<span id="elementtree-pull-parsing"></span><h3>Pull API for non-blocking parsing<a class="headerlink" href="#pull-api-for-non-blocking-parsing" title="Link to this heading">¶</a></h3>
<p>Most parsing functions provided by this module require the whole document
to be read at once before returning any result.  It is possible to use an
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> and feed data into it incrementally, but it is a push API that
calls methods on a callback target, which is too low-level and inconvenient for
most needs.  Sometimes what the user really wants is to be able to parse XML
incrementally, without blocking operations, while enjoying the convenience of
fully constructed <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> objects.</p>
<p>The most powerful tool for doing this is <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser" title="xml.etree.ElementTree.XMLPullParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLPullParser</span></code></a>.  It does not
require a blocking read to obtain the XML data, and is instead fed with data
incrementally with <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.feed" title="xml.etree.ElementTree.XMLPullParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLPullParser.feed()</span></code></a> calls.  To get the parsed XML
elements, call <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.read_events" title="xml.etree.ElementTree.XMLPullParser.read_events"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLPullParser.read_events()</span></code></a>.  Here is an example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">XMLPullParser</span><span class="p">([</span><span class="s1">&#39;start&#39;</span><span class="p">,</span> <span class="s1">&#39;end&#39;</span><span class="p">])</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;mytag&gt;sometext&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">parser</span><span class="o">.</span><span class="n">read_events</span><span class="p">())</span>
<span class="go">[(&#39;start&#39;, &lt;Element &#39;mytag&#39; at 0x7fa66db2be58&gt;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39; more text&lt;/mytag&gt;&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">event</span><span class="p">,</span> <span class="n">elem</span> <span class="ow">in</span> <span class="n">parser</span><span class="o">.</span><span class="n">read_events</span><span class="p">():</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">event</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">elem</span><span class="o">.</span><span class="n">tag</span><span class="p">,</span> <span class="s1">&#39;text=&#39;</span><span class="p">,</span> <span class="n">elem</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">end</span>
<span class="go">mytag text= sometext more text</span>
</pre></div>
</div>
<p>The obvious use case is applications that operate in a non-blocking fashion
where the XML data is being received from a socket or read incrementally from
some storage device.  In such cases, blocking reads are unacceptable.</p>
<p>Because it’s so flexible, <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser" title="xml.etree.ElementTree.XMLPullParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLPullParser</span></code></a> can be inconvenient to use for
simpler use-cases.  If you don’t mind your application blocking on reading XML
data but would still like to have incremental parsing capabilities, take a look
at <a class="reference internal" href="#xml.etree.ElementTree.iterparse" title="xml.etree.ElementTree.iterparse"><code class="xref py py-func docutils literal notranslate"><span class="pre">iterparse()</span></code></a>.  It can be useful when you’re reading a large XML document
and don’t want to hold it wholly in memory.</p>
<p>Where <em>immediate</em> feedback through events is wanted, calling method
<a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.flush" title="xml.etree.ElementTree.XMLPullParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLPullParser.flush()</span></code></a> can help reduce delay;
please make sure to study the related security notes.</p>
</section>
<section id="finding-interesting-elements">
<h3>Finding interesting elements<a class="headerlink" href="#finding-interesting-elements" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> has some useful methods that help iterate recursively over all
the sub-tree below it (its children, their children, and so on).  For example,
<a class="reference internal" href="#xml.etree.ElementTree.Element.iter" title="xml.etree.ElementTree.Element.iter"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.iter()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">neighbor</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">iter</span><span class="p">(</span><span class="s1">&#39;neighbor&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">neighbor</span><span class="o">.</span><span class="n">attrib</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">{&#39;name&#39;: &#39;Austria&#39;, &#39;direction&#39;: &#39;E&#39;}</span>
<span class="go">{&#39;name&#39;: &#39;Switzerland&#39;, &#39;direction&#39;: &#39;W&#39;}</span>
<span class="go">{&#39;name&#39;: &#39;Malaysia&#39;, &#39;direction&#39;: &#39;N&#39;}</span>
<span class="go">{&#39;name&#39;: &#39;Costa Rica&#39;, &#39;direction&#39;: &#39;W&#39;}</span>
<span class="go">{&#39;name&#39;: &#39;Colombia&#39;, &#39;direction&#39;: &#39;E&#39;}</span>
</pre></div>
</div>
<p><a class="reference internal" href="#xml.etree.ElementTree.Element.findall" title="xml.etree.ElementTree.Element.findall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.findall()</span></code></a> finds only elements with a tag which are direct
children of the current element.  <a class="reference internal" href="#xml.etree.ElementTree.Element.find" title="xml.etree.ElementTree.Element.find"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.find()</span></code></a> finds the <em>first</em> child
with a particular tag, and <a class="reference internal" href="#xml.etree.ElementTree.Element.text" title="xml.etree.ElementTree.Element.text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Element.text</span></code></a> accesses the element’s text
content.  <a class="reference internal" href="#xml.etree.ElementTree.Element.get" title="xml.etree.ElementTree.Element.get"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.get()</span></code></a> accesses the element’s attributes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">country</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;country&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">rank</span> <span class="o">=</span> <span class="n">country</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s1">&#39;rank&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">text</span>
<span class="gp">... </span>    <span class="n">name</span> <span class="o">=</span> <span class="n">country</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;name&#39;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">rank</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">Liechtenstein 1</span>
<span class="go">Singapore 4</span>
<span class="go">Panama 68</span>
</pre></div>
</div>
<p>More sophisticated specification of which elements to look for is possible by
using <a class="reference internal" href="#elementtree-xpath"><span class="std std-ref">XPath</span></a>.</p>
</section>
<section id="modifying-an-xml-file">
<h3>Modifying an XML File<a class="headerlink" href="#modifying-an-xml-file" title="Link to this heading">¶</a></h3>
<p><a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> provides a simple way to build XML documents and write them to files.
The <a class="reference internal" href="#xml.etree.ElementTree.ElementTree.write" title="xml.etree.ElementTree.ElementTree.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ElementTree.write()</span></code></a> method serves this purpose.</p>
<p>Once created, an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> object may be manipulated by directly changing
its fields (such as <a class="reference internal" href="#xml.etree.ElementTree.Element.text" title="xml.etree.ElementTree.Element.text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Element.text</span></code></a>), adding and modifying attributes
(<a class="reference internal" href="#xml.etree.ElementTree.Element.set" title="xml.etree.ElementTree.Element.set"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.set()</span></code></a> method), as well as adding new children (for example
with <a class="reference internal" href="#xml.etree.ElementTree.Element.append" title="xml.etree.ElementTree.Element.append"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.append()</span></code></a>).</p>
<p>Let’s say we want to add one to each country’s rank, and add an <code class="docutils literal notranslate"><span class="pre">updated</span></code>
attribute to the rank element:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">rank</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">iter</span><span class="p">(</span><span class="s1">&#39;rank&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">new_rank</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">rank</span><span class="o">.</span><span class="n">text</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">rank</span><span class="o">.</span><span class="n">text</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">new_rank</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">rank</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="s1">&#39;updated&#39;</span><span class="p">,</span> <span class="s1">&#39;yes&#39;</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tree</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;output.xml&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Our XML now looks like this:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;data&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Liechtenstein&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank</span><span class="w"> </span><span class="na">updated=</span><span class="s">&quot;yes&quot;</span><span class="nt">&gt;</span>2<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2008<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>141100<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Austria&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;E&quot;</span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Switzerland&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;W&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Singapore&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank</span><span class="w"> </span><span class="na">updated=</span><span class="s">&quot;yes&quot;</span><span class="nt">&gt;</span>5<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2011<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>59900<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Malaysia&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;N&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Panama&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank</span><span class="w"> </span><span class="na">updated=</span><span class="s">&quot;yes&quot;</span><span class="nt">&gt;</span>69<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2011<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>13600<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Costa Rica&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;W&quot;</span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Colombia&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;E&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="nt">&lt;/data&gt;</span>
</pre></div>
</div>
<p>We can remove elements using <a class="reference internal" href="#xml.etree.ElementTree.Element.remove" title="xml.etree.ElementTree.Element.remove"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.remove()</span></code></a>.  Let’s say we want to
remove all countries with a rank higher than 50:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">country</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;country&#39;</span><span class="p">):</span>
<span class="gp">... </span>    <span class="c1"># using root.findall() to avoid removal during traversal</span>
<span class="gp">... </span>    <span class="n">rank</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">country</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s1">&#39;rank&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">if</span> <span class="n">rank</span> <span class="o">&gt;</span> <span class="mi">50</span><span class="p">:</span>
<span class="gp">... </span>        <span class="n">root</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">country</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tree</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;output.xml&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that concurrent modification while iterating can lead to problems,
just like when iterating and modifying Python lists or dicts.
Therefore, the example first collects all matching elements with
<code class="docutils literal notranslate"><span class="pre">root.findall()</span></code>, and only then iterates over the list of matches.</p>
<p>Our XML now looks like this:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;data&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Liechtenstein&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank</span><span class="w"> </span><span class="na">updated=</span><span class="s">&quot;yes&quot;</span><span class="nt">&gt;</span>2<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2008<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>141100<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Austria&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;E&quot;</span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Switzerland&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;W&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="w">    </span><span class="nt">&lt;country</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Singapore&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;rank</span><span class="w"> </span><span class="na">updated=</span><span class="s">&quot;yes&quot;</span><span class="nt">&gt;</span>5<span class="nt">&lt;/rank&gt;</span>
<span class="w">        </span><span class="nt">&lt;year&gt;</span>2011<span class="nt">&lt;/year&gt;</span>
<span class="w">        </span><span class="nt">&lt;gdppc&gt;</span>59900<span class="nt">&lt;/gdppc&gt;</span>
<span class="w">        </span><span class="nt">&lt;neighbor</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Malaysia&quot;</span><span class="w"> </span><span class="na">direction=</span><span class="s">&quot;N&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/country&gt;</span>
<span class="nt">&lt;/data&gt;</span>
</pre></div>
</div>
</section>
<section id="building-xml-documents">
<h3>Building XML documents<a class="headerlink" href="#building-xml-documents" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="#xml.etree.ElementTree.SubElement" title="xml.etree.ElementTree.SubElement"><code class="xref py py-func docutils literal notranslate"><span class="pre">SubElement()</span></code></a> function also provides a convenient way to create new
sub-elements for a given element:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">Element</span><span class="p">(</span><span class="s1">&#39;a&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">SubElement</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">SubElement</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">SubElement</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="s1">&#39;d&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ET</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
<span class="go">&lt;a&gt;&lt;b /&gt;&lt;c&gt;&lt;d /&gt;&lt;/c&gt;&lt;/a&gt;</span>
</pre></div>
</div>
</section>
<section id="parsing-xml-with-namespaces">
<h3>Parsing XML with Namespaces<a class="headerlink" href="#parsing-xml-with-namespaces" title="Link to this heading">¶</a></h3>
<p>If the XML input has <a class="reference external" href="https://en.wikipedia.org/wiki/XML_namespace">namespaces</a>, tags and attributes
with prefixes in the form <code class="docutils literal notranslate"><span class="pre">prefix:sometag</span></code> get expanded to
<code class="docutils literal notranslate"><span class="pre">{uri}sometag</span></code> where the <em>prefix</em> is replaced by the full <em>URI</em>.
Also, if there is a <a class="reference external" href="https://www.w3.org/TR/xml-names/#defaulting">default namespace</a>,
that full URI gets prepended to all of the non-prefixed tags.</p>
<p>Here is an XML example that incorporates two namespaces, one with the
prefix “fictional” and the other serving as the default namespace:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;actors</span><span class="w"> </span><span class="na">xmlns:fictional=</span><span class="s">&quot;http://characters.example.com&quot;</span>
<span class="w">        </span><span class="na">xmlns=</span><span class="s">&quot;http://people.example.com&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;actor&gt;</span>
<span class="w">        </span><span class="nt">&lt;name&gt;</span>John<span class="w"> </span>Cleese<span class="nt">&lt;/name&gt;</span>
<span class="w">        </span><span class="nt">&lt;fictional:character&gt;</span>Lancelot<span class="nt">&lt;/fictional:character&gt;</span>
<span class="w">        </span><span class="nt">&lt;fictional:character&gt;</span>Archie<span class="w"> </span>Leach<span class="nt">&lt;/fictional:character&gt;</span>
<span class="w">    </span><span class="nt">&lt;/actor&gt;</span>
<span class="w">    </span><span class="nt">&lt;actor&gt;</span>
<span class="w">        </span><span class="nt">&lt;name&gt;</span>Eric<span class="w"> </span>Idle<span class="nt">&lt;/name&gt;</span>
<span class="w">        </span><span class="nt">&lt;fictional:character&gt;</span>Sir<span class="w"> </span>Robin<span class="nt">&lt;/fictional:character&gt;</span>
<span class="w">        </span><span class="nt">&lt;fictional:character&gt;</span>Gunther<span class="nt">&lt;/fictional:character&gt;</span>
<span class="w">        </span><span class="nt">&lt;fictional:character&gt;</span>Commander<span class="w"> </span>Clement<span class="nt">&lt;/fictional:character&gt;</span>
<span class="w">    </span><span class="nt">&lt;/actor&gt;</span>
<span class="nt">&lt;/actors&gt;</span>
</pre></div>
</div>
<p>One way to search and explore this XML example is to manually add the
URI to every tag or attribute in the xpath of a
<a class="reference internal" href="#xml.etree.ElementTree.Element.find" title="xml.etree.ElementTree.Element.find"><code class="xref py py-meth docutils literal notranslate"><span class="pre">find()</span></code></a> or <a class="reference internal" href="#xml.etree.ElementTree.Element.findall" title="xml.etree.ElementTree.Element.findall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">findall()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">root</span> <span class="o">=</span> <span class="n">fromstring</span><span class="p">(</span><span class="n">xml_text</span><span class="p">)</span>
<span class="k">for</span> <span class="n">actor</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;{http://people.example.com}actor&#39;</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">actor</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s1">&#39;{http://people.example.com}name&#39;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">char</span> <span class="ow">in</span> <span class="n">actor</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;{http://characters.example.com}character&#39;</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39; |--&gt;&#39;</span><span class="p">,</span> <span class="n">char</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
</pre></div>
</div>
<p>A better way to search the namespaced XML example is to create a
dictionary with your own prefixes and use those in the search functions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">ns</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;real_person&#39;</span><span class="p">:</span> <span class="s1">&#39;http://people.example.com&#39;</span><span class="p">,</span>
      <span class="s1">&#39;role&#39;</span><span class="p">:</span> <span class="s1">&#39;http://characters.example.com&#39;</span><span class="p">}</span>

<span class="k">for</span> <span class="n">actor</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;real_person:actor&#39;</span><span class="p">,</span> <span class="n">ns</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">actor</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s1">&#39;real_person:name&#39;</span><span class="p">,</span> <span class="n">ns</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">char</span> <span class="ow">in</span> <span class="n">actor</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s1">&#39;role:character&#39;</span><span class="p">,</span> <span class="n">ns</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39; |--&gt;&#39;</span><span class="p">,</span> <span class="n">char</span><span class="o">.</span><span class="n">text</span><span class="p">)</span>
</pre></div>
</div>
<p>These two approaches both output:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">John</span> <span class="n">Cleese</span>
 <span class="o">|--&gt;</span> <span class="n">Lancelot</span>
 <span class="o">|--&gt;</span> <span class="n">Archie</span> <span class="n">Leach</span>
<span class="n">Eric</span> <span class="n">Idle</span>
 <span class="o">|--&gt;</span> <span class="n">Sir</span> <span class="n">Robin</span>
 <span class="o">|--&gt;</span> <span class="n">Gunther</span>
 <span class="o">|--&gt;</span> <span class="n">Commander</span> <span class="n">Clement</span>
</pre></div>
</div>
</section>
</section>
<section id="xpath-support">
<span id="elementtree-xpath"></span><h2>XPath support<a class="headerlink" href="#xpath-support" title="Link to this heading">¶</a></h2>
<p>This module provides limited support for
<a class="reference external" href="https://www.w3.org/TR/xpath">XPath expressions</a> for locating elements in a
tree.  The goal is to support a small subset of the abbreviated syntax; a full
XPath engine is outside the scope of the module.</p>
<section id="example">
<h3>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h3>
<p>Here’s an example that demonstrates some of the XPath capabilities of the
module.  We’ll be using the <code class="docutils literal notranslate"><span class="pre">countrydata</span></code> XML document from the
<a class="reference internal" href="#elementtree-parsing-xml"><span class="std std-ref">Parsing XML</span></a> section:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xml.etree.ElementTree</span> <span class="k">as</span> <span class="nn">ET</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">ET</span><span class="o">.</span><span class="n">fromstring</span><span class="p">(</span><span class="n">countrydata</span><span class="p">)</span>

<span class="c1"># Top-level elements</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;.&quot;</span><span class="p">)</span>

<span class="c1"># All &#39;neighbor&#39; grand-children of &#39;country&#39; children of the top-level</span>
<span class="c1"># elements</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;./country/neighbor&quot;</span><span class="p">)</span>

<span class="c1"># Nodes with name=&#39;Singapore&#39; that have a &#39;year&#39; child</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;.//year/..[@name=&#39;Singapore&#39;]&quot;</span><span class="p">)</span>

<span class="c1"># &#39;year&#39; nodes that are children of nodes with name=&#39;Singapore&#39;</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;.//*[@name=&#39;Singapore&#39;]/year&quot;</span><span class="p">)</span>

<span class="c1"># All &#39;neighbor&#39; nodes that are the second child of their parent</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;.//neighbor[2]&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>For XML with namespaces, use the usual qualified <code class="docutils literal notranslate"><span class="pre">{namespace}tag</span></code> notation:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># All dublin-core &quot;title&quot; tags in the document</span>
<span class="n">root</span><span class="o">.</span><span class="n">findall</span><span class="p">(</span><span class="s2">&quot;.//{http://purl.org/dc/elements/1.1/}title&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="supported-xpath-syntax">
<h3>Supported XPath syntax<a class="headerlink" href="#supported-xpath-syntax" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Syntax</p></th>
<th class="head"><p>Meaning</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">tag</span></code></p></td>
<td><p>Selects all child elements with the given tag.
For example, <code class="docutils literal notranslate"><span class="pre">spam</span></code> selects all child elements
named <code class="docutils literal notranslate"><span class="pre">spam</span></code>, and <code class="docutils literal notranslate"><span class="pre">spam/egg</span></code> selects all
grandchildren named <code class="docutils literal notranslate"><span class="pre">egg</span></code> in all children named
<code class="docutils literal notranslate"><span class="pre">spam</span></code>.  <code class="docutils literal notranslate"><span class="pre">{namespace}*</span></code> selects all tags in the
given namespace, <code class="docutils literal notranslate"><span class="pre">{*}spam</span></code> selects tags named
<code class="docutils literal notranslate"><span class="pre">spam</span></code> in any (or no) namespace, and <code class="docutils literal notranslate"><span class="pre">{}*</span></code>
only selects tags that are not in a namespace.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Support for star-wildcards was added.</p>
</div>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">*</span></code></p></td>
<td><p>Selects all child elements, including comments and
processing instructions.  For example, <code class="docutils literal notranslate"><span class="pre">*/egg</span></code>
selects all grandchildren named <code class="docutils literal notranslate"><span class="pre">egg</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">.</span></code></p></td>
<td><p>Selects the current node.  This is mostly useful
at the beginning of the path, to indicate that it’s
a relative path.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">//</span></code></p></td>
<td><p>Selects all subelements, on all levels beneath the
current  element.  For example, <code class="docutils literal notranslate"><span class="pre">.//egg</span></code> selects
all <code class="docutils literal notranslate"><span class="pre">egg</span></code> elements in the entire tree.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">..</span></code></p></td>
<td><p>Selects the parent element.  Returns <code class="docutils literal notranslate"><span class="pre">None</span></code> if the
path attempts to reach the ancestors of the start
element (the element <code class="docutils literal notranslate"><span class="pre">find</span></code> was called on).</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">[&#64;attrib]</span></code></p></td>
<td><p>Selects all elements that have the given attribute.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">[&#64;attrib='value']</span></code></p></td>
<td><p>Selects all elements for which the given attribute
has the given value.  The value cannot contain
quotes.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">[&#64;attrib!='value']</span></code></p></td>
<td><p>Selects all elements for which the given attribute
does not have the given value. The value cannot
contain quotes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">[tag]</span></code></p></td>
<td><p>Selects all elements that have a child named
<code class="docutils literal notranslate"><span class="pre">tag</span></code>.  Only immediate children are supported.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">[.='text']</span></code></p></td>
<td><p>Selects all elements whose complete text content,
including descendants, equals the given <code class="docutils literal notranslate"><span class="pre">text</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">[.!='text']</span></code></p></td>
<td><p>Selects all elements whose complete text content,
including descendants, does not equal the given
<code class="docutils literal notranslate"><span class="pre">text</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">[tag='text']</span></code></p></td>
<td><p>Selects all elements that have a child named
<code class="docutils literal notranslate"><span class="pre">tag</span></code> whose complete text content, including
descendants, equals the given <code class="docutils literal notranslate"><span class="pre">text</span></code>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">[tag!='text']</span></code></p></td>
<td><p>Selects all elements that have a child named
<code class="docutils literal notranslate"><span class="pre">tag</span></code> whose complete text content, including
descendants, does not equal the given <code class="docutils literal notranslate"><span class="pre">text</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">[position]</span></code></p></td>
<td><p>Selects all elements that are located at the given
position.  The position can be either an integer
(1 is the first position), the expression <code class="docutils literal notranslate"><span class="pre">last()</span></code>
(for the last position), or a position relative to
the last position (e.g. <code class="docutils literal notranslate"><span class="pre">last()-1</span></code>).</p></td>
</tr>
</tbody>
</table>
<p>Predicates (expressions within square brackets) must be preceded by a tag
name, an asterisk, or another predicate.  <code class="docutils literal notranslate"><span class="pre">position</span></code> predicates must be
preceded by a tag name.</p>
</section>
</section>
<section id="reference">
<h2>Reference<a class="headerlink" href="#reference" title="Link to this heading">¶</a></h2>
<section id="functions">
<span id="elementtree-functions"></span><h3>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.canonicalize">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">canonicalize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">xml_data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">out</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">from_file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">options</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.canonicalize" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference external" href="https://www.w3.org/TR/xml-c14n2/">C14N 2.0</a> transformation function.</p>
<p>Canonicalization is a way to normalise XML output in a way that allows
byte-by-byte comparisons and digital signatures.  It reduced the freedom
that XML serializers have and instead generates a more constrained XML
representation.  The main restrictions regard the placement of namespace
declarations, the ordering of attributes, and ignorable whitespace.</p>
<p>This function takes an XML data string (<em>xml_data</em>) or a file path or
file-like object (<em>from_file</em>) as input, converts it to the canonical
form, and writes it out using the <em>out</em> file(-like) object, if provided,
or returns it as a text string if not.  The output file receives text,
not bytes.  It should therefore be opened in text mode with <code class="docutils literal notranslate"><span class="pre">utf-8</span></code>
encoding.</p>
<p>Typical uses:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">xml_data</span> <span class="o">=</span> <span class="s2">&quot;&lt;root&gt;...&lt;/root&gt;&quot;</span>
<span class="nb">print</span><span class="p">(</span><span class="n">canonicalize</span><span class="p">(</span><span class="n">xml_data</span><span class="p">))</span>

<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;c14n_output.xml&quot;</span><span class="p">,</span> <span class="n">mode</span><span class="o">=</span><span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">out_file</span><span class="p">:</span>
    <span class="n">canonicalize</span><span class="p">(</span><span class="n">xml_data</span><span class="p">,</span> <span class="n">out</span><span class="o">=</span><span class="n">out_file</span><span class="p">)</span>

<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;c14n_output.xml&quot;</span><span class="p">,</span> <span class="n">mode</span><span class="o">=</span><span class="s1">&#39;w&#39;</span><span class="p">,</span> <span class="n">encoding</span><span class="o">=</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">out_file</span><span class="p">:</span>
    <span class="n">canonicalize</span><span class="p">(</span><span class="n">from_file</span><span class="o">=</span><span class="s2">&quot;inputfile.xml&quot;</span><span class="p">,</span> <span class="n">out</span><span class="o">=</span><span class="n">out_file</span><span class="p">)</span>
</pre></div>
</div>
<p>The configuration <em>options</em> are as follows:</p>
<ul class="simple">
<li><p><em>with_comments</em>: set to true to include comments (default: false)</p></li>
<li><dl class="simple">
<dt><em>strip_text</em>: set to true to strip whitespace before and after text content</dt><dd><p>(default: false)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><em>rewrite_prefixes</em>: set to true to replace namespace prefixes by “n{number}”</dt><dd><p>(default: false)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><em>qname_aware_tags</em>: a set of qname aware tag names in which prefixes</dt><dd><p>should be replaced in text content (default: empty)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><em>qname_aware_attrs</em>: a set of qname aware attribute names in which prefixes</dt><dd><p>should be replaced in text content (default: empty)</p>
</dd>
</dl>
</li>
<li><p><em>exclude_attrs</em>: a set of attribute names that should not be serialised</p></li>
<li><p><em>exclude_tags</em>: a set of tag names that should not be serialised</p></li>
</ul>
<p>In the option list above, “a set” refers to any collection or iterable of
strings, no ordering is expected.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Comment">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">Comment</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Comment" title="Link to this definition">¶</a></dt>
<dd><p>Comment element factory.  This factory function creates a special element
that will be serialized as an XML comment by the standard serializer.  The
comment string can be either a bytestring or a Unicode string.  <em>text</em> is a
string containing the comment string.  Returns an element instance
representing a comment.</p>
<p>Note that <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> skips over comments in the input
instead of creating comment objects for them. An <a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> will
only contain comment nodes if they have been inserted into to
the tree using one of the <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> methods.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.dump">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">dump</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.dump" title="Link to this definition">¶</a></dt>
<dd><p>Writes an element tree or element structure to sys.stdout.  This function
should be used for debugging only.</p>
<p>The exact output format is implementation dependent.  In this version, it’s
written as an ordinary XML file.</p>
<p><em>elem</em> is an element tree or an individual element.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.etree.ElementTree.dump" title="xml.etree.ElementTree.dump"><code class="xref py py-func docutils literal notranslate"><span class="pre">dump()</span></code></a> function now preserves the attribute order specified
by the user.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.fromstring">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">fromstring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.fromstring" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML section from a string constant.  Same as <a class="reference internal" href="#xml.etree.ElementTree.XML" title="xml.etree.ElementTree.XML"><code class="xref py py-func docutils literal notranslate"><span class="pre">XML()</span></code></a>.  <em>text</em>
is a string containing XML data.  <em>parser</em> is an optional parser instance.
If not given, the standard <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.
Returns an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.fromstringlist">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">fromstringlist</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.fromstringlist" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML document from a sequence of string fragments.  <em>sequence</em> is a
list or other sequence containing XML data fragments.  <em>parser</em> is an
optional parser instance.  If not given, the standard <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a>
parser is used.  Returns an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.indent">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">indent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tree</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">space</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span>&#160; <span class="pre">'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.indent" title="Link to this definition">¶</a></dt>
<dd><p>Appends whitespace to the subtree to indent the tree visually.
This can be used to generate pretty-printed XML output.
<em>tree</em> can be an Element or ElementTree.  <em>space</em> is the whitespace
string that will be inserted for each indentation level, two space
characters by default.  For indenting partial subtrees inside of an
already indented tree, pass the initial indentation level as <em>level</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.iselement">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">iselement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.iselement" title="Link to this definition">¶</a></dt>
<dd><p>Check if an object appears to be a valid element object.  <em>element</em> is an
element instance.  Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if this is an element object.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.iterparse">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">iterparse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">events</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.iterparse" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML section into an element tree incrementally, and reports what’s
going on to the user.  <em>source</em> is a filename or <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>
containing XML data.  <em>events</em> is a sequence of events to report back.  The
supported events are the strings <code class="docutils literal notranslate"><span class="pre">&quot;start&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;end&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;comment&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;pi&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;start-ns&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;end-ns&quot;</span></code>
(the “ns” events are used to get detailed namespace
information).  If <em>events</em> is omitted, only <code class="docutils literal notranslate"><span class="pre">&quot;end&quot;</span></code> events are reported.
<em>parser</em> is an optional parser instance.  If not given, the standard
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.  <em>parser</em> must be a subclass of
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> and can only use the default <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> as a
target. Returns an <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> providing <code class="docutils literal notranslate"><span class="pre">(event,</span> <span class="pre">elem)</span></code> pairs;
it has a <code class="docutils literal notranslate"><span class="pre">root</span></code> attribute that references the root element of the
resulting XML tree once <em>source</em> is fully read.</p>
<p>Note that while <a class="reference internal" href="#xml.etree.ElementTree.iterparse" title="xml.etree.ElementTree.iterparse"><code class="xref py py-func docutils literal notranslate"><span class="pre">iterparse()</span></code></a> builds the tree incrementally, it issues
blocking reads on <em>source</em> (or the file it names).  As such, it’s unsuitable
for applications where blocking reads can’t be made.  For fully non-blocking
parsing, see <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser" title="xml.etree.ElementTree.XMLPullParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLPullParser</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#xml.etree.ElementTree.iterparse" title="xml.etree.ElementTree.iterparse"><code class="xref py py-func docutils literal notranslate"><span class="pre">iterparse()</span></code></a> only guarantees that it has seen the “&gt;” character of a
starting tag when it emits a “start” event, so the attributes are defined,
but the contents of the text and tail attributes are undefined at that
point.  The same applies to the element children; they may or may not be
present.</p>
<p>If you need a fully populated element, look for “end” events instead.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.4: </span>The <em>parser</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <code class="docutils literal notranslate"><span class="pre">comment</span></code> and <code class="docutils literal notranslate"><span class="pre">pi</span></code> events were added.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.parse">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.parse" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML section into an element tree.  <em>source</em> is a filename or file
object containing XML data.  <em>parser</em> is an optional parser instance.  If
not given, the standard <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.  Returns an
<a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> instance.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ProcessingInstruction">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">ProcessingInstruction</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ProcessingInstruction" title="Link to this definition">¶</a></dt>
<dd><p>PI element factory.  This factory function creates a special element that
will be serialized as an XML processing instruction.  <em>target</em> is a string
containing the PI target.  <em>text</em> is a string containing the PI contents, if
given.  Returns an element instance, representing a processing instruction.</p>
<p>Note that <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> skips over processing instructions
in the input instead of creating PI objects for them. An
<a class="reference internal" href="#xml.etree.ElementTree.ElementTree" title="xml.etree.ElementTree.ElementTree"><code class="xref py py-class docutils literal notranslate"><span class="pre">ElementTree</span></code></a> will only contain processing instruction nodes if
they have been inserted into to the tree using one of the
<a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> methods.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.register_namespace">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">register_namespace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.register_namespace" title="Link to this definition">¶</a></dt>
<dd><p>Registers a namespace prefix.  The registry is global, and any existing
mapping for either the given prefix or the namespace URI will be removed.
<em>prefix</em> is a namespace prefix.  <em>uri</em> is a namespace uri.  Tags and
attributes in this namespace will be serialized with the given prefix, if at
all possible.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.SubElement">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">SubElement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrib</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">extra</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.SubElement" title="Link to this definition">¶</a></dt>
<dd><p>Subelement factory.  This function creates an element instance, and appends
it to an existing element.</p>
<p>The element name, attribute names, and attribute values can be either
bytestrings or Unicode strings.  <em>parent</em> is the parent element.  <em>tag</em> is
the subelement name.  <em>attrib</em> is an optional dictionary, containing element
attributes.  <em>extra</em> contains additional attributes, given as keyword
arguments.  Returns an element instance.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.tostring">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">tostring</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'us-ascii'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'xml'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">xml_declaration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_namespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">short_empty_elements</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.tostring" title="Link to this definition">¶</a></dt>
<dd><p>Generates a string representation of an XML element, including all
subelements.  <em>element</em> is an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.  <em>encoding</em> <a class="footnote-reference brackets" href="#id9" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> is
the output encoding (default is US-ASCII).  Use <code class="docutils literal notranslate"><span class="pre">encoding=&quot;unicode&quot;</span></code> to
generate a Unicode string (otherwise, a bytestring is generated).  <em>method</em>
is either <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;html&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;text&quot;</span></code> (default is <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>).
<em>xml_declaration</em>, <em>default_namespace</em> and <em>short_empty_elements</em> has the same
meaning as in <a class="reference internal" href="#xml.etree.ElementTree.ElementTree.write" title="xml.etree.ElementTree.ElementTree.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ElementTree.write()</span></code></a>. Returns an (optionally) encoded string
containing the XML data.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>short_empty_elements</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>xml_declaration</em> and <em>default_namespace</em> parameters.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.etree.ElementTree.tostring" title="xml.etree.ElementTree.tostring"><code class="xref py py-func docutils literal notranslate"><span class="pre">tostring()</span></code></a> function now preserves the attribute order
specified by the user.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.tostringlist">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">tostringlist</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'us-ascii'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'xml'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">xml_declaration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_namespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">short_empty_elements</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.tostringlist" title="Link to this definition">¶</a></dt>
<dd><p>Generates a string representation of an XML element, including all
subelements.  <em>element</em> is an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.  <em>encoding</em> <a class="footnote-reference brackets" href="#id9" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> is
the output encoding (default is US-ASCII).  Use <code class="docutils literal notranslate"><span class="pre">encoding=&quot;unicode&quot;</span></code> to
generate a Unicode string (otherwise, a bytestring is generated).  <em>method</em>
is either <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;html&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;text&quot;</span></code> (default is <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>).
<em>xml_declaration</em>, <em>default_namespace</em> and <em>short_empty_elements</em> has the same
meaning as in <a class="reference internal" href="#xml.etree.ElementTree.ElementTree.write" title="xml.etree.ElementTree.ElementTree.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ElementTree.write()</span></code></a>. Returns a list of (optionally) encoded
strings containing the XML data. It does not guarantee any specific sequence,
except that <code class="docutils literal notranslate"><span class="pre">b&quot;&quot;.join(tostringlist(element))</span> <span class="pre">==</span> <span class="pre">tostring(element)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>short_empty_elements</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>xml_declaration</em> and <em>default_namespace</em> parameters.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.etree.ElementTree.tostringlist" title="xml.etree.ElementTree.tostringlist"><code class="xref py py-func docutils literal notranslate"><span class="pre">tostringlist()</span></code></a> function now preserves the attribute order
specified by the user.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XML">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">XML</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XML" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML section from a string constant.  This function can be used to
embed “XML literals” in Python code.  <em>text</em> is a string containing XML
data.  <em>parser</em> is an optional parser instance.  If not given, the standard
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.  Returns an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLID">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">XMLID</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLID" title="Link to this definition">¶</a></dt>
<dd><p>Parses an XML section from a string constant, and also returns a dictionary
which maps from element id:s to elements.  <em>text</em> is a string containing XML
data.  <em>parser</em> is an optional parser instance.  If not given, the standard
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.  Returns a tuple containing an
<a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance and a dictionary.</p>
</dd></dl>

</section>
</section>
<section id="xinclude-support">
<span id="elementtree-xinclude"></span><h2>XInclude support<a class="headerlink" href="#xinclude-support" title="Link to this heading">¶</a></h2>
<p>This module provides limited support for
<a class="reference external" href="https://www.w3.org/TR/xinclude/">XInclude directives</a>, via the <a class="reference internal" href="#module-xml.etree.ElementInclude" title="xml.etree.ElementInclude"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementInclude</span></code></a> helper module.  This module can be used to insert subtrees and text strings into element trees, based on information in the tree.</p>
<section id="id3">
<h3>Example<a class="headerlink" href="#id3" title="Link to this heading">¶</a></h3>
<p>Here’s an example that demonstrates use of the XInclude module. To include an XML document in the current document, use the <code class="docutils literal notranslate"><span class="pre">{http://www.w3.org/2001/XInclude}include</span></code> element and set the <strong>parse</strong> attribute to <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, and use the <strong>href</strong> attribute to specify the document to include.</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;document</span><span class="w"> </span><span class="na">xmlns:xi=</span><span class="s">&quot;http://www.w3.org/2001/XInclude&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;xi:include</span><span class="w"> </span><span class="na">href=</span><span class="s">&quot;source.xml&quot;</span><span class="w"> </span><span class="na">parse=</span><span class="s">&quot;xml&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="nt">&lt;/document&gt;</span>
</pre></div>
</div>
<p>By default, the <strong>href</strong> attribute is treated as a file name. You can use custom loaders to override this behaviour. Also note that the standard helper does not support XPointer syntax.</p>
<p>To process this file, load it as usual, and pass the root element to the <a class="reference internal" href="#module-xml.etree.ElementTree" title="xml.etree.ElementTree: Implementation of the ElementTree API."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code></a> module:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xml.etree</span> <span class="kn">import</span> <span class="n">ElementTree</span><span class="p">,</span> <span class="n">ElementInclude</span>

<span class="n">tree</span> <span class="o">=</span> <span class="n">ElementTree</span><span class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="s2">&quot;document.xml&quot;</span><span class="p">)</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">tree</span><span class="o">.</span><span class="n">getroot</span><span class="p">()</span>

<span class="n">ElementInclude</span><span class="o">.</span><span class="n">include</span><span class="p">(</span><span class="n">root</span><span class="p">)</span>
</pre></div>
</div>
<p>The ElementInclude module replaces the <code class="docutils literal notranslate"><span class="pre">{http://www.w3.org/2001/XInclude}include</span></code> element with the root element from the <strong>source.xml</strong> document. The result might look something like this:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;document</span><span class="w"> </span><span class="na">xmlns:xi=</span><span class="s">&quot;http://www.w3.org/2001/XInclude&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;para&gt;</span>This<span class="w"> </span>is<span class="w"> </span>a<span class="w"> </span>paragraph.<span class="nt">&lt;/para&gt;</span>
<span class="nt">&lt;/document&gt;</span>
</pre></div>
</div>
<p>If the <strong>parse</strong> attribute is omitted, it defaults to “xml”. The href attribute is required.</p>
<p>To include a text document, use the <code class="docutils literal notranslate"><span class="pre">{http://www.w3.org/2001/XInclude}include</span></code> element, and set the <strong>parse</strong> attribute to “text”:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;document</span><span class="w"> </span><span class="na">xmlns:xi=</span><span class="s">&quot;http://www.w3.org/2001/XInclude&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span>Copyright<span class="w"> </span>(c)<span class="w"> </span><span class="nt">&lt;xi:include</span><span class="w"> </span><span class="na">href=</span><span class="s">&quot;year.txt&quot;</span><span class="w"> </span><span class="na">parse=</span><span class="s">&quot;text&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>.
<span class="nt">&lt;/document&gt;</span>
</pre></div>
</div>
<p>The result might look something like:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;document</span><span class="w"> </span><span class="na">xmlns:xi=</span><span class="s">&quot;http://www.w3.org/2001/XInclude&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span>Copyright<span class="w"> </span>(c)<span class="w"> </span>2003.
<span class="nt">&lt;/document&gt;</span>
</pre></div>
</div>
</section>
</section>
<section id="id4">
<h2>Reference<a class="headerlink" href="#id4" title="Link to this heading">¶</a></h2>
<section id="elementinclude-functions">
<span id="id5"></span><h3>Functions<a class="headerlink" href="#elementinclude-functions" title="Link to this heading">¶</a></h3>
<dl class="py function" id="module-xml.etree.ElementInclude">
<dt class="sig sig-object py" id="xml.etree.ElementInclude.default_loader">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementInclude.</span></span><span class="sig-name descname"><span class="pre">default_loader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">href</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parse</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementInclude.default_loader" title="Link to this definition">¶</a></dt>
<dd><p>Default loader. This default loader reads an included resource from disk.  <em>href</em> is a URL.
<em>parse</em> is for parse mode either “xml” or “text”.  <em>encoding</em>
is an optional text encoding.  If not given, encoding is <code class="docutils literal notranslate"><span class="pre">utf-8</span></code>.  Returns the
expanded resource.  If the parse mode is <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, this is an ElementTree
instance.  If the parse mode is “text”, this is a Unicode string.  If the
loader fails, it can return None or raise an exception.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.etree.ElementInclude.include">
<span class="sig-prename descclassname"><span class="pre">xml.etree.ElementInclude.</span></span><span class="sig-name descname"><span class="pre">include</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elem</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">loader</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base_url</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_depth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">6</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementInclude.include" title="Link to this definition">¶</a></dt>
<dd><p>This function expands XInclude directives.  <em>elem</em> is the root element.  <em>loader</em> is
an optional resource loader.  If omitted, it defaults to <a class="reference internal" href="#xml.etree.ElementInclude.default_loader" title="xml.etree.ElementInclude.default_loader"><code class="xref py py-func docutils literal notranslate"><span class="pre">default_loader()</span></code></a>.
If given, it should be a callable that implements the same interface as
<a class="reference internal" href="#xml.etree.ElementInclude.default_loader" title="xml.etree.ElementInclude.default_loader"><code class="xref py py-func docutils literal notranslate"><span class="pre">default_loader()</span></code></a>.  <em>base_url</em> is base URL of the original file, to resolve
relative include file references.  <em>max_depth</em> is the maximum number of recursive
inclusions.  Limited to reduce the risk of malicious content explosion. Pass a
negative value to disable the limitation.</p>
<p>Returns the expanded resource.  If the parse mode is
<code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, this is an ElementTree instance.  If the parse mode is “text”,
this is a Unicode string.  If the loader fails, it can return None or
raise an exception.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added the <em>base_url</em> and <em>max_depth</em> parameters.</p>
</div>
</dd></dl>

</section>
<section id="element-objects">
<span id="elementtree-element-objects"></span><h3>Element Objects<a class="headerlink" href="#element-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">Element</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrib</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">extra</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element" title="Link to this definition">¶</a></dt>
<dd><p>Element class.  This class defines the Element interface, and provides a
reference implementation of this interface.</p>
<p>The element name, attribute names, and attribute values can be either
bytestrings or Unicode strings.  <em>tag</em> is the element name.  <em>attrib</em> is
an optional dictionary, containing element attributes.  <em>extra</em> contains
additional attributes, given as keyword arguments.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.tag">
<span class="sig-name descname"><span class="pre">tag</span></span><a class="headerlink" href="#xml.etree.ElementTree.Element.tag" title="Link to this definition">¶</a></dt>
<dd><p>A string identifying what kind of data this element represents (the
element type, in other words).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.text">
<span class="sig-name descname"><span class="pre">text</span></span><a class="headerlink" href="#xml.etree.ElementTree.Element.text" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.tail">
<span class="sig-name descname"><span class="pre">tail</span></span><a class="headerlink" href="#xml.etree.ElementTree.Element.tail" title="Link to this definition">¶</a></dt>
<dd><p>These attributes can be used to hold additional data associated with
the element.  Their values are usually strings but may be any
application-specific object.  If the element is created from
an XML file, the <em>text</em> attribute holds either the text between
the element’s start tag and its first child or end tag, or <code class="docutils literal notranslate"><span class="pre">None</span></code>, and
the <em>tail</em> attribute holds either the text between the element’s
end tag and the next tag, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  For the XML data</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;a&gt;&lt;b&gt;</span>1<span class="nt">&lt;c&gt;</span>2<span class="nt">&lt;d/&gt;</span>3<span class="nt">&lt;/c&gt;&lt;/b&gt;</span>4<span class="nt">&lt;/a&gt;</span>
</pre></div>
</div>
<p>the <em>a</em> element has <code class="docutils literal notranslate"><span class="pre">None</span></code> for both <em>text</em> and <em>tail</em> attributes,
the <em>b</em> element has <em>text</em> <code class="docutils literal notranslate"><span class="pre">&quot;1&quot;</span></code> and <em>tail</em> <code class="docutils literal notranslate"><span class="pre">&quot;4&quot;</span></code>,
the <em>c</em> element has <em>text</em> <code class="docutils literal notranslate"><span class="pre">&quot;2&quot;</span></code> and <em>tail</em> <code class="docutils literal notranslate"><span class="pre">None</span></code>,
and the <em>d</em> element has <em>text</em> <code class="docutils literal notranslate"><span class="pre">None</span></code> and <em>tail</em> <code class="docutils literal notranslate"><span class="pre">&quot;3&quot;</span></code>.</p>
<p>To collect the inner text of an element, see <a class="reference internal" href="#xml.etree.ElementTree.Element.itertext" title="xml.etree.ElementTree.Element.itertext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">itertext()</span></code></a>, for
example <code class="docutils literal notranslate"><span class="pre">&quot;&quot;.join(element.itertext())</span></code>.</p>
<p>Applications may store arbitrary objects in these attributes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.attrib">
<span class="sig-name descname"><span class="pre">attrib</span></span><a class="headerlink" href="#xml.etree.ElementTree.Element.attrib" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary containing the element’s attributes.  Note that while the
<em>attrib</em> value is always a real mutable Python dictionary, an ElementTree
implementation may choose to use another internal representation, and
create the dictionary only if someone asks for it.  To take advantage of
such implementations, use the dictionary methods below whenever possible.</p>
</dd></dl>

<p>The following dictionary-like methods work on the element attributes.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.clear">
<span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.clear" title="Link to this definition">¶</a></dt>
<dd><p>Resets an element.  This function removes all subelements, clears all
attributes, and sets the text and tail attributes to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.get" title="Link to this definition">¶</a></dt>
<dd><p>Gets the element attribute named <em>key</em>.</p>
<p>Returns the attribute value, or <em>default</em> if the attribute was not found.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.items">
<span class="sig-name descname"><span class="pre">items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.items" title="Link to this definition">¶</a></dt>
<dd><p>Returns the element attributes as a sequence of (name, value) pairs.  The
attributes are returned in an arbitrary order.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.keys">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.keys" title="Link to this definition">¶</a></dt>
<dd><p>Returns the elements attribute names as a list.  The names are returned
in an arbitrary order.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.set">
<span class="sig-name descname"><span class="pre">set</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.set" title="Link to this definition">¶</a></dt>
<dd><p>Set the attribute <em>key</em> on the element to <em>value</em>.</p>
</dd></dl>

<p>The following methods work on the element’s children (subelements).</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.append">
<span class="sig-name descname"><span class="pre">append</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subelement</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.append" title="Link to this definition">¶</a></dt>
<dd><p>Adds the element <em>subelement</em> to the end of this element’s internal list
of subelements.  Raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if <em>subelement</em> is not an
<a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.extend">
<span class="sig-name descname"><span class="pre">extend</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subelements</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.extend" title="Link to this definition">¶</a></dt>
<dd><p>Appends <em>subelements</em> from a sequence object with zero or more elements.
Raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if a subelement is not an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.find">
<span class="sig-name descname"><span class="pre">find</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.find" title="Link to this definition">¶</a></dt>
<dd><p>Finds the first subelement matching <em>match</em>.  <em>match</em> may be a tag name
or a <a class="reference internal" href="#elementtree-xpath"><span class="std std-ref">path</span></a>.  Returns an element instance
or <code class="docutils literal notranslate"><span class="pre">None</span></code>.  <em>namespaces</em> is an optional mapping from namespace prefix
to full name.  Pass <code class="docutils literal notranslate"><span class="pre">''</span></code> as prefix to move all unprefixed tag names
in the expression into the given namespace.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.findall">
<span class="sig-name descname"><span class="pre">findall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.findall" title="Link to this definition">¶</a></dt>
<dd><p>Finds all matching subelements, by tag name or
<a class="reference internal" href="#elementtree-xpath"><span class="std std-ref">path</span></a>.  Returns a list containing all matching
elements in document order.  <em>namespaces</em> is an optional mapping from
namespace prefix to full name.  Pass <code class="docutils literal notranslate"><span class="pre">''</span></code> as prefix to move all
unprefixed tag names in the expression into the given namespace.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.findtext">
<span class="sig-name descname"><span class="pre">findtext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.findtext" title="Link to this definition">¶</a></dt>
<dd><p>Finds text for the first subelement matching <em>match</em>.  <em>match</em> may be
a tag name or a <a class="reference internal" href="#elementtree-xpath"><span class="std std-ref">path</span></a>.  Returns the text content
of the first matching element, or <em>default</em> if no element was found.
Note that if the matching element has no text content an empty string
is returned. <em>namespaces</em> is an optional mapping from namespace prefix
to full name.  Pass <code class="docutils literal notranslate"><span class="pre">''</span></code> as prefix to move all unprefixed tag names
in the expression into the given namespace.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.insert">
<span class="sig-name descname"><span class="pre">insert</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">index</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subelement</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.insert" title="Link to this definition">¶</a></dt>
<dd><p>Inserts <em>subelement</em> at the given position in this element.  Raises
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if <em>subelement</em> is not an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.iter">
<span class="sig-name descname"><span class="pre">iter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.iter" title="Link to this definition">¶</a></dt>
<dd><p>Creates a tree <a class="reference internal" href="../glossary.html#term-iterator"><span class="xref std std-term">iterator</span></a> with the current element as the root.
The iterator iterates over this element and all elements below it, in
document (depth first) order.  If <em>tag</em> is not <code class="docutils literal notranslate"><span class="pre">None</span></code> or <code class="docutils literal notranslate"><span class="pre">'*'</span></code>, only
elements whose tag equals <em>tag</em> are returned from the iterator.  If the
tree structure is modified during iteration, the result is undefined.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.iterfind">
<span class="sig-name descname"><span class="pre">iterfind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.iterfind" title="Link to this definition">¶</a></dt>
<dd><p>Finds all matching subelements, by tag name or
<a class="reference internal" href="#elementtree-xpath"><span class="std std-ref">path</span></a>.  Returns an iterable yielding all
matching elements in document order. <em>namespaces</em> is an optional mapping
from namespace prefix to full name.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.itertext">
<span class="sig-name descname"><span class="pre">itertext</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.itertext" title="Link to this definition">¶</a></dt>
<dd><p>Creates a text iterator.  The iterator loops over this element and all
subelements, in document order, and returns all inner text.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.makeelement">
<span class="sig-name descname"><span class="pre">makeelement</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrib</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.makeelement" title="Link to this definition">¶</a></dt>
<dd><p>Creates a new element object of the same type as this element.  Do not
call this method, use the <a class="reference internal" href="#xml.etree.ElementTree.SubElement" title="xml.etree.ElementTree.SubElement"><code class="xref py py-func docutils literal notranslate"><span class="pre">SubElement()</span></code></a> factory function instead.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.Element.remove">
<span class="sig-name descname"><span class="pre">remove</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subelement</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.Element.remove" title="Link to this definition">¶</a></dt>
<dd><p>Removes <em>subelement</em> from the element.  Unlike the find* methods this
method compares elements based on the instance identity, not on tag value
or contents.</p>
</dd></dl>

<p><a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> objects also support the following sequence type methods
for working with subelements: <a class="reference internal" href="../reference/datamodel.html#object.__delitem__" title="object.__delitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__delitem__()</span></code></a>,
<a class="reference internal" href="../reference/datamodel.html#object.__getitem__" title="object.__getitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__getitem__()</span></code></a>, <a class="reference internal" href="../reference/datamodel.html#object.__setitem__" title="object.__setitem__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__setitem__()</span></code></a>,
<a class="reference internal" href="../reference/datamodel.html#object.__len__" title="object.__len__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__len__()</span></code></a>.</p>
<p>Caution: Elements with no subelements will test as <code class="docutils literal notranslate"><span class="pre">False</span></code>.  Testing the
truth value of an Element is deprecated and will raise an exception in
Python 3.14.  Use specific <code class="docutils literal notranslate"><span class="pre">len(elem)</span></code> or <code class="docutils literal notranslate"><span class="pre">elem</span> <span class="pre">is</span> <span class="pre">None</span></code> test instead.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">element</span> <span class="o">=</span> <span class="n">root</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span>

<span class="k">if</span> <span class="ow">not</span> <span class="n">element</span><span class="p">:</span>  <span class="c1"># careful!</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;element not found, or element has no subelements&quot;</span><span class="p">)</span>

<span class="k">if</span> <span class="n">element</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;element not found&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Testing the truth value of an Element emits <a class="reference internal" href="exceptions.html#DeprecationWarning" title="DeprecationWarning"><code class="xref py py-exc docutils literal notranslate"><span class="pre">DeprecationWarning</span></code></a>.</p>
</div>
<p>Prior to Python 3.8, the serialisation order of the XML attributes of
elements was artificially made predictable by sorting the attributes by
their name. Based on the now guaranteed ordering of dicts, this arbitrary
reordering was removed in Python 3.8 to preserve the order in which
attributes were originally parsed or created by user code.</p>
<p>In general, user code should try not to depend on a specific ordering of
attributes, given that the <a class="reference external" href="https://www.w3.org/TR/xml-infoset/">XML Information Set</a> explicitly excludes the attribute
order from conveying information. Code should be prepared to deal with
any ordering on input. In cases where deterministic XML output is required,
e.g. for cryptographic signing or test data sets, canonical serialisation
is available with the <a class="reference internal" href="#xml.etree.ElementTree.canonicalize" title="xml.etree.ElementTree.canonicalize"><code class="xref py py-func docutils literal notranslate"><span class="pre">canonicalize()</span></code></a> function.</p>
<p>In cases where canonical output is not applicable but a specific attribute
order is still desirable on output, code should aim for creating the
attributes directly in the desired order, to avoid perceptual mismatches
for readers of the code. In cases where this is difficult to achieve, a
recipe like the following can be applied prior to serialisation to enforce
an order independently from the Element creation:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">reorder_attributes</span><span class="p">(</span><span class="n">root</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">el</span> <span class="ow">in</span> <span class="n">root</span><span class="o">.</span><span class="n">iter</span><span class="p">():</span>
        <span class="n">attrib</span> <span class="o">=</span> <span class="n">el</span><span class="o">.</span><span class="n">attrib</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">attrib</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="c1"># adjust attribute order, e.g. by sorting</span>
            <span class="n">attribs</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">attrib</span><span class="o">.</span><span class="n">items</span><span class="p">())</span>
            <span class="n">attrib</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">attrib</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">attribs</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="elementtree-objects">
<span id="elementtree-elementtree-objects"></span><h3>ElementTree Objects<a class="headerlink" href="#elementtree-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">ElementTree</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree" title="Link to this definition">¶</a></dt>
<dd><p>ElementTree wrapper class.  This class represents an entire element
hierarchy, and adds some extra support for serialization to and from
standard XML.</p>
<p><em>element</em> is the root element.  The tree is initialized with the contents
of the XML <em>file</em> if given.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree._setroot">
<span class="sig-name descname"><span class="pre">_setroot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree._setroot" title="Link to this definition">¶</a></dt>
<dd><p>Replaces the root element for this tree.  This discards the current
contents of the tree, and replaces it with the given element.  Use with
care.  <em>element</em> is an element instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.find">
<span class="sig-name descname"><span class="pre">find</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.find" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#xml.etree.ElementTree.Element.find" title="xml.etree.ElementTree.Element.find"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.find()</span></code></a>, starting at the root of the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.findall">
<span class="sig-name descname"><span class="pre">findall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.findall" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#xml.etree.ElementTree.Element.findall" title="xml.etree.ElementTree.Element.findall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.findall()</span></code></a>, starting at the root of the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.findtext">
<span class="sig-name descname"><span class="pre">findtext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.findtext" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#xml.etree.ElementTree.Element.findtext" title="xml.etree.ElementTree.Element.findtext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.findtext()</span></code></a>, starting at the root of the tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.getroot">
<span class="sig-name descname"><span class="pre">getroot</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.getroot" title="Link to this definition">¶</a></dt>
<dd><p>Returns the root element for this tree.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.iter">
<span class="sig-name descname"><span class="pre">iter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.iter" title="Link to this definition">¶</a></dt>
<dd><p>Creates and returns a tree iterator for the root element.  The iterator
loops over all elements in this tree, in section order.  <em>tag</em> is the tag
to look for (default is to return all elements).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.iterfind">
<span class="sig-name descname"><span class="pre">iterfind</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">match</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespaces</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.iterfind" title="Link to this definition">¶</a></dt>
<dd><p>Same as <a class="reference internal" href="#xml.etree.ElementTree.Element.iterfind" title="xml.etree.ElementTree.Element.iterfind"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Element.iterfind()</span></code></a>, starting at the root of the tree.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.parse">
<span class="sig-name descname"><span class="pre">parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">source</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parser</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.parse" title="Link to this definition">¶</a></dt>
<dd><p>Loads an external XML section into this element tree.  <em>source</em> is a file
name or <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>.  <em>parser</em> is an optional parser instance.
If not given, the standard <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> parser is used.  Returns the
section root element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ElementTree.write">
<span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'us-ascii'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">xml_declaration</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default_namespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'xml'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">short_empty_elements</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.ElementTree.write" title="Link to this definition">¶</a></dt>
<dd><p>Writes the element tree to a file, as XML.  <em>file</em> is a file name, or a
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> opened for writing.  <em>encoding</em> <a class="footnote-reference brackets" href="#id9" id="id6" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> is the output
encoding (default is US-ASCII).
<em>xml_declaration</em> controls if an XML declaration should be added to the
file.  Use <code class="docutils literal notranslate"><span class="pre">False</span></code> for never, <code class="docutils literal notranslate"><span class="pre">True</span></code> for always, <code class="docutils literal notranslate"><span class="pre">None</span></code>
for only if not US-ASCII or UTF-8 or Unicode (default is <code class="docutils literal notranslate"><span class="pre">None</span></code>).
<em>default_namespace</em> sets the default XML namespace (for “xmlns”).
<em>method</em> is either <code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;html&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;text&quot;</span></code> (default is
<code class="docutils literal notranslate"><span class="pre">&quot;xml&quot;</span></code>).
The keyword-only <em>short_empty_elements</em> parameter controls the formatting
of elements that contain no content.  If <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default), they are
emitted as a single self-closed tag, otherwise they are emitted as a pair
of start/end tags.</p>
<p>The output is either a string (<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>) or binary (<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>).
This is controlled by the <em>encoding</em> argument.  If <em>encoding</em> is
<code class="docutils literal notranslate"><span class="pre">&quot;unicode&quot;</span></code>, the output is a string; otherwise, it’s binary.  Note that
this may conflict with the type of <em>file</em> if it’s an open
<a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a>; make sure you do not try to write a string to a
binary stream and vice versa.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>short_empty_elements</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <a class="reference internal" href="#xml.etree.ElementTree.ElementTree.write" title="xml.etree.ElementTree.ElementTree.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> method now preserves the attribute order specified
by the user.</p>
</div>
</dd></dl>

</dd></dl>

<p>This is the XML file that is going to be manipulated:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&lt;</span><span class="n">html</span><span class="o">&gt;</span>
    <span class="o">&lt;</span><span class="n">head</span><span class="o">&gt;</span>
        <span class="o">&lt;</span><span class="n">title</span><span class="o">&gt;</span><span class="n">Example</span> <span class="n">page</span><span class="o">&lt;/</span><span class="n">title</span><span class="o">&gt;</span>
    <span class="o">&lt;/</span><span class="n">head</span><span class="o">&gt;</span>
    <span class="o">&lt;</span><span class="n">body</span><span class="o">&gt;</span>
        <span class="o">&lt;</span><span class="n">p</span><span class="o">&gt;</span><span class="n">Moved</span> <span class="n">to</span> <span class="o">&lt;</span><span class="n">a</span> <span class="n">href</span><span class="o">=</span><span class="s2">&quot;http://example.org/&quot;</span><span class="o">&gt;</span><span class="n">example</span><span class="o">.</span><span class="n">org</span><span class="o">&lt;/</span><span class="n">a</span><span class="o">&gt;</span>
        <span class="ow">or</span> <span class="o">&lt;</span><span class="n">a</span> <span class="n">href</span><span class="o">=</span><span class="s2">&quot;http://example.com/&quot;</span><span class="o">&gt;</span><span class="n">example</span><span class="o">.</span><span class="n">com</span><span class="o">&lt;/</span><span class="n">a</span><span class="o">&gt;.&lt;/</span><span class="n">p</span><span class="o">&gt;</span>
    <span class="o">&lt;/</span><span class="n">body</span><span class="o">&gt;</span>
<span class="o">&lt;/</span><span class="n">html</span><span class="o">&gt;</span>
</pre></div>
</div>
<p>Example of changing the attribute “target” of every link in first paragraph:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">xml.etree.ElementTree</span> <span class="kn">import</span> <span class="n">ElementTree</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tree</span> <span class="o">=</span> <span class="n">ElementTree</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tree</span><span class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="s2">&quot;index.xhtml&quot;</span><span class="p">)</span>
<span class="go">&lt;Element &#39;html&#39; at 0xb77e6fac&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">tree</span><span class="o">.</span><span class="n">find</span><span class="p">(</span><span class="s2">&quot;body/p&quot;</span><span class="p">)</span>     <span class="c1"># Finds first occurrence of tag p in body</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span>
<span class="go">&lt;Element &#39;p&#39; at 0xb77ec26c&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">links</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">iter</span><span class="p">(</span><span class="s2">&quot;a&quot;</span><span class="p">))</span>   <span class="c1"># Returns list of all links</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">links</span>
<span class="go">[&lt;Element &#39;a&#39; at 0xb77ec2ac&gt;, &lt;Element &#39;a&#39; at 0xb77ec1cc&gt;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">links</span><span class="p">:</span>             <span class="c1"># Iterates through all found links</span>
<span class="gp">... </span>    <span class="n">i</span><span class="o">.</span><span class="n">attrib</span><span class="p">[</span><span class="s2">&quot;target&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;blank&quot;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tree</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;output.xhtml&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="qname-objects">
<span id="elementtree-qname-objects"></span><h3>QName Objects<a class="headerlink" href="#qname-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.QName">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">QName</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text_or_uri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tag</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.QName" title="Link to this definition">¶</a></dt>
<dd><p>QName wrapper.  This can be used to wrap a QName attribute value, in order
to get proper namespace handling on output.  <em>text_or_uri</em> is a string
containing the QName value, in the form {uri}local, or, if the tag argument
is given, the URI part of a QName.  If <em>tag</em> is given, the first argument is
interpreted as a URI, and this argument is interpreted as a local name.
<a class="reference internal" href="#xml.etree.ElementTree.QName" title="xml.etree.ElementTree.QName"><code class="xref py py-class docutils literal notranslate"><span class="pre">QName</span></code></a> instances are opaque.</p>
</dd></dl>

</section>
<section id="treebuilder-objects">
<span id="elementtree-treebuilder-objects"></span><h3>TreeBuilder Objects<a class="headerlink" href="#treebuilder-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">TreeBuilder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">element_factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">comment_factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pi_factory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">insert_comments</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">insert_pis</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder" title="Link to this definition">¶</a></dt>
<dd><p>Generic element structure builder.  This builder converts a sequence of
start, data, end, comment and pi method calls to a well-formed element
structure.  You can use this class to build an element structure using
a custom XML parser, or a parser for some other XML-like format.</p>
<p><em>element_factory</em>, when given, must be a callable accepting two positional
arguments: a tag and a dict of attributes.  It is expected to return a new
element instance.</p>
<p>The <em>comment_factory</em> and <em>pi_factory</em> functions, when given, should behave
like the <a class="reference internal" href="#xml.etree.ElementTree.Comment" title="xml.etree.ElementTree.Comment"><code class="xref py py-func docutils literal notranslate"><span class="pre">Comment()</span></code></a> and <a class="reference internal" href="#xml.etree.ElementTree.ProcessingInstruction" title="xml.etree.ElementTree.ProcessingInstruction"><code class="xref py py-func docutils literal notranslate"><span class="pre">ProcessingInstruction()</span></code></a> functions to
create comments and processing instructions.  When not given, the default
factories will be used.  When <em>insert_comments</em> and/or <em>insert_pis</em> is true,
comments/pis will be inserted into the tree if they appear within the root
element (but not outside of it).</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.close" title="Link to this definition">¶</a></dt>
<dd><p>Flushes the builder buffers, and returns the toplevel document
element.  Returns an <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> instance.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.data">
<span class="sig-name descname"><span class="pre">data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.data" title="Link to this definition">¶</a></dt>
<dd><p>Adds text to the current element.  <em>data</em> is a string.  This should be
either a bytestring, or a Unicode string.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.end">
<span class="sig-name descname"><span class="pre">end</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.end" title="Link to this definition">¶</a></dt>
<dd><p>Closes the current element.  <em>tag</em> is the element name.  Returns the
closed element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.start">
<span class="sig-name descname"><span class="pre">start</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.start" title="Link to this definition">¶</a></dt>
<dd><p>Opens a new element.  <em>tag</em> is the element name.  <em>attrs</em> is a dictionary
containing element attributes.  Returns the opened element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.comment">
<span class="sig-name descname"><span class="pre">comment</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.comment" title="Link to this definition">¶</a></dt>
<dd><p>Creates a comment with the given <em>text</em>.  If <code class="docutils literal notranslate"><span class="pre">insert_comments</span></code> is true,
this will also add it to the tree.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.pi">
<span class="sig-name descname"><span class="pre">pi</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.pi" title="Link to this definition">¶</a></dt>
<dd><p>Creates a process instruction with the given <em>target</em> name and <em>text</em>.
If <code class="docutils literal notranslate"><span class="pre">insert_pis</span></code> is true, this will also add it to the tree.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<p>In addition, a custom <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> object can provide the
following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.doctype">
<span class="sig-name descname"><span class="pre">doctype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pubid</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">system</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.doctype" title="Link to this definition">¶</a></dt>
<dd><p>Handles a doctype declaration.  <em>name</em> is the doctype name.  <em>pubid</em> is
the public identifier.  <em>system</em> is the system identifier.  This method
does not exist on the default <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> class.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.start_ns">
<span class="sig-name descname"><span class="pre">start_ns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.start_ns" title="Link to this definition">¶</a></dt>
<dd><p>Is called whenever the parser encounters a new namespace declaration,
before the <code class="docutils literal notranslate"><span class="pre">start()</span></code> callback for the opening element that defines it.
<em>prefix</em> is <code class="docutils literal notranslate"><span class="pre">''</span></code> for the default namespace and the declared
namespace prefix name otherwise.  <em>uri</em> is the namespace URI.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.TreeBuilder.end_ns">
<span class="sig-name descname"><span class="pre">end_ns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.TreeBuilder.end_ns" title="Link to this definition">¶</a></dt>
<dd><p>Is called after the <code class="docutils literal notranslate"><span class="pre">end()</span></code> callback of an element that declared
a namespace prefix mapping, with the name of the <em>prefix</em> that went
out of scope.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.C14NWriterTarget">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">C14NWriterTarget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">write</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">with_comments</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strip_text</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rewrite_prefixes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qname_aware_tags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">qname_aware_attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_attrs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exclude_tags</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.C14NWriterTarget" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference external" href="https://www.w3.org/TR/xml-c14n2/">C14N 2.0</a> writer.  Arguments are the
same as for the <a class="reference internal" href="#xml.etree.ElementTree.canonicalize" title="xml.etree.ElementTree.canonicalize"><code class="xref py py-func docutils literal notranslate"><span class="pre">canonicalize()</span></code></a> function.  This class does not build a
tree but translates the callback events directly into a serialised form
using the <em>write</em> function.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</section>
<section id="xmlparser-objects">
<span id="elementtree-xmlparser-objects"></span><h3>XMLParser Objects<a class="headerlink" href="#xmlparser-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">XMLParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLParser" title="Link to this definition">¶</a></dt>
<dd><p>This class is the low-level building block of the module.  It uses
<a class="reference internal" href="pyexpat.html#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> for efficient, event-based parsing of XML.  It can
be fed XML data incrementally with the <a class="reference internal" href="#xml.etree.ElementTree.XMLParser.feed" title="xml.etree.ElementTree.XMLParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">feed()</span></code></a> method, and parsing
events are translated to a push API - by invoking callbacks on the <em>target</em>
object.  If <em>target</em> is omitted, the standard <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> is used.
If <em>encoding</em> <a class="footnote-reference brackets" href="#id9" id="id8" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> is given, the value overrides the
encoding specified in the XML file.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Parameters are now <a class="reference internal" href="../glossary.html#keyword-only-parameter"><span class="std std-ref">keyword-only</span></a>.
The <em>html</em> argument no longer supported.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLParser.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLParser.close" title="Link to this definition">¶</a></dt>
<dd><p>Finishes feeding data to the parser.  Returns the result of calling the
<code class="docutils literal notranslate"><span class="pre">close()</span></code> method of the <em>target</em> passed during construction; by default,
this is the toplevel document element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLParser.feed">
<span class="sig-name descname"><span class="pre">feed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLParser.feed" title="Link to this definition">¶</a></dt>
<dd><p>Feeds data to the parser.  <em>data</em> is encoded data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLParser.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLParser.flush" title="Link to this definition">¶</a></dt>
<dd><p>Triggers parsing of any previously fed unparsed data, which can be
used to ensure more immediate feedback, in particular with Expat &gt;=2.6.0.
The implementation of <a class="reference internal" href="#xml.etree.ElementTree.XMLParser.flush" title="xml.etree.ElementTree.XMLParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> temporarily disables reparse deferral
with Expat (if currently enabled) and triggers a reparse.
Disabling reparse deferral has security consequences; please see
<a class="reference internal" href="pyexpat.html#xml.parsers.expat.xmlparser.SetReparseDeferralEnabled" title="xml.parsers.expat.xmlparser.SetReparseDeferralEnabled"><code class="xref py py-meth docutils literal notranslate"><span class="pre">xml.parsers.expat.xmlparser.SetReparseDeferralEnabled()</span></code></a> for details.</p>
<p>Note that <a class="reference internal" href="#xml.etree.ElementTree.XMLParser.flush" title="xml.etree.ElementTree.XMLParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> has been backported to some prior releases of
CPython as a security fix.  Check for availability of <a class="reference internal" href="#xml.etree.ElementTree.XMLParser.flush" title="xml.etree.ElementTree.XMLParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>
using <a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> if used in code running across a variety of Python
versions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.3.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#xml.etree.ElementTree.XMLParser.feed" title="xml.etree.ElementTree.XMLParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLParser.feed()</span></code></a> calls <em>target</em>'s <code class="docutils literal notranslate"><span class="pre">start(tag,</span> <span class="pre">attrs_dict)</span></code> method
for each opening tag, its <code class="docutils literal notranslate"><span class="pre">end(tag)</span></code> method for each closing tag, and data
is processed by method <code class="docutils literal notranslate"><span class="pre">data(data)</span></code>.  For further supported callback
methods, see the <a class="reference internal" href="#xml.etree.ElementTree.TreeBuilder" title="xml.etree.ElementTree.TreeBuilder"><code class="xref py py-class docutils literal notranslate"><span class="pre">TreeBuilder</span></code></a> class.  <a class="reference internal" href="#xml.etree.ElementTree.XMLParser.close" title="xml.etree.ElementTree.XMLParser.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLParser.close()</span></code></a> calls
<em>target</em>'s method <code class="docutils literal notranslate"><span class="pre">close()</span></code>. <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a> can be used not only for
building a tree structure. This is an example of counting the maximum depth
of an XML file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">xml.etree.ElementTree</span> <span class="kn">import</span> <span class="n">XMLParser</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MaxDepth</span><span class="p">:</span>                     <span class="c1"># The target object of the parser</span>
<span class="gp">... </span>    <span class="n">maxDepth</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">... </span>    <span class="n">depth</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">start</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">,</span> <span class="n">attrib</span><span class="p">):</span>   <span class="c1"># Called for each opening tag.</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">depth</span> <span class="o">+=</span> <span class="mi">1</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">depth</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">maxDepth</span><span class="p">:</span>
<span class="gp">... </span>            <span class="bp">self</span><span class="o">.</span><span class="n">maxDepth</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">depth</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">end</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">):</span>             <span class="c1"># Called for each closing tag.</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">depth</span> <span class="o">-=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">data</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">pass</span>            <span class="c1"># We do not need to do anything with data.</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">close</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>    <span class="c1"># Called when all data has been parsed.</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">maxDepth</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">target</span> <span class="o">=</span> <span class="n">MaxDepth</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span> <span class="o">=</span> <span class="n">XMLParser</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">target</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">exampleXml</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;</span>
<span class="gp">... </span><span class="s2">&lt;a&gt;</span>
<span class="gp">... </span><span class="s2">  &lt;b&gt;</span>
<span class="gp">... </span><span class="s2">  &lt;/b&gt;</span>
<span class="gp">... </span><span class="s2">  &lt;b&gt;</span>
<span class="gp">... </span><span class="s2">    &lt;c&gt;</span>
<span class="gp">... </span><span class="s2">      &lt;d&gt;</span>
<span class="gp">... </span><span class="s2">      &lt;/d&gt;</span>
<span class="gp">... </span><span class="s2">    &lt;/c&gt;</span>
<span class="gp">... </span><span class="s2">  &lt;/b&gt;</span>
<span class="gp">... </span><span class="s2">&lt;/a&gt;&quot;&quot;&quot;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="n">exampleXml</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="go">4</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="xmlpullparser-objects">
<span id="elementtree-xmlpullparser-objects"></span><h3>XMLPullParser Objects<a class="headerlink" href="#xmlpullparser-objects" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLPullParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">XMLPullParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">events</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLPullParser" title="Link to this definition">¶</a></dt>
<dd><p>A pull parser suitable for non-blocking applications.  Its input-side API is
similar to that of <a class="reference internal" href="#xml.etree.ElementTree.XMLParser" title="xml.etree.ElementTree.XMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLParser</span></code></a>, but instead of pushing calls to a
callback target, <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser" title="xml.etree.ElementTree.XMLPullParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLPullParser</span></code></a> collects an internal list of parsing
events and lets the user read from it. <em>events</em> is a sequence of events to
report back.  The supported events are the strings <code class="docutils literal notranslate"><span class="pre">&quot;start&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;end&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;comment&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;pi&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;start-ns&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;end-ns&quot;</span></code> (the “ns” events
are used to get detailed namespace information).  If <em>events</em> is omitted,
only <code class="docutils literal notranslate"><span class="pre">&quot;end&quot;</span></code> events are reported.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLPullParser.feed">
<span class="sig-name descname"><span class="pre">feed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLPullParser.feed" title="Link to this definition">¶</a></dt>
<dd><p>Feed the given bytes data to the parser.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLPullParser.flush">
<span class="sig-name descname"><span class="pre">flush</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLPullParser.flush" title="Link to this definition">¶</a></dt>
<dd><p>Triggers parsing of any previously fed unparsed data, which can be
used to ensure more immediate feedback, in particular with Expat &gt;=2.6.0.
The implementation of <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.flush" title="xml.etree.ElementTree.XMLPullParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> temporarily disables reparse deferral
with Expat (if currently enabled) and triggers a reparse.
Disabling reparse deferral has security consequences; please see
<a class="reference internal" href="pyexpat.html#xml.parsers.expat.xmlparser.SetReparseDeferralEnabled" title="xml.parsers.expat.xmlparser.SetReparseDeferralEnabled"><code class="xref py py-meth docutils literal notranslate"><span class="pre">xml.parsers.expat.xmlparser.SetReparseDeferralEnabled()</span></code></a> for details.</p>
<p>Note that <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.flush" title="xml.etree.ElementTree.XMLPullParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a> has been backported to some prior releases of
CPython as a security fix.  Check for availability of <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.flush" title="xml.etree.ElementTree.XMLPullParser.flush"><code class="xref py py-meth docutils literal notranslate"><span class="pre">flush()</span></code></a>
using <a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> if used in code running across a variety of Python
versions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLPullParser.close">
<span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLPullParser.close" title="Link to this definition">¶</a></dt>
<dd><p>Signal the parser that the data stream is terminated. Unlike
<a class="reference internal" href="#xml.etree.ElementTree.XMLParser.close" title="xml.etree.ElementTree.XMLParser.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">XMLParser.close()</span></code></a>, this method always returns <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>.
Any events not yet retrieved when the parser is closed can still be
read with <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.read_events" title="xml.etree.ElementTree.XMLPullParser.read_events"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read_events()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.etree.ElementTree.XMLPullParser.read_events">
<span class="sig-name descname"><span class="pre">read_events</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.etree.ElementTree.XMLPullParser.read_events" title="Link to this definition">¶</a></dt>
<dd><p>Return an iterator over the events which have been encountered in the
data fed to the
parser.  The iterator yields <code class="docutils literal notranslate"><span class="pre">(event,</span> <span class="pre">elem)</span></code> pairs, where <em>event</em> is a
string representing the type of event (e.g. <code class="docutils literal notranslate"><span class="pre">&quot;end&quot;</span></code>) and <em>elem</em> is the
encountered <a class="reference internal" href="#xml.etree.ElementTree.Element" title="xml.etree.ElementTree.Element"><code class="xref py py-class docutils literal notranslate"><span class="pre">Element</span></code></a> object, or other context value as follows.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">start</span></code>, <code class="docutils literal notranslate"><span class="pre">end</span></code>: the current Element.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">comment</span></code>, <code class="docutils literal notranslate"><span class="pre">pi</span></code>: the current comment / processing instruction</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">start-ns</span></code>: a tuple <code class="docutils literal notranslate"><span class="pre">(prefix,</span> <span class="pre">uri)</span></code> naming the declared namespace
mapping.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">end-ns</span></code>: <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> (this may change in a future version)</p></li>
</ul>
<p>Events provided in a previous call to <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.read_events" title="xml.etree.ElementTree.XMLPullParser.read_events"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read_events()</span></code></a> will not be
yielded again.  Events are consumed from the internal queue only when
they are retrieved from the iterator, so multiple readers iterating in
parallel over iterators obtained from <a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser.read_events" title="xml.etree.ElementTree.XMLPullParser.read_events"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read_events()</span></code></a> will have
unpredictable results.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#xml.etree.ElementTree.XMLPullParser" title="xml.etree.ElementTree.XMLPullParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">XMLPullParser</span></code></a> only guarantees that it has seen the “&gt;”
character of a starting tag when it emits a “start” event, so the
attributes are defined, but the contents of the text and tail attributes
are undefined at that point.  The same applies to the element children;
they may or may not be present.</p>
<p>If you need a fully populated element, look for “end” events instead.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <code class="docutils literal notranslate"><span class="pre">comment</span></code> and <code class="docutils literal notranslate"><span class="pre">pi</span></code> events were added.</p>
</div>
</dd></dl>

</section>
<section id="exceptions">
<h3>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ParseError">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.etree.ElementTree.</span></span><span class="sig-name descname"><span class="pre">ParseError</span></span><a class="headerlink" href="#xml.etree.ElementTree.ParseError" title="Link to this definition">¶</a></dt>
<dd><p>XML parse error, raised by the various parsing methods in this module when
parsing fails.  The string representation of an instance of this exception
will contain a user-friendly error message.  In addition, it will have
the following attributes available:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ParseError.code">
<span class="sig-name descname"><span class="pre">code</span></span><a class="headerlink" href="#xml.etree.ElementTree.ParseError.code" title="Link to this definition">¶</a></dt>
<dd><p>A numeric error code from the expat parser. See the documentation of
<a class="reference internal" href="pyexpat.html#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> for the list of error codes and their meanings.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.etree.ElementTree.ParseError.position">
<span class="sig-name descname"><span class="pre">position</span></span><a class="headerlink" href="#xml.etree.ElementTree.ParseError.position" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of <em>line</em>, <em>column</em> numbers, specifying where the error occurred.</p>
</dd></dl>

</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id9" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id1">1</a>,<a role="doc-backlink" href="#id2">2</a>,<a role="doc-backlink" href="#id6">3</a>,<a role="doc-backlink" href="#id8">4</a>)</span>
<p>The encoding string included in XML output should conform to the
appropriate standards.  For example, “UTF-8” is valid, but “UTF8” is
not.  See <a class="reference external" href="https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl">https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl</a>
and <a class="reference external" href="https://www.iana.org/assignments/character-sets/character-sets.xhtml">https://www.iana.org/assignments/character-sets/character-sets.xhtml</a>.</p>
</aside>
</aside>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a><ul>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#xml-tree-and-elements">XML tree and elements</a></li>
<li><a class="reference internal" href="#parsing-xml">Parsing XML</a></li>
<li><a class="reference internal" href="#pull-api-for-non-blocking-parsing">Pull API for non-blocking parsing</a></li>
<li><a class="reference internal" href="#finding-interesting-elements">Finding interesting elements</a></li>
<li><a class="reference internal" href="#modifying-an-xml-file">Modifying an XML File</a></li>
<li><a class="reference internal" href="#building-xml-documents">Building XML documents</a></li>
<li><a class="reference internal" href="#parsing-xml-with-namespaces">Parsing XML with Namespaces</a></li>
</ul>
</li>
<li><a class="reference internal" href="#xpath-support">XPath support</a><ul>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#supported-xpath-syntax">Supported XPath syntax</a></li>
</ul>
</li>
<li><a class="reference internal" href="#reference">Reference</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#xinclude-support">XInclude support</a><ul>
<li><a class="reference internal" href="#id3">Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id4">Reference</a><ul>
<li><a class="reference internal" href="#elementinclude-functions">Functions</a></li>
<li><a class="reference internal" href="#element-objects">Element Objects</a></li>
<li><a class="reference internal" href="#elementtree-objects">ElementTree Objects</a></li>
<li><a class="reference internal" href="#qname-objects">QName Objects</a></li>
<li><a class="reference internal" href="#treebuilder-objects">TreeBuilder Objects</a></li>
<li><a class="reference internal" href="#xmlparser-objects">XMLParser Objects</a></li>
<li><a class="reference internal" href="#xmlpullparser-objects">XMLPullParser Objects</a></li>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.html"
                          title="previous chapter">XML Processing Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="xml.dom.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.dom</span></code> — The Document Object Model API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/xml.etree.elementtree.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="xml.dom.html" title="xml.dom — The Document Object Model API"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.html" title="XML Processing Modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.etree.ElementTree</span></code> — The ElementTree XML API</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>