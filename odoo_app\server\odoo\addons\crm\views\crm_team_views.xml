<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- CRM lead search by Salesteams -->
        <record id="crm_case_form_view_salesteams_lead" model="ir.actions.act_window">
            <field name="name">Leads</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">['|', ('type','=','lead'), ('type','=',False)]</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'tree', 'view_id': ref('crm_case_tree_view_leads')}),
                          (0, 0, {'view_mode': 'kanban', 'view_id': ref('view_crm_lead_kanban')})]"/>
            <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'lead',
                }
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new lead
                </p><p>
                    Use leads if you need a qualification step before creating an
                    opportunity or a customer. It can be a business card you received,
                    a contact form filled in your website, or a file of unqualified
                    prospects you import, etc.
                </p>
            </field>
        </record>

        <!-- CRM opportunity search by Salesteams -->
        <record id="crm_case_form_view_salesteams_opportunity" model="ir.actions.act_window">
            <field name="name">Opportunities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,tree,graph,form,calendar,pivot</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="view_id" ref="crm.crm_case_kanban_view_leads"/>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
                }
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new lead
                </p><p>
                    Odoo helps you keep track of your sales pipeline to follow
                    up potential sales and better forecast your future revenues.
                </p><p>
                    You will be able to plan meetings and phone calls from
                    opportunities, convert them into quotations, attach related
                    documents, track all discussions, and much more.
                </p>
            </field>
        </record>

         <record id="crm_lead_action_team_overdue_opportunity" model="ir.actions.act_window">
            <field name="name">Overdue Opportunities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,tree,graph,form,calendar,pivot</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="view_id" ref="crm.crm_case_kanban_view_leads"/>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'search_default_overdue_opp': 1,
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
                }
            </field>
        </record>

       <record id="action_report_crm_lead_salesteam" model="ir.actions.act_window">
           <field name="name">Leads Analysis</field>
           <field name="res_model">crm.lead</field>
           <field name="context">{'search_default_team_id': [active_id], 'search_default_filter_create_date': 1}</field>
           <field name="domain">[]</field>
           <field name="view_mode">graph,pivot,tree,form</field>
           <field name="view_id" ref="crm_lead_view_graph"/>
           <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
           <field name="help">Leads Analysis allows you to check different CRM related information like the treatment delays or number of leads per state. You can sort out your leads analysis by different groups to get accurate grained analysis.</field>
       </record>
        <record id="action_report_crm_lead_salesteam_view_graph" model="ir.actions.act_window.view">
            <field name="sequence">2</field>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>
        <record id="action_report_crm_lead_salesteam_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence">3</field>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>
        <record id="action_report_crm_lead_salesteam_view_tree" model="ir.actions.act_window.view">
            <field name="sequence">4</field>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="crm_lead_view_tree_reporting"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>

       <record id="action_report_crm_opportunity_salesteam" model="ir.actions.act_window">
            <field name="name">Pipeline Analysis</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">graph,pivot,tree,form</field>
            <field name="search_view_id" ref="crm.crm_opportunity_report_view_search"/>
            <field name="context">{
                'search_default_team_id': [active_id],
                'tree_view_ref': 'crm.crm_lead_view_tree_opportunity_reporting',
                'search_default_filter_opportunity': True,
                'search_default_filter_create_date': 1}</field>
            <field name="domain">[]</field>
            <field name="help">Opportunities Analysis gives you an instant access to your opportunities with information such as the expected revenue, planned cost, missed deadlines or the number of interactions per opportunity. This report is mainly used by the sales manager in order to do the periodic review with the channels of the sales pipeline.</field>
        </record>

        <record id="crm_team_view_tree" model="ir.ui.view">
            <field name="name">crm.team.tree.inherit.crm</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_tree"/>
            <field name="arch" type="xml">
                <field name="name" position="after">
                    <field string="Alias" name="alias_id"/>
                </field>
            </field>
        </record>

        <record id="sales_team_form_view_in_crm" model="ir.ui.view">
            <field name="name">crm.team.form.inherit</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_form"/>
            <field name="priority">12</field>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="before">
                    <field name="use_leads" invisible="1"/>
                    <field name="use_opportunities" invisible="1"/>
                    <header>
                        <button name="action_assign_leads" type="object"
                            string="Assign Leads"
                            class="oe_highlight"
                            confirm="This will assign leads to all members. Do you want to proceed?"
                            invisible="not use_leads and not use_opportunities or not assignment_enabled"
                            confirm-label="Assign Leads"/>
                    </header>
                </xpath>
                <xpath expr="//div[@name='options_active']" position="inside">
                    <span name="opportunities" groups="crm.group_use_lead">
                        <field name="use_opportunities"/>
                        <label for="use_opportunities"/>
                    </span>
                    <span name="leads" groups="crm.group_use_lead">
                        <field name="use_leads"/>
                        <label for="use_leads" string="Leads"/>
                    </span>
                </xpath>
                <xpath expr="//field[@name='user_id']" position="after">
                    <label for="alias_name" string="Email Alias"
                           invisible="not use_leads and not use_opportunities"/>
                    <div name="alias_def" invisible="not use_leads and not use_opportunities">
                        <field name="alias_id" string="Email Alias" class="oe_read_only" required="0"/>
                        <div class="oe_edit_only" name="edit_alias" dir="ltr">
                            <field name="alias_name" class="oe_inline"/>@
                            <field name="alias_domain_id" class="oe_inline" placeholder="e.g. domain.com"
                                   options="{'no_create': True, 'no_open': True}"/>
                        </div>
                    </div>
                    <field name="alias_contact"
                        string="Accept Emails From"
                        invisible="not use_leads and not use_opportunities"/>
                </xpath>
                <xpath expr="//group[@name='right']" position="attributes">
                    <attribute name="string">Assignment Rules</attribute>
                    <attribute name="invisible">not assignment_enabled</attribute>
                </xpath>
                <xpath expr="//group[@name='right']" position="inside">
                    <field name="assignment_enabled" invisible="1"/>
                    <field name="assignment_auto_enabled" invisible="1"/>
                    <field name="lead_all_assigned_month_exceeded" invisible="1"/>
                    <div colspan="2">
                        <div class="o_crm_lead_all_assigned_month_exceeded" invisible="not lead_all_assigned_month_exceeded"/>
                        <div class="o_crm_lead_month_assignment text-muted" invisible="not assignment_enabled">
                            <i class="fa fa-info-circle me-2" title="Assigned Lead Count"/>
                            <field name="lead_all_assigned_month_count" class="oe_inline"/><span> leads assigned this month
                            on a maximum of </span><field name="assignment_max" class="oe_inline"/>
                        </div>
                    </div>
                    <field name="assignment_domain" widget="domain" string="Domain"
                        options="{'foldable': True, 'model': 'crm.lead', 'in_dialog': True}"
                        invisible="not assignment_enabled"/>
                    <field name="assignment_optout" invisible="not assignment_auto_enabled"/>
                </xpath>
                <xpath expr="//field[@name='member_ids']" position="attributes">
                    <attribute name="invisible">assignment_enabled</attribute>
                </xpath>
                <xpath expr="//field[@name='crm_team_member_ids']" position="attributes">
                    <attribute name="invisible">not assignment_enabled</attribute>
                </xpath>
            </field>
        </record>

        <!-- Case Teams Action -->
        <record id="action_crm_tag_kanban_view_salesteams_oppor11" model="ir.actions.act_window.view">
            <field name="sequence" eval="0"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="crm_case_kanban_view_leads"/>
            <field name="act_window_id" ref="crm_case_form_view_salesteams_opportunity"/>
        </record>

        <record id="action_crm_tag_tree_view_salesteams_oppor11" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="crm_case_tree_view_oppor"/>
            <field name="act_window_id" ref="crm_case_form_view_salesteams_opportunity"/>
        </record>

        <record id="action_opportunity_form" model="ir.actions.act_window">
            <field name="name">New Opportunity</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="crm_lead_view_form"/>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
            }
            </field>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
        </record>

        <record id="sales_team.crm_team_action_pipeline" model="ir.actions.act_window">
            <field name="domain">[('use_opportunities', '=', True)]</field>
        </record>

        <record id="crm_team_view_kanban_dashboard" model="ir.ui.view">
            <field name="name">crm.team.view.kanban.dashboard.inherit.crm</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_kanban_dashboard"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//templates" position="before">
                        <field name="alias_id"/>
                        <field name="alias_name"/>
                        <field name="alias_domain"/>
                        <field name="use_opportunities"/>
                        <field name="use_leads"/>
                    </xpath>

                    <xpath expr="//div[hasclass('o_primary')]" position="after">
                        <div t-if="record.alias_name.value and record.alias_domain.value">
                            <span t-translation="off"><i class="fa fa-envelope-o" aria-label="Leads" title="Leads" role="img"></i>&amp;nbsp; <field name="alias_id"/></span>
                        </div>
                    </xpath>

                    <xpath expr="//t[@name='first_options']" position="after">
                        <div class="row" t-if="record.lead_unassigned_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_case_form_view_salesteams_lead)d" type="action" context="{'search_default_unassigned_leads': 1}">
                                    <field name="lead_unassigned_count" class="me-1"/>
                                    <t t-if="record.lead_unassigned_count.raw_value == 1">Unassigned Lead</t>
                                    <t t-else="">Unassigned Leads</t>
                                </a>
                            </div>
                        </div>
                        <div class="row" t-if="record.opportunities_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_case_form_view_salesteams_opportunity)d" type="action" context="{'search_default_open_opportunities': True}"> <!-- context="{'search_default_probability': NOT or < 100}" -->
                                    <field name="opportunities_count" class="me-1"/>
                                    <t t-if="record.opportunities_count.raw_value == 1">Open Opportunity</t>
                                    <t t-else="">Open Opportunities</t>
                                </a>
                            </div>
                            <div class="col-4 text-end text-truncate">
                                <field name="opportunities_amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            </div>
                        </div>
                        <div class="row" t-if="record.opportunities_overdue_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_lead_action_team_overdue_opportunity)d" type="action">
                                    <field name="opportunities_overdue_count" class="me-1"/>
                                    <t t-if="record.opportunities_overdue_count.raw_value == 1">Overdue Opportunity</t>
                                    <t t-else="">Overdue Opportunities</t>
                                </a>
                            </div>
                             <div class="col-4 text-end text-truncate">
                                <field name="opportunities_overdue_amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            </div>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_view')]/h5[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(crm_case_form_view_salesteams_lead)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a name="%(crm_case_form_view_salesteams_opportunity)d" type="action">
                                Opportunities
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_new')]/h5[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(crm_lead_action_open_lead_form)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a  name="%(action_opportunity_form)d" type="action">
                                Opportunity
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_reports')]/h5[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(action_report_crm_lead_salesteam)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a name="%(action_report_crm_opportunity_salesteam)d" type="action">
                                Opportunities
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_reports')]/div[@name='o_team_kanban_report_separator']" position="after">
                        <div t-if="record.use_opportunities.raw_value">
                            <a name="%(crm.crm_activity_report_action_team)d" type="action">
                                Activities
                            </a>
                        </div>
                    </xpath>
                </data>
            </field>
        </record>

</odoo>
