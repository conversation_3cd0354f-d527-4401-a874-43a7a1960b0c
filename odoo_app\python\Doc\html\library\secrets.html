<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="secrets — Generate secure random numbers for managing secrets" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/secrets.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/secrets.py The secrets module is used for generating cryptographically strong random numbers suitable for managing data such as passwords, account authentication, security tokens, ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/secrets.py The secrets module is used for generating cryptographically strong random numbers suitable for managing data such as passwords, account authentication, security tokens, ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>secrets — Generate secure random numbers for managing secrets &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Generic Operating System Services" href="allos.html" />
    <link rel="prev" title="hmac — Keyed-Hashing for Message Authentication" href="hmac.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/secrets.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a><ul>
<li><a class="reference internal" href="#random-numbers">Random numbers</a></li>
<li><a class="reference internal" href="#generating-tokens">Generating tokens</a><ul>
<li><a class="reference internal" href="#how-many-bytes-should-tokens-use">How many bytes should tokens use?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
<li><a class="reference internal" href="#recipes-and-best-practices">Recipes and best practices</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="hmac.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="allos.html"
                          title="next chapter">Generic Operating System Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/secrets.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="allos.html" title="Generic Operating System Services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="hmac.html" title="hmac — Keyed-Hashing for Message Authentication"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" accesskey="U">Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-secrets">
<span id="secrets-generate-secure-random-numbers-for-managing-secrets"></span><h1><a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> — Generate secure random numbers for managing secrets<a class="headerlink" href="#module-secrets" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/secrets.py">Lib/secrets.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> module is used for generating cryptographically strong
random numbers suitable for managing data such as passwords, account
authentication, security tokens, and related secrets.</p>
<p>In particular, <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> should be used in preference to the
default pseudo-random number generator in the <a class="reference internal" href="random.html#module-random" title="random: Generate pseudo-random numbers with various common distributions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code></a> module, which
is designed for modelling and simulation, not security or cryptography.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0506/"><strong>PEP 506</strong></a></p>
</div>
<section id="random-numbers">
<h2>Random numbers<a class="headerlink" href="#random-numbers" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> module provides access to the most secure source of
randomness that your operating system provides.</p>
<dl class="py class">
<dt class="sig sig-object py" id="secrets.SystemRandom">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">SystemRandom</span></span><a class="headerlink" href="#secrets.SystemRandom" title="Link to this definition">¶</a></dt>
<dd><p>A class for generating random numbers using the highest-quality
sources provided by the operating system.  See
<a class="reference internal" href="random.html#random.SystemRandom" title="random.SystemRandom"><code class="xref py py-class docutils literal notranslate"><span class="pre">random.SystemRandom</span></code></a> for additional details.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="secrets.choice">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">choice</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sequence</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#secrets.choice" title="Link to this definition">¶</a></dt>
<dd><p>Return a randomly chosen element from a non-empty sequence.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="secrets.randbelow">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">randbelow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#secrets.randbelow" title="Link to this definition">¶</a></dt>
<dd><p>Return a random int in the range [0, <em>n</em>).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="secrets.randbits">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">randbits</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">k</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#secrets.randbits" title="Link to this definition">¶</a></dt>
<dd><p>Return an int with <em>k</em> random bits.</p>
</dd></dl>

</section>
<section id="generating-tokens">
<h2>Generating tokens<a class="headerlink" href="#generating-tokens" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> module provides functions for generating secure
tokens, suitable for applications such as password resets,
hard-to-guess URLs, and similar.</p>
<dl class="py function">
<dt class="sig sig-object py" id="secrets.token_bytes">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">token_bytes</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">nbytes=None</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#secrets.token_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Return a random byte string containing <em>nbytes</em> number of bytes.
If <em>nbytes</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> or not supplied, a reasonable default is
used.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">token_bytes</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>  
<span class="go">b&#39;\xebr\x17D*t\xae\xd4\xe3S\xb6\xe2\xebP1\x8b&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="secrets.token_hex">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">token_hex</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">nbytes=None</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#secrets.token_hex" title="Link to this definition">¶</a></dt>
<dd><p>Return a random text string, in hexadecimal.  The string has <em>nbytes</em>
random bytes, each byte converted to two hex digits.  If <em>nbytes</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code> or not supplied, a reasonable default is used.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">token_hex</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>  
<span class="go">&#39;f9bf78b9a18ce6d46a0cd2b0b86df9da&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="secrets.token_urlsafe">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">token_urlsafe</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">nbytes=None</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#secrets.token_urlsafe" title="Link to this definition">¶</a></dt>
<dd><p>Return a random URL-safe text string, containing <em>nbytes</em> random
bytes.  The text is Base64 encoded, so on average each byte results
in approximately 1.3 characters.  If <em>nbytes</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> or not
supplied, a reasonable default is used.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">token_urlsafe</span><span class="p">(</span><span class="mi">16</span><span class="p">)</span>  
<span class="go">&#39;Drmhze6EPcv0fN_81Bj-nA&#39;</span>
</pre></div>
</div>
</dd></dl>

<section id="how-many-bytes-should-tokens-use">
<h3>How many bytes should tokens use?<a class="headerlink" href="#how-many-bytes-should-tokens-use" title="Link to this heading">¶</a></h3>
<p>To be secure against
<a class="reference external" href="https://en.wikipedia.org/wiki/Brute-force_attack">brute-force attacks</a>,
tokens need to have sufficient randomness.  Unfortunately, what is
considered sufficient will necessarily increase as computers get more
powerful and able to make more guesses in a shorter period.  As of 2015,
it is believed that 32 bytes (256 bits) of randomness is sufficient for
the typical use-case expected for the <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a> module.</p>
<p>For those who want to manage their own token length, you can explicitly
specify how much randomness is used for tokens by giving an <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>
argument to the various <code class="docutils literal notranslate"><span class="pre">token_*</span></code> functions.  That argument is taken
as the number of bytes of randomness to use.</p>
<p>Otherwise, if no argument is provided, or if the argument is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
the <code class="docutils literal notranslate"><span class="pre">token_*</span></code> functions will use a reasonable default instead.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>That default is subject to change at any time, including during
maintenance releases.</p>
</div>
</section>
</section>
<section id="other-functions">
<h2>Other functions<a class="headerlink" href="#other-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="secrets.compare_digest">
<span class="sig-prename descclassname"><span class="pre">secrets.</span></span><span class="sig-name descname"><span class="pre">compare_digest</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#secrets.compare_digest" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if strings or
<a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like objects</span></a>
<em>a</em> and <em>b</em> are equal, otherwise <code class="docutils literal notranslate"><span class="pre">False</span></code>,
using a “constant-time compare” to reduce the risk of
<a class="reference external" href="https://codahale.com/a-lesson-in-timing-attacks/">timing attacks</a>.
See <a class="reference internal" href="hmac.html#hmac.compare_digest" title="hmac.compare_digest"><code class="xref py py-func docutils literal notranslate"><span class="pre">hmac.compare_digest()</span></code></a> for additional details.</p>
</dd></dl>

</section>
<section id="recipes-and-best-practices">
<h2>Recipes and best practices<a class="headerlink" href="#recipes-and-best-practices" title="Link to this heading">¶</a></h2>
<p>This section shows recipes and best practices for using <a class="reference internal" href="#module-secrets" title="secrets: Generate secure random numbers for managing secrets."><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code></a>
to manage a basic level of security.</p>
<p>Generate an eight-character alphanumeric password:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">string</span>
<span class="kn">import</span> <span class="nn">secrets</span>
<span class="n">alphabet</span> <span class="o">=</span> <span class="n">string</span><span class="o">.</span><span class="n">ascii_letters</span> <span class="o">+</span> <span class="n">string</span><span class="o">.</span><span class="n">digits</span>
<span class="n">password</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">secrets</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">alphabet</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">8</span><span class="p">))</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Applications should not
<a class="reference external" href="https://cwe.mitre.org/data/definitions/257.html">store passwords in a recoverable format</a>,
whether plain text or encrypted.  They should be salted and hashed
using a cryptographically strong one-way (irreversible) hash function.</p>
</div>
<p>Generate a ten-character alphanumeric password with at least one
lowercase character, at least one uppercase character, and at least
three digits:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">string</span>
<span class="kn">import</span> <span class="nn">secrets</span>
<span class="n">alphabet</span> <span class="o">=</span> <span class="n">string</span><span class="o">.</span><span class="n">ascii_letters</span> <span class="o">+</span> <span class="n">string</span><span class="o">.</span><span class="n">digits</span>
<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">password</span> <span class="o">=</span> <span class="s1">&#39;&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">secrets</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">alphabet</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">10</span><span class="p">))</span>
    <span class="k">if</span> <span class="p">(</span><span class="nb">any</span><span class="p">(</span><span class="n">c</span><span class="o">.</span><span class="n">islower</span><span class="p">()</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">password</span><span class="p">)</span>
            <span class="ow">and</span> <span class="nb">any</span><span class="p">(</span><span class="n">c</span><span class="o">.</span><span class="n">isupper</span><span class="p">()</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">password</span><span class="p">)</span>
            <span class="ow">and</span> <span class="nb">sum</span><span class="p">(</span><span class="n">c</span><span class="o">.</span><span class="n">isdigit</span><span class="p">()</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">password</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">3</span><span class="p">):</span>
        <span class="k">break</span>
</pre></div>
</div>
<p>Generate an <a class="reference external" href="https://xkcd.com/936/">XKCD-style passphrase</a>:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">secrets</span>
<span class="c1"># On standard Linux systems, use a convenient dictionary file.</span>
<span class="c1"># Other platforms may need to provide their own word-list.</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s1">&#39;/usr/share/dict/words&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="n">words</span> <span class="o">=</span> <span class="p">[</span><span class="n">word</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span> <span class="k">for</span> <span class="n">word</span> <span class="ow">in</span> <span class="n">f</span><span class="p">]</span>
    <span class="n">password</span> <span class="o">=</span> <span class="s1">&#39; &#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">secrets</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">words</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">4</span><span class="p">))</span>
</pre></div>
</div>
<p>Generate a hard-to-guess temporary URL containing a security token
suitable for password recovery applications:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">secrets</span>
<span class="n">url</span> <span class="o">=</span> <span class="s1">&#39;https://example.com/reset=&#39;</span> <span class="o">+</span> <span class="n">secrets</span><span class="o">.</span><span class="n">token_urlsafe</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a><ul>
<li><a class="reference internal" href="#random-numbers">Random numbers</a></li>
<li><a class="reference internal" href="#generating-tokens">Generating tokens</a><ul>
<li><a class="reference internal" href="#how-many-bytes-should-tokens-use">How many bytes should tokens use?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
<li><a class="reference internal" href="#recipes-and-best-practices">Recipes and best practices</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="hmac.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">hmac</span></code> — Keyed-Hashing for Message Authentication</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="allos.html"
                          title="next chapter">Generic Operating System Services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/secrets.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="allos.html" title="Generic Operating System Services"
             >next</a> |</li>
        <li class="right" >
          <a href="hmac.html" title="hmac — Keyed-Hashing for Message Authentication"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="crypto.html" >Cryptographic Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">secrets</span></code> — Generate secure random numbers for managing secrets</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>