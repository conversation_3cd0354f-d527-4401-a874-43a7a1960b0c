<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="types — Dynamic type creation and names for built-in types" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/types.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/types.py This module defines utility functions to assist in dynamic creation of new types. It also defines names for some object types that are used by the standard Python interpre..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/types.py This module defines utility functions to assist in dynamic creation of new types. It also defines names for some object types that are used by the standard Python interpre..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>types — Dynamic type creation and names for built-in types &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="copy — Shallow and deep copy operations" href="copy.html" />
    <link rel="prev" title="weakref — Weak references" href="weakref.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/types.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">types</span></code> — Dynamic type creation and names for built-in types</a><ul>
<li><a class="reference internal" href="#dynamic-type-creation">Dynamic Type Creation</a></li>
<li><a class="reference internal" href="#standard-interpreter-types">Standard Interpreter Types</a></li>
<li><a class="reference internal" href="#additional-utility-classes-and-functions">Additional Utility Classes and Functions</a></li>
<li><a class="reference internal" href="#coroutine-utility-functions">Coroutine Utility Functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="weakref.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">weakref</span></code> — Weak references</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="copy.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">copy</span></code> — Shallow and deep copy operations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/types.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="copy.html" title="copy — Shallow and deep copy operations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="weakref.html" title="weakref — Weak references"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">types</span></code> — Dynamic type creation and names for built-in types</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-types">
<span id="types-dynamic-type-creation-and-names-for-built-in-types"></span><h1><a class="reference internal" href="#module-types" title="types: Names for built-in types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">types</span></code></a> — Dynamic type creation and names for built-in types<a class="headerlink" href="#module-types" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/types.py">Lib/types.py</a></p>
<hr class="docutils" />
<p>This module defines utility functions to assist in dynamic creation of
new types.</p>
<p>It also defines names for some object types that are used by the standard
Python interpreter, but not exposed as builtins like <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> or
<a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> are.</p>
<p>Finally, it provides some additional type-related utility classes and functions
that are not fundamental enough to be builtins.</p>
<section id="dynamic-type-creation">
<h2>Dynamic Type Creation<a class="headerlink" href="#dynamic-type-creation" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="types.new_class">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">new_class</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bases</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exec_body</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.new_class" title="Link to this definition">¶</a></dt>
<dd><p>Creates a class object dynamically using the appropriate metaclass.</p>
<p>The first three arguments are the components that make up a class
definition header: the class name, the base classes (in order), the
keyword arguments (such as <code class="docutils literal notranslate"><span class="pre">metaclass</span></code>).</p>
<p>The <em>exec_body</em> argument is a callback that is used to populate the
freshly created class namespace. It should accept the class namespace
as its sole argument and update the namespace directly with the class
contents. If no callback is provided, it has the same effect as passing
in <code class="docutils literal notranslate"><span class="pre">lambda</span> <span class="pre">ns:</span> <span class="pre">None</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="types.prepare_class">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">prepare_class</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bases</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwds</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.prepare_class" title="Link to this definition">¶</a></dt>
<dd><p>Calculates the appropriate metaclass and creates the class namespace.</p>
<p>The arguments are the components that make up a class definition header:
the class name, the base classes (in order) and the keyword arguments
(such as <code class="docutils literal notranslate"><span class="pre">metaclass</span></code>).</p>
<p>The return value is a 3-tuple: <code class="docutils literal notranslate"><span class="pre">metaclass,</span> <span class="pre">namespace,</span> <span class="pre">kwds</span></code></p>
<p><em>metaclass</em> is the appropriate metaclass, <em>namespace</em> is the
prepared class namespace and <em>kwds</em> is an updated copy of the passed
in <em>kwds</em> argument with any <code class="docutils literal notranslate"><span class="pre">'metaclass'</span></code> entry removed. If no <em>kwds</em>
argument is passed in, this will be an empty dict.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The default value for the <code class="docutils literal notranslate"><span class="pre">namespace</span></code> element of the returned
tuple has changed.  Now an insertion-order-preserving mapping is
used when the metaclass does not have a <code class="docutils literal notranslate"><span class="pre">__prepare__</span></code> method.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="../reference/datamodel.html#metaclasses"><span class="std std-ref">Metaclasses</span></a></dt><dd><p>Full details of the class creation process supported by these functions</p>
</dd>
<dt><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3115/"><strong>PEP 3115</strong></a> - Metaclasses in Python 3000</dt><dd><p>Introduced the <code class="docutils literal notranslate"><span class="pre">__prepare__</span></code> namespace hook</p>
</dd>
</dl>
</div>
<dl class="py function">
<dt class="sig sig-object py" id="types.resolve_bases">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">resolve_bases</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">bases</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.resolve_bases" title="Link to this definition">¶</a></dt>
<dd><p>Resolve MRO entries dynamically as specified by <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0560/"><strong>PEP 560</strong></a>.</p>
<p>This function looks for items in <em>bases</em> that are not instances of
<a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>, and returns a tuple where each such object that has
an <a class="reference internal" href="../reference/datamodel.html#object.__mro_entries__" title="object.__mro_entries__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__mro_entries__()</span></code></a> method is replaced with an unpacked result of
calling this method.  If a <em>bases</em> item is an instance of <a class="reference internal" href="functions.html#type" title="type"><code class="xref py py-class docutils literal notranslate"><span class="pre">type</span></code></a>,
or it doesn’t have an <code class="xref py py-meth docutils literal notranslate"><span class="pre">__mro_entries__()</span></code> method, then it is included in
the return tuple unchanged.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="types.get_original_bases">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">get_original_bases</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cls</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">/</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.get_original_bases" title="Link to this definition">¶</a></dt>
<dd><p>Return the tuple of objects originally given as the bases of <em>cls</em> before
the <a class="reference internal" href="../reference/datamodel.html#object.__mro_entries__" title="object.__mro_entries__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__mro_entries__()</span></code></a> method has been called on any bases
(following the mechanisms laid out in <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0560/"><strong>PEP 560</strong></a>). This is useful for
introspecting <a class="reference internal" href="typing.html#user-defined-generics"><span class="std std-ref">Generics</span></a>.</p>
<p>For classes that have an <code class="docutils literal notranslate"><span class="pre">__orig_bases__</span></code> attribute, this
function returns the value of <code class="docutils literal notranslate"><span class="pre">cls.__orig_bases__</span></code>.
For classes without the <code class="docutils literal notranslate"><span class="pre">__orig_bases__</span></code> attribute, <code class="docutils literal notranslate"><span class="pre">cls.__bases__</span></code> is
returned.</p>
<p>Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span><span class="p">,</span> <span class="n">NamedTuple</span><span class="p">,</span> <span class="n">TypedDict</span>

<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>
<span class="k">class</span> <span class="nc">Foo</span><span class="p">(</span><span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span> <span class="o">...</span>
<span class="k">class</span> <span class="nc">Bar</span><span class="p">(</span><span class="n">Foo</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span> <span class="nb">float</span><span class="p">):</span> <span class="o">...</span>
<span class="k">class</span> <span class="nc">Baz</span><span class="p">(</span><span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">]):</span> <span class="o">...</span>
<span class="n">Eggs</span> <span class="o">=</span> <span class="n">NamedTuple</span><span class="p">(</span><span class="s2">&quot;Eggs&quot;</span><span class="p">,</span> <span class="p">[(</span><span class="s2">&quot;a&quot;</span><span class="p">,</span> <span class="nb">int</span><span class="p">),</span> <span class="p">(</span><span class="s2">&quot;b&quot;</span><span class="p">,</span> <span class="nb">str</span><span class="p">)])</span>
<span class="n">Spam</span> <span class="o">=</span> <span class="n">TypedDict</span><span class="p">(</span><span class="s2">&quot;Spam&quot;</span><span class="p">,</span> <span class="p">{</span><span class="s2">&quot;a&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="s2">&quot;b&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">})</span>

<span class="k">assert</span> <span class="n">Bar</span><span class="o">.</span><span class="vm">__bases__</span> <span class="o">==</span> <span class="p">(</span><span class="n">Foo</span><span class="p">,</span> <span class="nb">float</span><span class="p">)</span>
<span class="k">assert</span> <span class="n">get_original_bases</span><span class="p">(</span><span class="n">Bar</span><span class="p">)</span> <span class="o">==</span> <span class="p">(</span><span class="n">Foo</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span> <span class="nb">float</span><span class="p">)</span>

<span class="k">assert</span> <span class="n">Baz</span><span class="o">.</span><span class="vm">__bases__</span> <span class="o">==</span> <span class="p">(</span><span class="nb">list</span><span class="p">,)</span>
<span class="k">assert</span> <span class="n">get_original_bases</span><span class="p">(</span><span class="n">Baz</span><span class="p">)</span> <span class="o">==</span> <span class="p">(</span><span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">],)</span>

<span class="k">assert</span> <span class="n">Eggs</span><span class="o">.</span><span class="vm">__bases__</span> <span class="o">==</span> <span class="p">(</span><span class="nb">tuple</span><span class="p">,)</span>
<span class="k">assert</span> <span class="n">get_original_bases</span><span class="p">(</span><span class="n">Eggs</span><span class="p">)</span> <span class="o">==</span> <span class="p">(</span><span class="n">NamedTuple</span><span class="p">,)</span>

<span class="k">assert</span> <span class="n">Spam</span><span class="o">.</span><span class="vm">__bases__</span> <span class="o">==</span> <span class="p">(</span><span class="nb">dict</span><span class="p">,)</span>
<span class="k">assert</span> <span class="n">get_original_bases</span><span class="p">(</span><span class="n">Spam</span><span class="p">)</span> <span class="o">==</span> <span class="p">(</span><span class="n">TypedDict</span><span class="p">,)</span>

<span class="k">assert</span> <span class="nb">int</span><span class="o">.</span><span class="vm">__bases__</span> <span class="o">==</span> <span class="p">(</span><span class="nb">object</span><span class="p">,)</span>
<span class="k">assert</span> <span class="n">get_original_bases</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span> <span class="o">==</span> <span class="p">(</span><span class="nb">object</span><span class="p">,)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0560/"><strong>PEP 560</strong></a> - Core support for typing module and generic types</p>
</div>
</section>
<section id="standard-interpreter-types">
<h2>Standard Interpreter Types<a class="headerlink" href="#standard-interpreter-types" title="Link to this heading">¶</a></h2>
<p>This module provides names for many of the types that are required to
implement a Python interpreter. It deliberately avoids including some of
the types that arise only incidentally during processing such as the
<code class="docutils literal notranslate"><span class="pre">listiterator</span></code> type.</p>
<p>Typical use of these names is for <a class="reference internal" href="functions.html#isinstance" title="isinstance"><code class="xref py py-func docutils literal notranslate"><span class="pre">isinstance()</span></code></a> or
<a class="reference internal" href="functions.html#issubclass" title="issubclass"><code class="xref py py-func docutils literal notranslate"><span class="pre">issubclass()</span></code></a> checks.</p>
<p>If you instantiate any of these types, note that signatures may vary between Python versions.</p>
<p>Standard names are defined for the following types:</p>
<dl class="py data">
<dt class="sig sig-object py" id="types.NoneType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">NoneType</span></span><a class="headerlink" href="#types.NoneType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-data docutils literal notranslate"><span class="pre">None</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.FunctionType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">FunctionType</span></span><a class="headerlink" href="#types.FunctionType" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="types.LambdaType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">LambdaType</span></span><a class="headerlink" href="#types.LambdaType" title="Link to this definition">¶</a></dt>
<dd><p>The type of user-defined functions and functions created by
<a class="reference internal" href="../reference/expressions.html#lambda"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">lambda</span></code></a>  expressions.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">function.__new__</span></code> with argument <code class="docutils literal notranslate"><span class="pre">code</span></code>.</p>
<p>The audit event only occurs for direct instantiation of function objects,
and is not raised for normal compilation.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.GeneratorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">GeneratorType</span></span><a class="headerlink" href="#types.GeneratorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a>-iterator objects, created by
generator functions.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.CoroutineType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">CoroutineType</span></span><a class="headerlink" href="#types.CoroutineType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a> objects, created by
<a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span> <span class="pre">def</span></code></a> functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.AsyncGeneratorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">AsyncGeneratorType</span></span><a class="headerlink" href="#types.AsyncGeneratorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="../glossary.html#term-asynchronous-generator"><span class="xref std std-term">asynchronous generator</span></a>-iterator objects, created by
asynchronous generator functions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.CodeType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">CodeType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.CodeType" title="Link to this definition">¶</a></dt>
<dd><p id="index-4">The type of <a class="reference internal" href="../reference/datamodel.html#code-objects"><span class="std std-ref">code objects</span></a> such as returned by <a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">code.__new__</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">code</span></code>, <code class="docutils literal notranslate"><span class="pre">filename</span></code>, <code class="docutils literal notranslate"><span class="pre">name</span></code>, <code class="docutils literal notranslate"><span class="pre">argcount</span></code>, <code class="docutils literal notranslate"><span class="pre">posonlyargcount</span></code>, <code class="docutils literal notranslate"><span class="pre">kwonlyargcount</span></code>, <code class="docutils literal notranslate"><span class="pre">nlocals</span></code>, <code class="docutils literal notranslate"><span class="pre">stacksize</span></code>, <code class="docutils literal notranslate"><span class="pre">flags</span></code>.</p>
<p>Note that the audited arguments may not match the names or positions
required by the initializer.  The audit event only occurs for direct
instantiation of code objects, and is not raised for normal compilation.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.CellType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">CellType</span></span><a class="headerlink" href="#types.CellType" title="Link to this definition">¶</a></dt>
<dd><p>The type for cell objects: such objects are used as containers for
a function’s free variables.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.MethodType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">MethodType</span></span><a class="headerlink" href="#types.MethodType" title="Link to this definition">¶</a></dt>
<dd><p>The type of methods of user-defined class instances.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.BuiltinFunctionType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">BuiltinFunctionType</span></span><a class="headerlink" href="#types.BuiltinFunctionType" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="types.BuiltinMethodType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">BuiltinMethodType</span></span><a class="headerlink" href="#types.BuiltinMethodType" title="Link to this definition">¶</a></dt>
<dd><p>The type of built-in functions like <a class="reference internal" href="functions.html#len" title="len"><code class="xref py py-func docutils literal notranslate"><span class="pre">len()</span></code></a> or <a class="reference internal" href="sys.html#sys.exit" title="sys.exit"><code class="xref py py-func docutils literal notranslate"><span class="pre">sys.exit()</span></code></a>, and
methods of built-in classes.  (Here, the term “built-in” means “written in
C”.)</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.WrapperDescriptorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">WrapperDescriptorType</span></span><a class="headerlink" href="#types.WrapperDescriptorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of methods of some built-in data types and base classes such as
<a class="reference internal" href="../reference/datamodel.html#object.__init__" title="object.__init__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">object.__init__()</span></code></a> or <a class="reference internal" href="../reference/datamodel.html#object.__lt__" title="object.__lt__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">object.__lt__()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.MethodWrapperType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">MethodWrapperType</span></span><a class="headerlink" href="#types.MethodWrapperType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <em>bound</em> methods of some built-in data types and base classes.
For example it is the type of <code class="code docutils literal notranslate"><span class="pre">object().__str__</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.NotImplementedType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">NotImplementedType</span></span><a class="headerlink" href="#types.NotImplementedType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="constants.html#NotImplemented" title="NotImplemented"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotImplemented</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.MethodDescriptorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">MethodDescriptorType</span></span><a class="headerlink" href="#types.MethodDescriptorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of methods of some built-in data types such as <a class="reference internal" href="stdtypes.html#str.join" title="str.join"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.join()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.ClassMethodDescriptorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">ClassMethodDescriptorType</span></span><a class="headerlink" href="#types.ClassMethodDescriptorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <em>unbound</em> class methods of some built-in data types such as
<code class="docutils literal notranslate"><span class="pre">dict.__dict__['fromkeys']</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.ModuleType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">ModuleType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.ModuleType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="../glossary.html#term-module"><span class="xref std std-term">modules</span></a>. The constructor takes the name of the
module to be created and optionally its <a class="reference internal" href="../glossary.html#term-docstring"><span class="xref std std-term">docstring</span></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Use <a class="reference internal" href="importlib.html#importlib.util.module_from_spec" title="importlib.util.module_from_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.module_from_spec()</span></code></a> to create a new module if you
wish to set the various import-controlled attributes.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="types.ModuleType.__doc__">
<span class="sig-name descname"><span class="pre">__doc__</span></span><a class="headerlink" href="#types.ModuleType.__doc__" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="../glossary.html#term-docstring"><span class="xref std std-term">docstring</span></a> of the module. Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="types.ModuleType.__loader__">
<span class="sig-name descname"><span class="pre">__loader__</span></span><a class="headerlink" href="#types.ModuleType.__loader__" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> which loaded the module. Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>This attribute is to match <a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec.loader" title="importlib.machinery.ModuleSpec.loader"><code class="xref py py-attr docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec.loader</span></code></a>
as stored in the <a class="reference internal" href="../reference/import.html#spec__" title="__spec__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__</span></code></a> object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A future version of Python may stop setting this attribute by default.
To guard against this potential change, preferably read from the
<a class="reference internal" href="../reference/import.html#spec__" title="__spec__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__</span></code></a> attribute instead or use
<code class="docutils literal notranslate"><span class="pre">getattr(module,</span> <span class="pre">&quot;__loader__&quot;,</span> <span class="pre">None)</span></code> if you explicitly need to use
this attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>. Previously the attribute was optional.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="types.ModuleType.__name__">
<span class="sig-name descname"><span class="pre">__name__</span></span><a class="headerlink" href="#types.ModuleType.__name__" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module. Expected to match
<a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec.name" title="importlib.machinery.ModuleSpec.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec.name</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="types.ModuleType.__package__">
<span class="sig-name descname"><span class="pre">__package__</span></span><a class="headerlink" href="#types.ModuleType.__package__" title="Link to this definition">¶</a></dt>
<dd><p>Which <a class="reference internal" href="../glossary.html#term-package"><span class="xref std std-term">package</span></a> a module belongs to. If the module is top-level
(i.e. not a part of any specific package) then the attribute should be set
to <code class="docutils literal notranslate"><span class="pre">''</span></code>, else it should be set to the name of the package (which can be
<a class="reference internal" href="../reference/import.html#name__" title="__name__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__name__</span></code></a> if the module is a package itself). Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>This attribute is to match <a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec.parent" title="importlib.machinery.ModuleSpec.parent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec.parent</span></code></a>
as stored in the <a class="reference internal" href="../reference/import.html#spec__" title="__spec__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__</span></code></a> object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A future version of Python may stop setting this attribute by default.
To guard against this potential change, preferably read from the
<a class="reference internal" href="../reference/import.html#spec__" title="__spec__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__spec__</span></code></a> attribute instead or use
<code class="docutils literal notranslate"><span class="pre">getattr(module,</span> <span class="pre">&quot;__package__&quot;,</span> <span class="pre">None)</span></code> if you explicitly need to use
this attribute.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>. Previously the attribute was optional.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="types.ModuleType.__spec__">
<span class="sig-name descname"><span class="pre">__spec__</span></span><a class="headerlink" href="#types.ModuleType.__spec__" title="Link to this definition">¶</a></dt>
<dd><p>A record of the module’s import-system-related state. Expected to be an
instance of <a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.EllipsisType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">EllipsisType</span></span><a class="headerlink" href="#types.EllipsisType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="constants.html#Ellipsis" title="Ellipsis"><code class="xref py py-data docutils literal notranslate"><span class="pre">Ellipsis</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.GenericAlias">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">GenericAlias</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t_origin</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">t_args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.GenericAlias" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">parameterized generics</span></a> such as
<code class="docutils literal notranslate"><span class="pre">list[int]</span></code>.</p>
<p><code class="docutils literal notranslate"><span class="pre">t_origin</span></code> should be a non-parameterized generic class, such as <code class="docutils literal notranslate"><span class="pre">list</span></code>,
<code class="docutils literal notranslate"><span class="pre">tuple</span></code> or <code class="docutils literal notranslate"><span class="pre">dict</span></code>.  <code class="docutils literal notranslate"><span class="pre">t_args</span></code> should be a <a class="reference internal" href="stdtypes.html#tuple" title="tuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">tuple</span></code></a> (possibly of
length 1) of types which parameterize <code class="docutils literal notranslate"><span class="pre">t_origin</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">types</span> <span class="kn">import</span> <span class="n">GenericAlias</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">==</span> <span class="n">GenericAlias</span><span class="p">(</span><span class="nb">list</span><span class="p">,</span> <span class="p">(</span><span class="nb">int</span><span class="p">,))</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span> <span class="o">==</span> <span class="n">GenericAlias</span><span class="p">(</span><span class="nb">dict</span><span class="p">,</span> <span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">))</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9.2: </span>This type can now be subclassed.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="stdtypes.html#types-genericalias"><span class="std std-ref">Generic Alias Types</span></a></dt><dd><p>In-depth documentation on instances of <code class="xref py py-class docutils literal notranslate"><span class="pre">types.GenericAlias</span></code></p>
</dd>
<dt><span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0585/"><strong>PEP 585</strong></a> - Type Hinting Generics In Standard Collections</dt><dd><p>Introducing the <code class="xref py py-class docutils literal notranslate"><span class="pre">types.GenericAlias</span></code> class</p>
</dd>
</dl>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.UnionType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">UnionType</span></span><a class="headerlink" href="#types.UnionType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="stdtypes.html#types-union"><span class="std std-ref">union type expressions</span></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.TracebackType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">TracebackType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tb_next</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb_frame</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb_lasti</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tb_lineno</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.TracebackType" title="Link to this definition">¶</a></dt>
<dd><p>The type of traceback objects such as found in <code class="docutils literal notranslate"><span class="pre">sys.exception().__traceback__</span></code>.</p>
<p>See <a class="reference internal" href="../reference/datamodel.html#traceback-objects"><span class="std std-ref">the language reference</span></a> for details of the
available attributes and operations, and guidance on creating tracebacks
dynamically.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.FrameType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">FrameType</span></span><a class="headerlink" href="#types.FrameType" title="Link to this definition">¶</a></dt>
<dd><p>The type of <a class="reference internal" href="../reference/datamodel.html#frame-objects"><span class="std std-ref">frame objects</span></a> such as found in
<a class="reference internal" href="../reference/datamodel.html#traceback.tb_frame" title="traceback.tb_frame"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tb.tb_frame</span></code></a> if <code class="docutils literal notranslate"><span class="pre">tb</span></code> is a traceback object.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.GetSetDescriptorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">GetSetDescriptorType</span></span><a class="headerlink" href="#types.GetSetDescriptorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of objects defined in extension modules with <code class="docutils literal notranslate"><span class="pre">PyGetSetDef</span></code>, such
as <a class="reference internal" href="../reference/datamodel.html#frame.f_locals" title="frame.f_locals"><code class="xref py py-attr docutils literal notranslate"><span class="pre">FrameType.f_locals</span></code></a> or <code class="docutils literal notranslate"><span class="pre">array.array.typecode</span></code>.
This type is used as
descriptor for object attributes; it has the same purpose as the
<a class="reference internal" href="functions.html#property" title="property"><code class="xref py py-class docutils literal notranslate"><span class="pre">property</span></code></a> type, but for classes defined in extension modules.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="types.MemberDescriptorType">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">MemberDescriptorType</span></span><a class="headerlink" href="#types.MemberDescriptorType" title="Link to this definition">¶</a></dt>
<dd><p>The type of objects defined in extension modules with <code class="docutils literal notranslate"><span class="pre">PyMemberDef</span></code>, such
as <code class="docutils literal notranslate"><span class="pre">datetime.timedelta.days</span></code>.  This type is used as descriptor for simple C
data members which use standard conversion functions; it has the same purpose
as the <a class="reference internal" href="functions.html#property" title="property"><code class="xref py py-class docutils literal notranslate"><span class="pre">property</span></code></a> type, but for classes defined in extension modules.</p>
<p>In addition, when a class is defined with a <a class="reference internal" href="../reference/datamodel.html#object.__slots__" title="object.__slots__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__slots__</span></code></a> attribute, then for
each slot, an instance of <code class="xref py py-class docutils literal notranslate"><span class="pre">MemberDescriptorType</span></code> will be added as an attribute
on the class. This allows the slot to appear in the class’s <a class="reference internal" href="stdtypes.html#object.__dict__" title="object.__dict__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__dict__</span></code></a>.</p>
<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> In other implementations of Python, this type may be identical to
<code class="docutils literal notranslate"><span class="pre">GetSetDescriptorType</span></code>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="types.MappingProxyType">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">MappingProxyType</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mapping</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType" title="Link to this definition">¶</a></dt>
<dd><p>Read-only proxy of a mapping. It provides a dynamic view on the mapping’s
entries, which means that when the mapping changes, the view reflects these
changes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Updated to support the new union (<code class="docutils literal notranslate"><span class="pre">|</span></code>) operator from <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0584/"><strong>PEP 584</strong></a>, which
simply delegates to the underlying mapping.</p>
</div>
<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">key</span> <span class="pre">in</span> <span class="pre">proxy</span></span></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the underlying mapping has a key <em>key</em>, else
<code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd></dl>

<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">proxy[key]</span></span></dt>
<dd><p>Return the item of the underlying mapping with key <em>key</em>.  Raises a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> if <em>key</em> is not in the underlying mapping.</p>
</dd></dl>

<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">iter(proxy)</span></span></dt>
<dd><p>Return an iterator over the keys of the underlying mapping.  This is a
shortcut for <code class="docutils literal notranslate"><span class="pre">iter(proxy.keys())</span></code>.</p>
</dd></dl>

<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">len(proxy)</span></span></dt>
<dd><p>Return the number of items in the underlying mapping.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="types.MappingProxyType.copy">
<span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType.copy" title="Link to this definition">¶</a></dt>
<dd><p>Return a shallow copy of the underlying mapping.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="types.MappingProxyType.get">
<span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType.get" title="Link to this definition">¶</a></dt>
<dd><p>Return the value for <em>key</em> if <em>key</em> is in the underlying mapping, else
<em>default</em>.  If <em>default</em> is not given, it defaults to <code class="docutils literal notranslate"><span class="pre">None</span></code>, so that
this method never raises a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="types.MappingProxyType.items">
<span class="sig-name descname"><span class="pre">items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType.items" title="Link to this definition">¶</a></dt>
<dd><p>Return a new view of the underlying mapping’s items (<code class="docutils literal notranslate"><span class="pre">(key,</span> <span class="pre">value)</span></code>
pairs).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="types.MappingProxyType.keys">
<span class="sig-name descname"><span class="pre">keys</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType.keys" title="Link to this definition">¶</a></dt>
<dd><p>Return a new view of the underlying mapping’s keys.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="types.MappingProxyType.values">
<span class="sig-name descname"><span class="pre">values</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#types.MappingProxyType.values" title="Link to this definition">¶</a></dt>
<dd><p>Return a new view of the underlying mapping’s values.</p>
</dd></dl>

<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">reversed(proxy)</span></span></dt>
<dd><p>Return a reverse iterator over the keys of the underlying mapping.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="describe">
<dt class="sig sig-object">
<span class="sig-name descname"><span class="pre">hash(proxy)</span></span></dt>
<dd><p>Return a hash of the underlying mapping.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="additional-utility-classes-and-functions">
<h2>Additional Utility Classes and Functions<a class="headerlink" href="#additional-utility-classes-and-functions" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="types.SimpleNamespace">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">SimpleNamespace</span></span><a class="headerlink" href="#types.SimpleNamespace" title="Link to this definition">¶</a></dt>
<dd><p>A simple <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> subclass that provides attribute access to its
namespace, as well as a meaningful repr.</p>
<p>Unlike <a class="reference internal" href="functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>, with <code class="docutils literal notranslate"><span class="pre">SimpleNamespace</span></code> you can add and remove
attributes.  If a <code class="docutils literal notranslate"><span class="pre">SimpleNamespace</span></code> object is initialized with keyword
arguments, those are directly added to the underlying namespace.</p>
<p>The type is roughly equivalent to the following code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">SimpleNamespace</span><span class="p">:</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">/</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">kwargs</span><span class="p">)</span>

    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">items</span> <span class="o">=</span> <span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">k</span><span class="si">}</span><span class="s2">=</span><span class="si">{</span><span class="n">v</span><span class="si">!r}</span><span class="s2">&quot;</span> <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">items</span><span class="p">())</span>
        <span class="k">return</span> <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">(</span><span class="si">{}</span><span class="s2">)&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="vm">__name__</span><span class="p">,</span> <span class="s2">&quot;, &quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">items</span><span class="p">))</span>

    <span class="k">def</span> <span class="fm">__eq__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">SimpleNamespace</span><span class="p">)</span> <span class="ow">and</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">SimpleNamespace</span><span class="p">):</span>
           <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__dict__</span> <span class="o">==</span> <span class="n">other</span><span class="o">.</span><span class="vm">__dict__</span>
        <span class="k">return</span> <span class="bp">NotImplemented</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">SimpleNamespace</span></code> may be useful as a replacement for <code class="docutils literal notranslate"><span class="pre">class</span> <span class="pre">NS:</span> <span class="pre">pass</span></code>.
However, for a structured record type use <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a>
instead.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Attribute order in the repr changed from alphabetical to insertion (like
<code class="docutils literal notranslate"><span class="pre">dict</span></code>).</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="types.DynamicClassAttribute">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">DynamicClassAttribute</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fget</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fset</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fdel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.DynamicClassAttribute" title="Link to this definition">¶</a></dt>
<dd><p>Route attribute access on a class to __getattr__.</p>
<p>This is a descriptor, used to define attributes that act differently when
accessed through an instance and through a class.  Instance access remains
normal, but access to an attribute through a class will be routed to the
class’s __getattr__ method; this is done by raising AttributeError.</p>
<p>This allows one to have properties active on an instance, and have virtual
attributes on the class with the same name (see <a class="reference internal" href="enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">enum.Enum</span></code></a> for an example).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

</section>
<section id="coroutine-utility-functions">
<h2>Coroutine Utility Functions<a class="headerlink" href="#coroutine-utility-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="types.coroutine">
<span class="sig-prename descclassname"><span class="pre">types.</span></span><span class="sig-name descname"><span class="pre">coroutine</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">gen_func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#types.coroutine" title="Link to this definition">¶</a></dt>
<dd><p>This function transforms a <a class="reference internal" href="../glossary.html#term-generator"><span class="xref std std-term">generator</span></a> function into a
<a class="reference internal" href="../glossary.html#term-coroutine-function"><span class="xref std std-term">coroutine function</span></a> which returns a generator-based coroutine.
The generator-based coroutine is still a <a class="reference internal" href="../glossary.html#term-generator-iterator"><span class="xref std std-term">generator iterator</span></a>,
but is also considered to be a <a class="reference internal" href="../glossary.html#term-coroutine"><span class="xref std std-term">coroutine</span></a> object and is
<a class="reference internal" href="../glossary.html#term-awaitable"><span class="xref std std-term">awaitable</span></a>.  However, it may not necessarily implement
the <a class="reference internal" href="../reference/datamodel.html#object.__await__" title="object.__await__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__await__()</span></code></a> method.</p>
<p>If <em>gen_func</em> is a generator function, it will be modified in-place.</p>
<p>If <em>gen_func</em> is not a generator function, it will be wrapped. If it
returns an instance of <a class="reference internal" href="collections.abc.html#collections.abc.Generator" title="collections.abc.Generator"><code class="xref py py-class docutils literal notranslate"><span class="pre">collections.abc.Generator</span></code></a>, the instance
will be wrapped in an <em>awaitable</em> proxy object.  All other types
of objects will be returned as is.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">types</span></code> — Dynamic type creation and names for built-in types</a><ul>
<li><a class="reference internal" href="#dynamic-type-creation">Dynamic Type Creation</a></li>
<li><a class="reference internal" href="#standard-interpreter-types">Standard Interpreter Types</a></li>
<li><a class="reference internal" href="#additional-utility-classes-and-functions">Additional Utility Classes and Functions</a></li>
<li><a class="reference internal" href="#coroutine-utility-functions">Coroutine Utility Functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="weakref.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">weakref</span></code> — Weak references</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="copy.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">copy</span></code> — Shallow and deep copy operations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/types.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="copy.html" title="copy — Shallow and deep copy operations"
             >next</a> |</li>
        <li class="right" >
          <a href="weakref.html" title="weakref — Weak references"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">types</span></code> — Dynamic type creation and names for built-in types</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>