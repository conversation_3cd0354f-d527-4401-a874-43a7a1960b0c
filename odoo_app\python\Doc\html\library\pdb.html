<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pdb — The Python Debugger" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pdb.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pdb.py The module pdb defines an interactive source code debugger for Python programs. It supports setting (conditional) breakpoints and single stepping at the source line level, i..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pdb.py The module pdb defines an interactive source code debugger for Python programs. It supports setting (conditional) breakpoints and single stepping at the source line level, i..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pdb — The Python Debugger &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="The Python Profilers" href="profile.html" />
    <link rel="prev" title="faulthandler — Dump the Python traceback" href="faulthandler.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pdb.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code> — The Python Debugger</a><ul>
<li><a class="reference internal" href="#debugger-commands">Debugger Commands</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="faulthandler.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code> — Dump the Python traceback</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="profile.html"
                          title="next chapter">The Python Profilers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pdb.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="profile.html" title="The Python Profilers"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="faulthandler.html" title="faulthandler — Dump the Python traceback"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" accesskey="U">Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code> — The Python Debugger</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pdb">
<span id="pdb-the-python-debugger"></span><span id="debugger"></span><h1><a class="reference internal" href="#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> — The Python Debugger<a class="headerlink" href="#module-pdb" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pdb.py">Lib/pdb.py</a></p>
<hr class="docutils" id="index-0" />
<p>The module <a class="reference internal" href="#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> defines an interactive source code debugger for Python
programs.  It supports setting (conditional) breakpoints and single stepping at
the source line level, inspection of stack frames, source code listing, and
evaluation of arbitrary Python code in the context of any stack frame.  It also
supports post-mortem debugging and can be called under program control.</p>
<p id="index-1">The debugger is extensible – it is actually defined as the class <a class="reference internal" href="#pdb.Pdb" title="pdb.Pdb"><code class="xref py py-class docutils literal notranslate"><span class="pre">Pdb</span></code></a>.
This is currently undocumented but easily understood by reading the source.  The
extension interface uses the modules <a class="reference internal" href="bdb.html#module-bdb" title="bdb: Debugger framework."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bdb</span></code></a> and <a class="reference internal" href="cmd.html#module-cmd" title="cmd: Build line-oriented command interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmd</span></code></a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="faulthandler.html#module-faulthandler" title="faulthandler: Dump the Python traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code></a></dt><dd><p>Used to dump Python tracebacks explicitly, on a fault, after a timeout,
or on a user signal.</p>
</dd>
<dt>Module <a class="reference internal" href="traceback.html#module-traceback" title="traceback: Print or retrieve a stack traceback."><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code></a></dt><dd><p>Standard interface to extract, format and print stack traces of Python programs.</p>
</dd>
</dl>
</div>
<p>The typical usage to break into the debugger is to insert:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pdb</span><span class="p">;</span> <span class="n">pdb</span><span class="o">.</span><span class="n">set_trace</span><span class="p">()</span>
</pre></div>
</div>
<p>Or:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">breakpoint</span><span class="p">()</span>
</pre></div>
</div>
<p>at the location you want to break into the debugger, and then run the program.
You can then step through the code following this statement, and continue
running without the debugger using the <a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a> command.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The built-in <a class="reference internal" href="functions.html#breakpoint" title="breakpoint"><code class="xref py py-func docutils literal notranslate"><span class="pre">breakpoint()</span></code></a>, when called with defaults, can be used
instead of <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">pdb;</span> <span class="pre">pdb.set_trace()</span></code>.</p>
</div>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">double</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
   <span class="nb">breakpoint</span><span class="p">()</span>
   <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="mi">2</span>
<span class="n">val</span> <span class="o">=</span> <span class="mi">3</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">val</span><span class="si">}</span><span class="s2"> * 2 is </span><span class="si">{</span><span class="n">double</span><span class="p">(</span><span class="n">val</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>The debugger’s prompt is <code class="docutils literal notranslate"><span class="pre">(Pdb)</span></code>, which is the indicator that you are in debug mode:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&gt;</span> <span class="o">...</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="n">double</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="k">return</span> <span class="n">x</span> <span class="o">*</span> <span class="mi">2</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">p</span> <span class="n">x</span>
<span class="mi">3</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="k">continue</span>
<span class="mi">3</span> <span class="o">*</span> <span class="mi">2</span> <span class="ow">is</span> <span class="mi">6</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Tab-completion via the <a class="reference internal" href="readline.html#module-readline" title="readline: GNU readline support for Python. (Unix)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">readline</span></code></a> module is available for commands and
command arguments, e.g. the current global and local names are offered as
arguments of the <code class="docutils literal notranslate"><span class="pre">p</span></code> command.</p>
</div>
<p>You can also invoke <a class="reference internal" href="#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> from the command line to debug other scripts.  For
example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">pdb</span> <span class="n">myscript</span><span class="o">.</span><span class="n">py</span>
</pre></div>
</div>
<p>When invoked as a module, pdb will automatically enter post-mortem debugging if
the program being debugged exits abnormally.  After post-mortem debugging (or
after normal exit of the program), pdb will restart the program.  Automatic
restarting preserves pdb’s state (such as breakpoints) and in most cases is more
useful than quitting the debugger upon program’s exit.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <code class="docutils literal notranslate"><span class="pre">-c</span></code> option to execute commands as if given
in a <code class="file docutils literal notranslate"><span class="pre">.pdbrc</span></code> file; see <a class="reference internal" href="#debugger-commands"><span class="std std-ref">Debugger Commands</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Added the <code class="docutils literal notranslate"><span class="pre">-m</span></code> option to execute modules similar to the way
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span></code> does. As with a script, the debugger will pause execution just
before the first line of the module.</p>
</div>
<p>Typical usage to execute a statement under control of the debugger is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pdb</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="mi">1</span> <span class="o">/</span> <span class="n">x</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pdb</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="s2">&quot;f(2)&quot;</span><span class="p">)</span>
<span class="go">&gt; &lt;string&gt;(1)&lt;module&gt;()</span>
<span class="go">(Pdb) continue</span>
<span class="go">0.5</span>
<span class="gp">&gt;&gt;&gt;</span>
</pre></div>
</div>
<p>The typical usage to inspect a crashed program is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">pdb</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="mi">1</span> <span class="o">/</span> <span class="n">x</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">f</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">2</span>, in <span class="n">f</span>
<span class="gr">ZeroDivisionError</span>: <span class="n">division by zero</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pdb</span><span class="o">.</span><span class="n">pm</span><span class="p">()</span>
<span class="go">&gt; &lt;stdin&gt;(2)f()</span>
<span class="go">(Pdb) p x</span>
<span class="go">0</span>
<span class="go">(Pdb)</span>
</pre></div>
</div>
<p>The module defines the following functions; each enters the debugger in a
slightly different way:</p>
<dl class="py function">
<dt class="sig sig-object py" id="pdb.run">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">statement</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.run" title="Link to this definition">¶</a></dt>
<dd><p>Execute the <em>statement</em> (given as a string or a code object) under debugger
control.  The debugger prompt appears before any code is executed; you can
set breakpoints and type <a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a>, or you can step through the
statement using <a class="reference internal" href="#pdbcommand-step"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">step</span></code></a> or <a class="reference internal" href="#pdbcommand-next"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">next</span></code></a> (all these commands are
explained below).  The optional <em>globals</em> and <em>locals</em> arguments specify the
environment in which the code is executed; by default the dictionary of the
module <a class="reference internal" href="__main__.html#module-__main__" title="__main__: The environment where top-level code is run. Covers command-line interfaces, import-time behavior, and ``__name__ == '__main__'``."><code class="xref py py-mod docutils literal notranslate"><span class="pre">__main__</span></code></a> is used.  (See the explanation of the built-in
<a class="reference internal" href="functions.html#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a> or <a class="reference internal" href="functions.html#eval" title="eval"><code class="xref py py-func docutils literal notranslate"><span class="pre">eval()</span></code></a> functions.)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pdb.runeval">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">runeval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.runeval" title="Link to this definition">¶</a></dt>
<dd><p>Evaluate the <em>expression</em> (given as a string or a code object) under debugger
control.  When <a class="reference internal" href="#pdb.runeval" title="pdb.runeval"><code class="xref py py-func docutils literal notranslate"><span class="pre">runeval()</span></code></a> returns, it returns the value of the
<em>expression</em>.  Otherwise this function is similar to <a class="reference internal" href="#pdb.run" title="pdb.run"><code class="xref py py-func docutils literal notranslate"><span class="pre">run()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pdb.runcall">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">runcall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.runcall" title="Link to this definition">¶</a></dt>
<dd><p>Call the <em>function</em> (a function or method object, not a string) with the
given arguments.  When <a class="reference internal" href="#pdb.runcall" title="pdb.runcall"><code class="xref py py-func docutils literal notranslate"><span class="pre">runcall()</span></code></a> returns, it returns whatever the
function call returned.  The debugger prompt appears as soon as the function
is entered.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pdb.set_trace">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">set_trace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">header</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.set_trace" title="Link to this definition">¶</a></dt>
<dd><p>Enter the debugger at the calling stack frame.  This is useful to hard-code
a breakpoint at a given point in a program, even if the code is not
otherwise being debugged (e.g. when an assertion fails).  If given,
<em>header</em> is printed to the console just before debugging begins.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The keyword-only argument <em>header</em>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pdb.post_mortem">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">post_mortem</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">traceback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.post_mortem" title="Link to this definition">¶</a></dt>
<dd><p>Enter post-mortem debugging of the given <em>traceback</em> object.  If no
<em>traceback</em> is given, it uses the one of the exception that is currently
being handled (an exception must be being handled if the default is to be
used).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pdb.pm">
<span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">pm</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pdb.pm" title="Link to this definition">¶</a></dt>
<dd><p>Enter post-mortem debugging of the traceback found in
<a class="reference internal" href="sys.html#sys.last_traceback" title="sys.last_traceback"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.last_traceback</span></code></a>.</p>
</dd></dl>

<p>The <code class="docutils literal notranslate"><span class="pre">run*</span></code> functions and <a class="reference internal" href="#pdb.set_trace" title="pdb.set_trace"><code class="xref py py-func docutils literal notranslate"><span class="pre">set_trace()</span></code></a> are aliases for instantiating the
<a class="reference internal" href="#pdb.Pdb" title="pdb.Pdb"><code class="xref py py-class docutils literal notranslate"><span class="pre">Pdb</span></code></a> class and calling the method of the same name.  If you want to
access further features, you have to do this yourself:</p>
<dl class="py class">
<dt class="sig sig-object py" id="pdb.Pdb">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pdb.</span></span><span class="sig-name descname"><span class="pre">Pdb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">completekey</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'tab'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdin</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stdout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nosigint</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">readrc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.Pdb" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#pdb.Pdb" title="pdb.Pdb"><code class="xref py py-class docutils literal notranslate"><span class="pre">Pdb</span></code></a> is the debugger class.</p>
<p>The <em>completekey</em>, <em>stdin</em> and <em>stdout</em> arguments are passed to the
underlying <a class="reference internal" href="cmd.html#cmd.Cmd" title="cmd.Cmd"><code class="xref py py-class docutils literal notranslate"><span class="pre">cmd.Cmd</span></code></a> class; see the description there.</p>
<p>The <em>skip</em> argument, if given, must be an iterable of glob-style module name
patterns.  The debugger will not step into frames that originate in a module
that matches one of these patterns. <a class="footnote-reference brackets" href="#id3" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
<p>By default, Pdb sets a handler for the SIGINT signal (which is sent when the
user presses <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Ctrl</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd> on the console) when you give a <a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a> command.
This allows you to break into the debugger again by pressing <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Ctrl</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd>.  If you
want Pdb not to touch the SIGINT handler, set <em>nosigint</em> to true.</p>
<p>The <em>readrc</em> argument defaults to true and controls whether Pdb will load
.pdbrc files from the filesystem.</p>
<p>Example call to enable tracing with <em>skip</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">pdb</span><span class="p">;</span> <span class="n">pdb</span><span class="o">.</span><span class="n">Pdb</span><span class="p">(</span><span class="n">skip</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;django.*&#39;</span><span class="p">])</span><span class="o">.</span><span class="n">set_trace</span><span class="p">()</span>
</pre></div>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">pdb.Pdb</span></code> with no arguments.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Added the <em>skip</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>nosigint</em> parameter.
Previously, a SIGINT handler was never set by Pdb.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>readrc</em> argument.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="pdb.Pdb.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">statement</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.Pdb.run" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="pdb.Pdb.runeval">
<span class="sig-name descname"><span class="pre">runeval</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">expression</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">globals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">locals</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.Pdb.runeval" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="pdb.Pdb.runcall">
<span class="sig-name descname"><span class="pre">runcall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">function</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwds</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pdb.Pdb.runcall" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="pdb.Pdb.set_trace">
<span class="sig-name descname"><span class="pre">set_trace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pdb.Pdb.set_trace" title="Link to this definition">¶</a></dt>
<dd><p>See the documentation for the functions explained above.</p>
</dd></dl>

</dd></dl>

<section id="debugger-commands">
<span id="id2"></span><h2>Debugger Commands<a class="headerlink" href="#debugger-commands" title="Link to this heading">¶</a></h2>
<p>The commands recognized by the debugger are listed below.  Most commands can be
abbreviated to one or two letters as indicated; e.g. <code class="docutils literal notranslate"><span class="pre">h(elp)</span></code> means that
either <code class="docutils literal notranslate"><span class="pre">h</span></code> or <code class="docutils literal notranslate"><span class="pre">help</span></code> can be used to enter the help command (but not <code class="docutils literal notranslate"><span class="pre">he</span></code>
or <code class="docutils literal notranslate"><span class="pre">hel</span></code>, nor <code class="docutils literal notranslate"><span class="pre">H</span></code> or <code class="docutils literal notranslate"><span class="pre">Help</span></code> or <code class="docutils literal notranslate"><span class="pre">HELP</span></code>).  Arguments to commands must be
separated by whitespace (spaces or tabs).  Optional arguments are enclosed in
square brackets (<code class="docutils literal notranslate"><span class="pre">[]</span></code>) in the command syntax; the square brackets must not be
typed.  Alternatives in the command syntax are separated by a vertical bar
(<code class="docutils literal notranslate"><span class="pre">|</span></code>).</p>
<p>Entering a blank line repeats the last command entered.  Exception: if the last
command was a <a class="reference internal" href="#pdbcommand-list"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">list</span></code></a> command, the next 11 lines are listed.</p>
<p>Commands that the debugger doesn’t recognize are assumed to be Python statements
and are executed in the context of the program being debugged.  Python
statements can also be prefixed with an exclamation point (<code class="docutils literal notranslate"><span class="pre">!</span></code>).  This is a
powerful way to inspect the program being debugged; it is even possible to
change a variable or call a function.  When an exception occurs in such a
statement, the exception name is printed but the debugger’s state is not
changed.</p>
<p>The debugger supports <a class="reference internal" href="#debugger-aliases"><span class="std std-ref">aliases</span></a>.  Aliases can have
parameters which allows one a certain level of adaptability to the context under
examination.</p>
<p>Multiple commands may be entered on a single line, separated by <code class="docutils literal notranslate"><span class="pre">;;</span></code>.  (A
single <code class="docutils literal notranslate"><span class="pre">;</span></code> is not used as it is the separator for multiple commands in a line
that is passed to the Python parser.)  No intelligence is applied to separating
the commands; the input is split at the first <code class="docutils literal notranslate"><span class="pre">;;</span></code> pair, even if it is in the
middle of a quoted string. A workaround for strings with double semicolons
is to use implicit string concatenation <code class="docutils literal notranslate"><span class="pre">';'';'</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;;&quot;&quot;;&quot;</span></code>.</p>
<p>To set a temporary global variable, use a <em>convenience variable</em>. A <em>convenience
variable</em> is a variable whose name starts with <code class="docutils literal notranslate"><span class="pre">$</span></code>.  For example, <code class="docutils literal notranslate"><span class="pre">$foo</span> <span class="pre">=</span> <span class="pre">1</span></code>
sets a global variable <code class="docutils literal notranslate"><span class="pre">$foo</span></code> which you can use in the debugger session.  The
<em>convenience variables</em> are cleared when the program resumes execution so it’s
less likely to interfere with your program compared to using normal variables
like <code class="docutils literal notranslate"><span class="pre">foo</span> <span class="pre">=</span> <span class="pre">1</span></code>.</p>
<p>There are three preset <em>convenience variables</em>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">$_frame</span></code>: the current frame you are debugging</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$_retval</span></code>: the return value if the frame is returning</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">$_exception</span></code>: the exception if the frame is raising an exception</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p id="index-2">If a file <code class="file docutils literal notranslate"><span class="pre">.pdbrc</span></code> exists in the user’s home directory or in the current
directory, it is read with <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code> encoding and executed as if it had been
typed at the debugger prompt, with the exception that empty lines and lines
starting with <code class="docutils literal notranslate"><span class="pre">#</span></code> are ignored.  This is particularly useful for aliases.  If both
files exist, the one in the home directory is read first and aliases defined there
can be overridden by the local file.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><code class="file docutils literal notranslate"><span class="pre">.pdbrc</span></code> can now contain commands that continue debugging, such as
<a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a> or <a class="reference internal" href="#pdbcommand-next"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">next</span></code></a>.  Previously, these commands had no
effect.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><code class="file docutils literal notranslate"><span class="pre">.pdbrc</span></code> is now read with <code class="docutils literal notranslate"><span class="pre">'utf-8'</span></code> encoding. Previously, it was read
with the system locale encoding.</p>
</div>
<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-help">
<span class="sig-name descname"><span class="pre">h(elp)</span></span><span class="sig-prename descclassname"> <span class="pre">[command]</span></span><a class="headerlink" href="#pdbcommand-help" title="Link to this definition">¶</a></dt>
<dd><p>Without argument, print the list of available commands.  With a <em>command</em> as
argument, print help about that command.  <code class="docutils literal notranslate"><span class="pre">help</span> <span class="pre">pdb</span></code> displays the full
documentation (the docstring of the <a class="reference internal" href="#module-pdb" title="pdb: The Python debugger for interactive interpreters."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code></a> module).  Since the <em>command</em>
argument must be an identifier, <code class="docutils literal notranslate"><span class="pre">help</span> <span class="pre">exec</span></code> must be entered to get help on
the <code class="docutils literal notranslate"><span class="pre">!</span></code> command.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-where">
<span class="sig-name descname"><span class="pre">w(here)</span></span><a class="headerlink" href="#pdbcommand-where" title="Link to this definition">¶</a></dt>
<dd><p>Print a stack trace, with the most recent frame at the bottom.  An arrow (<code class="docutils literal notranslate"><span class="pre">&gt;</span></code>)
indicates the current frame, which determines the context of most commands.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-down">
<span class="sig-name descname"><span class="pre">d(own)</span></span><span class="sig-prename descclassname"> <span class="pre">[count]</span></span><a class="headerlink" href="#pdbcommand-down" title="Link to this definition">¶</a></dt>
<dd><p>Move the current frame <em>count</em> (default one) levels down in the stack trace
(to a newer frame).</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-up">
<span class="sig-name descname"><span class="pre">u(p)</span></span><span class="sig-prename descclassname"> <span class="pre">[count]</span></span><a class="headerlink" href="#pdbcommand-up" title="Link to this definition">¶</a></dt>
<dd><p>Move the current frame <em>count</em> (default one) levels up in the stack trace (to
an older frame).</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-break">
<span class="sig-name descname"><span class="pre">b(reak)</span></span><span class="sig-prename descclassname"> <span class="pre">[([filename:]lineno</span> <span class="pre">|</span> <span class="pre">function)</span> <span class="pre">[,</span> <span class="pre">condition]]</span></span><a class="headerlink" href="#pdbcommand-break" title="Link to this definition">¶</a></dt>
<dd><p>With a <em>lineno</em> argument, set a break there in the current file.  With a
<em>function</em> argument, set a break at the first executable statement within
that function.  The line number may be prefixed with a filename and a colon,
to specify a breakpoint in another file (probably one that hasn’t been loaded
yet).  The file is searched on <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.  Note that each breakpoint
is assigned a number to which all the other breakpoint commands refer.</p>
<p>If a second argument is present, it is an expression which must evaluate to
true before the breakpoint is honored.</p>
<p>Without argument, list all breaks, including for each breakpoint, the number
of times that breakpoint has been hit, the current ignore count, and the
associated condition if any.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-tbreak">
<span class="sig-name descname"><span class="pre">tbreak</span></span><span class="sig-prename descclassname"> <span class="pre">[([filename:]lineno</span> <span class="pre">|</span> <span class="pre">function)</span> <span class="pre">[,</span> <span class="pre">condition]]</span></span><a class="headerlink" href="#pdbcommand-tbreak" title="Link to this definition">¶</a></dt>
<dd><p>Temporary breakpoint, which is removed automatically when it is first hit.
The arguments are the same as for <a class="reference internal" href="#pdbcommand-break"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">break</span></code></a>.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-clear">
<span class="sig-name descname"><span class="pre">cl(ear)</span></span><span class="sig-prename descclassname"> <span class="pre">[filename:lineno</span> <span class="pre">|</span> <span class="pre">bpnumber</span> <span class="pre">...]</span></span><a class="headerlink" href="#pdbcommand-clear" title="Link to this definition">¶</a></dt>
<dd><p>With a <em>filename:lineno</em> argument, clear all the breakpoints at this line.
With a space separated list of breakpoint numbers, clear those breakpoints.
Without argument, clear all breaks (but first ask confirmation).</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-disable">
<span class="sig-name descname"><span class="pre">disable</span></span><span class="sig-prename descclassname"> <span class="pre">bpnumber</span> <span class="pre">[bpnumber</span> <span class="pre">...]</span></span><a class="headerlink" href="#pdbcommand-disable" title="Link to this definition">¶</a></dt>
<dd><p>Disable the breakpoints given as a space separated list of breakpoint
numbers.  Disabling a breakpoint means it cannot cause the program to stop
execution, but unlike clearing a breakpoint, it remains in the list of
breakpoints and can be (re-)enabled.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-enable">
<span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-prename descclassname"> <span class="pre">bpnumber</span> <span class="pre">[bpnumber</span> <span class="pre">...]</span></span><a class="headerlink" href="#pdbcommand-enable" title="Link to this definition">¶</a></dt>
<dd><p>Enable the breakpoints specified.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-ignore">
<span class="sig-name descname"><span class="pre">ignore</span></span><span class="sig-prename descclassname"> <span class="pre">bpnumber</span> <span class="pre">[count]</span></span><a class="headerlink" href="#pdbcommand-ignore" title="Link to this definition">¶</a></dt>
<dd><p>Set the ignore count for the given breakpoint number.  If <em>count</em> is omitted,
the ignore count is set to 0.  A breakpoint becomes active when the ignore
count is zero.  When non-zero, the <em>count</em> is decremented each time the
breakpoint is reached and the breakpoint is not disabled and any associated
condition evaluates to true.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-condition">
<span class="sig-name descname"><span class="pre">condition</span></span><span class="sig-prename descclassname"> <span class="pre">bpnumber</span> <span class="pre">[condition]</span></span><a class="headerlink" href="#pdbcommand-condition" title="Link to this definition">¶</a></dt>
<dd><p>Set a new <em>condition</em> for the breakpoint, an expression which must evaluate
to true before the breakpoint is honored.  If <em>condition</em> is absent, any
existing condition is removed; i.e., the breakpoint is made unconditional.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-commands">
<span class="sig-name descname"><span class="pre">commands</span></span><span class="sig-prename descclassname"> <span class="pre">[bpnumber]</span></span><a class="headerlink" href="#pdbcommand-commands" title="Link to this definition">¶</a></dt>
<dd><p>Specify a list of commands for breakpoint number <em>bpnumber</em>.  The commands
themselves appear on the following lines.  Type a line containing just
<code class="docutils literal notranslate"><span class="pre">end</span></code> to terminate the commands. An example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">commands</span> <span class="mi">1</span>
<span class="p">(</span><span class="n">com</span><span class="p">)</span> <span class="n">p</span> <span class="n">some_variable</span>
<span class="p">(</span><span class="n">com</span><span class="p">)</span> <span class="n">end</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span>
</pre></div>
</div>
<p>To remove all commands from a breakpoint, type <code class="docutils literal notranslate"><span class="pre">commands</span></code> and follow it
immediately with <code class="docutils literal notranslate"><span class="pre">end</span></code>; that is, give no commands.</p>
<p>With no <em>bpnumber</em> argument, <code class="docutils literal notranslate"><span class="pre">commands</span></code> refers to the last breakpoint set.</p>
<p>You can use breakpoint commands to start your program up again.  Simply use
the <a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a> command, or <a class="reference internal" href="#pdbcommand-step"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">step</span></code></a>,
or any other command that resumes execution.</p>
<p>Specifying any command resuming execution
(currently <a class="reference internal" href="#pdbcommand-continue"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">continue</span></code></a>, <a class="reference internal" href="#pdbcommand-step"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">step</span></code></a>, <a class="reference internal" href="#pdbcommand-next"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">next</span></code></a>,
<a class="reference internal" href="#pdbcommand-return"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">return</span></code></a>, <a class="reference internal" href="#pdbcommand-jump"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">jump</span></code></a>, <a class="reference internal" href="#pdbcommand-quit"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">quit</span></code></a> and their abbreviations)
terminates the command list (as if
that command was immediately followed by end). This is because any time you
resume execution (even with a simple next or step), you may encounter another
breakpoint—which could have its own command list, leading to ambiguities about
which list to execute.</p>
<p>If you use the <code class="docutils literal notranslate"><span class="pre">silent</span></code> command in the command list, the usual message about
stopping at a breakpoint is not printed.  This may be desirable for breakpoints
that are to print a specific message and then continue.  If none of the other
commands print anything, you see no sign that the breakpoint was reached.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-step">
<span class="sig-name descname"><span class="pre">s(tep)</span></span><a class="headerlink" href="#pdbcommand-step" title="Link to this definition">¶</a></dt>
<dd><p>Execute the current line, stop at the first possible occasion (either in a
function that is called or on the next line in the current function).</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-next">
<span class="sig-name descname"><span class="pre">n(ext)</span></span><a class="headerlink" href="#pdbcommand-next" title="Link to this definition">¶</a></dt>
<dd><p>Continue execution until the next line in the current function is reached or
it returns.  (The difference between <a class="reference internal" href="#pdbcommand-next"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">next</span></code></a> and <a class="reference internal" href="#pdbcommand-step"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">step</span></code></a> is
that <a class="reference internal" href="#pdbcommand-step"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">step</span></code></a> stops inside a called function, while <a class="reference internal" href="#pdbcommand-next"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">next</span></code></a>
executes called functions at (nearly) full speed, only stopping at the next
line in the current function.)</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-until">
<span class="sig-name descname"><span class="pre">unt(il)</span></span><span class="sig-prename descclassname"> <span class="pre">[lineno]</span></span><a class="headerlink" href="#pdbcommand-until" title="Link to this definition">¶</a></dt>
<dd><p>Without argument, continue execution until the line with a number greater
than the current one is reached.</p>
<p>With <em>lineno</em>, continue execution until a line with a number greater or
equal to <em>lineno</em> is reached.  In both cases, also stop when the current frame
returns.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Allow giving an explicit line number.</p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-return">
<span class="sig-name descname"><span class="pre">r(eturn)</span></span><a class="headerlink" href="#pdbcommand-return" title="Link to this definition">¶</a></dt>
<dd><p>Continue execution until the current function returns.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-continue">
<span class="sig-name descname"><span class="pre">c(ont(inue))</span></span><a class="headerlink" href="#pdbcommand-continue" title="Link to this definition">¶</a></dt>
<dd><p>Continue execution, only stop when a breakpoint is encountered.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-jump">
<span class="sig-name descname"><span class="pre">j(ump)</span></span><span class="sig-prename descclassname"> <span class="pre">lineno</span></span><a class="headerlink" href="#pdbcommand-jump" title="Link to this definition">¶</a></dt>
<dd><p>Set the next line that will be executed.  Only available in the bottom-most
frame.  This lets you jump back and execute code again, or jump forward to
skip code that you don’t want to run.</p>
<p>It should be noted that not all jumps are allowed – for instance it is not
possible to jump into the middle of a <a class="reference internal" href="../reference/compound_stmts.html#for"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">for</span></code></a> loop or out of a
<a class="reference internal" href="../reference/compound_stmts.html#finally"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">finally</span></code></a> clause.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-list">
<span class="sig-name descname"><span class="pre">l(ist)</span></span><span class="sig-prename descclassname"> <span class="pre">[first[,</span> <span class="pre">last]]</span></span><a class="headerlink" href="#pdbcommand-list" title="Link to this definition">¶</a></dt>
<dd><p>List source code for the current file.  Without arguments, list 11 lines
around the current line or continue the previous listing.  With <code class="docutils literal notranslate"><span class="pre">.</span></code> as
argument, list 11 lines around the current line.  With one argument,
list 11 lines around at that line.  With two arguments, list the given range;
if the second argument is less than the first, it is interpreted as a count.</p>
<p>The current line in the current frame is indicated by <code class="docutils literal notranslate"><span class="pre">-&gt;</span></code>.  If an
exception is being debugged, the line where the exception was originally
raised or propagated is indicated by <code class="docutils literal notranslate"><span class="pre">&gt;&gt;</span></code>, if it differs from the current
line.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <code class="docutils literal notranslate"><span class="pre">&gt;&gt;</span></code> marker.</p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-ll">
<span class="sig-name descname"><span class="pre">ll</span></span><span class="sig-prename descclassname"> <span class="pre">|</span> <span class="pre">longlist</span></span><a class="headerlink" href="#pdbcommand-ll" title="Link to this definition">¶</a></dt>
<dd><p>List all source code for the current function or frame.  Interesting lines
are marked as for <a class="reference internal" href="#pdbcommand-list"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">list</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-args">
<span class="sig-name descname"><span class="pre">a(rgs)</span></span><a class="headerlink" href="#pdbcommand-args" title="Link to this definition">¶</a></dt>
<dd><p>Print the arguments of the current function and their current values.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-p">
<span class="sig-name descname"><span class="pre">p</span></span><span class="sig-prename descclassname"> <span class="pre">expression</span></span><a class="headerlink" href="#pdbcommand-p" title="Link to this definition">¶</a></dt>
<dd><p>Evaluate <em>expression</em> in the current context and print its value.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">print()</span></code> can also be used, but is not a debugger command — this executes the
Python <a class="reference internal" href="functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a> function.</p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-pp">
<span class="sig-name descname"><span class="pre">pp</span></span><span class="sig-prename descclassname"> <span class="pre">expression</span></span><a class="headerlink" href="#pdbcommand-pp" title="Link to this definition">¶</a></dt>
<dd><p>Like the <a class="reference internal" href="#pdbcommand-p"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">p</span></code></a> command, except the value of <em>expression</em> is
pretty-printed using the <a class="reference internal" href="pprint.html#module-pprint" title="pprint: Data pretty printer."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pprint</span></code></a> module.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-whatis">
<span class="sig-name descname"><span class="pre">whatis</span></span><span class="sig-prename descclassname"> <span class="pre">expression</span></span><a class="headerlink" href="#pdbcommand-whatis" title="Link to this definition">¶</a></dt>
<dd><p>Print the type of <em>expression</em>.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-source">
<span class="sig-name descname"><span class="pre">source</span></span><span class="sig-prename descclassname"> <span class="pre">expression</span></span><a class="headerlink" href="#pdbcommand-source" title="Link to this definition">¶</a></dt>
<dd><p>Try to get source code of <em>expression</em> and display it.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-display">
<span class="sig-name descname"><span class="pre">display</span></span><span class="sig-prename descclassname"> <span class="pre">[expression]</span></span><a class="headerlink" href="#pdbcommand-display" title="Link to this definition">¶</a></dt>
<dd><p>Display the value of <em>expression</em> if it changed, each time execution stops
in the current frame.</p>
<p>Without <em>expression</em>, list all display expressions for the current frame.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Display evaluates <em>expression</em> and compares to the result of the previous
evaluation of <em>expression</em>, so when the result is mutable, display may not
be able to pick up the changes.</p>
</div>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">lst</span> <span class="o">=</span> <span class="p">[]</span>
<span class="nb">breakpoint</span><span class="p">()</span>
<span class="k">pass</span>
<span class="n">lst</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">lst</span><span class="p">)</span>
</pre></div>
</div>
<p>Display won’t realize <code class="docutils literal notranslate"><span class="pre">lst</span></code> has been changed because the result of evaluation
is modified in place by <code class="docutils literal notranslate"><span class="pre">lst.append(1)</span></code> before being compared:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="k">pass</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">display</span> <span class="n">lst</span>
<span class="n">display</span> <span class="n">lst</span><span class="p">:</span> <span class="p">[]</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">n</span>
<span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="n">lst</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">n</span>
<span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="nb">print</span><span class="p">(</span><span class="n">lst</span><span class="p">)</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span>
</pre></div>
</div>
<p>You can do some tricks with copy mechanism to make it work:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="k">pass</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">display</span> <span class="n">lst</span><span class="p">[:]</span>
<span class="n">display</span> <span class="n">lst</span><span class="p">[:]:</span> <span class="p">[]</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">n</span>
<span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="n">lst</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span> <span class="n">n</span>
<span class="o">&gt;</span> <span class="n">example</span><span class="o">.</span><span class="n">py</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">&lt;</span><span class="n">module</span><span class="o">&gt;</span><span class="p">()</span>
<span class="o">-&gt;</span> <span class="nb">print</span><span class="p">(</span><span class="n">lst</span><span class="p">)</span>
<span class="n">display</span> <span class="n">lst</span><span class="p">[:]:</span> <span class="p">[</span><span class="mi">1</span><span class="p">]</span>  <span class="p">[</span><span class="n">old</span><span class="p">:</span> <span class="p">[]]</span>
<span class="p">(</span><span class="n">Pdb</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-undisplay">
<span class="sig-name descname"><span class="pre">undisplay</span></span><span class="sig-prename descclassname"> <span class="pre">[expression]</span></span><a class="headerlink" href="#pdbcommand-undisplay" title="Link to this definition">¶</a></dt>
<dd><p>Do not display <em>expression</em> anymore in the current frame.  Without
<em>expression</em>, clear all display expressions for the current frame.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-interact">
<span class="sig-name descname"><span class="pre">interact</span></span><a class="headerlink" href="#pdbcommand-interact" title="Link to this definition">¶</a></dt>
<dd><p>Start an interactive interpreter (using the <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-mod docutils literal notranslate"><span class="pre">code</span></code></a> module) whose global
namespace contains all the (global and local) names found in the current
scope.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="std pdbcommand" id="debugger-aliases">
<dt class="sig sig-object std" id="pdbcommand-alias">
<span class="sig-name descname"><span class="pre">alias</span></span><span class="sig-prename descclassname"> <span class="pre">[name</span> <span class="pre">[command]]</span></span><a class="headerlink" href="#pdbcommand-alias" title="Link to this definition">¶</a></dt>
<dd><p>Create an alias called <em>name</em> that executes <em>command</em>.  The <em>command</em> must
<em>not</em> be enclosed in quotes.  Replaceable parameters can be indicated by
<code class="docutils literal notranslate"><span class="pre">%1</span></code>, <code class="docutils literal notranslate"><span class="pre">%2</span></code>, and so on, while <code class="docutils literal notranslate"><span class="pre">%*</span></code> is replaced by all the parameters.
If <em>command</em> is omitted, the current alias for <em>name</em> is shown. If no
arguments are given, all aliases are listed.</p>
<p>Aliases may be nested and can contain anything that can be legally typed at
the pdb prompt.  Note that internal pdb commands <em>can</em> be overridden by
aliases.  Such a command is then hidden until the alias is removed.  Aliasing
is recursively applied to the first word of the command line; all other words
in the line are left alone.</p>
<p>As an example, here are two useful aliases (especially when placed in the
<code class="file docutils literal notranslate"><span class="pre">.pdbrc</span></code> file):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Print instance variables (usage &quot;pi classInst&quot;)</span>
<span class="n">alias</span> <span class="n">pi</span> <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="o">%</span><span class="mf">1.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span> <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;%1.</span><span class="si">{</span><span class="n">k</span><span class="si">}</span><span class="s2"> = </span><span class="si">{</span><span class="o">%</span><span class="mf">1.</span><span class="vm">__dict__</span><span class="p">[</span><span class="n">k</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="c1"># Print instance variables in self</span>
<span class="n">alias</span> <span class="n">ps</span> <span class="n">pi</span> <span class="bp">self</span>
</pre></div>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-unalias">
<span class="sig-name descname"><span class="pre">unalias</span></span><span class="sig-prename descclassname"> <span class="pre">name</span></span><a class="headerlink" href="#pdbcommand-unalias" title="Link to this definition">¶</a></dt>
<dd><p>Delete the specified alias <em>name</em>.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-0">
<span class="sig-name descname"><span class="pre">!</span></span><span class="sig-prename descclassname"> <span class="pre">statement</span></span><a class="headerlink" href="#pdbcommand-0" title="Link to this definition">¶</a></dt>
<dd><p>Execute the (one-line) <em>statement</em> in the context of the current stack frame.
The exclamation point can be omitted unless the first word of the statement
resembles a debugger command, e.g.:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>(Pdb) ! n=42
(Pdb)
</pre></div>
</div>
<p>To set a global variable, you can prefix the assignment command with a
<a class="reference internal" href="../reference/simple_stmts.html#global"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">global</span></code></a> statement on the same line, e.g.:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>(Pdb) global list_options; list_options = [&#39;-l&#39;]
(Pdb)
</pre></div>
</div>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-prename descclassname"> <span class="pre">[args</span> <span class="pre">...]</span></span><a class="headerlink" href="#pdbcommand-run" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="pdbcommand-restart">
<span class="sig-name descname"><span class="pre">restart</span></span><span class="sig-prename descclassname"> <span class="pre">[args</span> <span class="pre">...]</span></span><a class="headerlink" href="#pdbcommand-restart" title="Link to this definition">¶</a></dt>
<dd><p>Restart the debugged Python program.  If <em>args</em> is supplied, it is split
with <a class="reference internal" href="shlex.html#module-shlex" title="shlex: Simple lexical analysis for Unix shell-like languages."><code class="xref py py-mod docutils literal notranslate"><span class="pre">shlex</span></code></a> and the result is used as the new <a class="reference internal" href="sys.html#sys.argv" title="sys.argv"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.argv</span></code></a>.
History, breakpoints, actions and debugger options are preserved.
<a class="reference internal" href="#pdbcommand-restart"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">restart</span></code></a> is an alias for <a class="reference internal" href="#pdbcommand-run"><code class="xref std std-pdbcmd docutils literal notranslate"><span class="pre">run</span></code></a>.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-quit">
<span class="sig-name descname"><span class="pre">q(uit)</span></span><a class="headerlink" href="#pdbcommand-quit" title="Link to this definition">¶</a></dt>
<dd><p>Quit from the debugger.  The program being executed is aborted.</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-debug">
<span class="sig-name descname"><span class="pre">debug</span></span><span class="sig-prename descclassname"> <span class="pre">code</span></span><a class="headerlink" href="#pdbcommand-debug" title="Link to this definition">¶</a></dt>
<dd><p>Enter a recursive debugger that steps through <em>code</em>
(which is an arbitrary expression or statement to be
executed in the current environment).</p>
</dd></dl>

<dl class="std pdbcommand">
<dt class="sig sig-object std" id="pdbcommand-retval">
<span class="sig-name descname"><span class="pre">retval</span></span><a class="headerlink" href="#pdbcommand-retval" title="Link to this definition">¶</a></dt>
<dd><p>Print the return value for the last return of the current function.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id3" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>Whether a frame is considered to originate in a certain module
is determined by the <code class="docutils literal notranslate"><span class="pre">__name__</span></code> in the frame globals.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code> — The Python Debugger</a><ul>
<li><a class="reference internal" href="#debugger-commands">Debugger Commands</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="faulthandler.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">faulthandler</span></code> — Dump the Python traceback</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="profile.html"
                          title="next chapter">The Python Profilers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pdb.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="profile.html" title="The Python Profilers"
             >next</a> |</li>
        <li class="right" >
          <a href="faulthandler.html" title="faulthandler — Dump the Python traceback"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="debug.html" >Debugging and Profiling</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pdb</span></code> — The Python Debugger</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>