<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sysconfig — Provide access to Python’s configuration information" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sysconfig.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/sysconfig.py The sysconfig module provides access to Python’s configuration information like the list of installation paths and the configuration variables relevant for the current..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/sysconfig.py The sysconfig module provides access to Python’s configuration information like the list of installation paths and the configuration variables relevant for the current..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sysconfig — Provide access to Python’s configuration information &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="builtins — Built-in objects" href="builtins.html" />
    <link rel="prev" title="sys.monitoring — Execution event monitoring" href="sys.monitoring.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sysconfig.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a><ul>
<li><a class="reference internal" href="#configuration-variables">Configuration variables</a></li>
<li><a class="reference internal" href="#installation-paths">Installation paths</a></li>
<li><a class="reference internal" href="#user-scheme">User scheme</a><ul>
<li><a class="reference internal" href="#posix-user"><code class="docutils literal notranslate"><span class="pre">posix_user</span></code></a></li>
<li><a class="reference internal" href="#nt-user"><code class="docutils literal notranslate"><span class="pre">nt_user</span></code></a></li>
<li><a class="reference internal" href="#osx-framework-user"><code class="docutils literal notranslate"><span class="pre">osx_framework_user</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#home-scheme">Home scheme</a><ul>
<li><a class="reference internal" href="#posix-home"><code class="docutils literal notranslate"><span class="pre">posix_home</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#prefix-scheme">Prefix scheme</a><ul>
<li><a class="reference internal" href="#posix-prefix"><code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code></a></li>
<li><a class="reference internal" href="#nt"><code class="docutils literal notranslate"><span class="pre">nt</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#installation-path-functions">Installation path functions</a></li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
<li><a class="reference internal" href="#using-sysconfig-as-a-script">Using <code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> as a script</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sys.monitoring.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="builtins.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code> — Built-in objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sysconfig.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="builtins.html" title="builtins — Built-in objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="sys.monitoring.html" title="sys.monitoring — Execution event monitoring"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sysconfig">
<span id="sysconfig-provide-access-to-python-s-configuration-information"></span><h1><a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> — Provide access to Python’s configuration information<a class="headerlink" href="#module-sysconfig" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sysconfig.py">Lib/sysconfig.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> module provides access to Python’s configuration
information like the list of installation paths and the configuration variables
relevant for the current platform.</p>
<section id="configuration-variables">
<h2>Configuration variables<a class="headerlink" href="#configuration-variables" title="Link to this heading">¶</a></h2>
<p>A Python distribution contains a <code class="file docutils literal notranslate"><span class="pre">Makefile</span></code> and a <code class="file docutils literal notranslate"><span class="pre">pyconfig.h</span></code>
header file that are necessary to build both the Python binary itself and
third-party C extensions compiled using <code class="docutils literal notranslate"><span class="pre">setuptools</span></code>.</p>
<p><a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> puts all variables found in these files in a dictionary that
can be accessed using <a class="reference internal" href="#sysconfig.get_config_vars" title="sysconfig.get_config_vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_config_vars()</span></code></a> or <a class="reference internal" href="#sysconfig.get_config_var" title="sysconfig.get_config_var"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_config_var()</span></code></a>.</p>
<p>Notice that on Windows, it’s a much smaller set.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_config_vars">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_config_vars</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_config_vars" title="Link to this definition">¶</a></dt>
<dd><p>With no arguments, return a dictionary of all configuration variables
relevant for the current platform.</p>
<p>With arguments, return a list of values that result from looking up each
argument in the configuration variable dictionary.</p>
<p>For each argument, if the value is not found, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_config_var">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_config_var</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_config_var" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of a single variable <em>name</em>. Equivalent to
<code class="docutils literal notranslate"><span class="pre">get_config_vars().get(name)</span></code>.</p>
<p>If <em>name</em> is not found, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<p>Example of usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">sysconfig</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sysconfig</span><span class="o">.</span><span class="n">get_config_var</span><span class="p">(</span><span class="s1">&#39;Py_ENABLE_SHARED&#39;</span><span class="p">)</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sysconfig</span><span class="o">.</span><span class="n">get_config_var</span><span class="p">(</span><span class="s1">&#39;LIBDIR&#39;</span><span class="p">)</span>
<span class="go">&#39;/usr/local/lib&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sysconfig</span><span class="o">.</span><span class="n">get_config_vars</span><span class="p">(</span><span class="s1">&#39;AR&#39;</span><span class="p">,</span> <span class="s1">&#39;CXX&#39;</span><span class="p">)</span>
<span class="go">[&#39;ar&#39;, &#39;g++&#39;]</span>
</pre></div>
</div>
</section>
<section id="installation-paths">
<span id="id1"></span><h2>Installation paths<a class="headerlink" href="#installation-paths" title="Link to this heading">¶</a></h2>
<p>Python uses an installation scheme that differs depending on the platform and on
the installation options.  These schemes are stored in <a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> under
unique identifiers based on the value returned by <a class="reference internal" href="os.html#os.name" title="os.name"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.name</span></code></a>.
The schemes are used by package installers to determine where to copy files to.</p>
<p>Python currently supports nine schemes:</p>
<ul class="simple">
<li><p><em>posix_prefix</em>: scheme for POSIX platforms like Linux or macOS.  This is
the default scheme used when Python or a component is installed.</p></li>
<li><p><em>posix_home</em>: scheme for POSIX platforms, when the <em>home</em> option is used.
This scheme defines paths located under a specific home prefix.</p></li>
<li><p><em>posix_user</em>: scheme for POSIX platforms, when the <em>user</em> option is used.
This scheme defines paths located under the user’s home directory
(<a class="reference internal" href="site.html#site.USER_BASE" title="site.USER_BASE"><code class="xref py py-const docutils literal notranslate"><span class="pre">site.USER_BASE</span></code></a>).</p></li>
<li><p><em>posix_venv</em>: scheme for <a class="reference internal" href="venv.html#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">Python</span> <span class="pre">virtual</span> <span class="pre">environments</span></code></a> on POSIX
platforms; by default it is the same as <em>posix_prefix</em>.</p></li>
<li><p><em>nt</em>: scheme for Windows.
This is the default scheme used when Python or a component is installed.</p></li>
<li><p><em>nt_user</em>: scheme for Windows, when the <em>user</em> option is used.</p></li>
<li><p><em>nt_venv</em>: scheme for <a class="reference internal" href="venv.html#module-venv" title="venv: Creation of virtual environments."><code class="xref py py-mod docutils literal notranslate"><span class="pre">Python</span> <span class="pre">virtual</span> <span class="pre">environments</span></code></a> on Windows;
by default it is the same as <em>nt</em>.</p></li>
<li><p><em>venv</em>: a scheme with values from either <em>posix_venv</em> or <em>nt_venv</em> depending
on the platform Python runs on.</p></li>
<li><p><em>osx_framework_user</em>: scheme for macOS, when the <em>user</em> option is used.</p></li>
</ul>
<p>Each scheme is itself composed of a series of paths and each path has a unique
identifier.  Python currently uses eight paths:</p>
<ul class="simple">
<li><p><em>stdlib</em>: directory containing the standard Python library files that are not
platform-specific.</p></li>
<li><p><em>platstdlib</em>: directory containing the standard Python library files that are
platform-specific.</p></li>
<li><p><em>platlib</em>: directory for site-specific, platform-specific files.</p></li>
<li><p><em>purelib</em>: directory for site-specific, non-platform-specific files (‘pure’ Python).</p></li>
<li><p><em>include</em>: directory for non-platform-specific header files for
the Python C-API.</p></li>
<li><p><em>platinclude</em>: directory for platform-specific header files for
the Python C-API.</p></li>
<li><p><em>scripts</em>: directory for script files.</p></li>
<li><p><em>data</em>: directory for data files.</p></li>
</ul>
</section>
<section id="user-scheme">
<span id="sysconfig-user-scheme"></span><h2>User scheme<a class="headerlink" href="#user-scheme" title="Link to this heading">¶</a></h2>
<p>This scheme is designed to be the most convenient solution for users that don’t
have write permission to the global site-packages directory or don’t want to
install into it.</p>
<p>Files will be installed into subdirectories of <a class="reference internal" href="site.html#site.USER_BASE" title="site.USER_BASE"><code class="xref py py-const docutils literal notranslate"><span class="pre">site.USER_BASE</span></code></a> (written
as <code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em></code> hereafter).  This scheme installs pure Python modules and
extension modules in the same location (also known as <a class="reference internal" href="site.html#site.USER_SITE" title="site.USER_SITE"><code class="xref py py-const docutils literal notranslate"><span class="pre">site.USER_SITE</span></code></a>).</p>
<section id="posix-user">
<h3><code class="docutils literal notranslate"><span class="pre">posix_user</span></code><a class="headerlink" href="#posix-user" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/include/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/bin</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="nt-user">
<h3><code class="docutils literal notranslate"><span class="pre">nt_user</span></code><a class="headerlink" href="#nt-user" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em><span class="pre">\site-packages</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em><span class="pre">\site-packages</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em><span class="pre">\Include</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">\Python</span><em><span class="pre">XY</span></em><span class="pre">\Scripts</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="osx-framework-user">
<h3><code class="docutils literal notranslate"><span class="pre">osx_framework_user</span></code><a class="headerlink" href="#osx-framework-user" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python/site-packages</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/lib/python/site-packages</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/include/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em><span class="pre">/bin</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">userbase</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="home-scheme">
<span id="sysconfig-home-scheme"></span><h2>Home scheme<a class="headerlink" href="#home-scheme" title="Link to this heading">¶</a></h2>
<p>The idea behind the “home scheme” is that you build and maintain a personal
stash of Python modules.  This scheme’s name is derived from the idea of a
“home” directory on Unix, since it’s not unusual for a Unix user to make their
home directory have a layout similar to <code class="file docutils literal notranslate"><span class="pre">/usr/</span></code> or <code class="file docutils literal notranslate"><span class="pre">/usr/local/</span></code>.
This scheme can be used by anyone, regardless of the operating system they
are installing for.</p>
<section id="posix-home">
<h3><code class="docutils literal notranslate"><span class="pre">posix_home</span></code><a class="headerlink" href="#posix-home" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/lib/python</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/include/python</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platinclude</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/include/python</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em><span class="pre">/bin</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">home</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="prefix-scheme">
<span id="sysconfig-prefix-scheme"></span><h2>Prefix scheme<a class="headerlink" href="#prefix-scheme" title="Link to this heading">¶</a></h2>
<p>The “prefix scheme” is useful when you wish to use one Python installation to
perform the build/install (i.e., to run the setup script), but install modules
into the third-party module directory of a different Python installation (or
something that looks like a different Python installation).  If this sounds a
trifle unusual, it is—that’s why the user and home schemes come before.  However,
there are at least two known cases where the prefix scheme will be useful.</p>
<p>First, consider that many Linux distributions put Python in <code class="file docutils literal notranslate"><span class="pre">/usr</span></code>, rather
than the more traditional <code class="file docutils literal notranslate"><span class="pre">/usr/local</span></code>.  This is entirely appropriate,
since in those cases Python is part of “the system” rather than a local add-on.
However, if you are installing Python modules from source, you probably want
them to go in <code class="file docutils literal notranslate"><span class="pre">/usr/local/lib/python2.</span><em><span class="pre">X</span></em></code> rather than
<code class="file docutils literal notranslate"><span class="pre">/usr/lib/python2.</span><em><span class="pre">X</span></em></code>.</p>
<p>Another possibility is a network filesystem where the name used to write to a
remote directory is different from the name used to read it: for example, the
Python interpreter accessed as <code class="file docutils literal notranslate"><span class="pre">/usr/local/bin/python</span></code> might search for
modules in <code class="file docutils literal notranslate"><span class="pre">/usr/local/lib/python2.</span><em><span class="pre">X</span></em></code>, but those modules would have to
be installed to, say, <code class="file docutils literal notranslate"><span class="pre">/mnt/</span><em><span class="pre">&#64;server</span></em><span class="pre">/export/lib/python2.</span><em><span class="pre">X</span></em></code>.</p>
<section id="posix-prefix">
<h3><code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code><a class="headerlink" href="#posix-prefix" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/lib/python</span><em><span class="pre">X.Y</span></em><span class="pre">/site-packages</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/include/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platinclude</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/include/python</span><em><span class="pre">X.Y</span></em></code></p></td>
</tr>
<tr class="row-even"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">/bin</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
<section id="nt">
<h3><code class="docutils literal notranslate"><span class="pre">nt</span></code><a class="headerlink" href="#nt" title="Link to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Path</p></th>
<th class="head"><p>Installation directory</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><em>stdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Lib</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platstdlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Lib</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>platlib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Lib\site-packages</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>purelib</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Lib\site-packages</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>include</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Include</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>platinclude</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Include</span></code></p></td>
</tr>
<tr class="row-even"><td><p><em>scripts</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em><span class="pre">\Scripts</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><em>data</em></p></td>
<td><p><code class="file docutils literal notranslate"><em><span class="pre">prefix</span></em></code></p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="installation-path-functions">
<h2>Installation path functions<a class="headerlink" href="#installation-path-functions" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> provides some functions to determine these installation paths.</p>
<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_scheme_names">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_scheme_names</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_scheme_names" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing all schemes currently supported in
<a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_default_scheme">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_default_scheme</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_default_scheme" title="Link to this definition">¶</a></dt>
<dd><p>Return the default scheme name for the current platform.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10: </span>This function was previously named <code class="docutils literal notranslate"><span class="pre">_get_default_scheme()</span></code> and
considered an implementation detail.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>When Python runs from a virtual environment,
the <em>venv</em> scheme is returned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_preferred_scheme">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_preferred_scheme</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_preferred_scheme" title="Link to this definition">¶</a></dt>
<dd><p>Return a preferred scheme name for an installation layout specified by <em>key</em>.</p>
<p><em>key</em> must be either <code class="docutils literal notranslate"><span class="pre">&quot;prefix&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;home&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;user&quot;</span></code>.</p>
<p>The return value is a scheme name listed in <a class="reference internal" href="#sysconfig.get_scheme_names" title="sysconfig.get_scheme_names"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_scheme_names()</span></code></a>. It
can be passed to <a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> functions that take a <em>scheme</em> argument,
such as <a class="reference internal" href="#sysconfig.get_paths" title="sysconfig.get_paths"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_paths()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>When Python runs from a virtual environment and <code class="docutils literal notranslate"><span class="pre">key=&quot;prefix&quot;</span></code>,
the <em>venv</em> scheme is returned.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig._get_preferred_schemes">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">_get_preferred_schemes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig._get_preferred_schemes" title="Link to this definition">¶</a></dt>
<dd><p>Return a dict containing preferred scheme names on the current platform.
Python implementers and redistributors may add their preferred schemes to
the <code class="docutils literal notranslate"><span class="pre">_INSTALL_SCHEMES</span></code> module-level global value, and modify this function
to return those scheme names, to e.g. provide different schemes for system
and language package managers to use, so packages installed by either do not
mix with those by the other.</p>
<p>End users should not use this function, but <a class="reference internal" href="#sysconfig.get_default_scheme" title="sysconfig.get_default_scheme"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_default_scheme()</span></code></a> and
<a class="reference internal" href="#sysconfig.get_preferred_scheme" title="sysconfig.get_preferred_scheme"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_preferred_scheme()</span></code></a> instead.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_path_names">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_path_names</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_path_names" title="Link to this definition">¶</a></dt>
<dd><p>Return a tuple containing all path names currently supported in
<a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_path">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">scheme</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">vars</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">expand</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_path" title="Link to this definition">¶</a></dt>
<dd><p>Return an installation path corresponding to the path <em>name</em>, from the
install scheme named <em>scheme</em>.</p>
<p><em>name</em> has to be a value from the list returned by <a class="reference internal" href="#sysconfig.get_path_names" title="sysconfig.get_path_names"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_path_names()</span></code></a>.</p>
<p><a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> stores installation paths corresponding to each path name,
for each platform, with variables to be expanded.  For instance the <em>stdlib</em>
path for the <em>nt</em> scheme is: <code class="docutils literal notranslate"><span class="pre">{base}/Lib</span></code>.</p>
<p><a class="reference internal" href="#sysconfig.get_path" title="sysconfig.get_path"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_path()</span></code></a> will use the variables returned by <a class="reference internal" href="#sysconfig.get_config_vars" title="sysconfig.get_config_vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_config_vars()</span></code></a>
to expand the path.  All variables have default values for each platform so
one may call this function and get the default value.</p>
<p>If <em>scheme</em> is provided, it must be a value from the list returned by
<a class="reference internal" href="#sysconfig.get_scheme_names" title="sysconfig.get_scheme_names"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_scheme_names()</span></code></a>.  Otherwise, the default scheme for the current
platform is used.</p>
<p>If <em>vars</em> is provided, it must be a dictionary of variables that will update
the dictionary returned by <a class="reference internal" href="#sysconfig.get_config_vars" title="sysconfig.get_config_vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_config_vars()</span></code></a>.</p>
<p>If <em>expand</em> is set to <code class="docutils literal notranslate"><span class="pre">False</span></code>, the path will not be expanded using the
variables.</p>
<p>If <em>name</em> is not found, raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_paths">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_paths</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">scheme</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">vars</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">expand</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_paths" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary containing all installation paths corresponding to an
installation scheme. See <a class="reference internal" href="#sysconfig.get_path" title="sysconfig.get_path"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_path()</span></code></a> for more information.</p>
<p>If <em>scheme</em> is not provided, will use the default scheme for the current
platform.</p>
<p>If <em>vars</em> is provided, it must be a dictionary of variables that will
update the dictionary used to expand the paths.</p>
<p>If <em>expand</em> is set to false, the paths will not be expanded.</p>
<p>If <em>scheme</em> is not an existing scheme, <a class="reference internal" href="#sysconfig.get_paths" title="sysconfig.get_paths"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_paths()</span></code></a> will raise a
<a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.</p>
</dd></dl>

</section>
<section id="other-functions">
<h2>Other functions<a class="headerlink" href="#other-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_python_version">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_python_version</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_python_version" title="Link to this definition">¶</a></dt>
<dd><p>Return the <code class="docutils literal notranslate"><span class="pre">MAJOR.MINOR</span></code> Python version number as a string.  Similar to
<code class="docutils literal notranslate"><span class="pre">'%d.%d'</span> <span class="pre">%</span> <span class="pre">sys.version_info[:2]</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_platform">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_platform</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_platform" title="Link to this definition">¶</a></dt>
<dd><p>Return a string that identifies the current platform.</p>
<p>This is used mainly to distinguish platform-specific build directories and
platform-specific built distributions.  Typically includes the OS name and
version and the architecture (as supplied by ‘os.uname()’), although the
exact information included depends on the OS; e.g., on Linux, the kernel
version isn’t particularly important.</p>
<p>Examples of returned values:</p>
<ul class="simple">
<li><p>linux-i586</p></li>
<li><p>linux-alpha (?)</p></li>
<li><p>solaris-2.6-sun4u</p></li>
</ul>
<p>Windows will return one of:</p>
<ul class="simple">
<li><p>win-amd64 (64bit Windows on AMD64, aka x86_64, Intel64, and EM64T)</p></li>
<li><p>win32 (all others - specifically, sys.platform is returned)</p></li>
</ul>
<p>macOS can return:</p>
<ul class="simple">
<li><p>macosx-10.6-ppc</p></li>
<li><p>macosx-10.4-ppc64</p></li>
<li><p>macosx-10.3-i386</p></li>
<li><p>macosx-10.4-fat</p></li>
</ul>
<p>For other non-POSIX platforms, currently just returns <a class="reference internal" href="sys.html#sys.platform" title="sys.platform"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.platform</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.is_python_build">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">is_python_build</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.is_python_build" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the running Python interpreter was built from source and
is being run from its built location, and not from a location resulting from
e.g. running <code class="docutils literal notranslate"><span class="pre">make</span> <span class="pre">install</span></code> or installing via a binary installer.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.parse_config_h">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">parse_config_h</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">vars</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.parse_config_h" title="Link to this definition">¶</a></dt>
<dd><p>Parse a <code class="file docutils literal notranslate"><span class="pre">config.h</span></code>-style file.</p>
<p><em>fp</em> is a file-like object pointing to the <code class="file docutils literal notranslate"><span class="pre">config.h</span></code>-like file.</p>
<p>A dictionary containing name/value pairs is returned.  If an optional
dictionary is passed in as the second argument, it is used instead of a new
dictionary, and updated with the values read in the file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_config_h_filename">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_config_h_filename</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_config_h_filename" title="Link to this definition">¶</a></dt>
<dd><p>Return the path of <code class="file docutils literal notranslate"><span class="pre">pyconfig.h</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sysconfig.get_makefile_filename">
<span class="sig-prename descclassname"><span class="pre">sysconfig.</span></span><span class="sig-name descname"><span class="pre">get_makefile_filename</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sysconfig.get_makefile_filename" title="Link to this definition">¶</a></dt>
<dd><p>Return the path of <code class="file docutils literal notranslate"><span class="pre">Makefile</span></code>.</p>
</dd></dl>

</section>
<section id="using-sysconfig-as-a-script">
<span id="sysconfig-cli"></span><h2>Using <a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> as a script<a class="headerlink" href="#using-sysconfig-as-a-script" title="Link to this heading">¶</a></h2>
<p>You can use <a class="reference internal" href="#module-sysconfig" title="sysconfig: Python's configuration information"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code></a> as a script with Python’s <em>-m</em> option:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>sysconfig
<span class="go">Platform: &quot;macosx-10.4-i386&quot;</span>
<span class="go">Python version: &quot;3.2&quot;</span>
<span class="go">Current installation scheme: &quot;posix_prefix&quot;</span>

<span class="go">Paths:</span>
<span class="go">        data = &quot;/usr/local&quot;</span>
<span class="go">        include = &quot;/Users/<USER>/Dev/svn.python.org/py3k/Include&quot;</span>
<span class="go">        platinclude = &quot;.&quot;</span>
<span class="go">        platlib = &quot;/usr/local/lib/python3.2/site-packages&quot;</span>
<span class="go">        platstdlib = &quot;/usr/local/lib/python3.2&quot;</span>
<span class="go">        purelib = &quot;/usr/local/lib/python3.2/site-packages&quot;</span>
<span class="go">        scripts = &quot;/usr/local/bin&quot;</span>
<span class="go">        stdlib = &quot;/usr/local/lib/python3.2&quot;</span>

<span class="go">Variables:</span>
<span class="go">        AC_APPLE_UNIVERSAL_BUILD = &quot;0&quot;</span>
<span class="go">        AIX_GENUINE_CPLUSPLUS = &quot;0&quot;</span>
<span class="go">        AR = &quot;ar&quot;</span>
<span class="go">        ARFLAGS = &quot;rc&quot;</span>
<span class="go">        ...</span>
</pre></div>
</div>
<p>This call will print in the standard output the information returned by
<a class="reference internal" href="#sysconfig.get_platform" title="sysconfig.get_platform"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_platform()</span></code></a>, <a class="reference internal" href="#sysconfig.get_python_version" title="sysconfig.get_python_version"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_python_version()</span></code></a>, <a class="reference internal" href="#sysconfig.get_path" title="sysconfig.get_path"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_path()</span></code></a> and
<a class="reference internal" href="#sysconfig.get_config_vars" title="sysconfig.get_config_vars"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_config_vars()</span></code></a>.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a><ul>
<li><a class="reference internal" href="#configuration-variables">Configuration variables</a></li>
<li><a class="reference internal" href="#installation-paths">Installation paths</a></li>
<li><a class="reference internal" href="#user-scheme">User scheme</a><ul>
<li><a class="reference internal" href="#posix-user"><code class="docutils literal notranslate"><span class="pre">posix_user</span></code></a></li>
<li><a class="reference internal" href="#nt-user"><code class="docutils literal notranslate"><span class="pre">nt_user</span></code></a></li>
<li><a class="reference internal" href="#osx-framework-user"><code class="docutils literal notranslate"><span class="pre">osx_framework_user</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#home-scheme">Home scheme</a><ul>
<li><a class="reference internal" href="#posix-home"><code class="docutils literal notranslate"><span class="pre">posix_home</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#prefix-scheme">Prefix scheme</a><ul>
<li><a class="reference internal" href="#posix-prefix"><code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code></a></li>
<li><a class="reference internal" href="#nt"><code class="docutils literal notranslate"><span class="pre">nt</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#installation-path-functions">Installation path functions</a></li>
<li><a class="reference internal" href="#other-functions">Other functions</a></li>
<li><a class="reference internal" href="#using-sysconfig-as-a-script">Using <code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> as a script</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="sys.monitoring.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sys.monitoring</span></code> — Execution event monitoring</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="builtins.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">builtins</span></code> — Built-in objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sysconfig.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="builtins.html" title="builtins — Built-in objects"
             >next</a> |</li>
        <li class="right" >
          <a href="sys.monitoring.html" title="sys.monitoring — Execution event monitoring"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sysconfig</span></code> — Provide access to Python’s configuration information</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>