# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_profile
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"!<br/>\n"
"            Did not receive it?"
msgstr ""
"!<br/>\n"
"            دریافت نکردید؟"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "(not verified)"
msgstr "(تأیید نشده)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
". Collect points on the forum or on the eLearning platform. Those points "
"will make you reach new ranks."
msgstr ""
". در انجمن یا روی پلتفرم eLearning امتیاز جمع‌آوری کنید. آن امتیازها شما را "
"به رتبه‌های جدیدی می‌رسانند."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid ". Try another search."
msgstr ". جستجوی دیگری را امتحان کنید"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"fa fa-arrow-right\"/> Get Badges"
msgstr "<i class=\"fa fa-arrow-right\"/> دریافت نشان‌ها"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-close me-1\"/>Cancel"
msgstr "<i class=\"fa fa-close me-1\"/>لغو"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" title=\"Edit\"/>"
msgstr ""
"<i class=\"fa fa-pencil fa-1g float-sm-none float-md-start\" "
"title=\"ویرایش\"/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-1\"/>EDIT"
msgstr "<i class=\"fa fa-pencil me-1\"/>ویرایش"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>ویرایش"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_header
msgid "<i class=\"fa fa-pencil me-2\"/>EDIT PROFILE"
msgstr "<i class=\"fa fa-pencil me-2\"/>ویرایش پروفایل"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Badges"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>تمام نشان‌ها"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "<i class=\"oi oi-arrow-right\"/> All Badges"
msgstr "<i class=\"oi oi-arrow-right\"/> همه نشان‌ها"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> کاربرانی که به آنها پاداش د اده شده است</i>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<option value=\"\">Country...</option>"
msgstr "HTML <option value=\"\">کشور...</option>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold me-2\">Current rank:</small>"
msgstr "<small class=\"fw-bold me-2\">رتبه فعلی:</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Badges</small>"
msgstr "<small class=\"fw-bold\">نشان‌ها</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "<small class=\"fw-bold\">Joined</small>"
msgstr "<small class=\"fw-bold\">پیوسته شد</small>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Biography</span>"
msgstr "<span class=\"fw-bold\">زندگینامه</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">City</span>"
msgstr "<span class=\"fw-bold\">شهر</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Country</span>"
msgstr "<span class=\"fw-bold\">کشور</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Email</span>"
msgstr "<span class=\"fw-bold\">ایمیل</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Name</span>"
msgstr "<span class=\"fw-bold\">نام</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Public Profile</span>"
msgstr "<span class=\"fw-bold\">پروفایل عمومی</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "<span class=\"fw-bold\">Website</span>"
msgstr "<span class=\"fw-bold\">وب‌سایت</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Badges</span>"
msgstr "<span class=\"text-muted small fw-bold\">نشان‌ها</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "<span class=\"text-muted small fw-bold\">XP</span>"
msgstr "<span class=\"text-muted small fw-bold\">تجربه</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">Badges</span>"
msgstr "<span class=\"text-muted\">نشان‌ها</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "<span class=\"text-muted\">XP</span>"
msgstr "<span class=\"text-muted\">امتیاز</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"<span id=\"email_validated_message\">Congratulations! Your email has just "
"been validated.</span>"
msgstr ""
"<span id=\"email_validated_message\">تبریک می‌گوییم! ایمیل شما به تازگی "
"تأیید شده است.</span>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "<strong class=\"mb-3 text-white me-2\">Rank by:</strong>"
msgstr "<strong class=\"mb-3 text-white me-2\">رتبه‌بندی بر اساس:</strong>"

#. module: website_profile
#: model:mail.template,body_html:website_profile.validation_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\">\n"
"                        <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                            <t t-out=\"object.company_id.name or ''\">YourCompany</t> Profile validation\n"
"                        </span>\n"
"                    </td>\n"
"                    <td t-if=\"not user.company_id.uses_default_logo\" valign=\"middle\" align=\"right\">\n"
"                        <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\">\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td colspan=\"2\" style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                            Hello <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                            You have been invited to validate your email in order to get access to \"<t t-out=\"object.company_id.name or ''\">YourCompany</t>\" website.\n"
"                            To validate your email, please click on the following link:\n"
"                            <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                <a t-att-href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                    Validate my account\n"
"                                </a>\n"
"                            </div>\n"
"                            Thanks for your participation!\n"
"                        </p>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" font-family: 'Verdana Regular'; color: #454748; min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"middle\" align=\"left\">\n"
"                         <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                    </td>\n"
"                    <td valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\">\n"
"                        <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                        <t t-if=\"user.company_id.email\">\n"
"                            | <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                        </t>\n"
"                        <t t-if=\"user.company_id.website\">\n"
"                            | <a t-attf-href=\"{{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                        </t>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"            Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" style=\"color: #875A7B;\">Odoo</a>\n"
"        </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top:"
" 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-"
"collapse:separate;\"><tr><td align=\"center\"> <table border=\"0\" "
"cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; "
"background-color: white; color: #454748; border-collapse:separate;\"> "
"<tbody> <!-- HEADER --> <tr> <td align=\"center\" style=\"min-width: "
"590px;\"> <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" "
"width=\"590\" style=\"min-width: 590px; background-color: white; padding: "
"0px 8px 0px 8px; border-collapse:separate;\"> <tr> <td valign=\"middle\"> "
"<span style=\"font-size: 20px; font-weight: bold;\"> <t "
"t-out=\"object.company_id.name or ''\">شرکت شما</t> تأیید اعتبار نمایه "
"</span> </td> <td t-if=\"not user.company_id.uses_default_logo\" "
"valign=\"middle\" align=\"right\"> <img t-attf-src=\"/logo.png?company={{ "
"user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; "
"width: 80px;\" t-att-alt=\"user.company_id.name\"> </td> </tr> <tr> <td "
"colspan=\"2\" style=\"text-align:center;\"> <hr width=\"100%\" "
"style=\"background-color:rgb(204,204,204);border:medium "
"none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; "
"margin:16px 0px 16px 0px;\"> </td> </tr> </table> </td> </tr> <!-- CONTENT "
"--> <tr> <td align=\"center\" style=\"min-width: 590px;\"> <table "
"border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-"
"width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-"
"collapse:separate;\"> <tr> <td valign=\"top\" style=\"font-size: 13px;\"> <p"
" style=\"margin: 0px; padding: 0px; font-size: 13px;\"> سلام <t "
"t-out=\"object.name or ''\">مارک دمو</t>,<br><br> شما برای تأیید اعتبار "
"ایمیل خود برای دسترسی به وبسایت \"<t t-out=\"object.company_id.name or "
"''\">شرکت شما</t>\" دعوت شده‌اید. برای تأیید ایمیل خود، لطفاً روی لینک زیر "
"کلیک کنید: <div style=\"margin: 16px 0px 16px 0px;\"> <a t-att-"
"href=\"ctx.get('token_url')\" style=\"background-color: #875A7B; padding: "
"8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; "
"font-size:13px;\"> تأیید حساب من </a> </div> از شرکت شما سپاسگزاریم! </p> "
"</td> </tr> <tr> <td style=\"text-align:center;\"> <hr width=\"100%\" "
"style=\"background-color:rgb(204,204,204);border:medium "
"none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; "
"margin: 16px 0px 16px 0px;\"> </td> </tr> </table> </td> </tr> <!-- FOOTER "
"--> <tr> <td align=\"center\" style=\"min-width: 590px;\"> <table "
"border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\" "
"font-family: 'Verdana Regular'; color: #454748; min-width: 590px; "
"background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-"
"collapse:separate;\"> <tr> <td valign=\"middle\" align=\"left\"> <t "
"t-out=\"user.company_id.name or ''\">شرکت شما</t> </td> <td "
"valign=\"middle\" align=\"right\" style=\"opacity: 0.7;\"> <t "
"t-out=\"user.company_id.phone or ''\">******-123-4567</t> <t "
"t-if=\"user.company_id.email\"> | <a t-attf-href=\"'mailto:%s' % {{ "
"user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" "
"t-out=\"user.company_id.email or ''\"><EMAIL></a> </t> <t "
"t-if=\"user.company_id.website\"> | <a t-attf-href=\"{{ "
"user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\""
" t-out=\"user.company_id.website or ''\">http://www.example.com</a> </t> "
"</td> </tr> </table> </td> </tr> </tbody> </table> </td></tr> <!-- POWERED "
"BY --> <tr><td align=\"center\" style=\"min-width: 590px;\"> <table "
"border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-"
"width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; "
"border-collapse:separate;\"> <tr><td style=\"text-align: center; font-size: "
"13px;\"> قدرت گرفته از <a target=\"_blank\" "
"href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=forum\" "
"style=\"color: #875A7B;\">اودو</a> </td></tr> </table> </td></tr> </table>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Correct your email address</u>"
msgstr "<u>آدرس ایمیل خود را تصحیح کنید</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>Send Again</u>"
msgstr "<u>ارسال دوباره</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "<u>here</u>"
msgstr "<u>اینجا</u>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "About"
msgstr "درباره"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All Users"
msgstr "همه کاربران"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "All time"
msgstr "تمام وقت"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Badges"
msgstr "بج‌ها"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "Badges are your collection of achievements. Wear them proudly! <br/>"
msgstr ""
"مدال‌ها مجموعه‌ای از دستاوردهای شما هستند. آنها را با افتخار به نمایش "
"بگذارید! <br/>"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.badge_content
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"                        you receive badges for being especially helpful.<br class=\"d-none d-lg-inline-block\"/>Badges\n"
"                        appear on your profile page, and your posts."
msgstr ""
"علاوه بر کسب شهرت از طریق پرسش‌ها و پاسخ‌هایتان،  \n"
"                        شما نشان‌هایی برای کمک ویژه دریافت می‌کنید.<br class=\"d-none d-lg-inline-block\"/>نشان‌ها\n"
"                        در صفحه پروفایل شما و پست‌هایتان ظاهر می‌شوند."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Biography"
msgstr "زندگینامه"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__can_publish
msgid "Can Publish"
msgstr "می تواند منتشر کند"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Clear"
msgstr "پاک کردن"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Close"
msgstr "بستن"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit"
msgstr "ویرایش"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Edit Profile"
msgstr "ویرایش پروفایل"

#. module: website_profile
#: model:mail.template,name:website_profile.validation_email
msgid "Forum: Email Verification"
msgstr "فوروم: تأیید ایمیل"

#. module: website_profile
#: model:ir.model,name:website_profile.model_gamification_badge
msgid "Gamification Badge"
msgstr "نشان گیمیفیکیشن"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "Get"
msgstr "دریافت"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Home"
msgstr "خانه"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I earn badges?"
msgstr "چگونه نشان‌ها کسب کنم؟"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "How do I score more points?"
msgstr "چگونه می توانم امتیاز بیشتری کسب کنم؟"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__is_published
msgid "Is Published"
msgstr "منتشر شده است"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Keep learning with"
msgstr "ادامه دهید به یادگیری با"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_website__karma_profile_min
msgid "Minimal karma to see other user's profile"
msgstr "حداقل کارما برای دیدن پروفایل سایر کاربران"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Mobile sub-nav"
msgstr "زیر منوی راهنمای متحرک"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "More info"
msgstr "اطلاعات بیشتر"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Nav"
msgstr "راهنما"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_content
msgid "Next rank:"
msgstr "رتبه‌ی بعدی:"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_main
msgid "No Leaderboard Yet :("
msgstr "هنوز هیچ تابلوی امتیازات وجود ندارد :("

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_badges
msgid "No badges yet!"
msgstr "هنوز هیچ نشانی وجود ندارد!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_content
msgid "No user found for"
msgstr "هیچ کاربری برای پیدا نشد"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "Not have enough karma to view other users' profile."
msgstr "کافی کارما برای مشاهده پروفایل دیگر کاربران ندارید."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""
"لطفاً برای دریافت اعلان‌ها از پاسخ‌ها یا نظرات، یک آدرس ایمیل معتبر وارد "
"کنید."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "Ranks"
msgstr "رتبه"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_access_denied
msgid "Return to the website."
msgstr "بازگشت به وبسایت."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search"
msgstr "جستجو"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Search users"
msgstr "جستجوی کاربران"

#. module: website_profile
#: model:mail.template,description:website_profile.validation_email
msgid "Sent to forum visitors to confirm their mail address"
msgstr "ارسال شده به بازدیدکنندگان انجمن برای تأیید آدرس ایمیلشان"

#. module: website_profile
#: model:ir.model.fields,help:website_profile.field_gamification_badge__website_url
msgid "The full URL to access the document through the website."
msgstr "آدرس URL کامل برای دسترسی سند در وبسایت."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This month"
msgstr "این ماه"

#. module: website_profile
#. odoo-python
#: code:addons/website_profile/controllers/main.py:0
#, python-format
msgid "This profile is private!"
msgstr "این پروفایل خصوصی است!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.users_page_header
msgid "This week"
msgstr "این هفته"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "Unpublished"
msgstr "منتشر نشده"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Update"
msgstr "به روز رسانی"

#. module: website_profile
#: model:ir.model,name:website_profile.model_res_users
msgid "User"
msgstr "کاربر"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "User rank"
msgstr "رتبه کاربر"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "Users"
msgstr "کاربران"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Verification Email sent to"
msgstr "ایمیل تأیید به  ارسال شد"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_published
msgid "Visible on current website"
msgstr "قابل دید در وبسایت حاضر"

#. module: website_profile
#: model:ir.model,name:website_profile.model_website
msgid "Website"
msgstr "تارنما"

#. module: website_profile
#: model:ir.model.fields,field_description:website_profile.field_gamification_badge__website_url
msgid "Website URL"
msgstr "URL وبسایت"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "When you finish a course or reach milestones, you're awarded badges."
msgstr ""
"هنگامی که یک دوره را به پایان می‌رسانید یا به نقاط عطف می‌رسید، نشان‌هایی به"
" شما اهدا می‌شود."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_biography_editor
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_edit_content
msgid "Write a few words about yourself..."
msgstr "درباره خودتان چند کلمه بنویسید..."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
#: model_terms:ir.ui.view,arch_db:website_profile.top3_user_card
msgid "XP"
msgstr ""
"متن ارائه شده نیاز به مشاهده متن اصلی دارد تا بتوان دقیقاً ترجمه‌ی صحیح و "
"محترمانه‌ای را فراهم کرد. لطفاً متن کامل مربوط به ماژول Odoo را ارائه کنید "
"تا ترجمه‌ای مطابق آن انجام شود."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid ""
"You can score more points by answering quizzes at the end of each course "
"content. Points can also be earned on the forum. Follow this link to the "
"guidelines of the forum."
msgstr ""
"می‌توانید با پاسخ دادن به آزمون‌های موجود در انتهای هر محتوای دوره امتیازات "
"بیشتری کسب کنید. امتیازات می‌توانند در انجمن نیز به دست آیند. برای مشاهده "
"راهنمایی‌های انجمن به این لینک مراجعه کنید."

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid ""
"Your Account has not yet been verified.<br/>\n"
"            Click"
msgstr ""
"حساب شما هنوز تأیید نشده است.<br/>\n"
"            کلیک کنید"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "Your account does not have an email set up. Please set it up on"
msgstr "حساب شما ایمیلی تنظیم نشده است. لطفاً آن را تنظیم کنید در"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "breadcrumb"
msgstr "خرده نان"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "or"
msgstr "یا"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.rank_badge_main
msgid "point"
msgstr "نقطه"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this month"
msgstr "این ماه"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.all_user_card
msgid "this week"
msgstr "این هفته"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "to receive a verification email"
msgstr "(دریافت یک ایمیل تأییدیه)"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid "xp"
msgstr "امتیاز"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"xp\n"
"                        to level up!"
msgstr ""
"ایکس‌پی\n"
"                        برای ارتقاء سطح!"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.email_validation_banner
msgid "your account settings"
msgstr "تنظیمات حساب شما"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.profile_next_rank_card
msgid ""
"{{ '%s/%s xp' % (user.karma, next_rank_id.karma_min) if next_rank_id else "
"''}}"
msgstr ""
"{{ '%s/%s اکس‌پی' % (user.karma, next_rank_id.karma_min) if next_rank_id "
"else ''}}"

#. module: website_profile
#: model:mail.template,subject:website_profile.validation_email
msgid "{{ object.company_id.name }} Profile validation"
msgstr "{{ object.company_id.name }} اعتبارسنجی پروفایل"

#. module: website_profile
#: model_terms:ir.ui.view,arch_db:website_profile.user_profile_sub_nav
msgid "└ Users"
msgstr "└ کاربران"
