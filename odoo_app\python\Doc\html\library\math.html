<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="math — Mathematical functions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/math.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides access to the mathematical functions defined by the C standard. These functions cannot be used with complex numbers; use the functions of the same name from the cmath module if..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides access to the mathematical functions defined by the C standard. These functions cannot be used with complex numbers; use the functions of the same name from the cmath module if..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>math — Mathematical functions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="cmath — Mathematical functions for complex numbers" href="cmath.html" />
    <link rel="prev" title="numbers — Numeric abstract base classes" href="numbers.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/math.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a><ul>
<li><a class="reference internal" href="#number-theoretic-and-representation-functions">Number-theoretic and representation functions</a></li>
<li><a class="reference internal" href="#power-and-logarithmic-functions">Power and logarithmic functions</a></li>
<li><a class="reference internal" href="#trigonometric-functions">Trigonometric functions</a></li>
<li><a class="reference internal" href="#angular-conversion">Angular conversion</a></li>
<li><a class="reference internal" href="#hyperbolic-functions">Hyperbolic functions</a></li>
<li><a class="reference internal" href="#special-functions">Special functions</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="numbers.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmath.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code> — Mathematical functions for complex numbers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/math.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmath.html" title="cmath — Mathematical functions for complex numbers"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="numbers.html" title="numbers — Numeric abstract base classes"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" accesskey="U">Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-math">
<span id="math-mathematical-functions"></span><h1><a class="reference internal" href="#module-math" title="math: Mathematical functions (sin() etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code></a> — Mathematical functions<a class="headerlink" href="#module-math" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides access to the mathematical functions defined by the C
standard.</p>
<p>These functions cannot be used with complex numbers; use the functions of the
same name from the <a class="reference internal" href="cmath.html#module-cmath" title="cmath: Mathematical functions for complex numbers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code></a> module if you require support for complex
numbers.  The distinction between functions which support complex numbers and
those which don’t is made since most users do not want to learn quite as much
mathematics as required to understand complex numbers.  Receiving an exception
instead of a complex result allows earlier detection of the unexpected complex
number used as a parameter, so that the programmer can determine how and why it
was generated in the first place.</p>
<p>The following functions are provided by this module.  Except when explicitly
noted otherwise, all return values are floats.</p>
<section id="number-theoretic-and-representation-functions">
<h2>Number-theoretic and representation functions<a class="headerlink" href="#number-theoretic-and-representation-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="math.ceil">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">ceil</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.ceil" title="Link to this definition">¶</a></dt>
<dd><p>Return the ceiling of <em>x</em>, the smallest integer greater than or equal to <em>x</em>.
If <em>x</em> is not a float, delegates to <a class="reference internal" href="../reference/datamodel.html#object.__ceil__" title="object.__ceil__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">x.__ceil__</span></code></a>,
which should return an <a class="reference internal" href="numbers.html#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">Integral</span></code></a> value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.comb">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">comb</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.comb" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of ways to choose <em>k</em> items from <em>n</em> items without repetition
and without order.</p>
<p>Evaluates to <code class="docutils literal notranslate"><span class="pre">n!</span> <span class="pre">/</span> <span class="pre">(k!</span> <span class="pre">*</span> <span class="pre">(n</span> <span class="pre">-</span> <span class="pre">k)!)</span></code> when <code class="docutils literal notranslate"><span class="pre">k</span> <span class="pre">&lt;=</span> <span class="pre">n</span></code> and evaluates
to zero when <code class="docutils literal notranslate"><span class="pre">k</span> <span class="pre">&gt;</span> <span class="pre">n</span></code>.</p>
<p>Also called the binomial coefficient because it is equivalent
to the coefficient of k-th term in polynomial expansion of
<code class="docutils literal notranslate"><span class="pre">(1</span> <span class="pre">+</span> <span class="pre">x)ⁿ</span></code>.</p>
<p>Raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if either of the arguments are not integers.
Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if either of the arguments are negative.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.copysign">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">copysign</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.copysign" title="Link to this definition">¶</a></dt>
<dd><p>Return a float with the magnitude (absolute value) of <em>x</em> but the sign of
<em>y</em>.  On platforms that support signed zeros, <code class="docutils literal notranslate"><span class="pre">copysign(1.0,</span> <span class="pre">-0.0)</span></code>
returns <em>-1.0</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.fabs">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">fabs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.fabs" title="Link to this definition">¶</a></dt>
<dd><p>Return the absolute value of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.factorial">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">factorial</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.factorial" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>n</em> factorial as an integer.  Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if <em>n</em> is not integral or
is negative.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Accepting floats with integral values (like <code class="docutils literal notranslate"><span class="pre">5.0</span></code>) is deprecated.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.floor">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">floor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.floor" title="Link to this definition">¶</a></dt>
<dd><p>Return the floor of <em>x</em>, the largest integer less than or equal to <em>x</em>.  If
<em>x</em> is not a float, delegates to <a class="reference internal" href="../reference/datamodel.html#object.__floor__" title="object.__floor__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">x.__floor__</span></code></a>, which
should return an <a class="reference internal" href="numbers.html#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">Integral</span></code></a> value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.fmod">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">fmod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.fmod" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">fmod(x,</span> <span class="pre">y)</span></code>, as defined by the platform C library. Note that the
Python expression <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">%</span> <span class="pre">y</span></code> may not return the same result.  The intent of the C
standard is that <code class="docutils literal notranslate"><span class="pre">fmod(x,</span> <span class="pre">y)</span></code> be exactly (mathematically; to infinite
precision) equal to <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">-</span> <span class="pre">n*y</span></code> for some integer <em>n</em> such that the result has
the same sign as <em>x</em> and magnitude less than <code class="docutils literal notranslate"><span class="pre">abs(y)</span></code>.  Python’s <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">%</span> <span class="pre">y</span></code>
returns a result with the sign of <em>y</em> instead, and may not be exactly computable
for float arguments. For example, <code class="docutils literal notranslate"><span class="pre">fmod(-1e-100,</span> <span class="pre">1e100)</span></code> is <code class="docutils literal notranslate"><span class="pre">-1e-100</span></code>, but
the result of Python’s <code class="docutils literal notranslate"><span class="pre">-1e-100</span> <span class="pre">%</span> <span class="pre">1e100</span></code> is <code class="docutils literal notranslate"><span class="pre">1e100-1e-100</span></code>, which cannot be
represented exactly as a float, and rounds to the surprising <code class="docutils literal notranslate"><span class="pre">1e100</span></code>.  For
this reason, function <a class="reference internal" href="#math.fmod" title="math.fmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">fmod()</span></code></a> is generally preferred when working with
floats, while Python’s <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">%</span> <span class="pre">y</span></code> is preferred when working with integers.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.frexp">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">frexp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.frexp" title="Link to this definition">¶</a></dt>
<dd><p>Return the mantissa and exponent of <em>x</em> as the pair <code class="docutils literal notranslate"><span class="pre">(m,</span> <span class="pre">e)</span></code>.  <em>m</em> is a float
and <em>e</em> is an integer such that <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">==</span> <span class="pre">m</span> <span class="pre">*</span> <span class="pre">2**e</span></code> exactly. If <em>x</em> is zero,
returns <code class="docutils literal notranslate"><span class="pre">(0.0,</span> <span class="pre">0)</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0.5</span> <span class="pre">&lt;=</span> <span class="pre">abs(m)</span> <span class="pre">&lt;</span> <span class="pre">1</span></code>.  This is used to “pick
apart” the internal representation of a float in a portable way.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.fsum">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">fsum</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.fsum" title="Link to this definition">¶</a></dt>
<dd><p>Return an accurate floating point sum of values in the iterable.  Avoids
loss of precision by tracking multiple intermediate partial sums.</p>
<p>The algorithm’s accuracy depends on IEEE-754 arithmetic guarantees and the
typical case where the rounding mode is half-even.  On some non-Windows
builds, the underlying C library uses extended precision addition and may
occasionally double-round an intermediate sum causing it to be off in its
least significant bit.</p>
<p>For further discussion and two alternative approaches, see the <a class="reference external" href="https://code.activestate.com/recipes/393090/">ASPN cookbook
recipes for accurate floating point summation</a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.gcd">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">gcd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">integers</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.gcd" title="Link to this definition">¶</a></dt>
<dd><p>Return the greatest common divisor of the specified integer arguments.
If any of the arguments is nonzero, then the returned value is the largest
positive integer that is a divisor of all arguments.  If all arguments
are zero, then the returned value is <code class="docutils literal notranslate"><span class="pre">0</span></code>.  <code class="docutils literal notranslate"><span class="pre">gcd()</span></code> without arguments
returns <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added support for an arbitrary number of arguments. Formerly, only two
arguments were supported.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.isclose">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">isclose</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">a</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">b</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">rel_tol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1e-09</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">abs_tol</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0.0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.isclose" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the values <em>a</em> and <em>b</em> are close to each other and
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<p>Whether or not two values are considered close is determined according to
given absolute and relative tolerances.</p>
<p><em>rel_tol</em> is the relative tolerance – it is the maximum allowed difference
between <em>a</em> and <em>b</em>, relative to the larger absolute value of <em>a</em> or <em>b</em>.
For example, to set a tolerance of 5%, pass <code class="docutils literal notranslate"><span class="pre">rel_tol=0.05</span></code>.  The default
tolerance is <code class="docutils literal notranslate"><span class="pre">1e-09</span></code>, which assures that the two values are the same
within about 9 decimal digits.  <em>rel_tol</em> must be greater than zero.</p>
<p><em>abs_tol</em> is the minimum absolute tolerance – useful for comparisons near
zero. <em>abs_tol</em> must be at least zero.</p>
<p>If no errors occur, the result will be:
<code class="docutils literal notranslate"><span class="pre">abs(a-b)</span> <span class="pre">&lt;=</span> <span class="pre">max(rel_tol</span> <span class="pre">*</span> <span class="pre">max(abs(a),</span> <span class="pre">abs(b)),</span> <span class="pre">abs_tol)</span></code>.</p>
<p>The IEEE 754 special values of <code class="docutils literal notranslate"><span class="pre">NaN</span></code>, <code class="docutils literal notranslate"><span class="pre">inf</span></code>, and <code class="docutils literal notranslate"><span class="pre">-inf</span></code> will be
handled according to IEEE rules.  Specifically, <code class="docutils literal notranslate"><span class="pre">NaN</span></code> is not considered
close to any other value, including <code class="docutils literal notranslate"><span class="pre">NaN</span></code>.  <code class="docutils literal notranslate"><span class="pre">inf</span></code> and <code class="docutils literal notranslate"><span class="pre">-inf</span></code> are only
considered close to themselves.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0485/"><strong>PEP 485</strong></a> – A function for testing approximate equality</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.isfinite">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">isfinite</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.isfinite" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>x</em> is neither an infinity nor a NaN, and
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.  (Note that <code class="docutils literal notranslate"><span class="pre">0.0</span></code> <em>is</em> considered finite.)</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.isinf">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">isinf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.isinf" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>x</em> is a positive or negative infinity, and
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.isnan">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">isnan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.isnan" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>x</em> is a NaN (not a number), and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.isqrt">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">isqrt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.isqrt" title="Link to this definition">¶</a></dt>
<dd><p>Return the integer square root of the nonnegative integer <em>n</em>. This is the
floor of the exact square root of <em>n</em>, or equivalently the greatest integer
<em>a</em> such that <em>a</em>² ≤ <em>n</em>.</p>
<p>For some applications, it may be more convenient to have the least integer
<em>a</em> such that <em>n</em> ≤ <em>a</em>², or in other words the ceiling of
the exact square root of <em>n</em>. For positive <em>n</em>, this can be computed using
<code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">=</span> <span class="pre">1</span> <span class="pre">+</span> <span class="pre">isqrt(n</span> <span class="pre">-</span> <span class="pre">1)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.lcm">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">lcm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">integers</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.lcm" title="Link to this definition">¶</a></dt>
<dd><p>Return the least common multiple of the specified integer arguments.
If all arguments are nonzero, then the returned value is the smallest
positive integer that is a multiple of all arguments.  If any of the arguments
is zero, then the returned value is <code class="docutils literal notranslate"><span class="pre">0</span></code>.  <code class="docutils literal notranslate"><span class="pre">lcm()</span></code> without arguments
returns <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.ldexp">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">ldexp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">i</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.ldexp" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">*</span> <span class="pre">(2**i)</span></code>.  This is essentially the inverse of function
<a class="reference internal" href="#math.frexp" title="math.frexp"><code class="xref py py-func docutils literal notranslate"><span class="pre">frexp()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.modf">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">modf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.modf" title="Link to this definition">¶</a></dt>
<dd><p>Return the fractional and integer parts of <em>x</em>.  Both results carry the sign
of <em>x</em> and are floats.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.nextafter">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">nextafter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">steps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.nextafter" title="Link to this definition">¶</a></dt>
<dd><p>Return the floating-point value <em>steps</em> steps after <em>x</em> towards <em>y</em>.</p>
<p>If <em>x</em> is equal to <em>y</em>, return <em>y</em>, unless <em>steps</em> is zero.</p>
<p>Examples:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">math.nextafter(x,</span> <span class="pre">math.inf)</span></code> goes up: towards positive infinity.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">math.nextafter(x,</span> <span class="pre">-math.inf)</span></code> goes down: towards minus infinity.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">math.nextafter(x,</span> <span class="pre">0.0)</span></code> goes towards zero.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">math.nextafter(x,</span> <span class="pre">math.copysign(math.inf,</span> <span class="pre">x))</span></code> goes away from zero.</p></li>
</ul>
<p>See also <a class="reference internal" href="#math.ulp" title="math.ulp"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.ulp()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Added the <em>steps</em> argument.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.perm">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">perm</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">k</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.perm" title="Link to this definition">¶</a></dt>
<dd><p>Return the number of ways to choose <em>k</em> items from <em>n</em> items
without repetition and with order.</p>
<p>Evaluates to <code class="docutils literal notranslate"><span class="pre">n!</span> <span class="pre">/</span> <span class="pre">(n</span> <span class="pre">-</span> <span class="pre">k)!</span></code> when <code class="docutils literal notranslate"><span class="pre">k</span> <span class="pre">&lt;=</span> <span class="pre">n</span></code> and evaluates
to zero when <code class="docutils literal notranslate"><span class="pre">k</span> <span class="pre">&gt;</span> <span class="pre">n</span></code>.</p>
<p>If <em>k</em> is not specified or is None, then <em>k</em> defaults to <em>n</em>
and the function returns <code class="docutils literal notranslate"><span class="pre">n!</span></code>.</p>
<p>Raises <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> if either of the arguments are not integers.
Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if either of the arguments are negative.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.prod">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">prod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">iterable</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.prod" title="Link to this definition">¶</a></dt>
<dd><p>Calculate the product of all the elements in the input <em>iterable</em>.
The default <em>start</em> value for the product is <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
<p>When the iterable is empty, return the start value.  This function is
intended specifically for use with numeric values and may reject
non-numeric types.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.remainder">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">remainder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.remainder" title="Link to this definition">¶</a></dt>
<dd><p>Return the IEEE 754-style remainder of <em>x</em> with respect to <em>y</em>.  For
finite <em>x</em> and finite nonzero <em>y</em>, this is the difference <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">-</span> <span class="pre">n*y</span></code>,
where <code class="docutils literal notranslate"><span class="pre">n</span></code> is the closest integer to the exact value of the quotient <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">/</span>
<span class="pre">y</span></code>.  If <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">/</span> <span class="pre">y</span></code> is exactly halfway between two consecutive integers, the
nearest <em>even</em> integer is used for <code class="docutils literal notranslate"><span class="pre">n</span></code>.  The remainder <code class="docutils literal notranslate"><span class="pre">r</span> <span class="pre">=</span> <span class="pre">remainder(x,</span>
<span class="pre">y)</span></code> thus always satisfies <code class="docutils literal notranslate"><span class="pre">abs(r)</span> <span class="pre">&lt;=</span> <span class="pre">0.5</span> <span class="pre">*</span> <span class="pre">abs(y)</span></code>.</p>
<p>Special cases follow IEEE 754: in particular, <code class="docutils literal notranslate"><span class="pre">remainder(x,</span> <span class="pre">math.inf)</span></code> is
<em>x</em> for any finite <em>x</em>, and <code class="docutils literal notranslate"><span class="pre">remainder(x,</span> <span class="pre">0)</span></code> and
<code class="docutils literal notranslate"><span class="pre">remainder(math.inf,</span> <span class="pre">x)</span></code> raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> for any non-NaN <em>x</em>.
If the result of the remainder operation is zero, that zero will have
the same sign as <em>x</em>.</p>
<p>On platforms using IEEE 754 binary floating-point, the result of this
operation is always exactly representable: no rounding error is introduced.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.sumprod">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">sumprod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">p</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">q</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.sumprod" title="Link to this definition">¶</a></dt>
<dd><p>Return the sum of products of values from two iterables <em>p</em> and <em>q</em>.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if the inputs do not have the same length.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">sum</span><span class="p">(</span><span class="n">itertools</span><span class="o">.</span><span class="n">starmap</span><span class="p">(</span><span class="n">operator</span><span class="o">.</span><span class="n">mul</span><span class="p">,</span> <span class="nb">zip</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">q</span><span class="p">,</span> <span class="n">strict</span><span class="o">=</span><span class="kc">True</span><span class="p">)))</span>
</pre></div>
</div>
<p>For float and mixed int/float inputs, the intermediate products
and sums are computed with extended precision.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.trunc">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">trunc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.trunc" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>x</em> with the fractional part
removed, leaving the integer part.  This rounds toward 0: <code class="docutils literal notranslate"><span class="pre">trunc()</span></code> is
equivalent to <a class="reference internal" href="#math.floor" title="math.floor"><code class="xref py py-func docutils literal notranslate"><span class="pre">floor()</span></code></a> for positive <em>x</em>, and equivalent to <a class="reference internal" href="#math.ceil" title="math.ceil"><code class="xref py py-func docutils literal notranslate"><span class="pre">ceil()</span></code></a>
for negative <em>x</em>. If <em>x</em> is not a float, delegates to <a class="reference internal" href="../reference/datamodel.html#object.__trunc__" title="object.__trunc__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">x.__trunc__</span></code></a>, which should return an <a class="reference internal" href="numbers.html#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">Integral</span></code></a> value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.ulp">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">ulp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.ulp" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the least significant bit of the float <em>x</em>:</p>
<ul class="simple">
<li><p>If <em>x</em> is a NaN (not a number), return <em>x</em>.</p></li>
<li><p>If <em>x</em> is negative, return <code class="docutils literal notranslate"><span class="pre">ulp(-x)</span></code>.</p></li>
<li><p>If <em>x</em> is a positive infinity, return <em>x</em>.</p></li>
<li><p>If <em>x</em> is equal to zero, return the smallest positive
<em>denormalized</em> representable float (smaller than the minimum positive
<em>normalized</em> float, <a class="reference internal" href="sys.html#sys.float_info" title="sys.float_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.float_info.min</span></code></a>).</p></li>
<li><p>If <em>x</em> is equal to the largest positive representable float,
return the value of the least significant bit of <em>x</em>, such that the first
float smaller than <em>x</em> is <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">-</span> <span class="pre">ulp(x)</span></code>.</p></li>
<li><p>Otherwise (<em>x</em> is a positive finite number), return the value of the least
significant bit of <em>x</em>, such that the first float bigger than <em>x</em>
is <code class="docutils literal notranslate"><span class="pre">x</span> <span class="pre">+</span> <span class="pre">ulp(x)</span></code>.</p></li>
</ul>
<p>ULP stands for “Unit in the Last Place”.</p>
<p>See also <a class="reference internal" href="#math.nextafter" title="math.nextafter"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.nextafter()</span></code></a> and <a class="reference internal" href="sys.html#sys.float_info" title="sys.float_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.float_info.epsilon</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<p>Note that <a class="reference internal" href="#math.frexp" title="math.frexp"><code class="xref py py-func docutils literal notranslate"><span class="pre">frexp()</span></code></a> and <a class="reference internal" href="#math.modf" title="math.modf"><code class="xref py py-func docutils literal notranslate"><span class="pre">modf()</span></code></a> have a different call/return pattern
than their C equivalents: they take a single argument and return a pair of
values, rather than returning their second return value through an ‘output
parameter’ (there is no such thing in Python).</p>
<p>For the <a class="reference internal" href="#math.ceil" title="math.ceil"><code class="xref py py-func docutils literal notranslate"><span class="pre">ceil()</span></code></a>, <a class="reference internal" href="#math.floor" title="math.floor"><code class="xref py py-func docutils literal notranslate"><span class="pre">floor()</span></code></a>, and <a class="reference internal" href="#math.modf" title="math.modf"><code class="xref py py-func docutils literal notranslate"><span class="pre">modf()</span></code></a> functions, note that <em>all</em>
floating-point numbers of sufficiently large magnitude are exact integers.
Python floats typically carry no more than 53 bits of precision (the same as the
platform C double type), in which case any float <em>x</em> with <code class="docutils literal notranslate"><span class="pre">abs(x)</span> <span class="pre">&gt;=</span> <span class="pre">2**52</span></code>
necessarily has no fractional bits.</p>
</section>
<section id="power-and-logarithmic-functions">
<h2>Power and logarithmic functions<a class="headerlink" href="#power-and-logarithmic-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="math.cbrt">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">cbrt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.cbrt" title="Link to this definition">¶</a></dt>
<dd><p>Return the cube root of <em>x</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.exp">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">exp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.exp" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>e</em> raised to the power <em>x</em>, where <em>e</em> = 2.718281… is the base
of natural logarithms.  This is usually more accurate than <code class="docutils literal notranslate"><span class="pre">math.e</span> <span class="pre">**</span> <span class="pre">x</span></code>
or <code class="docutils literal notranslate"><span class="pre">pow(math.e,</span> <span class="pre">x)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.exp2">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">exp2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.exp2" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>2</em> raised to the power <em>x</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.expm1">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">expm1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.expm1" title="Link to this definition">¶</a></dt>
<dd><p>Return <em>e</em> raised to the power <em>x</em>, minus 1.  Here <em>e</em> is the base of natural
logarithms.  For small floats <em>x</em>, the subtraction in <code class="docutils literal notranslate"><span class="pre">exp(x)</span> <span class="pre">-</span> <span class="pre">1</span></code>
can result in a <a class="reference external" href="https://en.wikipedia.org/wiki/Loss_of_significance">significant loss of precision</a>; the <a class="reference internal" href="#math.expm1" title="math.expm1"><code class="xref py py-func docutils literal notranslate"><span class="pre">expm1()</span></code></a>
function provides a way to compute this quantity to full precision:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">math</span> <span class="kn">import</span> <span class="n">exp</span><span class="p">,</span> <span class="n">expm1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">exp</span><span class="p">(</span><span class="mf">1e-5</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>  <span class="c1"># gives result accurate to 11 places</span>
<span class="go">1.0000050000069649e-05</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">expm1</span><span class="p">(</span><span class="mf">1e-5</span><span class="p">)</span>    <span class="c1"># result accurate to full precision</span>
<span class="go">1.0000050000166668e-05</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.log">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">log</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">base</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#math.log" title="Link to this definition">¶</a></dt>
<dd><p>With one argument, return the natural logarithm of <em>x</em> (to base <em>e</em>).</p>
<p>With two arguments, return the logarithm of <em>x</em> to the given <em>base</em>,
calculated as <code class="docutils literal notranslate"><span class="pre">log(x)/log(base)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.log1p">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">log1p</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.log1p" title="Link to this definition">¶</a></dt>
<dd><p>Return the natural logarithm of <em>1+x</em> (base <em>e</em>). The
result is calculated in a way which is accurate for <em>x</em> near zero.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.log2">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">log2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.log2" title="Link to this definition">¶</a></dt>
<dd><p>Return the base-2 logarithm of <em>x</em>. This is usually more accurate than
<code class="docutils literal notranslate"><span class="pre">log(x,</span> <span class="pre">2)</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="stdtypes.html#int.bit_length" title="int.bit_length"><code class="xref py py-meth docutils literal notranslate"><span class="pre">int.bit_length()</span></code></a> returns the number of bits necessary to represent
an integer in binary, excluding the sign and leading zeros.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.log10">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">log10</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.log10" title="Link to this definition">¶</a></dt>
<dd><p>Return the base-10 logarithm of <em>x</em>.  This is usually more accurate
than <code class="docutils literal notranslate"><span class="pre">log(x,</span> <span class="pre">10)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.pow">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">pow</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.pow" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">x</span></code> raised to the power <code class="docutils literal notranslate"><span class="pre">y</span></code>.  Exceptional cases follow
the IEEE 754 standard as far as possible.  In particular,
<code class="docutils literal notranslate"><span class="pre">pow(1.0,</span> <span class="pre">x)</span></code> and <code class="docutils literal notranslate"><span class="pre">pow(x,</span> <span class="pre">0.0)</span></code> always return <code class="docutils literal notranslate"><span class="pre">1.0</span></code>, even
when <code class="docutils literal notranslate"><span class="pre">x</span></code> is a zero or a NaN.  If both <code class="docutils literal notranslate"><span class="pre">x</span></code> and <code class="docutils literal notranslate"><span class="pre">y</span></code> are finite,
<code class="docutils literal notranslate"><span class="pre">x</span></code> is negative, and <code class="docutils literal notranslate"><span class="pre">y</span></code> is not an integer then <code class="docutils literal notranslate"><span class="pre">pow(x,</span> <span class="pre">y)</span></code>
is undefined, and raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p>Unlike the built-in <code class="docutils literal notranslate"><span class="pre">**</span></code> operator, <a class="reference internal" href="#math.pow" title="math.pow"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.pow()</span></code></a> converts both
its arguments to type <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>.  Use <code class="docutils literal notranslate"><span class="pre">**</span></code> or the built-in
<a class="reference internal" href="functions.html#pow" title="pow"><code class="xref py py-func docutils literal notranslate"><span class="pre">pow()</span></code></a> function for computing exact integer powers.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>The special cases <code class="docutils literal notranslate"><span class="pre">pow(0.0,</span> <span class="pre">-inf)</span></code> and <code class="docutils literal notranslate"><span class="pre">pow(-0.0,</span> <span class="pre">-inf)</span></code> were
changed to return <code class="docutils literal notranslate"><span class="pre">inf</span></code> instead of raising <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>,
for consistency with IEEE 754.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.sqrt">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">sqrt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.sqrt" title="Link to this definition">¶</a></dt>
<dd><p>Return the square root of <em>x</em>.</p>
</dd></dl>

</section>
<section id="trigonometric-functions">
<h2>Trigonometric functions<a class="headerlink" href="#trigonometric-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="math.acos">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">acos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.acos" title="Link to this definition">¶</a></dt>
<dd><p>Return the arc cosine of <em>x</em>, in radians. The result is between <code class="docutils literal notranslate"><span class="pre">0</span></code> and
<code class="docutils literal notranslate"><span class="pre">pi</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.asin">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">asin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.asin" title="Link to this definition">¶</a></dt>
<dd><p>Return the arc sine of <em>x</em>, in radians. The result is between <code class="docutils literal notranslate"><span class="pre">-pi/2</span></code> and
<code class="docutils literal notranslate"><span class="pre">pi/2</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.atan">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">atan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.atan" title="Link to this definition">¶</a></dt>
<dd><p>Return the arc tangent of <em>x</em>, in radians. The result is between <code class="docutils literal notranslate"><span class="pre">-pi/2</span></code> and
<code class="docutils literal notranslate"><span class="pre">pi/2</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.atan2">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">atan2</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">y</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.atan2" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">atan(y</span> <span class="pre">/</span> <span class="pre">x)</span></code>, in radians. The result is between <code class="docutils literal notranslate"><span class="pre">-pi</span></code> and <code class="docutils literal notranslate"><span class="pre">pi</span></code>.
The vector in the plane from the origin to point <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y)</span></code> makes this angle
with the positive X axis. The point of <a class="reference internal" href="#math.atan2" title="math.atan2"><code class="xref py py-func docutils literal notranslate"><span class="pre">atan2()</span></code></a> is that the signs of both
inputs are known to it, so it can compute the correct quadrant for the angle.
For example, <code class="docutils literal notranslate"><span class="pre">atan(1)</span></code> and <code class="docutils literal notranslate"><span class="pre">atan2(1,</span> <span class="pre">1)</span></code> are both <code class="docutils literal notranslate"><span class="pre">pi/4</span></code>, but <code class="docutils literal notranslate"><span class="pre">atan2(-1,</span>
<span class="pre">-1)</span></code> is <code class="docutils literal notranslate"><span class="pre">-3*pi/4</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.cos">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">cos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.cos" title="Link to this definition">¶</a></dt>
<dd><p>Return the cosine of <em>x</em> radians.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.dist">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">dist</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">p</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">q</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.dist" title="Link to this definition">¶</a></dt>
<dd><p>Return the Euclidean distance between two points <em>p</em> and <em>q</em>, each
given as a sequence (or iterable) of coordinates.  The two points
must have the same dimension.</p>
<p>Roughly equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">sqrt</span><span class="p">(</span><span class="nb">sum</span><span class="p">((</span><span class="n">px</span> <span class="o">-</span> <span class="n">qx</span><span class="p">)</span> <span class="o">**</span> <span class="mf">2.0</span> <span class="k">for</span> <span class="n">px</span><span class="p">,</span> <span class="n">qx</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">q</span><span class="p">)))</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.hypot">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">hypot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">coordinates</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.hypot" title="Link to this definition">¶</a></dt>
<dd><p>Return the Euclidean norm, <code class="docutils literal notranslate"><span class="pre">sqrt(sum(x**2</span> <span class="pre">for</span> <span class="pre">x</span> <span class="pre">in</span> <span class="pre">coordinates))</span></code>.
This is the length of the vector from the origin to the point
given by the coordinates.</p>
<p>For a two dimensional point <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y)</span></code>, this is equivalent to computing
the hypotenuse of a right triangle using the Pythagorean theorem,
<code class="docutils literal notranslate"><span class="pre">sqrt(x*x</span> <span class="pre">+</span> <span class="pre">y*y)</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added support for n-dimensional points. Formerly, only the two
dimensional case was supported.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Improved the algorithm’s accuracy so that the maximum error is
under 1 ulp (unit in the last place).  More typically, the result
is almost always correctly rounded to within 1/2 ulp.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.sin">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">sin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.sin" title="Link to this definition">¶</a></dt>
<dd><p>Return the sine of <em>x</em> radians.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.tan">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">tan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.tan" title="Link to this definition">¶</a></dt>
<dd><p>Return the tangent of <em>x</em> radians.</p>
</dd></dl>

</section>
<section id="angular-conversion">
<h2>Angular conversion<a class="headerlink" href="#angular-conversion" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="math.degrees">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">degrees</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.degrees" title="Link to this definition">¶</a></dt>
<dd><p>Convert angle <em>x</em> from radians to degrees.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.radians">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">radians</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.radians" title="Link to this definition">¶</a></dt>
<dd><p>Convert angle <em>x</em> from degrees to radians.</p>
</dd></dl>

</section>
<section id="hyperbolic-functions">
<h2>Hyperbolic functions<a class="headerlink" href="#hyperbolic-functions" title="Link to this heading">¶</a></h2>
<p><a class="reference external" href="https://en.wikipedia.org/wiki/Hyperbolic_functions">Hyperbolic functions</a>
are analogs of trigonometric functions that are based on hyperbolas
instead of circles.</p>
<dl class="py function">
<dt class="sig sig-object py" id="math.acosh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">acosh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.acosh" title="Link to this definition">¶</a></dt>
<dd><p>Return the inverse hyperbolic cosine of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.asinh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">asinh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.asinh" title="Link to this definition">¶</a></dt>
<dd><p>Return the inverse hyperbolic sine of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.atanh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">atanh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.atanh" title="Link to this definition">¶</a></dt>
<dd><p>Return the inverse hyperbolic tangent of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.cosh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">cosh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.cosh" title="Link to this definition">¶</a></dt>
<dd><p>Return the hyperbolic cosine of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.sinh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">sinh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.sinh" title="Link to this definition">¶</a></dt>
<dd><p>Return the hyperbolic sine of <em>x</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.tanh">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">tanh</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.tanh" title="Link to this definition">¶</a></dt>
<dd><p>Return the hyperbolic tangent of <em>x</em>.</p>
</dd></dl>

</section>
<section id="special-functions">
<h2>Special functions<a class="headerlink" href="#special-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="math.erf">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">erf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.erf" title="Link to this definition">¶</a></dt>
<dd><p>Return the <a class="reference external" href="https://en.wikipedia.org/wiki/Error_function">error function</a> at
<em>x</em>.</p>
<p>The <a class="reference internal" href="#math.erf" title="math.erf"><code class="xref py py-func docutils literal notranslate"><span class="pre">erf()</span></code></a> function can be used to compute traditional statistical
functions such as the <a class="reference external" href="https://en.wikipedia.org/wiki/Cumulative_distribution_function">cumulative standard normal distribution</a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">phi</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
    <span class="s1">&#39;Cumulative distribution function for the standard normal distribution&#39;</span>
    <span class="k">return</span> <span class="p">(</span><span class="mf">1.0</span> <span class="o">+</span> <span class="n">erf</span><span class="p">(</span><span class="n">x</span> <span class="o">/</span> <span class="n">sqrt</span><span class="p">(</span><span class="mf">2.0</span><span class="p">)))</span> <span class="o">/</span> <span class="mf">2.0</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.erfc">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">erfc</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.erfc" title="Link to this definition">¶</a></dt>
<dd><p>Return the complementary error function at <em>x</em>.  The <a class="reference external" href="https://en.wikipedia.org/wiki/Error_function">complementary error
function</a> is defined as
<code class="docutils literal notranslate"><span class="pre">1.0</span> <span class="pre">-</span> <span class="pre">erf(x)</span></code>.  It is used for large values of <em>x</em> where a subtraction
from one would cause a <a class="reference external" href="https://en.wikipedia.org/wiki/Loss_of_significance">loss of significance</a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.gamma">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">gamma</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.gamma" title="Link to this definition">¶</a></dt>
<dd><p>Return the <a class="reference external" href="https://en.wikipedia.org/wiki/Gamma_function">Gamma function</a> at
<em>x</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="math.lgamma">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">lgamma</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#math.lgamma" title="Link to this definition">¶</a></dt>
<dd><p>Return the natural logarithm of the absolute value of the Gamma
function at <em>x</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

</section>
<section id="constants">
<h2>Constants<a class="headerlink" href="#constants" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="math.pi">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">pi</span></span><a class="headerlink" href="#math.pi" title="Link to this definition">¶</a></dt>
<dd><p>The mathematical constant <em>π</em> = 3.141592…, to available precision.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="math.e">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">e</span></span><a class="headerlink" href="#math.e" title="Link to this definition">¶</a></dt>
<dd><p>The mathematical constant <em>e</em> = 2.718281…, to available precision.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="math.tau">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">tau</span></span><a class="headerlink" href="#math.tau" title="Link to this definition">¶</a></dt>
<dd><p>The mathematical constant <em>τ</em> = 6.283185…, to available precision.
Tau is a circle constant equal to 2<em>π</em>, the ratio of a circle’s circumference to
its radius. To learn more about Tau, check out Vi Hart’s video <a class="reference external" href="https://www.youtube.com/watch?v=jG7vhMMXagQ">Pi is (still)
Wrong</a>, and start celebrating
<a class="reference external" href="https://tauday.com/">Tau day</a> by eating twice as much pie!</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="math.inf">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">inf</span></span><a class="headerlink" href="#math.inf" title="Link to this definition">¶</a></dt>
<dd><p>A floating-point positive infinity.  (For negative infinity, use
<code class="docutils literal notranslate"><span class="pre">-math.inf</span></code>.)  Equivalent to the output of <code class="docutils literal notranslate"><span class="pre">float('inf')</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="math.nan">
<span class="sig-prename descclassname"><span class="pre">math.</span></span><span class="sig-name descname"><span class="pre">nan</span></span><a class="headerlink" href="#math.nan" title="Link to this definition">¶</a></dt>
<dd><p>A floating-point “not a number” (NaN) value. Equivalent to the output of
<code class="docutils literal notranslate"><span class="pre">float('nan')</span></code>. Due to the requirements of the <a class="reference external" href="https://en.wikipedia.org/wiki/IEEE_754">IEEE-754 standard</a>, <code class="docutils literal notranslate"><span class="pre">math.nan</span></code> and <code class="docutils literal notranslate"><span class="pre">float('nan')</span></code> are
not considered to equal to any other numeric value, including themselves. To check
whether a number is a NaN, use the <a class="reference internal" href="#math.isnan" title="math.isnan"><code class="xref py py-func docutils literal notranslate"><span class="pre">isnan()</span></code></a> function to test
for NaNs instead of <code class="docutils literal notranslate"><span class="pre">is</span></code> or <code class="docutils literal notranslate"><span class="pre">==</span></code>.
Example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">math</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">math</span><span class="o">.</span><span class="n">nan</span> <span class="o">==</span> <span class="n">math</span><span class="o">.</span><span class="n">nan</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;nan&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="nb">float</span><span class="p">(</span><span class="s1">&#39;nan&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">math</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">nan</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">math</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="s1">&#39;nan&#39;</span><span class="p">))</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>It is now always available.</p>
</div>
</dd></dl>

<div class="impl-detail compound">
<p><strong>CPython implementation detail:</strong> The <a class="reference internal" href="#module-math" title="math: Mathematical functions (sin() etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code></a> module consists mostly of thin wrappers around the platform C
math library functions.  Behavior in exceptional cases follows Annex F of
the C99 standard where appropriate.  The current implementation will raise
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> for invalid operations like <code class="docutils literal notranslate"><span class="pre">sqrt(-1.0)</span></code> or <code class="docutils literal notranslate"><span class="pre">log(0.0)</span></code>
(where C99 Annex F recommends signaling invalid operation or divide-by-zero),
and <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> for results that overflow (for example,
<code class="docutils literal notranslate"><span class="pre">exp(1000.0)</span></code>).  A NaN will not be returned from any of the functions
above unless one or more of the input arguments was a NaN; in that case,
most functions will return a NaN, but (again following C99 Annex F) there
are some exceptions to this rule, for example <code class="docutils literal notranslate"><span class="pre">pow(float('nan'),</span> <span class="pre">0.0)</span></code> or
<code class="docutils literal notranslate"><span class="pre">hypot(float('nan'),</span> <span class="pre">float('inf'))</span></code>.</p>
<p>Note that Python makes no effort to distinguish signaling NaNs from
quiet NaNs, and behavior for signaling NaNs remains unspecified.
Typical behavior is to treat all NaNs as though they were quiet.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="cmath.html#module-cmath" title="cmath: Mathematical functions for complex numbers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code></a></dt><dd><p>Complex number versions of many of these functions.</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a><ul>
<li><a class="reference internal" href="#number-theoretic-and-representation-functions">Number-theoretic and representation functions</a></li>
<li><a class="reference internal" href="#power-and-logarithmic-functions">Power and logarithmic functions</a></li>
<li><a class="reference internal" href="#trigonometric-functions">Trigonometric functions</a></li>
<li><a class="reference internal" href="#angular-conversion">Angular conversion</a></li>
<li><a class="reference internal" href="#hyperbolic-functions">Hyperbolic functions</a></li>
<li><a class="reference internal" href="#special-functions">Special functions</a></li>
<li><a class="reference internal" href="#constants">Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="numbers.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmath.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmath</span></code> — Mathematical functions for complex numbers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/math.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmath.html" title="cmath — Mathematical functions for complex numbers"
             >next</a> |</li>
        <li class="right" >
          <a href="numbers.html" title="numbers — Numeric abstract base classes"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" >Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>