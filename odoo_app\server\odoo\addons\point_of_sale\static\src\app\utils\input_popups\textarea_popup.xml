<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.TextAreaPopup">
        <div class="popup popup-textarea">
            <div class="modal-header">
                <h4 class="modal-title"><t t-esc="props.title" /></h4>
                <!-- when modal fixed <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>-->
            </div>

            <div class="modal-body">
                <textarea class="form-control" t-ref="input" placeholder="Add a note..." t-model="state.inputValue" rows="4" />
            </div>

            <footer class="footer footer-flex modal-footer">
                <div class="button confirm highlight btn btn-lg btn-primary" t-on-click="confirm">
                    <t t-esc="props.confirmText" />
                </div>
                <div class="button cancel btn btn-lg btn-secondary" t-on-click="cancel">
                    <t t-esc="props.cancelText" />
                </div>
            </footer>
        </div>
    </t>

</templates>
