.product:hover {
    border: 1px solid $o-gray-100;
}

.product-img img {
    height: 100px;
    object-fit: contain;
}

.pos-receipt {
    .product-name {
        -webkit-line-clamp: unset;
        overflow-wrap: anywhere;
    }
}

.product-name {

    box-sizing: border-box;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;

    &.no-image {
        -webkit-line-clamp: 7;
    }
}

@media screen and (max-width: 768px) {
    .product:active {
        box-shadow: 0 0 0 2px $primary;
    }
}

.product-information-tag {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 30px 30px 0;
    border-color: transparent #9a9ea180 transparent transparent;
    position: absolute;
    top: 0;
    right: 0;
    color: white;
    text-align: center;
}

.product-information-tag-logo {
    position: absolute;
    left: 19px;
    top: 3px;
}
