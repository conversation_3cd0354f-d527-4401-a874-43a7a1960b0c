<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="__future__ — Future statement definitions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/__future__.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/__future__.py Imports of the form from __future__ import feature are called future statements. These are special-cased by the Python compiler to allow the use of new Python feature..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/__future__.py Imports of the form from __future__ import feature are called future statements. These are special-cased by the Python compiler to allow the use of new Python feature..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>__future__ — Future statement definitions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="gc — Garbage Collector interface" href="gc.html" />
    <link rel="prev" title="traceback — Print or retrieve a stack traceback" href="traceback.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/__future__.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="traceback.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/__future__.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gc.html" title="gc — Garbage Collector interface"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="traceback.html" title="traceback — Print or retrieve a stack traceback"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" accesskey="U">Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-__future__">
<span id="future-future-statement-definitions"></span><h1><a class="reference internal" href="#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a> — Future statement definitions<a class="headerlink" href="#module-__future__" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/__future__.py">Lib/__future__.py</a></p>
<hr class="docutils" />
<p>Imports of the form <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">feature</span></code> are called
<a class="reference internal" href="../reference/simple_stmts.html#future"><span class="std std-ref">future statements</span></a>. These are special-cased by the Python compiler
to allow the use of new Python features in modules containing the future statement
before the release in which the feature becomes standard.</p>
<p>While these future statements are given additional special meaning by the
Python compiler, they are still executed like any other import statement and
the <a class="reference internal" href="#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a> exists and is handled by the import system the same way
any other Python module would be. This design serves three purposes:</p>
<ul class="simple">
<li><p>To avoid confusing existing tools that analyze import statements and expect to
find the modules they’re importing.</p></li>
<li><p>To document when incompatible changes were introduced, and when they will be
— or were — made mandatory.  This is a form of executable documentation, and
can be inspected programmatically via importing <a class="reference internal" href="#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a> and examining
its contents.</p></li>
<li><p>To ensure that <a class="reference internal" href="../reference/simple_stmts.html#future"><span class="std std-ref">future statements</span></a> run under releases prior to
Python 2.1 at least yield runtime exceptions (the import of <a class="reference internal" href="#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a>
will fail, because there was no module of that name prior to 2.1).</p></li>
</ul>
<section id="module-contents">
<h2>Module Contents<a class="headerlink" href="#module-contents" title="Link to this heading">¶</a></h2>
<p>No feature description will ever be deleted from <a class="reference internal" href="#module-__future__" title="__future__: Future statement definitions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code></a>. Since its
introduction in Python 2.1 the following features have found their way into the
language using this mechanism:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>feature</p></th>
<th class="head"><p>optional in</p></th>
<th class="head"><p>mandatory in</p></th>
<th class="head"><p>effect</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>nested_scopes</p></td>
<td><p>2.1.0b1</p></td>
<td><p>2.2</p></td>
<td><p><span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0227/"><strong>PEP 227</strong></a>:
<em>Statically Nested Scopes</em></p></td>
</tr>
<tr class="row-odd"><td><p>generators</p></td>
<td><p>2.2.0a1</p></td>
<td><p>2.3</p></td>
<td><p><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0255/"><strong>PEP 255</strong></a>:
<em>Simple Generators</em></p></td>
</tr>
<tr class="row-even"><td><p>division</p></td>
<td><p>2.2.0a2</p></td>
<td><p>3.0</p></td>
<td><p><span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0238/"><strong>PEP 238</strong></a>:
<em>Changing the Division Operator</em></p></td>
</tr>
<tr class="row-odd"><td><p>absolute_import</p></td>
<td><p>2.5.0a1</p></td>
<td><p>3.0</p></td>
<td><p><span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0328/"><strong>PEP 328</strong></a>:
<em>Imports: Multi-Line and Absolute/Relative</em></p></td>
</tr>
<tr class="row-even"><td><p>with_statement</p></td>
<td><p>2.5.0a1</p></td>
<td><p>2.6</p></td>
<td><p><span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0343/"><strong>PEP 343</strong></a>:
<em>The “with” Statement</em></p></td>
</tr>
<tr class="row-odd"><td><p>print_function</p></td>
<td><p>2.6.0a2</p></td>
<td><p>3.0</p></td>
<td><p><span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-3105/"><strong>PEP 3105</strong></a>:
<em>Make print a function</em></p></td>
</tr>
<tr class="row-even"><td><p>unicode_literals</p></td>
<td><p>2.6.0a2</p></td>
<td><p>3.0</p></td>
<td><p><span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-3112/"><strong>PEP 3112</strong></a>:
<em>Bytes literals in Python 3000</em></p></td>
</tr>
<tr class="row-odd"><td><p>generator_stop</p></td>
<td><p>3.5.0b1</p></td>
<td><p>3.7</p></td>
<td><p><span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0479/"><strong>PEP 479</strong></a>:
<em>StopIteration handling inside generators</em></p></td>
</tr>
<tr class="row-even"><td><p>annotations</p></td>
<td><p>3.7.0b1</p></td>
<td><p>TBD <a class="footnote-reference brackets" href="#id2" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
<td><p><span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0563/"><strong>PEP 563</strong></a>:
<em>Postponed evaluation of annotations</em></p></td>
</tr>
</tbody>
</table>
<dl class="py class" id="future-classes">
<dt class="sig sig-object py" id="future__._Feature">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">__future__.</span></span><span class="sig-name descname"><span class="pre">_Feature</span></span><a class="headerlink" href="#future__._Feature" title="Link to this definition">¶</a></dt>
<dd><p>Each statement in <code class="file docutils literal notranslate"><span class="pre">__future__.py</span></code> is of the form:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">FeatureName</span> <span class="o">=</span> <span class="n">_Feature</span><span class="p">(</span><span class="n">OptionalRelease</span><span class="p">,</span> <span class="n">MandatoryRelease</span><span class="p">,</span>
                       <span class="n">CompilerFlag</span><span class="p">)</span>
</pre></div>
</div>
<p>where, normally, <em>OptionalRelease</em> is less than <em>MandatoryRelease</em>, and both are
5-tuples of the same form as <a class="reference internal" href="sys.html#sys.version_info" title="sys.version_info"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.version_info</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">PY_MAJOR_VERSION</span><span class="p">,</span> <span class="c1"># the 2 in 2.1.0a3; an int</span>
 <span class="n">PY_MINOR_VERSION</span><span class="p">,</span> <span class="c1"># the 1; an int</span>
 <span class="n">PY_MICRO_VERSION</span><span class="p">,</span> <span class="c1"># the 0; an int</span>
 <span class="n">PY_RELEASE_LEVEL</span><span class="p">,</span> <span class="c1"># &quot;alpha&quot;, &quot;beta&quot;, &quot;candidate&quot; or &quot;final&quot;; string</span>
 <span class="n">PY_RELEASE_SERIAL</span> <span class="c1"># the 3; an int</span>
<span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="future__._Feature.getOptionalRelease">
<span class="sig-prename descclassname"><span class="pre">_Feature.</span></span><span class="sig-name descname"><span class="pre">getOptionalRelease</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#future__._Feature.getOptionalRelease" title="Link to this definition">¶</a></dt>
<dd><p><em>OptionalRelease</em> records the first release in which the feature was accepted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="future__._Feature.getMandatoryRelease">
<span class="sig-prename descclassname"><span class="pre">_Feature.</span></span><span class="sig-name descname"><span class="pre">getMandatoryRelease</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#future__._Feature.getMandatoryRelease" title="Link to this definition">¶</a></dt>
<dd><p>In the case of a <em>MandatoryRelease</em> that has not yet occurred,
<em>MandatoryRelease</em> predicts the release in which the feature will become part of
the language.</p>
<p>Else <em>MandatoryRelease</em> records when the feature became part of the language; in
releases at or after that, modules no longer need a future statement to use the
feature in question, but may continue to use such imports.</p>
<p><em>MandatoryRelease</em> may also be <code class="docutils literal notranslate"><span class="pre">None</span></code>, meaning that a planned feature got
dropped or that it is not yet decided.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="future__._Feature.compiler_flag">
<span class="sig-prename descclassname"><span class="pre">_Feature.</span></span><span class="sig-name descname"><span class="pre">compiler_flag</span></span><a class="headerlink" href="#future__._Feature.compiler_flag" title="Link to this definition">¶</a></dt>
<dd><p><em>CompilerFlag</em> is the (bitfield) flag that should be passed in the fourth
argument to the built-in function <a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a> to enable the feature in
dynamically compiled code.  This flag is stored in the <a class="reference internal" href="#future__._Feature.compiler_flag" title="__future__._Feature.compiler_flag"><code class="xref py py-attr docutils literal notranslate"><span class="pre">_Feature.compiler_flag</span></code></a>
attribute on <a class="reference internal" href="#future__._Feature" title="__future__._Feature"><code class="xref py py-class docutils literal notranslate"><span class="pre">_Feature</span></code></a> instances.</p>
</dd></dl>

<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id2" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p><code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">__future__</span> <span class="pre">import</span> <span class="pre">annotations</span></code> was previously scheduled to
become mandatory in Python 3.10, but the Python Steering Council
twice decided to delay the change
(<a class="reference external" href="https://mail.python.org/archives/list/python-dev&#64;python.org/message/CLVXXPQ2T2LQ5MP2Y53VVQFCXYWQJHKZ/">announcement for Python 3.10</a>;
<a class="reference external" href="https://mail.python.org/archives/list/python-dev&#64;python.org/message/VIZEBX5EYMSYIJNDBF6DMUMZOCWHARSO/">announcement for Python 3.11</a>).
No final decision has been made yet. See also <span class="target" id="index-9"></span><a class="pep reference external" href="https://peps.python.org/pep-0563/"><strong>PEP 563</strong></a> and <span class="target" id="index-10"></span><a class="pep reference external" href="https://peps.python.org/pep-0649/"><strong>PEP 649</strong></a>.</p>
</aside>
</aside>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference internal" href="../reference/simple_stmts.html#future"><span class="std std-ref">Future statements</span></a></dt><dd><p>How the compiler treats future imports.</p>
</dd>
<dt><span class="target" id="index-11"></span><a class="pep reference external" href="https://peps.python.org/pep-0236/"><strong>PEP 236</strong></a> - Back to the __future__</dt><dd><p>The original proposal for the __future__ mechanism.</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a><ul>
<li><a class="reference internal" href="#module-contents">Module Contents</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="traceback.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">traceback</span></code> — Print or retrieve a stack traceback</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="gc.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gc</span></code> — Garbage Collector interface</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/__future__.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="gc.html" title="gc — Garbage Collector interface"
             >next</a> |</li>
        <li class="right" >
          <a href="traceback.html" title="traceback — Print or retrieve a stack traceback"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="python.html" >Python Runtime Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">__future__</span></code> — Future statement definitions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>