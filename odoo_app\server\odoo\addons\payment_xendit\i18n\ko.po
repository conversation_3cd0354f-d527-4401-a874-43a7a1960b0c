# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_xendit
# 
# Translators:
# Wil Odoo, 2024
# Sarah Park, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 10:46+0000\n"
"PO-Revision-Date: 2024-01-23 08:23+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment (status %s). Please "
"try again."
msgstr "결제를 처리하는 중 오류가 발생했습니다. (상태 %s) 다시 시도해 주세요."

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__code
msgid "Code"
msgstr "코드"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "API 연결을 설정할 수 없습니다."

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "%s 참조와 일치하는 거래 항목이 없습니다."

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_provider
msgid "Payment Provider"
msgstr "결제대행업체"

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_transaction
msgid "Payment Transaction"
msgstr "지불 거래"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference."
msgstr "참조가 누락된 데이터가 수신되었습니다."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Secret Key"
msgstr "비밀 키"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
#, python-format
msgid ""
"The communication with the API failed. Xendit gave us the following "
"information: '%s'"
msgstr "API와의 통신에 실패했습니다. Xendit에서 다음 정보를 확인했습니다: '%s'"

#. module: payment_xendit
#: model:ir.model.fields,help:payment_xendit.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "이 결제대행업체의 기술 코드입니다."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Webhook Token"
msgstr "웹훅 토큰"

#. module: payment_xendit
#: model:ir.model.fields.selection,name:payment_xendit.selection__payment_provider__code__xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_secret_key
msgid "Xendit Secret Key"
msgstr "Xendit 보안 키"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_webhook_token
msgid "Xendit Webhook Token"
msgstr "Xendit 웹훅 토큰"
