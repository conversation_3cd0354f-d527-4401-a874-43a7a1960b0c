# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_gamification
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# JanaAvalah, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> Õigus <<EMAIL>>, 2023
# Anna, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Anna, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""
"Kasutaja poolt on seatud eesmärk ja eesmärgi tüüp.\n"
"                    Eesmärke saab luua automaatselt, kasutades väljakutseid."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr ""
"Kõik töötaja tunnustusmärgid, mis on seotud töötajaga otseselt või läbi "
"kasutajakonto"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""
"Määrake valitud kasutajate hindamiseks eesmärkide nimekiri.\n"
"                    Väljakutseid saab kasutada automaatseks eesmärkide loomiseks periooditi (iga nädal, iga kuu ... ).\n"
"                    Eesmärgid on loodud määratud kasutajatele või grupi liikmele."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr "Tunnustusmärgid"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr ""
"Märgid on tunnustused hea töö eest. Anna neid inimestele, kes teie meelest "
"väärivad seda."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr "Töötajaga otseselt seotud tunnustusmärgid"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee_base
msgid "Basic Employee"
msgstr "Keskmine töötaja"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "Tühista"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr "Väljakutsed"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr "Loo uus väljakutse"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr "Loo uus eesmärk"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr "Kirjelda, mille eest premeerid töötajat tunnustusmärgiga"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Direct Badge"
msgstr "Otsene tunnustusmärk"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "Töötaja"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__badge_ids
msgid "Employee Badges"
msgstr "Töötaja tunnustusmärgid"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__goal_ids
msgid "Employee HR Goals"
msgstr "Töötaja personali eesmärgid"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Tunnustusmärk"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Kasutaja tunnustusmärgid"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Juhend \"Kasutaja tunnustusmärgid\""

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr "Eesmärk"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr "Eesmärkide ajalugu"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr "Andke tunnustusmärk"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr "Andke sellele töötajale esimene tunnustusmärk"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr "Tunnustatud"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr "Tunnustatud töötajate arv"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__has_badges
msgid "Has Badges"
msgstr "Olemasolevad tunnustusmärgid"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr "Saadud tunnustusmärgid"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr "Tunnusta töötajat"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr "Tunnusta töötajat koos"

#. module: hr_gamification
#. odoo-python
#: code:addons/hr_gamification/models/gamification.py:0
#, python-format
msgid "The selected employee does not correspond to the selected user."
msgstr "Valitud töötaja ei vasta valitud kasutajale."

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr "Kasutaja"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr "Mille üle olete tänulik?"

#. module: hr_gamification
#. odoo-python
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can not send a badge to yourself."
msgstr "Märki ei saa saata iseendale."

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr "tunnustada selle töötaja head tegevust"
