<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tkinter — Python interface to Tcl/Tk" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tkinter.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tkinter/__init__.py The tkinter package (“Tk interface”) is the standard Python interface to the Tcl/Tk GUI toolkit. Both Tk and tkinter are available on most Unix platforms, inclu..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tkinter/__init__.py The tkinter package (“Tk interface”) is the standard Python interface to the Tcl/Tk GUI toolkit. Both Tk and tkinter are available on most Unix platforms, inclu..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tkinter — Python interface to Tcl/Tk &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tkinter.colorchooser — Color choosing dialog" href="tkinter.colorchooser.html" />
    <link rel="prev" title="Graphical User Interfaces with Tk" href="tk.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tkinter.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a><ul>
<li><a class="reference internal" href="#architecture">Architecture</a></li>
<li><a class="reference internal" href="#tkinter-modules">Tkinter Modules</a></li>
<li><a class="reference internal" href="#tkinter-life-preserver">Tkinter Life Preserver</a><ul>
<li><a class="reference internal" href="#a-hello-world-program">A Hello World Program</a></li>
<li><a class="reference internal" href="#important-tk-concepts">Important Tk Concepts</a></li>
<li><a class="reference internal" href="#understanding-how-tkinter-wraps-tcl-tk">Understanding How Tkinter Wraps Tcl/Tk</a></li>
<li><a class="reference internal" href="#how-do-i-what-option-does">How do I…? What option does…?</a></li>
<li><a class="reference internal" href="#navigating-the-tcl-tk-reference-manual">Navigating the Tcl/Tk Reference Manual</a></li>
</ul>
</li>
<li><a class="reference internal" href="#threading-model">Threading model</a></li>
<li><a class="reference internal" href="#handy-reference">Handy Reference</a><ul>
<li><a class="reference internal" href="#setting-options">Setting Options</a></li>
<li><a class="reference internal" href="#the-packer">The Packer</a></li>
<li><a class="reference internal" href="#packer-options">Packer Options</a></li>
<li><a class="reference internal" href="#coupling-widget-variables">Coupling Widget Variables</a></li>
<li><a class="reference internal" href="#the-window-manager">The Window Manager</a></li>
<li><a class="reference internal" href="#tk-option-data-types">Tk Option Data Types</a></li>
<li><a class="reference internal" href="#bindings-and-events">Bindings and Events</a></li>
<li><a class="reference internal" href="#the-index-parameter">The index Parameter</a></li>
<li><a class="reference internal" href="#images">Images</a></li>
</ul>
</li>
<li><a class="reference internal" href="#file-handlers">File Handlers</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tk.html"
                          title="previous chapter">Graphical User Interfaces with Tk</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.colorchooser.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code> — Color choosing dialog</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.colorchooser.html" title="tkinter.colorchooser — Color choosing dialog"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tk.html" title="Graphical User Interfaces with Tk"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tkinter">
<span id="tkinter-python-interface-to-tcl-tk"></span><h1><a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> — Python interface to Tcl/Tk<a class="headerlink" href="#module-tkinter" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tkinter/__init__.py">Lib/tkinter/__init__.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> package (“Tk interface”) is the standard Python interface to
the Tcl/Tk GUI toolkit.  Both Tk and <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> are available on most Unix
platforms, including macOS, as well as on Windows systems.</p>
<p>Running <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">tkinter</span></code> from the command line should open a window
demonstrating a simple Tk interface, letting you know that <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> is
properly installed on your system, and also showing what version of Tcl/Tk is
installed, so you can read the Tcl/Tk documentation specific to that version.</p>
<p>Tkinter supports a range of Tcl/Tk versions, built either with or
without thread support. The official Python binary release bundles Tcl/Tk 8.6
threaded. See the source code for the <a class="reference internal" href="#module-_tkinter" title="_tkinter: A binary module that contains the low-level interface to Tcl/Tk."><code class="xref py py-mod docutils literal notranslate"><span class="pre">_tkinter</span></code></a> module
for more information about supported versions.</p>
<p>Tkinter is not a thin wrapper, but adds a fair amount of its own logic to
make the experience more pythonic. This documentation will concentrate on these
additions and changes, and refer to the official Tcl/Tk documentation for
details that are unchanged.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Tcl/Tk 8.5 (2007) introduced a modern set of themed user interface components
along with a new API to use them. Both old and new APIs are still available.
Most documentation you will find online still uses the old API and
can be woefully outdated.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><dl class="simple">
<dt><a class="reference external" href="https://tkdocs.com/">TkDocs</a></dt><dd><p>Extensive tutorial on creating user interfaces with Tkinter.  Explains key concepts,
and illustrates recommended approaches using the modern API.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference external" href="https://www.tkdocs.com/shipman/">Tkinter 8.5 reference: a GUI for Python</a></dt><dd><p>Reference documentation for Tkinter 8.5 detailing available classes, methods, and options.</p>
</dd>
</dl>
</li>
</ul>
<p>Tcl/Tk Resources:</p>
<ul class="simple">
<li><dl class="simple">
<dt><a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/contents.htm">Tk commands</a></dt><dd><p>Comprehensive reference to each of the underlying Tcl/Tk commands used by Tkinter.</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference external" href="https://www.tcl.tk">Tcl/Tk Home Page</a></dt><dd><p>Additional documentation, and links to Tcl/Tk core development.</p>
</dd>
</dl>
</li>
</ul>
<p>Books:</p>
<ul class="simple">
<li><dl class="simple">
<dt><a class="reference external" href="https://tkdocs.com/book.html">Modern Tkinter for Busy Python Developers</a></dt><dd><p>By Mark Roseman. (ISBN 978-1999149567)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference external" href="https://www.packtpub.com/product/python-gui-programming-with-tkinter/9781788835886">Python GUI programming with Tkinter</a></dt><dd><p>By Alan D. Moore. (ISBN 978-1788835886)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference external" href="https://learning-python.com/about-pp4e.html">Programming Python</a></dt><dd><p>By Mark Lutz; has excellent coverage of Tkinter. (ISBN 978-0596158101)</p>
</dd>
</dl>
</li>
<li><dl class="simple">
<dt><a class="reference external" href="https://www.amazon.com/exec/obidos/ASIN/032133633X">Tcl and the Tk Toolkit (2nd edition)</a></dt><dd><p>By John Ousterhout, inventor of Tcl/Tk, and Ken Jones; does not cover Tkinter. (ISBN 978-0321336330)</p>
</dd>
</dl>
</li>
</ul>
</div>
<section id="architecture">
<h2>Architecture<a class="headerlink" href="#architecture" title="Link to this heading">¶</a></h2>
<p>Tcl/Tk is not a single library but rather consists of a few distinct
modules, each with separate functionality and its own official
documentation. Python’s binary releases also ship an add-on module
together with it.</p>
<dl class="simple">
<dt>Tcl</dt><dd><p>Tcl is a dynamic interpreted programming language, just like Python. Though
it can be used on its own as a general-purpose programming language, it is
most commonly embedded into C applications as a scripting engine or an
interface to the Tk toolkit. The Tcl library has a C interface to
create and manage one or more instances of a Tcl interpreter, run Tcl
commands and scripts in those instances, and add custom commands
implemented in either Tcl or C. Each interpreter has an event queue,
and there are facilities to send events to it and process them.
Unlike Python, Tcl’s execution model is designed around cooperative
multitasking, and Tkinter bridges this difference
(see <a class="reference internal" href="#threading-model">Threading model</a> for details).</p>
</dd>
<dt>Tk</dt><dd><p>Tk is a <a class="reference external" href="https://wiki.tcl-lang.org/37432">Tcl package</a> implemented in C
that adds custom commands to create and manipulate GUI widgets. Each
<a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a> object embeds its own Tcl interpreter instance with Tk loaded into
it. Tk’s widgets are very customizable, though at the cost of a dated appearance.
Tk uses Tcl’s event queue to generate and process GUI events.</p>
</dd>
<dt>Ttk</dt><dd><p>Themed Tk (Ttk) is a newer family of Tk widgets that provide a much better
appearance on different platforms than many of the classic Tk widgets.
Ttk is distributed as part of Tk, starting with Tk version 8.5. Python
bindings are provided in a separate module, <a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a>.</p>
</dd>
</dl>
<p>Internally, Tk and Ttk use facilities of the underlying operating system,
i.e., Xlib on Unix/X11, Cocoa on macOS, GDI on Windows.</p>
<p>When your Python application uses a class in Tkinter, e.g., to create a widget,
the <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> module first assembles a Tcl/Tk command string. It passes that
Tcl command string to an internal <a class="reference internal" href="#module-_tkinter" title="_tkinter: A binary module that contains the low-level interface to Tcl/Tk."><code class="xref py py-mod docutils literal notranslate"><span class="pre">_tkinter</span></code></a> binary module, which then
calls the Tcl interpreter to evaluate it. The Tcl interpreter will then call into the
Tk and/or Ttk packages, which will in turn make calls to Xlib, Cocoa, or GDI.</p>
</section>
<section id="tkinter-modules">
<h2>Tkinter Modules<a class="headerlink" href="#tkinter-modules" title="Link to this heading">¶</a></h2>
<p>Support for Tkinter is spread across several modules. Most applications will need the
main <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> module, as well as the <a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> module, which provides
the modern themed widget set and API:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="o">*</span>
<span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
</pre></div>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.Tk">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.</span></span><span class="sig-name descname"><span class="pre">Tk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">screenName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">baseName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">className</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Tk'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">useTk</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sync</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">use</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.Tk" title="Link to this definition">¶</a></dt>
<dd><p>Construct a toplevel Tk widget, which is usually the main window of an
application, and initialize a Tcl interpreter for this widget.  Each
instance has its own associated Tcl interpreter.</p>
<p>The <a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a> class is typically instantiated using all default values.
However, the following keyword arguments are currently recognized:</p>
<dl>
<dt><em>screenName</em></dt><dd><p>When given (as a string), sets the <span class="target" id="index-0"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">DISPLAY</span></code> environment
variable. (X11 only)</p>
</dd>
<dt><em>baseName</em></dt><dd><p>Name of the profile file.  By default, <em>baseName</em> is derived from the
program name (<code class="docutils literal notranslate"><span class="pre">sys.argv[0]</span></code>).</p>
</dd>
<dt><em>className</em></dt><dd><p>Name of the widget class.  Used as a profile file and also as the name
with which Tcl is invoked (<em>argv0</em> in <em>interp</em>).</p>
</dd>
<dt><em>useTk</em></dt><dd><p>If <code class="docutils literal notranslate"><span class="pre">True</span></code>, initialize the Tk subsystem.  The <a class="reference internal" href="#tkinter.Tcl" title="tkinter.Tcl"><code class="xref py py-func docutils literal notranslate"><span class="pre">tkinter.Tcl()</span></code></a>
function sets this to <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
</dd>
<dt><em>sync</em></dt><dd><p>If <code class="docutils literal notranslate"><span class="pre">True</span></code>, execute all X server commands synchronously, so that errors
are reported immediately.  Can be used for debugging. (X11 only)</p>
</dd>
<dt><em>use</em></dt><dd><p>Specifies the <em>id</em> of the window in which to embed the application,
instead of it being created as an independent toplevel window. <em>id</em> must
be specified in the same way as the value for the -use option for
toplevel widgets (that is, it has a form like that returned by
<code class="xref py py-meth docutils literal notranslate"><span class="pre">winfo_id()</span></code>).</p>
<p>Note that on some platforms this will only work correctly if <em>id</em> refers
to a Tk frame or toplevel that has its -container option enabled.</p>
</dd>
</dl>
<p><a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a> reads and interprets profile files, named
<code class="file docutils literal notranslate"><span class="pre">.</span><em><span class="pre">className</span></em><span class="pre">.tcl</span></code> and <code class="file docutils literal notranslate"><span class="pre">.</span><em><span class="pre">baseName</span></em><span class="pre">.tcl</span></code>, into the Tcl
interpreter and calls <a class="reference internal" href="functions.html#exec" title="exec"><code class="xref py py-func docutils literal notranslate"><span class="pre">exec()</span></code></a> on the contents of
<code class="file docutils literal notranslate"><span class="pre">.</span><em><span class="pre">className</span></em><span class="pre">.py</span></code> and <code class="file docutils literal notranslate"><span class="pre">.</span><em><span class="pre">baseName</span></em><span class="pre">.py</span></code>.  The path for the
profile files is the <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">HOME</span></code> environment variable or, if that
isn’t defined, then <a class="reference internal" href="os.html#os.curdir" title="os.curdir"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.curdir</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="tkinter.Tk.tk">
<span class="sig-name descname"><span class="pre">tk</span></span><a class="headerlink" href="#tkinter.Tk.tk" title="Link to this definition">¶</a></dt>
<dd><p>The Tk application object created by instantiating <a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a>.  This
provides access to the Tcl interpreter.  Each widget that is attached
the same instance of <a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a> has the same value for its <a class="reference internal" href="#tkinter.Tk.tk" title="tkinter.Tk.tk"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tk</span></code></a>
attribute.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tkinter.Tk.master">
<span class="sig-name descname"><span class="pre">master</span></span><a class="headerlink" href="#tkinter.Tk.master" title="Link to this definition">¶</a></dt>
<dd><p>The widget object that contains this widget.  For <a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a>, the
<em>master</em> is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> because it is the main window.  The terms
<em>master</em> and <em>parent</em> are similar and sometimes used interchangeably
as argument names; however, calling <code class="xref py py-meth docutils literal notranslate"><span class="pre">winfo_parent()</span></code> returns a
string of the widget name whereas <a class="reference internal" href="#tkinter.Tk.master" title="tkinter.Tk.master"><code class="xref py py-attr docutils literal notranslate"><span class="pre">master</span></code></a> returns the object.
<em>parent</em>/<em>child</em> reflects the tree-like relationship while
<em>master</em>/<em>slave</em> reflects the container structure.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="tkinter.Tk.children">
<span class="sig-name descname"><span class="pre">children</span></span><a class="headerlink" href="#tkinter.Tk.children" title="Link to this definition">¶</a></dt>
<dd><p>The immediate descendants of this widget as a <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dict</span></code></a> with the
child widget names as the keys and the child instance objects as the
values.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="tkinter.Tcl">
<span class="sig-prename descclassname"><span class="pre">tkinter.</span></span><span class="sig-name descname"><span class="pre">Tcl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">screenName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">baseName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">className</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Tk'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">useTk</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.Tcl" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#tkinter.Tcl" title="tkinter.Tcl"><code class="xref py py-func docutils literal notranslate"><span class="pre">Tcl()</span></code></a> function is a factory function which creates an object much like
that created by the <a class="reference internal" href="#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code></a> class, except that it does not initialize the Tk
subsystem.  This is most often useful when driving the Tcl interpreter in an
environment where one doesn’t want to create extraneous toplevel windows, or
where one cannot (such as Unix/Linux systems without an X server).  An object
created by the <a class="reference internal" href="#tkinter.Tcl" title="tkinter.Tcl"><code class="xref py py-func docutils literal notranslate"><span class="pre">Tcl()</span></code></a> object can have a Toplevel window created (and the Tk
subsystem initialized) by calling its <code class="xref py py-meth docutils literal notranslate"><span class="pre">loadtk()</span></code> method.</p>
</dd></dl>

<p>The modules that provide Tk support include:</p>
<dl class="simple">
<dt><a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a></dt><dd><p>Main Tkinter module.</p>
</dd>
<dt><a class="reference internal" href="tkinter.colorchooser.html#module-tkinter.colorchooser" title="tkinter.colorchooser: Color choosing dialog (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code></a></dt><dd><p>Dialog to let the user choose a color.</p>
</dd>
<dt><a class="reference internal" href="dialog.html#module-tkinter.commondialog" title="tkinter.commondialog: Tkinter base class for dialogs (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.commondialog</span></code></a></dt><dd><p>Base class for the dialogs defined in the other modules listed here.</p>
</dd>
<dt><a class="reference internal" href="dialog.html#module-tkinter.filedialog" title="tkinter.filedialog: Dialog classes for file selection (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.filedialog</span></code></a></dt><dd><p>Common dialogs to allow the user to specify a file to open or save.</p>
</dd>
<dt><a class="reference internal" href="tkinter.font.html#module-tkinter.font" title="tkinter.font: Tkinter font-wrapping class (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.font</span></code></a></dt><dd><p>Utilities to help work with fonts.</p>
</dd>
<dt><a class="reference internal" href="tkinter.messagebox.html#module-tkinter.messagebox" title="tkinter.messagebox: Various types of alert dialogs (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.messagebox</span></code></a></dt><dd><p>Access to standard Tk dialog boxes.</p>
</dd>
<dt><a class="reference internal" href="tkinter.scrolledtext.html#module-tkinter.scrolledtext" title="tkinter.scrolledtext: Text widget with a vertical scroll bar. (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.scrolledtext</span></code></a></dt><dd><p>Text widget with a vertical scroll bar built in.</p>
</dd>
<dt><a class="reference internal" href="dialog.html#module-tkinter.simpledialog" title="tkinter.simpledialog: Simple dialog windows (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.simpledialog</span></code></a></dt><dd><p>Basic dialogs and convenience functions.</p>
</dd>
<dt><a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a></dt><dd><p>Themed widget set introduced in Tk 8.5, providing modern alternatives
for many of the classic widgets in the main <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> module.</p>
</dd>
</dl>
<p>Additional modules:</p>
<dl class="simple" id="module-_tkinter">
<dt><a class="reference internal" href="#module-_tkinter" title="_tkinter: A binary module that contains the low-level interface to Tcl/Tk."><code class="xref py py-mod docutils literal notranslate"><span class="pre">_tkinter</span></code></a></dt><dd><p>A binary module that contains the low-level interface to Tcl/Tk.
It is automatically imported by the main <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> module,
and should never be used directly by application programmers.
It is usually a shared library (or DLL), but might in some cases be
statically linked with the Python interpreter.</p>
</dd>
<dt><a class="reference internal" href="idle.html#module-idlelib" title="idlelib: Implementation package for the IDLE shell/editor."><code class="xref py py-mod docutils literal notranslate"><span class="pre">idlelib</span></code></a></dt><dd><p>Python’s Integrated Development and Learning Environment (IDLE). Based
on <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>.</p>
</dd>
<dt><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.constants</span></code></dt><dd><p>Symbolic constants that can be used in place of strings when passing
various parameters to Tkinter calls. Automatically imported by the
main <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> module.</p>
</dd>
<dt><a class="reference internal" href="tkinter.dnd.html#module-tkinter.dnd" title="tkinter.dnd: Tkinter drag-and-drop interface (Tk)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.dnd</span></code></a></dt><dd><p>(experimental) Drag-and-drop support for <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>. This will
become deprecated when it is replaced with the Tk DND.</p>
</dd>
<dt><a class="reference internal" href="tkinter.tix.html#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a></dt><dd><p>(deprecated) An older third-party Tcl/Tk package that adds several new
widgets. Better alternatives for most can be found in <a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a>.</p>
</dd>
<dt><a class="reference internal" href="turtle.html#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a></dt><dd><p>Turtle graphics in a Tk window.</p>
</dd>
</dl>
</section>
<section id="tkinter-life-preserver">
<h2>Tkinter Life Preserver<a class="headerlink" href="#tkinter-life-preserver" title="Link to this heading">¶</a></h2>
<p>This section is not designed to be an exhaustive tutorial on either Tk or
Tkinter.  For that, refer to one of the external resources noted earlier.
Instead, this section provides a very quick orientation to what a Tkinter
application looks like, identifies foundational Tk concepts, and
explains how the Tkinter wrapper is structured.</p>
<p>The remainder of this section will help you to identify the classes,
methods, and options you’ll need in your Tkinter application, and where to
find more detailed documentation on them, including in the official Tcl/Tk
reference manual.</p>
<section id="a-hello-world-program">
<h3>A Hello World Program<a class="headerlink" href="#a-hello-world-program" title="Link to this heading">¶</a></h3>
<p>We’ll start by walking through a “Hello World” application in Tkinter. This
isn’t the smallest one we could write, but has enough to illustrate some
key concepts you’ll need to know.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="o">*</span>
<span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">ttk</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">Tk</span><span class="p">()</span>
<span class="n">frm</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Frame</span><span class="p">(</span><span class="n">root</span><span class="p">,</span> <span class="n">padding</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
<span class="n">frm</span><span class="o">.</span><span class="n">grid</span><span class="p">()</span>
<span class="n">ttk</span><span class="o">.</span><span class="n">Label</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">text</span><span class="o">=</span><span class="s2">&quot;Hello World!&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">grid</span><span class="p">(</span><span class="n">column</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">row</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
<span class="n">ttk</span><span class="o">.</span><span class="n">Button</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="n">text</span><span class="o">=</span><span class="s2">&quot;Quit&quot;</span><span class="p">,</span> <span class="n">command</span><span class="o">=</span><span class="n">root</span><span class="o">.</span><span class="n">destroy</span><span class="p">)</span><span class="o">.</span><span class="n">grid</span><span class="p">(</span><span class="n">column</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">row</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
<span class="n">root</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
<p>After the imports, the next line creates an instance of the <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code> class,
which initializes Tk and creates its associated Tcl interpreter. It also
creates a toplevel window, known as the root window, which serves as the main
window of the application.</p>
<p>The following line creates a frame widget, which in this case will contain
a label and a button we’ll create next. The frame is fit inside the root
window.</p>
<p>The next line creates a label widget holding a static text string. The
<code class="xref py py-meth docutils literal notranslate"><span class="pre">grid()</span></code> method is used to specify the relative layout (position) of the
label within its containing frame widget, similar to how tables in HTML work.</p>
<p>A button widget is then created, and placed to the right of the label. When
pressed, it will call the <code class="xref py py-meth docutils literal notranslate"><span class="pre">destroy()</span></code> method of the root window.</p>
<p>Finally, the <code class="xref py py-meth docutils literal notranslate"><span class="pre">mainloop()</span></code> method puts everything on the display, and
responds to user input until the program terminates.</p>
</section>
<section id="important-tk-concepts">
<h3>Important Tk Concepts<a class="headerlink" href="#important-tk-concepts" title="Link to this heading">¶</a></h3>
<p>Even this simple program illustrates the following key Tk concepts:</p>
<dl class="simple">
<dt>widgets</dt><dd><p>A Tkinter user interface is made up of individual <em>widgets</em>. Each widget is
represented as a Python object, instantiated from classes like
<code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Frame</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Label</span></code>, and <code class="xref py py-class docutils literal notranslate"><span class="pre">ttk.Button</span></code>.</p>
</dd>
<dt>widget hierarchy</dt><dd><p>Widgets are arranged in a <em>hierarchy</em>. The label and button were contained
within a frame, which in turn was contained within the root window. When
creating each <em>child</em> widget, its <em>parent</em> widget is passed as the first
argument to the widget constructor.</p>
</dd>
<dt>configuration options</dt><dd><p>Widgets have <em>configuration options</em>, which modify their appearance and
behavior, such as the text to display in a label or button. Different
classes of widgets will have different sets of options.</p>
</dd>
<dt>geometry management</dt><dd><p>Widgets aren’t automatically added to the user interface when they are
created. A <em>geometry manager</em> like <code class="docutils literal notranslate"><span class="pre">grid</span></code> controls where in the
user interface they are placed.</p>
</dd>
<dt>event loop</dt><dd><p>Tkinter reacts to user input, changes from your program, and even refreshes
the display only when actively running an <em>event loop</em>. If your program
isn’t running the event loop, your user interface won’t update.</p>
</dd>
</dl>
</section>
<section id="understanding-how-tkinter-wraps-tcl-tk">
<h3>Understanding How Tkinter Wraps Tcl/Tk<a class="headerlink" href="#understanding-how-tkinter-wraps-tcl-tk" title="Link to this heading">¶</a></h3>
<p>When your application uses Tkinter’s classes and methods, internally Tkinter
is assembling strings representing Tcl/Tk commands, and executing those
commands in the Tcl interpreter attached to your application’s <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code>
instance.</p>
<p>Whether it’s trying to navigate reference documentation, trying to find
the right method or option, adapting some existing code, or debugging your
Tkinter application, there are times that it will be useful to understand
what those underlying Tcl/Tk commands look like.</p>
<p>To illustrate, here is the Tcl/Tk equivalent of the main part of the Tkinter
script above.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">ttk</span><span class="p">::</span><span class="n">frame</span> <span class="o">.</span><span class="n">frm</span> <span class="o">-</span><span class="n">padding</span> <span class="mi">10</span>
<span class="n">grid</span> <span class="o">.</span><span class="n">frm</span>
<span class="n">grid</span> <span class="p">[</span><span class="n">ttk</span><span class="p">::</span><span class="n">label</span> <span class="o">.</span><span class="n">frm</span><span class="o">.</span><span class="n">lbl</span> <span class="o">-</span><span class="n">text</span> <span class="s2">&quot;Hello World!&quot;</span><span class="p">]</span> <span class="o">-</span><span class="n">column</span> <span class="mi">0</span> <span class="o">-</span><span class="n">row</span> <span class="mi">0</span>
<span class="n">grid</span> <span class="p">[</span><span class="n">ttk</span><span class="p">::</span><span class="n">button</span> <span class="o">.</span><span class="n">frm</span><span class="o">.</span><span class="n">btn</span> <span class="o">-</span><span class="n">text</span> <span class="s2">&quot;Quit&quot;</span> <span class="o">-</span><span class="n">command</span> <span class="s2">&quot;destroy .&quot;</span><span class="p">]</span> <span class="o">-</span><span class="n">column</span> <span class="mi">1</span> <span class="o">-</span><span class="n">row</span> <span class="mi">0</span>
</pre></div>
</div>
<p>Tcl’s syntax is similar to many shell languages, where the first word is the
command to be executed, with arguments to that command following it, separated
by spaces. Without getting into too many details, notice the following:</p>
<ul class="simple">
<li><p>The commands used to create widgets (like <code class="docutils literal notranslate"><span class="pre">ttk::frame</span></code>) correspond to
widget classes in Tkinter.</p></li>
<li><p>Tcl widget options (like <code class="docutils literal notranslate"><span class="pre">-text</span></code>) correspond to keyword arguments in
Tkinter.</p></li>
<li><p>Widgets are referred to by a <em>pathname</em> in Tcl (like <code class="docutils literal notranslate"><span class="pre">.frm.btn</span></code>),
whereas Tkinter doesn’t use names but object references.</p></li>
<li><p>A widget’s place in the widget hierarchy is encoded in its (hierarchical)
pathname, which uses a <code class="docutils literal notranslate"><span class="pre">.</span></code> (dot) as a path separator. The pathname for
the root window is just <code class="docutils literal notranslate"><span class="pre">.</span></code> (dot). In Tkinter, the hierarchy is defined
not by pathname but by specifying the parent widget when creating each
child widget.</p></li>
<li><p>Operations which are implemented as separate <em>commands</em> in Tcl (like
<code class="docutils literal notranslate"><span class="pre">grid</span></code> or <code class="docutils literal notranslate"><span class="pre">destroy</span></code>) are represented as <em>methods</em> on Tkinter widget
objects. As you’ll see shortly, at other times Tcl uses what appear to be
method calls on widget objects, which more closely mirror what would is
used in Tkinter.</p></li>
</ul>
</section>
<section id="how-do-i-what-option-does">
<h3>How do I…? What option does…?<a class="headerlink" href="#how-do-i-what-option-does" title="Link to this heading">¶</a></h3>
<p>If you’re not sure how to do something in Tkinter, and you can’t immediately
find it in the tutorial or reference documentation you’re using, there are a
few strategies that can be helpful.</p>
<p>First, remember that the details of how individual widgets work may vary
across different versions of both Tkinter and Tcl/Tk. If you’re searching
documentation, make sure it corresponds to the Python and Tcl/Tk versions
installed on your system.</p>
<p>When searching for how to use an API, it helps to know the exact name of the
class, option, or method that you’re using. Introspection, either in an
interactive Python shell or with <a class="reference internal" href="functions.html#print" title="print"><code class="xref py py-func docutils literal notranslate"><span class="pre">print()</span></code></a>, can help you identify what
you need.</p>
<p>To find out what configuration options are available on any widget, call its
<code class="xref py py-meth docutils literal notranslate"><span class="pre">configure()</span></code> method, which returns a dictionary containing a variety of
information about each object, including its default and current values. Use
<code class="xref py py-meth docutils literal notranslate"><span class="pre">keys()</span></code> to get just the names of each option.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">btn</span> <span class="o">=</span> <span class="n">ttk</span><span class="o">.</span><span class="n">Button</span><span class="p">(</span><span class="n">frm</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">btn</span><span class="o">.</span><span class="n">configure</span><span class="p">()</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>
</pre></div>
</div>
<p>As most widgets have many configuration options in common, it can be useful
to find out which are specific to a particular widget class. Comparing the
list of options to that of a simpler widget, like a frame, is one way to
do that.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">print</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">btn</span><span class="o">.</span><span class="n">configure</span><span class="p">()</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="n">frm</span><span class="o">.</span><span class="n">configure</span><span class="p">()</span><span class="o">.</span><span class="n">keys</span><span class="p">()))</span>
</pre></div>
</div>
<p>Similarly, you can find the available methods for a widget object using the
standard <a class="reference internal" href="functions.html#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a> function. If you try it, you’ll see there are over 200
common widget methods, so again identifying those specific to a widget class
is helpful.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">print</span><span class="p">(</span><span class="nb">dir</span><span class="p">(</span><span class="n">btn</span><span class="p">))</span>
<span class="nb">print</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="nb">dir</span><span class="p">(</span><span class="n">btn</span><span class="p">))</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="nb">dir</span><span class="p">(</span><span class="n">frm</span><span class="p">)))</span>
</pre></div>
</div>
</section>
<section id="navigating-the-tcl-tk-reference-manual">
<h3>Navigating the Tcl/Tk Reference Manual<a class="headerlink" href="#navigating-the-tcl-tk-reference-manual" title="Link to this heading">¶</a></h3>
<p>As noted, the official <a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/contents.htm">Tk commands</a>
reference manual (man pages) is often the most accurate description of what
specific operations on widgets do. Even when you know the name of the option
or method that you need, you may still have a few places to look.</p>
<p>While all operations in Tkinter are implemented as method calls on widget
objects, you’ve seen that many Tcl/Tk operations appear as commands that
take a widget pathname as its first parameter, followed by optional
parameters, e.g.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">destroy</span> <span class="o">.</span>
<span class="n">grid</span> <span class="o">.</span><span class="n">frm</span><span class="o">.</span><span class="n">btn</span> <span class="o">-</span><span class="n">column</span> <span class="mi">0</span> <span class="o">-</span><span class="n">row</span> <span class="mi">0</span>
</pre></div>
</div>
<p>Others, however, look more like methods called on a widget object (in fact,
when you create a widget in Tcl/Tk, it creates a Tcl command with the name
of the widget pathname, with the first parameter to that command being the
name of a method to call).</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="o">.</span><span class="n">frm</span><span class="o">.</span><span class="n">btn</span> <span class="n">invoke</span>
<span class="o">.</span><span class="n">frm</span><span class="o">.</span><span class="n">lbl</span> <span class="n">configure</span> <span class="o">-</span><span class="n">text</span> <span class="s2">&quot;Goodbye&quot;</span>
</pre></div>
</div>
<p>In the official Tcl/Tk reference documentation, you’ll find most operations
that look like method calls on the man page for a specific widget (e.g.,
you’ll find the <code class="xref py py-meth docutils literal notranslate"><span class="pre">invoke()</span></code> method on the
<a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/ttk_button.htm">ttk::button</a>
man page), while functions that take a widget as a parameter often have
their own man page (e.g.,
<a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/grid.htm">grid</a>).</p>
<p>You’ll find many common options and methods in the
<a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/options.htm">options</a> or
<a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/ttk_widget.htm">ttk::widget</a> man
pages, while others are found in the man page for a specific widget class.</p>
<p>You’ll also find that many Tkinter methods have compound names, e.g.,
<code class="xref py py-func docutils literal notranslate"><span class="pre">winfo_x()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">winfo_height()</span></code>, <code class="xref py py-func docutils literal notranslate"><span class="pre">winfo_viewable()</span></code>. You’d find
documentation for all of these in the
<a class="reference external" href="https://www.tcl.tk/man/tcl8.6/TkCmd/winfo.htm">winfo</a> man page.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Somewhat confusingly, there are also methods on all Tkinter widgets
that don’t actually operate on the widget, but operate at a global
scope, independent of any widget. Examples are methods for accessing
the clipboard or the system bell. (They happen to be implemented as
methods in the base <code class="xref py py-class docutils literal notranslate"><span class="pre">Widget</span></code> class that all Tkinter widgets
inherit from).</p>
</div>
</section>
</section>
<section id="threading-model">
<h2>Threading model<a class="headerlink" href="#threading-model" title="Link to this heading">¶</a></h2>
<p>Python and Tcl/Tk have very different threading models, which <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>
tries to bridge. If you use threads, you may need to be aware of this.</p>
<p>A Python interpreter may have many threads associated with it. In Tcl, multiple
threads can be created, but each thread has a separate Tcl interpreter instance
associated with it. Threads can also create more than one interpreter instance,
though each interpreter instance can be used only by the one thread that created it.</p>
<p>Each <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code> object created by <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> contains a Tcl interpreter.
It also keeps track of which thread created that interpreter. Calls to
<a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> can be made from any Python thread. Internally, if a call comes
from a thread other than the one that created the <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code> object, an event
is posted to the interpreter’s event queue, and when executed, the result is
returned to the calling Python thread.</p>
<p>Tcl/Tk applications are normally event-driven, meaning that after initialization,
the interpreter runs an event loop (i.e. <code class="xref py py-func docutils literal notranslate"><span class="pre">Tk.mainloop()</span></code>) and responds to events.
Because it is single-threaded, event handlers must respond quickly, otherwise they
will block other events from being processed. To avoid this, any long-running
computations should not run in an event handler, but are either broken into smaller
pieces using timers, or run in another thread. This is different from many GUI
toolkits where the GUI runs in a completely separate thread from all application
code including event handlers.</p>
<p>If the Tcl interpreter is not running the event loop and processing events, any
<a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> calls made from threads other than the one running the Tcl
interpreter will fail.</p>
<p>A number of special cases exist:</p>
<ul class="simple">
<li><p>Tcl/Tk libraries can be built so they are not thread-aware. In this case,
<a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> calls the library from the originating Python thread, even
if this is different than the thread that created the Tcl interpreter. A global
lock ensures only one call occurs at a time.</p></li>
<li><p>While <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> allows you to create more than one instance of a <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code>
object (with its own interpreter), all interpreters that are part of the same
thread share a common event queue, which gets ugly fast. In practice, don’t create
more than one instance of <code class="xref py py-class docutils literal notranslate"><span class="pre">Tk</span></code> at a time. Otherwise, it’s best to create
them in separate threads and ensure you’re running a thread-aware Tcl/Tk build.</p></li>
<li><p>Blocking event handlers are not the only way to prevent the Tcl interpreter from
reentering the event loop. It is even possible to run multiple nested event loops
or abandon the event loop entirely. If you’re doing anything tricky when it comes
to events or threads, be aware of these possibilities.</p></li>
<li><p>There are a few select <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> functions that presently work only when
called from the thread that created the Tcl interpreter.</p></li>
</ul>
</section>
<section id="handy-reference">
<h2>Handy Reference<a class="headerlink" href="#handy-reference" title="Link to this heading">¶</a></h2>
<section id="setting-options">
<span id="tkinter-setting-options"></span><h3>Setting Options<a class="headerlink" href="#setting-options" title="Link to this heading">¶</a></h3>
<p>Options control things like the color and border width of a widget. Options can
be set in three ways:</p>
<dl>
<dt>At object creation time, using keyword arguments</dt><dd><div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">fred</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">fg</span><span class="o">=</span><span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="n">bg</span><span class="o">=</span><span class="s2">&quot;blue&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
<dt>After object creation, treating the option name like a dictionary index</dt><dd><div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">fred</span><span class="p">[</span><span class="s2">&quot;fg&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;red&quot;</span>
<span class="n">fred</span><span class="p">[</span><span class="s2">&quot;bg&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;blue&quot;</span>
</pre></div>
</div>
</dd>
<dt>Use the config() method to update multiple attrs subsequent to object creation</dt><dd><div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">fred</span><span class="o">.</span><span class="n">config</span><span class="p">(</span><span class="n">fg</span><span class="o">=</span><span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="n">bg</span><span class="o">=</span><span class="s2">&quot;blue&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd>
</dl>
<p>For a complete explanation of a given option and its behavior, see the Tk man
pages for the widget in question.</p>
<p>Note that the man pages list “STANDARD OPTIONS” and “WIDGET SPECIFIC OPTIONS”
for each widget.  The former is a list of options that are common to many
widgets, the latter are the options that are idiosyncratic to that particular
widget.  The Standard Options are documented on the <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/options(3)">options(3)</a></em> man
page.</p>
<p>No distinction between standard and widget-specific options is made in this
document.  Some options don’t apply to some kinds of widgets. Whether a given
widget responds to a particular option depends on the class of the widget;
buttons have a <code class="docutils literal notranslate"><span class="pre">command</span></code> option, labels do not.</p>
<p>The options supported by a given widget are listed in that widget’s man page, or
can be queried at runtime by calling the <code class="xref py py-meth docutils literal notranslate"><span class="pre">config()</span></code> method without
arguments, or by calling the <code class="xref py py-meth docutils literal notranslate"><span class="pre">keys()</span></code> method on that widget.  The return
value of these calls is a dictionary whose key is the name of the option as a
string (for example, <code class="docutils literal notranslate"><span class="pre">'relief'</span></code>) and whose values are 5-tuples.</p>
<p>Some options, like <code class="docutils literal notranslate"><span class="pre">bg</span></code> are synonyms for common options with long names
(<code class="docutils literal notranslate"><span class="pre">bg</span></code> is shorthand for “background”). Passing the <code class="docutils literal notranslate"><span class="pre">config()</span></code> method the name
of a shorthand option will return a 2-tuple, not 5-tuple. The 2-tuple passed
back will contain the name of the synonym and the “real” option (such as
<code class="docutils literal notranslate"><span class="pre">('bg',</span> <span class="pre">'background')</span></code>).</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Example</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>0</p></td>
<td><p>option name</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'relief'</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>1</p></td>
<td><p>option name for database lookup</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'relief'</span></code></p></td>
</tr>
<tr class="row-even"><td><p>2</p></td>
<td><p>option class for database
lookup</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'Relief'</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>3</p></td>
<td><p>default value</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'raised'</span></code></p></td>
</tr>
<tr class="row-even"><td><p>4</p></td>
<td><p>current value</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">'groove'</span></code></p></td>
</tr>
</tbody>
</table>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">fred</span><span class="o">.</span><span class="n">config</span><span class="p">())</span>
<span class="go">{&#39;relief&#39;: (&#39;relief&#39;, &#39;relief&#39;, &#39;Relief&#39;, &#39;raised&#39;, &#39;groove&#39;)}</span>
</pre></div>
</div>
<p>Of course, the dictionary printed will include all the options available and
their values.  This is meant only as an example.</p>
</section>
<section id="the-packer">
<h3>The Packer<a class="headerlink" href="#the-packer" title="Link to this heading">¶</a></h3>
<p id="index-2">The packer is one of Tk’s geometry-management mechanisms.    Geometry managers
are used to specify the relative positioning of widgets within their container -
their mutual <em>master</em>.  In contrast to the more cumbersome <em>placer</em> (which is
used less commonly, and we do not cover here), the packer takes qualitative
relationship specification - <em>above</em>, <em>to the left of</em>, <em>filling</em>, etc - and
works everything out to determine the exact placement coordinates for you.</p>
<p>The size of any <em>master</em> widget is determined by the size of the “slave widgets”
inside.  The packer is used to control where slave widgets appear inside the
master into which they are packed.  You can pack widgets into frames, and frames
into other frames, in order to achieve the kind of layout you desire.
Additionally, the arrangement is dynamically adjusted to accommodate incremental
changes to the configuration, once it is packed.</p>
<p>Note that widgets do not appear until they have had their geometry specified
with a geometry manager.  It’s a common early mistake to leave out the geometry
specification, and then be surprised when the widget is created but nothing
appears.  A widget will appear only after it has had, for example, the packer’s
<code class="xref py py-meth docutils literal notranslate"><span class="pre">pack()</span></code> method applied to it.</p>
<p>The pack() method can be called with keyword-option/value pairs that control
where the widget is to appear within its container, and how it is to behave when
the main application window is resized.  Here are some examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">fred</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>                     <span class="c1"># defaults to side = &quot;top&quot;</span>
<span class="n">fred</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="n">side</span><span class="o">=</span><span class="s2">&quot;left&quot;</span><span class="p">)</span>
<span class="n">fred</span><span class="o">.</span><span class="n">pack</span><span class="p">(</span><span class="n">expand</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="packer-options">
<h3>Packer Options<a class="headerlink" href="#packer-options" title="Link to this heading">¶</a></h3>
<p>For more extensive information on the packer and the options that it can take,
see the man pages and page 183 of John Ousterhout’s book.</p>
<dl class="simple">
<dt>anchor</dt><dd><p>Anchor type.  Denotes where the packer is to place each slave in its parcel.</p>
</dd>
<dt>expand</dt><dd><p>Boolean, <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd>
<dt>fill</dt><dd><p>Legal values: <code class="docutils literal notranslate"><span class="pre">'x'</span></code>, <code class="docutils literal notranslate"><span class="pre">'y'</span></code>, <code class="docutils literal notranslate"><span class="pre">'both'</span></code>, <code class="docutils literal notranslate"><span class="pre">'none'</span></code>.</p>
</dd>
<dt>ipadx and ipady</dt><dd><p>A distance - designating internal padding on each side of the slave widget.</p>
</dd>
<dt>padx and pady</dt><dd><p>A distance - designating external padding on each side of the slave widget.</p>
</dd>
<dt>side</dt><dd><p>Legal values are: <code class="docutils literal notranslate"><span class="pre">'left'</span></code>, <code class="docutils literal notranslate"><span class="pre">'right'</span></code>, <code class="docutils literal notranslate"><span class="pre">'top'</span></code>, <code class="docutils literal notranslate"><span class="pre">'bottom'</span></code>.</p>
</dd>
</dl>
</section>
<section id="coupling-widget-variables">
<h3>Coupling Widget Variables<a class="headerlink" href="#coupling-widget-variables" title="Link to this heading">¶</a></h3>
<p>The current-value setting of some widgets (like text entry widgets) can be
connected directly to application variables by using special options.  These
options are <code class="docutils literal notranslate"><span class="pre">variable</span></code>, <code class="docutils literal notranslate"><span class="pre">textvariable</span></code>, <code class="docutils literal notranslate"><span class="pre">onvalue</span></code>, <code class="docutils literal notranslate"><span class="pre">offvalue</span></code>, and
<code class="docutils literal notranslate"><span class="pre">value</span></code>.  This connection works both ways: if the variable changes for any
reason, the widget it’s connected to will be updated to reflect the new value.</p>
<p>Unfortunately, in the current implementation of <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> it is not
possible to hand over an arbitrary Python variable to a widget through a
<code class="docutils literal notranslate"><span class="pre">variable</span></code> or <code class="docutils literal notranslate"><span class="pre">textvariable</span></code> option.  The only kinds of variables for which
this works are variables that are subclassed from a class called Variable,
defined in <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>.</p>
<p>There are many useful subclasses of Variable already defined:
<code class="xref py py-class docutils literal notranslate"><span class="pre">StringVar</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">IntVar</span></code>, <code class="xref py py-class docutils literal notranslate"><span class="pre">DoubleVar</span></code>, and
<code class="xref py py-class docutils literal notranslate"><span class="pre">BooleanVar</span></code>.  To read the current value of such a variable, call the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">get()</span></code> method on it, and to change its value you call the <code class="xref py py-meth docutils literal notranslate"><span class="pre">set()</span></code>
method.  If you follow this protocol, the widget will always track the value of
the variable, with no further intervention on your part.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tkinter</span> <span class="k">as</span> <span class="nn">tk</span>

<span class="k">class</span> <span class="nc">App</span><span class="p">(</span><span class="n">tk</span><span class="o">.</span><span class="n">Frame</span><span class="p">):</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">master</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">master</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">entrythingy</span> <span class="o">=</span> <span class="n">tk</span><span class="o">.</span><span class="n">Entry</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">entrythingy</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

        <span class="c1"># Create the application variable.</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">contents</span> <span class="o">=</span> <span class="n">tk</span><span class="o">.</span><span class="n">StringVar</span><span class="p">()</span>
        <span class="c1"># Set it to some value.</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">contents</span><span class="o">.</span><span class="n">set</span><span class="p">(</span><span class="s2">&quot;this is a variable&quot;</span><span class="p">)</span>
        <span class="c1"># Tell the entry widget to watch this variable.</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">entrythingy</span><span class="p">[</span><span class="s2">&quot;textvariable&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">contents</span>

        <span class="c1"># Define a callback for when the user hits return.</span>
        <span class="c1"># It prints the current value of the variable.</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">entrythingy</span><span class="o">.</span><span class="n">bind</span><span class="p">(</span><span class="s1">&#39;&lt;Key-Return&gt;&#39;</span><span class="p">,</span>
                             <span class="bp">self</span><span class="o">.</span><span class="n">print_contents</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">print_contents</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">event</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Hi. The current entry content is:&quot;</span><span class="p">,</span>
              <span class="bp">self</span><span class="o">.</span><span class="n">contents</span><span class="o">.</span><span class="n">get</span><span class="p">())</span>

<span class="n">root</span> <span class="o">=</span> <span class="n">tk</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>
<span class="n">myapp</span> <span class="o">=</span> <span class="n">App</span><span class="p">(</span><span class="n">root</span><span class="p">)</span>
<span class="n">myapp</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="the-window-manager">
<h3>The Window Manager<a class="headerlink" href="#the-window-manager" title="Link to this heading">¶</a></h3>
<p id="index-3">In Tk, there is a utility command, <code class="docutils literal notranslate"><span class="pre">wm</span></code>, for interacting with the window
manager.  Options to the <code class="docutils literal notranslate"><span class="pre">wm</span></code> command allow you to control things like titles,
placement, icon bitmaps, and the like.  In <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>, these commands have
been implemented as methods on the <code class="xref py py-class docutils literal notranslate"><span class="pre">Wm</span></code> class.  Toplevel widgets are
subclassed from the <code class="xref py py-class docutils literal notranslate"><span class="pre">Wm</span></code> class, and so can call the <code class="xref py py-class docutils literal notranslate"><span class="pre">Wm</span></code> methods
directly.</p>
<p>To get at the toplevel window that contains a given widget, you can often just
refer to the widget’s master.  Of course if the widget has been packed inside of
a frame, the master won’t represent a toplevel window.  To get at the toplevel
window that contains an arbitrary widget, you can call the <code class="xref py py-meth docutils literal notranslate"><span class="pre">_root()</span></code> method.
This method begins with an underscore to denote the fact that this function is
part of the implementation, and not an interface to Tk functionality.</p>
<p>Here are some examples of typical usage:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tkinter</span> <span class="k">as</span> <span class="nn">tk</span>

<span class="k">class</span> <span class="nc">App</span><span class="p">(</span><span class="n">tk</span><span class="o">.</span><span class="n">Frame</span><span class="p">):</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">master</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">master</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">pack</span><span class="p">()</span>

<span class="c1"># create the application</span>
<span class="n">myapp</span> <span class="o">=</span> <span class="n">App</span><span class="p">()</span>

<span class="c1">#</span>
<span class="c1"># here are method calls to the window manager class</span>
<span class="c1">#</span>
<span class="n">myapp</span><span class="o">.</span><span class="n">master</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="s2">&quot;My Do-Nothing Application&quot;</span><span class="p">)</span>
<span class="n">myapp</span><span class="o">.</span><span class="n">master</span><span class="o">.</span><span class="n">maxsize</span><span class="p">(</span><span class="mi">1000</span><span class="p">,</span> <span class="mi">400</span><span class="p">)</span>

<span class="c1"># start the program</span>
<span class="n">myapp</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="tk-option-data-types">
<h3>Tk Option Data Types<a class="headerlink" href="#tk-option-data-types" title="Link to this heading">¶</a></h3>
<dl id="index-4">
<dt>anchor</dt><dd><p>Legal values are points of the compass: <code class="docutils literal notranslate"><span class="pre">&quot;n&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;ne&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;e&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;se&quot;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&quot;s&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;sw&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;w&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;nw&quot;</span></code>, and also <code class="docutils literal notranslate"><span class="pre">&quot;center&quot;</span></code>.</p>
</dd>
<dt>bitmap</dt><dd><p>There are eight built-in, named bitmaps: <code class="docutils literal notranslate"><span class="pre">'error'</span></code>, <code class="docutils literal notranslate"><span class="pre">'gray25'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'gray50'</span></code>, <code class="docutils literal notranslate"><span class="pre">'hourglass'</span></code>, <code class="docutils literal notranslate"><span class="pre">'info'</span></code>, <code class="docutils literal notranslate"><span class="pre">'questhead'</span></code>, <code class="docutils literal notranslate"><span class="pre">'question'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'warning'</span></code>.  To specify an X bitmap filename, give the full path to the file,
preceded with an <code class="docutils literal notranslate"><span class="pre">&#64;</span></code>, as in <code class="docutils literal notranslate"><span class="pre">&quot;&#64;/usr/contrib/bitmap/gumby.bit&quot;</span></code>.</p>
</dd>
<dt>boolean</dt><dd><p>You can pass integers 0 or 1 or the strings <code class="docutils literal notranslate"><span class="pre">&quot;yes&quot;</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;no&quot;</span></code>.</p>
</dd>
<dt>callback</dt><dd><p>This is any Python function that takes no arguments.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">print_it</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;hi there&quot;</span><span class="p">)</span>
<span class="n">fred</span><span class="p">[</span><span class="s2">&quot;command&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">print_it</span>
</pre></div>
</div>
</dd>
<dt>color</dt><dd><p>Colors can be given as the names of X colors in the rgb.txt file, or as strings
representing RGB values in 4 bit: <code class="docutils literal notranslate"><span class="pre">&quot;#RGB&quot;</span></code>, 8 bit: <code class="docutils literal notranslate"><span class="pre">&quot;#RRGGBB&quot;</span></code>, 12 bit:
<code class="docutils literal notranslate"><span class="pre">&quot;#RRRGGGBBB&quot;</span></code>, or 16 bit: <code class="docutils literal notranslate"><span class="pre">&quot;#RRRRGGGGBBBB&quot;</span></code> ranges, where R,G,B here
represent any legal hex digit.  See page 160 of Ousterhout’s book for details.</p>
</dd>
<dt>cursor</dt><dd><p>The standard X cursor names from <code class="file docutils literal notranslate"><span class="pre">cursorfont.h</span></code> can be used, without the
<code class="docutils literal notranslate"><span class="pre">XC_</span></code> prefix.  For example to get a hand cursor (<code class="xref py py-const docutils literal notranslate"><span class="pre">XC_hand2</span></code>), use the
string <code class="docutils literal notranslate"><span class="pre">&quot;hand2&quot;</span></code>.  You can also specify a bitmap and mask file of your own.
See page 179 of Ousterhout’s book.</p>
</dd>
<dt>distance</dt><dd><p>Screen distances can be specified in either pixels or absolute distances.
Pixels are given as numbers and absolute distances as strings, with the trailing
character denoting units: <code class="docutils literal notranslate"><span class="pre">c</span></code> for centimetres, <code class="docutils literal notranslate"><span class="pre">i</span></code> for inches, <code class="docutils literal notranslate"><span class="pre">m</span></code> for
millimetres, <code class="docutils literal notranslate"><span class="pre">p</span></code> for printer’s points.  For example, 3.5 inches is expressed
as <code class="docutils literal notranslate"><span class="pre">&quot;3.5i&quot;</span></code>.</p>
</dd>
<dt>font</dt><dd><p>Tk uses a list font name format, such as <code class="docutils literal notranslate"><span class="pre">{courier</span> <span class="pre">10</span> <span class="pre">bold}</span></code>. Font sizes with
positive numbers are measured in points; sizes with negative numbers are
measured in pixels.</p>
</dd>
<dt>geometry</dt><dd><p>This is a string of the form <code class="docutils literal notranslate"><span class="pre">widthxheight</span></code>, where width and height are
measured in pixels for most widgets (in characters for widgets displaying text).
For example: <code class="docutils literal notranslate"><span class="pre">fred[&quot;geometry&quot;]</span> <span class="pre">=</span> <span class="pre">&quot;200x100&quot;</span></code>.</p>
</dd>
<dt>justify</dt><dd><p>Legal values are the strings: <code class="docutils literal notranslate"><span class="pre">&quot;left&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;center&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;right&quot;</span></code>, and
<code class="docutils literal notranslate"><span class="pre">&quot;fill&quot;</span></code>.</p>
</dd>
<dt>region</dt><dd><p>This is a string with four space-delimited elements, each of which is a legal
distance (see above).  For example: <code class="docutils literal notranslate"><span class="pre">&quot;2</span> <span class="pre">3</span> <span class="pre">4</span> <span class="pre">5&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;3i</span> <span class="pre">2i</span> <span class="pre">4.5i</span> <span class="pre">2i&quot;</span></code> and
<code class="docutils literal notranslate"><span class="pre">&quot;3c</span> <span class="pre">2c</span> <span class="pre">4c</span> <span class="pre">10.43c&quot;</span></code>  are all legal regions.</p>
</dd>
<dt>relief</dt><dd><p>Determines what the border style of a widget will be.  Legal values are:
<code class="docutils literal notranslate"><span class="pre">&quot;raised&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;sunken&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;flat&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;groove&quot;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&quot;ridge&quot;</span></code>.</p>
</dd>
<dt>scrollcommand</dt><dd><p>This is almost always the <code class="xref py py-meth docutils literal notranslate"><span class="pre">set()</span></code> method of some scrollbar widget, but can
be any widget method that takes a single argument.</p>
</dd>
<dt>wrap</dt><dd><p>Must be one of: <code class="docutils literal notranslate"><span class="pre">&quot;none&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;char&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;word&quot;</span></code>.</p>
</dd>
</dl>
</section>
<section id="bindings-and-events">
<span id="id2"></span><h3>Bindings and Events<a class="headerlink" href="#bindings-and-events" title="Link to this heading">¶</a></h3>
<p id="index-5">The bind method from the widget command allows you to watch for certain events
and to have a callback function trigger when that event type occurs.  The form
of the bind method is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">bind</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">sequence</span><span class="p">,</span> <span class="n">func</span><span class="p">,</span> <span class="n">add</span><span class="o">=</span><span class="s1">&#39;&#39;</span><span class="p">):</span>
</pre></div>
</div>
<p>where:</p>
<dl class="simple">
<dt>sequence</dt><dd><p>is a string that denotes the target kind of event.  (See the
<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/bind(3tk)">bind(3tk)</a></em> man page, and page 201 of John Ousterhout’s book,
<cite>Tcl and the Tk Toolkit (2nd edition)</cite>, for details).</p>
</dd>
<dt>func</dt><dd><p>is a Python function, taking one argument, to be invoked when the event occurs.
An Event instance will be passed as the argument. (Functions deployed this way
are commonly known as <em>callbacks</em>.)</p>
</dd>
<dt>add</dt><dd><p>is optional, either <code class="docutils literal notranslate"><span class="pre">''</span></code> or <code class="docutils literal notranslate"><span class="pre">'+'</span></code>.  Passing an empty string denotes that
this binding is to replace any other bindings that this event is associated
with.  Passing a <code class="docutils literal notranslate"><span class="pre">'+'</span></code> means that this function is to be added to the list
of functions bound to this event type.</p>
</dd>
</dl>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">turn_red</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">event</span><span class="p">):</span>
    <span class="n">event</span><span class="o">.</span><span class="n">widget</span><span class="p">[</span><span class="s2">&quot;activeforeground&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;red&quot;</span>

<span class="bp">self</span><span class="o">.</span><span class="n">button</span><span class="o">.</span><span class="n">bind</span><span class="p">(</span><span class="s2">&quot;&lt;Enter&gt;&quot;</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">turn_red</span><span class="p">)</span>
</pre></div>
</div>
<p>Notice how the widget field of the event is being accessed in the
<code class="docutils literal notranslate"><span class="pre">turn_red()</span></code> callback.  This field contains the widget that caught the X
event.  The following table lists the other event fields you can access, and how
they are denoted in Tk, which can be useful when referring to the Tk man pages.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Tk</p></th>
<th class="head"><p>Tkinter Event Field</p></th>
<th class="head"><p>Tk</p></th>
<th class="head"><p>Tkinter Event Field</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>%f</p></td>
<td><p>focus</p></td>
<td><p>%A</p></td>
<td><p>char</p></td>
</tr>
<tr class="row-odd"><td><p>%h</p></td>
<td><p>height</p></td>
<td><p>%E</p></td>
<td><p>send_event</p></td>
</tr>
<tr class="row-even"><td><p>%k</p></td>
<td><p>keycode</p></td>
<td><p>%K</p></td>
<td><p>keysym</p></td>
</tr>
<tr class="row-odd"><td><p>%s</p></td>
<td><p>state</p></td>
<td><p>%N</p></td>
<td><p>keysym_num</p></td>
</tr>
<tr class="row-even"><td><p>%t</p></td>
<td><p>time</p></td>
<td><p>%T</p></td>
<td><p>type</p></td>
</tr>
<tr class="row-odd"><td><p>%w</p></td>
<td><p>width</p></td>
<td><p>%W</p></td>
<td><p>widget</p></td>
</tr>
<tr class="row-even"><td><p>%x</p></td>
<td><p>x</p></td>
<td><p>%X</p></td>
<td><p>x_root</p></td>
</tr>
<tr class="row-odd"><td><p>%y</p></td>
<td><p>y</p></td>
<td><p>%Y</p></td>
<td><p>y_root</p></td>
</tr>
</tbody>
</table>
</section>
<section id="the-index-parameter">
<h3>The index Parameter<a class="headerlink" href="#the-index-parameter" title="Link to this heading">¶</a></h3>
<p>A number of widgets require “index” parameters to be passed.  These are used to
point at a specific place in a Text widget, or to particular characters in an
Entry widget, or to particular menu items in a Menu widget.</p>
<dl class="simple">
<dt>Entry widget indexes (index, view index, etc.)</dt><dd><p>Entry widgets have options that refer to character positions in the text being
displayed.  You can use these <a class="reference internal" href="#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> functions to access these special
points in text widgets:</p>
</dd>
<dt>Text widget indexes</dt><dd><p>The index notation for Text widgets is very rich and is best described in the Tk
man pages.</p>
</dd>
<dt>Menu indexes (menu.invoke(), menu.entryconfig(), etc.)</dt><dd><p>Some options and methods for menus manipulate specific menu entries. Anytime a
menu index is needed for an option or a parameter, you may pass in:</p>
<ul class="simple">
<li><p>an integer which refers to the numeric position of the entry in the widget,
counted from the top, starting with 0;</p></li>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">&quot;active&quot;</span></code>, which refers to the menu position that is currently
under the cursor;</p></li>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">&quot;last&quot;</span></code> which refers to the last menu item;</p></li>
<li><p>An integer preceded by <code class="docutils literal notranslate"><span class="pre">&#64;</span></code>, as in <code class="docutils literal notranslate"><span class="pre">&#64;6</span></code>, where the integer is interpreted
as a y pixel coordinate in the menu’s coordinate system;</p></li>
<li><p>the string <code class="docutils literal notranslate"><span class="pre">&quot;none&quot;</span></code>, which indicates no menu entry at all, most often used
with menu.activate() to deactivate all entries, and finally,</p></li>
<li><p>a text string that is pattern matched against the label of the menu entry, as
scanned from the top of the menu to the bottom.  Note that this index type is
considered after all the others, which means that matches for menu items
labelled <code class="docutils literal notranslate"><span class="pre">last</span></code>, <code class="docutils literal notranslate"><span class="pre">active</span></code>, or <code class="docutils literal notranslate"><span class="pre">none</span></code> may be interpreted as the above
literals, instead.</p></li>
</ul>
</dd>
</dl>
</section>
<section id="images">
<h3>Images<a class="headerlink" href="#images" title="Link to this heading">¶</a></h3>
<p>Images of different formats can be created through the corresponding subclass
of <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Image</span></code>:</p>
<ul class="simple">
<li><p><code class="xref py py-class docutils literal notranslate"><span class="pre">BitmapImage</span></code> for images in XBM format.</p></li>
<li><p><code class="xref py py-class docutils literal notranslate"><span class="pre">PhotoImage</span></code> for images in PGM, PPM, GIF and PNG formats. The latter
is supported starting with Tk 8.6.</p></li>
</ul>
<p>Either type of image is created through either the <code class="docutils literal notranslate"><span class="pre">file</span></code> or the <code class="docutils literal notranslate"><span class="pre">data</span></code>
option (other options are available as well).</p>
<p>The image object can then be used wherever an <code class="docutils literal notranslate"><span class="pre">image</span></code> option is supported by
some widget (e.g. labels, buttons, menus). In these cases, Tk will not keep a
reference to the image. When the last Python reference to the image object is
deleted, the image data is deleted as well, and Tk will display an empty box
wherever the image was used.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference external" href="https://python-pillow.org/">Pillow</a> package adds support for
formats such as BMP, JPEG, TIFF, and WebP, among others.</p>
</div>
</section>
</section>
<section id="file-handlers">
<span id="tkinter-file-handlers"></span><h2>File Handlers<a class="headerlink" href="#file-handlers" title="Link to this heading">¶</a></h2>
<p>Tk allows you to register and unregister a callback function which will be
called from the Tk mainloop when I/O is possible on a file descriptor.
Only one handler may be registered per file descriptor. Example code:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">tkinter</span>
<span class="n">widget</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>
<span class="n">mask</span> <span class="o">=</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">READABLE</span> <span class="o">|</span> <span class="n">tkinter</span><span class="o">.</span><span class="n">WRITABLE</span>
<span class="n">widget</span><span class="o">.</span><span class="n">tk</span><span class="o">.</span><span class="n">createfilehandler</span><span class="p">(</span><span class="n">file</span><span class="p">,</span> <span class="n">mask</span><span class="p">,</span> <span class="n">callback</span><span class="p">)</span>
<span class="o">...</span>
<span class="n">widget</span><span class="o">.</span><span class="n">tk</span><span class="o">.</span><span class="n">deletefilehandler</span><span class="p">(</span><span class="n">file</span><span class="p">)</span>
</pre></div>
</div>
<p>This feature is not available on Windows.</p>
<p>Since you don’t know how many bytes are available for reading, you may not
want to use the <a class="reference internal" href="io.html#io.BufferedIOBase" title="io.BufferedIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">BufferedIOBase</span></code></a> or <a class="reference internal" href="io.html#io.TextIOBase" title="io.TextIOBase"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextIOBase</span></code></a>
<a class="reference internal" href="io.html#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> or <a class="reference internal" href="io.html#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a> methods,
since these will insist on reading a predefined number of bytes.
For sockets, the <a class="reference internal" href="socket.html#socket.socket.recv" title="socket.socket.recv"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recv()</span></code></a> or
<a class="reference internal" href="socket.html#socket.socket.recvfrom" title="socket.socket.recvfrom"><code class="xref py py-meth docutils literal notranslate"><span class="pre">recvfrom()</span></code></a> methods will work fine; for other files,
use raw reads or <code class="docutils literal notranslate"><span class="pre">os.read(file.fileno(),</span> <span class="pre">maxbytecount)</span></code>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="tkinter.Widget.tk.createfilehandler">
<span class="sig-prename descclassname"><span class="pre">Widget.tk.</span></span><span class="sig-name descname"><span class="pre">createfilehandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mask</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">func</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.Widget.tk.createfilehandler" title="Link to this definition">¶</a></dt>
<dd><p>Registers the file handler callback function <em>func</em>. The <em>file</em> argument
may either be an object with a <a class="reference internal" href="io.html#io.IOBase.fileno" title="io.IOBase.fileno"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fileno()</span></code></a> method (such as
a file or socket object), or an integer file descriptor. The <em>mask</em>
argument is an ORed combination of any of the three constants below.
The callback is called as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">callback</span><span class="p">(</span><span class="n">file</span><span class="p">,</span> <span class="n">mask</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.Widget.tk.deletefilehandler">
<span class="sig-prename descclassname"><span class="pre">Widget.tk.</span></span><span class="sig-name descname"><span class="pre">deletefilehandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.Widget.tk.deletefilehandler" title="Link to this definition">¶</a></dt>
<dd><p>Unregisters a file handler.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="tkinter.READABLE">
<span class="sig-prename descclassname"><span class="pre">_tkinter.</span></span><span class="sig-name descname"><span class="pre">READABLE</span></span><a class="headerlink" href="#tkinter.READABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tkinter.WRITABLE">
<span class="sig-prename descclassname"><span class="pre">_tkinter.</span></span><span class="sig-name descname"><span class="pre">WRITABLE</span></span><a class="headerlink" href="#tkinter.WRITABLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="tkinter.EXCEPTION">
<span class="sig-prename descclassname"><span class="pre">_tkinter.</span></span><span class="sig-name descname"><span class="pre">EXCEPTION</span></span><a class="headerlink" href="#tkinter.EXCEPTION" title="Link to this definition">¶</a></dt>
<dd><p>Constants used in the <em>mask</em> arguments.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a><ul>
<li><a class="reference internal" href="#architecture">Architecture</a></li>
<li><a class="reference internal" href="#tkinter-modules">Tkinter Modules</a></li>
<li><a class="reference internal" href="#tkinter-life-preserver">Tkinter Life Preserver</a><ul>
<li><a class="reference internal" href="#a-hello-world-program">A Hello World Program</a></li>
<li><a class="reference internal" href="#important-tk-concepts">Important Tk Concepts</a></li>
<li><a class="reference internal" href="#understanding-how-tkinter-wraps-tcl-tk">Understanding How Tkinter Wraps Tcl/Tk</a></li>
<li><a class="reference internal" href="#how-do-i-what-option-does">How do I…? What option does…?</a></li>
<li><a class="reference internal" href="#navigating-the-tcl-tk-reference-manual">Navigating the Tcl/Tk Reference Manual</a></li>
</ul>
</li>
<li><a class="reference internal" href="#threading-model">Threading model</a></li>
<li><a class="reference internal" href="#handy-reference">Handy Reference</a><ul>
<li><a class="reference internal" href="#setting-options">Setting Options</a></li>
<li><a class="reference internal" href="#the-packer">The Packer</a></li>
<li><a class="reference internal" href="#packer-options">Packer Options</a></li>
<li><a class="reference internal" href="#coupling-widget-variables">Coupling Widget Variables</a></li>
<li><a class="reference internal" href="#the-window-manager">The Window Manager</a></li>
<li><a class="reference internal" href="#tk-option-data-types">Tk Option Data Types</a></li>
<li><a class="reference internal" href="#bindings-and-events">Bindings and Events</a></li>
<li><a class="reference internal" href="#the-index-parameter">The index Parameter</a></li>
<li><a class="reference internal" href="#images">Images</a></li>
</ul>
</li>
<li><a class="reference internal" href="#file-handlers">File Handlers</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tk.html"
                          title="previous chapter">Graphical User Interfaces with Tk</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tkinter.colorchooser.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.colorchooser</span></code> — Color choosing dialog</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tkinter.colorchooser.html" title="tkinter.colorchooser — Color choosing dialog"
             >next</a> |</li>
        <li class="right" >
          <a href="tk.html" title="Graphical User Interfaces with Tk"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code> — Python interface to Tcl/Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>