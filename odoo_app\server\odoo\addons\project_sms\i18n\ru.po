# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_sms
# 
# Translators:
# Сергей Ше<PERSON>анин <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>il O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_project_stage__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the project reaches this stage."
msgstr ""
"Если установлено, то при достижении этой стадии проекта клиенту будет "
"автоматически отправлено текстовое SMS-сообщение."

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_task_type__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the task reaches this stage."
msgstr ""
"Если установлено, SMS-сообщение будет автоматически отправлено клиенту, "
"когда задача достигнет этой стадии."

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project
msgid "Project"
msgstr "Проект"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project_stage
msgid "Project Stage"
msgstr "Стадия проекта"

#. module: project_sms
#: model:ir.model.fields,field_description:project_sms.field_project_project_stage__sms_template_id
#: model:ir.model.fields,field_description:project_sms.field_project_task_type__sms_template_id
msgid "SMS Template"
msgstr "SMS шаблон"

#. module: project_sms
#: model:ir.actions.act_window,name:project_sms.project_project_act_window_sms_composer
#: model:ir.actions.act_window,name:project_sms.project_task_act_window_sms_composer
msgid "Send SMS Text Message"
msgstr "Отправить текстовое сообщение SMS"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task
msgid "Task"
msgstr "Задача"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task_type
msgid "Task Stage"
msgstr "Этап задачи"
