<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pathlib — Object-oriented filesystem paths" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pathlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pathlib.py This module offers classes representing filesystem paths with semantics appropriate for different operating systems. Path classes are divided between pure paths, which p..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pathlib.py This module offers classes representing filesystem paths with semantics appropriate for different operating systems. Path classes are divided between pure paths, which p..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pathlib — Object-oriented filesystem paths &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="os.path — Common pathname manipulations" href="os.path.html" />
    <link rel="prev" title="File and Directory Access" href="filesys.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pathlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a><ul>
<li><a class="reference internal" href="#basic-use">Basic use</a></li>
<li><a class="reference internal" href="#pure-paths">Pure paths</a><ul>
<li><a class="reference internal" href="#general-properties">General properties</a></li>
<li><a class="reference internal" href="#operators">Operators</a></li>
<li><a class="reference internal" href="#accessing-individual-parts">Accessing individual parts</a></li>
<li><a class="reference internal" href="#methods-and-properties">Methods and properties</a></li>
</ul>
</li>
<li><a class="reference internal" href="#concrete-paths">Concrete paths</a><ul>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#correspondence-to-tools-in-the-os-module">Correspondence to tools in the <code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> module</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="filesys.html"
                          title="previous chapter">File and Directory Access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="os.path.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pathlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="os.path.html" title="os.path — Common pathname manipulations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="filesys.html" title="File and Directory Access"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" accesskey="U">File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pathlib">
<span id="pathlib-object-oriented-filesystem-paths"></span><h1><a class="reference internal" href="#module-pathlib" title="pathlib: Object-oriented filesystem paths"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code></a> — Object-oriented filesystem paths<a class="headerlink" href="#module-pathlib" title="Link to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pathlib.py">Lib/pathlib.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module offers classes representing filesystem paths with semantics
appropriate for different operating systems.  Path classes are divided
between <a class="reference internal" href="#pure-paths"><span class="std std-ref">pure paths</span></a>, which provide purely computational
operations without I/O, and <a class="reference internal" href="#concrete-paths"><span class="std std-ref">concrete paths</span></a>, which
inherit from pure paths but also provide I/O operations.</p>
<img alt="../_images/pathlib-inheritance.png" class="invert-in-dark-mode align-center" src="../_images/pathlib-inheritance.png" />
<p>If you’ve never used this module before or just aren’t sure which class is
right for your task, <a class="reference internal" href="#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> is most likely what you need. It instantiates
a <a class="reference internal" href="#concrete-paths"><span class="std std-ref">concrete path</span></a> for the platform the code is running on.</p>
<p>Pure paths are useful in some special cases; for example:</p>
<ol class="arabic simple">
<li><p>If you want to manipulate Windows paths on a Unix machine (or vice versa).
You cannot instantiate a <a class="reference internal" href="#pathlib.WindowsPath" title="pathlib.WindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">WindowsPath</span></code></a> when running on Unix, but you
can instantiate <a class="reference internal" href="#pathlib.PureWindowsPath" title="pathlib.PureWindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PureWindowsPath</span></code></a>.</p></li>
<li><p>You want to make sure that your code only manipulates paths without actually
accessing the OS. In this case, instantiating one of the pure classes may be
useful since those simply don’t have any OS-accessing operations.</p></li>
</ol>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0428/"><strong>PEP 428</strong></a>: The pathlib module – object-oriented filesystem paths.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>For low-level path manipulation on strings, you can also use the
<a class="reference internal" href="os.path.html#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a> module.</p>
</div>
<section id="basic-use">
<h2>Basic use<a class="headerlink" href="#basic-use" title="Link to this heading">¶</a></h2>
<p>Importing the main class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
</pre></div>
</div>
<p>Listing subdirectories:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">p</span><span class="o">.</span><span class="n">iterdir</span><span class="p">()</span> <span class="k">if</span> <span class="n">x</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()]</span>
<span class="go">[PosixPath(&#39;.hg&#39;), PosixPath(&#39;docs&#39;), PosixPath(&#39;dist&#39;),</span>
<span class="go"> PosixPath(&#39;__pycache__&#39;), PosixPath(&#39;build&#39;)]</span>
</pre></div>
</div>
<p>Listing Python source files in this directory tree:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s1">&#39;**/*.py&#39;</span><span class="p">))</span>
<span class="go">[PosixPath(&#39;test_pathlib.py&#39;), PosixPath(&#39;setup.py&#39;),</span>
<span class="go"> PosixPath(&#39;pathlib.py&#39;), PosixPath(&#39;docs/conf.py&#39;),</span>
<span class="go"> PosixPath(&#39;build/lib/pathlib.py&#39;)]</span>
</pre></div>
</div>
<p>Navigating inside a directory tree:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span> <span class="o">=</span> <span class="n">p</span> <span class="o">/</span> <span class="s1">&#39;init.d&#39;</span> <span class="o">/</span> <span class="s1">&#39;reboot&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span>
<span class="go">PosixPath(&#39;/etc/init.d/reboot&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/etc/rc.d/init.d/halt&#39;)</span>
</pre></div>
</div>
<p>Querying path properties:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">q</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span><span class="o">.</span><span class="n">is_dir</span><span class="p">()</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Opening a file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">q</span><span class="o">.</span><span class="n">open</span><span class="p">()</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span> <span class="n">f</span><span class="o">.</span><span class="n">readline</span><span class="p">()</span>
<span class="gp">...</span>
<span class="go">&#39;#!/bin/bash\n&#39;</span>
</pre></div>
</div>
</section>
<section id="pure-paths">
<span id="id1"></span><h2>Pure paths<a class="headerlink" href="#pure-paths" title="Link to this heading">¶</a></h2>
<p>Pure path objects provide path-handling operations which don’t actually
access a filesystem.  There are three ways to access these classes, which
we also call <em>flavours</em>:</p>
<dl class="py class">
<dt class="sig sig-object py" id="pathlib.PurePath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">PurePath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath" title="Link to this definition">¶</a></dt>
<dd><p>A generic class that represents the system’s path flavour (instantiating
it creates either a <a class="reference internal" href="#pathlib.PurePosixPath" title="pathlib.PurePosixPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePosixPath</span></code></a> or a <a class="reference internal" href="#pathlib.PureWindowsPath" title="pathlib.PureWindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PureWindowsPath</span></code></a>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>      <span class="c1"># Running on a Unix machine</span>
<span class="go">PurePosixPath(&#39;setup.py&#39;)</span>
</pre></div>
</div>
<p>Each element of <em>pathsegments</em> can be either a string representing a
path segment, or an object implementing the <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> interface
where the <a class="reference internal" href="os.html#os.PathLike.__fspath__" title="os.PathLike.__fspath__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__fspath__()</span></code></a> method returns a string,
such as another path object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">,</span> <span class="s1">&#39;some/path&#39;</span><span class="p">,</span> <span class="s1">&#39;bar&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;foo/some/path/bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">),</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;bar&#39;</span><span class="p">))</span>
<span class="go">PurePosixPath(&#39;foo/bar&#39;)</span>
</pre></div>
</div>
<p>When <em>pathsegments</em> is empty, the current directory is assumed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">()</span>
<span class="go">PurePosixPath(&#39;.&#39;)</span>
</pre></div>
</div>
<p>If a segment is an absolute path, all previous segments are ignored
(like <a class="reference internal" href="os.path.html#os.path.join" title="os.path.join"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.join()</span></code></a>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">,</span> <span class="s1">&#39;/usr&#39;</span><span class="p">,</span> <span class="s1">&#39;lib64&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;/usr/lib64&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Windows&#39;</span><span class="p">,</span> <span class="s1">&#39;d:bar&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;d:bar&#39;)</span>
</pre></div>
</div>
<p>On Windows, the drive is not reset when a rooted relative path
segment (e.g., <code class="docutils literal notranslate"><span class="pre">r'\foo'</span></code>) is encountered:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Windows&#39;</span><span class="p">,</span> <span class="s1">&#39;/Program Files&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Program Files&#39;)</span>
</pre></div>
</div>
<p>Spurious slashes and single dots are collapsed, but double dots (<code class="docutils literal notranslate"><span class="pre">'..'</span></code>)
and leading double slashes (<code class="docutils literal notranslate"><span class="pre">'//'</span></code>) are not, since this would change the
meaning of a path for various reasons (e.g. symbolic links, UNC paths):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;foo//bar&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;foo/bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;//foo/bar&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;//foo/bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;foo/./bar&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;foo/bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;foo/../bar&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;foo/../bar&#39;)</span>
</pre></div>
</div>
<p>(a naïve approach would make <code class="docutils literal notranslate"><span class="pre">PurePosixPath('foo/../bar')</span></code> equivalent
to <code class="docutils literal notranslate"><span class="pre">PurePosixPath('bar')</span></code>, which is wrong if <code class="docutils literal notranslate"><span class="pre">foo</span></code> is a symbolic link
to another directory)</p>
<p>Pure path objects implement the <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> interface, allowing them
to be used anywhere the interface is accepted.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Added support for the <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> interface.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="pathlib.PurePosixPath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">PurePosixPath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePosixPath" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>, this path flavour represents non-Windows
filesystem paths:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;/etc&#39;)</span>
</pre></div>
</div>
<p><em>pathsegments</em> is specified similarly to <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="pathlib.PureWindowsPath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">PureWindowsPath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PureWindowsPath" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>, this path flavour represents Windows
filesystem paths, including <a class="reference external" href="https://en.wikipedia.org/wiki/Path_(computing)#UNC">UNC paths</a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Program Files&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//server/share/file&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;//server/share/file&#39;)</span>
</pre></div>
</div>
<p><em>pathsegments</em> is specified similarly to <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>.</p>
</dd></dl>

<p>Regardless of the system you’re running on, you can instantiate all of
these classes, since they don’t provide any operation that does system calls.</p>
<section id="general-properties">
<h3>General properties<a class="headerlink" href="#general-properties" title="Link to this heading">¶</a></h3>
<p>Paths are immutable and <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>.  Paths of a same flavour are comparable
and orderable.  These properties respect the flavour’s case-folding
semantics:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;FOO&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;FOO&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;FOO&#39;</span><span class="p">)</span> <span class="ow">in</span> <span class="p">{</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="p">}</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;C:&#39;</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;d:&#39;</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Paths of a different flavour compare unequal and cannot be ordered:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">==</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">TypeError</span>: <span class="n">&#39;&lt;&#39; not supported between instances of &#39;PureWindowsPath&#39; and &#39;PurePosixPath&#39;</span>
</pre></div>
</div>
</section>
<section id="operators">
<h3>Operators<a class="headerlink" href="#operators" title="Link to this heading">¶</a></h3>
<p>The slash operator helps create child paths, like <a class="reference internal" href="os.path.html#os.path.join" title="os.path.join"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.join()</span></code></a>.
If the argument is an absolute path, the previous path is ignored.
On Windows, the drive is not reset when the argument is a rooted
relative path (e.g., <code class="docutils literal notranslate"><span class="pre">r'\foo'</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span>
<span class="go">PurePosixPath(&#39;/etc&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">/</span> <span class="s1">&#39;init.d&#39;</span> <span class="o">/</span> <span class="s1">&#39;apache2&#39;</span>
<span class="go">PurePosixPath(&#39;/etc/init.d/apache2&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;bin&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">&#39;/usr&#39;</span> <span class="o">/</span> <span class="n">q</span>
<span class="go">PurePosixPath(&#39;/usr/bin&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">/</span> <span class="s1">&#39;/an_absolute_path&#39;</span>
<span class="go">PurePosixPath(&#39;/an_absolute_path&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Windows&#39;</span><span class="p">,</span> <span class="s1">&#39;/Program Files&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Program Files&#39;)</span>
</pre></div>
</div>
<p>A path object can be used anywhere an object implementing <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a>
is accepted:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">fspath</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="go">&#39;/etc&#39;</span>
</pre></div>
</div>
<p>The string representation of a path is the raw filesystem path itself
(in native form, e.g. with backslashes under Windows), which you can
pass to any function taking a file path as a string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="go">&#39;/etc&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="go">&#39;c:\\Program Files&#39;</span>
</pre></div>
</div>
<p>Similarly, calling <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> on a path gives the raw filesystem path as a
bytes object, as encoded by <a class="reference internal" href="os.html#os.fsencode" title="os.fsencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fsencode()</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">bytes</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="go">b&#39;/etc&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> is only recommended under Unix.  Under Windows,
the unicode form is the canonical representation of filesystem paths.</p>
</div>
</section>
<section id="accessing-individual-parts">
<h3>Accessing individual parts<a class="headerlink" href="#accessing-individual-parts" title="Link to this heading">¶</a></h3>
<p>To access the individual “parts” (components) of a path, use the following
property:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.parts">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">parts</span></span><a class="headerlink" href="#pathlib.PurePath.parts" title="Link to this definition">¶</a></dt>
<dd><p>A tuple giving access to the path’s various components:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/usr/bin/python3&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parts</span>
<span class="go">(&#39;/&#39;, &#39;usr&#39;, &#39;bin&#39;, &#39;python3&#39;)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/PSF&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parts</span>
<span class="go">(&#39;c:\\&#39;, &#39;Program Files&#39;, &#39;PSF&#39;)</span>
</pre></div>
</div>
<p>(note how the drive and local root are regrouped in a single part)</p>
</dd></dl>

</section>
<section id="methods-and-properties">
<h3>Methods and properties<a class="headerlink" href="#methods-and-properties" title="Link to this heading">¶</a></h3>
<p>Pure paths provide the following methods and properties:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.drive">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">drive</span></span><a class="headerlink" href="#pathlib.PurePath.drive" title="Link to this definition">¶</a></dt>
<dd><p>A string representing the drive letter or name, if any:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">drive</span>
<span class="go">&#39;c:&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;/Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">drive</span>
<span class="go">&#39;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">drive</span>
<span class="go">&#39;&#39;</span>
</pre></div>
</div>
<p>UNC shares are also considered drives:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//host/share/foo.txt&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">drive</span>
<span class="go">&#39;\\\\host\\share&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.root">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">root</span></span><a class="headerlink" href="#pathlib.PurePath.root" title="Link to this definition">¶</a></dt>
<dd><p>A string representing the (local or global) root, if any:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;\\&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;/&#39;</span>
</pre></div>
</div>
<p>UNC shares always have a root:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//host/share&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;\\&#39;</span>
</pre></div>
</div>
<p>If the path starts with more than two successive slashes,
<a class="reference internal" href="#pathlib.PurePosixPath" title="pathlib.PurePosixPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePosixPath</span></code></a> collapses them:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;//etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;//&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;///etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;/&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;////etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">root</span>
<span class="go">&#39;/&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This behavior conforms to <em>The Open Group Base Specifications Issue 6</em>,
paragraph <a class="reference external" href="https://pubs.opengroup.org/onlinepubs/009695399/basedefs/xbd_chap04.html#tag_04_11">4.11 Pathname Resolution</a>:</p>
<p><em>“A pathname that begins with two successive slashes may be interpreted in
an implementation-defined manner, although more than two leading slashes
shall be treated as a single slash.”</em></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.anchor">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">anchor</span></span><a class="headerlink" href="#pathlib.PurePath.anchor" title="Link to this definition">¶</a></dt>
<dd><p>The concatenation of the drive and root:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">anchor</span>
<span class="go">&#39;c:\\&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:Program Files/&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">anchor</span>
<span class="go">&#39;c:&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">anchor</span>
<span class="go">&#39;/&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//host/share&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">anchor</span>
<span class="go">&#39;\\\\host\\share\\&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.parents">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">parents</span></span><a class="headerlink" href="#pathlib.PurePath.parents" title="Link to this definition">¶</a></dt>
<dd><p>An immutable sequence providing access to the logical ancestors of
the path:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/foo/bar/setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parents</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">PureWindowsPath(&#39;c:/foo/bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parents</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
<span class="go">PureWindowsPath(&#39;c:/foo&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parents</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
<span class="go">PureWindowsPath(&#39;c:/&#39;)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The parents sequence now supports <a class="reference internal" href="../glossary.html#term-slice"><span class="xref std std-term">slices</span></a> and negative index values.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.parent">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#pathlib.PurePath.parent" title="Link to this definition">¶</a></dt>
<dd><p>The logical parent of the path:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/a/b/c/d&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parent</span>
<span class="go">PurePosixPath(&#39;/a/b/c&#39;)</span>
</pre></div>
</div>
<p>You cannot go past an anchor, or empty path:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parent</span>
<span class="go">PurePosixPath(&#39;/&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parent</span>
<span class="go">PurePosixPath(&#39;.&#39;)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This is a purely lexical operation, hence the following behaviour:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;foo/..&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">parent</span>
<span class="go">PurePosixPath(&#39;foo&#39;)</span>
</pre></div>
</div>
<p>If you want to walk an arbitrary filesystem path upwards, it is
recommended to first call <a class="reference internal" href="#pathlib.Path.resolve" title="pathlib.Path.resolve"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.resolve()</span></code></a> so as to resolve
symlinks and eliminate <code class="docutils literal notranslate"><span class="pre">&quot;..&quot;</span></code> components.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.name">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#pathlib.PurePath.name" title="Link to this definition">¶</a></dt>
<dd><p>A string representing the final path component, excluding the drive and
root, if any:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library/setup.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;setup.py&#39;</span>
</pre></div>
</div>
<p>UNC drive names are not considered:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//some/share/setup.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;setup.py&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//some/share&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.suffix">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">suffix</span></span><a class="headerlink" href="#pathlib.PurePath.suffix" title="Link to this definition">¶</a></dt>
<dd><p>The file extension of the final component, if any:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library/setup.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffix</span>
<span class="go">&#39;.py&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library.tar.gz&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffix</span>
<span class="go">&#39;.gz&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffix</span>
<span class="go">&#39;&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.suffixes">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">suffixes</span></span><a class="headerlink" href="#pathlib.PurePath.suffixes" title="Link to this definition">¶</a></dt>
<dd><p>A list of the path’s file extensions:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library.tar.gar&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffixes</span>
<span class="go">[&#39;.tar&#39;, &#39;.gar&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library.tar.gz&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffixes</span>
<span class="go">[&#39;.tar&#39;, &#39;.gz&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">suffixes</span>
<span class="go">[]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pathlib.PurePath.stem">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">stem</span></span><a class="headerlink" href="#pathlib.PurePath.stem" title="Link to this definition">¶</a></dt>
<dd><p>The final path component, without its suffix:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library.tar.gz&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">stem</span>
<span class="go">&#39;library.tar&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library.tar&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">stem</span>
<span class="go">&#39;library&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;my/library&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">stem</span>
<span class="go">&#39;library&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.as_posix">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">as_posix</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.as_posix" title="Link to this definition">¶</a></dt>
<dd><p>Return a string representation of the path with forward slashes (<code class="docutils literal notranslate"><span class="pre">/</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:</span><span class="se">\\</span><span class="s1">windows&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">p</span><span class="p">)</span>
<span class="go">&#39;c:\\windows&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">as_posix</span><span class="p">()</span>
<span class="go">&#39;c:/windows&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.as_uri">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">as_uri</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.as_uri" title="Link to this definition">¶</a></dt>
<dd><p>Represent the path as a <code class="docutils literal notranslate"><span class="pre">file</span></code> URI.  <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised if
the path isn’t absolute.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc/passwd&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">as_uri</span><span class="p">()</span>
<span class="go">&#39;file:///etc/passwd&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Windows&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">as_uri</span><span class="p">()</span>
<span class="go">&#39;file:///c:/Windows&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.is_absolute">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">is_absolute</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.is_absolute" title="Link to this definition">¶</a></dt>
<dd><p>Return whether the path is absolute or not.  A path is considered absolute
if it has both a root and (if the flavour allows) a drive:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/a/b&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;a/b&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">False</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/a/b&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;/a/b&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;//some/share&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_absolute</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.is_relative_to">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">is_relative_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.is_relative_to" title="Link to this definition">¶</a></dt>
<dd><p>Return whether or not this path is relative to the <em>other</em> path.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/etc/passwd&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">is_relative_to</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">is_relative_to</span><span class="p">(</span><span class="s1">&#39;/usr&#39;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>This method is string-based; it neither accesses the filesystem nor treats
“<code class="docutils literal notranslate"><span class="pre">..</span></code>” segments specially. The following code is equivalent:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">u</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/usr&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">u</span> <span class="o">==</span> <span class="n">p</span> <span class="ow">or</span> <span class="n">u</span> <span class="ow">in</span> <span class="n">p</span><span class="o">.</span><span class="n">parents</span>
<span class="go">False</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Passing additional arguments is deprecated; if supplied, they are joined
with <em>other</em>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.is_reserved">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">is_reserved</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.is_reserved" title="Link to this definition">¶</a></dt>
<dd><p>With <a class="reference internal" href="#pathlib.PureWindowsPath" title="pathlib.PureWindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PureWindowsPath</span></code></a>, return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path is considered
reserved under Windows, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.  With <a class="reference internal" href="#pathlib.PurePosixPath" title="pathlib.PurePosixPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePosixPath</span></code></a>,
<code class="docutils literal notranslate"><span class="pre">False</span></code> is always returned.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;nul&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_reserved</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;nul&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">is_reserved</span><span class="p">()</span>
<span class="go">False</span>
</pre></div>
</div>
<p>File system calls on reserved paths can fail mysteriously or have
unintended effects.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.joinpath">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">joinpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.joinpath" title="Link to this definition">¶</a></dt>
<dd><p>Calling this method is equivalent to combining the path with each of
the given <em>pathsegments</em> in turn:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;passwd&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;/etc/passwd&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;passwd&#39;</span><span class="p">))</span>
<span class="go">PurePosixPath(&#39;/etc/passwd&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;init.d&#39;</span><span class="p">,</span> <span class="s1">&#39;apache2&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;/etc/init.d/apache2&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;/Program Files&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Program Files&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.match">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">match</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">case_sensitive</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.match" title="Link to this definition">¶</a></dt>
<dd><p>Match this path against the provided glob-style pattern.  Return <code class="docutils literal notranslate"><span class="pre">True</span></code>
if matching is successful, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<p>If <em>pattern</em> is relative, the path can be either relative or absolute,
and matching is done from the right:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;a/b.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;*.py&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/a/b/c.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;b/*.py&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/a/b/c.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;a/*.py&#39;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>If <em>pattern</em> is absolute, the path must be absolute, and the whole path
must match:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;/a.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;/*.py&#39;</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;a/b.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;/*.py&#39;</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>The <em>pattern</em> may be another path object; this speeds up matching the same
pattern against multiple files:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pattern</span> <span class="o">=</span> <span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;*.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PurePath</span><span class="p">(</span><span class="s1">&#39;a/b.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="n">pattern</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Accepts an object implementing the <a class="reference internal" href="os.html#os.PathLike" title="os.PathLike"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.PathLike</span></code></a> interface.</p>
</div>
<p>As with other methods, case-sensitivity follows platform defaults:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;b.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;*.PY&#39;</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;b.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">match</span><span class="p">(</span><span class="s1">&#39;*.PY&#39;</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Set <em>case_sensitive</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> to override this behaviour.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>case_sensitive</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.relative_to">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">relative_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">walk_up</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.relative_to" title="Link to this definition">¶</a></dt>
<dd><p>Compute a version of this path relative to the path represented by
<em>other</em>.  If it’s impossible, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PurePosixPath</span><span class="p">(</span><span class="s1">&#39;/etc/passwd&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">relative_to</span><span class="p">(</span><span class="s1">&#39;/&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;etc/passwd&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">relative_to</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;passwd&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">relative_to</span><span class="p">(</span><span class="s1">&#39;/usr&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;pathlib.py&quot;</span>, line <span class="m">941</span>, in <span class="n">relative_to</span>
<span class="w">    </span><span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="n">error_message</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="bp">self</span><span class="p">),</span> <span class="nb">str</span><span class="p">(</span><span class="n">formatted</span><span class="p">)))</span>
<span class="gr">ValueError</span>: <span class="n">&#39;/etc/passwd&#39; is not in the subpath of &#39;/usr&#39; OR one path is relative and the other is absolute.</span>
</pre></div>
</div>
<p>When <em>walk_up</em> is False (the default), the path must start with <em>other</em>.
When the argument is True, <code class="docutils literal notranslate"><span class="pre">..</span></code> entries may be added to form the
relative path. In all other cases, such as the paths referencing
different drives, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is raised.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">relative_to</span><span class="p">(</span><span class="s1">&#39;/usr&#39;</span><span class="p">,</span> <span class="n">walk_up</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="go">PurePosixPath(&#39;../etc/passwd&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">relative_to</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">,</span> <span class="n">walk_up</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;pathlib.py&quot;</span>, line <span class="m">941</span>, in <span class="n">relative_to</span>
<span class="w">    </span><span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="n">error_message</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="bp">self</span><span class="p">),</span> <span class="nb">str</span><span class="p">(</span><span class="n">formatted</span><span class="p">)))</span>
<span class="gr">ValueError</span>: <span class="n">&#39;/etc/passwd&#39; is not on the same drive as &#39;foo&#39; OR one path is relative and the other is absolute.</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This function is part of <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a> and works with strings.
It does not check or access the underlying file structure.
This can impact the <em>walk_up</em> option as it assumes that no symlinks
are present in the path; call <a class="reference internal" href="#pathlib.Path.resolve" title="pathlib.Path.resolve"><code class="xref py py-meth docutils literal notranslate"><span class="pre">resolve()</span></code></a> first if
necessary to resolve symlinks.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>walk_up</em> parameter was added (old behavior is the same as <code class="docutils literal notranslate"><span class="pre">walk_up=False</span></code>).</p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Passing additional positional arguments is deprecated; if supplied,
they are joined with <em>other</em>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.with_name">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">with_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.with_name" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path with the <a class="reference internal" href="#pathlib.PurePath.name" title="pathlib.PurePath.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code></a> changed.  If the original path
doesn’t have a name, ValueError is raised:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Downloads/pathlib.tar.gz&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_name</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Downloads/setup.py&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_name</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;/home/<USER>/cpython/default/Lib/pathlib.py&quot;</span>, line <span class="m">751</span>, in <span class="n">with_name</span>
<span class="w">    </span><span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%r</span><span class="s2"> has an empty name&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="p">,))</span>
<span class="gr">ValueError</span>: <span class="n">PureWindowsPath(&#39;c:/&#39;) has an empty name</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.with_stem">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">with_stem</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stem</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.with_stem" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path with the <a class="reference internal" href="#pathlib.PurePath.stem" title="pathlib.PurePath.stem"><code class="xref py py-attr docutils literal notranslate"><span class="pre">stem</span></code></a> changed.  If the original path
doesn’t have a name, ValueError is raised:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Downloads/draft.txt&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_stem</span><span class="p">(</span><span class="s1">&#39;final&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Downloads/final.txt&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Downloads/pathlib.tar.gz&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_stem</span><span class="p">(</span><span class="s1">&#39;lib&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Downloads/lib.gz&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_stem</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;/home/<USER>/cpython/default/Lib/pathlib.py&quot;</span>, line <span class="m">861</span>, in <span class="n">with_stem</span>
<span class="w">    </span><span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">with_name</span><span class="p">(</span><span class="n">stem</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">suffix</span><span class="p">)</span>
  File <span class="nb">&quot;/home/<USER>/cpython/default/Lib/pathlib.py&quot;</span>, line <span class="m">851</span>, in <span class="n">with_name</span>
<span class="w">    </span><span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%r</span><span class="s2"> has an empty name&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="p">,))</span>
<span class="gr">ValueError</span>: <span class="n">PureWindowsPath(&#39;c:/&#39;) has an empty name</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.with_suffix">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">with_suffix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">suffix</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.with_suffix" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path with the <a class="reference internal" href="#pathlib.PurePath.suffix" title="pathlib.PurePath.suffix"><code class="xref py py-attr docutils literal notranslate"><span class="pre">suffix</span></code></a> changed.  If the original path
doesn’t have a suffix, the new <em>suffix</em> is appended instead.  If the
<em>suffix</em> is an empty string, the original suffix is removed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Downloads/pathlib.tar.gz&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.bz2&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;c:/Downloads/pathlib.tar.bz2&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;README&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;.txt&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;README.txt&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PureWindowsPath</span><span class="p">(</span><span class="s1">&#39;README.txt&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">with_suffix</span><span class="p">(</span><span class="s1">&#39;&#39;</span><span class="p">)</span>
<span class="go">PureWindowsPath(&#39;README&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.PurePath.with_segments">
<span class="sig-prename descclassname"><span class="pre">PurePath.</span></span><span class="sig-name descname"><span class="pre">with_segments</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PurePath.with_segments" title="Link to this definition">¶</a></dt>
<dd><p>Create a new path object of the same type by combining the given
<em>pathsegments</em>. This method is called whenever a derivative path is created,
such as from <a class="reference internal" href="#pathlib.PurePath.parent" title="pathlib.PurePath.parent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">parent</span></code></a> and <a class="reference internal" href="#pathlib.PurePath.relative_to" title="pathlib.PurePath.relative_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">relative_to()</span></code></a>. Subclasses may
override this method to pass information to derivative paths, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">PurePosixPath</span>

<span class="k">class</span> <span class="nc">MyPath</span><span class="p">(</span><span class="n">PurePosixPath</span><span class="p">):</span>
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">pathsegments</span><span class="p">,</span> <span class="n">session_id</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="o">*</span><span class="n">pathsegments</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session_id</span> <span class="o">=</span> <span class="n">session_id</span>

    <span class="k">def</span> <span class="nf">with_segments</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">pathsegments</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">type</span><span class="p">(</span><span class="bp">self</span><span class="p">)(</span><span class="o">*</span><span class="n">pathsegments</span><span class="p">,</span> <span class="n">session_id</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">session_id</span><span class="p">)</span>

<span class="n">etc</span> <span class="o">=</span> <span class="n">MyPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">,</span> <span class="n">session_id</span><span class="o">=</span><span class="mi">42</span><span class="p">)</span>
<span class="n">hosts</span> <span class="o">=</span> <span class="n">etc</span> <span class="o">/</span> <span class="s1">&#39;hosts&#39;</span>
<span class="nb">print</span><span class="p">(</span><span class="n">hosts</span><span class="o">.</span><span class="n">session_id</span><span class="p">)</span>  <span class="c1"># 42</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="concrete-paths">
<span id="id2"></span><h2>Concrete paths<a class="headerlink" href="#concrete-paths" title="Link to this heading">¶</a></h2>
<p>Concrete paths are subclasses of the pure path classes.  In addition to
operations provided by the latter, they also provide methods to do system
calls on path objects.  There are three ways to instantiate concrete paths:</p>
<dl class="py class">
<dt class="sig sig-object py" id="pathlib.Path">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">Path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>, this class represents concrete paths of
the system’s path flavour (instantiating it creates either a
<a class="reference internal" href="#pathlib.PosixPath" title="pathlib.PosixPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PosixPath</span></code></a> or a <a class="reference internal" href="#pathlib.WindowsPath" title="pathlib.WindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">WindowsPath</span></code></a>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="go">PosixPath(&#39;setup.py&#39;)</span>
</pre></div>
</div>
<p><em>pathsegments</em> is specified similarly to <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="pathlib.PosixPath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">PosixPath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.PosixPath" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> and <a class="reference internal" href="#pathlib.PurePosixPath" title="pathlib.PurePosixPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePosixPath</span></code></a>, this class
represents concrete non-Windows filesystem paths:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">PosixPath</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span>
<span class="go">PosixPath(&#39;/etc&#39;)</span>
</pre></div>
</div>
<p><em>pathsegments</em> is specified similarly to <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="pathlib.WindowsPath">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pathlib.</span></span><span class="sig-name descname"><span class="pre">WindowsPath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.WindowsPath" title="Link to this definition">¶</a></dt>
<dd><p>A subclass of <a class="reference internal" href="#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> and <a class="reference internal" href="#pathlib.PureWindowsPath" title="pathlib.PureWindowsPath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PureWindowsPath</span></code></a>, this class
represents concrete Windows filesystem paths:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">WindowsPath</span><span class="p">(</span><span class="s1">&#39;c:/Program Files/&#39;</span><span class="p">)</span>
<span class="go">WindowsPath(&#39;c:/Program Files&#39;)</span>
</pre></div>
</div>
<p><em>pathsegments</em> is specified similarly to <a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>.</p>
</dd></dl>

<p>You can only instantiate the class flavour that corresponds to your system
(allowing system calls on non-compatible path flavours could lead to
bugs or failures in your application):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">os</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;posix&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="go">PosixPath(&#39;setup.py&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">PosixPath</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="go">PosixPath(&#39;setup.py&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">WindowsPath</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
  File <span class="nb">&quot;pathlib.py&quot;</span>, line <span class="m">798</span>, in <span class="n">__new__</span>
<span class="w">    </span><span class="o">%</span> <span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="vm">__name__</span><span class="p">,))</span>
<span class="gr">NotImplementedError</span>: <span class="n">cannot instantiate &#39;WindowsPath&#39; on your system</span>
</pre></div>
</div>
<section id="methods">
<h3>Methods<a class="headerlink" href="#methods" title="Link to this heading">¶</a></h3>
<p>Concrete paths provide the following methods in addition to pure paths
methods.  Many of these methods can raise an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if a system
call fails (for example because the path doesn’t exist).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span><a class="reference internal" href="#pathlib.Path.exists" title="pathlib.Path.exists"><code class="xref py py-meth docutils literal notranslate"><span class="pre">exists()</span></code></a>, <a class="reference internal" href="#pathlib.Path.is_dir" title="pathlib.Path.is_dir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_dir()</span></code></a>, <a class="reference internal" href="#pathlib.Path.is_file" title="pathlib.Path.is_file"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_file()</span></code></a>,
<a class="reference internal" href="#pathlib.Path.is_mount" title="pathlib.Path.is_mount"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_mount()</span></code></a>, <a class="reference internal" href="#pathlib.Path.is_symlink" title="pathlib.Path.is_symlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_symlink()</span></code></a>,
<a class="reference internal" href="#pathlib.Path.is_block_device" title="pathlib.Path.is_block_device"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_block_device()</span></code></a>, <a class="reference internal" href="#pathlib.Path.is_char_device" title="pathlib.Path.is_char_device"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_char_device()</span></code></a>,
<a class="reference internal" href="#pathlib.Path.is_fifo" title="pathlib.Path.is_fifo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_fifo()</span></code></a>, <a class="reference internal" href="#pathlib.Path.is_socket" title="pathlib.Path.is_socket"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_socket()</span></code></a> now return <code class="docutils literal notranslate"><span class="pre">False</span></code>
instead of raising an exception for paths that contain characters
unrepresentable at the OS level.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.cwd">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">cwd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.cwd" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path object representing the current directory (as returned
by <a class="reference internal" href="os.html#os.getcwd" title="os.getcwd"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.getcwd()</span></code></a>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="o">.</span><span class="n">cwd</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/pathlib&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.home">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">home</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.home" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path object representing the user’s home directory (as
returned by <a class="reference internal" href="os.path.html#os.path.expanduser" title="os.path.expanduser"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.expanduser()</span></code></a> with <code class="docutils literal notranslate"><span class="pre">~</span></code> construct). If the home
directory can’t be resolved, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.stat">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">stat</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.stat" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="os.html#os.stat_result" title="os.stat_result"><code class="xref py py-class docutils literal notranslate"><span class="pre">os.stat_result</span></code></a> object containing information about this path, like <a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a>.
The result is looked up at each call to this method.</p>
<p>This method normally follows symlinks; to stat a symlink add the argument
<code class="docutils literal notranslate"><span class="pre">follow_symlinks=False</span></code>, or use <a class="reference internal" href="#pathlib.Path.lstat" title="pathlib.Path.lstat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">lstat()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
<span class="go">956</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_mtime</span>
<span class="go">1327883547.852554</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>follow_symlinks</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.chmod">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">chmod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.chmod" title="Link to this definition">¶</a></dt>
<dd><p>Change the file mode and permissions, like <a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a>.</p>
<p>This method normally follows symlinks. Some Unix flavours support changing
permissions on the symlink itself; on these platforms you may add the
argument <code class="docutils literal notranslate"><span class="pre">follow_symlinks=False</span></code>, or use <a class="reference internal" href="#pathlib.Path.lchmod" title="pathlib.Path.lchmod"><code class="xref py py-meth docutils literal notranslate"><span class="pre">lchmod()</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_mode</span>
<span class="go">33277</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">chmod</span><span class="p">(</span><span class="mo">0o444</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_mode</span>
<span class="go">33060</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>follow_symlinks</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.exists">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">exists</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.exists" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to an existing file or directory.</p>
<p>This method normally follows symlinks; to check if a symlink exists, add
the argument <code class="docutils literal notranslate"><span class="pre">follow_symlinks=False</span></code>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;/etc&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;nonexistentfile&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">exists</span><span class="p">()</span>
<span class="go">False</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>follow_symlinks</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.expanduser">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">expanduser</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.expanduser" title="Link to this definition">¶</a></dt>
<dd><p>Return a new path with expanded <code class="docutils literal notranslate"><span class="pre">~</span></code> and <code class="docutils literal notranslate"><span class="pre">~user</span></code> constructs,
as returned by <a class="reference internal" href="os.path.html#os.path.expanduser" title="os.path.expanduser"><code class="xref py py-meth docutils literal notranslate"><span class="pre">os.path.expanduser()</span></code></a>. If a home directory can’t be
resolved, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">PosixPath</span><span class="p">(</span><span class="s1">&#39;~/films/Monty Python&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">expanduser</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/films/Monty Python&#39;)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.glob">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">glob</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">case_sensitive</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.glob" title="Link to this definition">¶</a></dt>
<dd><p>Glob the given relative <em>pattern</em> in the directory represented by this path,
yielding all matching files (of any kind):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s1">&#39;*.py&#39;</span><span class="p">))</span>
<span class="go">[PosixPath(&#39;pathlib.py&#39;), PosixPath(&#39;setup.py&#39;), PosixPath(&#39;test_pathlib.py&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s1">&#39;*/*.py&#39;</span><span class="p">))</span>
<span class="go">[PosixPath(&#39;docs/conf.py&#39;)]</span>
</pre></div>
</div>
<p>Patterns are the same as for <a class="reference internal" href="fnmatch.html#module-fnmatch" title="fnmatch: Unix shell style filename pattern matching."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fnmatch</span></code></a>, with the addition of “<code class="docutils literal notranslate"><span class="pre">**</span></code>”
which means “this directory and all subdirectories, recursively”.  In other
words, it enables recursive globbing:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">Path</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">glob</span><span class="p">(</span><span class="s1">&#39;**/*.py&#39;</span><span class="p">))</span>
<span class="go">[PosixPath(&#39;build/lib/pathlib.py&#39;),</span>
<span class="go"> PosixPath(&#39;docs/conf.py&#39;),</span>
<span class="go"> PosixPath(&#39;pathlib.py&#39;),</span>
<span class="go"> PosixPath(&#39;setup.py&#39;),</span>
<span class="go"> PosixPath(&#39;test_pathlib.py&#39;)]</span>
</pre></div>
</div>
<p>This method calls <a class="reference internal" href="#pathlib.Path.is_dir" title="pathlib.Path.is_dir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.is_dir()</span></code></a> on the top-level directory and
propagates any <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception that is raised. Subsequent
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exceptions from scanning directories are suppressed.</p>
<p>By default, or when the <em>case_sensitive</em> keyword-only argument is set to
<code class="docutils literal notranslate"><span class="pre">None</span></code>, this method matches paths using platform-specific casing rules:
typically, case-sensitive on POSIX, and case-insensitive on Windows.
Set <em>case_sensitive</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> to override this behaviour.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using the “<code class="docutils literal notranslate"><span class="pre">**</span></code>” pattern in large directory trees may consume
an inordinate amount of time.</p>
</div>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">pathlib.Path.glob</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">pattern</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Return only directories if <em>pattern</em> ends with a pathname components
separator (<a class="reference internal" href="os.html#os.sep" title="os.sep"><code class="xref py py-data docutils literal notranslate"><span class="pre">sep</span></code></a> or <a class="reference internal" href="os.html#os.altsep" title="os.altsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">altsep</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>case_sensitive</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.group">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">group</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.group" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the group owning the file.  <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> is raised
if the file’s gid isn’t found in the system database.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_dir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_dir" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a directory (or a symbolic link
pointing to a directory), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_file">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_file</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_file" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a regular file (or a symbolic link
pointing to a regular file), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_junction">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_junction</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_junction" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a junction, and <code class="docutils literal notranslate"><span class="pre">False</span></code> for any other
type of file. Currently only Windows supports junctions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_mount">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_mount</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_mount" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path is a <em class="dfn">mount point</em>: a point in a
file system where a different file system has been mounted.  On POSIX, the
function checks whether <em>path</em>’s parent, <code class="file docutils literal notranslate"><span class="pre">path/..</span></code>, is on a different
device than <em>path</em>, or whether <code class="file docutils literal notranslate"><span class="pre">path/..</span></code> and <em>path</em> point to the same
i-node on the same device — this should detect mount points for all Unix
and POSIX variants.  On Windows, a mount point is considered to be a drive
letter root (e.g. <code class="docutils literal notranslate"><span class="pre">c:\</span></code>), a UNC share (e.g. <code class="docutils literal notranslate"><span class="pre">\\server\share</span></code>), or a
mounted filesystem directory.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Windows support was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_symlink">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_symlink</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_symlink" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a symbolic link, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist; other errors (such
as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_socket">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_socket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_socket" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a Unix socket (or a symbolic link
pointing to a Unix socket), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_fifo">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_fifo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_fifo" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a FIFO (or a symbolic link
pointing to a FIFO), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_block_device">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_block_device</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_block_device" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a block device (or a symbolic link
pointing to a block device), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.is_char_device">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_char_device</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.is_char_device" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the path points to a character device (or a symbolic link
pointing to a character device), <code class="docutils literal notranslate"><span class="pre">False</span></code> if it points to another kind of file.</p>
<p><code class="docutils literal notranslate"><span class="pre">False</span></code> is also returned if the path doesn’t exist or is a broken symlink;
other errors (such as permission errors) are propagated.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.iterdir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">iterdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.iterdir" title="Link to this definition">¶</a></dt>
<dd><p>When the path points to a directory, yield path objects of the directory
contents:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;docs&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">p</span><span class="o">.</span><span class="n">iterdir</span><span class="p">():</span> <span class="n">child</span>
<span class="gp">...</span>
<span class="go">PosixPath(&#39;docs/conf.py&#39;)</span>
<span class="go">PosixPath(&#39;docs/_templates&#39;)</span>
<span class="go">PosixPath(&#39;docs/make.bat&#39;)</span>
<span class="go">PosixPath(&#39;docs/index.rst&#39;)</span>
<span class="go">PosixPath(&#39;docs/_build&#39;)</span>
<span class="go">PosixPath(&#39;docs/_static&#39;)</span>
<span class="go">PosixPath(&#39;docs/Makefile&#39;)</span>
</pre></div>
</div>
<p>The children are yielded in arbitrary order, and the special entries
<code class="docutils literal notranslate"><span class="pre">'.'</span></code> and <code class="docutils literal notranslate"><span class="pre">'..'</span></code> are not included.  If a file is removed from or added
to the directory after creating the iterator, whether a path object for
that file be included is unspecified.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.walk">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">walk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">top_down</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">on_error</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">follow_symlinks</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.walk" title="Link to this definition">¶</a></dt>
<dd><p>Generate the file names in a directory tree by walking the tree
either top-down or bottom-up.</p>
<p>For each directory in the directory tree rooted at <em>self</em> (including
<em>self</em> but excluding ‘.’ and ‘..’), the method yields a 3-tuple of
<code class="docutils literal notranslate"><span class="pre">(dirpath,</span> <span class="pre">dirnames,</span> <span class="pre">filenames)</span></code>.</p>
<p><em>dirpath</em> is a <a class="reference internal" href="#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> to the directory currently being walked,
<em>dirnames</em> is a list of strings for the names of subdirectories in <em>dirpath</em>
(excluding <code class="docutils literal notranslate"><span class="pre">'.'</span></code> and <code class="docutils literal notranslate"><span class="pre">'..'</span></code>), and <em>filenames</em> is a list of strings for
the names of the non-directory files in <em>dirpath</em>. To get a full path
(which begins with <em>self</em>) to a file or directory in <em>dirpath</em>, do
<code class="docutils literal notranslate"><span class="pre">dirpath</span> <span class="pre">/</span> <span class="pre">name</span></code>. Whether or not the lists are sorted is file
system-dependent.</p>
<p>If the optional argument <em>top_down</em> is true (which is the default), the triple for a
directory is generated before the triples for any of its subdirectories
(directories are walked top-down).  If <em>top_down</em> is false, the triple
for a directory is generated after the triples for all of its subdirectories
(directories are walked bottom-up). No matter the value of <em>top_down</em>, the
list of subdirectories is retrieved before the triples for the directory and
its subdirectories are walked.</p>
<p>When <em>top_down</em> is true, the caller can modify the <em>dirnames</em> list in-place
(for example, using <a class="reference internal" href="../reference/simple_stmts.html#del"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">del</span></code></a> or slice assignment), and <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a>
will only recurse into the subdirectories whose names remain in <em>dirnames</em>.
This can be used to prune the search, or to impose a specific order of visiting,
or even to inform <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> about directories the caller creates or
renames before it resumes <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> again. Modifying <em>dirnames</em> when
<em>top_down</em> is false has no effect on the behavior of <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> since the
directories in <em>dirnames</em> have already been generated by the time <em>dirnames</em>
is yielded to the caller.</p>
<p>By default, errors from <a class="reference internal" href="os.html#os.scandir" title="os.scandir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.scandir()</span></code></a> are ignored.  If the optional
argument <em>on_error</em> is specified, it should be a callable; it will be
called with one argument, an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> instance. The callable can handle the
error to continue the walk or re-raise it to stop the walk. Note that the
filename is available as the <code class="docutils literal notranslate"><span class="pre">filename</span></code> attribute of the exception object.</p>
<p>By default, <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> does not follow symbolic links, and instead adds them
to the <em>filenames</em> list. Set <em>follow_symlinks</em> to true to resolve symlinks
and place them in <em>dirnames</em> and <em>filenames</em> as appropriate for their targets, and
consequently visit directories pointed to by symlinks (where supported).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Be aware that setting <em>follow_symlinks</em> to true can lead to infinite
recursion if a link points to a parent directory of itself. <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a>
does not keep track of the directories it has already visited.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> assumes the directories it walks are not modified during
execution. For example, if a directory from <em>dirnames</em> has been replaced
with a symlink and <em>follow_symlinks</em> is false, <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> will
still try to descend into it. To prevent such behavior, remove directories
from <em>dirnames</em> as appropriate.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike <a class="reference internal" href="os.html#os.walk" title="os.walk"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.walk()</span></code></a>, <a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a> lists symlinks to directories in
<em>filenames</em> if <em>follow_symlinks</em> is false.</p>
</div>
<p>This example displays the number of bytes used by all files in each directory,
while ignoring <code class="docutils literal notranslate"><span class="pre">__pycache__</span></code> directories:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">pathlib</span> <span class="kn">import</span> <span class="n">Path</span>
<span class="k">for</span> <span class="n">root</span><span class="p">,</span> <span class="n">dirs</span><span class="p">,</span> <span class="n">files</span> <span class="ow">in</span> <span class="n">Path</span><span class="p">(</span><span class="s2">&quot;cpython/Lib/concurrent&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">walk</span><span class="p">(</span><span class="n">on_error</span><span class="o">=</span><span class="nb">print</span><span class="p">):</span>
  <span class="nb">print</span><span class="p">(</span>
      <span class="n">root</span><span class="p">,</span>
      <span class="s2">&quot;consumes&quot;</span><span class="p">,</span>
      <span class="nb">sum</span><span class="p">((</span><span class="n">root</span> <span class="o">/</span> <span class="n">file</span><span class="p">)</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span> <span class="k">for</span> <span class="n">file</span> <span class="ow">in</span> <span class="n">files</span><span class="p">),</span>
      <span class="s2">&quot;bytes in&quot;</span><span class="p">,</span>
      <span class="nb">len</span><span class="p">(</span><span class="n">files</span><span class="p">),</span>
      <span class="s2">&quot;non-directory files&quot;</span>
  <span class="p">)</span>
  <span class="k">if</span> <span class="s1">&#39;__pycache__&#39;</span> <span class="ow">in</span> <span class="n">dirs</span><span class="p">:</span>
        <span class="n">dirs</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="s1">&#39;__pycache__&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>This next example is a simple implementation of <a class="reference internal" href="shutil.html#shutil.rmtree" title="shutil.rmtree"><code class="xref py py-func docutils literal notranslate"><span class="pre">shutil.rmtree()</span></code></a>.
Walking the tree bottom-up is essential as <a class="reference internal" href="#pathlib.Path.rmdir" title="pathlib.Path.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">rmdir()</span></code></a> doesn’t allow
deleting a directory before it is empty:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># Delete everything reachable from the directory &quot;top&quot;.</span>
<span class="c1"># CAUTION:  This is dangerous! For example, if top == Path(&#39;/&#39;),</span>
<span class="c1"># it could delete all of your files.</span>
<span class="k">for</span> <span class="n">root</span><span class="p">,</span> <span class="n">dirs</span><span class="p">,</span> <span class="n">files</span> <span class="ow">in</span> <span class="n">top</span><span class="o">.</span><span class="n">walk</span><span class="p">(</span><span class="n">top_down</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">files</span><span class="p">:</span>
        <span class="p">(</span><span class="n">root</span> <span class="o">/</span> <span class="n">name</span><span class="p">)</span><span class="o">.</span><span class="n">unlink</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="n">dirs</span><span class="p">:</span>
        <span class="p">(</span><span class="n">root</span> <span class="o">/</span> <span class="n">name</span><span class="p">)</span><span class="o">.</span><span class="n">rmdir</span><span class="p">()</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.lchmod">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">lchmod</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.lchmod" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#pathlib.Path.chmod" title="pathlib.Path.chmod"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.chmod()</span></code></a> but, if the path points to a symbolic link, the
symbolic link’s mode is changed rather than its target’s.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.lstat">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">lstat</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.lstat" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#pathlib.Path.stat" title="pathlib.Path.stat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.stat()</span></code></a> but, if the path points to a symbolic link, return
the symbolic link’s information rather than its target’s.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.mkdir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">mkdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0o777</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parents</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exist_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.mkdir" title="Link to this definition">¶</a></dt>
<dd><p>Create a new directory at this given path.  If <em>mode</em> is given, it is
combined with the process’ <code class="docutils literal notranslate"><span class="pre">umask</span></code> value to determine the file mode
and access flags.  If the path already exists, <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a>
is raised.</p>
<p>If <em>parents</em> is true, any missing parents of this path are created
as needed; they are created with the default permissions without taking
<em>mode</em> into account (mimicking the POSIX <code class="docutils literal notranslate"><span class="pre">mkdir</span> <span class="pre">-p</span></code> command).</p>
<p>If <em>parents</em> is false (the default), a missing parent raises
<a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a>.</p>
<p>If <em>exist_ok</em> is false (the default), <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> is
raised if the target directory already exists.</p>
<p>If <em>exist_ok</em> is true, <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> will not be raised unless the given
path already exists in the file system and is not a directory (same
behavior as the POSIX <code class="docutils literal notranslate"><span class="pre">mkdir</span> <span class="pre">-p</span></code> command).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>exist_ok</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.open">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">buffering</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.open" title="Link to this definition">¶</a></dt>
<dd><p>Open the file pointed to by the path, like the built-in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>
function does:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">p</span><span class="o">.</span><span class="n">open</span><span class="p">()</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">f</span><span class="o">.</span><span class="n">readline</span><span class="p">()</span>
<span class="gp">...</span>
<span class="go">&#39;#!/usr/bin/env python3\n&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.owner">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">owner</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.owner" title="Link to this definition">¶</a></dt>
<dd><p>Return the name of the user owning the file.  <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a> is raised
if the file’s uid isn’t found in the system database.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.read_bytes">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">read_bytes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.read_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Return the binary contents of the pointed-to file as a bytes object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;my_binary_file&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">write_bytes</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Binary file contents&#39;</span><span class="p">)</span>
<span class="go">20</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">read_bytes</span><span class="p">()</span>
<span class="go">b&#39;Binary file contents&#39;</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.read_text">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">read_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.read_text" title="Link to this definition">¶</a></dt>
<dd><p>Return the decoded contents of the pointed-to file as a string:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;my_text_file&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">write_text</span><span class="p">(</span><span class="s1">&#39;Text file contents&#39;</span><span class="p">)</span>
<span class="go">18</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">read_text</span><span class="p">()</span>
<span class="go">&#39;Text file contents&#39;</span>
</pre></div>
</div>
<p>The file is opened and then closed. The optional parameters have the same
meaning as in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.readlink">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">readlink</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.readlink" title="Link to this definition">¶</a></dt>
<dd><p>Return the path to which the symbolic link points (as returned by
<a class="reference internal" href="os.html#os.readlink" title="os.readlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.readlink()</span></code></a>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;mylink&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">symlink_to</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">readlink</span><span class="p">()</span>
<span class="go">PosixPath(&#39;setup.py&#39;)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.rename">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">rename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.rename" title="Link to this definition">¶</a></dt>
<dd><p>Rename this file or directory to the given <em>target</em>, and return a new Path
instance pointing to <em>target</em>.  On Unix, if <em>target</em> exists and is a file,
it will be replaced silently if the user has permission.
On Windows, if <em>target</em> exists, <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> will be raised.
<em>target</em> can be either a string or another path object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;foo&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;w&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;some text&#39;</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">target</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;bar&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="n">target</span><span class="p">)</span>
<span class="go">PosixPath(&#39;bar&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">target</span><span class="o">.</span><span class="n">open</span><span class="p">()</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="go">&#39;some text&#39;</span>
</pre></div>
</div>
<p>The target path may be absolute or relative. Relative paths are interpreted
relative to the current working directory, <em>not</em> the directory of the Path
object.</p>
<p>It is implemented in terms of <a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a> and gives the same guarantees.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added return value, return the new Path instance.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.replace">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">replace</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.replace" title="Link to this definition">¶</a></dt>
<dd><p>Rename this file or directory to the given <em>target</em>, and return a new Path
instance pointing to <em>target</em>.  If <em>target</em> points to an existing file or
empty directory, it will be unconditionally replaced.</p>
<p>The target path may be absolute or relative. Relative paths are interpreted
relative to the current working directory, <em>not</em> the directory of the Path
object.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added return value, return the new Path instance.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.absolute">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">absolute</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.absolute" title="Link to this definition">¶</a></dt>
<dd><p>Make the path absolute, without normalization or resolving symlinks.
Returns a new path object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;tests&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span>
<span class="go">PosixPath(&#39;tests&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">absolute</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/pathlib/tests&#39;)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.resolve">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">resolve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">strict</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.resolve" title="Link to this definition">¶</a></dt>
<dd><p>Make the path absolute, resolving any symlinks.  A new path object is
returned:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span>
<span class="go">PosixPath(&#39;.&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/pathlib&#39;)</span>
</pre></div>
</div>
<p>“<code class="docutils literal notranslate"><span class="pre">..</span></code>” components are also eliminated (this is the only method to do so):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;docs/../setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/pathlib/setup.py&#39;)</span>
</pre></div>
</div>
<p>If the path doesn’t exist and <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a>
is raised.  If <em>strict</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>, the path is resolved as far as possible
and any remainder is appended without checking whether it exists.  If an
infinite loop is encountered along the resolution path, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a>
is raised.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <em>strict</em> parameter was added (pre-3.6 behavior is strict).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.rglob">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">rglob</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">case_sensitive</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.rglob" title="Link to this definition">¶</a></dt>
<dd><p>Glob the given relative <em>pattern</em> recursively.  This is like calling
<a class="reference internal" href="#pathlib.Path.glob" title="pathlib.Path.glob"><code class="xref py py-func docutils literal notranslate"><span class="pre">Path.glob()</span></code></a> with “<code class="docutils literal notranslate"><span class="pre">**/</span></code>” added in front of the <em>pattern</em>, where
<em>patterns</em> are the same as for <a class="reference internal" href="fnmatch.html#module-fnmatch" title="fnmatch: Unix shell style filename pattern matching."><code class="xref py py-mod docutils literal notranslate"><span class="pre">fnmatch</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">Path</span><span class="p">()</span><span class="o">.</span><span class="n">rglob</span><span class="p">(</span><span class="s2">&quot;*.py&quot;</span><span class="p">))</span>
<span class="go">[PosixPath(&#39;build/lib/pathlib.py&#39;),</span>
<span class="go"> PosixPath(&#39;docs/conf.py&#39;),</span>
<span class="go"> PosixPath(&#39;pathlib.py&#39;),</span>
<span class="go"> PosixPath(&#39;setup.py&#39;),</span>
<span class="go"> PosixPath(&#39;test_pathlib.py&#39;)]</span>
</pre></div>
</div>
<p>By default, or when the <em>case_sensitive</em> keyword-only argument is set to
<code class="docutils literal notranslate"><span class="pre">None</span></code>, this method matches paths using platform-specific casing rules:
typically, case-sensitive on POSIX, and case-insensitive on Windows.
Set <em>case_sensitive</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> to override this behaviour.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">pathlib.Path.rglob</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">pattern</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Return only directories if <em>pattern</em> ends with a pathname components
separator (<a class="reference internal" href="os.html#os.sep" title="os.sep"><code class="xref py py-data docutils literal notranslate"><span class="pre">sep</span></code></a> or <a class="reference internal" href="os.html#os.altsep" title="os.altsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">altsep</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The <em>case_sensitive</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.rmdir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">rmdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.rmdir" title="Link to this definition">¶</a></dt>
<dd><p>Remove this directory.  The directory must be empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.samefile">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">samefile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">other_path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.samefile" title="Link to this definition">¶</a></dt>
<dd><p>Return whether this path points to the same file as <em>other_path</em>, which
can be either a Path object, or a string.  The semantics are similar
to <a class="reference internal" href="os.path.html#os.path.samefile" title="os.path.samefile"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.samefile()</span></code></a> and <a class="reference internal" href="os.path.html#os.path.samestat" title="os.path.samestat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.samestat()</span></code></a>.</p>
<p>An <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> can be raised if either file cannot be accessed for some
reason.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">q</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;eggs&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">samefile</span><span class="p">(</span><span class="n">q</span><span class="p">)</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">samefile</span><span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.symlink_to">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">symlink_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target_is_directory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.symlink_to" title="Link to this definition">¶</a></dt>
<dd><p>Make this path a symbolic link pointing to <em>target</em>.</p>
<p>On Windows, a symlink represents either a file or a directory, and does not
morph to the target dynamically.  If the target is present, the type of the
symlink will be created to match. Otherwise, the symlink will be created
as a directory if <em>target_is_directory</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> or a file symlink (the
default) otherwise.  On non-Windows platforms, <em>target_is_directory</em> is ignored.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;mylink&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">symlink_to</span><span class="p">(</span><span class="s1">&#39;setup.py&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">resolve</span><span class="p">()</span>
<span class="go">PosixPath(&#39;/home/<USER>/pathlib/setup.py&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">stat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
<span class="go">956</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">lstat</span><span class="p">()</span><span class="o">.</span><span class="n">st_size</span>
<span class="go">8</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The order of arguments (link, target) is the reverse
of <a class="reference internal" href="os.html#os.symlink" title="os.symlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.symlink()</span></code></a>’s.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.hardlink_to">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">hardlink_to</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.hardlink_to" title="Link to this definition">¶</a></dt>
<dd><p>Make this path a hard link to the same file as <em>target</em>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The order of arguments (link, target) is the reverse
of <a class="reference internal" href="os.html#os.link" title="os.link"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.link()</span></code></a>’s.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.touch">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">touch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0o666</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">exist_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.touch" title="Link to this definition">¶</a></dt>
<dd><p>Create a file at this given path.  If <em>mode</em> is given, it is combined
with the process’ <code class="docutils literal notranslate"><span class="pre">umask</span></code> value to determine the file mode and access
flags.  If the file already exists, the function succeeds if <em>exist_ok</em>
is true (and its modification time is updated to the current time),
otherwise <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.unlink">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">unlink</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">missing_ok</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.unlink" title="Link to this definition">¶</a></dt>
<dd><p>Remove this file or symbolic link.  If the path points to a directory,
use <a class="reference internal" href="#pathlib.Path.rmdir" title="pathlib.Path.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">Path.rmdir()</span></code></a> instead.</p>
<p>If <em>missing_ok</em> is false (the default), <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> is
raised if the path does not exist.</p>
<p>If <em>missing_ok</em> is true, <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> exceptions will be
ignored (same behavior as the POSIX <code class="docutils literal notranslate"><span class="pre">rm</span> <span class="pre">-f</span></code> command).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>missing_ok</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.write_bytes">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">write_bytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.write_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Open the file pointed to in bytes mode, write <em>data</em> to it, and close the
file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;my_binary_file&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">write_bytes</span><span class="p">(</span><span class="sa">b</span><span class="s1">&#39;Binary file contents&#39;</span><span class="p">)</span>
<span class="go">20</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">read_bytes</span><span class="p">()</span>
<span class="go">b&#39;Binary file contents&#39;</span>
</pre></div>
</div>
<p>An existing file of the same name is overwritten.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="pathlib.Path.write_text">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">write_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">errors</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pathlib.Path.write_text" title="Link to this definition">¶</a></dt>
<dd><p>Open the file pointed to in text mode, write <em>data</em> to it, and close the
file:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="s1">&#39;my_text_file&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">write_text</span><span class="p">(</span><span class="s1">&#39;Text file contents&#39;</span><span class="p">)</span>
<span class="go">18</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span><span class="o">.</span><span class="n">read_text</span><span class="p">()</span>
<span class="go">&#39;Text file contents&#39;</span>
</pre></div>
</div>
<p>An existing file of the same name is overwritten. The optional parameters
have the same meaning as in <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>The <em>newline</em> parameter was added.</p>
</div>
</dd></dl>

</section>
</section>
<section id="correspondence-to-tools-in-the-os-module">
<h2>Correspondence to tools in the <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> module<a class="headerlink" href="#correspondence-to-tools-in-the-os-module" title="Link to this heading">¶</a></h2>
<p>Below is a table mapping various <a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> functions to their corresponding
<a class="reference internal" href="#pathlib.PurePath" title="pathlib.PurePath"><code class="xref py py-class docutils literal notranslate"><span class="pre">PurePath</span></code></a>/<a class="reference internal" href="#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">Path</span></code></a> equivalent.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Not all pairs of functions/methods below are equivalent. Some of them,
despite having some overlapping use-cases, have different semantics. They
include <a class="reference internal" href="os.path.html#os.path.abspath" title="os.path.abspath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.abspath()</span></code></a> and <a class="reference internal" href="#pathlib.Path.absolute" title="pathlib.Path.absolute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.absolute()</span></code></a>,
<a class="reference internal" href="os.path.html#os.path.relpath" title="os.path.relpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.relpath()</span></code></a> and <a class="reference internal" href="#pathlib.PurePath.relative_to" title="pathlib.PurePath.relative_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">PurePath.relative_to()</span></code></a>.</p>
</div>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><a class="reference internal" href="os.html#module-os" title="os: Miscellaneous operating system interfaces."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code></a> and <a class="reference internal" href="os.path.html#module-os.path" title="os.path: Operations on pathnames."><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code></a></p></th>
<th class="head"><p><a class="reference internal" href="#module-pathlib" title="pathlib: Object-oriented filesystem paths"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code></a></p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.abspath" title="os.path.abspath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.abspath()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.absolute" title="pathlib.Path.absolute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.absolute()</span></code></a> <a class="footnote-reference brackets" href="#id5" id="id3" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.realpath" title="os.path.realpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.realpath()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.resolve" title="pathlib.Path.resolve"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.resolve()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.chmod" title="os.chmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.chmod()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.chmod" title="pathlib.Path.chmod"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.chmod()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.mkdir" title="os.mkdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.mkdir()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.mkdir" title="pathlib.Path.mkdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.mkdir()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.makedirs" title="os.makedirs"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.makedirs()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.mkdir" title="pathlib.Path.mkdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.mkdir()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.rename" title="os.rename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rename()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.rename" title="pathlib.Path.rename"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.rename()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.replace" title="os.replace"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.replace()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.replace" title="pathlib.Path.replace"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.replace()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.rmdir" title="os.rmdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.rmdir()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.rmdir" title="pathlib.Path.rmdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.rmdir()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.remove" title="os.remove"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.remove()</span></code></a>, <a class="reference internal" href="os.html#os.unlink" title="os.unlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.unlink()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.unlink" title="pathlib.Path.unlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.unlink()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.getcwd" title="os.getcwd"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.getcwd()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.cwd" title="pathlib.Path.cwd"><code class="xref py py-func docutils literal notranslate"><span class="pre">Path.cwd()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.exists" title="os.path.exists"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.exists()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.exists" title="pathlib.Path.exists"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.exists()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.expanduser" title="os.path.expanduser"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.expanduser()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.expanduser" title="pathlib.Path.expanduser"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.expanduser()</span></code></a> and
<a class="reference internal" href="#pathlib.Path.home" title="pathlib.Path.home"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.home()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.listdir" title="os.listdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.listdir()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.iterdir" title="pathlib.Path.iterdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.iterdir()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.walk" title="os.walk"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.walk()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.walk" title="pathlib.Path.walk"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.walk()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.isdir" title="os.path.isdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.isdir()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.is_dir" title="pathlib.Path.is_dir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.is_dir()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.isfile" title="os.path.isfile"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.isfile()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.is_file" title="pathlib.Path.is_file"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.is_file()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.islink" title="os.path.islink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.islink()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.is_symlink" title="pathlib.Path.is_symlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.is_symlink()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.link" title="os.link"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.link()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.hardlink_to" title="pathlib.Path.hardlink_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.hardlink_to()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.html#os.symlink" title="os.symlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.symlink()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.symlink_to" title="pathlib.Path.symlink_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.symlink_to()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.readlink" title="os.readlink"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.readlink()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.readlink" title="pathlib.Path.readlink"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.readlink()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.relpath" title="os.path.relpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.relpath()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.relative_to" title="pathlib.PurePath.relative_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">PurePath.relative_to()</span></code></a> <a class="footnote-reference brackets" href="#id6" id="id4" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.html#os.stat" title="os.stat"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.stat()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.stat" title="pathlib.Path.stat"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.stat()</span></code></a>,
<a class="reference internal" href="#pathlib.Path.owner" title="pathlib.Path.owner"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.owner()</span></code></a>,
<a class="reference internal" href="#pathlib.Path.group" title="pathlib.Path.group"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.group()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.isabs" title="os.path.isabs"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.isabs()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.is_absolute" title="pathlib.PurePath.is_absolute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">PurePath.is_absolute()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.join" title="os.path.join"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.join()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.joinpath" title="pathlib.PurePath.joinpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">PurePath.joinpath()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.basename" title="os.path.basename"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.basename()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.name" title="pathlib.PurePath.name"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PurePath.name</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.dirname" title="os.path.dirname"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.dirname()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.parent" title="pathlib.PurePath.parent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PurePath.parent</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="os.path.html#os.path.samefile" title="os.path.samefile"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.samefile()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.Path.samefile" title="pathlib.Path.samefile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.samefile()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="os.path.html#os.path.splitext" title="os.path.splitext"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.splitext()</span></code></a></p></td>
<td><p><a class="reference internal" href="#pathlib.PurePath.stem" title="pathlib.PurePath.stem"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PurePath.stem</span></code></a> and
<a class="reference internal" href="#pathlib.PurePath.suffix" title="pathlib.PurePath.suffix"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PurePath.suffix</span></code></a></p></td>
</tr>
</tbody>
</table>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id5" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id3">1</a><span class="fn-bracket">]</span></span>
<p><a class="reference internal" href="os.path.html#os.path.abspath" title="os.path.abspath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.abspath()</span></code></a> normalizes the resulting path, which may change its meaning in the presence of symlinks, while <a class="reference internal" href="#pathlib.Path.absolute" title="pathlib.Path.absolute"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Path.absolute()</span></code></a> does not.</p>
</aside>
<aside class="footnote brackets" id="id6" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id4">2</a><span class="fn-bracket">]</span></span>
<p><a class="reference internal" href="#pathlib.PurePath.relative_to" title="pathlib.PurePath.relative_to"><code class="xref py py-meth docutils literal notranslate"><span class="pre">PurePath.relative_to()</span></code></a> requires <code class="docutils literal notranslate"><span class="pre">self</span></code> to be the subpath of the argument, but <a class="reference internal" href="os.path.html#os.path.relpath" title="os.path.relpath"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.relpath()</span></code></a> does not.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a><ul>
<li><a class="reference internal" href="#basic-use">Basic use</a></li>
<li><a class="reference internal" href="#pure-paths">Pure paths</a><ul>
<li><a class="reference internal" href="#general-properties">General properties</a></li>
<li><a class="reference internal" href="#operators">Operators</a></li>
<li><a class="reference internal" href="#accessing-individual-parts">Accessing individual parts</a></li>
<li><a class="reference internal" href="#methods-and-properties">Methods and properties</a></li>
</ul>
</li>
<li><a class="reference internal" href="#concrete-paths">Concrete paths</a><ul>
<li><a class="reference internal" href="#methods">Methods</a></li>
</ul>
</li>
<li><a class="reference internal" href="#correspondence-to-tools-in-the-os-module">Correspondence to tools in the <code class="xref py py-mod docutils literal notranslate"><span class="pre">os</span></code> module</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="filesys.html"
                          title="previous chapter">File and Directory Access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="os.path.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">os.path</span></code> — Common pathname manipulations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pathlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="os.path.html" title="os.path — Common pathname manipulations"
             >next</a> |</li>
        <li class="right" >
          <a href="filesys.html" title="File and Directory Access"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="filesys.html" >File and Directory Access</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib</span></code> — Object-oriented filesystem paths</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>