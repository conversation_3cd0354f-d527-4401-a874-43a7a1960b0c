# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_ldap
# 
# Translators:
# Серге<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <aidos.kakim<PERSON><EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2023-07-13 13:02+0000\n"
"Last-Translator: ILMIR <<EMAIL>>, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_filter
msgid ""
"    Filter used to look up user accounts in the LDAP database. It is an    arbitrary LDAP filter in string representation. Any `%s` placeholder    will be replaced by the login (identifier) provided by the user, the filter    should contain at least one such placeholder.\n"
"\n"
"    The filter must result in exactly one (1) result, otherwise the login will    be considered invalid.\n"
"\n"
"    Example (actual attributes depend on LDAP server and setup):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "
msgstr ""
"Фильтр, используемый для поиска учетных записей пользователей в базе данных LDAP. Это произвольный фильтр LDAP в строковом представлении. Любой `%s` заполнитель будет заменен логином (идентификатором), предоставленным пользователем, фильтр должен содержать хотя бы один такой заполнитель. \n"
"\n"
"   Фильтр должен дать ровно один (1) результат, иначе логин будет считаться недействительным. \n"
"\n"
"   Пример (фактические атрибуты зависят от сервера LDAP и настроек): \n"
"\n"
"(&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"   или\n"
" (|(mail=%s)(uid=%s))"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr ""
"Автоматически создавать локальную учётную запись для новых пользователей "
"прошедших проверку подлинности LDAP"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__company
msgid "Company"
msgstr "Компания"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "Company LDAP configuration"
msgstr "Настройки LDAP для компании"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_user
msgid "Create User"
msgstr "Создать пользователя"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_uid
msgid "Created by"
msgstr "Создано"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_base
msgid ""
"DN of the user search scope: all descendants of this base will be searched "
"for users."
msgstr ""
"DN области поиска пользователей: все потомки этой базы будут искать "
"пользователей. "

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__display_name
msgid "Display Name"
msgstr "Отображаемое название"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__id
msgid "ID"
msgstr "Идентификатор"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr "Конфигурация LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company__ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings__ldaps
msgid "LDAP Parameters"
msgstr "Параметры LDAP"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr "Сервер LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server
msgid "LDAP Server address"
msgstr "Адрес сервера LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server_port
msgid "LDAP Server port"
msgstr "Порт сервера LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_base
msgid "LDAP base"
msgstr "LDAP-база"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_binddn
msgid "LDAP binddn"
msgstr "LDAP binddn"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_filter
msgid "LDAP filter"
msgstr "Фильтр LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_password
msgid "LDAP password"
msgstr "Пароль LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr "Информация входа"

#. module: auth_ldap
#. odoo-python
#: code:addons/auth_ldap/models/res_company_ldap.py:0
#, python-format
msgid "No local user found for LDAP login and not configured to create one"
msgstr ""
"Не найден локальный пользователь для входа в LDAP и не настроен для его "
"создания"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr "Параметры подключения"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr ""
"Запросить поддержку TLS/SSL шифрования при подключении к LDAP серверу. Для "
"подключения требуется LDAP-сервер с поддержкой параметра STARTTLS, в "
"противном случае все попытки проверки подлинности потерпят неудачу."

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr "Сведения о сервере"

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr "Настройте параметры подключения к LDAP серверу"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__user
msgid "Template User"
msgstr "Шаблон пользователя"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr ""
"Пароль пользователя LDAP, который будет использован при запросах в LDAP-"
"каталог."

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr ""
"Учетная запись пользователя LDAP, которая будет использована при запросах в "
"LDAP-каталог. Оставьте пустым для анонимного подключения."

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_tls
msgid "Use TLS"
msgstr "Использовать TLS"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr "Сведения о пользователе"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__user
msgid "User to copy when creating new users"
msgstr "Шаблон локального пользователя для создания новых учетных записей"
