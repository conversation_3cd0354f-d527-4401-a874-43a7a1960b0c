<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="tkinter.tix — Extension widgets for Tk" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/tkinter.tix.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/tkinter/tix.py The tkinter.tix(Tk Interface Extension) module provides an additional rich set of widgets. Although the standard Tk library has many useful widgets, they are far fro..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/tkinter/tix.py The tkinter.tix(Tk Interface Extension) module provides an additional rich set of widgets. Although the standard Tk library has many useful widgets, they are far fro..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>tkinter.tix — Extension widgets for Tk &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="IDLE" href="idle.html" />
    <link rel="prev" title="tkinter.ttk — Tk themed widgets" href="tkinter.ttk.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/tkinter.tix.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a><ul>
<li><a class="reference internal" href="#using-tix">Using Tix</a></li>
<li><a class="reference internal" href="#tix-widgets">Tix Widgets</a><ul>
<li><a class="reference internal" href="#basic-widgets">Basic Widgets</a></li>
<li><a class="reference internal" href="#file-selectors">File Selectors</a></li>
<li><a class="reference internal" href="#hierarchical-listbox">Hierarchical ListBox</a></li>
<li><a class="reference internal" href="#tabular-listbox">Tabular ListBox</a></li>
<li><a class="reference internal" href="#manager-widgets">Manager Widgets</a></li>
<li><a class="reference internal" href="#image-types">Image Types</a></li>
<li><a class="reference internal" href="#miscellaneous-widgets">Miscellaneous Widgets</a></li>
<li><a class="reference internal" href="#form-geometry-manager">Form Geometry Manager</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tix-commands">Tix Commands</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.ttk.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="idle.html"
                          title="next chapter">IDLE</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.tix.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="idle.html" title="IDLE"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tkinter.ttk.html" title="tkinter.ttk — Tk themed widgets"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-tkinter.tix">
<span id="tkinter-tix-extension-widgets-for-tk"></span><h1><a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> — Extension widgets for Tk<a class="headerlink" href="#module-tkinter.tix" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/tkinter/tix.py">Lib/tkinter/tix.py</a></p>
<div class="deprecated" id="index-0">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span>This Tk extension is unmaintained and should not be used in new code.  Use
<a class="reference internal" href="tkinter.ttk.html#module-tkinter.ttk" title="tkinter.ttk: Tk themed widget set"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code></a> instead.</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> (Tk Interface Extension) module provides an additional
rich set of widgets. Although the standard Tk library has many useful widgets,
they are far from complete. The <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> library provides most of the
commonly needed widgets that are missing from standard Tk: <a class="reference internal" href="#tkinter.tix.HList" title="tkinter.tix.HList"><code class="xref py py-class docutils literal notranslate"><span class="pre">HList</span></code></a>,
<a class="reference internal" href="#tkinter.tix.ComboBox" title="tkinter.tix.ComboBox"><code class="xref py py-class docutils literal notranslate"><span class="pre">ComboBox</span></code></a>, <a class="reference internal" href="#tkinter.tix.Control" title="tkinter.tix.Control"><code class="xref py py-class docutils literal notranslate"><span class="pre">Control</span></code></a> (a.k.a. SpinBox) and an assortment of
scrollable widgets.
<a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> also includes many more widgets that are generally useful in
a wide range of applications: <a class="reference internal" href="#tkinter.tix.NoteBook" title="tkinter.tix.NoteBook"><code class="xref py py-class docutils literal notranslate"><span class="pre">NoteBook</span></code></a>, <a class="reference internal" href="#tkinter.tix.FileEntry" title="tkinter.tix.FileEntry"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileEntry</span></code></a>,
<a class="reference internal" href="#tkinter.tix.PanedWindow" title="tkinter.tix.PanedWindow"><code class="xref py py-class docutils literal notranslate"><span class="pre">PanedWindow</span></code></a>, etc; there are more than 40 of them.</p>
<p>With all these new widgets, you can introduce new interaction techniques into
applications, creating more useful and more intuitive user interfaces. You can
design your application by choosing the most appropriate widgets to match the
special needs of your application and users.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://tix.sourceforge.net/">Tix Homepage</a></dt><dd><p>The home page for <code class="xref py py-mod docutils literal notranslate"><span class="pre">Tix</span></code>.  This includes links to additional documentation
and downloads.</p>
</dd>
<dt><a class="reference external" href="https://tix.sourceforge.net/dist/current/man/">Tix Man Pages</a></dt><dd><p>On-line version of the man pages and reference material.</p>
</dd>
<dt><a class="reference external" href="https://tix.sourceforge.net/dist/current/docs/tix-book/tix.book.html">Tix Programming Guide</a></dt><dd><p>On-line version of the programmer’s reference material.</p>
</dd>
<dt><a class="reference external" href="https://tix.sourceforge.net/Tixapps/src/Tide.html">Tix Development Applications</a></dt><dd><p>Tix applications for development of Tix and Tkinter programs. Tide applications
work under Tk or Tkinter, and include <strong class="program">TixInspect</strong>, an inspector to
remotely modify and debug Tix/Tk/Tkinter applications.</p>
</dd>
</dl>
</div>
<section id="using-tix">
<h2>Using Tix<a class="headerlink" href="#using-tix" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Tk">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Tk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">screenName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">baseName</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">className</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'Tix'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.Tk" title="Link to this definition">¶</a></dt>
<dd><p>Toplevel widget of Tix which represents mostly the main window of an
application. It has an associated Tcl interpreter.</p>
<p>Classes in the <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> module subclasses the classes in the
<a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a>. The former imports the latter, so to use <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a>
with Tkinter, all you need to do is to import one module. In general, you
can just import <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a>, and replace the toplevel call to
<a class="reference internal" href="tkinter.html#tkinter.Tk" title="tkinter.Tk"><code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Tk</span></code></a> with <code class="xref py py-class docutils literal notranslate"><span class="pre">tix.Tk</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">tix</span>
<span class="kn">from</span> <span class="nn">tkinter.constants</span> <span class="kn">import</span> <span class="o">*</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">tix</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<p>To use <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a>, you must have the Tix widgets installed, usually
alongside your installation of the Tk widgets. To test your installation, try
the following:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">tix</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">tix</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>
<span class="n">root</span><span class="o">.</span><span class="n">tk</span><span class="o">.</span><span class="n">eval</span><span class="p">(</span><span class="s1">&#39;package require Tix&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="tix-widgets">
<h2>Tix Widgets<a class="headerlink" href="#tix-widgets" title="Link to this heading">¶</a></h2>
<p><a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/TixIntro.htm">Tix</a>
introduces over 40 widget classes to the <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> repertoire.</p>
<section id="basic-widgets">
<h3>Basic Widgets<a class="headerlink" href="#basic-widgets" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Balloon">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Balloon</span></span><a class="headerlink" href="#tkinter.tix.Balloon" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixBalloon.htm">Balloon</a> that
pops up over a widget to provide help.  When the user moves the cursor inside a
widget to which a Balloon widget has been bound, a small pop-up window with a
descriptive message will be shown on the screen.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.ButtonBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">ButtonBox</span></span><a class="headerlink" href="#tkinter.tix.ButtonBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixButtonBox.htm">ButtonBox</a>
widget creates a box of buttons, such as is commonly used for <code class="docutils literal notranslate"><span class="pre">Ok</span> <span class="pre">Cancel</span></code>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.ComboBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">ComboBox</span></span><a class="headerlink" href="#tkinter.tix.ComboBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixComboBox.htm">ComboBox</a>
widget is similar to the combo box control in MS Windows. The user can select a
choice by either typing in the entry subwidget or selecting from the listbox
subwidget.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Control">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Control</span></span><a class="headerlink" href="#tkinter.tix.Control" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixControl.htm">Control</a>
widget is also known as the <code class="xref py py-class docutils literal notranslate"><span class="pre">SpinBox</span></code> widget. The user can adjust the
value by pressing the two arrow buttons or by entering the value directly into
the entry. The new value will be checked against the user-defined upper and
lower limits.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.LabelEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">LabelEntry</span></span><a class="headerlink" href="#tkinter.tix.LabelEntry" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixLabelEntry.htm">LabelEntry</a>
widget packages an entry widget and a label into one mega widget. It can
be used to simplify the creation of “entry-form” type of interface.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.LabelFrame">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">LabelFrame</span></span><a class="headerlink" href="#tkinter.tix.LabelFrame" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixLabelFrame.htm">LabelFrame</a>
widget packages a frame widget and a label into one mega widget.  To create
widgets inside a LabelFrame widget, one creates the new widgets relative to the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">frame</span></code> subwidget and manage them inside the <code class="xref py py-attr docutils literal notranslate"><span class="pre">frame</span></code> subwidget.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Meter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Meter</span></span><a class="headerlink" href="#tkinter.tix.Meter" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixMeter.htm">Meter</a> widget
can be used to show the progress of a background job which may take a long time
to execute.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.OptionMenu">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">OptionMenu</span></span><a class="headerlink" href="#tkinter.tix.OptionMenu" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixOptionMenu.htm">OptionMenu</a>
creates a menu button of options.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.PopupMenu">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">PopupMenu</span></span><a class="headerlink" href="#tkinter.tix.PopupMenu" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixPopupMenu.htm">PopupMenu</a>
widget can be used as a replacement of the <code class="docutils literal notranslate"><span class="pre">tk_popup</span></code> command. The advantage
of the <code class="xref py py-mod docutils literal notranslate"><span class="pre">Tix</span></code> <a class="reference internal" href="#tkinter.tix.PopupMenu" title="tkinter.tix.PopupMenu"><code class="xref py py-class docutils literal notranslate"><span class="pre">PopupMenu</span></code></a> widget is it requires less application code
to manipulate.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Select">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Select</span></span><a class="headerlink" href="#tkinter.tix.Select" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixSelect.htm">Select</a> widget
is a container of button subwidgets. It can be used to provide radio-box or
check-box style of selection options for the user.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.StdButtonBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">StdButtonBox</span></span><a class="headerlink" href="#tkinter.tix.StdButtonBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixStdButtonBox.htm">StdButtonBox</a>
widget is a group of standard buttons for Motif-like dialog boxes.</p>
</dd></dl>

</section>
<section id="file-selectors">
<h3>File Selectors<a class="headerlink" href="#file-selectors" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.DirList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">DirList</span></span><a class="headerlink" href="#tkinter.tix.DirList" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixDirList.htm">DirList</a>
widget displays a list view of a directory, its previous directories and its
sub-directories. The user can choose one of the directories displayed in the
list or change to another directory.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.DirTree">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">DirTree</span></span><a class="headerlink" href="#tkinter.tix.DirTree" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixDirTree.htm">DirTree</a>
widget displays a tree view of a directory, its previous directories and its
sub-directories. The user can choose one of the directories displayed in the
list or change to another directory.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.DirSelectDialog">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">DirSelectDialog</span></span><a class="headerlink" href="#tkinter.tix.DirSelectDialog" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixDirSelectDialog.htm">DirSelectDialog</a>
widget presents the directories in the file system in a dialog window.  The user
can use this dialog window to navigate through the file system to select the
desired directory.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.DirSelectBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">DirSelectBox</span></span><a class="headerlink" href="#tkinter.tix.DirSelectBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#tkinter.tix.DirSelectBox" title="tkinter.tix.DirSelectBox"><code class="xref py py-class docutils literal notranslate"><span class="pre">DirSelectBox</span></code></a> is similar to the standard Motif(TM)
directory-selection box. It is generally used for the user to choose a
directory.  DirSelectBox stores the directories mostly recently selected into
a ComboBox widget so that they can be quickly selected again.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.ExFileSelectBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">ExFileSelectBox</span></span><a class="headerlink" href="#tkinter.tix.ExFileSelectBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixExFileSelectBox.htm">ExFileSelectBox</a>
widget is usually embedded in a tixExFileSelectDialog widget. It provides a
convenient method for the user to select files. The style of the
<a class="reference internal" href="#tkinter.tix.ExFileSelectBox" title="tkinter.tix.ExFileSelectBox"><code class="xref py py-class docutils literal notranslate"><span class="pre">ExFileSelectBox</span></code></a> widget is very similar to the standard file dialog on
MS Windows 3.1.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.FileSelectBox">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">FileSelectBox</span></span><a class="headerlink" href="#tkinter.tix.FileSelectBox" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixFileSelectBox.htm">FileSelectBox</a>
is similar to the standard Motif(TM) file-selection box. It is generally used
for the user to choose a file. FileSelectBox stores the files mostly recently
selected into a <a class="reference internal" href="#tkinter.tix.ComboBox" title="tkinter.tix.ComboBox"><code class="xref py py-class docutils literal notranslate"><span class="pre">ComboBox</span></code></a> widget so that they can be quickly selected
again.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.FileEntry">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">FileEntry</span></span><a class="headerlink" href="#tkinter.tix.FileEntry" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixFileEntry.htm">FileEntry</a>
widget can be used to input a filename. The user can type in the filename
manually. Alternatively, the user can press the button widget that sits next to
the entry, which will bring up a file selection dialog.</p>
</dd></dl>

</section>
<section id="hierarchical-listbox">
<h3>Hierarchical ListBox<a class="headerlink" href="#hierarchical-listbox" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.HList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">HList</span></span><a class="headerlink" href="#tkinter.tix.HList" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixHList.htm">HList</a> widget
can be used to display any data that have a hierarchical structure, for example,
file system directory trees. The list entries are indented and connected by
branch lines according to their places in the hierarchy.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.CheckList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">CheckList</span></span><a class="headerlink" href="#tkinter.tix.CheckList" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixCheckList.htm">CheckList</a>
widget displays a list of items to be selected by the user. CheckList acts
similarly to the Tk checkbutton or radiobutton widgets, except it is capable of
handling many more items than checkbuttons or radiobuttons.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Tree">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Tree</span></span><a class="headerlink" href="#tkinter.tix.Tree" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixTree.htm">Tree</a> widget
can be used to display hierarchical data in a tree form. The user can adjust the
view of the tree by opening or closing parts of the tree.</p>
</dd></dl>

</section>
<section id="tabular-listbox">
<h3>Tabular ListBox<a class="headerlink" href="#tabular-listbox" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.TList">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">TList</span></span><a class="headerlink" href="#tkinter.tix.TList" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixTList.htm">TList</a> widget
can be used to display data in a tabular format. The list entries of a
<a class="reference internal" href="#tkinter.tix.TList" title="tkinter.tix.TList"><code class="xref py py-class docutils literal notranslate"><span class="pre">TList</span></code></a> widget are similar to the entries in the Tk listbox widget.  The
main differences are (1) the <a class="reference internal" href="#tkinter.tix.TList" title="tkinter.tix.TList"><code class="xref py py-class docutils literal notranslate"><span class="pre">TList</span></code></a> widget can display the list entries
in a two dimensional format and (2) you can use graphical images as well as
multiple colors and fonts for the list entries.</p>
</dd></dl>

</section>
<section id="manager-widgets">
<h3>Manager Widgets<a class="headerlink" href="#manager-widgets" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.PanedWindow">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">PanedWindow</span></span><a class="headerlink" href="#tkinter.tix.PanedWindow" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixPanedWindow.htm">PanedWindow</a>
widget allows the user to interactively manipulate the sizes of several panes.
The panes can be arranged either vertically or horizontally.  The user changes
the sizes of the panes by dragging the resize handle between two panes.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.ListNoteBook">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">ListNoteBook</span></span><a class="headerlink" href="#tkinter.tix.ListNoteBook" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixListNoteBook.htm">ListNoteBook</a>
widget is very similar to the <code class="xref py py-class docutils literal notranslate"><span class="pre">TixNoteBook</span></code> widget: it can be used to
display many windows in a limited space using a notebook metaphor. The notebook
is divided into a stack of pages (windows). At one time only one of these pages
can be shown. The user can navigate through these pages by choosing the name of
the desired page in the <code class="xref py py-attr docutils literal notranslate"><span class="pre">hlist</span></code> subwidget.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.NoteBook">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">NoteBook</span></span><a class="headerlink" href="#tkinter.tix.NoteBook" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixNoteBook.htm">NoteBook</a>
widget can be used to display many windows in a limited space using a notebook
metaphor. The notebook is divided into a stack of pages. At one time only one of
these pages can be shown. The user can navigate through these pages by choosing
the visual “tabs” at the top of the NoteBook widget.</p>
</dd></dl>

</section>
<section id="image-types">
<h3>Image Types<a class="headerlink" href="#image-types" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> module adds:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/pixmap.htm">pixmap</a>
capabilities to all <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> and <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> widgets to create
color images from XPM files.</p>
</li>
<li><p><a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/compound.htm">Compound</a> image
types can be used to create images that consists of multiple horizontal lines;
each line is composed of a series of items (texts, bitmaps, images or spaces)
arranged from left to right. For example, a compound image can be used to
display a bitmap and a text string simultaneously in a Tk <code class="xref py py-class docutils literal notranslate"><span class="pre">Button</span></code>
widget.</p>
</li>
</ul>
</section>
<section id="miscellaneous-widgets">
<h3>Miscellaneous Widgets<a class="headerlink" href="#miscellaneous-widgets" title="Link to this heading">¶</a></h3>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.InputOnly">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">InputOnly</span></span><a class="headerlink" href="#tkinter.tix.InputOnly" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixInputOnly.htm">InputOnly</a>
widgets are to accept inputs from the user, which can be done with the <code class="docutils literal notranslate"><span class="pre">bind</span></code>
command (Unix only).</p>
</dd></dl>

</section>
<section id="form-geometry-manager">
<h3>Form Geometry Manager<a class="headerlink" href="#form-geometry-manager" title="Link to this heading">¶</a></h3>
<p>In addition, <a class="reference internal" href="#module-tkinter.tix" title="tkinter.tix: Tk Extension Widgets for Tkinter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code></a> augments <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter</span></code></a> by providing:</p>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.Form">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">Form</span></span><a class="headerlink" href="#tkinter.tix.Form" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tixForm.htm">Form</a> geometry
manager based on attachment rules for all Tk widgets.</p>
</dd></dl>

</section>
</section>
<section id="tix-commands">
<h2>Tix Commands<a class="headerlink" href="#tix-commands" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tkinter.tix.</span></span><span class="sig-name descname"><span class="pre">tixCommand</span></span><a class="headerlink" href="#tkinter.tix.tixCommand" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference external" href="https://tix.sourceforge.net/dist/current/man/html/TixCmd/tix.htm">tix commands</a> provide
access to miscellaneous elements of <code class="xref py py-mod docutils literal notranslate"><span class="pre">Tix</span></code>’s internal state and the
<code class="xref py py-mod docutils literal notranslate"><span class="pre">Tix</span></code> application context.  Most of the information manipulated by these
methods pertains to the application as a whole, or to a screen or display,
rather than to a particular window.</p>
<p>To view the current settings, the common usage is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">tkinter</span> <span class="kn">import</span> <span class="n">tix</span>
<span class="n">root</span> <span class="o">=</span> <span class="n">tix</span><span class="o">.</span><span class="n">Tk</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="n">root</span><span class="o">.</span><span class="n">tix_configure</span><span class="p">())</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_configure">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_configure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cnf</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kw</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_configure" title="Link to this definition">¶</a></dt>
<dd><p>Query or modify the configuration options of the Tix application context. If no
option is specified, returns a dictionary all of the available options.  If
option is specified with no value, then the method returns a list describing the
one named option (this list will be identical to the corresponding sublist of
the value returned if no option is specified).  If one or more option-value
pairs are specified, then the method modifies the given option(s) to have the
given value(s); in this case the method returns an empty string. Option may be
any of the configuration options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_cget">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_cget</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">option</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_cget" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current value of the configuration option given by <em>option</em>. Option
may be any of the configuration options.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_getbitmap">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_getbitmap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_getbitmap" title="Link to this definition">¶</a></dt>
<dd><p>Locates a bitmap file of the name <code class="docutils literal notranslate"><span class="pre">name.xpm</span></code> or <code class="docutils literal notranslate"><span class="pre">name</span></code> in one of the bitmap
directories (see the <a class="reference internal" href="#tkinter.tix.tixCommand.tix_addbitmapdir" title="tkinter.tix.tixCommand.tix_addbitmapdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_addbitmapdir()</span></code></a> method).  By using
<a class="reference internal" href="#tkinter.tix.tixCommand.tix_getbitmap" title="tkinter.tix.tixCommand.tix_getbitmap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getbitmap()</span></code></a>, you can avoid hard coding the pathnames of the bitmap
files in your application. When successful, it returns the complete pathname of
the bitmap file, prefixed with the character <code class="docutils literal notranslate"><span class="pre">&#64;</span></code>.  The returned value can be
used to configure the <code class="docutils literal notranslate"><span class="pre">bitmap</span></code> option of the Tk and Tix widgets.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_addbitmapdir">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_addbitmapdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">directory</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_addbitmapdir" title="Link to this definition">¶</a></dt>
<dd><p>Tix maintains a list of directories under which the <a class="reference internal" href="#tkinter.tix.tixCommand.tix_getimage" title="tkinter.tix.tixCommand.tix_getimage"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getimage()</span></code></a> and
<a class="reference internal" href="#tkinter.tix.tixCommand.tix_getbitmap" title="tkinter.tix.tixCommand.tix_getbitmap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getbitmap()</span></code></a> methods will search for image files.  The standard bitmap
directory is <code class="file docutils literal notranslate"><span class="pre">$TIX_LIBRARY/bitmaps</span></code>. The <a class="reference internal" href="#tkinter.tix.tixCommand.tix_addbitmapdir" title="tkinter.tix.tixCommand.tix_addbitmapdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_addbitmapdir()</span></code></a> method
adds <em>directory</em> into this list. By using this method, the image files of an
applications can also be located using the <a class="reference internal" href="#tkinter.tix.tixCommand.tix_getimage" title="tkinter.tix.tixCommand.tix_getimage"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getimage()</span></code></a> or
<a class="reference internal" href="#tkinter.tix.tixCommand.tix_getbitmap" title="tkinter.tix.tixCommand.tix_getbitmap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getbitmap()</span></code></a> method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_filedialog">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_filedialog</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">dlgclass</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_filedialog" title="Link to this definition">¶</a></dt>
<dd><p>Returns the file selection dialog that may be shared among different calls from
this application.  This method will create a file selection dialog widget when
it is called the first time. This dialog will be returned by all subsequent
calls to <a class="reference internal" href="#tkinter.tix.tixCommand.tix_filedialog" title="tkinter.tix.tixCommand.tix_filedialog"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_filedialog()</span></code></a>.  An optional dlgclass parameter can be passed
as a string to specified what type of file selection dialog widget is desired.
Possible options are <code class="docutils literal notranslate"><span class="pre">tix</span></code>, <code class="docutils literal notranslate"><span class="pre">FileSelectDialog</span></code> or <code class="docutils literal notranslate"><span class="pre">tixExFileSelectDialog</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_getimage">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_getimage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_getimage" title="Link to this definition">¶</a></dt>
<dd><p>Locates an image file of the name <code class="file docutils literal notranslate"><span class="pre">name.xpm</span></code>, <code class="file docutils literal notranslate"><span class="pre">name.xbm</span></code> or
<code class="file docutils literal notranslate"><span class="pre">name.ppm</span></code> in one of the bitmap directories (see the
<a class="reference internal" href="#tkinter.tix.tixCommand.tix_addbitmapdir" title="tkinter.tix.tixCommand.tix_addbitmapdir"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_addbitmapdir()</span></code></a> method above). If more than one file with the same name
(but different extensions) exist, then the image type is chosen according to the
depth of the X display: xbm images are chosen on monochrome displays and color
images are chosen on color displays. By using <a class="reference internal" href="#tkinter.tix.tixCommand.tix_getimage" title="tkinter.tix.tixCommand.tix_getimage"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_getimage()</span></code></a>, you can
avoid hard coding the pathnames of the image files in your application. When
successful, this method returns the name of the newly created image, which can
be used to configure the <code class="docutils literal notranslate"><span class="pre">image</span></code> option of the Tk and Tix widgets.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_option_get">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_option_get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_option_get" title="Link to this definition">¶</a></dt>
<dd><p>Gets the options maintained by the Tix scheme mechanism.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="tkinter.tix.tixCommand.tix_resetoptions">
<span class="sig-prename descclassname"><span class="pre">tixCommand.</span></span><span class="sig-name descname"><span class="pre">tix_resetoptions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">newScheme</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newFontSet</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">newScmPrio</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#tkinter.tix.tixCommand.tix_resetoptions" title="Link to this definition">¶</a></dt>
<dd><p>Resets the scheme and fontset of the Tix application to <em>newScheme</em> and
<em>newFontSet</em>, respectively.  This affects only those widgets created after this
call.  Therefore, it is best to call the resetoptions method before the creation
of any widgets in a Tix application.</p>
<p>The optional parameter <em>newScmPrio</em> can be given to reset the priority level of
the Tk options set by the Tix schemes.</p>
<p>Because of the way Tk handles the X option database, after Tix has been has
imported and inited, it is not possible to reset the color schemes and font sets
using the <code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_config()</span></code> method. Instead, the <a class="reference internal" href="#tkinter.tix.tixCommand.tix_resetoptions" title="tkinter.tix.tixCommand.tix_resetoptions"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tix_resetoptions()</span></code></a>
method must be used.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a><ul>
<li><a class="reference internal" href="#using-tix">Using Tix</a></li>
<li><a class="reference internal" href="#tix-widgets">Tix Widgets</a><ul>
<li><a class="reference internal" href="#basic-widgets">Basic Widgets</a></li>
<li><a class="reference internal" href="#file-selectors">File Selectors</a></li>
<li><a class="reference internal" href="#hierarchical-listbox">Hierarchical ListBox</a></li>
<li><a class="reference internal" href="#tabular-listbox">Tabular ListBox</a></li>
<li><a class="reference internal" href="#manager-widgets">Manager Widgets</a></li>
<li><a class="reference internal" href="#image-types">Image Types</a></li>
<li><a class="reference internal" href="#miscellaneous-widgets">Miscellaneous Widgets</a></li>
<li><a class="reference internal" href="#form-geometry-manager">Form Geometry Manager</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tix-commands">Tix Commands</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tkinter.ttk.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.ttk</span></code> — Tk themed widgets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="idle.html"
                          title="next chapter">IDLE</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/tkinter.tix.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="idle.html" title="IDLE"
             >next</a> |</li>
        <li class="right" >
          <a href="tkinter.ttk.html" title="tkinter.ttk — Tk themed widgets"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >Graphical User Interfaces with Tk</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">tkinter.tix</span></code> — Extension widgets for Tk</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>