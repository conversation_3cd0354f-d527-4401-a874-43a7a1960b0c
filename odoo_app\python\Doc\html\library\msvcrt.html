<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="msvcrt — Useful routines from the MS VC++ runtime" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/msvcrt.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="These functions provide access to some useful capabilities on Windows platforms. Some higher-level modules use these functions to build the Windows implementations of their services. For example, t..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="These functions provide access to some useful capabilities on Windows platforms. Some higher-level modules use these functions to build the Windows implementations of their services. For example, t..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>msvcrt — Useful routines from the MS VC++ runtime &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="winreg — Windows registry access" href="winreg.html" />
    <link rel="prev" title="MS Windows Specific Services" href="windows.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/msvcrt.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a><ul>
<li><a class="reference internal" href="#file-operations">File Operations</a></li>
<li><a class="reference internal" href="#console-i-o">Console I/O</a></li>
<li><a class="reference internal" href="#other-functions">Other Functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="windows.html"
                          title="previous chapter">MS Windows Specific Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="winreg.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/msvcrt.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="winreg.html" title="winreg — Windows registry access"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="windows.html" title="MS Windows Specific Services"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" accesskey="U">MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-msvcrt">
<span id="msvcrt-useful-routines-from-the-ms-vc-runtime"></span><h1><a class="reference internal" href="#module-msvcrt" title="msvcrt: Miscellaneous useful routines from the MS VC++ runtime. (Windows)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code></a> — Useful routines from the MS VC++ runtime<a class="headerlink" href="#module-msvcrt" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>These functions provide access to some useful capabilities on Windows platforms.
Some higher-level modules use these functions to build the  Windows
implementations of their services.  For example, the <a class="reference internal" href="getpass.html#module-getpass" title="getpass: Portable reading of passwords and retrieval of the userid."><code class="xref py py-mod docutils literal notranslate"><span class="pre">getpass</span></code></a> module uses
this in the implementation of the <a class="reference internal" href="getpass.html#module-getpass" title="getpass: Portable reading of passwords and retrieval of the userid."><code class="xref py py-func docutils literal notranslate"><span class="pre">getpass()</span></code></a> function.</p>
<p>Further documentation on these functions can be found in the Platform API
documentation.</p>
<p>The module implements both the normal and wide char variants of the console I/O
api. The normal API deals only with ASCII characters and is of limited use
for internationalized applications. The wide char API should be used where
ever possible.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Operations in this module now raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> where <a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a>
was raised.</p>
</div>
<section id="file-operations">
<span id="msvcrt-files"></span><h2>File Operations<a class="headerlink" href="#file-operations" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.locking">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">locking</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">nbytes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.locking" title="Link to this definition">¶</a></dt>
<dd><p>Lock part of a file based on file descriptor <em>fd</em> from the C runtime.  Raises
<a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> on failure.  The locked region of the file extends from the
current file position for <em>nbytes</em> bytes, and may continue beyond the end of the
file.  <em>mode</em> must be one of the <code class="xref py py-const docutils literal notranslate"><span class="pre">LK_*</span></code> constants listed below. Multiple
regions in a file may be locked at the same time, but may not overlap.  Adjacent
regions are not merged; they must be unlocked individually.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">msvcrt.locking</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">fd</span></code>, <code class="docutils literal notranslate"><span class="pre">mode</span></code>, <code class="docutils literal notranslate"><span class="pre">nbytes</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.LK_LOCK">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LK_LOCK</span></span><a class="headerlink" href="#msvcrt.LK_LOCK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="msvcrt.LK_RLCK">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LK_RLCK</span></span><a class="headerlink" href="#msvcrt.LK_RLCK" title="Link to this definition">¶</a></dt>
<dd><p>Locks the specified bytes. If the bytes cannot be locked, the program
immediately tries again after 1 second.  If, after 10 attempts, the bytes cannot
be locked, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.LK_NBLCK">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LK_NBLCK</span></span><a class="headerlink" href="#msvcrt.LK_NBLCK" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="msvcrt.LK_NBRLCK">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LK_NBRLCK</span></span><a class="headerlink" href="#msvcrt.LK_NBRLCK" title="Link to this definition">¶</a></dt>
<dd><p>Locks the specified bytes. If the bytes cannot be locked, <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> is
raised.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.LK_UNLCK">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LK_UNLCK</span></span><a class="headerlink" href="#msvcrt.LK_UNLCK" title="Link to this definition">¶</a></dt>
<dd><p>Unlocks the specified bytes, which must have been previously locked.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.setmode">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">setmode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.setmode" title="Link to this definition">¶</a></dt>
<dd><p>Set the line-end translation mode for the file descriptor <em>fd</em>. To set it to
text mode, <em>flags</em> should be <a class="reference internal" href="os.html#os.O_TEXT" title="os.O_TEXT"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.O_TEXT</span></code></a>; for binary, it should be
<a class="reference internal" href="os.html#os.O_BINARY" title="os.O_BINARY"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.O_BINARY</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.open_osfhandle">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">open_osfhandle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handle</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.open_osfhandle" title="Link to this definition">¶</a></dt>
<dd><p>Create a C runtime file descriptor from the file handle <em>handle</em>.  The <em>flags</em>
parameter should be a bitwise OR of <a class="reference internal" href="os.html#os.O_APPEND" title="os.O_APPEND"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.O_APPEND</span></code></a>, <a class="reference internal" href="os.html#os.O_RDONLY" title="os.O_RDONLY"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.O_RDONLY</span></code></a>,
and <a class="reference internal" href="os.html#os.O_TEXT" title="os.O_TEXT"><code class="xref py py-const docutils literal notranslate"><span class="pre">os.O_TEXT</span></code></a>.  The returned file descriptor may be used as a parameter
to <a class="reference internal" href="os.html#os.fdopen" title="os.fdopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fdopen()</span></code></a> to create a file object.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">msvcrt.open_osfhandle</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">handle</span></code>, <code class="docutils literal notranslate"><span class="pre">flags</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.get_osfhandle">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">get_osfhandle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.get_osfhandle" title="Link to this definition">¶</a></dt>
<dd><p>Return the file handle for the file descriptor <em>fd</em>.  Raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if
<em>fd</em> is not recognized.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">msvcrt.get_osfhandle</span></code> with argument <code class="docutils literal notranslate"><span class="pre">fd</span></code>.</p>
</dd></dl>

</section>
<section id="console-i-o">
<span id="msvcrt-console"></span><h2>Console I/O<a class="headerlink" href="#console-i-o" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.kbhit">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">kbhit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.kbhit" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if a keypress is waiting to be read.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.getch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">getch</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.getch" title="Link to this definition">¶</a></dt>
<dd><p>Read a keypress and return the resulting character as a byte string.
Nothing is echoed to the console.  This call will block if a keypress
is not already available, but will not wait for <kbd class="kbd docutils literal notranslate">Enter</kbd> to be
pressed. If the pressed key was a special function key, this will
return <code class="docutils literal notranslate"><span class="pre">'\000'</span></code> or <code class="docutils literal notranslate"><span class="pre">'\xe0'</span></code>; the next call will return the keycode.
The <kbd class="kbd compound docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Control</kbd>-<kbd class="kbd docutils literal notranslate">C</kbd></kbd> keypress cannot be read with this function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.getwch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">getwch</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.getwch" title="Link to this definition">¶</a></dt>
<dd><p>Wide char variant of <a class="reference internal" href="#msvcrt.getch" title="msvcrt.getch"><code class="xref py py-func docutils literal notranslate"><span class="pre">getch()</span></code></a>, returning a Unicode value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.getche">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">getche</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.getche" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#msvcrt.getch" title="msvcrt.getch"><code class="xref py py-func docutils literal notranslate"><span class="pre">getch()</span></code></a>, but the keypress will be echoed if it  represents a
printable character.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.getwche">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">getwche</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.getwche" title="Link to this definition">¶</a></dt>
<dd><p>Wide char variant of <a class="reference internal" href="#msvcrt.getche" title="msvcrt.getche"><code class="xref py py-func docutils literal notranslate"><span class="pre">getche()</span></code></a>, returning a Unicode value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.putch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">putch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">char</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.putch" title="Link to this definition">¶</a></dt>
<dd><p>Print the byte string <em>char</em> to the console without buffering.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.putwch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">putwch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unicode_char</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.putwch" title="Link to this definition">¶</a></dt>
<dd><p>Wide char variant of <a class="reference internal" href="#msvcrt.putch" title="msvcrt.putch"><code class="xref py py-func docutils literal notranslate"><span class="pre">putch()</span></code></a>, accepting a Unicode value.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.ungetch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">ungetch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">char</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.ungetch" title="Link to this definition">¶</a></dt>
<dd><p>Cause the byte string <em>char</em> to be “pushed back” into the console buffer;
it will be the next character read by <a class="reference internal" href="#msvcrt.getch" title="msvcrt.getch"><code class="xref py py-func docutils literal notranslate"><span class="pre">getch()</span></code></a> or <a class="reference internal" href="#msvcrt.getche" title="msvcrt.getche"><code class="xref py py-func docutils literal notranslate"><span class="pre">getche()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.ungetwch">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">ungetwch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">unicode_char</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.ungetwch" title="Link to this definition">¶</a></dt>
<dd><p>Wide char variant of <a class="reference internal" href="#msvcrt.ungetch" title="msvcrt.ungetch"><code class="xref py py-func docutils literal notranslate"><span class="pre">ungetch()</span></code></a>, accepting a Unicode value.</p>
</dd></dl>

</section>
<section id="other-functions">
<span id="msvcrt-other"></span><h2>Other Functions<a class="headerlink" href="#other-functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="msvcrt.heapmin">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">heapmin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#msvcrt.heapmin" title="Link to this definition">¶</a></dt>
<dd><p>Force the <code class="xref c c-func docutils literal notranslate"><span class="pre">malloc()</span></code> heap to clean itself up and return unused blocks to
the operating system. On failure, this raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.CRT_ASSEMBLY_VERSION">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">CRT_ASSEMBLY_VERSION</span></span><a class="headerlink" href="#msvcrt.CRT_ASSEMBLY_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>The CRT Assembly version, from the <code class="file docutils literal notranslate"><span class="pre">crtassem.h</span></code> header file.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.VC_ASSEMBLY_PUBLICKEYTOKEN">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">VC_ASSEMBLY_PUBLICKEYTOKEN</span></span><a class="headerlink" href="#msvcrt.VC_ASSEMBLY_PUBLICKEYTOKEN" title="Link to this definition">¶</a></dt>
<dd><p>The VC Assembly public key token, from the <code class="file docutils literal notranslate"><span class="pre">crtassem.h</span></code> header file.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="msvcrt.LIBRARIES_ASSEMBLY_NAME_PREFIX">
<span class="sig-prename descclassname"><span class="pre">msvcrt.</span></span><span class="sig-name descname"><span class="pre">LIBRARIES_ASSEMBLY_NAME_PREFIX</span></span><a class="headerlink" href="#msvcrt.LIBRARIES_ASSEMBLY_NAME_PREFIX" title="Link to this definition">¶</a></dt>
<dd><p>The Libraries Assembly name prefix, from the <code class="file docutils literal notranslate"><span class="pre">crtassem.h</span></code> header file.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a><ul>
<li><a class="reference internal" href="#file-operations">File Operations</a></li>
<li><a class="reference internal" href="#console-i-o">Console I/O</a></li>
<li><a class="reference internal" href="#other-functions">Other Functions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="windows.html"
                          title="previous chapter">MS Windows Specific Services</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="winreg.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">winreg</span></code> — Windows registry access</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/msvcrt.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="winreg.html" title="winreg — Windows registry access"
             >next</a> |</li>
        <li class="right" >
          <a href="windows.html" title="MS Windows Specific Services"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="windows.html" >MS Windows Specific Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">msvcrt</span></code> — Useful routines from the MS VC++ runtime</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>