<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.ProductCard">
        <article class="self_order_product_card d-flex flex-row-reverse flex-md-column align-items-start gap-2 user-select-none"
            role="button"
            t-att-title="props.product.name"
            t-on-click="() => this.selectProduct()"
            t-ref="selfProductCard">
            <div t-if="props.product.description_self_order" class="product-information-tag" t-on-click.prevent.stop="showProductInfo">
                <i class="product-information-tag-logo fa fa-info fs-4" role="img" aria-label="Product Information" title="Product Information" />
            </div>
            <div
                class="ratio ratio-1x1 w-25 w-sm-50 w-md-100"
                t-att-class="{
                    'd-md-block': !props.product.has_image
                }">
                <div class="placeholder-glow o_self_order_item_card_no_image">
                    <div t-attf-class="{{ props.product.has_image ? 'placeholder' : 'd-flex align-items-center justify-content-center h-100' }} bg-200 w-100 h-100 rounded">
                        <span t-if="!props.product.has_image" t-esc="props.product.name" class="text-center text-white fs-2 fw-bold mb-1 mb-sm-2"/>
                    </div>
                </div>
                <img
                    t-if="props.product.has_image"
                    class="o_self_order_item_card_image w-100 rounded"
                    t-attf-src="/menu/get-image/{{ props.product.id }}/512?unique={{props.product.write_date}}"
                    alt="Product image"
                    loading="lazy"
                    onerror="this.remove()"/>
            </div>
            <div class="product-infos d-flex flex-column justify-content-between text-start flex-grow-1 w-100 lh-1">
                <span t-esc="props.product.name" class="fs-4 fw-bold mb-1 mb-sm-2"/>
                <div class="d-flex justify-content-between align-items-end gap-3">
                    <span t-esc="selfOrder.formatMonetary(selfOrder.getProductDisplayPrice(props.product))" class="o-so-tabular-nums fs-4 text-muted flex-grow-1" />
                    <div class="text-center ms-2 fs-lighter">
                        <div t-if="!props.product.self_order_available" class="fs-lighter bg-secondary rounded">Out of stock</div>
                    </div>
                </div>
            </div>
        </article>
    </t>
</templates>
