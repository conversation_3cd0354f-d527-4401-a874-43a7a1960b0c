<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.maintenance</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="35"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form" />
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <app data-string="Maintenance" string="Maintenance" name="maintenance" groups="maintenance.group_equipment_manager">
                        <block title="Custom Worksheets">
                            <setting help="Create custom worksheet templates">
                                <field name="module_maintenance_worksheet" widget="upgrade_boolean"/>
                            </setting>
                        </block>
                    </app>
                </xpath>
            </field>
        </record>

        <record id="action_maintenance_configuration" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'maintenance', 'bin_size': False}</field>
        </record>

        <menuitem id="menu_maintenance_config" name="Settings" parent="menu_maintenance_configuration"
            sequence="0" action="action_maintenance_configuration" groups="base.group_system"/>

    </data>
</odoo>
