# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Manon <PERSON>dou, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_resource_calendar_leaves__timesheet_ids
msgid "Analytic Lines"
msgstr "Analytische boekingen"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#, python-format
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr ""
"Zowel het interne project als de taak zijn vereist om een urenstaat voor het"
" verlof %s te genereren. Als je geen urenstaat wilt, moet je het interne "
"project en de taak leeg laten."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "Werknemer"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheets"
msgstr "Genereer urenstaten"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Generate timesheets when validating time off requests of this type"
msgstr ""
"Genereer urenstaten bij het bevestigen van verlofverzoeken van dit type"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__global_leave_id
msgid "Global Time Off"
msgstr "Algemeen verlof"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Indien aangevinkt wordt er een urenstaat gegenereerd in het verlofproject "
"van het bedrijf wanneer de verlofaanvraag bevestigd wordt."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
#: code:addons/project_timesheet_holidays/__init__.py:0
#, python-format
msgid "Internal"
msgstr "Intern"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "Intern project"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_project_task__is_timeoff_task
msgid "Is Time off Task"
msgstr "Is een verloftaak"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/project_task.py:0
#, python-format
msgid "Operation not supported"
msgstr "Bewerking niet ondersteund"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Project"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Resource verlof detail"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_project_task
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Taak"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid ""
"The default project used when automatically generating timesheets via time "
"off requests. You can specify another project on each time off type "
"individually."
msgstr ""
"Het standaardproject dat wordt gebruikt bij het automatisch genereren van "
"urenstaten via verlofaanvragen. Je kan voor elk verlofsoort afzonderlijk een"
" ander project bepalen."

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid ""
"The default task used when automatically generating timesheets via time off "
"requests. You can specify another task on each time off type individually."
msgstr ""
"De standaardtaak die wordt gebruikt bij het automatisch genereren van "
"urenstaten via verlofaanvragen. Je kan voor elk verlofsoort afzonderlijk een"
" andere taak bepalen."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/__init__.py:0
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
#, python-format
msgid "Time Off"
msgstr "Verlof"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
#, python-format
msgid "Time Off (%s/%s)"
msgstr "Verlof (%s/%s)"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Time Off Request"
msgstr "Verlofaanvraag"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "Taak verlof"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "Soort verlof"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_project_task__leave_types_count
msgid "Time Off Types Count"
msgstr "Aantal verlofsoorten"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "Urenstaten"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid "View Time Off"
msgstr "Bekijk het verlof"

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot create timesheets for a task that is linked to a time off type. "
"Please use the Time Off application to request new time off instead."
msgstr ""
"Je kan geen urenstaten aanmaken voor een taak die aan een verlofsoort "
"gekoppeld is. Gebruik liever de Verlof applicatie om een nieuw verlof aan te"
" vragen."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheets linked to time off. Please, cancel the time off"
" instead."
msgstr ""
"Je kunt geen aan verlof gekoppelde urenstaten verwijderen. Annuleer in "
"plaats daarvan de verlof."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid "You cannot delete timesheets that are linked to global time off."
msgstr ""
"Je kunt geen timesheets verwijderen die gekoppeld zijn aan een algemene "
"verlofregistratie."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheets that are linked to time off requests. Please "
"cancel your time off request from the Time Off application instead."
msgstr ""
"Je kan urenstaten die aan verlofaanvragen gekoppeld zijn niet verwijderen. "
"Annuleer liever je verlofaanvraag in de Verlof applicatie."

#. module: project_timesheet_holidays
#. odoo-python
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot modify timesheets that are linked to time off requests. Please "
"use the Time Off application to modify your time off requests instead."
msgstr ""
"Je kunt urenstaten die aan verlofaanvragen gekoppeld zijn niet wijzigen. "
"Gebruik liever de Verlof applicatie om je verlofaanvragen te wijzigen."
