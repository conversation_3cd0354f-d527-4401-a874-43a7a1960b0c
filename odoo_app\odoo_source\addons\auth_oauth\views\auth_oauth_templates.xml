<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <template id="providers" name="OAuth Providers">
            <t t-if="len(providers) &gt; 0">
                <em t-attf-class="d-block text-center text-muted small my-#{len(providers) if len(providers) &lt; 3 else 3}">- or -</em>
                <div class="o_auth_oauth_providers list-group mt-1 mb-1 text-start">
                    <a t-foreach="providers" t-as="p" class="list-group-item list-group-item-action py-2" t-att-href="p['auth_link']">
                        <i t-att-class="p['css_class']"/>
                        <t t-esc="p['body']"/>
                    </a>
                </div>
            </t>
        </template>

        <template id="login" inherit_id="web.login" name="OAuth Login buttons">
            <xpath expr="//form" position="before">
                <t t-set="form_small" t-value="True" t-if="len(providers) &gt; 2"/>
            </xpath>
            <xpath expr="//div[hasclass('o_login_auth')]" position="inside">
                <t t-call="auth_oauth.providers"/>
            </xpath>
        </template>

        <template id="signup" inherit_id="auth_signup.signup" name="OAuth Signup buttons">
            <xpath expr="//form" position="before">
                <t t-set="form_small" t-value="True"/>
            </xpath>
            <xpath expr="//div[hasclass('o_login_auth')]" position="inside">
                <t t-call="auth_oauth.providers"/>
            </xpath>
        </template>

        <template id="reset_password" inherit_id="auth_signup.reset_password" name="OAuth Reset Password buttons">
            <xpath expr="//div[hasclass('o_login_auth')]" position="inside">
                <t t-call="auth_oauth.providers"/>
            </xpath>
        </template>
</odoo>
