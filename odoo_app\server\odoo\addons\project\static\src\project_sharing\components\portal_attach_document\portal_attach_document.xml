<templates id="template" xml:space="preserve">

    <t t-name="project.PortalAttachDocument">
        <button t-attf-class="btn o_attachment_button #{props.highlight ? 'btn-primary' : 'btn-secondary'}">
            <PortalFileInput
                onUpload="props.onUpload"
                beforeOpen="props.beforeOpen"
                multiUpload="props.multiUpload"
                resModel="props.resModel"
                resId="props.resId"
                route="props.route"
                accessToken="props.token"
            >
                <t t-set-slot="default">
                    <i class="fa fa-paperclip"/>
                </t>
            </PortalFileInput>
        </button>
    </t>

</templates>
