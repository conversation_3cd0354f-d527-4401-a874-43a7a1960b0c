# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Bo<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot display the payment form"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__code
msgid "Code"
msgstr "Šifra"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Connect Stripe"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Delivery"
msgstr "Otprema"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Enable Apple Pay"
msgstr ""

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Free Shipping"
msgstr "Besplatna dostava"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Generate your webhook"
msgstr ""

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_provider_form
msgid "Get your Secret and Publishable keys"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect payment details"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Other Payment Providers"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_provider
msgid "Payment Provider"
msgstr "Pružatelj usluge naplate"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_provider_onboarding
msgid "Payment Providers"
msgstr "Pružatelj usluge naplate"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Token plaćanja"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Please use live credentials to enable Apple Pay."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "Publishable Key"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_secret_key
msgid "Secret Key"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_provider__code__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_mandate
msgid "Stripe Mandate"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The customer left the payment page."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The refund did not go through. Please log into your Stripe Dashboard to get "
"more information on that matter, and address any accounting discrepancies."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_provider__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot set the provider to Test Mode while it is linked with your Stripe"
" account."
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr ""

#. module: payment_stripe
#. odoo-javascript
#: code:addons/payment_stripe/static/src/js/express_checkout_form.js:0
#, python-format
msgid "Your order"
msgstr ""

#. module: payment_stripe
#. odoo-python
#: code:addons/payment_stripe/models/payment_provider.py:0
#, python-format
msgid "Your web domain was successfully verified."
msgstr ""
