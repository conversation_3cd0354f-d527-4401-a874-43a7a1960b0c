<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.user_demo" model="res.users">
            <field name="groups_id" eval="[(3, ref('point_of_sale.group_pos_manager'))]"/>
        </record>

        <!-- Partners with Barcodes -->
        <record id='base.res_partner_1' model='res.partner'>
            <field name='barcode'>0420100000005</field>
        </record>
        <record id='base.res_partner_2' model='res.partner'>
            <field name='barcode'>0420200000004</field>
        </record>
        <record id='base.res_partner_3' model='res.partner'>
            <field name='barcode'>0420300000003</field>
        </record>
        <record id='base.res_partner_4' model='res.partner'>
            <field name='barcode'>0420700000009</field>
        </record>
        <record id='base.res_partner_10' model='res.partner'>
            <field name='barcode'>0421000000003</field>
        </record>
        <record id='base.res_partner_12' model='res.partner'>
            <field name='barcode'>0420800000008</field>
        </record>
        <record id='base.res_partner_18' model='res.partner'>
            <field name='barcode'>0421800000005</field>
        </record>

        <record id="base.user_root" model="res.users">
            <field name="barcode">0410100000006</field>
            <field name="groups_id" eval="[(4,ref('group_pos_manager'))]" />
        </record>

        <record id="base.user_demo" model="res.users">
            <field name="groups_id" eval="[(4, ref('group_pos_user'))]" />
        </record>


        <!-- Resource: pos.category -->
        <record id="pos_category_miscellaneous" model="pos.category">
          <field name="name">Misc</field>
          <field name="image_128" type="base64" file="point_of_sale/static/img/misc_category.png" />
        </record>
        <record id="pos_category_desks" model="pos.category">
          <field name="name">Desks</field>
          <field name="image_128" type="base64" file="point_of_sale/static/img/desk_category.png" />
        </record>
        <record id="pos_category_chairs" model="pos.category">
          <field name="name">Chairs</field>
          <field name="image_128" type="base64" file="point_of_sale/static/img/chair_category.png" />
        </record>

        <record model="pos.config" id="pos_config_main">
            <field name="iface_start_categ_id" ref="pos_category_desks" />
            <field name="start_category">True</field>
            <field name="limit_categories">True</field>
            <field name="iface_available_categ_ids"
                eval="[(6, 0, [ref('pos_category_miscellaneous'), ref('pos_category_desks'), ref('pos_category_chairs')])]" />
        </record>
        <function model="pos.config" name="add_cash_payment_method" />

        <!-- Preparation Printer -->
        <record id="preparation_printer" model="pos.printer">
            <field name="name">Preparation Printer</field>
            <field name="proxy_ip">localhost</field>
            <field name="product_categories_ids" eval="[(6, 0, [ref('point_of_sale.pos_category_miscellaneous')])]" />
            <field name="printer_type" eval="False" />
        </record>

        <!-- Taxes -->
        <record id="pos_taxe_group_0" model="account.tax.group">
            <field name="name">PoS Taxes</field>
        </record>

        <record id="pos_taxes_0" model="account.tax">
            <field name="name">Default Tax for PoS</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="tax_group_id" ref="pos_taxe_group_0"/>
        </record>

        <!-- Products -->

        <!-- Old -->

        <record id="stock.product_cable_management_box" model="product.product">
            <field name="pos_categ_ids" eval="[(6, 0, [ref('point_of_sale.pos_category_miscellaneous')])]" />
        </record>
        <record id="wall_shelf" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.98</field>
            <field name="name">Wall Shelf Unit</field>
            <field name="default_code">FURN_0009</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="barcode">*************</field>
            <field name="taxes_id" eval="[(5,)]" />
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/wall_shelf_unit.png" />
        </record>
        <record id="small_shelf" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.83</field>
            <field name="name">Small Shelf</field>
            <field name="default_code">FURN_0008</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="taxes_id" eval="[(5,)]" />
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="to_weight">True</field>
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/small_shelf.png" />
        </record>

        <record id="letter_tray" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">4.80</field>
            <field name="name">Letter Tray</field>
            <field name="default_code">FURN_0004</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/letter_tray.png" />
        </record>
        <record id="desk_organizer" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">5.10</field>
            <field name="name">Desk Organizer</field>
            <field name="description_sale">The desk organiser is perfect for storing all kinds of small things and since the 5 boxes are loose, you can move and place them in the way that suits you and your things best.</field>
            <field name="default_code">FURN_0001</field>
            <field name="to_weight">True</field>
            <field name="barcode">2300001000008</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/desk_organizer.png" />
            <field name="taxes_id" eval="[(5,)]" /> <!-- no taxes -->
        </record>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                      'xml_id': 'point_of_sale.desk_organizer_product_template',
                      'record': obj().env.ref('point_of_sale.desk_organizer').product_tmpl_id,
                      'noupdate': True,
                  }]" />
        </function>

        <record id="size_attribute" model="product.attribute">
            <field name="name">Size</field>
            <field name="sequence">30</field>
            <field name="display_type">radio</field>
            <field name="create_variant">no_variant</field>
        </record>
        <record id="size_attribute_s" model="product.attribute.value">
            <field name="name">S</field>
            <field name="sequence">1</field>
            <field name="attribute_id" ref="size_attribute" />
        </record>
        <record id="size_attribute_m" model="product.attribute.value">
            <field name="name">M</field>
            <field name="sequence">2</field>
            <field name="attribute_id" ref="size_attribute" />
        </record>
        <record id="size_attribute_l" model="product.attribute.value">
            <field name="name">L</field>
            <field name="sequence">3</field>
            <field name="attribute_id" ref="size_attribute" />
        </record>
        <record id="desk_organizer_size" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="point_of_sale.desk_organizer_product_template" />
            <field name="attribute_id" ref="size_attribute" />
            <field name="value_ids"
                eval="[(6, 0, [ref('size_attribute_s'), ref('size_attribute_m'), ref('size_attribute_l')])]" />
        </record>

        <record id="fabric_attribute" model="product.attribute">
            <field name="name">Fabric</field>
            <field name="sequence">40</field>
            <field name="display_type">select</field>
            <field name="create_variant">no_variant</field>
        </record>
        <record id="fabric_attribute_plastic" model="product.attribute.value">
            <field name="name">Plastic</field>
            <field name="sequence">1</field>
            <field name="attribute_id" ref="fabric_attribute" />
        </record>
        <record id="fabric_attribute_leather" model="product.attribute.value">
            <field name="name">Leather</field>
            <field name="sequence">2</field>
            <field name="attribute_id" ref="fabric_attribute" />
        </record>
        <record id="fabric_attribute_custom" model="product.attribute.value">
            <field name="name">Custom</field>
            <field name="sequence">3</field>
            <field name="attribute_id" ref="fabric_attribute" />
            <field name="is_custom">True</field>
        </record>
        <record id="desk_organizer_fabric" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="point_of_sale.desk_organizer_product_template" />
            <field name="attribute_id" ref="fabric_attribute" />
            <field name="value_ids"
                eval="[(6, 0, [ref('fabric_attribute_plastic'), ref('fabric_attribute_leather'), ref('fabric_attribute_custom')])]" />
        </record>

        <record id="magnetic_board" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.98</field>
            <field name="name">Magnetic Board</field>
            <field name="default_code">FURN_0005</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="barcode">2301000000006</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/magnetic_board.png" />
        </record>
        <record id="monitor_stand" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.19</field>
            <field name="name">Monitor Stand</field>
            <field name="default_code">FURN_0006</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/monitor_stand.png" />
        </record>
        <record id="desk_pad" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.98</field>
            <field name="name">Desk Pad</field>
            <field name="default_code">FURN_0002</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/desk_pad.png" />
        </record>

        <record id="whiteboard" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.70</field>
            <field name="name">Whiteboard</field>
            <field name="to_weight">True</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/whiteboard.png" />
        </record>

        <record id="led_lamp" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">0.90</field>
            <field name="name">LED Lamp</field>
            <field name="default_code">FURN_0003</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/led_lamp.png" />
        </record>

        <record id="newspaper_rack" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.28</field>
            <field name="name">Newspaper Rack</field>
            <field name="default_code">FURN_0007</field>
            <field name="type">product</field>
            <field name="weight">0.01</field>
            <field name="to_weight">True</field>
            <field name="barcode">2100001000004</field>
            <field name="categ_id" ref="product.product_category_5" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/newspaper_stand.png" />
        </record>

        <record id="whiteboard_pen" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">1.20</field>
            <field name="name">Whiteboard Pen</field>
            <field name="weight">0.01</field>
            <field name="default_code">CONS_0001</field>
            <field name="to_weight">True</field>
            <field name="categ_id" ref="product.product_category_consumable" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
            <field name="uom_id" ref="uom.product_uom_unit" />
            <field name="uom_po_id" ref="uom.product_uom_unit" />
            <field name="image_1920" type="base64" file="point_of_sale/static/img/whiteboard_pen.png" />
        </record>

        <record id="product.product_product_1" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_2" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_delivery_01" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]" />
        </record>
        <record id="product.product_delivery_02" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_order_01" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_3" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.product_product_4_product_template" model="product.template">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.product_product_5" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.product_product_6" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_7" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_8" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.product_product_9" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_10" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_11" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]" />
        </record>
        <record id="product.product_product_11b" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]" />
        </record>
        <record id="product.product_product_12" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_chairs')])]" />
        </record>
        <record id="product.product_product_13" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.product_product_16" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_20" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_22" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_24" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_25" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.product_product_27" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.consu_delivery_03" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_desks')])]" />
        </record>
        <record id="product.consu_delivery_02" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>
        <record id="product.consu_delivery_01" model="product.product">
            <field name="available_in_pos" eval="True" />
            <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous')])]" />
        </record>

        <record id="desk_organizer_combo_line" model="pos.combo.line">
            <field name="product_id" ref="desk_organizer"/>
            <field name="combo_price">0</field>
        </record>
        <record id="desk_pad_combo_line" model="pos.combo.line">
            <field name="product_id" ref="desk_pad"/>
            <field name="combo_price">0</field>
        </record>
        <record id="monitor_stand_combo_line" model="pos.combo.line">
            <field name="product_id" ref="monitor_stand"/>
            <field name="combo_price">2</field>
        </record>
        <record id="desk_accessories_combo" model="pos.combo">
            <field name="name">Desk Accessories Combo</field>
            <field name="combo_line_ids" eval="[(6, 0, [ref('desk_organizer_combo_line'), ref('desk_pad_combo_line'), ref('monitor_stand_combo_line')])]"/>
        </record>

        <record id="product_3_combo_line" model="pos.combo.line">
            <field name="product_id" ref="product.product_product_3"/>
            <field name="combo_price">0</field>
        </record>
        <record id="product_5_combo_line" model="pos.combo.line">
            <field name="product_id" ref="product.product_product_5"/>
            <field name="combo_price">0</field>
        </record>
        <record id="desks_combo" model="pos.combo">
            <field name="name">Desks Combo</field>
            <field name="combo_line_ids" eval="[(6, 0, [ref('product_3_combo_line'), ref('product_5_combo_line')])]"/>
        </record>

        <record id="product_11_combo_line" model="pos.combo.line">
            <field name="product_id" ref="product.product_product_11"/>
            <field name="combo_price">0</field>
        </record>
        <record id="product_11b_combo_line" model="pos.combo.line">
            <field name="product_id" ref="product.product_product_11b"/>
            <field name="combo_price">0</field>
        </record>
        <record id="product_12_combo_line" model="pos.combo.line">
            <field name="product_id" ref="product.product_product_12"/>
            <field name="combo_price">0</field>
        </record>
        <record id="chairs_combo" model="pos.combo">
            <field name="name">Chairs Combo</field>
            <field name="combo_line_ids" eval="[(6, 0, [ref('product_11_combo_line'), ref('product_11b_combo_line'), ref('product_12_combo_line')])]"/>
        </record> 

        <record id="office_combo" model="product.product">
          <field name="available_in_pos">True</field>
          <field name="list_price">160</field>
          <field name="name">Office combo</field>
          <field name="type">combo</field>
          <field name="categ_id" ref="product.product_category_5"/>
          <field name="uom_id" ref="uom.product_uom_unit"/>
          <field name="uom_po_id" ref="uom.product_uom_unit"/>
          <field name="image_1920" type="base64" file="point_of_sale/static/img/office_combo.jpg"/>
          <field name="combo_ids" eval="[(6, 0, [ref('desks_combo'), ref('chairs_combo'), ref('desk_accessories_combo')])]"/>
          <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_miscellaneous'), ref('pos_category_desks')])]"/>
          <field name="taxes_id" eval="[(5,)]"/>  <!-- no taxes -->
        </record>

        <!-- Closed Sessions -->
        <!-- forcecreate is set to false in order to not create record when updating the db -->

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                    'xml_id': 'point_of_sale.payment_method',
                    'record': obj().env.ref('point_of_sale.pos_config_main')._get_payment_method('cash'),
                    'noupdate': True,
                    }]" />
        </function>

        <!-- Closed Session 1 -->

        <record id="pos_closed_session_1" model="pos.session" forcecreate="False">
            <field name="config_id" ref="pos_config_main" />
            <field name="user_id" ref="base.user_admin" />
            <field name="start_at" eval="(DateTime.today() + relativedelta(days=-1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="stop_at"
                eval="(DateTime.today() + relativedelta(days=-1, hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
        </record>

        <record id="pos_closed_order_1_1" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_1" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0001</field>
            <field name="state">paid</field>
            <field name="amount_total">4.81</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">4.81</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-001-1001</field>
            <field name="partner_id" ref="base.res_partner_1" />
        </record>

        <record id="pos_closed_orderline_1_1_1" model="pos.order.line" forcecreate="False">
            <field name="product_id" ref="wall_shelf" />
            <field name="price_subtotal">1.98</field>
            <field name="price_subtotal_incl">1.98</field>
            <field name="price_unit">1.98</field>
            <field name="order_id" ref="pos_closed_order_1_1" />
            <field name="full_product_name">Wall Shelf</field>
        </record>

        <record id="pos_closed_orderline_1_1_2" model="pos.order.line" forcecreate="False">
            <field name="product_id" ref="small_shelf" />
            <field name="price_subtotal">2.83</field>
            <field name="price_subtotal_incl">2.83</field>
            <field name="price_unit">2.83</field>
            <field name="order_id" ref="pos_closed_order_1_1" />
            <field name="full_product_name">Small Shelf</field>
        </record>

        <record id="pos_payment_1" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="point_of_sale.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_1_1" />
            <field name="amount">4.81</field>
        </record>

        <record id="pos_closed_order_1_2" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_1" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0002</field>
            <field name="state">paid</field>
            <field name="amount_total">2220.50</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">2220.50</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-001-1002</field>
            <field name="partner_id" ref="base.res_partner_1" />
        </record>

        <record id="pos_closed_orderline_1_2_1" model="pos.order.line" forcecreate="False">
            <field name="product_id" ref="product.product_product_12" />
            <field name="price_subtotal">120.50</field>
            <field name="price_subtotal_incl">120.5</field>
            <field name="price_unit">120.50</field>
            <field name="order_id" ref="pos_closed_order_1_2" />
            <field name="full_product_name">Office Chair Black</field>
        </record>

        <record id="pos_closed_orderline_1_2_2" model="pos.order.line" forcecreate="False">
            <field name="product_id" ref="product.product_product_22" />
            <field name="price_subtotal">2100.0</field>
            <field name="price_subtotal_incl">2100.0</field>
            <field name="price_unit">2100.0</field>
            <field name="order_id" ref="pos_closed_order_1_2" />
            <field name="full_product_name">Desk Stand with Screen</field>
        </record>

        <record id="pos_payment_2" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="point_of_sale.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_1_2" />
            <field name="amount">2220.50</field>
        </record>

        <function model="pos.session" name="post_closing_cash_details" eval="[[ref('pos_closed_session_1')], 2225.31]" />

        <function model="pos.session" name="update_closing_control_state_session"
            eval="[[ref('pos_closed_session_1')], '']" />

        <function model="pos.session" name="action_pos_session_closing_control"
            eval="[[ref('pos_closed_session_1')]]" />

        <!-- Closed Session 2 -->

        <record id="pos_closed_session_2" model="pos.session" forcecreate="False">
            <field name="config_id" ref="pos_config_main" />
            <field name="user_id" ref="base.user_admin" />
            <field name="start_at" eval="(DateTime.today() + relativedelta(hours=-3)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="stop_at" eval="(DateTime.today() + relativedelta(hours=-2)).strftime('%Y-%m-%d %H:%M:%S')" />
        </record>

        <record id="pos_closed_order_2_1" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0003</field>
            <field name="state">paid</field>
            <field name="amount_total">9.90</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">9.90</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-002-1001</field>
            <field name="partner_id" ref="base.res_partner_12" />
        </record>

        <record id="pos_closed_orderline_2_1_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 2.1.1</field>
            <field name="product_id" ref="letter_tray" />
            <field name="price_subtotal">4.80</field>
            <field name="price_subtotal_incl">4.80</field>
            <field name="price_unit">4.80</field>
            <field name="order_id" ref="pos_closed_order_2_1" />
            <field name="full_product_name">Letter Tray</field>
        </record>

        <record id="pos_closed_orderline_2_1_2" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 2.1.2</field>
            <field name="product_id" ref="desk_organizer" />
            <field name="price_subtotal">5.10</field>
            <field name="price_subtotal_incl">5.10</field>
            <field name="price_unit">5.10</field>
            <field name="order_id" ref="pos_closed_order_2_1" />
            <field name="full_product_name">Desk Organizer</field>
        </record>

        <record id="pos_payment_3" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="point_of_sale.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_2_1" />
            <field name="amount">9.90</field>
        </record>

        <record id="pos_closed_order_2_2" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0004</field>
            <field name="state">paid</field>
            <field name="amount_total">8.36</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">8.36</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-002-1002</field>
            <field name="partner_id" ref="base.res_partner_12" />
        </record>

        <record id="pos_closed_orderline_2_2_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 2.2.1</field>
            <field name="product_id" ref="magnetic_board" />
            <field name="price_subtotal">1.98</field>
            <field name="price_subtotal_incl">1.98</field>
            <field name="price_unit">1.98</field>
            <field name="order_id" ref="pos_closed_order_2_2" />
            <field name="full_product_name">Magnetic Board</field>
        </record>

        <record id="pos_closed_orderline_2_2_2" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 2.1.2</field>
            <field name="product_id" ref="monitor_stand" />
            <field name="price_subtotal">6.38</field>
            <field name="price_subtotal_incl">6.38</field>
            <field name="qty">2</field>
            <field name="price_unit">3.19</field>
            <field name="order_id" ref="pos_closed_order_2_2" />
            <field name="full_product_name">Monitor Stand</field>
        </record>

        <record id="pos_payment_4" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="point_of_sale.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_2_2" />
            <field name="amount">8.36</field>
        </record>

        <function model="pos.session" name="post_closing_cash_details"
            eval="[[ref('pos_closed_session_2')], 2243.57]" />

        <function model="pos.session" name="update_closing_control_state_session"
            eval="[[ref('pos_closed_session_2')], '']" />

        <function model="pos.session" name="action_pos_session_closing_control"
            eval="[[ref('pos_closed_session_2')]]" />
    </data>
</odoo>
