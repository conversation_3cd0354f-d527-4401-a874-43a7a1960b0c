<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.CustomerFacingDisplayButton">
        <button class="menu-item navbar-button btn btn-light rounded-0 d-flex align-items-center px-3 cursor-pointer" t-on-click="() => {customerDisplay.connect(); this.displayMessage(message);}">
            <div t-if="customerDisplay.status === 'warning'" class="js_warning oe_icon oe_orange text-warning">
                <i class="fa fa-fw fa-desktop" role="img" t-attf-aria-label="Customer Screen Warning : {{message}}" t-attf-title="Customer Screen Warning : {{message}}"></i>
            </div>
            <div t-if="customerDisplay.status === 'failure'" class="js_disconnected oe_icon oe_red text-danger">
                <i class="fa fa-fw fa-desktop" role="img" t-attf-aria-label="Customer Screen : {{message}}" t-attf-title="Customer Screen : {{message}}"></i>
            </div>
            <div t-if="customerDisplay.status === 'not_found'" class="js_disconnected oe_icon oe_red text-danger">
                <i class="fa fa-fw fa-desktop" role="img" t-attf-aria-label="Customer Screen Not Found : {{message}}" t-attf-title="Customer Screen Not Found : {{message}}"></i>
            </div>
            <div t-if="customerDisplay.status === 'success'" class="js_connected oe_icon oe_green text-success">
                <i class="fa fa-fw fa-desktop" role="img" aria-label="Customer Screen Connected" title="Customer Screen Connected"></i>
            </div>
            <span class="ms-1">Customer Screen</span>
        </button>
    </t>

</templates>
