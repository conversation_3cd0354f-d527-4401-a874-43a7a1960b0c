<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="hr_employee_view_form_inherit_expense" model="ir.ui.view">
            <field name="name">hr.employee.view.form.expense</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='managers']" position="inside">
                    <field name="expense_manager_id" context="{'default_company_id': company_id}" widget="many2one_avatar_user"/>
                </xpath>
                 <xpath expr="//group[@name='managers']" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_employee_tree_inherit_expense" model="ir.ui.view">
            <field name="name">hr.employee.tree.expense</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='work_location_id']" position="after">
                    <field name="expense_manager_id" optional="hide" string="Expense Approver" widget="many2one_avatar_user"/>
                </xpath>
            </field>
        </record>

        <record id="res_users_view_form_preferences" model="ir.ui.view">
            <field name="name">hr.user.preferences.form.inherit.hr.expense</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="hr.res_users_view_form_profile" />
            <field name="arch" type="xml">
                <xpath expr="//group[@name='managers']" position="inside">
                    <field name="expense_manager_id" readonly="not can_edit" context="{'default_company_id': company_id}"/>
                </xpath>
                 <xpath expr="//group[@name='managers']" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
            </field>
        </record>

        <record id="hr_expense_view_expenses_analysis_tree" model="ir.ui.view">
            <field name="name">hr.expense.tree</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <tree string="Expenses" multi_edit="1" sample="1" js_class="hr_expense_tree" decoration-info="state == 'draft'">
                    <field name="is_editable" column_invisible="True"/>
                    <field name="company_id" column_invisible="True"/>
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="nb_attachment" column_invisible="True"/>
                    <field name="is_multiple_currency" column_invisible="True"/>
                    <field name="product_has_cost" column_invisible="True"/>
                    <field name="date" optional="show" readonly="not is_editable"/>
                    <field name="product_id" optional="hide" readonly="not is_editable"/>
                    <field name="name" readonly="not is_editable"/>
                    <field name="employee_id" widget="many2one_avatar_user" readonly="not is_editable"/>
                    <field name="sheet_id" optional="show" readonly="True" column_invisible="not context.get('show_report', False)"/>
                    <field name="payment_mode" optional="show" readonly="not is_editable"/>
                    <field name="activity_ids" widget="list_activity" optional="show"/>
                    <field name="accounting_date" optional="hide" groups="account.group_account_invoice,account.group_account_readonly"
                           readonly="not is_editable"/>
                    <field name="analytic_distribution" widget="analytic_distribution"
                           optional="show"
                           groups="analytic.group_analytic_accounting"
                           readonly="not is_editable"
                           options="{'product_field': 'product_id', 'business_domain': 'expense'}"/>
                    <field name="account_id" optional="hide" groups="account.group_account_readonly"
                           readonly="not is_editable"/>
                    <field name="company_id" optional="show" groups="base.group_multi_company" readonly="True"/>
                    <field name="price_unit" string="Unit Price" optional="hide" widget="monetary"
                           options="{'currency_field': 'company_currency_id', 'field_digits': True}" readonly="True"/>
                    <field name="quantity" optional="hide" readonly="not is_editable or not product_has_cost"/>
                    <field name="tax_ids" optional="hide" widget="many2many_tags"
                           groups="account.group_account_invoice,account.group_account_readonly"
                           readonly="not is_editable or not product_has_cost"/>
                    <field name="tax_amount" sum="Total Taxes" readonly="True"
                           optional="hide" groups="account.group_account_invoice,account.group_account_readonly"/>
                    <field name="nb_attachment" widget="nb_attachment" nolabel="1" readonly="True"/>
                    <field name="total_amount" sum="Total Amount" widget='monetary'
                           readonly="not is_editable or product_has_cost"
                           options="{'currency_field': 'company_currency_id'}" decoration-bf="1"/>
                    <field name="total_amount_currency" widget='monetary'
                           readonly="not is_editable or not is_multiple_currency or product_has_cost"
                           options="{'currency_field': 'currency_id'}" optional="hide" decoration-bf="1"
                           groups="base.group_multi_currency"/>
                    <field name="currency_id" optional="hide" readonly="True" groups="base.group_multi_currency"/>
                    <field name="state" optional="show" readonly="True" decoration-info="state in ['draft', 'reported']"
                           decoration-success="state in ['approved', 'done']"
                           decoration-warning="state == 'submitted'" decoration-danger="state == 'refused'" widget="badge"/>
                </tree>
            </field>
        </record>

        <record id="view_expenses_tree" model="ir.ui.view">
            <field name="name">hr.expense.tree</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense_view_expenses_analysis_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <!-- Display the tree dashboard view with the header -->
                    <attribute name="js_class">hr_expense_dashboard_tree</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_my_expenses_tree" model="ir.ui.view">
            <field name="name">hr.expense.tree</field>
            <field name="model">hr.expense</field>
            <field name="priority">20</field>
            <field name="inherit_id" ref="hr_expense.view_expenses_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <!-- Display the tree dashboard view with the header -->
                    <attribute name="js_class">hr_expense_dashboard_tree</attribute>
                </xpath>
            </field>
        </record>

        <record id="hr_expense_view_form" model="ir.ui.view">
            <field name="name">hr.expense.view.form</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <form string="Expenses" js_class="hr_expense_form_view">
                <header>
                  <button name="action_submit_expenses" string="Create Report" type="object"
                          class="oe_highlight o_expense_submit" invisible="nb_attachment &lt;= 0 or sheet_id" data-hotkey="v"/>
                  <button name="action_view_sheet" type="object" string="View Report" class="oe_highlight" invisible="not sheet_id or nb_attachment &lt; 1" data-hotkey="w"/>
                  <widget name="attach_document" string="Attach Receipt" action="attach_document" invisible="nb_attachment &lt; 1"/>
                  <widget name="attach_document" string="Attach Receipt" action="attach_document" highlight="1" invisible="nb_attachment &gt;= 1"/>
                  <button name="action_submit_expenses" string="Create Report" type="object" class="o_expense_submit"
                          invisible="nb_attachment &gt;= 1 or sheet_id" data-hotkey="v"/>
                  <field name="state" widget="statusbar" statusbar_visible="draft,reported,submitted,approved,done"
                         invisible="state == 'refused'"/>
                  <field name="state" widget="statusbar" statusbar_visible="draft,reported,submitted,refused"
                         invisible="state != 'refused'"/>
                  <button name="action_view_sheet" type="object" string="View Report" class="oe_highlight" invisible="not sheet_id or nb_attachment &gt;= 1" data-hotkey="w"/>
                  <button name="action_split_wizard" string="Split Expense" type="object" invisible="sheet_id or product_has_cost"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" placeholder="e.g. Lunch with Customer" readonly="not is_editable"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="sheet_id" invisible="1"/>
                            <field name="product_has_cost" invisible="1"/>
                            <field name="product_has_tax" invisible="1"/>
                            <field name="is_multiple_currency" invisible="1"/>
                            <field name="is_editable" invisible="1"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                            <field name="company_currency_id" invisible="1"/>
                            <field name="tax_amount" invisible="1"/>
                            <field name="price_unit" invisible="1"/>
                            <field name="nb_attachment" invisible="1"/>
                            <field name="total_amount" invisible="1"/>
                            <field name="duplicate_expense_ids" invisible="1"/>
                            <field name="currency_rate" invisible="1"/>
                            <field name="product_uom_category_id" invisible="1"/>

                            <label for="product_id"/>
                            <div>
                                <field name="product_id" required="1" readonly="not is_editable"
                                       context="{'default_detailed_type': 'service', 'default_can_be_expensed': 1, 'tree_view_ref': 'hr_expense.product_product_expense_tree_view', 'form_view_ref': 'hr_expense.product_product_expense_form_view'}"
                                       class="w-100"/>
                                <div class="fst-italic" invisible="not is_editable or not product_description or not product_id">
                                    <field name="product_description"/>
                                </div>
                            </div>

                            <!-- CASE: product has a cost defined -> user input qty (always in company currency) -->
                            <field name="price_unit" required="1" widget="monetary"
                                   options="{'currency_field': 'currency_id', 'field_digits': True}"
                                   invisible="not product_has_cost" readonly="True"/>
                            <label for="quantity" invisible="not product_has_cost"/>
                            <div invisible="not product_has_cost">
                                <div class="o_row">
                                    <field name="quantity" class="oe_inline" readonly="not is_editable"/>
                                    <field name="product_uom_id" required="1" force_save="1"
                                           options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"
                                           readonly="not is_editable"/>
                                </div>
                            </div>

                            <!-- CASE: product has no cost defined -> user input amount (in other currency if multi-currency) -->
                            <label for="total_amount_currency" string="Total" invisible="product_has_cost" readonly="not is_editable"/>
                            <div class="o_row" invisible="product_has_cost">
                                <field name="total_amount_currency" widget='monetary' options="{'currency_field': 'currency_id'}"
                                       readonly="not is_editable" class="oe_inline mw-50 me-0"/>
                                <field name="currency_id" class="mw-25 ms-0" groups="base.group_multi_currency"
                                       options="{'no_create': True}"
                                       readonly="not is_editable"/>
                            </div>

                            <!-- CASE: converter when currency is different than the company one -->
                            <label name="total_amount_product_cost_label" for="total_amount" string="Total" invisible="is_multiple_currency or not product_has_cost"/>
                            <label name="total_amount_multicurrency_label" for="total_amount" string="" invisible="not is_multiple_currency"/>
                            <div class="o_row" invisible="not is_multiple_currency and not product_has_cost">
                                <field name="total_amount" widget='monetary' options="{'currency_field': 'company_currency_id'}"
                                       force_save="1" readonly="not is_editable or product_has_cost" class="oe_inline"/>
                                <field name="label_currency_rate" class="ps-0"/>
                            </div>

                            <label for="tax_ids"/>
                            <div class="o_row">
                                <field name="tax_ids"
                                       force_save="1"
                                       widget="many2many_tags"
                                       readonly="not is_editable"
                                       options="{'no_create': True}"/>
                                <field name="tax_amount_currency"/>
                            </div>
                            <t groups="hr_expense.group_hr_expense_team_approver">
                                <field name="employee_id" groups="!hr.group_hr_user"
                                       context="{'default_company_id': company_id}" widget="many2one_avatar_employee"
                                       options="{'relation': 'hr.employee.public', 'no_create': True}"
                                       readonly="not is_editable"/>
                                <field name="employee_id" groups="hr.group_hr_user"
                                       context="{'default_company_id': company_id}" widget="many2one_avatar_employee"
                                       options="{'relation': 'hr.employee', 'no_create': True}"
                                   readonly="not is_editable"/>
                            </t>
                            <label id="lo" for="payment_mode"/>
                            <div id="payment_mode">
                                <field name="payment_mode" widget="radio" readonly="sheet_id"/>
                            </div>
                        </group>
                        <group>
                            <field name="date" readonly="not is_editable"/>
                            <field name="accounting_date"
                                   invisible="not accounting_date or state not in ['approved', 'done']"
                                   readonly="not is_editable"/>
                            <field name="account_id" options="{'no_create': True}"
                                   domain="[('account_type', 'not in', ('asset_receivable','liability_payable','asset_cash','liability_credit_card')), ('deprecated', '=', False)]"
                                   groups="account.group_account_readonly" readonly="not is_editable"
                                   context="{'default_company_id': company_id}"/>
                            <field name="analytic_distribution" widget="analytic_distribution"
                                groups="analytic.group_analytic_accounting"
                                options="{'product_field': 'product_id', 'account_field': 'account_id', 'business_domain': 'expense'}"
                                readonly="not is_editable"/>
                            <field name="company_id" groups="base.group_multi_company" readonly="state != 'draft'"/>
                        </group>
                    </group>
                    <div>
                        <field name="description" placeholder="Notes..." readonly="not is_editable"/>
                    </div>
                </sheet>
                <div class="o_attachment_preview o_center_attachment"/>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

        <record id="hr_expense_view_form_without_header" model="ir.ui.view">
            <field name="name">hr.expense.view.form</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense.hr_expense_view_form"/>
            <field eval="35" name="priority"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="/form/header" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <field name="employee_id" position="attributes">
                    <attribute name="readonly">1</attribute>
                </field>
                <field name="company_id" position="attributes">
                    <attribute name="readonly">1</attribute>
                </field>
            </field>
        </record>

        <record id="hr_expense_view_expenses_analysis_kanban" model="ir.ui.view">
            <field name="name">hr.expense.kanban</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile hr_expense" sample="1" js_class="hr_expense_kanban" quick_create="false">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="total_amount_currency"/>
                    <field name="date"/>
                    <field name="state"/>
                    <field name="activity_state"/>
                    <field name="currency_id"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-12">
                                        <strong class="o_kanban_record_title"><span><t t-out="record.name.value"/></span></strong>
                                        <strong class="o_kanban_record_subtitle float-end"><span class="text-end">
                                            <field name="total_amount_currency" widget="monetary"/></span>
                                        </strong>
                                    </div>
                                </div>
                                <div class="row mt8">
                                    <div class="col-6 text-muted">
                                        <field name="employee_id" widget="many2one_avatar_user"
                                               options="{'display_avatar_name': True}"
                                               readonly="True"/><br/>
                                        <t t-out="record.date.value"/>
                                    </div>
                                    <div class="col-6">
                                        <span class="float-end text-end">
                                            <field name="state" widget="label_selection"
                                                   options="{'classes': {'draft': 'default', 'reported': 'primary',
                                                                         'submitted': 'warning', 'refused': 'danger',
                                                                         'done': 'warning', 'approved': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Kanban view without header -->
        <record id="hr_expense_kanban_view" model="ir.ui.view">
            <field name="name">hr.expense.kanban</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense_view_expenses_analysis_kanban"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="js_class">hr_expense_kanban</attribute>
                </xpath>
            </field>
        </record>

        <!-- Kanban view with header. Used in "My Expenses -->
        <record id="hr_expense_kanban_view_header" model="ir.ui.view">
            <field name="name">hr.expense.kanban</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense_view_expenses_analysis_kanban"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="js_class">hr_expense_dashboard_kanban</attribute>
                </xpath>
            </field>
        </record>

        <record id="hr_expense_view_pivot" model="ir.ui.view">
            <field name="name">hr.expense.pivot</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <pivot string="Expenses Analysis" sample="1">
                    <field name="employee_id" type="row"/>
                    <field name="date" interval="month" type="col"/>
                    <field name="total_amount_currency" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="hr_expense_view_graph" model="ir.ui.view">
            <field name="name">hr.expense.graph</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <graph string="Expenses Analysis" sample="1">
                    <field name="price_unit" invisible="1"/>
                    <field name="quantity" invisible="1"/>
                    <field name="date"/>
                    <field name="employee_id"/>
                    <field name="total_amount" type="measure" />
                    <field name="tax_amount"/>
                </graph>
            </field>
        </record>

        <record id="hr_expense_view_search" model="ir.ui.view">
            <field name="name">hr.expense.view.search</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <search string="Expense">
                    <field string="Expense" name="name"
                           filter_domain="['|', '|', ('employee_id', 'ilike', self), ('name', 'ilike', self), ('product_id', 'ilike', self)]"/>
                    <field name="date"/>
                    <field name="employee_id"/>
                    <filter string="My Expenses" name="my_expenses" domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="My Team" name="my_team_expenses" domain="[('employee_id.parent_id.user_id', '=', uid)]"
                            groups="hr_expense.group_hr_expense_team_approver" help="Expenses of Your Team Member"/>
                    <separator />
                    <filter string="To Report" name="no_report" domain="[('sheet_id', '=', False)]"/>
                    <filter string="Refused" name="refused" domain="[('state', '=', 'refused')]" help="Refused Expenses"/>
                    <separator />
                    <filter string="Expense Date" name="date" date="date"/>
                    <separator />
                    <filter string="Former Employees" name="inactive" domain="[('employee_id.active', '=', False)]"
                            groups="hr_expense.group_hr_expense_user,hr_expense.group_hr_expense_manager"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                        <filter string="Category" name="product" domain="[]" context="{'group_by': 'product_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Expense Date" name="expensesmonth" domain="[]" context="{'group_by': 'date'}"
                                help="Expense Date"/>
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"
                                groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="hr_expense_view_activity" model="ir.ui.view">
            <field name="name">hr.expense.activity</field>
            <field name="model">hr.expense</field>
            <field name="arch" type="xml">
                <activity string="Expenses">
                    <field name="employee_id"/>
                    <field name="currency_id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img class="rounded"
                                 t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)"
                                 t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                            <div class="ms-2">
                                <field name="name" display="full" class="o_text_block"/>
                                <field name="total_amount_currency" widget="monetary" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="hr_expense_actions_all" model="ir.actions.act_window">
            <field name="name">Expenses Analysis</field>
            <field name="res_model">hr.expense</field>
            <field name="view_mode">graph,pivot,tree,form</field>
            <field name="search_view_id" ref="hr_expense_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_empty_folder">
                    No data yet!
                </p><p>
                    Create new expenses to get statistics.
                </p>
            </field>
        </record>

        <record id="hr_expense_actions_all_graph" model="ir.actions.act_window.view">
            <field name="view_mode">graph</field>
            <field name="view_id" ref="hr_expense.hr_expense_view_graph"/>
            <field name="act_window_id" ref="hr_expense_actions_all"/>
        </record>

        <record id="hr_expense_actions_all_pivot" model="ir.actions.act_window.view">
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="hr_expense.hr_expense_view_pivot"/>
            <field name="act_window_id" ref="hr_expense_actions_all"/>
        </record>

        <record id="hr_expense_actions_all_tree" model="ir.actions.act_window.view">
            <field name="view_mode">tree</field>
            <field name="view_id" ref="hr_expense_view_expenses_analysis_tree"/>
            <field name="act_window_id" ref="hr_expense_actions_all"/>
        </record>

        <record id="hr_expense_actions_my_all" model="ir.actions.act_window">
            <field name="name">My Expenses</field>
            <field name="res_model">hr.expense</field>
            <field name="view_mode">tree,kanban,form,graph,pivot,activity</field>
            <field name="search_view_id" ref="hr_expense_view_search"/>
            <field name="context">{'search_default_my_expenses': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_expense_receipt">
                    <h2 class="d-none d-md-block">
                        Drag and drop files to create expenses
                    </h2>
                    <p class="d-none d-md-block">
                        Or
                    </p>
                    <h2 class="d-none d-md-block">
                        Did you try the mobile app?
                    </h2>
                </p>
                <p>Snap pictures of your receipts and let Odoo<br/> automatically create expenses for you.</p>
                <p class="d-none d-md-block">
                    <a href="https://apps.apple.com/be/app/odoo/id1272543640" target="_blank" class="o_expense_mobile_app">
                        <img alt="Apple App Store" class="img img-fluid h-100 o_expense_apple_store"
                             src="/hr_expense/static/img/app_store.png"/>
                    </a>
                    <a href="https://play.google.com/store/apps/details?id=com.odoo.mobile" target="_blank"
                       class="o_expense_mobile_app">
                        <img alt="Google Play Store" class="img img-fluid h-100 o_expense_google_store"
                             src="/hr_expense/static/img/play_store.png"/>
                    </a>
                </p>
            </field>
        </record>

        <!-- Tree & Kanban view for "All My Expenses" with header -->
        <record id="hr_expense_actions_my_all_tree" model="ir.actions.act_window.view">
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_my_expenses_tree"/>
            <field name="act_window_id" ref="hr_expense_actions_my_all"/>
        </record>

        <record id="hr_expense_actions_my_all_kanban" model="ir.actions.act_window.view">
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="hr_expense_kanban_view_header"/>
            <field name="act_window_id" ref="hr_expense_actions_my_all"/>
        </record>

        <record id="view_product_hr_expense_form" model="ir.ui.view">
            <field name="name">product.template.expense.form</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <div name="options" position="inside">
                    <span class="d-inline-flex">
                        <field name="can_be_expensed"/>
                        <label for="can_be_expensed"/>
                    </span>
                </div>
            </field>
        </record>

        <record id="product_template_search_view_inherit_hr_expense" model="ir.ui.view">
            <field name="name">product.template.search.view.inherit.hr_expense</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <filter name="filter_to_purchase" position="after">
                    <filter string="Can be Expensed" name="filter_to_expense" domain="[('can_be_expensed', '=', True)]"/>
                </filter>
            </field>
        </record>

        <record id="product_product_expense_form_view" model="ir.ui.view">
            <field name="name">product.product.expense.form</field>
            <field name="model">product.product</field>
            <field name="arch" type="xml">
                <form string="Expense Categories">
                    <div class="alert alert-warning" role="alert" invisible="not standard_price_update_warning">
                        <field name="standard_price_update_warning"/>
                    </div>
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <field name='product_variant_count' invisible='1'/>
                        <field name="id" invisible="1"/>
                        <field name="image_1920" widget="image" class="oe_avatar" options="{'image_preview': 'image_128'}"/>
                        <field name="detailed_type" invisible="1"/>
                        <div class="oe_title">
                            <label for="name" string="Product Name"/>
                            <h1><field name="name" placeholder="e.g. Lunch"/></h1>
                            <div name="options" groups="base.group_user" invisible="1">
                                <div>
                                    <field name="can_be_expensed"/>
                                    <label for="can_be_expensed"/>
                                </div>
                            </div>
                        </div>
                        <group name="product_details">
                            <group string="General Information">
                                <field name="active" invisible="1"/>
                                <field name="type" invisible="1"/>
                                <field name="standard_price" class="w-25"
                                       help="When the cost of an expense product is different than 0, then the user
                                        using this product won't be able to change the amount of the expense,
                                        only the quantity. Use a cost different than 0 for expense categories funded by
                                        the company at fixed cost like allowances for mileage, per diem, accommodation
                                        or meal."/>
                                <field name="uom_id" class="w-25" groups="uom.group_uom" options="{'no_create': True}"/>
                                <field name="uom_po_id" invisible="1"/>
                                <label for="default_code"/>
                                <div>
                                    <field name="default_code" class="w-50"/>
                                    <span class="d-inline-block">
                                        <i class="text-muted">Use this reference as a subject prefix when submitting by email.</i>
                                    </span>
                                </div>
                                <field name="categ_id" class="w-50"/>
                                <field name="company_id" class="w-50" groups="base.group_multi_company"/>
                            </group>
                            <group string="Accounting">
                                <field name="property_account_expense_id" class="w-50" groups="account.group_account_readonly"/>
                                <field name="supplier_taxes_id" class="w-50" widget="many2many_tags"
                                       context="{'default_type_tax_use':'purchase', 'default_price_include': 1}"
                                       options="{'no_quick_create': True}"/>
                            </group>
                        </group>
                        <field name="description" class="mt-5"
                               placeholder="This note will be shown to users when they select this expense product."/>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="product_product_expense_kanban_view" model="ir.ui.view">
            <field name="name">product.product.kanban.expense</field>
            <field name="inherit_id" ref="product.product_kanban_view"/>
            <field name="mode">primary</field>
            <field name="model">product.product</field>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='product_lst_price']" position="after">
                    <div name="product_standard_price" class="mt-1">
                        Cost: <field name="standard_price"/>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="product_product_expense_tree_view" model="ir.ui.view">
            <field name="name">product.product.expense.tree</field>
            <field name="model">product.product</field>
            <field eval="50" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Product Variants">
                    <field name="default_code"/>
                    <field name="name"/>
                    <field name="product_template_attribute_value_ids" widget="many2many_tags"
                           groups="product.group_product_variant"/>
                    <field name="standard_price"/>
                    <field name="uom_id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                    <field name="barcode"/>
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="product_product_expense_categories_tree_view">
            <field name="name">product.product.expense.categories.tree.view</field>
            <field name="model">product.product</field>
            <field name="arch" type="xml">
                <tree class="o_expense_categories">
                    <field name="name" readonly="1"/>
                    <field name="default_code" optional="show" readonly="1"/>
                    <field name="description" widget="html" string="Internal Note" optional="show" readonly="1"/>
                    <field name="lst_price" optional="show" string="Sales Price"/>
                    <field name="standard_price" optional="show"/>
                    <field name="supplier_taxes_id" widget="many2many_tags" optional="show"/>
                </tree>
            </field>
        </record>

        <record id="hr_expense_product" model="ir.actions.act_window">
            <field name="name">Expense Categories</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="product.product_search_form_view"/>
            <field name="context">{"default_can_be_expensed": 1, 'default_detailed_type': 'service'}</field>
            <field name="domain">[('can_be_expensed', '=', True)]</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No expense categories found. Let's create one!
              </p><p>
                Expense categories can be reinvoiced to your customers.
              </p>
            </field>
        </record>

        <record id="hr_expense_product_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="product_product_expense_categories_tree_view"/>
            <field name="act_window_id" ref="hr_expense_product"/>
        </record>

        <record id="hr_expense_product_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="product_product_expense_kanban_view"/>
            <field name="act_window_id" ref="hr_expense_product"/>
        </record>

        <record id="hr_expense_product_form" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="product_product_expense_form_view"/>
            <field name="act_window_id" ref="hr_expense_product"/>
        </record>

        <record id="view_hr_expense_sheet_tree" model="ir.ui.view">
            <field name="name">hr.expense.sheet.tree</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <tree string="Expense Reports" multi_edit="1" js_class="hr_expense_tree" sample="1" decoration-info="state == 'draft'">
                    <field name="product_ids" column_invisible="True"/>
                    <field name="currency_id" column_invisible="True"/>
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="is_editable" column_invisible="True"/>
                    <field name="employee_id" widget="many2one_avatar_user" readonly="state != 'draft'"/>
                    <field name="accounting_date" optional="hide" groups="account.group_account_manager" readonly="not is_editable"/>
                    <field name="create_date" optional="hide"/>
                    <field name="name" string="Expense Report" readonly="not is_editable"/>
                    <field name="payment_mode" optional="hide"/>
                    <field name="user_id" optional="hide" widget="many2one_avatar_user" readonly="state != 'draft'"/>
                    <field name="company_id" optional="show" groups="base.group_multi_company" readonly="state != 'draft'"/>
                    <field name="activity_ids" widget="list_activity" optional="show" readonly="True"/>
                    <field name="journal_id" optional="hide" readonly="not is_editable"/>
                    <field name="total_amount" sum="Total Amount" decoration-bf="1" widget="monetary"/>
                    <field name="state" optional="show"
                           decoration-info="state == 'draft'"
                           decoration-success="state in ['approve', 'post', 'done']"
                           decoration-warning="state == 'submit'"
                           decoration-danger="state == 'cancel'"
                           widget="badge"/>
                    <field name="payment_state" optional="show"
                           decoration-info="payment_state in ('partial','in_payment')"
                           decoration-success="payment_state == 'paid'"
                           decoration-danger="payment_state in ('reversed','not_paid')"
                           widget="badge" invisible="state in ['draft', 'submit', 'cancel']"/>
                </tree>
            </field>
        </record>

        <!-- Tree view for "My Reports" with header -->
        <record id="view_hr_expense_sheet_dashboard_tree_header" model="ir.ui.view">
            <field name="name">hr.expense.sheet.dashboard.tree</field>
            <field name="model">hr.expense.sheet</field>
            <field name="priority">20</field>
            <field name="inherit_id" ref="hr_expense.view_hr_expense_sheet_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="js_class">hr_expense_dashboard_tree</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_hr_expense_sheet_form" model="ir.ui.view">
            <field name="name">hr.expense.sheet.form</field>
            <field name="model">hr.expense.sheet</field>
            <field eval="25" name="priority"/>
            <field name="arch" type="xml">
                <form string="Expense Reports">
                <field name="can_reset" invisible="1"/>
                <field name="can_approve" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <field name="payment_state" invisible="1"/>
                <field name="is_editable" invisible="1"/>
                <field name="currency_id" invisible="1"/>
                <field name="company_currency_id" invisible="1"/>
                <header>
                    <button name="action_submit_sheet"
                            string="Submit to Manager"
                            invisible="state != 'draft'"
                            type="object"
                            class="oe_highlight o_expense_sheet_submit"
                            data-hotkey="l"/>
                    <button name="action_approve_expense_sheets"
                            string="Approve"
                            type="object"
                            data-hotkey="q"
                            context="{'validate_analytic': True}"
                            invisible="not can_approve or state != 'submit'"
                            class="oe_highlight o_expense_sheet_approve"/>
                    <button name="action_sheet_move_create"
                            string="Post Journal Entries"
                            type="object"
                            data-hotkey="y"
                            class="oe_highlight o_expense_sheet_post"
                            invisible="state != 'approve'"
                            groups="account.group_account_invoice"/>
                    <button name="action_register_payment"
                            string="Register Payment"
                            type="object"
                            data-hotkey="w"
                            class="oe_highlight o_expense_sheet_pay"
                            context="{'dont_redirect_to_payments': True}"
                            invisible="payment_mode == 'company_account' or state not in ('post', 'done') or payment_state in ('paid', 'in_payment')"
                            groups="account.group_account_invoice"/>
                    <button name="action_refuse_expense_sheets"
                            string="Refuse"
                            invisible="state not in ('submit', 'approve')"
                            type="object"
                            groups="hr_expense.group_hr_expense_team_approver"
                            data-hotkey="x"/>
                    <button name="action_reset_approval_expense_sheets"
                            string="Reset to Draft"
                            type="object"
                            invisible="not can_reset or state not in ('submit', 'cancel', 'approve')"
                            data-hotkey="k"/>
                    <button name="action_reset_expense_sheets"
                            string="Reset to Draft"
                            type="object"
                            data-hotkey="c"
                            invisible="state != 'post'"
                            groups="account.group_account_readonly,account.group_account_invoice"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submit,approve,post,done"
                           force_save="1" invisible="state == 'cancel'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submit,cancel"
                           force_save="1" invisible="state != 'cancel'"/>

                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_account_moves"
                            class="oe_stat_button"
                            icon="fa-file-text-o"
                            type="object"
                            invisible="state not in ['post', 'done'] or nb_account_move == 0"
                            groups="account.group_account_invoice">
                            <div class="o_stat_info">
                                <field name="nb_account_move" class="o_stat_value"/>
                                <span class="o_stat_text">Journal Entry</span>
                            </div>
                        </button>
                        <button name="action_open_expense_view"
                            class="oe_stat_button"
                            icon="fa-file-text-o"
                            type="object"
                            invisible="nb_expense == 0">
                            <field name="nb_expense" widget="statinfo" string="Expenses"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Paid" bg_color="text-bg-success" invisible="payment_state != 'paid'"/>
                    <widget name="web_ribbon" title="Partial" bg_color="text-bg-info" invisible="payment_state != 'partial'"/>
                    <widget name="web_ribbon" title="In Payment" invisible="payment_state != 'in_payment'"/>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="e.g. Trip to NY" readonly="not is_editable" force_save="1"/>
                        </h1>
                    </div>
                    <group>
                        <group name="employee_details">
                            <field name="employee_id" context="{'default_company_id': company_id}"
                                   widget="many2one_avatar_user" readonly="state != 'draft'"/>
                            <field name="payment_mode"/>
                            <field name="employee_journal_id"
                                   groups="account.group_account_invoice,account.group_account_readonly"
                                   options="{'no_open': True, 'no_create': True}"
                                   invisible="payment_mode != 'own_account'" readonly="not is_editable"
                                   context="{'default_company_id': company_id}"/>
                            <field name="selectable_payment_method_line_ids" invisible="1"/>
                            <field name="payment_method_line_id"
                                   context="{'show_payment_journal_id': 1}"
                                   options="{'no_open': True, 'no_create': True}"
                                   invisible="payment_mode != 'company_account'"
                                   readonly="not is_editable"
                                   required="payment_mode == 'company_account'"/>
                            <field name="department_id" invisible="1" readonly="not is_editable"
                                   context="{'default_company_id': company_id}"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" readonly="state != 'draft'"/>
                            <field name="user_id" widget="many2one_avatar_user" readonly="state != 'draft'"/>
                            <field name="accounting_date"
                                   groups="account.group_account_invoice,account.group_account_readonly"
                                   invisible="state not in ['approve', 'post', 'done']"
                                   readonly="not is_editable"/>
                        </group>
                    </group>
                     <notebook>
                        <page name="expenses" string="Expense">
                            <field name="expense_line_ids"
                                    nolabel="1"
                                    widget="expense_lines_widget"
                                    mode="tree,kanban"
                                    domain="[
                                        ('sheet_id', '=', False),
                                        ('employee_id', '=', employee_id),
                                        ('company_id', '=', company_id),
                                        ('payment_mode', '=?', payment_mode),
                                    ]"
                                    options="{'reload_on_button': True}"
                                    context="{
                                        'form_view_ref' : 'hr_expense.hr_expense_view_form_without_header',
                                        'default_company_id': company_id,
                                        'default_employee_id': employee_id,
                                        'default_payment_mode': payment_mode or 'own_account',
                                    }"
                                    readonly="not is_editable"
                                    force_save="1">
                                <tree editable="bottom">
                                    <field name="employee_id" column_invisible="True"/>
                                    <field name="state" column_invisible="True"/>
                                    <field name="nb_attachment" column_invisible="True"/>
                                    <field name="message_main_attachment_id" column_invisible="True"/>
                                    <field name="company_id" column_invisible="True"/>
                                    <field name="company_currency_id" column_invisible="True"/>
                                    <field name="is_multiple_currency" column_invisible="True"/>
                                    <field name="product_has_cost" column_invisible="True"/>
                                    <field name="date" optional="show"/>
                                    <field name="product_id"/>
                                    <field name="name"/>
                                    <field name="description" optional="hide"/>
                                    <button name="action_get_attachment_view" type="object" icon="fa-paperclip"
                                            aria-label="View Attachments" title="View Attachments" class="float-end pe-0"
                                            readonly="True" invisible="nb_attachment == 0"/>
                                    <field name="analytic_distribution" widget="analytic_distribution"
                                           groups="analytic.group_analytic_accounting"
                                           optional="show"
                                           options="{'product_field': 'product_id', 'account_field': 'account_id', 'business_domain': 'expense'}"/>
                                    <field name="account_id" optional="hide" groups="account.group_account_readonly"/>
                                    <field name="price_unit" optional="hide" widget="monetary" options="{'currency_field': 'company_currency_id', 'field_digits': True}" readonly="True"/>
                                    <field name="currency_id" optional="hide" readonly="True" groups="base.group_multi_currency"/>
                                    <field name="quantity" optional="hide" readonly="True"/>
                                    <field name="tax_ids" string="Taxes" optional="show" widget="many2many_tags"
                                           context="{'default_company_id': company_id}" readonly="True"/>
                                    <field name="tax_amount_currency" optional="hide"  options="{'currency_field': 'currency_id'}"
                                           context="{'default_company_id': company_id}" readonly="True" groups="base.group_multi_currency"/>
                                    <field name="tax_amount" optional="hide" readonly="True"/>
                                    <field name="total_amount_currency" options="{'currency_field': 'currency_id'}"
                                           string="Subtotal In Currency " optional="show"
                                           readonly="true" groups="base.group_multi_currency"/>
                                    <field name="total_amount" string="Subtotal" readonly="True"/>
                                </tree>
                            </field>
                            <group class="oe_subtotal_footer" colspan="2" name="expense_total">
                                <field name="untaxed_amount"/>
                                <div class="oe_inline o_td_label">
                                    <label for="total_tax_amount"/>
                                </div>
                                <field name="total_tax_amount" nolabel="1"/>
                                <div class="oe_inline o_td_label">
                                    <label for="total_amount"/>
                                </div>
                                <field name="total_amount" nolabel="1" class="oe_subtotal_footer_separator"/>
                                <field name="amount_residual"
                                    class="oe_subtotal_footer_separator"
                                    invisible="state not in ('post', 'done')"/>
                            </group>
                        </page>
                     </notebook>
                </sheet>
                <div class="o_attachment_preview o_center_attachment"/>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

        <record id="view_hr_expense_sheet_kanban" model="ir.ui.view">
            <field name="name">hr.expense.sheet.kanban</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="total_amount"/>
                    <field name="accounting_date"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-12">
                                        <strong class="o_kanban_record_title"><span><t t-out="record.name.value"/></span></strong>
                                        <strong class="o_kanban_record_subtitle float-end">
                                            <span class="text-end"><field name="total_amount" widget="monetary"/></span>
                                        </strong>
                                    </div>
                                </div>
                                <div class="row mt8">
                                    <div class="col-6 text-muted">
                                        <field name="employee_id" widget="many2one_avatar_user"  options="{'display_avatar_name': True}" readonly="state != 'draft'"/><t t-out="record.accounting_date.value"/>
                                    </div>
                                    <div class="col-6">
                                        <span class="float-end text-end">
                                            <field name="state" widget="label_selection"
                                                   options="{'classes': {'draft': 'default', 'submit': 'default',
                                                                         'cancel': 'danger', 'post': 'warning',
                                                                         'done': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Kanban view without header -->
        <record id="view_hr_expense_sheet_kanban_no_header" model="ir.ui.view">
            <field name="name">hr.expense.sheet.kanban</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="view_hr_expense_sheet_kanban"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="js_class">hr_expense_kanban</attribute>
                </xpath>
            </field>
        </record>

        <!-- Kanban view with header -->
        <record id="view_hr_expense_sheet_kanban_header" model="ir.ui.view">
            <field name="name">hr.expense.sheet.kanban</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="view_hr_expense_sheet_kanban"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="js_class">hr_expense_dashboard_kanban</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_hr_expense_sheet_pivot" model="ir.ui.view">
            <field name="name">hr.expense.sheet.pivot</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <pivot string="Expenses Analysis" sample="1">
                    <field name="employee_id" type="row"/>
                    <field name="accounting_date" interval="month" type="col"/>
                    <field name="total_amount" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="view_hr_expense_sheet_graph" model="ir.ui.view">
            <field name="name">hr.expense.sheet.graph</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <graph string="Expenses Analysis" sample="1">
                    <field name="employee_id"/>
                    <field name="accounting_date" interval="month"/>
                    <field name="total_amount" type="measure"/>
                </graph>
            </field>
        </record>


        <record id="hr_expense_sheet_view_search" model="ir.ui.view">
            <field name="name">hr.expense.sheet.view.search</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <search string="Expense">
                    <field string="Expense Report" name="name"/>
                    <field name="accounting_date"/>
                    <separator />
                    <field name="employee_id"/>
                    <field string="Department" name="department_id" operator="child_of"/>
                    <field string="Journal" name="journal_id"/>
                    <filter string="My Reports" name="my_reports" domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="My Team" name="my_team_reports"
                            domain="[('employee_id.parent_id.user_id', '=', uid)]"
                            groups="hr_expense.group_hr_expense_manager" help="Expenses of Your Team Member"/>
                    <separator invisible="1"/>
                    <filter string="Not Refused" name="not_refused_reports"
                            domain="[('employee_id.user_id', '=', uid), ('state', '!=', 'cancel')]" invisible="1"/>
                    <separator />
                    <filter string="Date" name="filter_accounting_date" date="accounting_date"/>
                    <separator/>
                    <filter domain="[('employee_id.active', '=', False)]" string="Former Employees"
                            name="inactive" groups="hr_expense.group_hr_expense_user,hr_expense.group_hr_expense_manager"/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                            domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                            ]"/>
                    <group expand="0" string="Group By" name="group_filters">
                        <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                        <filter string="Department" name="department" domain="[]" context="{'group_by': 'department_id'}"/>
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"
                                groups="base.group_multi_company"/>
                        <filter string="Date" name="expenses_month" domain="[]" context="{'group_by': 'accounting_date'}"
                                help="Expenses by Date"/>
                        <filter string="Status" domain="[]" context="{'group_by': 'state'}" name="state"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="hr_expense_sheet_view_search_with_panel" model="ir.ui.view">
            <field name="name">hr.expense.sheet.view.search.with.panel</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="hr_expense_sheet_view_search"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='group_filters']" position='after'>
                    <searchpanel>
                        <field name="state" expand="1" select="multi" icon="fa-check-square-o" enable_counters="1"/>
                        <field name="employee_id"  limit="20" hierarchize="0" select="one" icon="fa-users"/>
                        <field name="company_id" expand="1" icon="fa-building" groups="base.group_multi_company"/>
                    </searchpanel>
                </xpath>
            </field>
        </record>

        <record id="hr_expense_sheet_view_activity" model="ir.ui.view">
            <field name="name">hr.expense.sheet.activity</field>
            <field name="model">hr.expense.sheet</field>
            <field name="arch" type="xml">
                <activity string="Expenses">
                    <field name="employee_id"/>
                    <field name="currency_id"/>
                    <templates>
                        <div t-name="activity-box">
                            <img class="rounded"
                                 t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)"
                                 t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                            <div class="ms-2">
                                <field name="name" display="full" class="o_text_block"/>
                                <field name="total_amount" widget="monetary" muted="1" display="full"/>
                                <field name="state" display="right"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="action_hr_expense_sheet_my_all" model="ir.actions.act_window">
            <field name="name">My Reports</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">tree,kanban,form,pivot,graph,activity</field>
            <field name="search_view_id" ref="hr_expense_sheet_view_search"/>
            <field name="context">{'search_default_my_reports': 1, 'search_default_not_refused_reports': 1}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No expense report found. Let's create one!
              </p><p>
                Once you have created your expense, submit it to your manager who will validate it.
              </p>
            </field>
        </record>

        <!-- Tree & Kanban view for "My Reports" with header -->
        <record id="action_hr_expense_sheet_my_all_tree" model="ir.actions.act_window.view">
            <field name="view_mode">tree</field>
            <field name="view_id" ref="view_hr_expense_sheet_dashboard_tree_header"/>
            <field name="act_window_id" ref="action_hr_expense_sheet_my_all"/>
        </record>

        <record id="action_hr_expense_sheet_my_all_kanban" model="ir.actions.act_window.view">
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="view_hr_expense_sheet_kanban_header"/>
            <field name="act_window_id" ref="action_hr_expense_sheet_my_all"/>
        </record>

        <record id="action_hr_expense_sheet_all" model="ir.actions.act_window">
            <field name="name">All Reports</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">tree,kanban,form,pivot,graph</field>
            <field name="search_view_id" ref="hr_expense_sheet_view_search_with_panel"/>
            <field name="domain">[]</field>
            <field name="context">{ 'searchpanel_default_state': ["draft", "submit", "approve", "post", "done"] }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No expense reports found. Let's create one!
                </p><p>
                    Expense reports regroup all the expenses incurred during a specific event.
                </p>
            </field>
        </record>

        <record id="action_hr_expense_account" model="ir.actions.act_window">
            <field name="name">Employee Expenses</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">tree,kanban,form,pivot,graph</field>
            <field name="search_view_id" ref="hr_expense_sheet_view_search"/>
            <field name="view_id" ref="view_hr_expense_sheet_tree"/>
            <field name="domain">[]</field>
            <field name="context">{
                'search_default_approved': 1,
                'search_default_to_post': 1,
            }
            </field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new expense report
              </p><p>
                Once you have created your expense, submit it to your manager who will validate it.
              </p>
            </field>
        </record>

        <record id="action_hr_expense_sheet_all_all" model="ir.actions.act_window">
            <field name="name">All Expense Reports</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">graph,pivot,tree,kanban,form</field>
            <field name="search_view_id" ref="hr_expense_sheet_view_search"/>
            <field name="domain">[]</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new expense report
              </p><p>
                Once you have created your expense, submit it to your manager who will validate it.
              </p>
            </field>
        </record>

        <record id="action_hr_expense_sheet_department_to_approve" model="ir.actions.act_window">
            <field name="name">Expense Reports to Approve</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">tree,kanban,form,pivot,graph</field>
            <field name="search_view_id" ref="hr_expense_sheet_view_search_with_panel"/>
            <field name="context">{ 'searchpanel_default_state': ["submit"] }</field>
        </record>

        <record id="action_hr_expense_sheet_department_filtered" model="ir.actions.act_window">
            <field name="name">Expense Reports Analysis</field>
            <field name="res_model">hr.expense.sheet</field>
            <field name="view_mode">graph,pivot</field>
            <field name="context">{
                'search_default_department_id': [active_id],
                'default_department_id': active_id}
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p>
            </field>
        </record>

        <menuitem id="menu_hr_expense_root" name="Expenses" sequence="230" web_icon="hr_expense,static/description/icon.png"/>

        <menuitem id="menu_hr_expense_my_expenses" name="My Expenses" sequence="1" parent="menu_hr_expense_root" groups="base.group_user"/>
        <menuitem id="menu_hr_expense_my_expenses_all" sequence="1" parent="menu_hr_expense_my_expenses"
                  action="hr_expense_actions_my_all" name="My Expenses"/>
        <menuitem id="menu_hr_expense_sheet_my_reports" sequence="2" parent="menu_hr_expense_my_expenses"
                  action="action_hr_expense_sheet_my_all" name="My Reports"/>

        <menuitem id="menu_hr_expense_report" name="Expense Reports" sequence="2" parent="menu_hr_expense_root"
                   action="action_hr_expense_sheet_all"
                   groups="account.group_account_user,hr_expense.group_hr_expense_team_approver"/>

        <menuitem id="menu_hr_expense_reports" name="Reporting" sequence="4" parent="menu_hr_expense_root"
                  groups="hr_expense.group_hr_expense_manager"/>
        <menuitem id="menu_hr_expense_all_expenses" name="Expenses Analysis" sequence="0"
                  parent="menu_hr_expense_reports" action="hr_expense_actions_all"/>

        <menuitem id="menu_hr_expense_configuration" name="Configuration" parent="menu_hr_expense_root"
            sequence="100"/>
        <menuitem id="menu_hr_product" name="Expense Categories" parent="menu_hr_expense_configuration"
            action="hr_expense_product" groups="hr_expense.group_hr_expense_manager" sequence="10"/>

        <menuitem id="menu_hr_expense_account_employee_expenses" name="Employee Expenses" sequence="22"
                  parent="account.menu_finance_payables" groups="hr_expense.group_hr_expense_user"
                  action="action_hr_expense_account"/>
</odoo>
