<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.SelectionPopup">
        <div class="popup popup-selection">
            <div class="modal-header drag-handle">
                <h4 class="modal-title"><t t-esc="props.title" /></h4>
            </div>
            <div class="selection btn-group-vertical justify-content-start w-100 p-3 overflow-y-auto">
                <t t-foreach="props.list" t-as="item" t-key="item.id">
                    <button type="button" class="selection-item d-flex align-items-center justify-content-between btn btn-lg btn-outline-secondary w-100 p-3 text-start" t-att-class="{ 'selected active': item.isSelected }"
                            t-on-click="() => this.selectItem(item.id)">
                        <div class="d-flex flex-column">
                            <span t-esc="item.label" />
                            <span t-esc="item.description" t-if="item.description" />
                        </div>
                        <i class="oi oi-chevron-right"/>
                    </button>
                </t>
            </div>
            <footer class="footer modal-footer">
                <div class="button cancel btn btn-lg btn-primary" t-on-click="cancel">
                    <t t-esc="props.cancelText" />
                </div>
            </footer>
        </div>
    </t>

</templates>
