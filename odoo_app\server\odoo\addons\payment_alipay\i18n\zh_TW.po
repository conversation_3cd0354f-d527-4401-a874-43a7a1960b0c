# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_alipay
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_provider__alipay_payment_method
msgid "Account"
msgstr "帳戶"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_provider__code__alipay
#: model:payment.provider,name:payment_alipay.payment_provider_alipay
msgid "Alipay"
msgstr "支付寶"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_provider__alipay_seller_email
msgid "Alipay Seller Email"
msgstr "支付寶賣家電子郵件"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_provider__code
msgid "Code"
msgstr "程式碼"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_provider__alipay_payment_method__standard_checkout
msgid "Cross-border"
msgstr "跨境"

#. module: payment_alipay
#: model:ir.model.fields.selection,name:payment_alipay.selection__payment_provider__alipay_payment_method__express_checkout
msgid "Express Checkout (only for Chinese merchants)"
msgstr "快速結賬（只適用於中國商戶）"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_provider__alipay_md5_signature_key
msgid "MD5 Signature Key"
msgstr "MD5 簽名金鑰"

#. module: payment_alipay
#: model:ir.model.fields,field_description:payment_alipay.field_payment_provider__alipay_merchant_partner_id
msgid "Merchant Partner ID"
msgstr "商戶合作夥伴 ID"

#. module: payment_alipay
#. odoo-python
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_provider
msgid "Payment Provider"
msgstr "付款服務商"

#. module: payment_alipay
#: model:ir.model,name:payment_alipay.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_alipay
#. odoo-python
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference %(r)s or txn_id %(t)s."
msgstr "收到的數據中缺漏參考編號 %(r)s 或 txn_id %(t)s。"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_provider__alipay_seller_email
msgid "The public Alipay partner email"
msgstr "公開的支付寶合作夥伴電子郵件"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_provider__alipay_merchant_partner_id
msgid "The public partner ID solely used to identify the account with Alipay"
msgstr "只用於識別支付寶賬戶的公開合作夥伴識別碼"

#. module: payment_alipay
#: model:ir.model.fields,help:payment_alipay.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "此付款服務商的技術代碼。"

#. module: payment_alipay
#: model_terms:ir.ui.view,arch_db:payment_alipay.payment_provider_form
msgid ""
"This provider is deprecated.\n"
"                    Consider disabling it and moving to <strong>Asiapay</strong>."
msgstr ""
"此服務商已被棄用。\n"
"                    請考慮將它設為停用，並轉用 <strong>Asiapay</strong>。"

#. module: payment_alipay
#: model_terms:payment.provider,auth_msg:payment_alipay.payment_provider_alipay
msgid "Your payment has been authorized."
msgstr "您的付款已獲授權。"

#. module: payment_alipay
#: model_terms:payment.provider,cancel_msg:payment_alipay.payment_provider_alipay
msgid "Your payment has been cancelled."
msgstr "您的付款已被取消。"

#. module: payment_alipay
#: model_terms:payment.provider,pending_msg:payment_alipay.payment_provider_alipay
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "您的付款已成功處理，但正在等待批准。"

#. module: payment_alipay
#: model_terms:payment.provider,done_msg:payment_alipay.payment_provider_alipay
msgid "Your payment has been successfully processed."
msgstr "你的付款已成功處理。"

#. module: payment_alipay
#. odoo-python
#: code:addons/payment_alipay/models/payment_transaction.py:0
#, python-format
msgid "received invalid transaction status: %s"
msgstr "收到的無效交易狀態：%s"
