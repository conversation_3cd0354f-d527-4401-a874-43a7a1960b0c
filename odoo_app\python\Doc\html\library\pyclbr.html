<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pyclbr — Python module browser support" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pyclbr.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pyclbr.py The pyclbr module provides limited information about the functions, classes, and methods defined in a Python-coded module. The information is sufficient to implement a mo..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pyclbr.py The pyclbr module provides limited information about the functions, classes, and methods defined in a Python-coded module. The information is sufficient to implement a mo..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pyclbr — Python module browser support &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="py_compile — Compile Python source files" href="py_compile.html" />
    <link rel="prev" title="tabnanny — Detection of ambiguous indentation" href="tabnanny.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pyclbr.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a><ul>
<li><a class="reference internal" href="#function-objects">Function Objects</a></li>
<li><a class="reference internal" href="#class-objects">Class Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tabnanny.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tabnanny</span></code> — Detection of ambiguous indentation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="py_compile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pyclbr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="py_compile.html" title="py_compile — Compile Python source files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tabnanny.html" title="tabnanny — Detection of ambiguous indentation"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" accesskey="U">Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pyclbr">
<span id="pyclbr-python-module-browser-support"></span><h1><a class="reference internal" href="#module-pyclbr" title="pyclbr: Supports information extraction for a Python module browser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code></a> — Python module browser support<a class="headerlink" href="#module-pyclbr" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pyclbr.py">Lib/pyclbr.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-pyclbr" title="pyclbr: Supports information extraction for a Python module browser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code></a> module provides limited information about the
functions, classes, and methods defined in a Python-coded module.  The
information is sufficient to implement a module browser.  The
information is extracted from the Python source code rather than by
importing the module, so this module is safe to use with untrusted code.
This restriction makes it impossible to use this module with modules not
implemented in Python, including all standard and optional extension
modules.</p>
<dl class="py function">
<dt class="sig sig-object py" id="pyclbr.readmodule">
<span class="sig-prename descclassname"><span class="pre">pyclbr.</span></span><span class="sig-name descname"><span class="pre">readmodule</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pyclbr.readmodule" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary mapping module-level class names to class
descriptors.  If possible, descriptors for imported base classes are
included.  Parameter <em>module</em> is a string with the name of the module
to read; it may be the name of a module within a package.  If given,
<em>path</em> is a sequence of directory paths prepended to <code class="docutils literal notranslate"><span class="pre">sys.path</span></code>,
which is used to locate the module source code.</p>
<p>This function is the original interface and is only kept for back
compatibility.  It returns a filtered version of the following.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pyclbr.readmodule_ex">
<span class="sig-prename descclassname"><span class="pre">pyclbr.</span></span><span class="sig-name descname"><span class="pre">readmodule_ex</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pyclbr.readmodule_ex" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary-based tree containing a function or class
descriptors for each function and class defined in the module with a
<code class="docutils literal notranslate"><span class="pre">def</span></code> or <code class="docutils literal notranslate"><span class="pre">class</span></code> statement.  The returned dictionary maps
module-level function and class names to their descriptors.  Nested
objects are entered into the children dictionary of their parent.  As
with readmodule, <em>module</em> names the module to be read and <em>path</em> is
prepended to sys.path.  If the module being read is a package, the
returned dictionary has a key <code class="docutils literal notranslate"><span class="pre">'__path__'</span></code> whose value is a list
containing the package search path.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>Descriptors for nested definitions.  They are accessed through the
new children attribute.  Each has a new parent attribute.</p>
</div>
<p>The descriptors returned by these functions are instances of
Function and Class classes.  Users are not expected to create instances
of these classes.</p>
<section id="function-objects">
<span id="pyclbr-function-objects"></span><h2>Function Objects<a class="headerlink" href="#function-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="pyclbr.Function">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pyclbr.</span></span><span class="sig-name descname"><span class="pre">Function</span></span><a class="headerlink" href="#pyclbr.Function" title="Link to this definition">¶</a></dt>
<dd><p>Class <code class="xref py py-class docutils literal notranslate"><span class="pre">Function</span></code> instances describe functions defined by def
statements.  They have the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.file">
<span class="sig-name descname"><span class="pre">file</span></span><a class="headerlink" href="#pyclbr.Function.file" title="Link to this definition">¶</a></dt>
<dd><p>Name of the file in which the function is defined.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.module">
<span class="sig-name descname"><span class="pre">module</span></span><a class="headerlink" href="#pyclbr.Function.module" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module defining the function described.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#pyclbr.Function.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the function.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#pyclbr.Function.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line number in the file where the definition starts.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#pyclbr.Function.parent" title="Link to this definition">¶</a></dt>
<dd><p>For top-level functions, <code class="docutils literal notranslate"><span class="pre">None</span></code>.  For nested functions, the parent.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.children">
<span class="sig-name descname"><span class="pre">children</span></span><a class="headerlink" href="#pyclbr.Function.children" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dictionary</span></code></a> mapping names to descriptors for nested functions and
classes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Function.is_async">
<span class="sig-name descname"><span class="pre">is_async</span></span><a class="headerlink" href="#pyclbr.Function.is_async" title="Link to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">True</span></code> for functions that are defined with the
<a class="reference internal" href="../reference/compound_stmts.html#async-def"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">async</span></code></a> prefix, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="class-objects">
<span id="pyclbr-class-objects"></span><h2>Class Objects<a class="headerlink" href="#class-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="pyclbr.Class">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pyclbr.</span></span><span class="sig-name descname"><span class="pre">Class</span></span><a class="headerlink" href="#pyclbr.Class" title="Link to this definition">¶</a></dt>
<dd><p>Class <code class="xref py py-class docutils literal notranslate"><span class="pre">Class</span></code> instances describe classes defined by class
statements.  They have the same attributes as <a class="reference internal" href="#pyclbr.Function" title="pyclbr.Function"><code class="xref py py-class docutils literal notranslate"><span class="pre">Functions</span></code></a>
and two more.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.file">
<span class="sig-name descname"><span class="pre">file</span></span><a class="headerlink" href="#pyclbr.Class.file" title="Link to this definition">¶</a></dt>
<dd><p>Name of the file in which the class is defined.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.module">
<span class="sig-name descname"><span class="pre">module</span></span><a class="headerlink" href="#pyclbr.Class.module" title="Link to this definition">¶</a></dt>
<dd><p>The name of the module defining the class described.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#pyclbr.Class.name" title="Link to this definition">¶</a></dt>
<dd><p>The name of the class.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.lineno">
<span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#pyclbr.Class.lineno" title="Link to this definition">¶</a></dt>
<dd><p>The line number in the file where the definition starts.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.parent">
<span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#pyclbr.Class.parent" title="Link to this definition">¶</a></dt>
<dd><p>For top-level classes, None.  For nested classes, the parent.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.children">
<span class="sig-name descname"><span class="pre">children</span></span><a class="headerlink" href="#pyclbr.Class.children" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping names to descriptors for nested functions and
classes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.super">
<span class="sig-name descname"><span class="pre">super</span></span><a class="headerlink" href="#pyclbr.Class.super" title="Link to this definition">¶</a></dt>
<dd><p>A list of <code class="xref py py-class docutils literal notranslate"><span class="pre">Class</span></code> objects which describe the immediate base
classes of the class being described.  Classes which are named as
superclasses but which are not discoverable by <a class="reference internal" href="#pyclbr.readmodule_ex" title="pyclbr.readmodule_ex"><code class="xref py py-func docutils literal notranslate"><span class="pre">readmodule_ex()</span></code></a>
are listed as a string with the class name instead of as
<code class="xref py py-class docutils literal notranslate"><span class="pre">Class</span></code> objects.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pyclbr.Class.methods">
<span class="sig-name descname"><span class="pre">methods</span></span><a class="headerlink" href="#pyclbr.Class.methods" title="Link to this definition">¶</a></dt>
<dd><p>A <a class="reference internal" href="stdtypes.html#dict" title="dict"><code class="xref py py-class docutils literal notranslate"><span class="pre">dictionary</span></code></a> mapping method names to line numbers.
This can be derived from the newer <a class="reference internal" href="#pyclbr.Class.children" title="pyclbr.Class.children"><code class="xref py py-attr docutils literal notranslate"><span class="pre">children</span></code></a> dictionary,
but remains for
back-compatibility.</p>
</dd></dl>

</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a><ul>
<li><a class="reference internal" href="#function-objects">Function Objects</a></li>
<li><a class="reference internal" href="#class-objects">Class Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="tabnanny.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tabnanny</span></code> — Detection of ambiguous indentation</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="py_compile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pyclbr.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="py_compile.html" title="py_compile — Compile Python source files"
             >next</a> |</li>
        <li class="right" >
          <a href="tabnanny.html" title="tabnanny — Detection of ambiguous indentation"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" >Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>