<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="time — Time access and conversions" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/time.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="This module provides various time-related functions. For related functionality, see also the datetime and calendar modules. Although this module is always available, not all functions are available..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="This module provides various time-related functions. For related functionality, see also the datetime and calendar modules. Although this module is always available, not all functions are available..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>time — Time access and conversions &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="argparse — Parser for command-line options, arguments and sub-commands" href="argparse.html" />
    <link rel="prev" title="io — Core tools for working with streams" href="io.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/time.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#clock-id-constants">Clock ID Constants</a></li>
<li><a class="reference internal" href="#timezone-constants">Timezone Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="io.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="argparse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">argparse</span></code> — Parser for command-line options, arguments and sub-commands</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/time.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="argparse.html" title="argparse — Parser for command-line options, arguments and sub-commands"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="io.html" title="io — Core tools for working with streams"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" accesskey="U">Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-time">
<span id="time-time-access-and-conversions"></span><h1><a class="reference internal" href="#module-time" title="time: Time access and conversions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code></a> — Time access and conversions<a class="headerlink" href="#module-time" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<p>This module provides various time-related functions. For related
functionality, see also the <a class="reference internal" href="datetime.html#module-datetime" title="datetime: Basic date and time types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a> and <a class="reference internal" href="calendar.html#module-calendar" title="calendar: Functions for working with calendars, including some emulation of the Unix cal program."><code class="xref py py-mod docutils literal notranslate"><span class="pre">calendar</span></code></a> modules.</p>
<p>Although this module is always available,
not all functions are available on all platforms.  Most of the functions
defined in this module call platform C library functions with the same name.  It
may sometimes be helpful to consult the platform documentation, because the
semantics of these functions varies among platforms.</p>
<p>An explanation of some terminology and conventions is in order.</p>
<span id="epoch"></span><ul class="simple" id="index-0">
<li><p>The <em class="dfn">epoch</em> is the point where the time starts, the return value of
<code class="docutils literal notranslate"><span class="pre">time.gmtime(0)</span></code>. It is January 1, 1970, 00:00:00 (UTC) on all platforms.</p></li>
</ul>
<ul class="simple" id="index-1">
<li><p>The term <em class="dfn">seconds since the epoch</em> refers to the total number
of elapsed seconds since the epoch, typically excluding
<a class="reference external" href="https://en.wikipedia.org/wiki/Leap_second">leap seconds</a>.  Leap seconds are excluded from this total on all
POSIX-compliant platforms.</p></li>
</ul>
<ul class="simple" id="index-2">
<li><p>The functions in this module may not handle dates and times before the <a class="reference internal" href="#epoch">epoch</a> or
far in the future.  The cut-off point in the future is determined by the C
library; for 32-bit systems, it is typically in 2038.</p></li>
</ul>
<ul class="simple" id="index-3">
<li><p>Function <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a> can parse 2-digit years when given <code class="docutils literal notranslate"><span class="pre">%y</span></code> format
code. When 2-digit years are parsed, they are converted according to the POSIX
and ISO C standards: values 69–99 are mapped to 1969–1999, and values 0–68
are mapped to 2000–2068.</p></li>
</ul>
<ul class="simple" id="index-4">
<li><p>UTC is Coordinated Universal Time (formerly known as Greenwich Mean Time, or
GMT).  The acronym UTC is not a mistake but a compromise between English and
French.</p></li>
</ul>
<ul id="index-5">
<li><p>DST is Daylight Saving Time, an adjustment of the timezone by (usually) one
hour during part of the year.  DST rules are magic (determined by local law) and
can change from year to year.  The C library has a table containing the local
rules (often it is read from a system file for flexibility) and is the only
source of True Wisdom in this respect.</p></li>
<li><p>The precision of the various real-time functions may be less than suggested by
the units in which their value or argument is expressed. E.g. on most Unix
systems, the clock “ticks” only 50 or 100 times a second.</p></li>
<li><p>On the other hand, the precision of <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> and <a class="reference internal" href="#time.sleep" title="time.sleep"><code class="xref py py-func docutils literal notranslate"><span class="pre">sleep()</span></code></a> is better
than their Unix equivalents: times are expressed as floating point numbers,
<a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> returns the most accurate time available (using Unix
<code class="xref c c-func docutils literal notranslate"><span class="pre">gettimeofday()</span></code> where available), and <a class="reference internal" href="#time.sleep" title="time.sleep"><code class="xref py py-func docutils literal notranslate"><span class="pre">sleep()</span></code></a> will accept a time
with a nonzero fraction (Unix <code class="xref c c-func docutils literal notranslate"><span class="pre">select()</span></code> is used to implement this, where
available).</p></li>
<li><p>The time value as returned by <a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a>, <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>, and
<a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a>, and accepted by <a class="reference internal" href="#time.asctime" title="time.asctime"><code class="xref py py-func docutils literal notranslate"><span class="pre">asctime()</span></code></a>, <a class="reference internal" href="#time.mktime" title="time.mktime"><code class="xref py py-func docutils literal notranslate"><span class="pre">mktime()</span></code></a> and
<a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a>, is a sequence of 9 integers.  The return values of
<a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a>, <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>, and <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a> also offer attribute
names for individual fields.</p>
<p>See <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> for a description of these objects.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>The <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> type was extended to provide
the <a class="reference internal" href="#time.struct_time.tm_gmtoff" title="time.struct_time.tm_gmtoff"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_gmtoff</span></code></a> and <a class="reference internal" href="#time.struct_time.tm_zone" title="time.struct_time.tm_zone"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_zone</span></code></a>
attributes when platform supports corresponding
<code class="docutils literal notranslate"><span class="pre">struct</span> <span class="pre">tm</span></code> members.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>The <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> attributes
<a class="reference internal" href="#time.struct_time.tm_gmtoff" title="time.struct_time.tm_gmtoff"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_gmtoff</span></code></a> and <a class="reference internal" href="#time.struct_time.tm_zone" title="time.struct_time.tm_zone"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_zone</span></code></a>
are now available on all platforms.</p>
</div>
</li>
<li><p>Use the following functions to convert between time representations:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>From</p></th>
<th class="head"><p>To</p></th>
<th class="head"><p>Use</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>seconds since the epoch</p></td>
<td><p><a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> in
UTC</p></td>
<td><p><a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p>seconds since the epoch</p></td>
<td><p><a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> in
local time</p></td>
<td><p><a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p><a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> in
UTC</p></td>
<td><p>seconds since the epoch</p></td>
<td><p><a class="reference internal" href="calendar.html#calendar.timegm" title="calendar.timegm"><code class="xref py py-func docutils literal notranslate"><span class="pre">calendar.timegm()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p><a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> in
local time</p></td>
<td><p>seconds since the epoch</p></td>
<td><p><a class="reference internal" href="#time.mktime" title="time.mktime"><code class="xref py py-func docutils literal notranslate"><span class="pre">mktime()</span></code></a></p></td>
</tr>
</tbody>
</table>
</li>
</ul>
<section id="functions">
<span id="time-functions"></span><h2>Functions<a class="headerlink" href="#functions" title="Link to this heading">¶</a></h2>
<dl class="py function">
<dt class="sig sig-object py" id="time.asctime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">asctime</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">t</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.asctime" title="Link to this definition">¶</a></dt>
<dd><p>Convert a tuple or <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> representing a time as returned by
<a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> or <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> to a string of the following
form: <code class="docutils literal notranslate"><span class="pre">'Sun</span> <span class="pre">Jun</span> <span class="pre">20</span> <span class="pre">23:21:05</span> <span class="pre">1993'</span></code>. The day field is two characters long
and is space padded if the day is a single digit,
e.g.: <code class="docutils literal notranslate"><span class="pre">'Wed</span> <span class="pre">Jun</span>&#160; <span class="pre">9</span> <span class="pre">04:26:40</span> <span class="pre">1993'</span></code>.</p>
<p>If <em>t</em> is not provided, the current time as returned by <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>
is used. Locale information is not used by <a class="reference internal" href="#time.asctime" title="time.asctime"><code class="xref py py-func docutils literal notranslate"><span class="pre">asctime()</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike the C function of the same name, <a class="reference internal" href="#time.asctime" title="time.asctime"><code class="xref py py-func docutils literal notranslate"><span class="pre">asctime()</span></code></a> does not add a
trailing newline.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.pthread_getcpuclockid">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">pthread_getcpuclockid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">thread_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.pthread_getcpuclockid" title="Link to this definition">¶</a></dt>
<dd><p>Return the <em>clk_id</em> of the thread-specific CPU-time clock for the specified <em>thread_id</em>.</p>
<p>Use <a class="reference internal" href="threading.html#threading.get_ident" title="threading.get_ident"><code class="xref py py-func docutils literal notranslate"><span class="pre">threading.get_ident()</span></code></a> or the <a class="reference internal" href="threading.html#threading.Thread.ident" title="threading.Thread.ident"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ident</span></code></a>
attribute of <a class="reference internal" href="threading.html#threading.Thread" title="threading.Thread"><code class="xref py py-class docutils literal notranslate"><span class="pre">threading.Thread</span></code></a> objects to get a suitable value
for <em>thread_id</em>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Passing an invalid or expired <em>thread_id</em> may result in
undefined behavior, such as segmentation fault.</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix</p>
<p>See the man page for <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/pthread_getcpuclockid(3)">pthread_getcpuclockid(3)</a></em> for
further information.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.clock_getres">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">clock_getres</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">clk_id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.clock_getres" title="Link to this definition">¶</a></dt>
<dd><p>Return the resolution (precision) of the specified clock <em>clk_id</em>.  Refer to
<a class="reference internal" href="#time-clock-id-constants"><span class="std std-ref">Clock ID Constants</span></a> for a list of accepted values for <em>clk_id</em>.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.clock_gettime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">clock_gettime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">clk_id</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.clock_gettime" title="Link to this definition">¶</a></dt>
<dd><p>Return the time of the specified clock <em>clk_id</em>.  Refer to
<a class="reference internal" href="#time-clock-id-constants"><span class="std std-ref">Clock ID Constants</span></a> for a list of accepted values for <em>clk_id</em>.</p>
<p>Use <a class="reference internal" href="#time.clock_gettime_ns" title="time.clock_gettime_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_gettime_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.clock_gettime_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">clock_gettime_ns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">clk_id</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.clock_gettime_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.clock_gettime" title="time.clock_gettime"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_gettime()</span></code></a> but return time as nanoseconds.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.clock_settime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">clock_settime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">clk_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.clock_settime" title="Link to this definition">¶</a></dt>
<dd><p>Set the time of the specified clock <em>clk_id</em>.  Currently,
<a class="reference internal" href="#time.CLOCK_REALTIME" title="time.CLOCK_REALTIME"><code class="xref py py-data docutils literal notranslate"><span class="pre">CLOCK_REALTIME</span></code></a> is the only accepted value for <em>clk_id</em>.</p>
<p>Use <a class="reference internal" href="#time.clock_settime_ns" title="time.clock_settime_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_settime_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.clock_settime_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">clock_settime_ns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">clk_id</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">time</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.clock_settime_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.clock_settime" title="time.clock_settime"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_settime()</span></code></a> but set time with nanoseconds.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.ctime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">ctime</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">secs</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.ctime" title="Link to this definition">¶</a></dt>
<dd><p>Convert a time expressed in seconds since the <a class="reference internal" href="#epoch">epoch</a> to a string of a form:
<code class="docutils literal notranslate"><span class="pre">'Sun</span> <span class="pre">Jun</span> <span class="pre">20</span> <span class="pre">23:21:05</span> <span class="pre">1993'</span></code> representing local time. The day field
is two characters long and is space padded if the day is a single digit,
e.g.: <code class="docutils literal notranslate"><span class="pre">'Wed</span> <span class="pre">Jun</span>&#160; <span class="pre">9</span> <span class="pre">04:26:40</span> <span class="pre">1993'</span></code>.</p>
<p>If <em>secs</em> is not provided or <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the current time as
returned by <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> is used. <code class="docutils literal notranslate"><span class="pre">ctime(secs)</span></code> is equivalent to
<code class="docutils literal notranslate"><span class="pre">asctime(localtime(secs))</span></code>. Locale information is not used by
<a class="reference internal" href="#time.ctime" title="time.ctime"><code class="xref py py-func docutils literal notranslate"><span class="pre">ctime()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.get_clock_info">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">get_clock_info</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.get_clock_info" title="Link to this definition">¶</a></dt>
<dd><p>Get information on the specified clock as a namespace object.
Supported clock names and the corresponding functions to read their value
are:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">'monotonic'</span></code>: <a class="reference internal" href="#time.monotonic" title="time.monotonic"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.monotonic()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'perf_counter'</span></code>: <a class="reference internal" href="#time.perf_counter" title="time.perf_counter"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.perf_counter()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'process_time'</span></code>: <a class="reference internal" href="#time.process_time" title="time.process_time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.process_time()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'thread_time'</span></code>: <a class="reference internal" href="#time.thread_time" title="time.thread_time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.thread_time()</span></code></a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">'time'</span></code>: <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.time()</span></code></a></p></li>
</ul>
<p>The result has the following attributes:</p>
<ul class="simple">
<li><p><em>adjustable</em>: <code class="docutils literal notranslate"><span class="pre">True</span></code> if the clock can be changed automatically (e.g. by
a NTP daemon) or manually by the system administrator, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise</p></li>
<li><p><em>implementation</em>: The name of the underlying C function used to get
the clock value.  Refer to <a class="reference internal" href="#time-clock-id-constants"><span class="std std-ref">Clock ID Constants</span></a> for possible values.</p></li>
<li><p><em>monotonic</em>: <code class="docutils literal notranslate"><span class="pre">True</span></code> if the clock cannot go backward,
<code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise</p></li>
<li><p><em>resolution</em>: The resolution of the clock in seconds (<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>)</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.gmtime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">gmtime</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">secs</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.gmtime" title="Link to this definition">¶</a></dt>
<dd><p>Convert a time expressed in seconds since the <a class="reference internal" href="#epoch">epoch</a> to a <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> in
UTC in which the dst flag is always zero.  If <em>secs</em> is not provided or
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the current time as returned by <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> is used.  Fractions
of a second are ignored.  See above for a description of the
<a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> object. See <a class="reference internal" href="calendar.html#calendar.timegm" title="calendar.timegm"><code class="xref py py-func docutils literal notranslate"><span class="pre">calendar.timegm()</span></code></a> for the inverse of this
function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.localtime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">localtime</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">secs</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.localtime" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> but converts to local time.  If <em>secs</em> is not provided or
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the current time as returned by <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> is used.  The dst
flag is set to <code class="docutils literal notranslate"><span class="pre">1</span></code> when DST applies to the given time.</p>
<p><a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> may raise <a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a>, if the timestamp is
outside the range of values supported by the platform C <code class="xref c c-func docutils literal notranslate"><span class="pre">localtime()</span></code>
or <code class="xref c c-func docutils literal notranslate"><span class="pre">gmtime()</span></code> functions, and <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> on <code class="xref c c-func docutils literal notranslate"><span class="pre">localtime()</span></code> or
<code class="xref c c-func docutils literal notranslate"><span class="pre">gmtime()</span></code> failure. It’s common for this to be restricted to years
between 1970 and 2038.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.mktime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">mktime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.mktime" title="Link to this definition">¶</a></dt>
<dd><p>This is the inverse function of <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>.  Its argument is the
<a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> or full 9-tuple (since the dst flag is needed; use <code class="docutils literal notranslate"><span class="pre">-1</span></code>
as the dst flag if it is unknown) which expresses the time in <em>local</em> time, not
UTC.  It returns a floating point number, for compatibility with <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a>.
If the input value cannot be represented as a valid time, either
<a class="reference internal" href="exceptions.html#OverflowError" title="OverflowError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OverflowError</span></code></a> or <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised (which depends on
whether the invalid value is caught by Python or the underlying C libraries).
The earliest date for which it can generate a time is platform-dependent.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.monotonic">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">monotonic</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.monotonic" title="Link to this definition">¶</a></dt>
<dd><p>Return the value (in fractional seconds) of a monotonic clock, i.e. a clock
that cannot go backwards.  The clock is not affected by system clock updates.
The reference point of the returned value is undefined, so that only the
difference between the results of two calls is valid.</p>
<p>Use <a class="reference internal" href="#time.monotonic_ns" title="time.monotonic_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">monotonic_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The function is now always available and always system-wide.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>On macOS, the function is now system-wide.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.monotonic_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">monotonic_ns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.monotonic_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.monotonic" title="time.monotonic"><code class="xref py py-func docutils literal notranslate"><span class="pre">monotonic()</span></code></a>, but return time as nanoseconds.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.perf_counter">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">perf_counter</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.perf_counter" title="Link to this definition">¶</a></dt>
<dd><p id="index-6">Return the value (in fractional seconds) of a performance counter, i.e. a
clock with the highest available resolution to measure a short duration.  It
does include time elapsed during sleep and is system-wide.  The reference
point of the returned value is undefined, so that only the difference between
the results of two calls is valid.</p>
<p>Use <a class="reference internal" href="#time.perf_counter_ns" title="time.perf_counter_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">perf_counter_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>On Windows, the function is now system-wide.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.perf_counter_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">perf_counter_ns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.perf_counter_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.perf_counter" title="time.perf_counter"><code class="xref py py-func docutils literal notranslate"><span class="pre">perf_counter()</span></code></a>, but return time as nanoseconds.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.process_time">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">process_time</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.process_time" title="Link to this definition">¶</a></dt>
<dd><p id="index-7">Return the value (in fractional seconds) of the sum of the system and user
CPU time of the current process.  It does not include time elapsed during
sleep.  It is process-wide by definition.  The reference point of the
returned value is undefined, so that only the difference between the results
of two calls is valid.</p>
<p>Use <a class="reference internal" href="#time.process_time_ns" title="time.process_time_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">process_time_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.process_time_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">process_time_ns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.process_time_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.process_time" title="time.process_time"><code class="xref py py-func docutils literal notranslate"><span class="pre">process_time()</span></code></a> but return time as nanoseconds.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.sleep">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">sleep</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">secs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#time.sleep" title="Link to this definition">¶</a></dt>
<dd><p>Suspend execution of the calling thread for the given number of seconds.
The argument may be a floating point number to indicate a more precise sleep
time.</p>
<p>If the sleep is interrupted by a signal and no exception is raised by the
signal handler, the sleep is restarted with a recomputed timeout.</p>
<p>The suspension time may be longer than requested by an arbitrary amount,
because of the scheduling of other activity in the system.</p>
<p>On Windows, if <em>secs</em> is zero, the thread relinquishes the remainder of its
time slice to any other thread that is ready to run. If there are no other
threads ready to run, the function returns immediately, and the thread
continues execution.  On Windows 8.1 and newer the implementation uses
a <a class="reference external" href="https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/high-resolution-timers">high-resolution timer</a>
which provides resolution of 100 nanoseconds. If <em>secs</em> is zero, <code class="docutils literal notranslate"><span class="pre">Sleep(0)</span></code> is used.</p>
<p>Unix implementation:</p>
<ul class="simple">
<li><p>Use <code class="docutils literal notranslate"><span class="pre">clock_nanosleep()</span></code> if available (resolution: 1 nanosecond);</p></li>
<li><p>Or use <code class="docutils literal notranslate"><span class="pre">nanosleep()</span></code> if available (resolution: 1 nanosecond);</p></li>
<li><p>Or use <code class="docutils literal notranslate"><span class="pre">select()</span></code> (resolution: 1 microsecond).</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The function now sleeps at least <em>secs</em> even if the sleep is interrupted
by a signal, except if the signal handler raises an exception (see
<span class="target" id="index-8"></span><a class="pep reference external" href="https://peps.python.org/pep-0475/"><strong>PEP 475</strong></a> for the rationale).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>On Unix, the <code class="docutils literal notranslate"><span class="pre">clock_nanosleep()</span></code> and <code class="docutils literal notranslate"><span class="pre">nanosleep()</span></code> functions are now
used if available. On Windows, a waitable timer is now used.</p>
</div>
</dd></dl>

<dl class="py function" id="index-9">
<dt class="sig sig-object py" id="time.strftime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">strftime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">t</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.strftime" title="Link to this definition">¶</a></dt>
<dd><p>Convert a tuple or <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> representing a time as returned by
<a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> or <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> to a string as specified by the <em>format</em>
argument.  If <em>t</em> is not provided, the current time as returned by
<a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> is used.  <em>format</em> must be a string.  <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is
raised if any field in <em>t</em> is outside of the allowed range.</p>
<p>0 is a legal argument for any position in the time tuple; if it is normally
illegal the value is forced to a correct one.</p>
<p>The following directives can be embedded in the <em>format</em> string. They are shown
without the optional field width and precision specification, and are replaced
by the indicated characters in the <a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a> result:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Directive</p></th>
<th class="head"><p>Meaning</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%a</span></code></p></td>
<td><p>Locale’s abbreviated weekday name.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%A</span></code></p></td>
<td><p>Locale’s full weekday name.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%b</span></code></p></td>
<td><p>Locale’s abbreviated month name.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%B</span></code></p></td>
<td><p>Locale’s full month name.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%c</span></code></p></td>
<td><p>Locale’s appropriate date and time
representation.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%d</span></code></p></td>
<td><p>Day of the month as a decimal number [01,31].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%f</span></code></p></td>
<td><dl class="simple">
<dt>Microseconds as a decimal number</dt><dd><p>[000000,999999].</p>
</dd>
</dl>
</td>
<td><p>(1)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%H</span></code></p></td>
<td><p>Hour (24-hour clock) as a decimal number
[00,23].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%I</span></code></p></td>
<td><p>Hour (12-hour clock) as a decimal number
[01,12].</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%j</span></code></p></td>
<td><p>Day of the year as a decimal number [001,366].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%m</span></code></p></td>
<td><p>Month as a decimal number [01,12].</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%M</span></code></p></td>
<td><p>Minute as a decimal number [00,59].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%p</span></code></p></td>
<td><p>Locale’s equivalent of either AM or PM.</p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%S</span></code></p></td>
<td><p>Second as a decimal number [00,61].</p></td>
<td><p>(3)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%U</span></code></p></td>
<td><p>Week number of the year (Sunday as the first
day of the week) as a decimal number [00,53].
All days in a new year preceding the first
Sunday are considered to be in week 0.</p></td>
<td><p>(4)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%w</span></code></p></td>
<td><p>Weekday as a decimal number [0(Sunday),6].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%W</span></code></p></td>
<td><p>Week number of the year (Monday as the first
day of the week) as a decimal number [00,53].
All days in a new year preceding the first
Monday are considered to be in week 0.</p></td>
<td><p>(4)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%x</span></code></p></td>
<td><p>Locale’s appropriate date representation.</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%X</span></code></p></td>
<td><p>Locale’s appropriate time representation.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%y</span></code></p></td>
<td><p>Year without century as a decimal number
[00,99].</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%Y</span></code></p></td>
<td><p>Year with century as a decimal number.</p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%z</span></code></p></td>
<td><p>Time zone offset indicating a positive or
negative time difference from UTC/GMT of the
form +HHMM or -HHMM, where H represents decimal
hour digits and M represents decimal minute
digits [-23:59, +23:59]. <a class="footnote-reference brackets" href="#id4" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">%Z</span></code></p></td>
<td><p>Time zone name (no characters if no time zone
exists). Deprecated. <a class="footnote-reference brackets" href="#id4" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">%%</span></code></p></td>
<td><p>A literal <code class="docutils literal notranslate"><span class="pre">'%'</span></code> character.</p></td>
<td></td>
</tr>
</tbody>
</table>
<p>Notes:</p>
<ol class="arabic simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">%f</span></code> format directive only applies to <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a>,
not to <a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a>. However, see also <a class="reference internal" href="datetime.html#datetime.datetime.strptime" title="datetime.datetime.strptime"><code class="xref py py-meth docutils literal notranslate"><span class="pre">datetime.datetime.strptime()</span></code></a> and
<a class="reference internal" href="datetime.html#datetime.datetime.strftime" title="datetime.datetime.strftime"><code class="xref py py-meth docutils literal notranslate"><span class="pre">datetime.datetime.strftime()</span></code></a> where the <code class="docutils literal notranslate"><span class="pre">%f</span></code> format directive
<a class="reference internal" href="datetime.html#format-codes"><span class="std std-ref">applies to microseconds</span></a>.</p></li>
<li><p>When used with the <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a> function, the <code class="docutils literal notranslate"><span class="pre">%p</span></code> directive only affects
the output hour field if the <code class="docutils literal notranslate"><span class="pre">%I</span></code> directive is used to parse the hour.</p></li>
</ol>
<ol class="arabic simple" id="leap-second" start="3">
<li><p>The range really is <code class="docutils literal notranslate"><span class="pre">0</span></code> to <code class="docutils literal notranslate"><span class="pre">61</span></code>; value <code class="docutils literal notranslate"><span class="pre">60</span></code> is valid in
timestamps representing <a class="reference external" href="https://en.wikipedia.org/wiki/Leap_second">leap seconds</a> and value <code class="docutils literal notranslate"><span class="pre">61</span></code> is supported
for historical reasons.</p></li>
<li><p>When used with the <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a> function, <code class="docutils literal notranslate"><span class="pre">%U</span></code> and <code class="docutils literal notranslate"><span class="pre">%W</span></code> are only used in
calculations when the day of the week and the year are specified.</p></li>
</ol>
<p>Here is an example, a format for dates compatible with that specified  in the
<span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a> Internet email standard.  <a class="footnote-reference brackets" href="#id4" id="id3" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">time</span> <span class="kn">import</span> <span class="n">gmtime</span><span class="p">,</span> <span class="n">strftime</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%a</span><span class="s2">, </span><span class="si">%d</span><span class="s2"> %b %Y %H:%M:%S +0000&quot;</span><span class="p">,</span> <span class="n">gmtime</span><span class="p">())</span>
<span class="go">&#39;Thu, 28 Jun 2001 14:17:15 +0000&#39;</span>
</pre></div>
</div>
<p>Additional directives may be supported on certain platforms, but only the
ones listed here have a meaning standardized by ANSI C.  To see the full set
of format codes supported on your platform, consult the <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/strftime(3)">strftime(3)</a></em>
documentation.</p>
<p>On some platforms, an optional field width and precision specification can
immediately follow the initial <code class="docutils literal notranslate"><span class="pre">'%'</span></code> of a directive in the following order;
this is also not portable. The field width is normally 2 except for <code class="docutils literal notranslate"><span class="pre">%j</span></code> where
it is 3.</p>
</dd></dl>

<dl class="py function" id="index-11">
<dt class="sig sig-object py" id="time.strptime">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">strptime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">string</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">format</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#time.strptime" title="Link to this definition">¶</a></dt>
<dd><p>Parse a string representing a time according to a format.  The return value
is a <a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> as returned by <a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> or
<a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>.</p>
<p>The <em>format</em> parameter uses the same directives as those used by
<a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a>; it defaults to <code class="docutils literal notranslate"><span class="pre">&quot;%a</span> <span class="pre">%b</span> <span class="pre">%d</span> <span class="pre">%H:%M:%S</span> <span class="pre">%Y&quot;</span></code> which matches the
formatting returned by <a class="reference internal" href="#time.ctime" title="time.ctime"><code class="xref py py-func docutils literal notranslate"><span class="pre">ctime()</span></code></a>. If <em>string</em> cannot be parsed according
to <em>format</em>, or if it has excess data after parsing, <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> is
raised. The default values used to fill in any missing data when more
accurate values cannot be inferred are <code class="docutils literal notranslate"><span class="pre">(1900,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">1,</span> <span class="pre">-1)</span></code>.
Both <em>string</em> and <em>format</em> must be strings.</p>
<p>For example:</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">time</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">strptime</span><span class="p">(</span><span class="s2">&quot;30 Nov 00&quot;</span><span class="p">,</span> <span class="s2">&quot;</span><span class="si">%d</span><span class="s2"> %b %y&quot;</span><span class="p">)</span>   
<span class="go">time.struct_time(tm_year=2000, tm_mon=11, tm_mday=30, tm_hour=0, tm_min=0,</span>
<span class="go">                 tm_sec=0, tm_wday=3, tm_yday=335, tm_isdst=-1)</span>
</pre></div>
</div>
<p>Support for the <code class="docutils literal notranslate"><span class="pre">%Z</span></code> directive is based on the values contained in <code class="docutils literal notranslate"><span class="pre">tzname</span></code>
and whether <code class="docutils literal notranslate"><span class="pre">daylight</span></code> is true.  Because of this, it is platform-specific
except for recognizing UTC and GMT which are always known (and are considered to
be non-daylight savings timezones).</p>
<p>Only the directives specified in the documentation are supported.  Because
<code class="docutils literal notranslate"><span class="pre">strftime()</span></code> is implemented per platform it can sometimes offer more
directives than those listed.  But <code class="docutils literal notranslate"><span class="pre">strptime()</span></code> is independent of any platform
and thus does not necessarily support all directives available that are not
documented as supported.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="time.struct_time">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">struct_time</span></span><a class="headerlink" href="#time.struct_time" title="Link to this definition">¶</a></dt>
<dd><p>The type of the time value sequence returned by <a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a>,
<a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a>, and <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a>.  It is an object with a <a class="reference internal" href="../glossary.html#term-named-tuple"><span class="xref std std-term">named
tuple</span></a> interface: values can be accessed by index and by attribute name.  The
following values are present:</p>
<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><p>Index</p></td>
<td><p>Attribute</p></td>
<td><p>Values</p></td>
</tr>
<tr class="row-even"><td><p>0</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_year">
<span class="sig-name descname"><span class="pre">tm_year</span></span><a class="headerlink" href="#time.struct_time.tm_year" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>(for example, 1993)</p></td>
</tr>
<tr class="row-odd"><td><p>1</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_mon">
<span class="sig-name descname"><span class="pre">tm_mon</span></span><a class="headerlink" href="#time.struct_time.tm_mon" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [1, 12]</p></td>
</tr>
<tr class="row-even"><td><p>2</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_day">
<span class="sig-name descname"><span class="pre">tm_day</span></span><a class="headerlink" href="#time.struct_time.tm_day" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [1, 31]</p></td>
</tr>
<tr class="row-odd"><td><p>3</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_hour">
<span class="sig-name descname"><span class="pre">tm_hour</span></span><a class="headerlink" href="#time.struct_time.tm_hour" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [0, 23]</p></td>
</tr>
<tr class="row-even"><td><p>4</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_min">
<span class="sig-name descname"><span class="pre">tm_min</span></span><a class="headerlink" href="#time.struct_time.tm_min" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [0, 59]</p></td>
</tr>
<tr class="row-odd"><td><p>5</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_sec">
<span class="sig-name descname"><span class="pre">tm_sec</span></span><a class="headerlink" href="#time.struct_time.tm_sec" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [0, 61]; see <a class="reference internal" href="#leap-second"><span class="std std-ref">Note (2)</span></a> in <a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>6</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_wday">
<span class="sig-name descname"><span class="pre">tm_wday</span></span><a class="headerlink" href="#time.struct_time.tm_wday" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [0, 6]; Monday is 0</p></td>
</tr>
<tr class="row-odd"><td><p>7</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_yday">
<span class="sig-name descname"><span class="pre">tm_yday</span></span><a class="headerlink" href="#time.struct_time.tm_yday" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>range [1, 366]</p></td>
</tr>
<tr class="row-even"><td><p>8</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_isdst">
<span class="sig-name descname"><span class="pre">tm_isdst</span></span><a class="headerlink" href="#time.struct_time.tm_isdst" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>0, 1 or -1; see below</p></td>
</tr>
<tr class="row-odd"><td><p>N/A</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_zone">
<span class="sig-name descname"><span class="pre">tm_zone</span></span><a class="headerlink" href="#time.struct_time.tm_zone" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>abbreviation of timezone name</p></td>
</tr>
<tr class="row-even"><td><p>N/A</p></td>
<td><dl class="py attribute">
<dt class="sig sig-object py" id="time.struct_time.tm_gmtoff">
<span class="sig-name descname"><span class="pre">tm_gmtoff</span></span><a class="headerlink" href="#time.struct_time.tm_gmtoff" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>offset east of UTC in seconds</p></td>
</tr>
</tbody>
</table>
<p>Note that unlike the C structure, the month value is a range of [1, 12], not
[0, 11].</p>
<p>In calls to <a class="reference internal" href="#time.mktime" title="time.mktime"><code class="xref py py-func docutils literal notranslate"><span class="pre">mktime()</span></code></a>, <a class="reference internal" href="#time.struct_time.tm_isdst" title="time.struct_time.tm_isdst"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_isdst</span></code></a> may be set to 1 when daylight
savings time is in effect, and 0 when it is not.  A value of -1 indicates that
this is not known, and will usually result in the correct state being filled in.</p>
<p>When a tuple with an incorrect length is passed to a function expecting a
<a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a>, or having elements of the wrong type, a
<a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a> is raised.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.time">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">time</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.time" title="Link to this definition">¶</a></dt>
<dd><p>Return the time in seconds since the <a class="reference internal" href="#epoch">epoch</a> as a floating point
number. The handling of <a class="reference external" href="https://en.wikipedia.org/wiki/Leap_second">leap seconds</a> is platform dependent.
On Windows and most Unix systems, the leap seconds are not counted towards
the time in seconds since the <a class="reference internal" href="#epoch">epoch</a>. This is commonly referred to as <a class="reference external" href="https://en.wikipedia.org/wiki/Unix_time">Unix
time</a>.</p>
<p>Note that even though the time is always returned as a floating point
number, not all systems provide time with a better precision than 1 second.
While this function normally returns non-decreasing values, it can return a
lower value than a previous call if the system clock has been set back
between the two calls.</p>
<p>The number returned by <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> may be converted into a more common
time format (i.e. year, month, day, hour, etc…) in UTC by passing it to
<a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> function or in local time by passing it to the
<a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> function. In both cases a
<a class="reference internal" href="#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">struct_time</span></code></a> object is returned, from which the components
of the calendar date may be accessed as attributes.</p>
<p>Use <a class="reference internal" href="#time.time_ns" title="time.time_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">time_ns()</span></code></a> to avoid the precision loss caused by the <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>
type.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.time_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">time_ns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.time_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time()</span></code></a> but returns time as an integer number of
nanoseconds since the <a class="reference internal" href="#epoch">epoch</a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.thread_time">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">thread_time</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#float" title="float"><span class="pre">float</span></a></span></span><a class="headerlink" href="#time.thread_time" title="Link to this definition">¶</a></dt>
<dd><p id="index-12">Return the value (in fractional seconds) of the sum of the system and user
CPU time of the current thread.  It does not include time elapsed during
sleep.  It is thread-specific by definition.  The reference point of the
returned value is undefined, so that only the difference between the results
of two calls in the same thread is valid.</p>
<p>Use <a class="reference internal" href="#time.thread_time_ns" title="time.thread_time_ns"><code class="xref py py-func docutils literal notranslate"><span class="pre">thread_time_ns()</span></code></a> to avoid the precision loss caused by the
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> type.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux, Unix, Windows.</p>
<p>Unix systems supporting <code class="docutils literal notranslate"><span class="pre">CLOCK_THREAD_CPUTIME_ID</span></code>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.thread_time_ns">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">thread_time_ns</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="functions.html#int" title="int"><span class="pre">int</span></a></span></span><a class="headerlink" href="#time.thread_time_ns" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.thread_time" title="time.thread_time"><code class="xref py py-func docutils literal notranslate"><span class="pre">thread_time()</span></code></a> but return time as nanoseconds.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="time.tzset">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">tzset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#time.tzset" title="Link to this definition">¶</a></dt>
<dd><p>Reset the time conversion rules used by the library routines. The environment
variable <span class="target" id="index-13"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> specifies how this is done. It will also set the variables
<code class="docutils literal notranslate"><span class="pre">tzname</span></code> (from the <span class="target" id="index-14"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> environment variable), <code class="docutils literal notranslate"><span class="pre">timezone</span></code> (non-DST
seconds West of UTC), <code class="docutils literal notranslate"><span class="pre">altzone</span></code> (DST seconds west of UTC) and <code class="docutils literal notranslate"><span class="pre">daylight</span></code>
(to 0 if this timezone does not have any daylight saving time rules, or to
nonzero if there is a time, past, present or future when daylight saving time
applies).</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Although in many cases, changing the <span class="target" id="index-15"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> environment variable may
affect the output of functions like <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> without calling
<a class="reference internal" href="#time.tzset" title="time.tzset"><code class="xref py py-func docutils literal notranslate"><span class="pre">tzset()</span></code></a>, this behavior should not be relied on.</p>
<p>The <span class="target" id="index-16"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> environment variable should contain no whitespace.</p>
</div>
<p>The standard format of the <span class="target" id="index-17"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> environment variable is (whitespace
added for clarity):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">std</span> <span class="n">offset</span> <span class="p">[</span><span class="n">dst</span> <span class="p">[</span><span class="n">offset</span> <span class="p">[,</span><span class="n">start</span><span class="p">[</span><span class="o">/</span><span class="n">time</span><span class="p">],</span> <span class="n">end</span><span class="p">[</span><span class="o">/</span><span class="n">time</span><span class="p">]]]]</span>
</pre></div>
</div>
<p>Where the components are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">std</span></code> and <code class="docutils literal notranslate"><span class="pre">dst</span></code></dt><dd><p>Three or more alphanumerics giving the timezone abbreviations. These will be
propagated into time.tzname</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">offset</span></code></dt><dd><p>The offset has the form: <code class="docutils literal notranslate"><span class="pre">±</span> <span class="pre">hh[:mm[:ss]]</span></code>. This indicates the value
added the local time to arrive at UTC.  If preceded by a ‘-’, the timezone
is east of the Prime Meridian; otherwise, it is west. If no offset follows
dst, summer time is assumed to be one hour ahead of standard time.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">start[/time],</span> <span class="pre">end[/time]</span></code></dt><dd><p>Indicates when to change to and back from DST. The format of the
start and end dates are one of the following:</p>
<dl class="simple">
<dt><code class="samp docutils literal notranslate"><span class="pre">J</span><em><span class="pre">n</span></em></code></dt><dd><p>The Julian day <em>n</em> (1 &lt;= <em>n</em> &lt;= 365). Leap days are not counted, so in
all years February 28 is day 59 and March 1 is day 60.</p>
</dd>
<dt><code class="samp docutils literal notranslate"><em><span class="pre">n</span></em></code></dt><dd><p>The zero-based Julian day (0 &lt;= <em>n</em> &lt;= 365). Leap days are counted, and
it is possible to refer to February 29.</p>
</dd>
<dt><code class="samp docutils literal notranslate"><span class="pre">M</span><em><span class="pre">m</span></em><span class="pre">.</span><em><span class="pre">n</span></em><span class="pre">.</span><em><span class="pre">d</span></em></code></dt><dd><p>The <em>d</em>’th day (0 &lt;= <em>d</em> &lt;= 6) of week <em>n</em> of month <em>m</em> of the year (1
&lt;= <em>n</em> &lt;= 5, 1 &lt;= <em>m</em> &lt;= 12, where week 5 means “the last <em>d</em> day in
month <em>m</em>” which may occur in either the fourth or the fifth
week). Week 1 is the first week in which the <em>d</em>’th day occurs. Day
zero is a Sunday.</p>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">time</span></code> has the same format as <code class="docutils literal notranslate"><span class="pre">offset</span></code> except that no leading sign
(‘-’ or ‘+’) is allowed. The default, if time is not given, is 02:00:00.</p>
</dd>
</dl>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s1">&#39;TZ&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;EST+05EDT,M4.1.0,M10.5.0&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%X</span><span class="s1"> </span><span class="si">%x</span><span class="s1"> %Z&#39;</span><span class="p">)</span>
<span class="go">&#39;02:07:36 05/08/03 EDT&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s1">&#39;TZ&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;AEST-10AEDT-11,M10.5.0,M3.5.0&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;</span><span class="si">%X</span><span class="s1"> </span><span class="si">%x</span><span class="s1"> %Z&#39;</span><span class="p">)</span>
<span class="go">&#39;16:08:12 05/08/03 AEST&#39;</span>
</pre></div>
</div>
<p>On many Unix systems (including *BSD, Linux, Solaris, and Darwin), it is more
convenient to use the system’s zoneinfo (<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/tzfile(5)">tzfile(5)</a></em>)  database to
specify the timezone rules. To do this, set the  <span class="target" id="index-18"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">TZ</span></code> environment
variable to the path of the required timezone  datafile, relative to the root of
the systems ‘zoneinfo’ timezone database, usually located at
<code class="file docutils literal notranslate"><span class="pre">/usr/share/zoneinfo</span></code>. For example,  <code class="docutils literal notranslate"><span class="pre">'US/Eastern'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'Australia/Melbourne'</span></code>, <code class="docutils literal notranslate"><span class="pre">'Egypt'</span></code> or  <code class="docutils literal notranslate"><span class="pre">'Europe/Amsterdam'</span></code>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s1">&#39;TZ&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;US/Eastern&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzname</span>
<span class="go">(&#39;EST&#39;, &#39;EDT&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="p">[</span><span class="s1">&#39;TZ&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;Egypt&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">time</span><span class="o">.</span><span class="n">tzname</span>
<span class="go">(&#39;EET&#39;, &#39;EEST&#39;)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="clock-id-constants">
<span id="time-clock-id-constants"></span><h2>Clock ID Constants<a class="headerlink" href="#clock-id-constants" title="Link to this heading">¶</a></h2>
<p>These constants are used as parameters for <a class="reference internal" href="#time.clock_getres" title="time.clock_getres"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_getres()</span></code></a> and
<a class="reference internal" href="#time.clock_gettime" title="time.clock_gettime"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_gettime()</span></code></a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_BOOTTIME">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_BOOTTIME</span></span><a class="headerlink" href="#time.CLOCK_BOOTTIME" title="Link to this definition">¶</a></dt>
<dd><p>Identical to <a class="reference internal" href="#time.CLOCK_MONOTONIC" title="time.CLOCK_MONOTONIC"><code class="xref py py-data docutils literal notranslate"><span class="pre">CLOCK_MONOTONIC</span></code></a>, except it also includes any time that
the system is suspended.</p>
<p>This allows applications to get a suspend-aware monotonic  clock  without
having to deal with the complications of <a class="reference internal" href="#time.CLOCK_REALTIME" title="time.CLOCK_REALTIME"><code class="xref py py-data docutils literal notranslate"><span class="pre">CLOCK_REALTIME</span></code></a>, which may
have  discontinuities if the time is changed using <code class="docutils literal notranslate"><span class="pre">settimeofday()</span></code> or
similar.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.39.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_HIGHRES">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_HIGHRES</span></span><a class="headerlink" href="#time.CLOCK_HIGHRES" title="Link to this definition">¶</a></dt>
<dd><p>The Solaris OS has a <code class="docutils literal notranslate"><span class="pre">CLOCK_HIGHRES</span></code> timer that attempts to use an optimal
hardware source, and may give close to nanosecond resolution.
<code class="docutils literal notranslate"><span class="pre">CLOCK_HIGHRES</span></code> is the nonadjustable, high-resolution clock.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Solaris.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_MONOTONIC">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_MONOTONIC</span></span><a class="headerlink" href="#time.CLOCK_MONOTONIC" title="Link to this definition">¶</a></dt>
<dd><p>Clock that cannot be set and represents monotonic time since some unspecified
starting point.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_MONOTONIC_RAW">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_MONOTONIC_RAW</span></span><a class="headerlink" href="#time.CLOCK_MONOTONIC_RAW" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#time.CLOCK_MONOTONIC" title="time.CLOCK_MONOTONIC"><code class="xref py py-data docutils literal notranslate"><span class="pre">CLOCK_MONOTONIC</span></code></a>, but provides access to a raw
hardware-based time that is not subject to NTP adjustments.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux &gt;= 2.6.28, macOS &gt;= 10.12.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_PROCESS_CPUTIME_ID">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_PROCESS_CPUTIME_ID</span></span><a class="headerlink" href="#time.CLOCK_PROCESS_CPUTIME_ID" title="Link to this definition">¶</a></dt>
<dd><p>High-resolution per-process timer from the CPU.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_PROF">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_PROF</span></span><a class="headerlink" href="#time.CLOCK_PROF" title="Link to this definition">¶</a></dt>
<dd><p>High-resolution per-process timer from the CPU.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD, NetBSD &gt;= 7, OpenBSD.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_TAI">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_TAI</span></span><a class="headerlink" href="#time.CLOCK_TAI" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference external" href="https://www.nist.gov/pml/time-and-frequency-division/nist-time-frequently-asked-questions-faq#tai">International Atomic Time</a></p>
<p>The system must have a current leap second table in order for this to give
the correct answer.  PTP or NTP software can maintain a leap second table.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Linux.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_THREAD_CPUTIME_ID">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_THREAD_CPUTIME_ID</span></span><a class="headerlink" href="#time.CLOCK_THREAD_CPUTIME_ID" title="Link to this definition">¶</a></dt>
<dd><p>Thread-specific CPU-time clock.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_UPTIME">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_UPTIME</span></span><a class="headerlink" href="#time.CLOCK_UPTIME" title="Link to this definition">¶</a></dt>
<dd><p>Time whose absolute value is the time the system has been running and not
suspended, providing accurate uptime measurement, both absolute and
interval.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: FreeBSD, OpenBSD &gt;= 5.5.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_UPTIME_RAW">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_UPTIME_RAW</span></span><a class="headerlink" href="#time.CLOCK_UPTIME_RAW" title="Link to this definition">¶</a></dt>
<dd><p>Clock that increments monotonically, tracking the time since an arbitrary
point, unaffected by frequency or time adjustments and not incremented while
the system is asleep.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: macOS &gt;= 10.12.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<p>The following constant is the only parameter that can be sent to
<a class="reference internal" href="#time.clock_settime" title="time.clock_settime"><code class="xref py py-func docutils literal notranslate"><span class="pre">clock_settime()</span></code></a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="time.CLOCK_REALTIME">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">CLOCK_REALTIME</span></span><a class="headerlink" href="#time.CLOCK_REALTIME" title="Link to this definition">¶</a></dt>
<dd><p>System-wide real-time clock.  Setting this clock requires appropriate
privileges.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: Unix.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

</section>
<section id="timezone-constants">
<span id="time-timezone-constants"></span><h2>Timezone Constants<a class="headerlink" href="#timezone-constants" title="Link to this heading">¶</a></h2>
<dl class="py data">
<dt class="sig sig-object py" id="time.altzone">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">altzone</span></span><a class="headerlink" href="#time.altzone" title="Link to this definition">¶</a></dt>
<dd><p>The offset of the local DST timezone, in seconds west of UTC, if one is defined.
This is negative if the local DST timezone is east of UTC (as in Western Europe,
including the UK).  Only use this if <code class="docutils literal notranslate"><span class="pre">daylight</span></code> is nonzero.  See note below.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.daylight">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">daylight</span></span><a class="headerlink" href="#time.daylight" title="Link to this definition">¶</a></dt>
<dd><p>Nonzero if a DST timezone is defined.  See note below.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.timezone">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">timezone</span></span><a class="headerlink" href="#time.timezone" title="Link to this definition">¶</a></dt>
<dd><p>The offset of the local (non-DST) timezone, in seconds west of UTC (negative in
most of Western Europe, positive in the US, zero in the UK).  See note below.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="time.tzname">
<span class="sig-prename descclassname"><span class="pre">time.</span></span><span class="sig-name descname"><span class="pre">tzname</span></span><a class="headerlink" href="#time.tzname" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of two strings: the first is the name of the local non-DST timezone, the
second is the name of the local DST timezone.  If no DST timezone is defined,
the second string should not be used.  See note below.</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For the above Timezone constants (<a class="reference internal" href="#time.altzone" title="time.altzone"><code class="xref py py-data docutils literal notranslate"><span class="pre">altzone</span></code></a>, <a class="reference internal" href="#time.daylight" title="time.daylight"><code class="xref py py-data docutils literal notranslate"><span class="pre">daylight</span></code></a>, <a class="reference internal" href="#time.timezone" title="time.timezone"><code class="xref py py-data docutils literal notranslate"><span class="pre">timezone</span></code></a>,
and <a class="reference internal" href="#time.tzname" title="time.tzname"><code class="xref py py-data docutils literal notranslate"><span class="pre">tzname</span></code></a>), the value is determined by the timezone rules in effect
at module load time or the last time <a class="reference internal" href="#time.tzset" title="time.tzset"><code class="xref py py-func docutils literal notranslate"><span class="pre">tzset()</span></code></a> is called and may be incorrect
for times in the past.  It is recommended to use the <a class="reference internal" href="#time.struct_time.tm_gmtoff" title="time.struct_time.tm_gmtoff"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_gmtoff</span></code></a> and
<a class="reference internal" href="#time.struct_time.tm_zone" title="time.struct_time.tm_zone"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tm_zone</span></code></a> results from <a class="reference internal" href="#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">localtime()</span></code></a> to obtain timezone information.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="datetime.html#module-datetime" title="datetime: Basic date and time types."><code class="xref py py-mod docutils literal notranslate"><span class="pre">datetime</span></code></a></dt><dd><p>More object-oriented interface to dates and times.</p>
</dd>
<dt>Module <a class="reference internal" href="locale.html#module-locale" title="locale: Internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code></a></dt><dd><p>Internationalization services.  The locale setting affects the interpretation
of many format specifiers in <a class="reference internal" href="#time.strftime" title="time.strftime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strftime()</span></code></a> and <a class="reference internal" href="#time.strptime" title="time.strptime"><code class="xref py py-func docutils literal notranslate"><span class="pre">strptime()</span></code></a>.</p>
</dd>
<dt>Module <a class="reference internal" href="calendar.html#module-calendar" title="calendar: Functions for working with calendars, including some emulation of the Unix cal program."><code class="xref py py-mod docutils literal notranslate"><span class="pre">calendar</span></code></a></dt><dd><p>General calendar-related functions.   <a class="reference internal" href="calendar.html#calendar.timegm" title="calendar.timegm"><code class="xref py py-func docutils literal notranslate"><span class="pre">timegm()</span></code></a> is the
inverse of <a class="reference internal" href="#time.gmtime" title="time.gmtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">gmtime()</span></code></a> from this module.</p>
</dd>
</dl>
</div>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id4" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></span>
<span class="backrefs">(<a role="doc-backlink" href="#id1">1</a>,<a role="doc-backlink" href="#id2">2</a>,<a role="doc-backlink" href="#id3">3</a>)</span>
<p>The use of <code class="docutils literal notranslate"><span class="pre">%Z</span></code> is now deprecated, but the <code class="docutils literal notranslate"><span class="pre">%z</span></code> escape that expands to the
preferred hour/minute offset is not supported by all ANSI C libraries. Also, a
strict reading of the original 1982 <span class="target" id="index-19"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> standard calls for a two-digit
year (<code class="docutils literal notranslate"><span class="pre">%y</span></code> rather than <code class="docutils literal notranslate"><span class="pre">%Y</span></code>), but practice moved to 4-digit years long before the
year 2000.  After that, <span class="target" id="index-20"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a> became obsolete and the 4-digit year has
been first recommended by <span class="target" id="index-21"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1123.html"><strong>RFC 1123</strong></a> and then mandated by <span class="target" id="index-22"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2822.html"><strong>RFC 2822</strong></a>.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a><ul>
<li><a class="reference internal" href="#functions">Functions</a></li>
<li><a class="reference internal" href="#clock-id-constants">Clock ID Constants</a></li>
<li><a class="reference internal" href="#timezone-constants">Timezone Constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="io.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">io</span></code> — Core tools for working with streams</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="argparse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">argparse</span></code> — Parser for command-line options, arguments and sub-commands</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/time.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="argparse.html" title="argparse — Parser for command-line options, arguments and sub-commands"
             >next</a> |</li>
        <li class="right" >
          <a href="io.html" title="io — Core tools for working with streams"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="allos.html" >Generic Operating System Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">time</span></code> — Time access and conversions</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>