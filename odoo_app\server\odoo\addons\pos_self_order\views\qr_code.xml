<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="report_self_order_qr_codes_page" model="ir.actions.report">
            <field name="name">QR Codes</field>
            <field name="model">pos.config</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">pos_self_order.qr_codes_page</field>
            <field name="report_file">pos_self_order.qr_codes_page</field>
            <field name="print_report_name">"QR codes"</field>
            <field name="binding_model_id" ref="model_pos_config"/>
            <field name="binding_type">report</field>
        </record>
        <record id="paperformat_qrcodes" model="report.paperformat">
            <field name="name">QR Codes Page</field>
            <field name="default" eval="True"/>
            <field name="header_line" eval="False"/>
        </record>
    </data>

    <template id="qr_codes_page">
        <!-- This page-break-inside does not seem to work;
            It would be nice to have the qr codes so the title is never on
            a different page then the qr code itself -->
        <t t-set="qr_code_size" t-value="190"/>
        <t t-call="web.basic_layout">
            <div class="w-100" style="text-align: right">
                <span class="fs-4">Point of sale: <t t-esc="pos_name" /></span><span class="fs-4" style="margin-left:20px">Self Order: <t t-if="self_order">Yes</t><t t-else="">No</t></span>
            </div>
            <div class="mb-5" style="border-bottom: 1px solid black;">
                <h2 t-if="table_mode">Make it easy for your customers to explore your menu
                online or order with the QR codes on your tables</h2>
                <h2 t-else="">Make it easy for your customers to explore your menu
                online with the QR codes on your tables</h2>
            </div>

            <div>
                <h3>How to use</h3>
                <t t-if="table_mode">
                    <p>Each table in your floor plan is assigned a unique QR code based on your configuration. For security reasons,
                    both the point of sale and table names are encrypted in the generated URL, as shown in the example below:.</p>
                    <p class="mt-2">Table: <span t-if="table_example" t-esc="table_example['name']" /><br/>
                    URL: <span t-if="table_example" t-esc="table_example['decoded_url']" /></p>
                </t>
                <t t-else="">
                    <p>Feel free to use and print this QR code as many times as needed according to your requirements.</p>
                    <p>URL: <span t-if="table_example" t-esc="table_example['decoded_url']" /></p>
                </t>
            </div>

            <!-- Table with access token -->
            <t t-if="floors" t-foreach="floors" t-as="floor">
                <div class="mb-5">
                    <h4 t-if="floor.get('name')" t-out="floor['name']" class="mb-3 mt-5"/>
                    <table style="border: none; border-collapse: collapse; width: 100%;">
                        <tbody style="border: none;">
                            <tr t-foreach="floor.get('table_rows')" t-as="row" style="border: none;">
                                <td t-foreach="row" t-as="table" style="width: 33%; text-align:center; border: none;">
                                    <div class="mb-3" style="page-break-inside: avoid">
                                        <h4 class="fw-bold text-center" t-if="table.get('name')">
                                            <t t-esc="table['name']"/>
                                        </h4>
                                        <div class="mt-1 mb-1 position-relative top-0 start-0">
                                            <img t-att-src="'/report/barcode/QR/%s?width=%s&amp;height=%s&amp;barLevel=H' %(table['url'], qr_code_size, qr_code_size)" class="position-relative top-0 start-0" />
                                            <svg class="position-absolute top-50 start-50" style="-webkit-transform: translate(-50%, -50%);" t-att-width="qr_code_size/4" t-att-height="qr_code_size/4" viewBox="0 0 120 120" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <rect width="120" height="120" rx="33" fill="white"/>
                                                    <g>
                                                        <path d="M60 110C87.6142 110 110 87.6142 110 60C110 32.3858 87.6142 10 60 10C32.3858 10 10 32.3858 10 60C10 87.6142 32.3858 110 60 110Z" fill="#9C5789"/>
                                                        <path d="M59.5166 89.6961C75.8029 89.6961 89.0055 76.4935 89.0055 60.2072C89.0055 43.9209 75.8029 30.7182 59.5166 30.7182C43.2303 30.7182 30.0276 43.9209 30.0276 60.2072C30.0276 76.4935 43.2303 89.6961 59.5166 89.6961Z" fill="white"/>
                                                    </g>
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </t>

            <div>
                <h3>How to customize</h3>
                <p>If you need customized QR codes, start by scanning the relevant QR code to acquire the URL. Then, make
                use of a QR code generator like https://www.qrcode-monkey.com or https://www.qr-code-generator.com</p>
            </div>
        </t>
    </template>
</odoo>
