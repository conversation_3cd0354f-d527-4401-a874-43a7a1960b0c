<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.ReprintReceiptScreen">
        <div class="receipt-screen screen h-100 bg-100">
            <div class="screen-content d-flex flex-column h-100">
                <div class="top-content d-flex align-items-center p-2 border-bottom text-center">
                    <button class="button back btn btn-lg btn-secondary" t-on-click="confirm">
                        <i class="fa fa-angle-double-left me-2"></i>
                        <span> </span>
                        <span>Back</span>
                    </button>
                </div>
                <div class="centered-content d-grid mx-auto mt-3 border-0 overflow-hidden">
                    <button class="button print btn btn-lg btn-primary" t-on-click="tryReprint">
                        <i class="fa fa-print ms-2"></i> Print Receipt
                    </button>
                    <div class="pos-receipt-container mt-2 bg-200 text-center overflow-y-auto">
                        <div class="d-inline-block m-3 p-3 border rounded bg-view text-start overflow-hidden">
                            <OrderReceipt data="props.order.export_for_printing()" formatCurrency="env.utils.formatCurrency" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
