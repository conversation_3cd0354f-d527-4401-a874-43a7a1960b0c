# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment_authorize
# 
# Translators:
# <PERSON>il <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rasaree<PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_payment_authorize
#: model:ir.model.fields,field_description:website_payment_authorize.field_res_config_settings__authorize_capture_method
msgid "Authorize.net: Payment Capture Method"
msgstr "Authorize.net: วิธีการบันทึกการชำระเงิน"

#. module: website_payment_authorize
#: model:ir.model.fields.selection,name:website_payment_authorize.selection__res_config_settings__authorize_capture_method__auto
msgid "Automatically Capture Payment"
msgstr "เก็บเงินอัตโนมัติ"

#. module: website_payment_authorize
#: model_terms:ir.ui.view,arch_db:website_payment_authorize.res_config_settings_view_form
msgid ""
"Charge order directly or authorize at the order and capture the payment "
"later on, manually."
msgstr ""
"เรียกเก็บเงินตามคำสั่งซื้อโดยตรงหรืออนุมัติตามคำสั่งซื้อและบันทึกการชำระเงินในภายหลังด้วยตนเอง"

#. module: website_payment_authorize
#: model:ir.model,name:website_payment_authorize.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_payment_authorize
#: model:ir.model.fields.selection,name:website_payment_authorize.selection__res_config_settings__authorize_capture_method__manual
msgid "Manually Charge Later"
msgstr "ชาร์จด้วยตนเองในภายหลัง"
