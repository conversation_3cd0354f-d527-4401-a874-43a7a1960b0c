<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="pos_self_order.OrderChangeReceipt">
        <div class="pos-receipt">
            <div class="pos-receipt-order-data"><t t-esc="changes.name" /></div>
            <t t-if="changes.tracker || changes.trackingNumber">
                <br />
                <div class="pos-receipt-title">
                    <t t-esc="changes.trackingNumber"/>
                    <t t-if="changes.tracker">
                        / Tracker number: <t t-esc="changes.tracker"/>
                    </t>
                </div>
            </t>
            <br />
            <br />
            <div class="pos-receipt-title">
                NEW
                <t t-esc='changes.time.hours'/>:<t t-esc='changes.time.minutes'/>
            </div>
            <br />
            <br />
            <t t-foreach="changes.new" t-as="change" t-key="change_index">
                <div class="multiprint-flex">
                    <span class="product-quantity" t-esc="change.qty"/>
                    <span class="product-name" t-esc="change.full_product_name"/>
                </div>
                <t t-if="change.customer_note">
                    <div>
                        NOTE
                        <span class="pos-receipt-right-align">...</span>
                    </div>
                    <div><span class="pos-receipt-left-padding">--- <t t-esc="change.customer_note" /></span></div>
                    <br/>
                </t>
            </t>
            <br />
            <br />
    </div>
    </t>

</templates>
