/** @odoo-module */

import { registry } from '@web/core/registry';
import { <PERSON><PERSON><PERSON><PERSON>, charField } from '@web/views/fields/char/char_field';

export class ProjectTaskNameWithSubtaskCountCharField extends Char<PERSON>ield {
    static template = "project.ProjectTaskNameWithSubtaskCountCharField";
}

export const projectTaskNameWithSubtaskCountCharField = {
    ...charField,
    component: ProjectTaskNameWithSubtaskCountCharField,
    fieldsDependencies: [
        { name: "subtask_count", type: "integer" },
        { name: "closed_subtask_count", type: "integer" },
    ],
}
registry.category("fields").add("name_with_subtask_count", projectTaskNameWithSubtaskCountCharField);
