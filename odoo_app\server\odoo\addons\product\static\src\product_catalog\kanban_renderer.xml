<?xml version="1.0" encoding="UTF-8"?>

<templates xml:space="preserve">
    <t t-name="ProductCatalogKanbanRenderer" t-inherit="web.KanbanRenderer" t-inherit-mode="primary">
        <t t-if="showNoContentHelper" position="replace">
            <t t-if="showNoContentHelper">
                <div class="o_view_nocontent" role="alert">
                    <div class="o_nocontent_help">
                        <p class="o_view_nocontent_smiling_face">
                            No products could be found.<br/>
                            <button class="mt-2 btn btn-primary" t-on-click="this.createProduct">Create a product</button>
                        </p>
                        <p>
                            You must define a product for everything you sell or purchase,
                            whether it's a storable product, a consumable or a service.
                        </p>
                    </div>
                </div>
            </t>
        </t>
    </t>
</templates>
