<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="imaplib — IMAP4 protocol client" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/imaplib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/imaplib.py This module defines three classes, IMAP4, IMAP4_SSL and IMAP4_stream, which encapsulate a connection to an IMAP4 server and implement a large subset of the IMAP4rev1 cli..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/imaplib.py This module defines three classes, IMAP4, IMAP4_SSL and IMAP4_stream, which encapsulate a connection to an IMAP4 server and implement a large subset of the IMAP4rev1 cli..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>imaplib — IMAP4 protocol client &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="smtplib — SMTP protocol client" href="smtplib.html" />
    <link rel="prev" title="poplib — POP3 protocol client" href="poplib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/imaplib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a><ul>
<li><a class="reference internal" href="#imap4-objects">IMAP4 Objects</a></li>
<li><a class="reference internal" href="#imap4-example">IMAP4 Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="poplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="smtplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/imaplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="smtplib.html" title="smtplib — SMTP protocol client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="poplib.html" title="poplib — POP3 protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-imaplib">
<span id="imaplib-imap4-protocol-client"></span><h1><a class="reference internal" href="#module-imaplib" title="imaplib: IMAP4 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code></a> — IMAP4 protocol client<a class="headerlink" href="#module-imaplib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/imaplib.py">Lib/imaplib.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module defines three classes, <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a>, <a class="reference internal" href="#imaplib.IMAP4_SSL" title="imaplib.IMAP4_SSL"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4_SSL</span></code></a> and
<a class="reference internal" href="#imaplib.IMAP4_stream" title="imaplib.IMAP4_stream"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4_stream</span></code></a>, which encapsulate a connection to an IMAP4 server and
implement a large subset of the IMAP4rev1 client protocol as defined in
<span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2060.html"><strong>RFC 2060</strong></a>. It is backward compatible with IMAP4 (<span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc1730.html"><strong>RFC 1730</strong></a>) servers, but
note that the <code class="docutils literal notranslate"><span class="pre">STATUS</span></code> command is not supported in IMAP4.</p>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>Three classes are provided by the <a class="reference internal" href="#module-imaplib" title="imaplib: IMAP4 protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code></a> module, <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> is the
base class:</p>
<dl class="py class">
<dt class="sig sig-object py" id="imaplib.IMAP4">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">IMAP4</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">IMAP4_PORT</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4" title="Link to this definition">¶</a></dt>
<dd><p>This class implements the actual IMAP4 protocol.  The connection is created and
protocol version (IMAP4 or IMAP4rev1) is determined when the instance is
initialized. If <em>host</em> is not specified, <code class="docutils literal notranslate"><span class="pre">''</span></code> (the local host) is used. If
<em>port</em> is omitted, the standard IMAP4 port (143) is used. The optional <em>timeout</em>
parameter specifies a timeout in seconds for the connection attempt.
If timeout is not given or is None, the global default socket timeout is used.</p>
<p>The <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> class supports the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  When used
like this, the IMAP4 <code class="docutils literal notranslate"><span class="pre">LOGOUT</span></code> command is issued automatically when the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement exits.  E.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">imaplib</span> <span class="kn">import</span> <span class="n">IMAP4</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">IMAP4</span><span class="p">(</span><span class="s2">&quot;domain.org&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">M</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">M</span><span class="o">.</span><span class="n">noop</span><span class="p">()</span>
<span class="gp">...</span>
<span class="go">(&#39;OK&#39;, [b&#39;Nothing Accomplished. d25if65hy903weo.87&#39;])</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Support for the <a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The optional <em>timeout</em> parameter was added.</p>
</div>
</dd></dl>

<p>Three exceptions are defined as attributes of the <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> class:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="imaplib.IMAP4.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#imaplib.IMAP4.error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised on any errors.  The reason for the exception is passed to the
constructor as a string.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="imaplib.IMAP4.abort">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">abort</span></span><a class="headerlink" href="#imaplib.IMAP4.abort" title="Link to this definition">¶</a></dt>
<dd><p>IMAP4 server errors cause this exception to be raised.  This is a sub-class of
<a class="reference internal" href="#imaplib.IMAP4.error" title="imaplib.IMAP4.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IMAP4.error</span></code></a>.  Note that closing the instance and instantiating a new one
will usually allow recovery from this exception.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="imaplib.IMAP4.readonly">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">readonly</span></span><a class="headerlink" href="#imaplib.IMAP4.readonly" title="Link to this definition">¶</a></dt>
<dd><p>This exception is raised when a writable mailbox has its status changed by the
server.  This is a sub-class of <a class="reference internal" href="#imaplib.IMAP4.error" title="imaplib.IMAP4.error"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IMAP4.error</span></code></a>.  Some other client now has
write permission, and the mailbox will need to be re-opened to re-obtain write
permission.</p>
</dd></dl>

<p>There’s also a subclass for secure connections:</p>
<dl class="py class">
<dt class="sig sig-object py" id="imaplib.IMAP4_SSL">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">IMAP4_SSL</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">IMAP4_SSL_PORT</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ssl_context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4_SSL" title="Link to this definition">¶</a></dt>
<dd><p>This is a subclass derived from <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> that connects over an SSL
encrypted socket (to use this class you need a socket module that was compiled
with SSL support).  If <em>host</em> is not specified, <code class="docutils literal notranslate"><span class="pre">''</span></code> (the local host) is used.
If <em>port</em> is omitted, the standard IMAP4-over-SSL port (993) is used.
<em>ssl_context</em> is a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object which allows bundling
SSL configuration options, certificates and private keys into a single
(potentially long-lived) structure.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for
best practices.</p>
<p>The optional <em>timeout</em> parameter specifies a timeout in seconds for the
connection attempt. If timeout is not given or is None, the global default
socket timeout is used.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>ssl_context</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The class now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The optional <em>timeout</em> parameter was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>The deprecated <em>keyfile</em> and <em>certfile</em> parameters have been removed.</p>
</div>
</dd></dl>

<p>The second subclass allows for connections created by a child process:</p>
<dl class="py class">
<dt class="sig sig-object py" id="imaplib.IMAP4_stream">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">IMAP4_stream</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">command</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4_stream" title="Link to this definition">¶</a></dt>
<dd><p>This is a subclass derived from <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> that connects to the
<code class="docutils literal notranslate"><span class="pre">stdin/stdout</span></code> file descriptors created by passing <em>command</em> to
<code class="docutils literal notranslate"><span class="pre">subprocess.Popen()</span></code>.</p>
</dd></dl>

<p>The following utility functions are defined:</p>
<dl class="py function">
<dt class="sig sig-object py" id="imaplib.Internaldate2tuple">
<span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">Internaldate2tuple</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">datestr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.Internaldate2tuple" title="Link to this definition">¶</a></dt>
<dd><p>Parse an IMAP4 <code class="docutils literal notranslate"><span class="pre">INTERNALDATE</span></code> string and return corresponding local
time.  The return value is a <a class="reference internal" href="time.html#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">time.struct_time</span></code></a> tuple or
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the string has wrong format.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="imaplib.Int2AP">
<span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">Int2AP</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">num</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.Int2AP" title="Link to this definition">¶</a></dt>
<dd><p>Converts an integer into a bytes representation using characters from the set
[<code class="docutils literal notranslate"><span class="pre">A</span></code> .. <code class="docutils literal notranslate"><span class="pre">P</span></code>].</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="imaplib.ParseFlags">
<span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">ParseFlags</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flagstr</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.ParseFlags" title="Link to this definition">¶</a></dt>
<dd><p>Converts an IMAP4 <code class="docutils literal notranslate"><span class="pre">FLAGS</span></code> response to a tuple of individual flags.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="imaplib.Time2Internaldate">
<span class="sig-prename descclassname"><span class="pre">imaplib.</span></span><span class="sig-name descname"><span class="pre">Time2Internaldate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">date_time</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.Time2Internaldate" title="Link to this definition">¶</a></dt>
<dd><p>Convert <em>date_time</em> to an IMAP4 <code class="docutils literal notranslate"><span class="pre">INTERNALDATE</span></code> representation.
The return value is a string in the form: <code class="docutils literal notranslate"><span class="pre">&quot;DD-Mmm-YYYY</span> <span class="pre">HH:MM:SS</span>
<span class="pre">+HHMM&quot;</span></code> (including double-quotes).  The <em>date_time</em> argument can
be a number (int or float) representing seconds since epoch (as
returned by <a class="reference internal" href="time.html#time.time" title="time.time"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.time()</span></code></a>), a 9-tuple representing local time
an instance of <a class="reference internal" href="time.html#time.struct_time" title="time.struct_time"><code class="xref py py-class docutils literal notranslate"><span class="pre">time.struct_time</span></code></a> (as returned by
<a class="reference internal" href="time.html#time.localtime" title="time.localtime"><code class="xref py py-func docutils literal notranslate"><span class="pre">time.localtime()</span></code></a>), an aware instance of
<a class="reference internal" href="datetime.html#datetime.datetime" title="datetime.datetime"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.datetime</span></code></a>, or a double-quoted string.  In the last
case, it is assumed to already be in the correct format.</p>
</dd></dl>

<p>Note that IMAP4 message numbers change as the mailbox changes; in particular,
after an <code class="docutils literal notranslate"><span class="pre">EXPUNGE</span></code> command performs deletions the remaining messages are
renumbered. So it is highly advisable to use UIDs instead, with the UID command.</p>
<p>At the end of the module, there is a test section that contains a more extensive
example of usage.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>Documents describing the protocol, sources for servers
implementing it, by the University of Washington’s IMAP Information Center
can all be found at (<strong>Source Code</strong>) <a class="reference external" href="https://github.com/uw-imap/imap">https://github.com/uw-imap/imap</a> (<strong>Not Maintained</strong>).</p>
</div>
<section id="imap4-objects">
<span id="id1"></span><h2>IMAP4 Objects<a class="headerlink" href="#imap4-objects" title="Link to this heading">¶</a></h2>
<p>All IMAP4rev1 commands are represented by methods of the same name, either
uppercase or lowercase.</p>
<p>All arguments to commands are converted to strings, except for <code class="docutils literal notranslate"><span class="pre">AUTHENTICATE</span></code>,
and the last argument to <code class="docutils literal notranslate"><span class="pre">APPEND</span></code> which is passed as an IMAP4 literal.  If
necessary (the string contains IMAP4 protocol-sensitive characters and isn’t
enclosed with either parentheses or double quotes) each string is quoted.
However, the <em>password</em> argument to the <code class="docutils literal notranslate"><span class="pre">LOGIN</span></code> command is always quoted. If
you want to avoid having an argument string quoted (eg: the <em>flags</em> argument to
<code class="docutils literal notranslate"><span class="pre">STORE</span></code>) then enclose the string in parentheses (eg: <code class="docutils literal notranslate"><span class="pre">r'(\Deleted)'</span></code>).</p>
<p>Each command returns a tuple: <code class="docutils literal notranslate"><span class="pre">(type,</span> <span class="pre">[data,</span> <span class="pre">...])</span></code> where <em>type</em> is usually
<code class="docutils literal notranslate"><span class="pre">'OK'</span></code> or <code class="docutils literal notranslate"><span class="pre">'NO'</span></code>, and <em>data</em> is either the text from the command response,
or mandated results from the command. Each <em>data</em> is either a <code class="docutils literal notranslate"><span class="pre">bytes</span></code>, or a
tuple. If a tuple, then the first part is the header of the response, and the
second part contains the data (ie: ‘literal’ value).</p>
<p>The <em>message_set</em> options to commands below is a string specifying one or more
messages to be acted upon.  It may be a simple message number (<code class="docutils literal notranslate"><span class="pre">'1'</span></code>), a range
of message numbers (<code class="docutils literal notranslate"><span class="pre">'2:4'</span></code>), or a group of non-contiguous ranges separated by
commas (<code class="docutils literal notranslate"><span class="pre">'1:3,6:9'</span></code>).  A range can contain an asterisk to indicate an infinite
upper bound (<code class="docutils literal notranslate"><span class="pre">'3:*'</span></code>).</p>
<p>An <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> instance has the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.append">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">append</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flags</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">date_time</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.append" title="Link to this definition">¶</a></dt>
<dd><p>Append <em>message</em> to named mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.authenticate">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">authenticate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mechanism</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">authobject</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.authenticate" title="Link to this definition">¶</a></dt>
<dd><p>Authenticate command — requires response processing.</p>
<p><em>mechanism</em> specifies which authentication mechanism is to be used - it should
appear in the instance variable <code class="docutils literal notranslate"><span class="pre">capabilities</span></code> in the form <code class="docutils literal notranslate"><span class="pre">AUTH=mechanism</span></code>.</p>
<p><em>authobject</em> must be a callable object:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="n">authobject</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
</pre></div>
</div>
<p>It will be called to process server continuation responses; the <em>response</em>
argument it is passed will be <code class="docutils literal notranslate"><span class="pre">bytes</span></code>.  It should return <code class="docutils literal notranslate"><span class="pre">bytes</span></code> <em>data</em>
that will be base64 encoded and sent to the server.  It should return
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the client abort response <code class="docutils literal notranslate"><span class="pre">*</span></code> should be sent instead.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>string usernames and passwords are now encoded to <code class="docutils literal notranslate"><span class="pre">utf-8</span></code> instead of
being limited to ASCII.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.check">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">check</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.check" title="Link to this definition">¶</a></dt>
<dd><p>Checkpoint mailbox on server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.close">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.close" title="Link to this definition">¶</a></dt>
<dd><p>Close currently selected mailbox. Deleted messages are removed from writable
mailbox. This is the recommended command before <code class="docutils literal notranslate"><span class="pre">LOGOUT</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.copy">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">copy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_set</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.copy" title="Link to this definition">¶</a></dt>
<dd><p>Copy <em>message_set</em> messages onto end of <em>new_mailbox</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.create">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">create</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.create" title="Link to this definition">¶</a></dt>
<dd><p>Create new mailbox named <em>mailbox</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.delete">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">delete</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.delete" title="Link to this definition">¶</a></dt>
<dd><p>Delete old mailbox named <em>mailbox</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.deleteacl">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">deleteacl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">who</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.deleteacl" title="Link to this definition">¶</a></dt>
<dd><p>Delete the ACLs (remove any rights) set for who on mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.enable">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">enable</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">capability</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.enable" title="Link to this definition">¶</a></dt>
<dd><p>Enable <em>capability</em> (see <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc5161.html"><strong>RFC 5161</strong></a>).  Most capabilities do not need to be
enabled.  Currently only the <code class="docutils literal notranslate"><span class="pre">UTF8=ACCEPT</span></code> capability is supported
(see <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6855.html"><strong>RFC 6855</strong></a>).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>The <a class="reference internal" href="#imaplib.IMAP4.enable" title="imaplib.IMAP4.enable"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enable()</span></code></a> method itself, and <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc6855.html"><strong>RFC 6855</strong></a> support.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.expunge">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">expunge</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.expunge" title="Link to this definition">¶</a></dt>
<dd><p>Permanently remove deleted items from selected mailbox. Generates an <code class="docutils literal notranslate"><span class="pre">EXPUNGE</span></code>
response for each deleted message. Returned data contains a list of <code class="docutils literal notranslate"><span class="pre">EXPUNGE</span></code>
message numbers in order received.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.fetch">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">fetch</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_set</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message_parts</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.fetch" title="Link to this definition">¶</a></dt>
<dd><p>Fetch (parts of) messages.  <em>message_parts</em> should be a string of message part
names enclosed within parentheses, eg: <code class="docutils literal notranslate"><span class="pre">&quot;(UID</span> <span class="pre">BODY[TEXT])&quot;</span></code>.  Returned data
are tuples of message part envelope and data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.getacl">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">getacl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.getacl" title="Link to this definition">¶</a></dt>
<dd><p>Get the <code class="docutils literal notranslate"><span class="pre">ACL</span></code>s for <em>mailbox</em>. The method is non-standard, but is supported
by the <code class="docutils literal notranslate"><span class="pre">Cyrus</span></code> server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.getannotation">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">getannotation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attribute</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.getannotation" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve the specified <code class="docutils literal notranslate"><span class="pre">ANNOTATION</span></code>s for <em>mailbox</em>. The method is
non-standard, but is supported by the <code class="docutils literal notranslate"><span class="pre">Cyrus</span></code> server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.getquota">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">getquota</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.getquota" title="Link to this definition">¶</a></dt>
<dd><p>Get the <code class="docutils literal notranslate"><span class="pre">quota</span></code> <em>root</em>’s resource usage and limits. This method is part of the
IMAP4 QUOTA extension defined in rfc2087.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.getquotaroot">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">getquotaroot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.getquotaroot" title="Link to this definition">¶</a></dt>
<dd><p>Get the list of <code class="docutils literal notranslate"><span class="pre">quota</span></code> <code class="docutils literal notranslate"><span class="pre">roots</span></code> for the named <em>mailbox</em>. This method is part
of the IMAP4 QUOTA extension defined in rfc2087.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.list">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">list</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">directory</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span></em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.list" title="Link to this definition">¶</a></dt>
<dd><p>List mailbox names in <em>directory</em> matching <em>pattern</em>.  <em>directory</em> defaults to
the top-level mail folder, and <em>pattern</em> defaults to match anything.  Returned
data contains a list of <code class="docutils literal notranslate"><span class="pre">LIST</span></code> responses.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.login">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">login</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.login" title="Link to this definition">¶</a></dt>
<dd><p>Identify the client using a plaintext password. The <em>password</em> will be quoted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.login_cram_md5">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">login_cram_md5</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">password</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.login_cram_md5" title="Link to this definition">¶</a></dt>
<dd><p>Force use of <code class="docutils literal notranslate"><span class="pre">CRAM-MD5</span></code> authentication when identifying the client to protect
the password.  Will only work if the server <code class="docutils literal notranslate"><span class="pre">CAPABILITY</span></code> response includes the
phrase <code class="docutils literal notranslate"><span class="pre">AUTH=CRAM-MD5</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.logout">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">logout</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.logout" title="Link to this definition">¶</a></dt>
<dd><p>Shutdown connection to server. Returns server <code class="docutils literal notranslate"><span class="pre">BYE</span></code> response.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The method no longer ignores silently arbitrary exceptions.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.lsub">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">lsub</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">directory</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'&quot;&quot;'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pattern</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'*'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.lsub" title="Link to this definition">¶</a></dt>
<dd><p>List subscribed mailbox names in directory matching pattern. <em>directory</em>
defaults to the top level directory and <em>pattern</em> defaults to match any mailbox.
Returned data are tuples of message part envelope and data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.myrights">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">myrights</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.myrights" title="Link to this definition">¶</a></dt>
<dd><p>Show my ACLs for a mailbox (i.e. the rights that I have on mailbox).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.namespace">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">namespace</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.namespace" title="Link to this definition">¶</a></dt>
<dd><p>Returns IMAP namespaces as defined in <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2342.html"><strong>RFC 2342</strong></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.noop">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">noop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.noop" title="Link to this definition">¶</a></dt>
<dd><p>Send <code class="docutils literal notranslate"><span class="pre">NOOP</span></code> to server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.open">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">port</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.open" title="Link to this definition">¶</a></dt>
<dd><p>Opens socket to <em>port</em> at <em>host</em>. The optional <em>timeout</em> parameter
specifies a timeout in seconds for the connection attempt.
If timeout is not given or is None, the global default socket timeout
is used. Also note that if the <em>timeout</em> parameter is set to be zero,
it will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-class docutils literal notranslate"><span class="pre">ValueError</span></code></a> to reject creating a non-blocking socket.
This method is implicitly called by the <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a> constructor.
The connection objects established by this method will be used in
the <a class="reference internal" href="#imaplib.IMAP4.read" title="imaplib.IMAP4.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">IMAP4.read()</span></code></a>, <a class="reference internal" href="#imaplib.IMAP4.readline" title="imaplib.IMAP4.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">IMAP4.readline()</span></code></a>, <a class="reference internal" href="#imaplib.IMAP4.send" title="imaplib.IMAP4.send"><code class="xref py py-meth docutils literal notranslate"><span class="pre">IMAP4.send()</span></code></a>,
and <a class="reference internal" href="#imaplib.IMAP4.shutdown" title="imaplib.IMAP4.shutdown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">IMAP4.shutdown()</span></code></a> methods. You may override this method.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">imaplib.open</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">host</span></code>, <code class="docutils literal notranslate"><span class="pre">port</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>The <em>timeout</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.partial">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">partial</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_num</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message_part</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">length</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.partial" title="Link to this definition">¶</a></dt>
<dd><p>Fetch truncated part of a message. Returned data is a tuple of message part
envelope and data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.proxyauth">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">proxyauth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.proxyauth" title="Link to this definition">¶</a></dt>
<dd><p>Assume authentication as <em>user</em>. Allows an authorised administrator to proxy
into any user’s mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.read">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.read" title="Link to this definition">¶</a></dt>
<dd><p>Reads <em>size</em> bytes from the remote server. You may override this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.readline">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">readline</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.readline" title="Link to this definition">¶</a></dt>
<dd><p>Reads one line from the remote server. You may override this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.recent">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">recent</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.recent" title="Link to this definition">¶</a></dt>
<dd><p>Prompt server for an update. Returned data is <code class="docutils literal notranslate"><span class="pre">None</span></code> if no new messages, else
value of <code class="docutils literal notranslate"><span class="pre">RECENT</span></code> response.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.rename">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">rename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">oldmailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newmailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.rename" title="Link to this definition">¶</a></dt>
<dd><p>Rename mailbox named <em>oldmailbox</em> to <em>newmailbox</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.response">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">response</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">code</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.response" title="Link to this definition">¶</a></dt>
<dd><p>Return data for response <em>code</em> if received, or <code class="docutils literal notranslate"><span class="pre">None</span></code>. Returns the given
code, instead of the usual type.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.search">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">charset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">criterion</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.search" title="Link to this definition">¶</a></dt>
<dd><p>Search mailbox for matching messages.  <em>charset</em> may be <code class="docutils literal notranslate"><span class="pre">None</span></code>, in which case
no <code class="docutils literal notranslate"><span class="pre">CHARSET</span></code> will be specified in the request to the server.  The IMAP
protocol requires that at least one criterion be specified; an exception will be
raised when the server returns an error.  <em>charset</em> must be <code class="docutils literal notranslate"><span class="pre">None</span></code> if
the <code class="docutils literal notranslate"><span class="pre">UTF8=ACCEPT</span></code> capability was enabled using the <a class="reference internal" href="#imaplib.IMAP4.enable" title="imaplib.IMAP4.enable"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enable()</span></code></a>
command.</p>
<p>Example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># M is a connected IMAP4 instance...</span>
<span class="n">typ</span><span class="p">,</span> <span class="n">msgnums</span> <span class="o">=</span> <span class="n">M</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="s1">&#39;FROM&#39;</span><span class="p">,</span> <span class="s1">&#39;&quot;LDJ&quot;&#39;</span><span class="p">)</span>

<span class="c1"># or:</span>
<span class="n">typ</span><span class="p">,</span> <span class="n">msgnums</span> <span class="o">=</span> <span class="n">M</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="s1">&#39;(FROM &quot;LDJ&quot;)&#39;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.select">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">select</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'INBOX'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">readonly</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.select" title="Link to this definition">¶</a></dt>
<dd><p>Select a mailbox. Returned data is the count of messages in <em>mailbox</em>
(<code class="docutils literal notranslate"><span class="pre">EXISTS</span></code> response).  The default <em>mailbox</em> is <code class="docutils literal notranslate"><span class="pre">'INBOX'</span></code>.  If the <em>readonly</em>
flag is set, modifications to the mailbox are not allowed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.send">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">send</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.send" title="Link to this definition">¶</a></dt>
<dd><p>Sends <code class="docutils literal notranslate"><span class="pre">data</span></code> to the remote server. You may override this method.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">imaplib.send</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">self</span></code>, <code class="docutils literal notranslate"><span class="pre">data</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.setacl">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">setacl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">who</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">what</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.setacl" title="Link to this definition">¶</a></dt>
<dd><p>Set an <code class="docutils literal notranslate"><span class="pre">ACL</span></code> for <em>mailbox</em>. The method is non-standard, but is supported by
the <code class="docutils literal notranslate"><span class="pre">Cyrus</span></code> server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.setannotation">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">setannotation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">entry</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attribute</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.setannotation" title="Link to this definition">¶</a></dt>
<dd><p>Set <code class="docutils literal notranslate"><span class="pre">ANNOTATION</span></code>s for <em>mailbox</em>. The method is non-standard, but is
supported by the <code class="docutils literal notranslate"><span class="pre">Cyrus</span></code> server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.setquota">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">setquota</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limits</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.setquota" title="Link to this definition">¶</a></dt>
<dd><p>Set the <code class="docutils literal notranslate"><span class="pre">quota</span></code> <em>root</em>’s resource <em>limits</em>. This method is part of the IMAP4
QUOTA extension defined in rfc2087.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.shutdown">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">shutdown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.shutdown" title="Link to this definition">¶</a></dt>
<dd><p>Close connection established in <code class="docutils literal notranslate"><span class="pre">open</span></code>.  This method is implicitly
called by <a class="reference internal" href="#imaplib.IMAP4.logout" title="imaplib.IMAP4.logout"><code class="xref py py-meth docutils literal notranslate"><span class="pre">IMAP4.logout()</span></code></a>.  You may override this method.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.socket">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">socket</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.socket" title="Link to this definition">¶</a></dt>
<dd><p>Returns socket instance used to connect to server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.sort">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">sort</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sort_criteria</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search_criterion</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.sort" title="Link to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">sort</span></code> command is a variant of <code class="docutils literal notranslate"><span class="pre">search</span></code> with sorting semantics for the
results.  Returned data contains a space separated list of matching message
numbers.</p>
<p>Sort has two arguments before the <em>search_criterion</em> argument(s); a
parenthesized list of <em>sort_criteria</em>, and the searching <em>charset</em>.  Note that
unlike <code class="docutils literal notranslate"><span class="pre">search</span></code>, the searching <em>charset</em> argument is mandatory.  There is also
a <code class="docutils literal notranslate"><span class="pre">uid</span> <span class="pre">sort</span></code> command which corresponds to <code class="docutils literal notranslate"><span class="pre">sort</span></code> the way that <code class="docutils literal notranslate"><span class="pre">uid</span> <span class="pre">search</span></code>
corresponds to <code class="docutils literal notranslate"><span class="pre">search</span></code>.  The <code class="docutils literal notranslate"><span class="pre">sort</span></code> command first searches the mailbox for
messages that match the given searching criteria using the charset argument for
the interpretation of strings in the searching criteria.  It then returns the
numbers of matching messages.</p>
<p>This is an <code class="docutils literal notranslate"><span class="pre">IMAP4rev1</span></code> extension command.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.starttls">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">starttls</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ssl_context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.starttls" title="Link to this definition">¶</a></dt>
<dd><p>Send a <code class="docutils literal notranslate"><span class="pre">STARTTLS</span></code> command.  The <em>ssl_context</em> argument is optional
and should be a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> object.  This will enable
encryption on the IMAP connection.  Please read <a class="reference internal" href="ssl.html#ssl-security"><span class="std std-ref">Security considerations</span></a> for
best practices.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The method now supports hostname check with
<a class="reference internal" href="ssl.html#ssl.SSLContext.check_hostname" title="ssl.SSLContext.check_hostname"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ssl.SSLContext.check_hostname</span></code></a> and <em>Server Name Indication</em> (see
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a>).</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.status">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">status</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.status" title="Link to this definition">¶</a></dt>
<dd><p>Request named status conditions for <em>mailbox</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.store">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">store</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message_set</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">command</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">flag_list</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.store" title="Link to this definition">¶</a></dt>
<dd><p>Alters flag dispositions for messages in mailbox.  <em>command</em> is specified by
section 6.4.6 of <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2060.html"><strong>RFC 2060</strong></a> as being one of “FLAGS”, “+FLAGS”, or “-FLAGS”,
optionally with a suffix of “.SILENT”.</p>
<p>For example, to set the delete flag on all messages:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">typ</span><span class="p">,</span> <span class="n">data</span> <span class="o">=</span> <span class="n">M</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="s1">&#39;ALL&#39;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">num</span> <span class="ow">in</span> <span class="n">data</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">split</span><span class="p">():</span>
   <span class="n">M</span><span class="o">.</span><span class="n">store</span><span class="p">(</span><span class="n">num</span><span class="p">,</span> <span class="s1">&#39;+FLAGS&#39;</span><span class="p">,</span> <span class="s1">&#39;</span><span class="se">\\</span><span class="s1">Deleted&#39;</span><span class="p">)</span>
<span class="n">M</span><span class="o">.</span><span class="n">expunge</span><span class="p">()</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Creating flags containing ‘]’ (for example: “[test]”) violates
<span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc3501.html"><strong>RFC 3501</strong></a> (the IMAP protocol).  However, imaplib has historically
allowed creation of such tags, and popular IMAP servers, such as Gmail,
accept and produce such flags.  There are non-Python programs which also
create such tags.  Although it is an RFC violation and IMAP clients and
servers are supposed to be strict, imaplib nonetheless continues to allow
such tags to be created for backward compatibility reasons, and as of
Python 3.6, handles them if they are sent from the server, since this
improves real-world compatibility.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.subscribe">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">subscribe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.subscribe" title="Link to this definition">¶</a></dt>
<dd><p>Subscribe to new mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.thread">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">thread</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">threading_algorithm</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">charset</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search_criterion</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.thread" title="Link to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">thread</span></code> command is a variant of <code class="docutils literal notranslate"><span class="pre">search</span></code> with threading semantics for
the results.  Returned data contains a space separated list of thread members.</p>
<p>Thread members consist of zero or more messages numbers, delimited by spaces,
indicating successive parent and child.</p>
<p>Thread has two arguments before the <em>search_criterion</em> argument(s); a
<em>threading_algorithm</em>, and the searching <em>charset</em>.  Note that unlike
<code class="docutils literal notranslate"><span class="pre">search</span></code>, the searching <em>charset</em> argument is mandatory.  There is also a
<code class="docutils literal notranslate"><span class="pre">uid</span> <span class="pre">thread</span></code> command which corresponds to <code class="docutils literal notranslate"><span class="pre">thread</span></code> the way that <code class="docutils literal notranslate"><span class="pre">uid</span>
<span class="pre">search</span></code> corresponds to <code class="docutils literal notranslate"><span class="pre">search</span></code>.  The <code class="docutils literal notranslate"><span class="pre">thread</span></code> command first searches the
mailbox for messages that match the given searching criteria using the <em>charset</em>
argument for the interpretation of strings in the searching criteria. It then
returns the matching messages threaded according to the specified threading
algorithm.</p>
<p>This is an <code class="docutils literal notranslate"><span class="pre">IMAP4rev1</span></code> extension command.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.uid">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">uid</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">command</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arg</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.uid" title="Link to this definition">¶</a></dt>
<dd><p>Execute command args with messages identified by UID, rather than message
number.  Returns response appropriate to command.  At least one argument must be
supplied; if none are provided, the server will return an error and an exception
will be raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.unsubscribe">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">unsubscribe</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mailbox</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.unsubscribe" title="Link to this definition">¶</a></dt>
<dd><p>Unsubscribe from old mailbox.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.unselect">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">unselect</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.unselect" title="Link to this definition">¶</a></dt>
<dd><p><a class="reference internal" href="#imaplib.IMAP4.unselect" title="imaplib.IMAP4.unselect"><code class="xref py py-meth docutils literal notranslate"><span class="pre">imaplib.IMAP4.unselect()</span></code></a> frees server’s resources associated with the
selected mailbox and returns the server to the authenticated
state. This command performs the same actions as <a class="reference internal" href="#imaplib.IMAP4.close" title="imaplib.IMAP4.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">imaplib.IMAP4.close()</span></code></a>, except
that no messages are permanently removed from the currently
selected mailbox.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="imaplib.IMAP4.xatom">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">xatom</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#imaplib.IMAP4.xatom" title="Link to this definition">¶</a></dt>
<dd><p>Allow simple extension commands notified by server in <code class="docutils literal notranslate"><span class="pre">CAPABILITY</span></code> response.</p>
</dd></dl>

<p>The following attributes are defined on instances of <a class="reference internal" href="#imaplib.IMAP4" title="imaplib.IMAP4"><code class="xref py py-class docutils literal notranslate"><span class="pre">IMAP4</span></code></a>:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="imaplib.IMAP4.PROTOCOL_VERSION">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">PROTOCOL_VERSION</span></span><a class="headerlink" href="#imaplib.IMAP4.PROTOCOL_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>The most recent supported protocol in the <code class="docutils literal notranslate"><span class="pre">CAPABILITY</span></code> response from the
server.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="imaplib.IMAP4.debug">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">debug</span></span><a class="headerlink" href="#imaplib.IMAP4.debug" title="Link to this definition">¶</a></dt>
<dd><p>Integer value to control debugging output.  The initialize value is taken from
the module variable <code class="docutils literal notranslate"><span class="pre">Debug</span></code>.  Values greater than three trace each command.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="imaplib.IMAP4.utf8_enabled">
<span class="sig-prename descclassname"><span class="pre">IMAP4.</span></span><span class="sig-name descname"><span class="pre">utf8_enabled</span></span><a class="headerlink" href="#imaplib.IMAP4.utf8_enabled" title="Link to this definition">¶</a></dt>
<dd><p>Boolean value that is normally <code class="docutils literal notranslate"><span class="pre">False</span></code>, but is set to <code class="docutils literal notranslate"><span class="pre">True</span></code> if an
<a class="reference internal" href="#imaplib.IMAP4.enable" title="imaplib.IMAP4.enable"><code class="xref py py-meth docutils literal notranslate"><span class="pre">enable()</span></code></a> command is successfully issued for the <code class="docutils literal notranslate"><span class="pre">UTF8=ACCEPT</span></code>
capability.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

</section>
<section id="imap4-example">
<span id="id2"></span><h2>IMAP4 Example<a class="headerlink" href="#imap4-example" title="Link to this heading">¶</a></h2>
<p>Here is a minimal example (without error checking) that opens a mailbox and
retrieves and prints all messages:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">getpass</span><span class="o">,</span> <span class="nn">imaplib</span>

<span class="n">M</span> <span class="o">=</span> <span class="n">imaplib</span><span class="o">.</span><span class="n">IMAP4</span><span class="p">(</span><span class="n">host</span><span class="o">=</span><span class="s1">&#39;example.org&#39;</span><span class="p">)</span>
<span class="n">M</span><span class="o">.</span><span class="n">login</span><span class="p">(</span><span class="n">getpass</span><span class="o">.</span><span class="n">getuser</span><span class="p">(),</span> <span class="n">getpass</span><span class="o">.</span><span class="n">getpass</span><span class="p">())</span>
<span class="n">M</span><span class="o">.</span><span class="n">select</span><span class="p">()</span>
<span class="n">typ</span><span class="p">,</span> <span class="n">data</span> <span class="o">=</span> <span class="n">M</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="s1">&#39;ALL&#39;</span><span class="p">)</span>
<span class="k">for</span> <span class="n">num</span> <span class="ow">in</span> <span class="n">data</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">split</span><span class="p">():</span>
    <span class="n">typ</span><span class="p">,</span> <span class="n">data</span> <span class="o">=</span> <span class="n">M</span><span class="o">.</span><span class="n">fetch</span><span class="p">(</span><span class="n">num</span><span class="p">,</span> <span class="s1">&#39;(RFC822)&#39;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Message </span><span class="si">%s</span><span class="se">\n</span><span class="si">%s</span><span class="se">\n</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="p">(</span><span class="n">num</span><span class="p">,</span> <span class="n">data</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">]))</span>
<span class="n">M</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
<span class="n">M</span><span class="o">.</span><span class="n">logout</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a><ul>
<li><a class="reference internal" href="#imap4-objects">IMAP4 Objects</a></li>
<li><a class="reference internal" href="#imap4-example">IMAP4 Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="poplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">poplib</span></code> — POP3 protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="smtplib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/imaplib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="smtplib.html" title="smtplib — SMTP protocol client"
             >next</a> |</li>
        <li class="right" >
          <a href="poplib.html" title="poplib — POP3 protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">imaplib</span></code> — IMAP4 protocol client</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>