<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.CashierName">
        <div t-att-class="cssClass" class="btn btn-light border-0 rounded-0">
            <span class="cashier-name d-flex align-items-center gap-1"><img t-att-src="avatar" t-att-alt="username" class="avatar rounded-3"/> <span t-if="!ui.isSmall" t-esc="username" class="username"/></span>
        </div>
    </t>

</templates>
