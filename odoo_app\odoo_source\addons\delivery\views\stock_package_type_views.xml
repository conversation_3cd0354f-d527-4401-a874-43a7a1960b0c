<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="stock_package_type_form_delivery">
        <field name="name">stock.package.type.form.delivery</field>
        <field name="model">stock.package.type</field>
        <field name="inherit_id" ref="stock.stock_package_type_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='delivery']" position="inside">
                <group>
                    <field name="package_carrier_type"/>
                    <field name="shipper_package_code"/>
                </group>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="stock_package_type_tree_delivery">
        <field name="name">stock.package.type.tree.delivery</field>
        <field name="model">stock.package.type</field>
        <field name="inherit_id" ref="stock.stock_package_type_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='sequence']" position="after">
                <field name="package_carrier_type"/>
            </xpath>
            <xpath expr="//field[@name='max_weight']" position="after">
                <field name="shipper_package_code"/>
            </xpath>
        </field>
    </record>

</odoo>
