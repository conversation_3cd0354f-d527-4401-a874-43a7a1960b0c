<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_picking_type_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.type.form.inherit</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock_picking_batch.view_picking_type_form_inherit"/>
        <field name="arch" type="xml">
            <div name="batch_contact" position="after">
                <span attrs="{'invisible':[('auto_batch', '=', False)]}"/>
                <div name="batch_carrier" class="o_row" attrs="{'invisible':[('auto_batch', '=', False)]}">
                    <field name="batch_group_by_carrier"/>
                    <label for="batch_group_by_carrier"/>
                </div>
            </div>
            <field name="batch_auto_confirm" position="before">
                <label for="batch_max_weight" attrs="{'invisible': [('auto_batch', '=', False)]}"/>
                <div class="o_row" name="batch_max_weight" attrs="{'invisible': [('auto_batch', '=', False)]}">
                    <field name="batch_max_weight"/>
                    <span><field name="weight_uom_name"/></span>
                </div>
            </field>
        </field>
    </record>
</odoo>
