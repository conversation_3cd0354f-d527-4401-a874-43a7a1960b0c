<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="zipfile — Work with ZIP archives" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/zipfile.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/zipfile/ The ZIP file format is a common archive and compression standard. This module provides tools to create, read, write, append, and list a ZIP file. Any advanced use of this ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/zipfile/ The ZIP file format is a common archive and compression standard. This module provides tools to create, read, write, append, and list a ZIP file. Any advanced use of this ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>zipfile — Work with ZIP archives &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="tarfile — Read and write tar archive files" href="tarfile.html" />
    <link rel="prev" title="lzma — Compression using the LZMA algorithm" href="lzma.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/zipfile.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a><ul>
<li><a class="reference internal" href="#zipfile-objects">ZipFile Objects</a></li>
<li><a class="reference internal" href="#path-objects">Path Objects</a></li>
<li><a class="reference internal" href="#pyzipfile-objects">PyZipFile Objects</a></li>
<li><a class="reference internal" href="#zipinfo-objects">ZipInfo Objects</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#decompression-pitfalls">Decompression pitfalls</a><ul>
<li><a class="reference internal" href="#from-file-itself">From file itself</a></li>
<li><a class="reference internal" href="#file-system-limitations">File System limitations</a></li>
<li><a class="reference internal" href="#resources-limitations">Resources limitations</a></li>
<li><a class="reference internal" href="#interruption">Interruption</a></li>
<li><a class="reference internal" href="#default-behaviors-of-extraction">Default behaviors of extraction</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="lzma.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tarfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zipfile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tarfile.html" title="tarfile — Read and write tar archive files"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="lzma.html" title="lzma — Compression using the LZMA algorithm"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" accesskey="U">Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-zipfile">
<span id="zipfile-work-with-zip-archives"></span><h1><a class="reference internal" href="#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> — Work with ZIP archives<a class="headerlink" href="#module-zipfile" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/zipfile/">Lib/zipfile/</a></p>
<hr class="docutils" />
<p>The ZIP file format is a common archive and compression standard. This module
provides tools to create, read, write, append, and list a ZIP file.  Any
advanced use of this module will require an understanding of the format, as
defined in <a class="reference external" href="https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT">PKZIP Application Note</a>.</p>
<p>This module does not currently handle multi-disk ZIP files.
It can handle ZIP files that use the ZIP64 extensions
(that is ZIP files that are more than 4 GiB in size).  It supports
decryption of encrypted files in ZIP archives, but it currently cannot
create an encrypted file.  Decryption is extremely slow as it is
implemented in native Python rather than C.</p>
<p>The module defines the following items:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="zipfile.BadZipFile">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">BadZipFile</span></span><a class="headerlink" href="#zipfile.BadZipFile" title="Link to this definition">¶</a></dt>
<dd><p>The error raised for bad ZIP files.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="zipfile.BadZipfile">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">BadZipfile</span></span><a class="headerlink" href="#zipfile.BadZipfile" title="Link to this definition">¶</a></dt>
<dd><p>Alias of <a class="reference internal" href="#zipfile.BadZipFile" title="zipfile.BadZipFile"><code class="xref py py-exc docutils literal notranslate"><span class="pre">BadZipFile</span></code></a>, for compatibility with older Python versions.</p>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="zipfile.LargeZipFile">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">LargeZipFile</span></span><a class="headerlink" href="#zipfile.LargeZipFile" title="Link to this definition">¶</a></dt>
<dd><p>The error raised when a ZIP file would require ZIP64 functionality but that has
not been enabled.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZipFile</span></span></dt>
<dd><p>The class for reading and writing ZIP files.  See section
<a class="reference internal" href="#zipfile-objects"><span class="std std-ref">ZipFile Objects</span></a> for constructor details.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">Path</span></span></dt>
<dd><p>Class that implements a subset of the interface provided by
<a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a>, including the full
<a class="reference internal" href="importlib.resources.abc.html#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.Traversable</span></code></a> interface.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">PyZipFile</span></span></dt>
<dd><p>Class for creating ZIP archives containing Python libraries.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="zipfile.ZipInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZipInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'NoName'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">date_time</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">(1980,</span> <span class="pre">1,</span> <span class="pre">1,</span> <span class="pre">0,</span> <span class="pre">0,</span> <span class="pre">0)</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipInfo" title="Link to this definition">¶</a></dt>
<dd><p>Class used to represent information about a member of an archive. Instances
of this class are returned by the <a class="reference internal" href="#zipfile.ZipFile.getinfo" title="zipfile.ZipFile.getinfo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getinfo()</span></code></a> and <a class="reference internal" href="#zipfile.ZipFile.infolist" title="zipfile.ZipFile.infolist"><code class="xref py py-meth docutils literal notranslate"><span class="pre">infolist()</span></code></a>
methods of <a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> objects.  Most users of the <a class="reference internal" href="#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> module
will not need to create these, but only use those created by this
module. <em>filename</em> should be the full name of the archive member, and
<em>date_time</em> should be a tuple containing six fields which describe the time
of the last modification to the file; the fields are described in section
<a class="reference internal" href="#zipinfo-objects"><span class="std std-ref">ZipInfo Objects</span></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="zipfile.is_zipfile">
<span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">is_zipfile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.is_zipfile" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if <em>filename</em> is a valid ZIP file based on its magic number,
otherwise returns <code class="docutils literal notranslate"><span class="pre">False</span></code>.  <em>filename</em> may be a file or file-like object too.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.1: </span>Support for file and file-like objects.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.ZIP_STORED">
<span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZIP_STORED</span></span><a class="headerlink" href="#zipfile.ZIP_STORED" title="Link to this definition">¶</a></dt>
<dd><p>The numeric constant for an uncompressed archive member.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.ZIP_DEFLATED">
<span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZIP_DEFLATED</span></span><a class="headerlink" href="#zipfile.ZIP_DEFLATED" title="Link to this definition">¶</a></dt>
<dd><p>The numeric constant for the usual ZIP compression method.  This requires the
<a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a> module.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.ZIP_BZIP2">
<span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZIP_BZIP2</span></span><a class="headerlink" href="#zipfile.ZIP_BZIP2" title="Link to this definition">¶</a></dt>
<dd><p>The numeric constant for the BZIP2 compression method.  This requires the
<a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> module.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.ZIP_LZMA">
<span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZIP_LZMA</span></span><a class="headerlink" href="#zipfile.ZIP_LZMA" title="Link to this definition">¶</a></dt>
<dd><p>The numeric constant for the LZMA compression method.  This requires the
<a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> module.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The ZIP file format specification has included support for bzip2 compression
since 2001, and for LZMA compression since 2006. However, some tools
(including older Python releases) do not support these compression
methods, and may either refuse to process the ZIP file altogether,
or fail to extract individual files.</p>
</div>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT">PKZIP Application Note</a></dt><dd><p>Documentation on the ZIP file format by Phil Katz, the creator of the format and
algorithms used.</p>
</dd>
<dt><a class="reference external" href="https://infozip.sourceforge.net/">Info-ZIP Home Page</a></dt><dd><p>Information about the Info-ZIP project’s ZIP archive programs and development
libraries.</p>
</dd>
</dl>
</div>
<section id="zipfile-objects">
<span id="id1"></span><h2>ZipFile Objects<a class="headerlink" href="#zipfile-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="zipfile.ZipFile">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">ZipFile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compression</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">ZIP_STORED</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowZip64</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compresslevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_timestamps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">metadata_encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile" title="Link to this definition">¶</a></dt>
<dd><p>Open a ZIP file, where <em>file</em> can be a path to a file (a string), a
file-like object or a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
<p>The <em>mode</em> parameter should be <code class="docutils literal notranslate"><span class="pre">'r'</span></code> to read an existing
file, <code class="docutils literal notranslate"><span class="pre">'w'</span></code> to truncate and write a new file, <code class="docutils literal notranslate"><span class="pre">'a'</span></code> to append to an
existing file, or <code class="docutils literal notranslate"><span class="pre">'x'</span></code> to exclusively create and write a new file.
If <em>mode</em> is <code class="docutils literal notranslate"><span class="pre">'x'</span></code> and <em>file</em> refers to an existing file,
a <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> will be raised.
If <em>mode</em> is <code class="docutils literal notranslate"><span class="pre">'a'</span></code> and <em>file</em> refers to an existing ZIP
file, then additional files are added to it.  If <em>file</em> does not refer to a
ZIP file, then a new ZIP archive is appended to the file.  This is meant for
adding a ZIP archive to another file (such as <code class="file docutils literal notranslate"><span class="pre">python.exe</span></code>).  If
<em>mode</em> is <code class="docutils literal notranslate"><span class="pre">'a'</span></code> and the file does not exist at all, it is created.
If <em>mode</em> is <code class="docutils literal notranslate"><span class="pre">'r'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code>, the file should be seekable.</p>
<p><em>compression</em> is the ZIP compression method to use when writing the archive,
and should be <a class="reference internal" href="#zipfile.ZIP_STORED" title="zipfile.ZIP_STORED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_STORED</span></code></a>, <a class="reference internal" href="#zipfile.ZIP_DEFLATED" title="zipfile.ZIP_DEFLATED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_DEFLATED</span></code></a>,
<a class="reference internal" href="#zipfile.ZIP_BZIP2" title="zipfile.ZIP_BZIP2"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_BZIP2</span></code></a> or <a class="reference internal" href="#zipfile.ZIP_LZMA" title="zipfile.ZIP_LZMA"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_LZMA</span></code></a>; unrecognized
values will cause <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a> to be raised.  If
<a class="reference internal" href="#zipfile.ZIP_DEFLATED" title="zipfile.ZIP_DEFLATED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_DEFLATED</span></code></a>, <a class="reference internal" href="#zipfile.ZIP_BZIP2" title="zipfile.ZIP_BZIP2"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_BZIP2</span></code></a> or <a class="reference internal" href="#zipfile.ZIP_LZMA" title="zipfile.ZIP_LZMA"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_LZMA</span></code></a> is specified
but the corresponding module (<a class="reference internal" href="zlib.html#module-zlib" title="zlib: Low-level interface to compression and decompression routines compatible with gzip."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zlib</span></code></a>, <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bz2</span></code></a> or <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a>) is not
available, <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> is raised. The default is <a class="reference internal" href="#zipfile.ZIP_STORED" title="zipfile.ZIP_STORED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_STORED</span></code></a>.</p>
<p>If <em>allowZip64</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default) zipfile will create ZIP files that
use the ZIP64 extensions when the zipfile is larger than 4 GiB. If it is
<code class="docutils literal notranslate"><span class="pre">false</span></code> <a class="reference internal" href="#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> will raise an exception when the ZIP file would
require ZIP64 extensions.</p>
<p>The <em>compresslevel</em> parameter controls the compression level to use when
writing files to the archive.
When using <a class="reference internal" href="#zipfile.ZIP_STORED" title="zipfile.ZIP_STORED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_STORED</span></code></a> or <a class="reference internal" href="#zipfile.ZIP_LZMA" title="zipfile.ZIP_LZMA"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_LZMA</span></code></a> it has no effect.
When using <a class="reference internal" href="#zipfile.ZIP_DEFLATED" title="zipfile.ZIP_DEFLATED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_DEFLATED</span></code></a> integers <code class="docutils literal notranslate"><span class="pre">0</span></code> through <code class="docutils literal notranslate"><span class="pre">9</span></code> are accepted
(see <a class="reference internal" href="zlib.html#zlib.compressobj" title="zlib.compressobj"><code class="xref py py-class docutils literal notranslate"><span class="pre">zlib</span></code></a> for more information).
When using <a class="reference internal" href="#zipfile.ZIP_BZIP2" title="zipfile.ZIP_BZIP2"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_BZIP2</span></code></a> integers <code class="docutils literal notranslate"><span class="pre">1</span></code> through <code class="docutils literal notranslate"><span class="pre">9</span></code> are accepted
(see <a class="reference internal" href="bz2.html#bz2.BZ2File" title="bz2.BZ2File"><code class="xref py py-class docutils literal notranslate"><span class="pre">bz2</span></code></a> for more information).</p>
<p>The <em>strict_timestamps</em> argument, when set to <code class="docutils literal notranslate"><span class="pre">False</span></code>, allows to
zip files older than 1980-01-01 at the cost of setting the
timestamp to 1980-01-01.
Similar behavior occurs with files newer than 2107-12-31,
the timestamp is also set to the limit.</p>
<p>When mode is <code class="docutils literal notranslate"><span class="pre">'r'</span></code>, <em>metadata_encoding</em> may be set to the name of a codec,
which will be used to decode metadata such as the names of members and ZIP
comments.</p>
<p>If the file is created with mode <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code> and then
<a class="reference internal" href="#zipfile.ZipFile.close" title="zipfile.ZipFile.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">closed</span></code></a> without adding any files to the archive, the appropriate
ZIP structures for an empty archive will be written to the file.</p>
<p>ZipFile is also a context manager and therefore supports the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement.  In the example, <em>myzip</em> is closed after the
<code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code> statement’s suite is finished—even if an exception occurs:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">ZipFile</span><span class="p">(</span><span class="s1">&#39;spam.zip&#39;</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">myzip</span><span class="p">:</span>
    <span class="n">myzip</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s1">&#39;eggs.txt&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><em>metadata_encoding</em> is an instance-wide setting for the ZipFile.
It is not currently possible to set this on a per-member basis.</p>
<p>This attribute is a workaround for legacy implementations which produce
archives with names in the current locale encoding or code page (mostly
on Windows).  According to the .ZIP standard, the encoding of metadata
may be specified to be either IBM code page (default) or UTF-8 by a flag
in the archive header.
That flag takes precedence over <em>metadata_encoding</em>, which is
a Python-specific extension.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the ability to use <a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> as a context manager.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Added support for <a class="reference internal" href="bz2.html#module-bz2" title="bz2: Interfaces for bzip2 compression and decompression."><code class="xref py py-mod docutils literal notranslate"><span class="pre">bzip2</span></code></a> and <a class="reference internal" href="lzma.html#module-lzma" title="lzma: A Python wrapper for the liblzma compression library."><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code></a> compression.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>ZIP64 extensions are enabled by default.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>Added support for writing to unseekable streams.
Added support for the <code class="docutils literal notranslate"><span class="pre">'x'</span></code> mode.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Previously, a plain <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised for unrecognized
compression values.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.2: </span>The <em>file</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Add the <em>compresslevel</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>strict_timestamps</em> keyword-only parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span>Added support for specifying member name encoding for reading
metadata in the zipfile’s directory and file headers.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.close">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the archive file.  You must call <a class="reference internal" href="#zipfile.ZipFile.close" title="zipfile.ZipFile.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> before exiting your program
or essential records will not be written.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.getinfo">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">getinfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.getinfo" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object with information about the archive member
<em>name</em>.  Calling <a class="reference internal" href="#zipfile.ZipFile.getinfo" title="zipfile.ZipFile.getinfo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getinfo()</span></code></a> for a name not currently contained in the
archive will raise a <a class="reference internal" href="exceptions.html#KeyError" title="KeyError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">KeyError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.infolist">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">infolist</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.infolist" title="Link to this definition">¶</a></dt>
<dd><p>Return a list containing a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object for each member of the
archive.  The objects are in the same order as their entries in the actual ZIP
file on disk if an existing archive was opened.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.namelist">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">namelist</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.namelist" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of archive members by name.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.open">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">force_zip64</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.open" title="Link to this definition">¶</a></dt>
<dd><p>Access a member of the archive as a binary file-like object.  <em>name</em>
can be either the name of a file within the archive or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a>
object.  The <em>mode</em> parameter, if included, must be <code class="docutils literal notranslate"><span class="pre">'r'</span></code> (the default)
or <code class="docutils literal notranslate"><span class="pre">'w'</span></code>.  <em>pwd</em> is the password used to decrypt encrypted ZIP files as a
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<p><a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> is also a context manager and therefore supports the
<a class="reference internal" href="../reference/compound_stmts.html#with"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">with</span></code></a> statement:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="n">ZipFile</span><span class="p">(</span><span class="s1">&#39;spam.zip&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">myzip</span><span class="p">:</span>
    <span class="k">with</span> <span class="n">myzip</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;eggs.txt&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">myfile</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">myfile</span><span class="o">.</span><span class="n">read</span><span class="p">())</span>
</pre></div>
</div>
<p>With <em>mode</em> <code class="docutils literal notranslate"><span class="pre">'r'</span></code> the file-like object
(<code class="docutils literal notranslate"><span class="pre">ZipExtFile</span></code>) is read-only and provides the following methods:
<a class="reference internal" href="io.html#io.BufferedIOBase.read" title="io.BufferedIOBase.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a>, <a class="reference internal" href="io.html#io.IOBase.readline" title="io.IOBase.readline"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readline()</span></code></a>,
<a class="reference internal" href="io.html#io.IOBase.readlines" title="io.IOBase.readlines"><code class="xref py py-meth docutils literal notranslate"><span class="pre">readlines()</span></code></a>, <a class="reference internal" href="io.html#io.IOBase.seek" title="io.IOBase.seek"><code class="xref py py-meth docutils literal notranslate"><span class="pre">seek()</span></code></a>,
<a class="reference internal" href="io.html#io.IOBase.tell" title="io.IOBase.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a>, <a class="reference internal" href="stdtypes.html#container.__iter__" title="container.__iter__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code></a>, <a class="reference internal" href="stdtypes.html#iterator.__next__" title="iterator.__next__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__next__()</span></code></a>.
These objects can operate independently of the ZipFile.</p>
<p>With <code class="docutils literal notranslate"><span class="pre">mode='w'</span></code>, a writable file handle is returned, which supports the
<a class="reference internal" href="io.html#io.BufferedIOBase.write" title="io.BufferedIOBase.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> method.  While a writable file handle is open,
attempting to read or write other files in the ZIP file will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.</p>
<p>When writing a file, if the file size is not known in advance but may exceed
2 GiB, pass <code class="docutils literal notranslate"><span class="pre">force_zip64=True</span></code> to ensure that the header format is
capable of supporting large files.  If the file size is known in advance,
construct a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object with <a class="reference internal" href="#zipfile.ZipInfo.file_size" title="zipfile.ZipInfo.file_size"><code class="xref py py-attr docutils literal notranslate"><span class="pre">file_size</span></code></a> set, and
use that as the <em>name</em> parameter.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a>, <a class="reference internal" href="#zipfile.ZipFile.read" title="zipfile.ZipFile.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> and <a class="reference internal" href="#zipfile.ZipFile.extract" title="zipfile.ZipFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> methods can take a filename
or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object.  You will appreciate this when trying to read a
ZIP file that contains members with duplicate names.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Removed support of <code class="docutils literal notranslate"><span class="pre">mode='U'</span></code>.  Use <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> for reading
compressed text files in <a class="reference internal" href="../glossary.html#term-universal-newlines"><span class="xref std std-term">universal newlines</span></a> mode.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span><a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ZipFile.open()</span></code></a> can now be used to write files into the archive with the
<code class="docutils literal notranslate"><span class="pre">mode='w'</span></code> option.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> on a closed ZipFile will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.
Previously, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.extract">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">extract</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">member</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.extract" title="Link to this definition">¶</a></dt>
<dd><p>Extract a member from the archive to the current working directory; <em>member</em>
must be its full name or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object.  Its file information is
extracted as accurately as possible.  <em>path</em> specifies a different directory
to extract to.  <em>member</em> can be a filename or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object.
<em>pwd</em> is the password used for encrypted files as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<p>Returns the normalized path created (a directory or new file).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If a member filename is an absolute path, a drive/UNC sharepoint and
leading (back)slashes will be stripped, e.g.: <code class="docutils literal notranslate"><span class="pre">///foo/bar</span></code> becomes
<code class="docutils literal notranslate"><span class="pre">foo/bar</span></code> on Unix, and <code class="docutils literal notranslate"><span class="pre">C:\foo\bar</span></code> becomes <code class="docutils literal notranslate"><span class="pre">foo\bar</span></code> on Windows.
And all <code class="docutils literal notranslate"><span class="pre">&quot;..&quot;</span></code> components in a member filename will be removed, e.g.:
<code class="docutils literal notranslate"><span class="pre">../../foo../../ba..r</span></code> becomes <code class="docutils literal notranslate"><span class="pre">foo../ba..r</span></code>.  On Windows illegal
characters (<code class="docutils literal notranslate"><span class="pre">:</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">|</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">?</span></code>, and <code class="docutils literal notranslate"><span class="pre">*</span></code>)
replaced by underscore (<code class="docutils literal notranslate"><span class="pre">_</span></code>).</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.extract" title="zipfile.ZipFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> on a closed ZipFile will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  Previously, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.2: </span>The <em>path</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.extractall">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">extractall</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">members</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.extractall" title="Link to this definition">¶</a></dt>
<dd><p>Extract all members from the archive to the current working directory.  <em>path</em>
specifies a different directory to extract to.  <em>members</em> is optional and must
be a subset of the list returned by <a class="reference internal" href="#zipfile.ZipFile.namelist" title="zipfile.ZipFile.namelist"><code class="xref py py-meth docutils literal notranslate"><span class="pre">namelist()</span></code></a>.  <em>pwd</em> is the password
used for encrypted files as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Never extract archives from untrusted sources without prior inspection.
It is possible that files are created outside of <em>path</em>, e.g. members
that have absolute filenames starting with <code class="docutils literal notranslate"><span class="pre">&quot;/&quot;</span></code> or filenames with two
dots <code class="docutils literal notranslate"><span class="pre">&quot;..&quot;</span></code>.  This module attempts to prevent that.
See <a class="reference internal" href="#zipfile.ZipFile.extract" title="zipfile.ZipFile.extract"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extract()</span></code></a> note.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.extractall" title="zipfile.ZipFile.extractall"><code class="xref py py-meth docutils literal notranslate"><span class="pre">extractall()</span></code></a> on a closed ZipFile will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  Previously, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.2: </span>The <em>path</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.printdir">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">printdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.printdir" title="Link to this definition">¶</a></dt>
<dd><p>Print a table of contents for the archive to <code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.setpassword">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">setpassword</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pwd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.setpassword" title="Link to this definition">¶</a></dt>
<dd><p>Set <em>pwd</em> (a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object) as default password to extract encrypted files.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.read">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">read</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pwd</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.read" title="Link to this definition">¶</a></dt>
<dd><p>Return the bytes of the file <em>name</em> in the archive.  <em>name</em> is the name of the
file in the archive, or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> object.  The archive must be open for
read or append. <em>pwd</em> is the password used for encrypted files as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a>
object and, if specified, overrides the default password set with <a class="reference internal" href="#zipfile.ZipFile.setpassword" title="zipfile.ZipFile.setpassword"><code class="xref py py-meth docutils literal notranslate"><span class="pre">setpassword()</span></code></a>.
Calling <a class="reference internal" href="#zipfile.ZipFile.read" title="zipfile.ZipFile.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> on a ZipFile that uses a compression method other than
<a class="reference internal" href="#zipfile.ZIP_STORED" title="zipfile.ZIP_STORED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_STORED</span></code></a>, <a class="reference internal" href="#zipfile.ZIP_DEFLATED" title="zipfile.ZIP_DEFLATED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_DEFLATED</span></code></a>, <a class="reference internal" href="#zipfile.ZIP_BZIP2" title="zipfile.ZIP_BZIP2"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_BZIP2</span></code></a> or
<a class="reference internal" href="#zipfile.ZIP_LZMA" title="zipfile.ZIP_LZMA"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_LZMA</span></code></a> will raise a <a class="reference internal" href="exceptions.html#NotImplementedError" title="NotImplementedError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">NotImplementedError</span></code></a>. An error will also
be raised if the corresponding compression module is not available.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.read" title="zipfile.ZipFile.read"><code class="xref py py-meth docutils literal notranslate"><span class="pre">read()</span></code></a> on a closed ZipFile will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.
Previously, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.testzip">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">testzip</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.testzip" title="Link to this definition">¶</a></dt>
<dd><p>Read all the files in the archive and check their CRC’s and file headers.
Return the name of the first bad file, or else return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.testzip" title="zipfile.ZipFile.testzip"><code class="xref py py-meth docutils literal notranslate"><span class="pre">testzip()</span></code></a> on a closed ZipFile will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  Previously, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.write">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arcname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compresslevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.write" title="Link to this definition">¶</a></dt>
<dd><p>Write the file named <em>filename</em> to the archive, giving it the archive name
<em>arcname</em> (by default, this will be the same as <em>filename</em>, but without a drive
letter and with leading path separators removed).  If given, <em>compress_type</em>
overrides the value given for the <em>compression</em> parameter to the constructor for
the new entry. Similarly, <em>compresslevel</em> will override the constructor if
given.
The archive must be open with mode <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The ZIP file standard historically did not specify a metadata encoding,
but strongly recommended CP437 (the original IBM PC encoding) for
interoperability.  Recent versions allow use of UTF-8 (only).  In this
module, UTF-8 will automatically be used to write the member names if
they contain any non-ASCII characters.  It is not possible to write
member names in any encoding other than ASCII or UTF-8.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Archive names should be relative to the archive root, that is, they should not
start with a path separator.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <code class="docutils literal notranslate"><span class="pre">arcname</span></code> (or <code class="docutils literal notranslate"><span class="pre">filename</span></code>, if <code class="docutils literal notranslate"><span class="pre">arcname</span></code> is  not given) contains a null
byte, the name of the file in the archive will be truncated at the null byte.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A leading slash in the filename may lead to the archive being impossible to
open in some zip programs on Windows systems.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.write" title="zipfile.ZipFile.write"><code class="xref py py-meth docutils literal notranslate"><span class="pre">write()</span></code></a> on a ZipFile created with mode <code class="docutils literal notranslate"><span class="pre">'r'</span></code> or
a closed ZipFile will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  Previously,
a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.writestr">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">writestr</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">zinfo_or_arcname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compress_type</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compresslevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.writestr" title="Link to this definition">¶</a></dt>
<dd><p>Write a file into the archive.  The contents is <em>data</em>, which may be either
a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a> or a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> instance; if it is a <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>,
it is encoded as UTF-8 first.  <em>zinfo_or_arcname</em> is either the file
name it will be given in the archive, or a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance.  If it’s
an instance, at least the filename, date, and time must be given.  If it’s a
name, the date and time is set to the current date and time.
The archive must be opened with mode <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code>.</p>
<p>If given, <em>compress_type</em> overrides the value given for the <em>compression</em>
parameter to the constructor for the new entry, or in the <em>zinfo_or_arcname</em>
(if that is a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance). Similarly, <em>compresslevel</em> will
override the constructor if given.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When passing a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance as the <em>zinfo_or_arcname</em> parameter,
the compression method used will be that specified in the <em>compress_type</em>
member of the given <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance.  By default, the
<a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> constructor sets this member to <a class="reference internal" href="#zipfile.ZIP_STORED" title="zipfile.ZIP_STORED"><code class="xref py py-const docutils literal notranslate"><span class="pre">ZIP_STORED</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>The <em>compress_type</em> argument.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Calling <a class="reference internal" href="#zipfile.ZipFile.writestr" title="zipfile.ZipFile.writestr"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writestr()</span></code></a> on a ZipFile created with mode <code class="docutils literal notranslate"><span class="pre">'r'</span></code> or
a closed ZipFile will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a>.  Previously,
a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> was raised.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipFile.mkdir">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">mkdir</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">zinfo_or_directory</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">511</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipFile.mkdir" title="Link to this definition">¶</a></dt>
<dd><p>Create a directory inside the archive.  If <em>zinfo_or_directory</em> is a string,
a directory is created inside the archive with the mode that is specified in
the <em>mode</em> argument. If, however, <em>zinfo_or_directory</em> is
a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance then the <em>mode</em> argument is ignored.</p>
<p>The archive must be opened with mode <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

<p>The following data attributes are also available:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipFile.filename">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#zipfile.ZipFile.filename" title="Link to this definition">¶</a></dt>
<dd><p>Name of the ZIP file.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipFile.debug">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">debug</span></span><a class="headerlink" href="#zipfile.ZipFile.debug" title="Link to this definition">¶</a></dt>
<dd><p>The level of debug output to use.  This may be set from <code class="docutils literal notranslate"><span class="pre">0</span></code> (the default, no
output) to <code class="docutils literal notranslate"><span class="pre">3</span></code> (the most output).  Debugging information is written to
<code class="docutils literal notranslate"><span class="pre">sys.stdout</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipFile.comment">
<span class="sig-prename descclassname"><span class="pre">ZipFile.</span></span><span class="sig-name descname"><span class="pre">comment</span></span><a class="headerlink" href="#zipfile.ZipFile.comment" title="Link to this definition">¶</a></dt>
<dd><p>The comment associated with the ZIP file as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.
If assigning a comment to a
<a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> instance created with mode <code class="docutils literal notranslate"><span class="pre">'w'</span></code>, <code class="docutils literal notranslate"><span class="pre">'x'</span></code> or <code class="docutils literal notranslate"><span class="pre">'a'</span></code>,
it should be no longer than 65535 bytes.  Comments longer than this will be
truncated.</p>
</dd></dl>

</section>
<section id="path-objects">
<span id="id2"></span><h2>Path Objects<a class="headerlink" href="#path-objects" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="zipfile.Path">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">Path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">root</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">at</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path" title="Link to this definition">¶</a></dt>
<dd><p>Construct a Path object from a <code class="docutils literal notranslate"><span class="pre">root</span></code> zipfile (which may be a
<a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> instance or <code class="docutils literal notranslate"><span class="pre">file</span></code> suitable for passing to
the <a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> constructor).</p>
<p><code class="docutils literal notranslate"><span class="pre">at</span></code> specifies the location of this Path within the zipfile,
e.g. ‘dir/file.txt’, ‘dir/’, or ‘’. Defaults to the empty string,
indicating the root.</p>
</dd></dl>

<p>Path objects expose the following features of <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a>
objects:</p>
<p>Path objects are traversable using the <code class="docutils literal notranslate"><span class="pre">/</span></code> operator or <code class="docutils literal notranslate"><span class="pre">joinpath</span></code>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.Path.name">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#zipfile.Path.name" title="Link to this definition">¶</a></dt>
<dd><p>The final path component.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.open">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode='r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pwd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">**</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.open" title="Link to this definition">¶</a></dt>
<dd><p>Invoke <a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ZipFile.open()</span></code></a> on the current path.
Allows opening for read or write, text or binary
through supported modes: ‘r’, ‘w’, ‘rb’, ‘wb’.
Positional and keyword arguments are passed through to
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> when opened as text and
ignored otherwise.
<code class="docutils literal notranslate"><span class="pre">pwd</span></code> is the <code class="docutils literal notranslate"><span class="pre">pwd</span></code> parameter to
<a class="reference internal" href="#zipfile.ZipFile.open" title="zipfile.ZipFile.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ZipFile.open()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.9: </span>Added support for text and binary modes for open. Default
mode is now text.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.2: </span>The <code class="docutils literal notranslate"><span class="pre">encoding</span></code> parameter can be supplied as a positional argument
without causing a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>. As it could in 3.9. Code needing to
be compatible with unpatched 3.10 and 3.11 versions must pass all
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> arguments, <code class="docutils literal notranslate"><span class="pre">encoding</span></code> included, as keywords.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.iterdir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">iterdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.iterdir" title="Link to this definition">¶</a></dt>
<dd><p>Enumerate the children of the current directory.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.is_dir">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.is_dir" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the current context references a directory.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.is_file">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">is_file</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.is_file" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the current context references a file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.exists">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">exists</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.exists" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the current context references a file or
directory in the zip file.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.Path.suffix">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">suffix</span></span><a class="headerlink" href="#zipfile.Path.suffix" title="Link to this definition">¶</a></dt>
<dd><p>The file extension of the final component.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Added <a class="reference internal" href="#zipfile.Path.suffix" title="zipfile.Path.suffix"><code class="xref py py-data docutils literal notranslate"><span class="pre">Path.suffix</span></code></a> property.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.Path.stem">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">stem</span></span><a class="headerlink" href="#zipfile.Path.stem" title="Link to this definition">¶</a></dt>
<dd><p>The final path component, without its suffix.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Added <a class="reference internal" href="#zipfile.Path.stem" title="zipfile.Path.stem"><code class="xref py py-data docutils literal notranslate"><span class="pre">Path.stem</span></code></a> property.</p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="zipfile.Path.suffixes">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">suffixes</span></span><a class="headerlink" href="#zipfile.Path.suffixes" title="Link to this definition">¶</a></dt>
<dd><p>A list of the path’s file extensions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11: </span>Added <a class="reference internal" href="#zipfile.Path.suffixes" title="zipfile.Path.suffixes"><code class="xref py py-data docutils literal notranslate"><span class="pre">Path.suffixes</span></code></a> property.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.read_text">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">read_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">**</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.read_text" title="Link to this definition">¶</a></dt>
<dd><p>Read the current file as unicode text. Positional and
keyword arguments are passed through to
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> (except <code class="docutils literal notranslate"><span class="pre">buffer</span></code>, which is
implied by the context).</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.2: </span>The <code class="docutils literal notranslate"><span class="pre">encoding</span></code> parameter can be supplied as a positional argument
without causing a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>. As it could in 3.9. Code needing to
be compatible with unpatched 3.10 and 3.11 versions must pass all
<a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a> arguments, <code class="docutils literal notranslate"><span class="pre">encoding</span></code> included, as keywords.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.read_bytes">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">read_bytes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.read_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Read the current file as bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="zipfile.Path.joinpath">
<span class="sig-prename descclassname"><span class="pre">Path.</span></span><span class="sig-name descname"><span class="pre">joinpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">other</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.Path.joinpath" title="Link to this definition">¶</a></dt>
<dd><p>Return a new Path object with each of the <em>other</em> arguments
joined. The following are equivalent:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;child&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;grandchild&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;child&#39;</span><span class="p">,</span> <span class="s1">&#39;grandchild&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">)</span> <span class="o">/</span> <span class="s1">&#39;child&#39;</span> <span class="o">/</span> <span class="s1">&#39;grandchild&#39;</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Prior to 3.10, <code class="docutils literal notranslate"><span class="pre">joinpath</span></code> was undocumented and accepted
exactly one parameter.</p>
</div>
</dd></dl>

<p>The <a class="reference external" href="https://pypi.org/project/zipp">zipp</a> project provides backports
of the latest path object functionality to older Pythons. Use
<code class="docutils literal notranslate"><span class="pre">zipp.Path</span></code> in place of <code class="docutils literal notranslate"><span class="pre">zipfile.Path</span></code> for early access to
changes.</p>
</section>
<section id="pyzipfile-objects">
<span id="id3"></span><h2>PyZipFile Objects<a class="headerlink" href="#pyzipfile-objects" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#zipfile.PyZipFile" title="zipfile.PyZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">PyZipFile</span></code></a> constructor takes the same parameters as the
<a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> constructor, and one additional parameter, <em>optimize</em>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="zipfile.PyZipFile">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">zipfile.</span></span><span class="sig-name descname"><span class="pre">PyZipFile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">compression</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">ZIP_STORED</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">allowZip64</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optimize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.PyZipFile" title="Link to this definition">¶</a></dt>
<dd><div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added the <em>optimize</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>ZIP64 extensions are enabled by default.</p>
</div>
<p>Instances have one method in addition to those of <a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> objects:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zipfile.PyZipFile.writepy">
<span class="sig-name descname"><span class="pre">writepy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pathname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">basename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filterfunc</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.PyZipFile.writepy" title="Link to this definition">¶</a></dt>
<dd><p>Search for files <code class="file docutils literal notranslate"><span class="pre">*.py</span></code> and add the corresponding file to the
archive.</p>
<p>If the <em>optimize</em> parameter to <a class="reference internal" href="#zipfile.PyZipFile" title="zipfile.PyZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">PyZipFile</span></code></a> was not given or <code class="docutils literal notranslate"><span class="pre">-1</span></code>,
the corresponding file is a <code class="file docutils literal notranslate"><span class="pre">*.pyc</span></code> file, compiling if necessary.</p>
<p>If the <em>optimize</em> parameter to <a class="reference internal" href="#zipfile.PyZipFile" title="zipfile.PyZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">PyZipFile</span></code></a> was <code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">1</span></code> or
<code class="docutils literal notranslate"><span class="pre">2</span></code>, only files with that optimization level (see <a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a>) are
added to the archive, compiling if necessary.</p>
<p>If <em>pathname</em> is a file, the filename must end with <code class="file docutils literal notranslate"><span class="pre">.py</span></code>, and
just the (corresponding <code class="file docutils literal notranslate"><span class="pre">*.pyc</span></code>) file is added at the top level
(no path information).  If <em>pathname</em> is a file that does not end with
<code class="file docutils literal notranslate"><span class="pre">.py</span></code>, a <a class="reference internal" href="exceptions.html#RuntimeError" title="RuntimeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">RuntimeError</span></code></a> will be raised.  If it is a directory,
and the directory is not a package directory, then all the files
<code class="file docutils literal notranslate"><span class="pre">*.pyc</span></code> are added at the top level.  If the directory is a
package directory, then all <code class="file docutils literal notranslate"><span class="pre">*.pyc</span></code> are added under the package
name as a file path, and if any subdirectories are package directories,
all of these are added recursively in sorted order.</p>
<p><em>basename</em> is intended for internal use only.</p>
<p><em>filterfunc</em>, if given, must be a function taking a single string
argument.  It will be passed each path (including each individual full
file path) before it is added to the archive.  If <em>filterfunc</em> returns a
false value, the path will not be added, and if it is a directory its
contents will be ignored.  For example, if our test files are all either
in <code class="docutils literal notranslate"><span class="pre">test</span></code> directories or start with the string <code class="docutils literal notranslate"><span class="pre">test_</span></code>, we can use a
<em>filterfunc</em> to exclude them:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">zf</span> <span class="o">=</span> <span class="n">PyZipFile</span><span class="p">(</span><span class="s1">&#39;myprog.zip&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">notests</span><span class="p">(</span><span class="n">s</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">fn</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">return</span> <span class="p">(</span><span class="ow">not</span> <span class="p">(</span><span class="n">fn</span> <span class="o">==</span> <span class="s1">&#39;test&#39;</span> <span class="ow">or</span> <span class="n">fn</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;test_&#39;</span><span class="p">)))</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">zf</span><span class="o">.</span><span class="n">writepy</span><span class="p">(</span><span class="s1">&#39;myprog&#39;</span><span class="p">,</span> <span class="n">filterfunc</span><span class="o">=</span><span class="n">notests</span><span class="p">)</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#zipfile.PyZipFile.writepy" title="zipfile.PyZipFile.writepy"><code class="xref py py-meth docutils literal notranslate"><span class="pre">writepy()</span></code></a> method makes archives with file names like
this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">string</span><span class="o">.</span><span class="n">pyc</span>                   <span class="c1"># Top level name</span>
<span class="n">test</span><span class="o">/</span><span class="fm">__init__</span><span class="o">.</span><span class="n">pyc</span>            <span class="c1"># Package directory</span>
<span class="n">test</span><span class="o">/</span><span class="n">testall</span><span class="o">.</span><span class="n">pyc</span>             <span class="c1"># Module test.testall</span>
<span class="n">test</span><span class="o">/</span><span class="n">bogus</span><span class="o">/</span><span class="fm">__init__</span><span class="o">.</span><span class="n">pyc</span>      <span class="c1"># Subpackage directory</span>
<span class="n">test</span><span class="o">/</span><span class="n">bogus</span><span class="o">/</span><span class="n">myfile</span><span class="o">.</span><span class="n">pyc</span>        <span class="c1"># Submodule test.bogus.myfile</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added the <em>filterfunc</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.2: </span>The <em>pathname</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Recursion sorts directory entries.</p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="zipinfo-objects">
<span id="id4"></span><h2>ZipInfo Objects<a class="headerlink" href="#zipinfo-objects" title="Link to this heading">¶</a></h2>
<p>Instances of the <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> class are returned by the <a class="reference internal" href="#zipfile.ZipFile.getinfo" title="zipfile.ZipFile.getinfo"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getinfo()</span></code></a> and
<a class="reference internal" href="#zipfile.ZipFile.infolist" title="zipfile.ZipFile.infolist"><code class="xref py py-meth docutils literal notranslate"><span class="pre">infolist()</span></code></a> methods of <a class="reference internal" href="#zipfile.ZipFile" title="zipfile.ZipFile"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipFile</span></code></a> objects.  Each object stores
information about a single member of the ZIP archive.</p>
<p>There is one classmethod to make a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance for a filesystem
file:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipInfo.from_file">
<em class="property"><span class="pre">classmethod</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">from_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">arcname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">strict_timestamps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipInfo.from_file" title="Link to this definition">¶</a></dt>
<dd><p>Construct a <a class="reference internal" href="#zipfile.ZipInfo" title="zipfile.ZipInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ZipInfo</span></code></a> instance for a file on the filesystem, in
preparation for adding it to a zip file.</p>
<p><em>filename</em> should be the path to a file or directory on the filesystem.</p>
<p>If <em>arcname</em> is specified, it is used as the name within the archive.
If <em>arcname</em> is not specified, the name will be the same as <em>filename</em>, but
with any drive letter and leading path separators removed.</p>
<p>The <em>strict_timestamps</em> argument, when set to <code class="docutils literal notranslate"><span class="pre">False</span></code>, allows to
zip files older than 1980-01-01 at the cost of setting the
timestamp to 1980-01-01.
Similar behavior occurs with files newer than 2107-12-31,
the timestamp is also set to the limit.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6.2: </span>The <em>filename</em> parameter accepts a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added the <em>strict_timestamps</em> keyword-only parameter.</p>
</div>
</dd></dl>

<p>Instances have the following methods and attributes:</p>
<dl class="py method">
<dt class="sig sig-object py" id="zipfile.ZipInfo.is_dir">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">is_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#zipfile.ZipInfo.is_dir" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if this archive member is a directory.</p>
<p>This uses the entry’s name: directories should always end with <code class="docutils literal notranslate"><span class="pre">/</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.filename">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#zipfile.ZipInfo.filename" title="Link to this definition">¶</a></dt>
<dd><p>Name of the file in the archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.date_time">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">date_time</span></span><a class="headerlink" href="#zipfile.ZipInfo.date_time" title="Link to this definition">¶</a></dt>
<dd><p>The time and date of the last modification to the archive member.  This is a
tuple of six values:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Index</p></th>
<th class="head"><p>Value</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">0</span></code></p></td>
<td><p>Year (&gt;= 1980)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">1</span></code></p></td>
<td><p>Month (one-based)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">2</span></code></p></td>
<td><p>Day of month (one-based)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">3</span></code></p></td>
<td><p>Hours (zero-based)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">4</span></code></p></td>
<td><p>Minutes (zero-based)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">5</span></code></p></td>
<td><p>Seconds (zero-based)</p></td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The ZIP file format does not support timestamps before 1980.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.compress_type">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">compress_type</span></span><a class="headerlink" href="#zipfile.ZipInfo.compress_type" title="Link to this definition">¶</a></dt>
<dd><p>Type of compression for the archive member.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.comment">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">comment</span></span><a class="headerlink" href="#zipfile.ZipInfo.comment" title="Link to this definition">¶</a></dt>
<dd><p>Comment for the individual archive member as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.extra">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">extra</span></span><a class="headerlink" href="#zipfile.ZipInfo.extra" title="Link to this definition">¶</a></dt>
<dd><p>Expansion field data.  The <a class="reference external" href="https://pkware.cachefly.net/webdocs/casestudies/APPNOTE.TXT">PKZIP Application Note</a> contains
some comments on the internal structure of the data contained in this
<a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.create_system">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">create_system</span></span><a class="headerlink" href="#zipfile.ZipInfo.create_system" title="Link to this definition">¶</a></dt>
<dd><p>System which created ZIP archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.create_version">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">create_version</span></span><a class="headerlink" href="#zipfile.ZipInfo.create_version" title="Link to this definition">¶</a></dt>
<dd><p>PKZIP version which created ZIP archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.extract_version">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">extract_version</span></span><a class="headerlink" href="#zipfile.ZipInfo.extract_version" title="Link to this definition">¶</a></dt>
<dd><p>PKZIP version needed to extract archive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.reserved">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">reserved</span></span><a class="headerlink" href="#zipfile.ZipInfo.reserved" title="Link to this definition">¶</a></dt>
<dd><p>Must be zero.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.flag_bits">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">flag_bits</span></span><a class="headerlink" href="#zipfile.ZipInfo.flag_bits" title="Link to this definition">¶</a></dt>
<dd><p>ZIP flag bits.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.volume">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">volume</span></span><a class="headerlink" href="#zipfile.ZipInfo.volume" title="Link to this definition">¶</a></dt>
<dd><p>Volume number of file header.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.internal_attr">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">internal_attr</span></span><a class="headerlink" href="#zipfile.ZipInfo.internal_attr" title="Link to this definition">¶</a></dt>
<dd><p>Internal attributes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.external_attr">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">external_attr</span></span><a class="headerlink" href="#zipfile.ZipInfo.external_attr" title="Link to this definition">¶</a></dt>
<dd><p>External file attributes.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.header_offset">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">header_offset</span></span><a class="headerlink" href="#zipfile.ZipInfo.header_offset" title="Link to this definition">¶</a></dt>
<dd><p>Byte offset to the file header.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.CRC">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">CRC</span></span><a class="headerlink" href="#zipfile.ZipInfo.CRC" title="Link to this definition">¶</a></dt>
<dd><p>CRC-32 of the uncompressed file.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.compress_size">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">compress_size</span></span><a class="headerlink" href="#zipfile.ZipInfo.compress_size" title="Link to this definition">¶</a></dt>
<dd><p>Size of the compressed data.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="zipfile.ZipInfo.file_size">
<span class="sig-prename descclassname"><span class="pre">ZipInfo.</span></span><span class="sig-name descname"><span class="pre">file_size</span></span><a class="headerlink" href="#zipfile.ZipInfo.file_size" title="Link to this definition">¶</a></dt>
<dd><p>Size of the uncompressed file.</p>
</dd></dl>

</section>
<section id="command-line-interface">
<span id="zipfile-commandline"></span><h2>Command-Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-zipfile" title="zipfile: Read and write ZIP-format archive files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code></a> module provides a simple command-line interface to interact
with ZIP archives.</p>
<p>If you want to create a new ZIP archive, specify its name after the <a class="reference internal" href="#cmdoption-zipfile-c"><code class="xref std std-option docutils literal notranslate"><span class="pre">-c</span></code></a>
option and then list the filename(s) that should be included:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>zipfile<span class="w"> </span>-c<span class="w"> </span>monty.zip<span class="w"> </span>spam.txt<span class="w"> </span>eggs.txt
</pre></div>
</div>
<p>Passing a directory is also acceptable:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>zipfile<span class="w"> </span>-c<span class="w"> </span>monty.zip<span class="w"> </span>life-of-brian_1979/
</pre></div>
</div>
<p>If you want to extract a ZIP archive into the specified directory, use
the <a class="reference internal" href="#cmdoption-zipfile-e"><code class="xref std std-option docutils literal notranslate"><span class="pre">-e</span></code></a> option:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>zipfile<span class="w"> </span>-e<span class="w"> </span>monty.zip<span class="w"> </span>target-dir/
</pre></div>
</div>
<p>For a list of the files in a ZIP archive, use the <a class="reference internal" href="#cmdoption-zipfile-l"><code class="xref std std-option docutils literal notranslate"><span class="pre">-l</span></code></a> option:</p>
<div class="highlight-shell-session notranslate"><div class="highlight"><pre><span></span><span class="gp">$ </span>python<span class="w"> </span>-m<span class="w"> </span>zipfile<span class="w"> </span>-l<span class="w"> </span>monty.zip
</pre></div>
</div>
<section id="command-line-options">
<h3>Command-line options<a class="headerlink" href="#command-line-options" title="Link to this heading">¶</a></h3>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-zipfile-l">
<span class="sig-name descname"><span class="pre">-l</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-l" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-zipfile-list">
<span class="sig-name descname"><span class="pre">--list</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-list" title="Link to this definition">¶</a></dt>
<dd><p>List files in a zipfile.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-zipfile-c">
<span class="sig-name descname"><span class="pre">-c</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span> <span class="pre">&lt;source1&gt;</span> <span class="pre">...</span> <span class="pre">&lt;sourceN&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-c" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-zipfile-create">
<span class="sig-name descname"><span class="pre">--create</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span> <span class="pre">&lt;source1&gt;</span> <span class="pre">...</span> <span class="pre">&lt;sourceN&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-create" title="Link to this definition">¶</a></dt>
<dd><p>Create zipfile from source files.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-zipfile-e">
<span class="sig-name descname"><span class="pre">-e</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span> <span class="pre">&lt;output_dir&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-e" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-zipfile-extract">
<span class="sig-name descname"><span class="pre">--extract</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span> <span class="pre">&lt;output_dir&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-extract" title="Link to this definition">¶</a></dt>
<dd><p>Extract zipfile into target directory.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-zipfile-t">
<span class="sig-name descname"><span class="pre">-t</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-t" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-zipfile-test">
<span class="sig-name descname"><span class="pre">--test</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;zipfile&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-test" title="Link to this definition">¶</a></dt>
<dd><p>Test whether the zipfile is valid or not.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-zipfile-metadata-encoding">
<span class="sig-name descname"><span class="pre">--metadata-encoding</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;encoding&gt;</span></span><a class="headerlink" href="#cmdoption-zipfile-metadata-encoding" title="Link to this definition">¶</a></dt>
<dd><p>Specify encoding of member names for <a class="reference internal" href="#cmdoption-zipfile-l"><code class="xref std std-option docutils literal notranslate"><span class="pre">-l</span></code></a>, <a class="reference internal" href="#cmdoption-zipfile-e"><code class="xref std std-option docutils literal notranslate"><span class="pre">-e</span></code></a> and
<a class="reference internal" href="#cmdoption-zipfile-t"><code class="xref std std-option docutils literal notranslate"><span class="pre">-t</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
</section>
<section id="decompression-pitfalls">
<h2>Decompression pitfalls<a class="headerlink" href="#decompression-pitfalls" title="Link to this heading">¶</a></h2>
<p>The extraction in zipfile module might fail due to some pitfalls listed below.</p>
<section id="from-file-itself">
<h3>From file itself<a class="headerlink" href="#from-file-itself" title="Link to this heading">¶</a></h3>
<p>Decompression may fail due to incorrect password / CRC checksum / ZIP format or
unsupported compression method / decryption.</p>
</section>
<section id="file-system-limitations">
<h3>File System limitations<a class="headerlink" href="#file-system-limitations" title="Link to this heading">¶</a></h3>
<p>Exceeding limitations on different file systems can cause decompression failed.
Such as allowable characters in the directory entries, length of the file name,
length of the pathname, size of a single file, and number of files, etc.</p>
</section>
<section id="resources-limitations">
<span id="zipfile-resources-limitations"></span><h3>Resources limitations<a class="headerlink" href="#resources-limitations" title="Link to this heading">¶</a></h3>
<p>The lack of memory or disk volume would lead to decompression
failed. For example, decompression bombs (aka <a class="reference external" href="https://en.wikipedia.org/wiki/Zip_bomb">ZIP bomb</a>)
apply to zipfile library that can cause disk volume exhaustion.</p>
</section>
<section id="interruption">
<h3>Interruption<a class="headerlink" href="#interruption" title="Link to this heading">¶</a></h3>
<p>Interruption during the decompression, such as pressing control-C or killing the
decompression process may result in incomplete decompression of the archive.</p>
</section>
<section id="default-behaviors-of-extraction">
<h3>Default behaviors of extraction<a class="headerlink" href="#default-behaviors-of-extraction" title="Link to this heading">¶</a></h3>
<p>Not knowing the default extraction behaviors
can cause unexpected decompression results.
For example, when extracting the same archive twice,
it overwrites files without asking.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a><ul>
<li><a class="reference internal" href="#zipfile-objects">ZipFile Objects</a></li>
<li><a class="reference internal" href="#path-objects">Path Objects</a></li>
<li><a class="reference internal" href="#pyzipfile-objects">PyZipFile Objects</a></li>
<li><a class="reference internal" href="#zipinfo-objects">ZipInfo Objects</a></li>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a><ul>
<li><a class="reference internal" href="#command-line-options">Command-line options</a></li>
</ul>
</li>
<li><a class="reference internal" href="#decompression-pitfalls">Decompression pitfalls</a><ul>
<li><a class="reference internal" href="#from-file-itself">From file itself</a></li>
<li><a class="reference internal" href="#file-system-limitations">File System limitations</a></li>
<li><a class="reference internal" href="#resources-limitations">Resources limitations</a></li>
<li><a class="reference internal" href="#interruption">Interruption</a></li>
<li><a class="reference internal" href="#default-behaviors-of-extraction">Default behaviors of extraction</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="lzma.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">lzma</span></code> — Compression using the LZMA algorithm</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="tarfile.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">tarfile</span></code> — Read and write tar archive files</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/zipfile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="tarfile.html" title="tarfile — Read and write tar archive files"
             >next</a> |</li>
        <li class="right" >
          <a href="lzma.html" title="lzma — Compression using the LZMA algorithm"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="archiving.html" >Data Compression and Archiving</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipfile</span></code> — Work with ZIP archives</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>