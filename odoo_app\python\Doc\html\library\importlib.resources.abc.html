<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="importlib.resources.abc – Abstract base classes for resources" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/importlib.resources.abc.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/importlib/resources/abc.py" />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/importlib/resources/abc.py" />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>importlib.resources.abc – Abstract base classes for resources &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="importlib.metadata – Accessing package metadata" href="importlib.metadata.html" />
    <link rel="prev" title="importlib.resources – Package resource reading, opening and access" href="importlib.resources.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/importlib.resources.abc.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.resources.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.metadata.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.resources.abc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.metadata.html" title="importlib.metadata – Accessing package metadata"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="importlib.resources.html" title="importlib.resources – Package resource reading, opening and access"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-importlib.resources.abc">
<span id="importlib-resources-abc-abstract-base-classes-for-resources"></span><h1><a class="reference internal" href="#module-importlib.resources.abc" title="importlib.resources.abc: Abstract base classes for resources"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code></a> – Abstract base classes for resources<a class="headerlink" href="#module-importlib.resources.abc" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/importlib/resources/abc.py">Lib/importlib/resources/abc.py</a></p>
<hr class="docutils" />
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<dl class="py class">
<dt class="sig sig-object py" id="importlib.resources.abc.ResourceReader">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.resources.abc.</span></span><span class="sig-name descname"><span class="pre">ResourceReader</span></span><a class="headerlink" href="#importlib.resources.abc.ResourceReader" title="Link to this definition">¶</a></dt>
<dd><p><em>Superseded by TraversableResources</em></p>
<p>An <a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base class</span></a> to provide the ability to read
<em>resources</em>.</p>
<p>From the perspective of this ABC, a <em>resource</em> is a binary
artifact that is shipped within a package. Typically this is
something like a data file that lives next to the <code class="docutils literal notranslate"><span class="pre">__init__.py</span></code>
file of the package. The purpose of this class is to help abstract
out the accessing of such data files so that it does not matter if
the package and its data file(s) are stored in a e.g. zip file
versus on the file system.</p>
<p>For any of methods of this class, a <em>resource</em> argument is
expected to be a <a class="reference internal" href="../glossary.html#term-path-like-object"><span class="xref std std-term">path-like object</span></a> which represents
conceptually just a file name. This means that no subdirectory
paths should be included in the <em>resource</em> argument. This is
because the location of the package the reader is for, acts as the
“directory”. Hence the metaphor for directories and file
names is packages and resources, respectively. This is also why
instances of this class are expected to directly correlate to
a specific package (instead of potentially representing multiple
packages or a module).</p>
<p>Loaders that wish to support resource reading are expected to
provide a method called <code class="docutils literal notranslate"><span class="pre">get_resource_reader(fullname)</span></code> which
returns an object implementing this ABC’s interface. If the module
specified by fullname is not a package, this method should return
<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>. An object compatible with this ABC should only be
returned when the specified module is a package.</p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="#importlib.resources.abc.TraversableResources" title="importlib.resources.abc.TraversableResources"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.TraversableResources</span></code></a> instead.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.ResourceReader.open_resource">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">open_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.ResourceReader.open_resource" title="Link to this definition">¶</a></dt>
<dd><p>Returns an opened, <a class="reference internal" href="../glossary.html#term-file-like-object"><span class="xref std std-term">file-like object</span></a> for binary reading
of the <em>resource</em>.</p>
<p>If the resource cannot be found, <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> is
raised.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.ResourceReader.resource_path">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">resource_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.ResourceReader.resource_path" title="Link to this definition">¶</a></dt>
<dd><p>Returns the file system path to the <em>resource</em>.</p>
<p>If the resource does not concretely exist on the file system,
raise <a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.ResourceReader.is_resource">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_resource</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.ResourceReader.is_resource" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if the named <em>name</em> is considered a resource.
<a class="reference internal" href="exceptions.html#FileNotFoundError" title="FileNotFoundError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileNotFoundError</span></code></a> is raised if <em>name</em> does not exist.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.ResourceReader.contents">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">contents</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.ResourceReader.contents" title="Link to this definition">¶</a></dt>
<dd><p>Returns an <a class="reference internal" href="../glossary.html#term-iterable"><span class="xref std std-term">iterable</span></a> of strings over the contents of
the package. Do note that it is not required that all names
returned by the iterator be actual resources, e.g. it is
acceptable to return names for which <a class="reference internal" href="#importlib.resources.abc.ResourceReader.is_resource" title="importlib.resources.abc.ResourceReader.is_resource"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_resource()</span></code></a> would
be false.</p>
<p>Allowing non-resource names to be returned is to allow for
situations where how a package and its resources are stored
are known a priori and the non-resource names would be useful.
For instance, returning subdirectory names is allowed so that
when it is known that the package and resources are stored on
the file system then those subdirectory names can be used
directly.</p>
<p>The abstract method returns an iterable of no items.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.resources.abc.</span></span><span class="sig-name descname"><span class="pre">Traversable</span></span><a class="headerlink" href="#importlib.resources.abc.Traversable" title="Link to this definition">¶</a></dt>
<dd><p>An object with a subset of <a class="reference internal" href="pathlib.html#pathlib.Path" title="pathlib.Path"><code class="xref py py-class docutils literal notranslate"><span class="pre">pathlib.Path</span></code></a> methods suitable for
traversing directories and opening files.</p>
<p>For a representation of the object on the file-system, use
<a class="reference internal" href="importlib.resources.html#importlib.resources.as_file" title="importlib.resources.as_file"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.resources.as_file()</span></code></a>.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#importlib.resources.abc.Traversable.name" title="Link to this definition">¶</a></dt>
<dd><p>Abstract. The base name of this object without any parent references.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.iterdir">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">iterdir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.iterdir" title="Link to this definition">¶</a></dt>
<dd><p>Yield Traversable objects in self.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.is_dir">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.is_dir" title="Link to this definition">¶</a></dt>
<dd><p>Return True if self is a directory.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.is_file">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">is_file</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.is_file" title="Link to this definition">¶</a></dt>
<dd><p>Return True if self is a file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.joinpath">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">joinpath</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">pathsegments</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.joinpath" title="Link to this definition">¶</a></dt>
<dd><p>Traverse directories according to <em>pathsegments</em> and return
the result as <code class="xref py py-class docutils literal notranslate"><span class="pre">Traversable</span></code>.</p>
<p>Each <em>pathsegments</em> argument may contain multiple names separated by
forward slashes (<code class="docutils literal notranslate"><span class="pre">/</span></code>, <code class="docutils literal notranslate"><span class="pre">posixpath.sep</span></code> ).
For example, the following are equivalent:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;subdir&#39;</span><span class="p">,</span> <span class="s1">&#39;subsuddir&#39;</span><span class="p">,</span> <span class="s1">&#39;file.txt&#39;</span><span class="p">)</span>
<span class="n">files</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;subdir/subsuddir/file.txt&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Note that some <code class="xref py py-class docutils literal notranslate"><span class="pre">Traversable</span></code> implementations
might not be updated to the latest version of the protocol.
For compatibility with such implementations, provide a single argument
without path separators to each call to <code class="docutils literal notranslate"><span class="pre">joinpath</span></code>. For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">files</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;subdir&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;subsubdir&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="s1">&#39;file.txt&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><code class="docutils literal notranslate"><span class="pre">joinpath</span></code> accepts multiple <em>pathsegments</em>, and these segments
may contain forward slashes as path separators.
Previously, only a single <em>child</em> argument was accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.__truediv__">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">__truediv__</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">child</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.__truediv__" title="Link to this definition">¶</a></dt>
<dd><p>Return Traversable child in self.
Equivalent to <code class="docutils literal notranslate"><span class="pre">joinpath(child)</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.open">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'r'</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.open" title="Link to this definition">¶</a></dt>
<dd><p><em>mode</em> may be ‘r’ or ‘rb’ to open as text or binary. Return a handle
suitable for reading (same as <a class="reference internal" href="pathlib.html#pathlib.Path.open" title="pathlib.Path.open"><code class="xref py py-attr docutils literal notranslate"><span class="pre">pathlib.Path.open</span></code></a>).</p>
<p>When opening as text, accepts encoding parameters such as those
accepted by <a class="reference internal" href="io.html#io.TextIOWrapper" title="io.TextIOWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">io.TextIOWrapper</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.read_bytes">
<span class="sig-name descname"><span class="pre">read_bytes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.read_bytes" title="Link to this definition">¶</a></dt>
<dd><p>Read contents of self as bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.Traversable.read_text">
<span class="sig-name descname"><span class="pre">read_text</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.Traversable.read_text" title="Link to this definition">¶</a></dt>
<dd><p>Read contents of self as text.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="importlib.resources.abc.TraversableResources">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">importlib.resources.abc.</span></span><span class="sig-name descname"><span class="pre">TraversableResources</span></span><a class="headerlink" href="#importlib.resources.abc.TraversableResources" title="Link to this definition">¶</a></dt>
<dd><p>An abstract base class for resource readers capable of serving
the <a class="reference internal" href="importlib.resources.html#importlib.resources.files" title="importlib.resources.files"><code class="xref py py-meth docutils literal notranslate"><span class="pre">importlib.resources.files()</span></code></a> interface. Subclasses
<a class="reference internal" href="#importlib.resources.abc.ResourceReader" title="importlib.resources.abc.ResourceReader"><code class="xref py py-class docutils literal notranslate"><span class="pre">ResourceReader</span></code></a> and provides
concrete implementations of the <code class="xref py py-class docutils literal notranslate"><span class="pre">ResourceReader</span></code>’s
abstract methods. Therefore, any loader supplying
<code class="xref py py-class docutils literal notranslate"><span class="pre">TraversableResources</span></code> also supplies <code class="xref py py-class docutils literal notranslate"><span class="pre">ResourceReader</span></code>.</p>
<p>Loaders that wish to support resource reading are expected to
implement this interface.</p>
<dl class="py method">
<dt class="sig sig-object py" id="importlib.resources.abc.TraversableResources.files">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">files</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#importlib.resources.abc.TraversableResources.files" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <a class="reference internal" href="#importlib.resources.abc.Traversable" title="importlib.resources.abc.Traversable"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.resources.abc.Traversable</span></code></a> object for the loaded
package.</p>
</dd></dl>

</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="importlib.resources.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources</span></code> – Package resource reading, opening and access</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="importlib.metadata.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.metadata</span></code> – Accessing package metadata</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/importlib.resources.abc.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="importlib.metadata.html" title="importlib.metadata – Accessing package metadata"
             >next</a> |</li>
        <li class="right" >
          <a href="importlib.resources.html" title="importlib.resources – Package resource reading, opening and access"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib.resources.abc</span></code> – Abstract base classes for resources</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>