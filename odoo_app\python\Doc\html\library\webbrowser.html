<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="webbrowser — Convenient web-browser controller" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/webbrowser.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/webbrowser.py The webbrowser module provides a high-level interface to allow displaying web-based documents to users. Under most circumstances, simply calling the open() function f..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/webbrowser.py The webbrowser module provides a high-level interface to allow displaying web-based documents to users. Under most circumstances, simply calling the open() function f..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>webbrowser — Convenient web-browser controller &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="wsgiref — WSGI Utilities and Reference Implementation" href="wsgiref.html" />
    <link rel="prev" title="Internet Protocols and Support" href="internet.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/webbrowser.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a><ul>
<li><a class="reference internal" href="#browser-controller-objects">Browser Controller Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="internet.html"
                          title="previous chapter">Internet Protocols and Support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="wsgiref.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/webbrowser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="wsgiref.html" title="wsgiref — WSGI Utilities and Reference Implementation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="internet.html" title="Internet Protocols and Support"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-webbrowser">
<span id="webbrowser-convenient-web-browser-controller"></span><h1><a class="reference internal" href="#module-webbrowser" title="webbrowser: Easy-to-use controller for web browsers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code></a> — Convenient web-browser controller<a class="headerlink" href="#module-webbrowser" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/webbrowser.py">Lib/webbrowser.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-webbrowser" title="webbrowser: Easy-to-use controller for web browsers."><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code></a> module provides a high-level interface to allow displaying
web-based documents to users. Under most circumstances, simply calling the
<a class="reference internal" href="#webbrowser.open" title="webbrowser.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> function from this module will do the right thing.</p>
<p>Under Unix, graphical browsers are preferred under X11, but text-mode browsers
will be used if graphical browsers are not available or an X11 display isn’t
available.  If text-mode browsers are used, the calling process will block until
the user exits the browser.</p>
<p>If the environment variable <span class="target" id="index-0"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">BROWSER</span></code> exists, it is interpreted as the
<a class="reference internal" href="os.html#os.pathsep" title="os.pathsep"><code class="xref py py-data docutils literal notranslate"><span class="pre">os.pathsep</span></code></a>-separated list of browsers to try ahead of the platform
defaults.  When the value of a list part contains the string <code class="docutils literal notranslate"><span class="pre">%s</span></code>, then it is
interpreted as a literal browser command line to be used with the argument URL
substituted for <code class="docutils literal notranslate"><span class="pre">%s</span></code>; if the part does not contain <code class="docutils literal notranslate"><span class="pre">%s</span></code>, it is simply
interpreted as the name of the browser to launch. <a class="footnote-reference brackets" href="#id2" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
<p>For non-Unix platforms, or when a remote browser is available on Unix, the
controlling process will not wait for the user to finish with the browser, but
allow the remote browser to maintain its own windows on the display.  If remote
browsers are not available on Unix, the controlling process will launch a new
browser and wait.</p>
<p>The script <strong class="program">webbrowser</strong> can be used as a command-line interface for the
module. It accepts a URL as the argument. It accepts the following optional
parameters: <code class="docutils literal notranslate"><span class="pre">-n</span></code> opens the URL in a new browser window, if possible;
<code class="docutils literal notranslate"><span class="pre">-t</span></code> opens the URL in a new browser page (“tab”). The options are,
naturally, mutually exclusive.  Usage example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">webbrowser</span> <span class="o">-</span><span class="n">t</span> <span class="s2">&quot;https://www.python.org&quot;</span>
</pre></div>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>The following exception is defined:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="webbrowser.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#webbrowser.Error" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when a browser control error occurs.</p>
</dd></dl>

<p>The following functions are defined:</p>
<dl class="py function">
<dt class="sig sig-object py" id="webbrowser.open">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autoraise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.open" title="Link to this definition">¶</a></dt>
<dd><p>Display <em>url</em> using the default browser. If <em>new</em> is 0, the <em>url</em> is opened
in the same browser window if possible.  If <em>new</em> is 1, a new browser window
is opened if possible.  If <em>new</em> is 2, a new browser page (“tab”) is opened
if possible.  If <em>autoraise</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, the window is raised if possible
(note that under many window managers this will occur regardless of the
setting of this variable).</p>
<p>Note that on some platforms, trying to open a filename using this function,
may work and start the operating system’s associated program.  However, this
is neither supported nor portable.</p>
<p class="audit-hook">Raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a> <code class="docutils literal notranslate"><span class="pre">webbrowser.open</span></code> with argument <code class="docutils literal notranslate"><span class="pre">url</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="webbrowser.open_new">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">open_new</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.open_new" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em> in a new window of the default browser, if possible, otherwise, open
<em>url</em> in the only browser window.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="webbrowser.open_new_tab">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">open_new_tab</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.open_new_tab" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em> in a new page (“tab”) of the default browser, if possible, otherwise
equivalent to <a class="reference internal" href="#webbrowser.open_new" title="webbrowser.open_new"><code class="xref py py-func docutils literal notranslate"><span class="pre">open_new()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="webbrowser.get">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">using</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.get" title="Link to this definition">¶</a></dt>
<dd><p>Return a controller object for the browser type <em>using</em>.  If <em>using</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code>, return a controller for a default browser appropriate to the
caller’s environment.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="webbrowser.register">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">register</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">constructor</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">instance</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">preferred</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.register" title="Link to this definition">¶</a></dt>
<dd><p>Register the browser type <em>name</em>.  Once a browser type is registered, the
<a class="reference internal" href="#webbrowser.get" title="webbrowser.get"><code class="xref py py-func docutils literal notranslate"><span class="pre">get()</span></code></a> function can return a controller for that browser type.  If
<em>instance</em> is not provided, or is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <em>constructor</em> will be called without
parameters to create an instance when needed.  If <em>instance</em> is provided,
<em>constructor</em> will never be called, and may be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<p>Setting <em>preferred</em> to <code class="docutils literal notranslate"><span class="pre">True</span></code> makes this browser a preferred result for
a <a class="reference internal" href="#webbrowser.get" title="webbrowser.get"><code class="xref py py-func docutils literal notranslate"><span class="pre">get()</span></code></a> call with no argument.  Otherwise, this entry point is only
useful if you plan to either set the <span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">BROWSER</span></code> variable or call
<a class="reference internal" href="#webbrowser.get" title="webbrowser.get"><code class="xref py py-func docutils literal notranslate"><span class="pre">get()</span></code></a> with a nonempty argument matching the name of a handler you
declare.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span><em>preferred</em> keyword-only parameter was added.</p>
</div>
</dd></dl>

<p>A number of browser types are predefined.  This table gives the type names that
may be passed to the <a class="reference internal" href="#webbrowser.get" title="webbrowser.get"><code class="xref py py-func docutils literal notranslate"><span class="pre">get()</span></code></a> function and the corresponding instantiations
for the controller classes, all defined in this module.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Type Name</p></th>
<th class="head"><p>Class Name</p></th>
<th class="head"><p>Notes</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'mozilla'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Mozilla('mozilla')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'firefox'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Mozilla('mozilla')</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'epiphany'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Epiphany('epiphany')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'kfmclient'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Konqueror()</span></code></p></td>
<td><p>(1)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'konqueror'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Konqueror()</span></code></p></td>
<td><p>(1)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'kfm'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Konqueror()</span></code></p></td>
<td><p>(1)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'opera'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Opera()</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'links'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">GenericBrowser('links')</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'elinks'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Elinks('elinks')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'lynx'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">GenericBrowser('lynx')</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'w3m'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">GenericBrowser('w3m')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'windows-default'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">WindowsDefault</span></code></p></td>
<td><p>(2)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'macosx'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">MacOSXOSAScript('default')</span></code></p></td>
<td><p>(3)</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'safari'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">MacOSXOSAScript('safari')</span></code></p></td>
<td><p>(3)</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'google-chrome'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Chrome('google-chrome')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'chrome'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Chrome('chrome')</span></code></p></td>
<td></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">'chromium'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Chromium('chromium')</span></code></p></td>
<td></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">'chromium-browser'</span></code></p></td>
<td><p><code class="xref py py-class docutils literal notranslate"><span class="pre">Chromium('chromium-browser')</span></code></p></td>
<td></td>
</tr>
</tbody>
</table>
<p>Notes:</p>
<ol class="arabic simple">
<li><p>“Konqueror” is the file manager for the KDE desktop environment for Unix, and
only makes sense to use if KDE is running.  Some way of reliably detecting KDE
would be nice; the <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">KDEDIR</span></code> variable is not sufficient.  Note also that
the name “kfm” is used even when using the <strong class="program">konqueror</strong> command with KDE
2 — the implementation selects the best strategy for running Konqueror.</p></li>
<li><p>Only on Windows platforms.</p></li>
<li><p>Only on macOS platform.</p></li>
</ol>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>Support for Chrome/Chromium has been added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Support for several obsolete browsers has been removed.
Removed browsers include Grail, Mosaic, Netscape, Galeon,
Skipstone, Iceape, and Firefox versions 35 and below.</p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span><code class="xref py py-class docutils literal notranslate"><span class="pre">MacOSX</span></code> is deprecated, use <code class="xref py py-class docutils literal notranslate"><span class="pre">MacOSXOSAScript</span></code> instead.</p>
</div>
<p>Here are some simple examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">url</span> <span class="o">=</span> <span class="s1">&#39;https://docs.python.org/&#39;</span>

<span class="c1"># Open URL in a new tab, if a browser window is already open.</span>
<span class="n">webbrowser</span><span class="o">.</span><span class="n">open_new_tab</span><span class="p">(</span><span class="n">url</span><span class="p">)</span>

<span class="c1"># Open URL in new window, raising the window if possible.</span>
<span class="n">webbrowser</span><span class="o">.</span><span class="n">open_new</span><span class="p">(</span><span class="n">url</span><span class="p">)</span>
</pre></div>
</div>
<section id="browser-controller-objects">
<span id="browser-controllers"></span><h2>Browser Controller Objects<a class="headerlink" href="#browser-controller-objects" title="Link to this heading">¶</a></h2>
<p>Browser controllers provide these methods which parallel three of the
module-level convenience functions:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="webbrowser.name">
<span class="sig-prename descclassname"><span class="pre">webbrowser.</span></span><span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#webbrowser.name" title="Link to this definition">¶</a></dt>
<dd><p>System-dependent name for the browser.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="webbrowser.controller.open">
<span class="sig-prename descclassname"><span class="pre">controller.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">autoraise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.controller.open" title="Link to this definition">¶</a></dt>
<dd><p>Display <em>url</em> using the browser handled by this controller. If <em>new</em> is 1, a new
browser window is opened if possible. If <em>new</em> is 2, a new browser page (“tab”)
is opened if possible.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="webbrowser.controller.open_new">
<span class="sig-prename descclassname"><span class="pre">controller.</span></span><span class="sig-name descname"><span class="pre">open_new</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.controller.open_new" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em> in a new window of the browser handled by this controller, if
possible, otherwise, open <em>url</em> in the only browser window.  Alias
<a class="reference internal" href="#webbrowser.open_new" title="webbrowser.open_new"><code class="xref py py-func docutils literal notranslate"><span class="pre">open_new()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="webbrowser.controller.open_new_tab">
<span class="sig-prename descclassname"><span class="pre">controller.</span></span><span class="sig-name descname"><span class="pre">open_new_tab</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#webbrowser.controller.open_new_tab" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em> in a new page (“tab”) of the browser handled by this controller, if
possible, otherwise equivalent to <a class="reference internal" href="#webbrowser.open_new" title="webbrowser.open_new"><code class="xref py py-func docutils literal notranslate"><span class="pre">open_new()</span></code></a>.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id2" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>Executables named here without a full path will be searched in the
directories given in the <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">PATH</span></code> environment variable.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a><ul>
<li><a class="reference internal" href="#browser-controller-objects">Browser Controller Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="internet.html"
                          title="previous chapter">Internet Protocols and Support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="wsgiref.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">wsgiref</span></code> — WSGI Utilities and Reference Implementation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/webbrowser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="wsgiref.html" title="wsgiref — WSGI Utilities and Reference Implementation"
             >next</a> |</li>
        <li class="right" >
          <a href="internet.html" title="Internet Protocols and Support"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">webbrowser</span></code> — Convenient web-browser controller</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>