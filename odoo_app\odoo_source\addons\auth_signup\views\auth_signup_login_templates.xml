<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <template id="auth_signup.login" inherit_id="web.login" name="Sign up - Reset Password">
            <xpath expr="//button[@type='submit']" position="after">
                <div class="justify-content-between mt-2 d-flex small">
                    <a t-if="signup_enabled" t-attf-href="/web/signup?{{ keep_query() }}">Don't have an account?</a>
                    <a t-if="reset_password_enabled" t-attf-href="/web/reset_password?{{ keep_query() }}">Reset Password</a>
                </div>
            </xpath>
        </template>

        <template id="auth_signup.fields" name="Auth Signup/ResetPassword form fields">

            <div class="mb-3 field-login">
                <label for="login">Your Email</label>
                <input type="text" name="login" t-att-value="login" id="login" class="form-control form-control-sm" autofocus="autofocus"
                    autocapitalize="off" required="required" t-att-readonly="'readonly' if only_passwords else None"/>
            </div>

            <div class="mb-3 field-name">
                <label for="name">Your Name</label>
                <input type="text" name="name" t-att-value="name" id="name" class="form-control form-control-sm" placeholder="e.g. John Doe"
                    required="required" t-att-readonly="'readonly' if only_passwords else None"
                    t-att-autofocus="'autofocus' if login and not only_passwords else None" />
            </div>

            <div class="mb-3 field-password pt-2">
                <label for="password">Password</label>
                <input type="password" name="password" id="password" class="form-control form-control-sm"
                    required="required" t-att-autofocus="'autofocus' if only_passwords else None"/>
            </div>

            <div class="mb-3 field-confirm_password">
                <label for="confirm_password">Confirm Password</label>
                <input type="password" name="confirm_password" id="confirm_password" class="form-control form-control-sm" required="required"/>
            </div>
        </template>

        <template id="auth_signup.signup" name="Sign up login">
            <t t-call="web.login_layout">
                <form class="oe_signup_form" role="form" method="post" t-if="not message">
                  <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                    <t t-call="auth_signup.fields">
                        <t t-set="only_passwords" t-value="bool(token and not invalid_token)"/>
                    </t>

                    <p class="alert alert-danger" t-if="error" role="alert">
                        <t t-esc="error"/>
                    </p>
                    <input type="hidden" name="redirect" t-att-value="redirect"/>
                    <input type="hidden" name="token" t-att-value="token"/>
                    <div class="text-center oe_login_buttons d-grid pt-3">
                        <button type="submit" class="btn btn-primary"> Sign up</button>
                        <a t-attf-href="/web/login?{{ keep_query() }}" class="btn btn-link btn-sm" role="button">Already have an account?</a>
                        <div class="o_login_auth"/>
                    </div>
                </form>
            </t>
        </template>

        <template id="auth_signup.reset_password" name="Reset password">
            <t t-call="web.login_layout">
                <div t-if="message" class="oe_login_form clearfix">
                    <p class="alert alert-success" t-if="message" role="status">
                        <t t-esc="message"/>
                    </p>
                    <a href="/web/login" class="btn btn-link btn-sm float-start" role="button">Back to Login</a>
                </div>

                <form class="oe_reset_password_form" role="form" method="post" t-if="not message">
                  <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                    <t t-if="token and not invalid_token">
                        <t t-call="auth_signup.fields">
                            <t t-set="only_passwords" t-value="1"/>
                        </t>
                    </t>

                    <t t-if="not token">
                        <div class="mb-3 field-login">
                            <label for="login" class="col-form-label">Your Email</label>
                            <input type="text" name="login" t-att-value="login" id="login" class="form-control"
                                autofocus="autofocus" required="required" autocapitalize="off"/>
                        </div>
                    </t>

                    <p class="alert alert-danger" t-if="error" role="alert">
                        <t t-esc="error"/>
                    </p>
                    <input type="hidden" name="redirect" t-att-value="redirect"/>
                    <input type="hidden" name="token" t-att-value="token"/>
                    <div class="clearfix oe_login_buttons d-grid mt-3">
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                        <div class="d-flex justify-content-between align-items-center small mt-2">
                            <a t-if="not token" t-attf-href="/web/login?{{ keep_query() }}">Back to Login</a>
                            <a t-if="invalid_token" href="/web/login">Back to Login</a>
                        </div>
                        <div class="o_login_auth"/>
                    </div>

                </form>

            </t>
        </template>
</odoo>
