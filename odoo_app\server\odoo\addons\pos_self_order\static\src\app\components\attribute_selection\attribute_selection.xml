<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.AttributeSelection">
        <div class="self_order_attribute_selection d-flex flex-column flex-grow-1">
            <div class="attribute-selection-content align-items-center justify-content-start px-3 flex-grow-1">
                <div class="d-flex flex-column">
                    <div t-foreach="this.props.product.attributes" t-as="attribute" t-key="attribute.id" class="attribute-row">
                        <h2 t-out="attribute.name"/>
                        <div class="row g-2 g-md-3 g-xl-4 justify-content-between justify-content-md-start mb-5 row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-xl-5 row-cols-xxl-6"
                            t-ref="attribute_grid_{{attribute.id}}">
                            <t t-foreach="availableAttributeValue(attribute)" t-as="value" t-key="value.id">
                                <div class="col">
                                    <label t-attf-for="{{ attribute.id }}_{{ value.id }}"
                                            t-attf-class="self_order_attribute_selection_option {{ this.isChecked(attribute, value) ? 'text-bg-primary border-primary active' : '' }}
                                            d-flex align-items-center justify-content-center h-100 rounded border ratio ratio-16x9"
                                            t-ref="value_{{attribute.id}}_{{value.id}}">
                                        <div class="name position-relative d-flex flex-column justify-content-center align-items-center flex-grow-1 w-100 p-4 text-center">
                                            <span t-out="value.name"/>
                                            <span t-if="shouldShowPriceExtra(value)">
                                                <t t-esc="getfPriceExtra(value)"/>
                                            </span>
                                        </div>
                                    </label>
                                    <input
                                        type="radio" 
                                        class="d-none"
                                        t-if="attribute.display_type !== 'multi'"
                                        t-att-value="value.id"
                                        t-attf-id="{{ attribute.id }}_{{ value.id }}"
                                        t-model="this.selectedValues[attribute.id]" />
                                    <input
                                        type="checkbox"
                                        class="d-none"
                                        t-else=""
                                        t-att-checked="this.isChecked(attribute, value)"
                                        t-att-value="value.id"
                                        t-model="this.selectedValues[attribute.id][value.id]"
                                        t-attf-id="{{ attribute.id }}_{{ value.id }}" />
                                </div>
                                <div t-if="this.isChecked(attribute, value) and selfOrder.attributeValueById[value.id].is_custom" class="col w-100 order-2">
                                    <input type="text" t-model="this.env.customValues[value.id].custom_value" class="form-control form-control-lg" placeholder="Enter your custom value" />
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
