<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="paperformat_label_sheet" model="report.paperformat">
            <field name="name">A4 Label Sheet</field>
            <field name="default" eval="True" />
            <field name="format">A4</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">0</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">96</field>
        </record>
        <record id="report_product_template_label_2x7" model="ir.actions.report">
            <field name="name">Product Label 2x7 (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_producttemplatelabel2x7</field>
            <field name="report_file">product.report_producttemplatelabel2x7</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>
        <record id="report_product_template_label_4x7" model="ir.actions.report">
            <field name="name">Product Label 4x7 (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_producttemplatelabel4x7</field>
            <field name="report_file">product.report_producttemplatelabel4x7</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>
        <record id="report_product_template_label_4x12" model="ir.actions.report">
            <field name="name">Product Label 4x12 (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_producttemplatelabel4x12</field>
            <field name="report_file">product.report_producttemplatelabel4x12</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>
        <record id="report_product_template_label_4x12_noprice" model="ir.actions.report">
            <field name="name">Product Label 4x12 No Price (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_producttemplatelabel4x12noprice</field>
            <field name="report_file">product.report_producttemplatelabel4x12noprice</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_model_id" eval="False"/>
            <field name="binding_type">report</field>
        </record>

        <record id="report_product_packaging" model="ir.actions.report">
            <field name="name">Product Packaging (PDF)</field>
            <field name="model">product.packaging</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_packagingbarcode</field>
            <field name="report_file">product.report_packagingbarcode</field>
            <field name="print_report_name">'Products packaging - %s' % (object.name)</field>
            <field name="binding_model_id" ref="product.model_product_packaging"/>
            <field name="binding_type">report</field>
        </record>

        <record id="action_report_pricelist" model="ir.actions.report">
            <field name="name">Pricelist</field>
            <field name="model">product.product</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_pricelist</field>
            <field name="report_file">product.report_pricelist</field>
        </record>

        <record id="paperformat_label_sheet_dymo" model="report.paperformat">
            <field name="name">Dymo Label Sheet</field>
            <field name="default" eval="True" />
            <field name="format">custom</field>
            <field name="page_height">57</field>
            <field name="page_width">32</field>
            <field name="orientation">Landscape</field>
            <field name="margin_top">0</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">96</field>
        </record>

        <record id="report_product_template_label_dymo" model="ir.actions.report">
            <field name="name">Product Label (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">product.report_producttemplatelabel_dymo</field>
            <field name="report_file">product.report_producttemplatelabel_dymo</field>
            <field name="paperformat_id" ref="product.paperformat_label_sheet_dymo"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>
