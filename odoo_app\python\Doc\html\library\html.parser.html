<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="html.parser — Simple HTML and XHTML parser" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/html.parser.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/html/parser.py This module defines a class HTMLParser which serves as the basis for parsing text files formatted in HTML (HyperText Mark-up Language) and XHTML. Example HTML Parser..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/html/parser.py This module defines a class HTMLParser which serves as the basis for parsing text files formatted in HTML (HyperText Mark-up Language) and XHTML. Example HTML Parser..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>html.parser — Simple HTML and XHTML parser &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="html.entities — Definitions of HTML general entities" href="html.entities.html" />
    <link rel="prev" title="html — HyperText Markup Language support" href="html.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/html.parser.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code> — Simple HTML and XHTML parser</a><ul>
<li><a class="reference internal" href="#example-html-parser-application">Example HTML Parser Application</a></li>
<li><a class="reference internal" href="#htmlparser-methods"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code> Methods</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="html.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html</span></code> — HyperText Markup Language support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="html.entities.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code> — Definitions of HTML general entities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/html.parser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="html.entities.html" title="html.entities — Definitions of HTML general entities"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="html.html" title="html — HyperText Markup Language support"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code> — Simple HTML and XHTML parser</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-html.parser">
<span id="html-parser-simple-html-and-xhtml-parser"></span><h1><a class="reference internal" href="#module-html.parser" title="html.parser: A simple parser that can handle HTML and XHTML."><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code></a> — Simple HTML and XHTML parser<a class="headerlink" href="#module-html.parser" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/html/parser.py">Lib/html/parser.py</a></p>
<hr class="docutils" id="index-0" />
<p>This module defines a class <a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> which serves as the basis for
parsing text files formatted in HTML (HyperText Mark-up Language) and XHTML.</p>
<dl class="py class">
<dt class="sig sig-object py" id="html.parser.HTMLParser">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">html.parser.</span></span><span class="sig-name descname"><span class="pre">HTMLParser</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">convert_charrefs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser" title="Link to this definition">¶</a></dt>
<dd><p>Create a parser instance able to parse invalid markup.</p>
<p>If <em>convert_charrefs</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code> (the default), all character
references (except the ones in <code class="docutils literal notranslate"><span class="pre">script</span></code>/<code class="docutils literal notranslate"><span class="pre">style</span></code> elements) are
automatically converted to the corresponding Unicode characters.</p>
<p>An <a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> instance is fed HTML data and calls handler methods
when start tags, end tags, text, comments, and other markup elements are
encountered.  The user should subclass <a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> and override its
methods to implement the desired behavior.</p>
<p>This parser does not check that end tags match start tags or call the end-tag
handler for elements which are closed implicitly by closing an outer element.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span><em>convert_charrefs</em> keyword argument added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The default value for argument <em>convert_charrefs</em> is now <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</div>
</dd></dl>

<section id="example-html-parser-application">
<h2>Example HTML Parser Application<a class="headerlink" href="#example-html-parser-application" title="Link to this heading">¶</a></h2>
<p>As a basic example, below is a simple HTML parser that uses the
<a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> class to print out start tags, end tags, and data
as they are encountered:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">html.parser</span> <span class="kn">import</span> <span class="n">HTMLParser</span>

<span class="k">class</span> <span class="nc">MyHTMLParser</span><span class="p">(</span><span class="n">HTMLParser</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">handle_starttag</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">,</span> <span class="n">attrs</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Encountered a start tag:&quot;</span><span class="p">,</span> <span class="n">tag</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_endtag</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Encountered an end tag :&quot;</span><span class="p">,</span> <span class="n">tag</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_data</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Encountered some data  :&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

<span class="n">parser</span> <span class="o">=</span> <span class="n">MyHTMLParser</span><span class="p">()</span>
<span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;html&gt;&lt;head&gt;&lt;title&gt;Test&lt;/title&gt;&lt;/head&gt;&#39;</span>
            <span class="s1">&#39;&lt;body&gt;&lt;h1&gt;Parse me!&lt;/h1&gt;&lt;/body&gt;&lt;/html&gt;&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>The output will then be:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Encountered a start tag: html
Encountered a start tag: head
Encountered a start tag: title
Encountered some data  : Test
Encountered an end tag : title
Encountered an end tag : head
Encountered a start tag: body
Encountered a start tag: h1
Encountered some data  : Parse me!
Encountered an end tag : h1
Encountered an end tag : body
Encountered an end tag : html
</pre></div>
</div>
</section>
<section id="htmlparser-methods">
<h2><a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> Methods<a class="headerlink" href="#htmlparser-methods" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.feed">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">feed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.feed" title="Link to this definition">¶</a></dt>
<dd><p>Feed some text to the parser.  It is processed insofar as it consists of
complete elements; incomplete data is buffered until more data is fed or
<a class="reference internal" href="#html.parser.HTMLParser.close" title="html.parser.HTMLParser.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a> is called.  <em>data</em> must be <a class="reference internal" href="stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.close">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.close" title="Link to this definition">¶</a></dt>
<dd><p>Force processing of all buffered data as if it were followed by an end-of-file
mark.  This method may be redefined by a derived class to define additional
processing at the end of the input, but the redefined version should always call
the <a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> base class method <a class="reference internal" href="#html.parser.HTMLParser.close" title="html.parser.HTMLParser.close"><code class="xref py py-meth docutils literal notranslate"><span class="pre">close()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.reset">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.reset" title="Link to this definition">¶</a></dt>
<dd><p>Reset the instance.  Loses all unprocessed data.  This is called implicitly at
instantiation time.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.getpos">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">getpos</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.getpos" title="Link to this definition">¶</a></dt>
<dd><p>Return current line number and offset.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.get_starttag_text">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">get_starttag_text</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.get_starttag_text" title="Link to this definition">¶</a></dt>
<dd><p>Return the text of the most recently opened start tag.  This should not normally
be needed for structured processing, but may be useful in dealing with HTML “as
deployed” or for re-generating input with minimal changes (whitespace between
attributes can be preserved, etc.).</p>
</dd></dl>

<p>The following methods are called when data or markup elements are encountered
and they are meant to be overridden in a subclass.  The base class
implementations do nothing (except for <a class="reference internal" href="#html.parser.HTMLParser.handle_startendtag" title="html.parser.HTMLParser.handle_startendtag"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_startendtag()</span></code></a>):</p>
<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_starttag">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_starttag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_starttag" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to handle the start tag of an element (e.g. <code class="docutils literal notranslate"><span class="pre">&lt;div</span> <span class="pre">id=&quot;main&quot;&gt;</span></code>).</p>
<p>The <em>tag</em> argument is the name of the tag converted to lower case. The <em>attrs</em>
argument is a list of <code class="docutils literal notranslate"><span class="pre">(name,</span> <span class="pre">value)</span></code> pairs containing the attributes found
inside the tag’s <code class="docutils literal notranslate"><span class="pre">&lt;&gt;</span></code> brackets.  The <em>name</em> will be translated to lower case,
and quotes in the <em>value</em> have been removed, and character and entity references
have been replaced.</p>
<p>For instance, for the tag <code class="docutils literal notranslate"><span class="pre">&lt;A</span> <span class="pre">HREF=&quot;https://www.cwi.nl/&quot;&gt;</span></code>, this method
would be called as <code class="docutils literal notranslate"><span class="pre">handle_starttag('a',</span> <span class="pre">[('href',</span> <span class="pre">'https://www.cwi.nl/')])</span></code>.</p>
<p>All entity references from <a class="reference internal" href="html.entities.html#module-html.entities" title="html.entities: Definitions of HTML general entities."><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code></a> are replaced in the attribute
values.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_endtag">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_endtag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_endtag" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to handle the end tag of an element (e.g. <code class="docutils literal notranslate"><span class="pre">&lt;/div&gt;</span></code>).</p>
<p>The <em>tag</em> argument is the name of the tag converted to lower case.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_startendtag">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_startendtag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tag</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_startendtag" title="Link to this definition">¶</a></dt>
<dd><p>Similar to <a class="reference internal" href="#html.parser.HTMLParser.handle_starttag" title="html.parser.HTMLParser.handle_starttag"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_starttag()</span></code></a>, but called when the parser encounters an
XHTML-style empty tag (<code class="docutils literal notranslate"><span class="pre">&lt;img</span> <span class="pre">...</span> <span class="pre">/&gt;</span></code>).  This method may be overridden by
subclasses which require this particular lexical information; the default
implementation simply calls <a class="reference internal" href="#html.parser.HTMLParser.handle_starttag" title="html.parser.HTMLParser.handle_starttag"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_starttag()</span></code></a> and <a class="reference internal" href="#html.parser.HTMLParser.handle_endtag" title="html.parser.HTMLParser.handle_endtag"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_endtag()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_data">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_data" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to process arbitrary data (e.g. text nodes and the
content of <code class="docutils literal notranslate"><span class="pre">&lt;script&gt;...&lt;/script&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;style&gt;...&lt;/style&gt;</span></code>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_entityref">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_entityref</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_entityref" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to process a named character reference of the form
<code class="docutils literal notranslate"><span class="pre">&amp;name;</span></code> (e.g. <code class="docutils literal notranslate"><span class="pre">&amp;gt;</span></code>), where <em>name</em> is a general entity reference
(e.g. <code class="docutils literal notranslate"><span class="pre">'gt'</span></code>).  This method is never called if <em>convert_charrefs</em> is
<code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_charref">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_charref</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_charref" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to process decimal and hexadecimal numeric character
references of the form <code class="samp docutils literal notranslate"><span class="pre">&amp;#</span><em><span class="pre">NNN</span></em><span class="pre">;</span></code> and <code class="samp docutils literal notranslate"><span class="pre">&amp;#x</span><em><span class="pre">NNN</span></em><span class="pre">;</span></code>.  For example, the decimal
equivalent for <code class="docutils literal notranslate"><span class="pre">&amp;gt;</span></code> is <code class="docutils literal notranslate"><span class="pre">&amp;#62;</span></code>, whereas the hexadecimal is <code class="docutils literal notranslate"><span class="pre">&amp;#x3E;</span></code>;
in this case the method will receive <code class="docutils literal notranslate"><span class="pre">'62'</span></code> or <code class="docutils literal notranslate"><span class="pre">'x3E'</span></code>.  This method
is never called if <em>convert_charrefs</em> is <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_comment">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_comment</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_comment" title="Link to this definition">¶</a></dt>
<dd><p>This method is called when a comment is encountered (e.g. <code class="docutils literal notranslate"><span class="pre">&lt;!--comment--&gt;</span></code>).</p>
<p>For example, the comment <code class="docutils literal notranslate"><span class="pre">&lt;!--</span> <span class="pre">comment</span> <span class="pre">--&gt;</span></code> will cause this method to be
called with the argument <code class="docutils literal notranslate"><span class="pre">'</span> <span class="pre">comment</span> <span class="pre">'</span></code>.</p>
<p>The content of Internet Explorer conditional comments (condcoms) will also be
sent to this method, so, for <code class="docutils literal notranslate"><span class="pre">&lt;!--[if</span> <span class="pre">IE</span> <span class="pre">9]&gt;IE9-specific</span> <span class="pre">content&lt;![endif]--&gt;</span></code>,
this method will receive <code class="docutils literal notranslate"><span class="pre">'[if</span> <span class="pre">IE</span> <span class="pre">9]&gt;IE9-specific</span> <span class="pre">content&lt;![endif]'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_decl">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_decl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">decl</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_decl" title="Link to this definition">¶</a></dt>
<dd><p>This method is called to handle an HTML doctype declaration (e.g.
<code class="docutils literal notranslate"><span class="pre">&lt;!DOCTYPE</span> <span class="pre">html&gt;</span></code>).</p>
<p>The <em>decl</em> parameter will be the entire contents of the declaration inside
the <code class="docutils literal notranslate"><span class="pre">&lt;!...&gt;</span></code> markup (e.g. <code class="docutils literal notranslate"><span class="pre">'DOCTYPE</span> <span class="pre">html'</span></code>).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.handle_pi">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">handle_pi</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.handle_pi" title="Link to this definition">¶</a></dt>
<dd><p>Method called when a processing instruction is encountered.  The <em>data</em>
parameter will contain the entire processing instruction. For example, for the
processing instruction <code class="docutils literal notranslate"><span class="pre">&lt;?proc</span> <span class="pre">color='red'&gt;</span></code>, this method would be called as
<code class="docutils literal notranslate"><span class="pre">handle_pi(&quot;proc</span> <span class="pre">color='red'&quot;)</span></code>.  It is intended to be overridden by a derived
class; the base class implementation does nothing.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#html.parser.HTMLParser" title="html.parser.HTMLParser"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code></a> class uses the SGML syntactic rules for processing
instructions.  An XHTML processing instruction using the trailing <code class="docutils literal notranslate"><span class="pre">'?'</span></code> will
cause the <code class="docutils literal notranslate"><span class="pre">'?'</span></code> to be included in <em>data</em>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="html.parser.HTMLParser.unknown_decl">
<span class="sig-prename descclassname"><span class="pre">HTMLParser.</span></span><span class="sig-name descname"><span class="pre">unknown_decl</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#html.parser.HTMLParser.unknown_decl" title="Link to this definition">¶</a></dt>
<dd><p>This method is called when an unrecognized declaration is read by the parser.</p>
<p>The <em>data</em> parameter will be the entire contents of the declaration inside
the <code class="docutils literal notranslate"><span class="pre">&lt;![...]&gt;</span></code> markup.  It is sometimes useful to be overridden by a
derived class.  The base class implementation does nothing.</p>
</dd></dl>

</section>
<section id="examples">
<span id="htmlparser-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>The following class implements a parser that will be used to illustrate more
examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">html.parser</span> <span class="kn">import</span> <span class="n">HTMLParser</span>
<span class="kn">from</span> <span class="nn">html.entities</span> <span class="kn">import</span> <span class="n">name2codepoint</span>

<span class="k">class</span> <span class="nc">MyHTMLParser</span><span class="p">(</span><span class="n">HTMLParser</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">handle_starttag</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">,</span> <span class="n">attrs</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Start tag:&quot;</span><span class="p">,</span> <span class="n">tag</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">attr</span> <span class="ow">in</span> <span class="n">attrs</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;     attr:&quot;</span><span class="p">,</span> <span class="n">attr</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_endtag</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">tag</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;End tag  :&quot;</span><span class="p">,</span> <span class="n">tag</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_data</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Data     :&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_comment</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Comment  :&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_entityref</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">):</span>
        <span class="n">c</span> <span class="o">=</span> <span class="nb">chr</span><span class="p">(</span><span class="n">name2codepoint</span><span class="p">[</span><span class="n">name</span><span class="p">])</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Named ent:&quot;</span><span class="p">,</span> <span class="n">c</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_charref</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">name</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">name</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s1">&#39;x&#39;</span><span class="p">):</span>
            <span class="n">c</span> <span class="o">=</span> <span class="nb">chr</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">name</span><span class="p">[</span><span class="mi">1</span><span class="p">:],</span> <span class="mi">16</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">c</span> <span class="o">=</span> <span class="nb">chr</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">name</span><span class="p">))</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Num ent  :&quot;</span><span class="p">,</span> <span class="n">c</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">handle_decl</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Decl     :&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>

<span class="n">parser</span> <span class="o">=</span> <span class="n">MyHTMLParser</span><span class="p">()</span>
</pre></div>
</div>
<p>Parsing a doctype:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.01//EN&quot; &#39;</span>
<span class="gp">... </span>            <span class="s1">&#39;&quot;http://www.w3.org/TR/html4/strict.dtd&quot;&gt;&#39;</span><span class="p">)</span>
<span class="go">Decl     : DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.01//EN&quot; &quot;http://www.w3.org/TR/html4/strict.dtd&quot;</span>
</pre></div>
</div>
<p>Parsing an element with a few attributes and a title:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;img src=&quot;python-logo.png&quot; alt=&quot;The Python logo&quot;&gt;&#39;</span><span class="p">)</span>
<span class="go">Start tag: img</span>
<span class="go">     attr: (&#39;src&#39;, &#39;python-logo.png&#39;)</span>
<span class="go">     attr: (&#39;alt&#39;, &#39;The Python logo&#39;)</span>
<span class="gp">&gt;&gt;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;h1&gt;Python&lt;/h1&gt;&#39;</span><span class="p">)</span>
<span class="go">Start tag: h1</span>
<span class="go">Data     : Python</span>
<span class="go">End tag  : h1</span>
</pre></div>
</div>
<p>The content of <code class="docutils literal notranslate"><span class="pre">script</span></code> and <code class="docutils literal notranslate"><span class="pre">style</span></code> elements is returned as is, without
further parsing:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;style type=&quot;text/css&quot;&gt;#python { color: green }&lt;/style&gt;&#39;</span><span class="p">)</span>
<span class="go">Start tag: style</span>
<span class="go">     attr: (&#39;type&#39;, &#39;text/css&#39;)</span>
<span class="go">Data     : #python { color: green }</span>
<span class="go">End tag  : style</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;script type=&quot;text/javascript&quot;&gt;&#39;</span>
<span class="gp">... </span>            <span class="s1">&#39;alert(&quot;&lt;strong&gt;hello!&lt;/strong&gt;&quot;);&lt;/script&gt;&#39;</span><span class="p">)</span>
<span class="go">Start tag: script</span>
<span class="go">     attr: (&#39;type&#39;, &#39;text/javascript&#39;)</span>
<span class="go">Data     : alert(&quot;&lt;strong&gt;hello!&lt;/strong&gt;&quot;);</span>
<span class="go">End tag  : script</span>
</pre></div>
</div>
<p>Parsing comments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;!-- a comment --&gt;&#39;</span>
<span class="gp">... </span>            <span class="s1">&#39;&lt;!--[if IE 9]&gt;IE-specific content&lt;![endif]--&gt;&#39;</span><span class="p">)</span>
<span class="go">Comment  :  a comment</span>
<span class="go">Comment  : [if IE 9]&gt;IE-specific content&lt;![endif]</span>
</pre></div>
</div>
<p>Parsing named and numeric character references and converting them to the
correct char (note: these 3 references are all equivalent to <code class="docutils literal notranslate"><span class="pre">'&gt;'</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&amp;gt;&amp;#62;&amp;#x3E;&#39;</span><span class="p">)</span>
<span class="go">Named ent: &gt;</span>
<span class="go">Num ent  : &gt;</span>
<span class="go">Num ent  : &gt;</span>
</pre></div>
</div>
<p>Feeding incomplete chunks to <a class="reference internal" href="#html.parser.HTMLParser.feed" title="html.parser.HTMLParser.feed"><code class="xref py py-meth docutils literal notranslate"><span class="pre">feed()</span></code></a> works, but
<a class="reference internal" href="#html.parser.HTMLParser.handle_data" title="html.parser.HTMLParser.handle_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">handle_data()</span></code></a> might be called more than once
(unless <em>convert_charrefs</em> is set to <code class="docutils literal notranslate"><span class="pre">True</span></code>):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">chunk</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;&lt;sp&#39;</span><span class="p">,</span> <span class="s1">&#39;an&gt;buff&#39;</span><span class="p">,</span> <span class="s1">&#39;ered &#39;</span><span class="p">,</span> <span class="s1">&#39;text&lt;/s&#39;</span><span class="p">,</span> <span class="s1">&#39;pan&gt;&#39;</span><span class="p">]:</span>
<span class="gp">... </span>    <span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="n">chunk</span><span class="p">)</span>
<span class="gp">...</span>
<span class="go">Start tag: span</span>
<span class="go">Data     : buff</span>
<span class="go">Data     : ered</span>
<span class="go">Data     : text</span>
<span class="go">End tag  : span</span>
</pre></div>
</div>
<p>Parsing invalid HTML (e.g. unquoted attributes) also works:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">parser</span><span class="o">.</span><span class="n">feed</span><span class="p">(</span><span class="s1">&#39;&lt;p&gt;&lt;a class=link href=#main&gt;tag soup&lt;/p &gt;&lt;/a&gt;&#39;</span><span class="p">)</span>
<span class="go">Start tag: p</span>
<span class="go">Start tag: a</span>
<span class="go">     attr: (&#39;class&#39;, &#39;link&#39;)</span>
<span class="go">     attr: (&#39;href&#39;, &#39;#main&#39;)</span>
<span class="go">Data     : tag soup</span>
<span class="go">End tag  : p</span>
<span class="go">End tag  : a</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code> — Simple HTML and XHTML parser</a><ul>
<li><a class="reference internal" href="#example-html-parser-application">Example HTML Parser Application</a></li>
<li><a class="reference internal" href="#htmlparser-methods"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTMLParser</span></code> Methods</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="html.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html</span></code> — HyperText Markup Language support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="html.entities.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.entities</span></code> — Definitions of HTML general entities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/html.parser.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="html.entities.html" title="html.entities — Definitions of HTML general entities"
             >next</a> |</li>
        <li class="right" >
          <a href="html.html" title="html — HyperText Markup Language support"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code> — Simple HTML and XHTML parser</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>