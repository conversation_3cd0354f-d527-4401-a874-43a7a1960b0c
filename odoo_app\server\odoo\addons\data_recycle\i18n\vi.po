# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_recycle
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid ""
"' recycling rule.<br/>\n"
"You can validate those changes"
msgstr ""
"' quy tắc tái chế.<br/>\n"
"Bạn có thể xác thực những thay đổi đó"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "**Record Deleted**"
msgstr "**Bản ghi đã bị xoá**"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "<span class=\"me-1\">Every</span>"
msgstr "<span class=\"me-1\">Mỗi</span>"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__active
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__archive
msgid "Archive"
msgstr "Lưu trữ"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__automatic
msgid "Automatic"
msgstr "Tự động"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__company_id
msgid "Company"
msgstr "Công ty"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config
msgid "Configuration"
msgstr "Cấu hình"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "Configure rules to identify records to clean"
msgstr "Configure rules to identify records to clean"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__create_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "Làm sạch Dữ liệu"

#. module: data_recycle
#: model:ir.actions.server,name:data_recycle.ir_cron_clean_records_ir_actions_server
msgid "Data Recycle: Clean Records"
msgstr "Tái chế dữ liệu: Dọn dẹp bản ghi"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
#, python-format
msgid "Data to Recycle"
msgstr "Dữ liệu cần tái chế"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__days
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__days
msgid "Days"
msgstr "Ngày"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_action__unlink
msgid "Delete"
msgstr "Xoá"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta
msgid "Delta"
msgstr "Delta"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_delta_unit
msgid "Delta Unit"
msgstr "Đơn vị delta"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Discarded"
msgstr "Discarded"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__display_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_record_notification
msgid "Field Recycle Records"
msgstr "Tái chế trường"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__domain
msgid "Filter"
msgstr "Bộ lọc"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__id
msgid "ID"
msgstr "ID"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__include_archived
msgid "Include Archived"
msgstr "Bao gồm dữ liệu đã lưu trữ"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__last_notification
msgid "Last Notification"
msgstr "Last Notification"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_uid
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__write_date
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: data_recycle
#: model:ir.model.fields,help:data_recycle.field_data_recycle_model__notify_user_ids
msgid "List of users to notify when there are new records to recycle"
msgstr "Danh sách người dùng cần thông báo khi có bản ghi mới cần tái chế"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__recycle_mode__manual
msgid "Manual"
msgstr "Thủ công"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_id
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_id
msgid "Model"
msgstr "Mô hình"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__res_model_name
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_model_name
msgid "Model Name"
msgstr "Tên mô hình"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__months
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__months
msgid "Months"
msgstr "Tháng"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__name
msgid "Name"
msgstr "Tên"

#. module: data_recycle
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record
#: model_terms:ir.actions.act_window,help:data_recycle.action_data_recycle_record_notification
msgid "No cleaning suggestions"
msgstr "No cleaning suggestions"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency
msgid "Notify"
msgstr "Notify"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Notify Frequency Period"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__notify_user_ids
msgid "Notify Users"
msgstr "Notify Users"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__res_id
msgid "Record ID"
msgstr "ID bản ghi"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__name
msgid "Record Name"
msgstr "Record Name"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Records"
msgstr "Bản ghi"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__records_to_recycle_count
msgid "Records To Recycle"
msgstr "Bản ghi cần tái chế"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_action
msgid "Recycle Action"
msgstr "Tác vụ tái chế"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_mode
msgid "Recycle Mode"
msgstr "Chế độ tái chế"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_record__recycle_model_id
msgid "Recycle Model"
msgstr "Mô hình tái chế"

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__recycle_record_ids
msgid "Recycle Record"
msgstr "Tái chế bản ghi"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_recycle_record
msgid "Recycle Records"
msgstr "Tái chế bản ghi"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
msgid "Recycle Rule"
msgstr "Quy tắc tái chế"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_search
msgid "Recycle Rules"
msgstr "Quy tắc tái chế"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_model
msgid "Recycling Model"
msgstr "Mô hình tái chế"

#. module: data_recycle
#: model:ir.model,name:data_recycle.model_data_recycle_record
msgid "Recycling Record"
msgstr "Tái chế bản ghi"

#. module: data_recycle
#: model:ir.actions.act_window,name:data_recycle.action_data_recycle_config
msgid "Recyle Records Rules"
msgstr "Quy tắc tái chế dữ liệu"

#. module: data_recycle
#: model:ir.ui.menu,name:data_recycle.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Quy tắc"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Run Now"
msgstr "Chạy ngay"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_merge_model_form
msgid "Select a model to configure recycling actions"
msgstr "Chọn một mô hình để cấu hình tác vụ tái chế"

#. module: data_recycle
#: model:ir.model.constraint,message:data_recycle.constraint_data_recycle_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "The notification frequency should be greater than 0"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_model.py:0
#, python-format
msgid "This model doesn't manage archived records. Only deletion is possible."
msgstr "Mô hình này không quản lý hồ sơ lưu trữ. Chỉ có thể xóa."

#. module: data_recycle
#: model:ir.model.fields,field_description:data_recycle.field_data_recycle_model__time_field_id
msgid "Time Field"
msgstr "Trường thời gian"

#. module: data_recycle
#. odoo-python
#: code:addons/data_recycle/models/data_recycle_record.py:0
#, python-format
msgid "Undefined Name"
msgstr "Tên không xác định"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#, python-format
msgid "Unselect"
msgstr "Unselect"

#. module: data_recycle
#. odoo-javascript
#: code:addons/data_recycle/static/src/views/data_recycle_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:data_recycle.view_data_recycle_record_list
#, python-format
msgid "Validate"
msgstr "Xác nhận"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "We've identified"
msgstr "Chúng tôi đã phát hiện"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__notify_frequency_period__weeks
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__weeks
msgid "Weeks"
msgstr "Tuần"

#. module: data_recycle
#: model:ir.model.fields.selection,name:data_recycle.selection__data_recycle_model__time_field_delta_unit__years
msgid "Years"
msgstr "Năm"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "here"
msgstr "ở đây"

#. module: data_recycle
#: model_terms:ir.ui.view,arch_db:data_recycle.notification
msgid "records to clean with the '"
msgstr "records to clean with the '"
