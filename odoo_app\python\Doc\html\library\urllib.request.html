<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="urllib.request — Extensible library for opening URLs" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/urllib.request.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/urllib/request.py The urllib.request module defines functions and classes which help in opening URLs (mostly HTTP) in a complex world — basic and digest authentication, redirection..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/urllib/request.py The urllib.request module defines functions and classes which help in opening URLs (mostly HTTP) in a complex world — basic and digest authentication, redirection..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>urllib.request — Extensible library for opening URLs &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="urllib.parse — Parse URLs into components" href="urllib.parse.html" />
    <link rel="prev" title="urllib — URL handling modules" href="urllib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/urllib.request.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> — Extensible library for opening URLs</a><ul>
<li><a class="reference internal" href="#request-objects">Request Objects</a></li>
<li><a class="reference internal" href="#openerdirector-objects">OpenerDirector Objects</a></li>
<li><a class="reference internal" href="#basehandler-objects">BaseHandler Objects</a></li>
<li><a class="reference internal" href="#httpredirecthandler-objects">HTTPRedirectHandler Objects</a></li>
<li><a class="reference internal" href="#httpcookieprocessor-objects">HTTPCookieProcessor Objects</a></li>
<li><a class="reference internal" href="#proxyhandler-objects">ProxyHandler Objects</a></li>
<li><a class="reference internal" href="#httppasswordmgr-objects">HTTPPasswordMgr Objects</a></li>
<li><a class="reference internal" href="#httppasswordmgrwithpriorauth-objects">HTTPPasswordMgrWithPriorAuth Objects</a></li>
<li><a class="reference internal" href="#abstractbasicauthhandler-objects">AbstractBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httpbasicauthhandler-objects">HTTPBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#proxybasicauthhandler-objects">ProxyBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#abstractdigestauthhandler-objects">AbstractDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httpdigestauthhandler-objects">HTTPDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#proxydigestauthhandler-objects">ProxyDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httphandler-objects">HTTPHandler Objects</a></li>
<li><a class="reference internal" href="#httpshandler-objects">HTTPSHandler Objects</a></li>
<li><a class="reference internal" href="#filehandler-objects">FileHandler Objects</a></li>
<li><a class="reference internal" href="#datahandler-objects">DataHandler Objects</a></li>
<li><a class="reference internal" href="#ftphandler-objects">FTPHandler Objects</a></li>
<li><a class="reference internal" href="#cacheftphandler-objects">CacheFTPHandler Objects</a></li>
<li><a class="reference internal" href="#unknownhandler-objects">UnknownHandler Objects</a></li>
<li><a class="reference internal" href="#httperrorprocessor-objects">HTTPErrorProcessor Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#legacy-interface">Legacy interface</a></li>
<li><a class="reference internal" href="#urllib-request-restrictions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> Restrictions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-urllib.response"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.response</span></code> — Response classes used by urllib</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="urllib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code> — URL handling modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="urllib.parse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.parse</span></code> — Parse URLs into components</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/urllib.request.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="urllib.parse.html" title="urllib.parse — Parse URLs into components"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="urllib.html" title="urllib — URL handling modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> — Extensible library for opening URLs</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-urllib.request">
<span id="urllib-request-extensible-library-for-opening-urls"></span><h1><a class="reference internal" href="#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> — Extensible library for opening URLs<a class="headerlink" href="#module-urllib.request" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/urllib/request.py">Lib/urllib/request.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> module defines functions and classes which help in
opening URLs (mostly HTTP) in a complex world — basic and digest
authentication, redirections, cookies and more.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>The <a class="reference external" href="https://requests.readthedocs.io/en/master/">Requests package</a>
is recommended for a higher-level HTTP client interface.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>On macOS it is unsafe to use this module in programs using
<a class="reference internal" href="os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.fork()</span></code></a> because the <a class="reference internal" href="#urllib.request.getproxies" title="urllib.request.getproxies"><code class="xref py py-func docutils literal notranslate"><span class="pre">getproxies()</span></code></a> implementation for
macOS uses a higher-level system API. Set the environment variable
<code class="docutils literal notranslate"><span class="pre">no_proxy</span></code> to <code class="docutils literal notranslate"><span class="pre">*</span></code> to avoid this problem
(e.g. <code class="docutils literal notranslate"><span class="pre">os.environ[&quot;no_proxy&quot;]</span> <span class="pre">=</span> <span class="pre">&quot;*&quot;</span></code>).</p>
</div>
<div class="availability docutils container">
<p><a class="reference internal" href="intro.html#availability"><span class="std std-ref">Availability</span></a>: not Emscripten, not WASI.</p>
<p>This module does not work or is not available on WebAssembly platforms
<code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> and <code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code>. See
<a class="reference internal" href="intro.html#wasm-availability"><span class="std std-ref">WebAssembly platforms</span></a> for more information.</p>
</div>
<p>The <a class="reference internal" href="#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.urlopen">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">urlopen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data=None</span></span></em>, <span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em>, <span class="optional">]</span><em class="sig-param"><span class="n"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cafile=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">capath=None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cadefault=False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context=None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.urlopen" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>url</em>, which can be either a string containing a valid, properly
encoded URL, or a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object.</p>
<p><em>data</em> must be an object specifying additional data to be sent to the
server, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no such data is needed.  See <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a>
for details.</p>
<p>urllib.request module uses HTTP/1.1 and includes <code class="docutils literal notranslate"><span class="pre">Connection:close</span></code> header
in its HTTP requests.</p>
<p>The optional <em>timeout</em> parameter specifies a timeout in seconds for
blocking operations like the connection attempt (if not specified,
the global default timeout setting will be used).  This actually
only works for HTTP, HTTPS and FTP connections.</p>
<p>If <em>context</em> is specified, it must be a <a class="reference internal" href="ssl.html#ssl.SSLContext" title="ssl.SSLContext"><code class="xref py py-class docutils literal notranslate"><span class="pre">ssl.SSLContext</span></code></a> instance
describing the various SSL options. See <a class="reference internal" href="http.client.html#http.client.HTTPSConnection" title="http.client.HTTPSConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPSConnection</span></code></a>
for more details.</p>
<p>The optional <em>cafile</em> and <em>capath</em> parameters specify a set of trusted
CA certificates for HTTPS requests.  <em>cafile</em> should point to a single
file containing a bundle of CA certificates, whereas <em>capath</em> should
point to a directory of hashed certificate files.  More information can
be found in <a class="reference internal" href="ssl.html#ssl.SSLContext.load_verify_locations" title="ssl.SSLContext.load_verify_locations"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ssl.SSLContext.load_verify_locations()</span></code></a>.</p>
<p>The <em>cadefault</em> parameter is ignored.</p>
<p>This function always returns an object which can work as a
<a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> and has the properties <em>url</em>, <em>headers</em>, and <em>status</em>.
See <a class="reference internal" href="#urllib.response.addinfourl" title="urllib.response.addinfourl"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.response.addinfourl</span></code></a> for more detail on these properties.</p>
<p>For HTTP and HTTPS URLs, this function returns a
<a class="reference internal" href="http.client.html#http.client.HTTPResponse" title="http.client.HTTPResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPResponse</span></code></a> object slightly modified. In addition
to the three new methods above, the msg attribute contains the
same information as the <a class="reference internal" href="http.client.html#http.client.HTTPResponse.reason" title="http.client.HTTPResponse.reason"><code class="xref py py-attr docutils literal notranslate"><span class="pre">reason</span></code></a>
attribute — the reason phrase returned by server — instead of
the response headers as it is specified in the documentation for
<a class="reference internal" href="http.client.html#http.client.HTTPResponse" title="http.client.HTTPResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPResponse</span></code></a>.</p>
<p>For FTP, file, and data URLs and requests explicitly handled by legacy
<a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a> and <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a> classes, this function
returns a <a class="reference internal" href="#urllib.response.addinfourl" title="urllib.response.addinfourl"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.response.addinfourl</span></code></a> object.</p>
<p>Raises <a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a> on protocol errors.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">None</span></code> may be returned if no handler handles the request (though
the default installed global <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> uses
<a class="reference internal" href="#urllib.request.UnknownHandler" title="urllib.request.UnknownHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnknownHandler</span></code></a> to ensure this never happens).</p>
<p>In addition, if proxy settings are detected (for example, when a <code class="docutils literal notranslate"><span class="pre">*_proxy</span></code>
environment variable like <code class="xref std std-envvar docutils literal notranslate"><span class="pre">http_proxy</span></code> is set),
<a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> is default installed and makes sure the requests are
handled through the proxy.</p>
<p>The legacy <code class="docutils literal notranslate"><span class="pre">urllib.urlopen</span></code> function from Python 2.6 and earlier has been
discontinued; <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.request.urlopen()</span></code></a> corresponds to the old
<code class="docutils literal notranslate"><span class="pre">urllib2.urlopen</span></code>.  Proxy handling, which was done by passing a dictionary
parameter to <code class="docutils literal notranslate"><span class="pre">urllib.urlopen</span></code>, can be obtained by using
<a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> objects.</p>
<p class="audit-hook"><p>The default opener raises an <a class="reference internal" href="sys.html#auditing"><span class="std std-ref">auditing event</span></a>
<code class="docutils literal notranslate"><span class="pre">urllib.Request</span></code> with arguments <code class="docutils literal notranslate"><span class="pre">fullurl</span></code>, <code class="docutils literal notranslate"><span class="pre">data</span></code>, <code class="docutils literal notranslate"><span class="pre">headers</span></code>,
<code class="docutils literal notranslate"><span class="pre">method</span></code> taken from the request object.</p>
</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>cafile</em> and <em>capath</em> were added.</p>
<p>HTTPS virtual hosts are now supported if possible (that is, if
<a class="reference internal" href="ssl.html#ssl.HAS_SNI" title="ssl.HAS_SNI"><code class="xref py py-const docutils literal notranslate"><span class="pre">ssl.HAS_SNI</span></code></a> is true).</p>
<p><em>data</em> can be an iterable object.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><em>cadefault</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.3: </span><em>context</em> was added.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>HTTPS connection now send an ALPN extension with protocol indicator
<code class="docutils literal notranslate"><span class="pre">http/1.1</span></code> when no <em>context</em> is given. Custom <em>context</em> should set
ALPN protocols with <a class="reference internal" href="ssl.html#ssl.SSLContext.set_alpn_protocols" title="ssl.SSLContext.set_alpn_protocols"><code class="xref py py-meth docutils literal notranslate"><span class="pre">set_alpn_protocols()</span></code></a>.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.6: </span><em>cafile</em>, <em>capath</em> and <em>cadefault</em> are deprecated in favor of <em>context</em>.
Please use <a class="reference internal" href="ssl.html#ssl.SSLContext.load_cert_chain" title="ssl.SSLContext.load_cert_chain"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ssl.SSLContext.load_cert_chain()</span></code></a> instead, or let
<a class="reference internal" href="ssl.html#ssl.create_default_context" title="ssl.create_default_context"><code class="xref py py-func docutils literal notranslate"><span class="pre">ssl.create_default_context()</span></code></a> select the system’s trusted CA
certificates for you.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.install_opener">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">install_opener</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">opener</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.install_opener" title="Link to this definition">¶</a></dt>
<dd><p>Install an <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> instance as the default global opener.
Installing an opener is only necessary if you want urlopen to use that
opener; otherwise, simply call <a class="reference internal" href="#urllib.request.OpenerDirector.open" title="urllib.request.OpenerDirector.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OpenerDirector.open()</span></code></a> instead of
<a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.  The code does not check for a real
<a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>, and any class with the appropriate interface will
work.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.build_opener">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">build_opener</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.build_opener" title="Link to this definition">¶</a></dt>
<dd><p>Return an <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> instance, which chains the handlers in the
order given. <em>handler</em>s can be either instances of <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, or
subclasses of <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> (in which case it must be possible to call
the constructor without any parameters).  Instances of the following classes
will be in front of the <em>handler</em>s, unless the <em>handler</em>s contain them,
instances of them or subclasses of them: <a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> (if proxy
settings are detected), <a class="reference internal" href="#urllib.request.UnknownHandler" title="urllib.request.UnknownHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">UnknownHandler</span></code></a>, <a class="reference internal" href="#urllib.request.HTTPHandler" title="urllib.request.HTTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPHandler</span></code></a>,
<a class="reference internal" href="#urllib.request.HTTPDefaultErrorHandler" title="urllib.request.HTTPDefaultErrorHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPDefaultErrorHandler</span></code></a>, <a class="reference internal" href="#urllib.request.HTTPRedirectHandler" title="urllib.request.HTTPRedirectHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPRedirectHandler</span></code></a>,
<a class="reference internal" href="#urllib.request.FTPHandler" title="urllib.request.FTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTPHandler</span></code></a>, <a class="reference internal" href="#urllib.request.FileHandler" title="urllib.request.FileHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FileHandler</span></code></a>, <a class="reference internal" href="#urllib.request.HTTPErrorProcessor" title="urllib.request.HTTPErrorProcessor"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPErrorProcessor</span></code></a>.</p>
<p>If the Python installation has SSL support (i.e., if the <a class="reference internal" href="ssl.html#module-ssl" title="ssl: TLS/SSL wrapper for socket objects"><code class="xref py py-mod docutils literal notranslate"><span class="pre">ssl</span></code></a> module
can be imported), <a class="reference internal" href="#urllib.request.HTTPSHandler" title="urllib.request.HTTPSHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPSHandler</span></code></a> will also be added.</p>
<p>A <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> subclass may also change its <code class="xref py py-attr docutils literal notranslate"><span class="pre">handler_order</span></code>
attribute to modify its position in the handlers list.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.pathname2url">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">pathname2url</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.pathname2url" title="Link to this definition">¶</a></dt>
<dd><p>Convert the pathname <em>path</em> from the local syntax for a path to the form used in
the path component of a URL.  This does not produce a complete URL.  The return
value will already be quoted using the <a class="reference internal" href="urllib.parse.html#urllib.parse.quote" title="urllib.parse.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">quote()</span></code></a> function.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.url2pathname">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">url2pathname</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.url2pathname" title="Link to this definition">¶</a></dt>
<dd><p>Convert the path component <em>path</em> from a percent-encoded URL to the local syntax for a
path.  This does not accept a complete URL.  This function uses
<a class="reference internal" href="urllib.parse.html#urllib.parse.unquote" title="urllib.parse.unquote"><code class="xref py py-func docutils literal notranslate"><span class="pre">unquote()</span></code></a> to decode <em>path</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.getproxies">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">getproxies</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.getproxies" title="Link to this definition">¶</a></dt>
<dd><p>This helper function returns a dictionary of scheme to proxy server URL
mappings. It scans the environment for variables named <code class="docutils literal notranslate"><span class="pre">&lt;scheme&gt;_proxy</span></code>,
in a case insensitive approach, for all operating systems first, and when it
cannot find it, looks for proxy information from System
Configuration for macOS and Windows Systems Registry for Windows.
If both lowercase and uppercase environment variables exist (and disagree),
lowercase is preferred.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If the environment variable <code class="docutils literal notranslate"><span class="pre">REQUEST_METHOD</span></code> is set, which usually
indicates your script is running in a CGI environment, the environment
variable <code class="docutils literal notranslate"><span class="pre">HTTP_PROXY</span></code> (uppercase <code class="docutils literal notranslate"><span class="pre">_PROXY</span></code>) will be ignored. This is
because that variable can be injected by a client using the “Proxy:” HTTP
header. If you need to use an HTTP proxy in a CGI environment, either use
<code class="docutils literal notranslate"><span class="pre">ProxyHandler</span></code> explicitly, or make sure the variable name is in
lowercase (or at least the <code class="docutils literal notranslate"><span class="pre">_proxy</span></code> suffix).</p>
</div>
</dd></dl>

<p>The following classes are provided:</p>
<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.Request">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">Request</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">{}</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">origin_req_host</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">unverifiable</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">method</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request" title="Link to this definition">¶</a></dt>
<dd><p>This class is an abstraction of a URL request.</p>
<p><em>url</em> should be a string containing a valid, properly encoded URL.</p>
<p><em>data</em> must be an object specifying additional data to send to the
server, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if no such data is needed.  Currently HTTP
requests are the only ones that use <em>data</em>.  The supported object
types include bytes, file-like objects, and iterables of bytes-like objects.
If no <code class="docutils literal notranslate"><span class="pre">Content-Length</span></code> nor <code class="docutils literal notranslate"><span class="pre">Transfer-Encoding</span></code> header field
has been provided, <a class="reference internal" href="#urllib.request.HTTPHandler" title="urllib.request.HTTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPHandler</span></code></a> will set these headers according
to the type of <em>data</em>.  <code class="docutils literal notranslate"><span class="pre">Content-Length</span></code> will be used to send
bytes objects, while <code class="docutils literal notranslate"><span class="pre">Transfer-Encoding:</span> <span class="pre">chunked</span></code> as specified in
<span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc7230.html"><strong>RFC 7230</strong></a>, Section 3.3.1 will be used to send files and other iterables.</p>
<p>For an HTTP POST request method, <em>data</em> should be a buffer in the
standard <em class="mimetype">application/x-www-form-urlencoded</em> format.  The
<a class="reference internal" href="urllib.parse.html#urllib.parse.urlencode" title="urllib.parse.urlencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.parse.urlencode()</span></code></a> function takes a mapping or sequence
of 2-tuples and returns an ASCII string in this format. It should
be encoded to bytes before being used as the <em>data</em> parameter.</p>
<p><em>headers</em> should be a dictionary, and will be treated as if
<a class="reference internal" href="#urllib.request.Request.add_header" title="urllib.request.Request.add_header"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_header()</span></code></a> was called with each key and value as arguments.
This is often used to “spoof” the <code class="docutils literal notranslate"><span class="pre">User-Agent</span></code> header value, which is
used by a browser to identify itself – some HTTP servers only
allow requests coming from common browsers as opposed to scripts.
For example, Mozilla Firefox may identify itself as <code class="docutils literal notranslate"><span class="pre">&quot;Mozilla/5.0</span>
<span class="pre">(X11;</span> <span class="pre">U;</span> <span class="pre">Linux</span> <span class="pre">i686)</span> <span class="pre">Gecko/20071127</span> <span class="pre">Firefox/********&quot;</span></code>, while
<a class="reference internal" href="urllib.html#module-urllib" title="urllib"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code></a>’s default user agent string is
<code class="docutils literal notranslate"><span class="pre">&quot;Python-urllib/2.6&quot;</span></code> (on Python 2.6).
All header keys are sent in camel case.</p>
<p>An appropriate <code class="docutils literal notranslate"><span class="pre">Content-Type</span></code> header should be included if the <em>data</em>
argument is present.  If this header has not been provided and <em>data</em>
is not None, <code class="docutils literal notranslate"><span class="pre">Content-Type:</span> <span class="pre">application/x-www-form-urlencoded</span></code> will
be added as a default.</p>
<p>The next two arguments are only of interest for correct handling
of third-party HTTP cookies:</p>
<p><em>origin_req_host</em> should be the request-host of the origin
transaction, as defined by <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a>.  It defaults to
<code class="docutils literal notranslate"><span class="pre">http.cookiejar.request_host(self)</span></code>.  This is the host name or IP
address of the original request that was initiated by the user.
For example, if the request is for an image in an HTML document,
this should be the request-host of the request for the page
containing the image.</p>
<p><em>unverifiable</em> should indicate whether the request is unverifiable,
as defined by <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a>.  It defaults to <code class="docutils literal notranslate"><span class="pre">False</span></code>.  An unverifiable
request is one whose URL the user did not have the option to
approve.  For example, if the request is for an image in an HTML
document, and the user had no option to approve the automatic
fetching of the image, this should be true.</p>
<p><em>method</em> should be a string that indicates the HTTP request method that
will be used (e.g. <code class="docutils literal notranslate"><span class="pre">'HEAD'</span></code>).  If provided, its value is stored in the
<a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">method</span></code></a> attribute and is used by <a class="reference internal" href="#urllib.request.Request.get_method" title="urllib.request.Request.get_method"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_method()</span></code></a>.
The default is <code class="docutils literal notranslate"><span class="pre">'GET'</span></code> if <em>data</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code> or <code class="docutils literal notranslate"><span class="pre">'POST'</span></code> otherwise.
Subclasses may indicate a different default method by setting the
<a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">method</span></code></a> attribute in the class itself.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The request will not work as expected if the data object is unable
to deliver its content more than once (e.g. a file or an iterable
that can produce the content only once) and the request is retried
for HTTP redirects or authentication.  The <em>data</em> is sent to the
HTTP server right away after the headers.  There is no support for
a 100-continue expectation in the library.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.method</span></code></a> argument is added to the Request class.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Default <a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.method</span></code></a> may be indicated at the class level.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>Do not raise an error if the <code class="docutils literal notranslate"><span class="pre">Content-Length</span></code> has not been
provided and <em>data</em> is neither <code class="docutils literal notranslate"><span class="pre">None</span></code> nor a bytes object.
Fall back to use chunked transfer encoding instead.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.OpenerDirector">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">OpenerDirector</span></span><a class="headerlink" href="#urllib.request.OpenerDirector" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> class opens URLs via <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>s chained
together. It manages the chaining of handlers, and recovery from errors.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.BaseHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">BaseHandler</span></span><a class="headerlink" href="#urllib.request.BaseHandler" title="Link to this definition">¶</a></dt>
<dd><p>This is the base class for all registered handlers — and handles only the
simple mechanics of registration.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPDefaultErrorHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPDefaultErrorHandler</span></span><a class="headerlink" href="#urllib.request.HTTPDefaultErrorHandler" title="Link to this definition">¶</a></dt>
<dd><p>A class which defines a default handler for HTTP error responses; all responses
are turned into <a class="reference internal" href="urllib.error.html#urllib.error.HTTPError" title="urllib.error.HTTPError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPError</span></code></a> exceptions.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPRedirectHandler</span></span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler" title="Link to this definition">¶</a></dt>
<dd><p>A class to handle redirections.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPCookieProcessor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPCookieProcessor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cookiejar</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPCookieProcessor" title="Link to this definition">¶</a></dt>
<dd><p>A class to handle HTTP Cookies.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.ProxyHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">ProxyHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">proxies</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.ProxyHandler" title="Link to this definition">¶</a></dt>
<dd><p>Cause requests to go through a proxy. If <em>proxies</em> is given, it must be a
dictionary mapping protocol names to URLs of proxies. The default is to read
the list of proxies from the environment variables
<code class="docutils literal notranslate"><span class="pre">&lt;protocol&gt;_proxy</span></code>.  If no proxy environment variables are set, then
in a Windows environment proxy settings are obtained from the registry’s
Internet Settings section, and in a macOS environment proxy information
is retrieved from the System Configuration Framework.</p>
<p>To disable autodetected proxy pass an empty dictionary.</p>
<p>The <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">no_proxy</span></code> environment variable can be used to specify hosts
which shouldn’t be reached via proxy; if set, it should be a comma-separated
list of hostname suffixes, optionally with <code class="docutils literal notranslate"><span class="pre">:port</span></code> appended, for example
<code class="docutils literal notranslate"><span class="pre">cern.ch,ncsa.uiuc.edu,some.host:8080</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">HTTP_PROXY</span></code> will be ignored if a variable <code class="docutils literal notranslate"><span class="pre">REQUEST_METHOD</span></code> is set;
see the documentation on <a class="reference internal" href="#urllib.request.getproxies" title="urllib.request.getproxies"><code class="xref py py-func docutils literal notranslate"><span class="pre">getproxies()</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgr">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPPasswordMgr</span></span><a class="headerlink" href="#urllib.request.HTTPPasswordMgr" title="Link to this definition">¶</a></dt>
<dd><p>Keep a database of  <code class="docutils literal notranslate"><span class="pre">(realm,</span> <span class="pre">uri)</span> <span class="pre">-&gt;</span> <span class="pre">(user,</span> <span class="pre">password)</span></code> mappings.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithDefaultRealm">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="Link to this definition">¶</a></dt>
<dd><p>Keep a database of  <code class="docutils literal notranslate"><span class="pre">(realm,</span> <span class="pre">uri)</span> <span class="pre">-&gt;</span> <span class="pre">(user,</span> <span class="pre">password)</span></code> mappings. A realm of
<code class="docutils literal notranslate"><span class="pre">None</span></code> is considered a catch-all realm, which is searched if no other realm
fits.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithPriorAuth">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPPasswordMgrWithPriorAuth</span></span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithPriorAuth" title="Link to this definition">¶</a></dt>
<dd><p>A variant of <a class="reference internal" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="urllib.request.HTTPPasswordMgrWithDefaultRealm"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></code></a> that also has a
database of <code class="docutils literal notranslate"><span class="pre">uri</span> <span class="pre">-&gt;</span> <span class="pre">is_authenticated</span></code> mappings.  Can be used by a
BasicAuth handler to determine when to send authentication credentials
immediately instead of waiting for a <code class="docutils literal notranslate"><span class="pre">401</span></code> response first.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.AbstractBasicAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">AbstractBasicAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.AbstractBasicAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>This is a mixin class that helps with HTTP authentication, both to the remote
host and to a proxy. <em>password_mgr</em>, if given, should be something that is
compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to section
<a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must be
supported.  If <em>passwd_mgr</em> also provides <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> and
<code class="docutils literal notranslate"><span class="pre">update_authenticated</span></code> methods (see
<a class="reference internal" href="#http-password-mgr-with-prior-auth"><span class="std std-ref">HTTPPasswordMgrWithPriorAuth Objects</span></a>), then the handler will use the
<code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> result for a given URI to determine whether or not to
send authentication credentials with the request.  If <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code>
returns <code class="docutils literal notranslate"><span class="pre">True</span></code> for the URI, credentials are sent.  If <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code>
is <code class="docutils literal notranslate"><span class="pre">False</span></code>, credentials are not sent, and then if a <code class="docutils literal notranslate"><span class="pre">401</span></code> response is
received the request is re-sent with the authentication credentials.  If
authentication succeeds, <code class="docutils literal notranslate"><span class="pre">update_authenticated</span></code> is called to set
<code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> <code class="docutils literal notranslate"><span class="pre">True</span></code> for the URI, so that subsequent requests to
the URI or any of its super-URIs will automatically include the
authentication credentials.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>Added <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> support.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPBasicAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPBasicAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPBasicAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>Handle authentication with the remote host. <em>password_mgr</em>, if given, should
be something that is compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to
section <a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must
be supported. HTTPBasicAuthHandler will raise a <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> when
presented with a wrong Authentication scheme.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.ProxyBasicAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">ProxyBasicAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.ProxyBasicAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>Handle authentication with the proxy. <em>password_mgr</em>, if given, should be
something that is compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to section
<a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must be
supported.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.AbstractDigestAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">AbstractDigestAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.AbstractDigestAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>This is a mixin class that helps with HTTP authentication, both to the remote
host and to a proxy. <em>password_mgr</em>, if given, should be something that is
compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to section
<a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must be
supported.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPDigestAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPDigestAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPDigestAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>Handle authentication with the remote host. <em>password_mgr</em>, if given, should
be something that is compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to
section <a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must
be supported. When both Digest Authentication Handler and Basic
Authentication Handler are both added, Digest Authentication is always tried
first. If the Digest Authentication returns a 40x response again, it is sent
to Basic Authentication handler to Handle.  This Handler method will raise a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> when presented with an authentication scheme other than
Digest or Basic.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Raise <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> on unsupported Authentication Scheme.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.ProxyDigestAuthHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">ProxyDigestAuthHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">password_mgr</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.ProxyDigestAuthHandler" title="Link to this definition">¶</a></dt>
<dd><p>Handle authentication with the proxy. <em>password_mgr</em>, if given, should be
something that is compatible with <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a>; refer to section
<a class="reference internal" href="#http-password-mgr"><span class="std std-ref">HTTPPasswordMgr Objects</span></a> for information on the interface that must be
supported.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPHandler</span></span><a class="headerlink" href="#urllib.request.HTTPHandler" title="Link to this definition">¶</a></dt>
<dd><p>A class to handle opening of HTTP URLs.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPSHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPSHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">debuglevel</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">check_hostname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPSHandler" title="Link to this definition">¶</a></dt>
<dd><p>A class to handle opening of HTTPS URLs.  <em>context</em> and <em>check_hostname</em>
have the same meaning as in <a class="reference internal" href="http.client.html#http.client.HTTPSConnection" title="http.client.HTTPSConnection"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.client.HTTPSConnection</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span><em>context</em> and <em>check_hostname</em> were added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.FileHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">FileHandler</span></span><a class="headerlink" href="#urllib.request.FileHandler" title="Link to this definition">¶</a></dt>
<dd><p>Open local files.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.DataHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">DataHandler</span></span><a class="headerlink" href="#urllib.request.DataHandler" title="Link to this definition">¶</a></dt>
<dd><p>Open data URLs.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.FTPHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">FTPHandler</span></span><a class="headerlink" href="#urllib.request.FTPHandler" title="Link to this definition">¶</a></dt>
<dd><p>Open FTP URLs.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.CacheFTPHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">CacheFTPHandler</span></span><a class="headerlink" href="#urllib.request.CacheFTPHandler" title="Link to this definition">¶</a></dt>
<dd><p>Open FTP URLs, keeping a cache of open FTP connections to minimize delays.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.UnknownHandler">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">UnknownHandler</span></span><a class="headerlink" href="#urllib.request.UnknownHandler" title="Link to this definition">¶</a></dt>
<dd><p>A catch-all class to handle unknown URLs.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.HTTPErrorProcessor">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">HTTPErrorProcessor</span></span><a class="headerlink" href="#urllib.request.HTTPErrorProcessor" title="Link to this definition">¶</a></dt>
<dd><p>Process HTTP error responses.</p>
</dd></dl>

<section id="request-objects">
<span id="id1"></span><h2>Request Objects<a class="headerlink" href="#request-objects" title="Link to this heading">¶</a></h2>
<p>The following methods describe <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a>’s public interface,
and so all may be overridden in subclasses.  It also defines several
public attributes that can be used by clients to inspect the parsed
request.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.full_url">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">full_url</span></span><a class="headerlink" href="#urllib.request.Request.full_url" title="Link to this definition">¶</a></dt>
<dd><p>The original URL passed to the constructor.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.</span></p>
</div>
<p>Request.full_url is a property with setter, getter and a deleter. Getting
<a class="reference internal" href="#urllib.request.Request.full_url" title="urllib.request.Request.full_url"><code class="xref py py-attr docutils literal notranslate"><span class="pre">full_url</span></code></a> returns the original request URL with the
fragment, if it was present.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.type">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">type</span></span><a class="headerlink" href="#urllib.request.Request.type" title="Link to this definition">¶</a></dt>
<dd><p>The URI scheme.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.host">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">host</span></span><a class="headerlink" href="#urllib.request.Request.host" title="Link to this definition">¶</a></dt>
<dd><p>The URI authority, typically a host, but may also contain a port
separated by a colon.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.origin_req_host">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">origin_req_host</span></span><a class="headerlink" href="#urllib.request.Request.origin_req_host" title="Link to this definition">¶</a></dt>
<dd><p>The original host for the request, without port.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.selector">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">selector</span></span><a class="headerlink" href="#urllib.request.Request.selector" title="Link to this definition">¶</a></dt>
<dd><p>The URI path.  If the <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> uses a proxy, then selector
will be the full URL that is passed to the proxy.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.data">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">data</span></span><a class="headerlink" href="#urllib.request.Request.data" title="Link to this definition">¶</a></dt>
<dd><p>The entity body for the request, or <code class="docutils literal notranslate"><span class="pre">None</span></code> if not specified.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Changing value of <a class="reference internal" href="#urllib.request.Request.data" title="urllib.request.Request.data"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.data</span></code></a> now deletes “Content-Length”
header if it was previously set or calculated.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.unverifiable">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">unverifiable</span></span><a class="headerlink" href="#urllib.request.Request.unverifiable" title="Link to this definition">¶</a></dt>
<dd><p>boolean, indicates whether the request is unverifiable as defined
by <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2965.html"><strong>RFC 2965</strong></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.Request.method">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">method</span></span><a class="headerlink" href="#urllib.request.Request.method" title="Link to this definition">¶</a></dt>
<dd><p>The HTTP request method to use.  By default its value is <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>,
which means that <a class="reference internal" href="#urllib.request.Request.get_method" title="urllib.request.Request.get_method"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_method()</span></code></a> will do its normal computation
of the method to be used.  Its value can be set (thus overriding the default
computation in <a class="reference internal" href="#urllib.request.Request.get_method" title="urllib.request.Request.get_method"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_method()</span></code></a>) either by providing a default
value by setting it at the class level in a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> subclass, or by
passing a value in to the <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> constructor via the <em>method</em>
argument.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>A default value can now be set in subclasses; previously it could only
be set via the constructor argument.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.get_method">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">get_method</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.get_method" title="Link to this definition">¶</a></dt>
<dd><p>Return a string indicating the HTTP request method.  If
<a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.method</span></code></a> is not <code class="docutils literal notranslate"><span class="pre">None</span></code>, return its value, otherwise return
<code class="docutils literal notranslate"><span class="pre">'GET'</span></code> if <a class="reference internal" href="#urllib.request.Request.data" title="urllib.request.Request.data"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.data</span></code></a> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, or <code class="docutils literal notranslate"><span class="pre">'POST'</span></code> if it’s not.
This is only meaningful for HTTP requests.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>get_method now looks at the value of <a class="reference internal" href="#urllib.request.Request.method" title="urllib.request.Request.method"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.method</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.add_header">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">add_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">val</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.add_header" title="Link to this definition">¶</a></dt>
<dd><p>Add another header to the request.  Headers are currently ignored by all
handlers except HTTP handlers, where they are added to the list of headers sent
to the server.  Note that there cannot be more than one header with the same
name, and later calls will overwrite previous calls in case the <em>key</em> collides.
Currently, this is no loss of HTTP functionality, since all headers which have
meaning when used more than once have a (header-specific) way of gaining the
same functionality using only one header.  Note that headers added using
this method are also added to redirected requests.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.add_unredirected_header">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">add_unredirected_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">key</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">header</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.add_unredirected_header" title="Link to this definition">¶</a></dt>
<dd><p>Add a header that will not be added to a redirected request.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.has_header">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">has_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.has_header" title="Link to this definition">¶</a></dt>
<dd><p>Return whether the instance has the named header (checks both regular and
unredirected).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.remove_header">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">remove_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.remove_header" title="Link to this definition">¶</a></dt>
<dd><p>Remove named header from the request instance (both from regular and
unredirected headers).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.get_full_url">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">get_full_url</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.get_full_url" title="Link to this definition">¶</a></dt>
<dd><p>Return the URL given in the constructor.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.</span></p>
</div>
<p>Returns <a class="reference internal" href="#urllib.request.Request.full_url" title="urllib.request.Request.full_url"><code class="xref py py-attr docutils literal notranslate"><span class="pre">Request.full_url</span></code></a></p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.set_proxy">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">set_proxy</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.set_proxy" title="Link to this definition">¶</a></dt>
<dd><p>Prepare the request by connecting to a proxy server. The <em>host</em> and <em>type</em> will
replace those of the instance, and the instance’s selector will be the original
URL given in the constructor.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.get_header">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">get_header</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">header_name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.get_header" title="Link to this definition">¶</a></dt>
<dd><p>Return the value of the given header. If the header is not present, return
the default value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.Request.header_items">
<span class="sig-prename descclassname"><span class="pre">Request.</span></span><span class="sig-name descname"><span class="pre">header_items</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.Request.header_items" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of tuples (header_name, header_value) of the Request headers.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>The request methods add_data, has_data, get_data, get_type, get_host,
get_selector, get_origin_req_host and is_unverifiable that were deprecated
since 3.3 have been removed.</p>
</div>
</section>
<section id="openerdirector-objects">
<span id="opener-director-objects"></span><h2>OpenerDirector Objects<a class="headerlink" href="#openerdirector-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> instances have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.OpenerDirector.add_handler">
<span class="sig-prename descclassname"><span class="pre">OpenerDirector.</span></span><span class="sig-name descname"><span class="pre">add_handler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">handler</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.OpenerDirector.add_handler" title="Link to this definition">¶</a></dt>
<dd><p><em>handler</em> should be an instance of <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>.  The following methods
are searched, and added to the possible chains (note that HTTP errors are a
special case).  Note that, in the following, <em>protocol</em> should be replaced
with the actual protocol to handle, for example <code class="xref py py-meth docutils literal notranslate"><span class="pre">http_response()</span></code> would
be the HTTP protocol response handler.  Also <em>type</em> should be replaced with
the actual HTTP code, for example <code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_404()</span></code> would handle HTTP
404 errors.</p>
<ul>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_open()</span></code> — signal that the handler knows how to open <em>protocol</em>
URLs.</p>
<p>See <a class="reference internal" href="#protocol-open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BaseHandler.&lt;protocol&gt;_open()</span></code></a> for more information.</p>
</li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_&lt;type&gt;()</span></code> — signal that the handler knows how to handle HTTP
errors with HTTP error code <em>type</em>.</p>
<p>See <a class="reference internal" href="#http-error-nnn"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BaseHandler.http_error_&lt;nnn&gt;()</span></code></a> for more information.</p>
</li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_error()</span></code> — signal that the handler knows how to handle errors
from (non-<code class="docutils literal notranslate"><span class="pre">http</span></code>) <em>protocol</em>.</p></li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_request()</span></code> — signal that the handler knows how to pre-process
<em>protocol</em> requests.</p>
<p>See <a class="reference internal" href="#protocol-request"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BaseHandler.&lt;protocol&gt;_request()</span></code></a> for more information.</p>
</li>
<li><p><code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_response()</span></code> — signal that the handler knows how to
post-process <em>protocol</em> responses.</p>
<p>See <a class="reference internal" href="#protocol-response"><code class="xref py py-meth docutils literal notranslate"><span class="pre">BaseHandler.&lt;protocol&gt;_response()</span></code></a> for more information.</p>
</li>
</ul>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.OpenerDirector.open">
<span class="sig-prename descclassname"><span class="pre">OpenerDirector.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data=None</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.OpenerDirector.open" title="Link to this definition">¶</a></dt>
<dd><p>Open the given <em>url</em> (which can be a request object or a string), optionally
passing the given <em>data</em>. Arguments, return values and exceptions raised are
the same as those of <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a> (which simply calls the <a class="reference internal" href="functions.html#open" title="open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a>
method on the currently installed global <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>).  The
optional <em>timeout</em> parameter specifies a timeout in seconds for blocking
operations like the connection attempt (if not specified, the global default
timeout setting will be used). The timeout feature actually works only for
HTTP, HTTPS and FTP connections.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.OpenerDirector.error">
<span class="sig-prename descclassname"><span class="pre">OpenerDirector.</span></span><span class="sig-name descname"><span class="pre">error</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">proto</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.OpenerDirector.error" title="Link to this definition">¶</a></dt>
<dd><p>Handle an error of the given protocol.  This will call the registered error
handlers for the given protocol with the given arguments (which are protocol
specific).  The HTTP protocol is a special case which uses the HTTP response
code to determine the specific error handler; refer to the <code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_&lt;type&gt;()</span></code>
methods of the handler classes.</p>
<p>Return values and exceptions raised are the same as those of <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.</p>
</dd></dl>

<p>OpenerDirector objects open URLs in three stages:</p>
<p>The order in which these methods are called within each stage is determined by
sorting the handler instances.</p>
<ol class="arabic">
<li><p>Every handler with a method named like <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_request()</span></code> has that
method called to pre-process the request.</p></li>
<li><p>Handlers with a method named like <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_open()</span></code> are called to handle
the request. This stage ends when a handler either returns a non-<a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>
value (ie. a response), or raises an exception (usually
<a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a>).  Exceptions are allowed to propagate.</p>
<p>In fact, the above algorithm is first tried for methods named
<a class="reference internal" href="#urllib.request.BaseHandler.default_open" title="urllib.request.BaseHandler.default_open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default_open()</span></code></a>.  If all such methods return <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the algorithm
is repeated for methods named like <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_open()</span></code>.  If all such methods
return <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a>, the algorithm is repeated for methods named
<a class="reference internal" href="#urllib.request.BaseHandler.unknown_open" title="urllib.request.BaseHandler.unknown_open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">unknown_open()</span></code></a>.</p>
<p>Note that the implementation of these methods may involve calls of the parent
<a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> instance’s <a class="reference internal" href="#urllib.request.OpenerDirector.open" title="urllib.request.OpenerDirector.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> and
<a class="reference internal" href="#urllib.request.OpenerDirector.error" title="urllib.request.OpenerDirector.error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">error()</span></code></a> methods.</p>
</li>
<li><p>Every handler with a method named like <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_response()</span></code> has that
method called to post-process the response.</p></li>
</ol>
</section>
<section id="basehandler-objects">
<span id="base-handler-objects"></span><h2>BaseHandler Objects<a class="headerlink" href="#basehandler-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a> objects provide a couple of methods that are directly
useful, and others that are meant to be used by derived classes.  These are
intended for direct use:</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.add_parent">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">add_parent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">director</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.BaseHandler.add_parent" title="Link to this definition">¶</a></dt>
<dd><p>Add a director as parent.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.close">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.BaseHandler.close" title="Link to this definition">¶</a></dt>
<dd><p>Remove any parents.</p>
</dd></dl>

<p>The following attribute and methods should only be used by classes derived from
<a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The convention has been adopted that subclasses defining
<code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_request()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_response()</span></code> methods are named
<code class="xref py py-class docutils literal notranslate"><span class="pre">*Processor</span></code>; all others are named <code class="xref py py-class docutils literal notranslate"><span class="pre">*Handler</span></code>.</p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.parent">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">parent</span></span><a class="headerlink" href="#urllib.request.BaseHandler.parent" title="Link to this definition">¶</a></dt>
<dd><p>A valid <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>, which can be used to open using a different
protocol, or handle errors.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.default_open">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">default_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.BaseHandler.default_open" title="Link to this definition">¶</a></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
define it if they want to catch all URLs.</p>
<p>This method, if implemented, will be called by the parent
<a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>.  It should return a file-like object as described in
the return value of the <a class="reference internal" href="#urllib.request.OpenerDirector.open" title="urllib.request.OpenerDirector.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open()</span></code></a> method of <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>, or <code class="docutils literal notranslate"><span class="pre">None</span></code>.
It should raise <a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a>, unless a truly exceptional
thing happens (for example, <a class="reference internal" href="exceptions.html#MemoryError" title="MemoryError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">MemoryError</span></code></a> should not be mapped to
<a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a>).</p>
<p>This method will be called before any protocol-specific open method.</p>
</dd></dl>

<dl class="py method" id="protocol-open">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">BaseHandler.&lt;protocol&gt;_open(req)</span></span></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
define it if they want to handle URLs with the given protocol.</p>
<p>This method, if defined, will be called by the parent <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>.
Return values should be the same as for  <a class="reference internal" href="#urllib.request.BaseHandler.default_open" title="urllib.request.BaseHandler.default_open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default_open()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.unknown_open">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">unknown_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.BaseHandler.unknown_open" title="Link to this definition">¶</a></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
define it if they want to catch all URLs with no specific registered handler to
open it.</p>
<p>This method, if implemented, will be called by the <a class="reference internal" href="#urllib.request.BaseHandler.parent" title="urllib.request.BaseHandler.parent"><code class="xref py py-attr docutils literal notranslate"><span class="pre">parent</span></code></a>
<a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>.  Return values should be the same as for
<a class="reference internal" href="#urllib.request.BaseHandler.default_open" title="urllib.request.BaseHandler.default_open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">default_open()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.BaseHandler.http_error_default">
<span class="sig-prename descclassname"><span class="pre">BaseHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_default</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.BaseHandler.http_error_default" title="Link to this definition">¶</a></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
override it if they intend to provide a catch-all for otherwise unhandled HTTP
errors.  It will be called automatically by the  <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> getting
the error, and should not normally be called in other circumstances.</p>
<p><em>req</em> will be a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object, <em>fp</em> will be a file-like object with
the HTTP error body, <em>code</em> will be the three-digit code of the error, <em>msg</em>
will be the user-visible explanation of the code and <em>hdrs</em> will be a mapping
object with the headers of the error.</p>
<p>Return values and exceptions raised should be the same as those of
<a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.</p>
</dd></dl>

<dl class="py method" id="http-error-nnn">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">BaseHandler.http_error_&lt;nnn&gt;(req,</span> <span class="pre">fp,</span> <span class="pre">code,</span> <span class="pre">msg,</span> <span class="pre">hdrs)</span></span></dt>
<dd><p><em>nnn</em> should be a three-digit HTTP error code.  This method is also not defined
in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but will be called, if it exists, on an instance of a
subclass, when an HTTP error with code <em>nnn</em> occurs.</p>
<p>Subclasses should override this method to handle specific HTTP errors.</p>
<p>Arguments, return values and exceptions raised should be the same as for
<a class="reference internal" href="#urllib.request.BaseHandler.http_error_default" title="urllib.request.BaseHandler.http_error_default"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_default()</span></code></a>.</p>
</dd></dl>

<dl class="py method" id="protocol-request">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">BaseHandler.&lt;protocol&gt;_request(req)</span></span></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
define it if they want to pre-process requests of the given protocol.</p>
<p>This method, if defined, will be called by the parent <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>.
<em>req</em> will be a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object. The return value should be a
<a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object.</p>
</dd></dl>

<dl class="py method" id="protocol-response">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">BaseHandler.&lt;protocol&gt;_response(req,</span> <span class="pre">response)</span></span></dt>
<dd><p>This method is <em>not</em> defined in <a class="reference internal" href="#urllib.request.BaseHandler" title="urllib.request.BaseHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">BaseHandler</span></code></a>, but subclasses should
define it if they want to post-process responses of the given protocol.</p>
<p>This method, if defined, will be called by the parent <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a>.
<em>req</em> will be a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object. <em>response</em> will be an object
implementing the same interface as the return value of <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.  The
return value should implement the same interface as the return value of
<a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.</p>
</dd></dl>

</section>
<section id="httpredirecthandler-objects">
<span id="http-redirect-handler"></span><h2>HTTPRedirectHandler Objects<a class="headerlink" href="#httpredirecthandler-objects" title="Link to this heading">¶</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some HTTP redirections require action from this module’s client code.  If this
is the case, <a class="reference internal" href="urllib.error.html#urllib.error.HTTPError" title="urllib.error.HTTPError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPError</span></code></a> is raised.  See <span class="target" id="index-5"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html"><strong>RFC 2616</strong></a> for
details of the precise meanings of the various redirection codes.</p>
<p>An <a class="reference internal" href="urllib.error.html#urllib.error.HTTPError" title="urllib.error.HTTPError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPError</span></code></a> exception raised as a security consideration if the
HTTPRedirectHandler is presented with a redirected URL which is not an HTTP,
HTTPS or FTP URL.</p>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.redirect_request">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">redirect_request</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">newurl</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.redirect_request" title="Link to this definition">¶</a></dt>
<dd><p>Return a <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> or <code class="docutils literal notranslate"><span class="pre">None</span></code> in response to a redirect. This is called
by the default implementations of the <code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_30*()</span></code> methods when a
redirection is received from the server.  If a redirection should take place,
return a new <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> to allow <code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_30*()</span></code> to perform the
redirect to <em>newurl</em>.  Otherwise, raise <a class="reference internal" href="urllib.error.html#urllib.error.HTTPError" title="urllib.error.HTTPError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPError</span></code></a> if
no other handler should try to handle this URL, or return <code class="docutils literal notranslate"><span class="pre">None</span></code> if you
can’t but another handler might.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The default implementation of this method does not strictly follow <span class="target" id="index-6"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html"><strong>RFC 2616</strong></a>,
which says that 301 and 302 responses to <code class="docutils literal notranslate"><span class="pre">POST</span></code> requests must not be
automatically redirected without confirmation by the user.  In reality, browsers
do allow automatic redirection of these responses, changing the POST to a
<code class="docutils literal notranslate"><span class="pre">GET</span></code>, and the default implementation reproduces this behavior.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.http_error_301">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_301</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.http_error_301" title="Link to this definition">¶</a></dt>
<dd><p>Redirect to the <code class="docutils literal notranslate"><span class="pre">Location:</span></code> or <code class="docutils literal notranslate"><span class="pre">URI:</span></code> URL.  This method is called by the
parent <a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> when getting an HTTP ‘moved permanently’ response.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.http_error_302">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_302</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.http_error_302" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#urllib.request.HTTPRedirectHandler.http_error_301" title="urllib.request.HTTPRedirectHandler.http_error_301"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_301()</span></code></a>, but called for the ‘found’ response.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.http_error_303">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_303</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.http_error_303" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#urllib.request.HTTPRedirectHandler.http_error_301" title="urllib.request.HTTPRedirectHandler.http_error_301"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_301()</span></code></a>, but called for the ‘see other’ response.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.http_error_307">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_307</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.http_error_307" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#urllib.request.HTTPRedirectHandler.http_error_301" title="urllib.request.HTTPRedirectHandler.http_error_301"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_301()</span></code></a>, but called for the ‘temporary redirect’
response. It does not allow changing the request method from <code class="docutils literal notranslate"><span class="pre">POST</span></code>
to <code class="docutils literal notranslate"><span class="pre">GET</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPRedirectHandler.http_error_308">
<span class="sig-prename descclassname"><span class="pre">HTTPRedirectHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_308</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPRedirectHandler.http_error_308" title="Link to this definition">¶</a></dt>
<dd><p>The same as <a class="reference internal" href="#urllib.request.HTTPRedirectHandler.http_error_301" title="urllib.request.HTTPRedirectHandler.http_error_301"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_301()</span></code></a>, but called for the ‘permanent redirect’
response. It does not allow changing the request method from <code class="docutils literal notranslate"><span class="pre">POST</span></code>
to <code class="docutils literal notranslate"><span class="pre">GET</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</dd></dl>

</section>
<section id="httpcookieprocessor-objects">
<span id="http-cookie-processor"></span><h2>HTTPCookieProcessor Objects<a class="headerlink" href="#httpcookieprocessor-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#urllib.request.HTTPCookieProcessor" title="urllib.request.HTTPCookieProcessor"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPCookieProcessor</span></code></a> instances have one attribute:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.HTTPCookieProcessor.cookiejar">
<span class="sig-prename descclassname"><span class="pre">HTTPCookieProcessor.</span></span><span class="sig-name descname"><span class="pre">cookiejar</span></span><a class="headerlink" href="#urllib.request.HTTPCookieProcessor.cookiejar" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="http.cookiejar.html#http.cookiejar.CookieJar" title="http.cookiejar.CookieJar"><code class="xref py py-class docutils literal notranslate"><span class="pre">http.cookiejar.CookieJar</span></code></a> in which cookies are stored.</p>
</dd></dl>

</section>
<section id="proxyhandler-objects">
<span id="proxy-handler"></span><h2>ProxyHandler Objects<a class="headerlink" href="#proxyhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py">
<span class="sig-name descname"><span class="pre">ProxyHandler.&lt;protocol&gt;_open(request)</span></span></dt>
<dd><p>The <a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> will have a method <code class="xref py py-meth docutils literal notranslate"><span class="pre">&lt;protocol&gt;_open()</span></code> for every
<em>protocol</em> which has a proxy in the <em>proxies</em> dictionary given in the
constructor.  The method will modify requests to go through the proxy, by
calling <code class="docutils literal notranslate"><span class="pre">request.set_proxy()</span></code>, and call the next handler in the chain to
actually execute the protocol.</p>
</dd></dl>

</section>
<section id="httppasswordmgr-objects">
<span id="http-password-mgr"></span><h2>HTTPPasswordMgr Objects<a class="headerlink" href="#httppasswordmgr-objects" title="Link to this heading">¶</a></h2>
<p>These methods are available on <a class="reference internal" href="#urllib.request.HTTPPasswordMgr" title="urllib.request.HTTPPasswordMgr"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgr</span></code></a> and
<a class="reference internal" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="urllib.request.HTTPPasswordMgrWithDefaultRealm"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></code></a> objects.</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgr.add_password">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgr.</span></span><span class="sig-name descname"><span class="pre">add_password</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">realm</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">passwd</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgr.add_password" title="Link to this definition">¶</a></dt>
<dd><p><em>uri</em> can be either a single URI, or a sequence of URIs. <em>realm</em>, <em>user</em> and
<em>passwd</em> must be strings. This causes <code class="docutils literal notranslate"><span class="pre">(user,</span> <span class="pre">passwd)</span></code> to be used as
authentication tokens when authentication for <em>realm</em> and a super-URI of any of
the given URIs is given.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgr.find_user_password">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgr.</span></span><span class="sig-name descname"><span class="pre">find_user_password</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">realm</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">authuri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgr.find_user_password" title="Link to this definition">¶</a></dt>
<dd><p>Get user/password for given realm and URI, if any.  This method will return
<code class="docutils literal notranslate"><span class="pre">(None,</span> <span class="pre">None)</span></code> if there is no matching user/password.</p>
<p>For <a class="reference internal" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="urllib.request.HTTPPasswordMgrWithDefaultRealm"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></code></a> objects, the realm <code class="docutils literal notranslate"><span class="pre">None</span></code> will be
searched if the given <em>realm</em> has no matching user/password.</p>
</dd></dl>

</section>
<section id="httppasswordmgrwithpriorauth-objects">
<span id="http-password-mgr-with-prior-auth"></span><h2>HTTPPasswordMgrWithPriorAuth Objects<a class="headerlink" href="#httppasswordmgrwithpriorauth-objects" title="Link to this heading">¶</a></h2>
<p>This password manager extends <a class="reference internal" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="urllib.request.HTTPPasswordMgrWithDefaultRealm"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></code></a> to support
tracking URIs for which authentication credentials should always be sent.</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithPriorAuth.add_password">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgrWithPriorAuth.</span></span><span class="sig-name descname"><span class="pre">add_password</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">realm</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">passwd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_authenticated</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithPriorAuth.add_password" title="Link to this definition">¶</a></dt>
<dd><p><em>realm</em>, <em>uri</em>, <em>user</em>, <em>passwd</em> are as for
<a class="reference internal" href="#urllib.request.HTTPPasswordMgr.add_password" title="urllib.request.HTTPPasswordMgr.add_password"><code class="xref py py-meth docutils literal notranslate"><span class="pre">HTTPPasswordMgr.add_password()</span></code></a>.  <em>is_authenticated</em> sets the initial
value of the <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> flag for the given URI or list of URIs.
If <em>is_authenticated</em> is specified as <code class="docutils literal notranslate"><span class="pre">True</span></code>, <em>realm</em> is ignored.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithPriorAuth.find_user_password">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgrWithPriorAuth.</span></span><span class="sig-name descname"><span class="pre">find_user_password</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">realm</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">authuri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithPriorAuth.find_user_password" title="Link to this definition">¶</a></dt>
<dd><p>Same as for <a class="reference internal" href="#urllib.request.HTTPPasswordMgrWithDefaultRealm" title="urllib.request.HTTPPasswordMgrWithDefaultRealm"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPPasswordMgrWithDefaultRealm</span></code></a> objects</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithPriorAuth.update_authenticated">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgrWithPriorAuth.</span></span><span class="sig-name descname"><span class="pre">update_authenticated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_authenticated</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithPriorAuth.update_authenticated" title="Link to this definition">¶</a></dt>
<dd><p>Update the <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> flag for the given <em>uri</em> or list
of URIs.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPPasswordMgrWithPriorAuth.is_authenticated">
<span class="sig-prename descclassname"><span class="pre">HTTPPasswordMgrWithPriorAuth.</span></span><span class="sig-name descname"><span class="pre">is_authenticated</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">self</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">authuri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPPasswordMgrWithPriorAuth.is_authenticated" title="Link to this definition">¶</a></dt>
<dd><p>Returns the current state of the <code class="docutils literal notranslate"><span class="pre">is_authenticated</span></code> flag for
the given URI.</p>
</dd></dl>

</section>
<section id="abstractbasicauthhandler-objects">
<span id="abstract-basic-auth-handler"></span><h2>AbstractBasicAuthHandler Objects<a class="headerlink" href="#abstractbasicauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.AbstractBasicAuthHandler.http_error_auth_reqed">
<span class="sig-prename descclassname"><span class="pre">AbstractBasicAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_auth_reqed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">authreq</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.AbstractBasicAuthHandler.http_error_auth_reqed" title="Link to this definition">¶</a></dt>
<dd><p>Handle an authentication request by getting a user/password pair, and re-trying
the request.  <em>authreq</em> should be the name of the header where the information
about the realm is included in the request, <em>host</em> specifies the URL and path to
authenticate for, <em>req</em> should be the (failed) <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object, and
<em>headers</em> should be the error headers.</p>
<p><em>host</em> is either an authority (e.g. <code class="docutils literal notranslate"><span class="pre">&quot;python.org&quot;</span></code>) or a URL containing an
authority component (e.g. <code class="docutils literal notranslate"><span class="pre">&quot;http://python.org/&quot;</span></code>). In either case, the
authority must not contain a userinfo component (so, <code class="docutils literal notranslate"><span class="pre">&quot;python.org&quot;</span></code> and
<code class="docutils literal notranslate"><span class="pre">&quot;python.org:80&quot;</span></code> are fine, <code class="docutils literal notranslate"><span class="pre">&quot;joe:password&#64;python.org&quot;</span></code> is not).</p>
</dd></dl>

</section>
<section id="httpbasicauthhandler-objects">
<span id="http-basic-auth-handler"></span><h2>HTTPBasicAuthHandler Objects<a class="headerlink" href="#httpbasicauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPBasicAuthHandler.http_error_401">
<span class="sig-prename descclassname"><span class="pre">HTTPBasicAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_401</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPBasicAuthHandler.http_error_401" title="Link to this definition">¶</a></dt>
<dd><p>Retry the request with authentication information, if available.</p>
</dd></dl>

</section>
<section id="proxybasicauthhandler-objects">
<span id="proxy-basic-auth-handler"></span><h2>ProxyBasicAuthHandler Objects<a class="headerlink" href="#proxybasicauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.ProxyBasicAuthHandler.http_error_407">
<span class="sig-prename descclassname"><span class="pre">ProxyBasicAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_407</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.ProxyBasicAuthHandler.http_error_407" title="Link to this definition">¶</a></dt>
<dd><p>Retry the request with authentication information, if available.</p>
</dd></dl>

</section>
<section id="abstractdigestauthhandler-objects">
<span id="abstract-digest-auth-handler"></span><h2>AbstractDigestAuthHandler Objects<a class="headerlink" href="#abstractdigestauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.AbstractDigestAuthHandler.http_error_auth_reqed">
<span class="sig-prename descclassname"><span class="pre">AbstractDigestAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_auth_reqed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">authreq</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">headers</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.AbstractDigestAuthHandler.http_error_auth_reqed" title="Link to this definition">¶</a></dt>
<dd><p><em>authreq</em> should be the name of the header where the information about the realm
is included in the request, <em>host</em> should be the host to authenticate to, <em>req</em>
should be the (failed) <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> object, and <em>headers</em> should be the
error headers.</p>
</dd></dl>

</section>
<section id="httpdigestauthhandler-objects">
<span id="http-digest-auth-handler"></span><h2>HTTPDigestAuthHandler Objects<a class="headerlink" href="#httpdigestauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPDigestAuthHandler.http_error_401">
<span class="sig-prename descclassname"><span class="pre">HTTPDigestAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_401</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPDigestAuthHandler.http_error_401" title="Link to this definition">¶</a></dt>
<dd><p>Retry the request with authentication information, if available.</p>
</dd></dl>

</section>
<section id="proxydigestauthhandler-objects">
<span id="proxy-digest-auth-handler"></span><h2>ProxyDigestAuthHandler Objects<a class="headerlink" href="#proxydigestauthhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.ProxyDigestAuthHandler.http_error_407">
<span class="sig-prename descclassname"><span class="pre">ProxyDigestAuthHandler.</span></span><span class="sig-name descname"><span class="pre">http_error_407</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fp</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">code</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">msg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">hdrs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.ProxyDigestAuthHandler.http_error_407" title="Link to this definition">¶</a></dt>
<dd><p>Retry the request with authentication information, if available.</p>
</dd></dl>

</section>
<section id="httphandler-objects">
<span id="http-handler-objects"></span><h2>HTTPHandler Objects<a class="headerlink" href="#httphandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPHandler.http_open">
<span class="sig-prename descclassname"><span class="pre">HTTPHandler.</span></span><span class="sig-name descname"><span class="pre">http_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPHandler.http_open" title="Link to this definition">¶</a></dt>
<dd><p>Send an HTTP request, which can be either GET or POST, depending on
<code class="docutils literal notranslate"><span class="pre">req.has_data()</span></code>.</p>
</dd></dl>

</section>
<section id="httpshandler-objects">
<span id="https-handler-objects"></span><h2>HTTPSHandler Objects<a class="headerlink" href="#httpshandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPSHandler.https_open">
<span class="sig-prename descclassname"><span class="pre">HTTPSHandler.</span></span><span class="sig-name descname"><span class="pre">https_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPSHandler.https_open" title="Link to this definition">¶</a></dt>
<dd><p>Send an HTTPS request, which can be either GET or POST, depending on
<code class="docutils literal notranslate"><span class="pre">req.has_data()</span></code>.</p>
</dd></dl>

</section>
<section id="filehandler-objects">
<span id="file-handler-objects"></span><h2>FileHandler Objects<a class="headerlink" href="#filehandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.FileHandler.file_open">
<span class="sig-prename descclassname"><span class="pre">FileHandler.</span></span><span class="sig-name descname"><span class="pre">file_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.FileHandler.file_open" title="Link to this definition">¶</a></dt>
<dd><p>Open the file locally, if there is no host name, or the host name is
<code class="docutils literal notranslate"><span class="pre">'localhost'</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>This method is applicable only for local hostnames.  When a remote
hostname is given, an <a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a> is raised.</p>
</div>
</dd></dl>

</section>
<section id="datahandler-objects">
<span id="data-handler-objects"></span><h2>DataHandler Objects<a class="headerlink" href="#datahandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.DataHandler.data_open">
<span class="sig-prename descclassname"><span class="pre">DataHandler.</span></span><span class="sig-name descname"><span class="pre">data_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.DataHandler.data_open" title="Link to this definition">¶</a></dt>
<dd><p>Read a data URL. This kind of URL contains the content encoded in the URL
itself. The data URL syntax is specified in <span class="target" id="index-7"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2397.html"><strong>RFC 2397</strong></a>. This implementation
ignores white spaces in base64 encoded data URLs so the URL may be wrapped
in whatever source file it comes from. But even though some browsers don’t
mind about a missing padding at the end of a base64 encoded data URL, this
implementation will raise an <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> in that case.</p>
</dd></dl>

</section>
<section id="ftphandler-objects">
<span id="ftp-handler-objects"></span><h2>FTPHandler Objects<a class="headerlink" href="#ftphandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.FTPHandler.ftp_open">
<span class="sig-prename descclassname"><span class="pre">FTPHandler.</span></span><span class="sig-name descname"><span class="pre">ftp_open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">req</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.FTPHandler.ftp_open" title="Link to this definition">¶</a></dt>
<dd><p>Open the FTP file indicated by <em>req</em>. The login is always done with empty
username and password.</p>
</dd></dl>

</section>
<section id="cacheftphandler-objects">
<span id="cacheftp-handler-objects"></span><h2>CacheFTPHandler Objects<a class="headerlink" href="#cacheftphandler-objects" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#urllib.request.CacheFTPHandler" title="urllib.request.CacheFTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">CacheFTPHandler</span></code></a> objects are <a class="reference internal" href="#urllib.request.FTPHandler" title="urllib.request.FTPHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">FTPHandler</span></code></a> objects with the
following additional methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.CacheFTPHandler.setTimeout">
<span class="sig-prename descclassname"><span class="pre">CacheFTPHandler.</span></span><span class="sig-name descname"><span class="pre">setTimeout</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.CacheFTPHandler.setTimeout" title="Link to this definition">¶</a></dt>
<dd><p>Set timeout of connections to <em>t</em> seconds.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.CacheFTPHandler.setMaxConns">
<span class="sig-prename descclassname"><span class="pre">CacheFTPHandler.</span></span><span class="sig-name descname"><span class="pre">setMaxConns</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">m</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.CacheFTPHandler.setMaxConns" title="Link to this definition">¶</a></dt>
<dd><p>Set maximum number of cached connections to <em>m</em>.</p>
</dd></dl>

</section>
<section id="unknownhandler-objects">
<span id="unknown-handler-objects"></span><h2>UnknownHandler Objects<a class="headerlink" href="#unknownhandler-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.UnknownHandler.unknown_open">
<span class="sig-prename descclassname"><span class="pre">UnknownHandler.</span></span><span class="sig-name descname"><span class="pre">unknown_open</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.UnknownHandler.unknown_open" title="Link to this definition">¶</a></dt>
<dd><p>Raise a <a class="reference internal" href="urllib.error.html#urllib.error.URLError" title="urllib.error.URLError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">URLError</span></code></a> exception.</p>
</dd></dl>

</section>
<section id="httperrorprocessor-objects">
<span id="http-error-processor-objects"></span><h2>HTTPErrorProcessor Objects<a class="headerlink" href="#httperrorprocessor-objects" title="Link to this heading">¶</a></h2>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPErrorProcessor.http_response">
<span class="sig-prename descclassname"><span class="pre">HTTPErrorProcessor.</span></span><span class="sig-name descname"><span class="pre">http_response</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPErrorProcessor.http_response" title="Link to this definition">¶</a></dt>
<dd><p>Process HTTP error responses.</p>
<p>For 200 error codes, the response object is returned immediately.</p>
<p>For non-200 error codes, this simply passes the job on to the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_&lt;type&gt;()</span></code> handler methods, via <a class="reference internal" href="#urllib.request.OpenerDirector.error" title="urllib.request.OpenerDirector.error"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OpenerDirector.error()</span></code></a>.
Eventually, <a class="reference internal" href="#urllib.request.HTTPDefaultErrorHandler" title="urllib.request.HTTPDefaultErrorHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">HTTPDefaultErrorHandler</span></code></a> will raise an
<a class="reference internal" href="urllib.error.html#urllib.error.HTTPError" title="urllib.error.HTTPError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">HTTPError</span></code></a> if no other handler handles the error.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.HTTPErrorProcessor.https_response">
<span class="sig-prename descclassname"><span class="pre">HTTPErrorProcessor.</span></span><span class="sig-name descname"><span class="pre">https_response</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">response</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.HTTPErrorProcessor.https_response" title="Link to this definition">¶</a></dt>
<dd><p>Process HTTPS error responses.</p>
<p>The behavior is same as <a class="reference internal" href="#urllib.request.HTTPErrorProcessor.http_response" title="urllib.request.HTTPErrorProcessor.http_response"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_response()</span></code></a>.</p>
</dd></dl>

</section>
<section id="examples">
<span id="urllib-request-examples"></span><h2>Examples<a class="headerlink" href="#examples" title="Link to this heading">¶</a></h2>
<p>In addition to the examples below, more examples are given in
<a class="reference internal" href="../howto/urllib2.html#urllib-howto"><span class="std std-ref">HOWTO Fetch Internet Resources Using The urllib Package</span></a>.</p>
<p>This example gets the python.org main page and displays the first 300 bytes of
it.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;http://www.python.org/&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">300</span><span class="p">))</span>
<span class="gp">...</span>
<span class="go">b&#39;&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot;</span>
<span class="go">&quot;http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd&quot;&gt;\n\n\n&lt;html</span>
<span class="go">xmlns=&quot;http://www.w3.org/1999/xhtml&quot; xml:lang=&quot;en&quot; lang=&quot;en&quot;&gt;\n\n&lt;head&gt;\n</span>
<span class="go">&lt;meta http-equiv=&quot;content-type&quot; content=&quot;text/html; charset=utf-8&quot; /&gt;\n</span>
<span class="go">&lt;title&gt;Python Programming &#39;</span>
</pre></div>
</div>
<p>Note that urlopen returns a bytes object.  This is because there is no way
for urlopen to automatically determine the encoding of the byte stream
it receives from the HTTP server. In general, a program will decode
the returned bytes object to string once it determines or guesses
the appropriate encoding.</p>
<p>The following W3C document, <a class="reference external" href="https://www.w3.org/International/O-charset">https://www.w3.org/International/O-charset</a>, lists
the various ways in which an (X)HTML or an XML document could have specified its
encoding information.</p>
<p>As the python.org website uses <em>utf-8</em> encoding as specified in its meta tag, we
will use the same for decoding the bytes object.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;http://www.python.org/&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="gp">...</span>
<span class="go">&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot;</span>
<span class="go">&quot;http://www.w3.org/TR/xhtml1/DTD/xhtm</span>
</pre></div>
</div>
<p>It is also possible to achieve the same result without using the
<a class="reference internal" href="../glossary.html#term-context-manager"><span class="xref std std-term">context manager</span></a> approach.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">f</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;http://www.python.org/&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="go">&lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot;</span>
<span class="go">&quot;http://www.w3.org/TR/xhtml1/DTD/xhtm</span>
</pre></div>
</div>
<p>In the following example, we are sending a data-stream to the stdin of a CGI
and reading the data it returns to us. Note that this example will only work
when the Python installation supports SSL.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">req</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">Request</span><span class="p">(</span><span class="n">url</span><span class="o">=</span><span class="s1">&#39;https://localhost/cgi-bin/test.cgi&#39;</span><span class="p">,</span>
<span class="gp">... </span>                      <span class="n">data</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;This data is passed to stdin of the CGI&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="n">req</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="gp">...</span>
<span class="go">Got Data: &quot;This data is passed to stdin of the CGI&quot;</span>
</pre></div>
</div>
<p>The code for the sample CGI used in the above example is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="ch">#!/usr/bin/env python</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="n">data</span> <span class="o">=</span> <span class="n">sys</span><span class="o">.</span><span class="n">stdin</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Content-type: text/plain</span><span class="se">\n\n</span><span class="s1">Got Data: &quot;</span><span class="si">%s</span><span class="s1">&quot;&#39;</span> <span class="o">%</span> <span class="n">data</span><span class="p">)</span>
</pre></div>
</div>
<p>Here is an example of doing a <code class="docutils literal notranslate"><span class="pre">PUT</span></code> request using <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="n">DATA</span> <span class="o">=</span> <span class="sa">b</span><span class="s1">&#39;some data&#39;</span>
<span class="n">req</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">Request</span><span class="p">(</span><span class="n">url</span><span class="o">=</span><span class="s1">&#39;http://localhost:8080&#39;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="n">DATA</span><span class="p">,</span> <span class="n">method</span><span class="o">=</span><span class="s1">&#39;PUT&#39;</span><span class="p">)</span>
<span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="n">req</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
    <span class="k">pass</span>
<span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">status</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">reason</span><span class="p">)</span>
</pre></div>
</div>
<p>Use of Basic HTTP Authentication:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="c1"># Create an OpenerDirector with support for Basic HTTP Authentication...</span>
<span class="n">auth_handler</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">HTTPBasicAuthHandler</span><span class="p">()</span>
<span class="n">auth_handler</span><span class="o">.</span><span class="n">add_password</span><span class="p">(</span><span class="n">realm</span><span class="o">=</span><span class="s1">&#39;PDQ Application&#39;</span><span class="p">,</span>
                          <span class="n">uri</span><span class="o">=</span><span class="s1">&#39;https://mahler:8092/site-updates.py&#39;</span><span class="p">,</span>
                          <span class="n">user</span><span class="o">=</span><span class="s1">&#39;klem&#39;</span><span class="p">,</span>
                          <span class="n">passwd</span><span class="o">=</span><span class="s1">&#39;kadidd!ehopper&#39;</span><span class="p">)</span>
<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">(</span><span class="n">auth_handler</span><span class="p">)</span>
<span class="c1"># ...and install it globally so it can be used with urlopen.</span>
<span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">install_opener</span><span class="p">(</span><span class="n">opener</span><span class="p">)</span>
<span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s1">&#39;http://www.example.com/login.html&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#urllib.request.build_opener" title="urllib.request.build_opener"><code class="xref py py-func docutils literal notranslate"><span class="pre">build_opener()</span></code></a> provides many handlers by default, including a
<a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a>.  By default, <a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> uses the environment
variables named <code class="docutils literal notranslate"><span class="pre">&lt;scheme&gt;_proxy</span></code>, where <code class="docutils literal notranslate"><span class="pre">&lt;scheme&gt;</span></code> is the URL scheme
involved.  For example, the <code class="xref std std-envvar docutils literal notranslate"><span class="pre">http_proxy</span></code> environment variable is read to
obtain the HTTP proxy’s URL.</p>
<p>This example replaces the default <a class="reference internal" href="#urllib.request.ProxyHandler" title="urllib.request.ProxyHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyHandler</span></code></a> with one that uses
programmatically supplied proxy URLs, and adds proxy authorization support with
<a class="reference internal" href="#urllib.request.ProxyBasicAuthHandler" title="urllib.request.ProxyBasicAuthHandler"><code class="xref py py-class docutils literal notranslate"><span class="pre">ProxyBasicAuthHandler</span></code></a>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">proxy_handler</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">ProxyHandler</span><span class="p">({</span><span class="s1">&#39;http&#39;</span><span class="p">:</span> <span class="s1">&#39;http://www.example.com:3128/&#39;</span><span class="p">})</span>
<span class="n">proxy_auth_handler</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">ProxyBasicAuthHandler</span><span class="p">()</span>
<span class="n">proxy_auth_handler</span><span class="o">.</span><span class="n">add_password</span><span class="p">(</span><span class="s1">&#39;realm&#39;</span><span class="p">,</span> <span class="s1">&#39;host&#39;</span><span class="p">,</span> <span class="s1">&#39;username&#39;</span><span class="p">,</span> <span class="s1">&#39;password&#39;</span><span class="p">)</span>

<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">(</span><span class="n">proxy_handler</span><span class="p">,</span> <span class="n">proxy_auth_handler</span><span class="p">)</span>
<span class="c1"># This time, rather than install the OpenerDirector, we use it directly:</span>
<span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;http://www.example.com/login.html&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Adding HTTP headers:</p>
<p>Use the <em>headers</em> argument to the <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> constructor, or:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="n">req</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">Request</span><span class="p">(</span><span class="s1">&#39;http://www.example.com/&#39;</span><span class="p">)</span>
<span class="n">req</span><span class="o">.</span><span class="n">add_header</span><span class="p">(</span><span class="s1">&#39;Referer&#39;</span><span class="p">,</span> <span class="s1">&#39;http://www.python.org/&#39;</span><span class="p">)</span>
<span class="c1"># Customize the default User-Agent header value:</span>
<span class="n">req</span><span class="o">.</span><span class="n">add_header</span><span class="p">(</span><span class="s1">&#39;User-Agent&#39;</span><span class="p">,</span> <span class="s1">&#39;urllib-example/0.1 (Contact: . . .)&#39;</span><span class="p">)</span>
<span class="n">r</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="n">req</span><span class="p">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#urllib.request.OpenerDirector" title="urllib.request.OpenerDirector"><code class="xref py py-class docutils literal notranslate"><span class="pre">OpenerDirector</span></code></a> automatically adds a <em class="mailheader">User-Agent</em> header to
every <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a>.  To change this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">build_opener</span><span class="p">()</span>
<span class="n">opener</span><span class="o">.</span><span class="n">addheaders</span> <span class="o">=</span> <span class="p">[(</span><span class="s1">&#39;User-agent&#39;</span><span class="p">,</span> <span class="s1">&#39;Mozilla/5.0&#39;</span><span class="p">)]</span>
<span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s1">&#39;http://www.example.com/&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Also, remember that a few standard headers (<em class="mailheader">Content-Length</em>,
<em class="mailheader">Content-Type</em> and <em class="mailheader">Host</em>)
are added when the <a class="reference internal" href="#urllib.request.Request" title="urllib.request.Request"><code class="xref py py-class docutils literal notranslate"><span class="pre">Request</span></code></a> is passed to <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a> (or
<a class="reference internal" href="#urllib.request.OpenerDirector.open" title="urllib.request.OpenerDirector.open"><code class="xref py py-meth docutils literal notranslate"><span class="pre">OpenerDirector.open()</span></code></a>).</p>
<p id="urllib-examples">Here is an example session that uses the <code class="docutils literal notranslate"><span class="pre">GET</span></code> method to retrieve a URL
containing parameters:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.parse</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">params</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlencode</span><span class="p">({</span><span class="s1">&#39;spam&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;eggs&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;bacon&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">url</span> <span class="o">=</span> <span class="s2">&quot;http://www.musi-cal.com/cgi-bin/query?</span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">params</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="n">url</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>The following example uses the <code class="docutils literal notranslate"><span class="pre">POST</span></code> method instead. Note that params output
from urlencode is encoded to bytes before it is sent to urlopen as data:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.parse</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">parse</span><span class="o">.</span><span class="n">urlencode</span><span class="p">({</span><span class="s1">&#39;spam&#39;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;eggs&#39;</span><span class="p">:</span> <span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;bacon&#39;</span><span class="p">:</span> <span class="mi">0</span><span class="p">})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">data</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">encode</span><span class="p">(</span><span class="s1">&#39;ascii&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlopen</span><span class="p">(</span><span class="s2">&quot;http://requestb.in/xrbl82xr&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">))</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>The following example uses an explicitly specified HTTP proxy, overriding
environment settings:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">proxies</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;http&#39;</span><span class="p">:</span> <span class="s1">&#39;http://proxy.example.com:8080/&#39;</span><span class="p">}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">FancyURLopener</span><span class="p">(</span><span class="n">proxies</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;http://www.python.org&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>The following example uses no proxies at all, overriding environment settings:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">opener</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">FancyURLopener</span><span class="p">({})</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">with</span> <span class="n">opener</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="s2">&quot;http://www.python.org/&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">f</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">(</span><span class="s1">&#39;utf-8&#39;</span><span class="p">)</span>
<span class="gp">...</span>
</pre></div>
</div>
</section>
<section id="legacy-interface">
<h2>Legacy interface<a class="headerlink" href="#legacy-interface" title="Link to this heading">¶</a></h2>
<p>The following functions and classes are ported from the Python 2 module
<code class="docutils literal notranslate"><span class="pre">urllib</span></code> (as opposed to <code class="docutils literal notranslate"><span class="pre">urllib2</span></code>).  They might become deprecated at
some point in the future.</p>
<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.urlretrieve">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">urlretrieve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reporthook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.urlretrieve" title="Link to this definition">¶</a></dt>
<dd><p>Copy a network object denoted by a URL to a local file. If the URL
points to a local file, the object will not be copied unless filename is supplied.
Return a tuple <code class="docutils literal notranslate"><span class="pre">(filename,</span> <span class="pre">headers)</span></code> where <em>filename</em> is the
local file name under which the object can be found, and <em>headers</em> is whatever
the <code class="xref py py-meth docutils literal notranslate"><span class="pre">info()</span></code> method of the object returned by <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a> returned (for
a remote object). Exceptions are the same as for <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.</p>
<p>The second argument, if present, specifies the file location to copy to (if
absent, the location will be a tempfile with a generated name). The third
argument, if present, is a callable that will be called once on
establishment of the network connection and once after each block read
thereafter.  The callable will be passed three arguments; a count of blocks
transferred so far, a block size in bytes, and the total size of the file.  The
third argument may be <code class="docutils literal notranslate"><span class="pre">-1</span></code> on older FTP servers which do not return a file
size in response to a retrieval request.</p>
<p>The following example illustrates the most common usage scenario:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">urllib.request</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">local_filename</span><span class="p">,</span> <span class="n">headers</span> <span class="o">=</span> <span class="n">urllib</span><span class="o">.</span><span class="n">request</span><span class="o">.</span><span class="n">urlretrieve</span><span class="p">(</span><span class="s1">&#39;http://python.org/&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">html</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="n">local_filename</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">html</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
<p>If the <em>url</em> uses the <code class="file docutils literal notranslate"><span class="pre">http:</span></code> scheme identifier, the optional <em>data</em>
argument may be given to specify a <code class="docutils literal notranslate"><span class="pre">POST</span></code> request (normally the request
type is <code class="docutils literal notranslate"><span class="pre">GET</span></code>).  The <em>data</em> argument must be a bytes object in standard
<em class="mimetype">application/x-www-form-urlencoded</em> format; see the
<a class="reference internal" href="urllib.parse.html#urllib.parse.urlencode" title="urllib.parse.urlencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.parse.urlencode()</span></code></a> function.</p>
<p><a class="reference internal" href="#urllib.request.urlretrieve" title="urllib.request.urlretrieve"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlretrieve()</span></code></a> will raise <a class="reference internal" href="urllib.error.html#urllib.error.ContentTooShortError" title="urllib.error.ContentTooShortError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ContentTooShortError</span></code></a> when it detects that
the amount of data available  was less than the expected amount (which is the
size reported by a  <em>Content-Length</em> header). This can occur, for example, when
the  download is interrupted.</p>
<p>The <em>Content-Length</em> is treated as a lower bound: if there’s more data  to read,
urlretrieve reads more data, but if less data is available,  it raises the
exception.</p>
<p>You can still retrieve the downloaded data in this case, it is stored in the
<code class="xref py py-attr docutils literal notranslate"><span class="pre">content</span></code> attribute of the exception instance.</p>
<p>If no <em>Content-Length</em> header was supplied, urlretrieve can not check the size
of the data it has downloaded, and just returns it.  In this case you just have
to assume that the download was successful.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="urllib.request.urlcleanup">
<span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">urlcleanup</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.urlcleanup" title="Link to this definition">¶</a></dt>
<dd><p>Cleans up temporary files that may have been left behind by previous
calls to <a class="reference internal" href="#urllib.request.urlretrieve" title="urllib.request.urlretrieve"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlretrieve()</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.URLopener">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">URLopener</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">proxies</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">x509</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.URLopener" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.3.</span></p>
</div>
<p>Base class for opening and reading URLs.  Unless you need to support opening
objects using schemes other than <code class="file docutils literal notranslate"><span class="pre">http:</span></code>, <code class="file docutils literal notranslate"><span class="pre">ftp:</span></code>, or <code class="file docutils literal notranslate"><span class="pre">file:</span></code>,
you probably want to use <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a>.</p>
<p>By default, the <a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a> class sends a <em class="mailheader">User-Agent</em> header
of <code class="docutils literal notranslate"><span class="pre">urllib/VVV</span></code>, where <em>VVV</em> is the <a class="reference internal" href="urllib.html#module-urllib" title="urllib"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code></a> version number.
Applications can define their own <em class="mailheader">User-Agent</em> header by subclassing
<a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a> or <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a> and setting the class attribute
<a class="reference internal" href="#urllib.request.URLopener.version" title="urllib.request.URLopener.version"><code class="xref py py-attr docutils literal notranslate"><span class="pre">version</span></code></a> to an appropriate string value in the subclass definition.</p>
<p>The optional <em>proxies</em> parameter should be a dictionary mapping scheme names to
proxy URLs, where an empty dictionary turns proxies off completely.  Its default
value is <code class="docutils literal notranslate"><span class="pre">None</span></code>, in which case environmental proxy settings will be used if
present, as discussed in the definition of <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>, above.</p>
<p>Additional keyword parameters, collected in <em>x509</em>, may be used for
authentication of the client when using the <code class="file docutils literal notranslate"><span class="pre">https:</span></code> scheme.  The keywords
<em>key_file</em> and <em>cert_file</em> are supported to provide an  SSL key and certificate;
both are needed to support client authentication.</p>
<p><a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a> objects will raise an <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> exception if the server
returns an error code.</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.URLopener.open">
<span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullurl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.URLopener.open" title="Link to this definition">¶</a></dt>
<dd><p>Open <em>fullurl</em> using the appropriate protocol.  This method sets up cache and
proxy information, then calls the appropriate open method with its input
arguments.  If the scheme is not recognized, <a class="reference internal" href="#urllib.request.URLopener.open_unknown" title="urllib.request.URLopener.open_unknown"><code class="xref py py-meth docutils literal notranslate"><span class="pre">open_unknown()</span></code></a> is called.
The <em>data</em> argument has the same meaning as the <em>data</em> argument of
<a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a>.</p>
<p>This method always quotes <em>fullurl</em> using <a class="reference internal" href="urllib.parse.html#urllib.parse.quote" title="urllib.parse.quote"><code class="xref py py-func docutils literal notranslate"><span class="pre">quote()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.URLopener.open_unknown">
<span class="sig-name descname"><span class="pre">open_unknown</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullurl</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.URLopener.open_unknown" title="Link to this definition">¶</a></dt>
<dd><p>Overridable interface to open unknown URL types.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.URLopener.retrieve">
<span class="sig-name descname"><span class="pre">retrieve</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">url</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">reporthook</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.URLopener.retrieve" title="Link to this definition">¶</a></dt>
<dd><p>Retrieves the contents of <em>url</em> and places it in <em>filename</em>.  The return value
is a tuple consisting of a local filename and either an
<a class="reference internal" href="email.compat32-message.html#email.message.Message" title="email.message.Message"><code class="xref py py-class docutils literal notranslate"><span class="pre">email.message.Message</span></code></a> object containing the response headers (for remote
URLs) or <code class="docutils literal notranslate"><span class="pre">None</span></code> (for local URLs).  The caller must then open and read the
contents of <em>filename</em>.  If <em>filename</em> is not given and the URL refers to a
local file, the input filename is returned.  If the URL is non-local and
<em>filename</em> is not given, the filename is the output of <a class="reference internal" href="tempfile.html#tempfile.mktemp" title="tempfile.mktemp"><code class="xref py py-func docutils literal notranslate"><span class="pre">tempfile.mktemp()</span></code></a>
with a suffix that matches the suffix of the last path component of the input
URL.  If <em>reporthook</em> is given, it must be a function accepting three numeric
parameters: A chunk number, the maximum size chunks are read in and the total size of the download
(-1 if unknown).  It will be called once at the start and after each chunk of data is read from the
network.  <em>reporthook</em> is ignored for local URLs.</p>
<p>If the <em>url</em> uses the <code class="file docutils literal notranslate"><span class="pre">http:</span></code> scheme identifier, the optional <em>data</em>
argument may be given to specify a <code class="docutils literal notranslate"><span class="pre">POST</span></code> request (normally the request type
is <code class="docutils literal notranslate"><span class="pre">GET</span></code>).  The <em>data</em> argument must in standard
<em class="mimetype">application/x-www-form-urlencoded</em> format; see the
<a class="reference internal" href="urllib.parse.html#urllib.parse.urlencode" title="urllib.parse.urlencode"><code class="xref py py-func docutils literal notranslate"><span class="pre">urllib.parse.urlencode()</span></code></a> function.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.request.URLopener.version">
<span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#urllib.request.URLopener.version" title="Link to this definition">¶</a></dt>
<dd><p>Variable that specifies the user agent of the opener object.  To get
<a class="reference internal" href="urllib.html#module-urllib" title="urllib"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code></a> to tell servers that it is a particular user agent, set this in a
subclass as a class variable or in the constructor before calling the base
constructor.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="urllib.request.FancyURLopener">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.request.</span></span><span class="sig-name descname"><span class="pre">FancyURLopener</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">...</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.FancyURLopener" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.3.</span></p>
</div>
<p><a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a> subclasses <a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a> providing default handling
for the following HTTP response codes: 301, 302, 303, 307 and 401.  For the 30x
response codes listed above, the <em class="mailheader">Location</em> header is used to fetch
the actual URL.  For 401 response codes (authentication required), basic HTTP
authentication is performed.  For the 30x response codes, recursion is bounded
by the value of the <em>maxtries</em> attribute, which defaults to 10.</p>
<p>For all other response codes, the method <a class="reference internal" href="#urllib.request.BaseHandler.http_error_default" title="urllib.request.BaseHandler.http_error_default"><code class="xref py py-meth docutils literal notranslate"><span class="pre">http_error_default()</span></code></a> is called
which you can override in subclasses to handle the error appropriately.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>According to the letter of <span class="target" id="index-8"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc2616.html"><strong>RFC 2616</strong></a>, 301 and 302 responses to POST requests
must not be automatically redirected without confirmation by the user.  In
reality, browsers do allow automatic redirection of these responses, changing
the POST to a GET, and <a class="reference internal" href="urllib.html#module-urllib" title="urllib"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code></a> reproduces this behaviour.</p>
</div>
<p>The parameters to the constructor are the same as those for <a class="reference internal" href="#urllib.request.URLopener" title="urllib.request.URLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">URLopener</span></code></a>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When performing basic authentication, a <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a> instance calls
its <a class="reference internal" href="#urllib.request.FancyURLopener.prompt_user_passwd" title="urllib.request.FancyURLopener.prompt_user_passwd"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prompt_user_passwd()</span></code></a> method.  The default implementation asks the
users for the required information on the controlling terminal.  A subclass may
override this method to support more appropriate behavior if needed.</p>
</div>
<p>The <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a> class offers one additional method that should be
overloaded to provide the appropriate behavior:</p>
<dl class="py method">
<dt class="sig sig-object py" id="urllib.request.FancyURLopener.prompt_user_passwd">
<span class="sig-name descname"><span class="pre">prompt_user_passwd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">host</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">realm</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#urllib.request.FancyURLopener.prompt_user_passwd" title="Link to this definition">¶</a></dt>
<dd><p>Return information needed to authenticate the user at the given host in the
specified security realm.  The return value should be a tuple, <code class="docutils literal notranslate"><span class="pre">(user,</span>
<span class="pre">password)</span></code>, which can be used for basic authentication.</p>
<p>The implementation prompts for this information on the terminal; an application
should override this method to use an appropriate interaction model in the local
environment.</p>
</dd></dl>

</dd></dl>

</section>
<section id="urllib-request-restrictions">
<h2><a class="reference internal" href="#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> Restrictions<a class="headerlink" href="#urllib-request-restrictions" title="Link to this heading">¶</a></h2>
<ul id="index-9">
<li><p>Currently, only the following protocols are supported: HTTP (versions 0.9 and
1.0), FTP, local files, and data URLs.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for data URLs.</p>
</div>
</li>
<li><p>The caching feature of <a class="reference internal" href="#urllib.request.urlretrieve" title="urllib.request.urlretrieve"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlretrieve()</span></code></a> has been disabled until someone
finds the time to hack proper processing of Expiration time headers.</p></li>
<li><p>There should be a function to query whether a particular URL is in the cache.</p></li>
<li><p>For backward compatibility, if a URL appears to point to a local file but the
file can’t be opened, the URL is re-interpreted using the FTP protocol.  This
can sometimes cause confusing error messages.</p></li>
<li><p>The <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a> and <a class="reference internal" href="#urllib.request.urlretrieve" title="urllib.request.urlretrieve"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlretrieve()</span></code></a> functions can cause arbitrarily
long delays while waiting for a network connection to be set up.  This means
that it is difficult to build an interactive web client using these functions
without using threads.</p>
</li>
<li id="index-10"><p>The data returned by <a class="reference internal" href="#urllib.request.urlopen" title="urllib.request.urlopen"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlopen()</span></code></a> or <a class="reference internal" href="#urllib.request.urlretrieve" title="urllib.request.urlretrieve"><code class="xref py py-func docutils literal notranslate"><span class="pre">urlretrieve()</span></code></a> is the raw data
returned by the server.  This may be binary data (such as an image), plain text
or (for example) HTML.  The HTTP protocol provides type information in the reply
header, which can be inspected by looking at the <em class="mailheader">Content-Type</em>
header.  If the returned data is HTML, you can use the module
<a class="reference internal" href="html.parser.html#module-html.parser" title="html.parser: A simple parser that can handle HTML and XHTML."><code class="xref py py-mod docutils literal notranslate"><span class="pre">html.parser</span></code></a> to parse it.</p>
</li>
<li id="index-11"><p>The code handling the FTP protocol cannot differentiate between a file and a
directory.  This can lead to unexpected behavior when attempting to read a URL
that points to a file that is not accessible.  If the URL ends in a <code class="docutils literal notranslate"><span class="pre">/</span></code>, it is
assumed to refer to a directory and will be handled accordingly.  But if an
attempt to read a file leads to a 550 error (meaning the URL cannot be found or
is not accessible, often for permission reasons), then the path is treated as a
directory in order to handle the case when a directory is specified by a URL but
the trailing <code class="docutils literal notranslate"><span class="pre">/</span></code> has been left off.  This can cause misleading results when
you try to fetch a file whose read permissions make it inaccessible; the FTP
code will try to read it, fail with a 550 error, and then perform a directory
listing for the unreadable file. If fine-grained control is needed, consider
using the <a class="reference internal" href="ftplib.html#module-ftplib" title="ftplib: FTP protocol client (requires sockets)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">ftplib</span></code></a> module, subclassing <a class="reference internal" href="#urllib.request.FancyURLopener" title="urllib.request.FancyURLopener"><code class="xref py py-class docutils literal notranslate"><span class="pre">FancyURLopener</span></code></a>, or changing
<em>_urlopener</em> to meet your needs.</p></li>
</ul>
</section>
</section>
<section id="module-urllib.response">
<span id="urllib-response-response-classes-used-by-urllib"></span><h1><a class="reference internal" href="#module-urllib.response" title="urllib.response: Response classes used by urllib."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.response</span></code></a> — Response classes used by urllib<a class="headerlink" href="#module-urllib.response" title="Link to this heading">¶</a></h1>
<p>The <a class="reference internal" href="#module-urllib.response" title="urllib.response: Response classes used by urllib."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.response</span></code></a> module defines functions and classes which define a
minimal file-like interface, including <code class="docutils literal notranslate"><span class="pre">read()</span></code> and <code class="docutils literal notranslate"><span class="pre">readline()</span></code>.
Functions defined by this module are used internally by the <a class="reference internal" href="#module-urllib.request" title="urllib.request: Extensible library for opening URLs."><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code></a> module.
The typical response object is a <a class="reference internal" href="#urllib.response.addinfourl" title="urllib.response.addinfourl"><code class="xref py py-class docutils literal notranslate"><span class="pre">urllib.response.addinfourl</span></code></a> instance:</p>
<dl class="py class">
<dt class="sig sig-object py" id="urllib.response.addinfourl">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">urllib.response.</span></span><span class="sig-name descname"><span class="pre">addinfourl</span></span><a class="headerlink" href="#urllib.response.addinfourl" title="Link to this definition">¶</a></dt>
<dd><dl class="py attribute">
<dt class="sig sig-object py" id="urllib.response.addinfourl.url">
<span class="sig-name descname"><span class="pre">url</span></span><a class="headerlink" href="#urllib.response.addinfourl.url" title="Link to this definition">¶</a></dt>
<dd><p>URL of the resource retrieved, commonly used to determine if a redirect was followed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.response.addinfourl.headers">
<span class="sig-name descname"><span class="pre">headers</span></span><a class="headerlink" href="#urllib.response.addinfourl.headers" title="Link to this definition">¶</a></dt>
<dd><p>Returns the headers of the response in the form of an <a class="reference internal" href="email.message.html#email.message.EmailMessage" title="email.message.EmailMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">EmailMessage</span></code></a> instance.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.response.addinfourl.status">
<span class="sig-name descname"><span class="pre">status</span></span><a class="headerlink" href="#urllib.response.addinfourl.status" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Status code returned by server.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.response.addinfourl.geturl">
<span class="sig-name descname"><span class="pre">geturl</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.response.addinfourl.geturl" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#urllib.response.addinfourl.url" title="urllib.response.addinfourl.url"><code class="xref py py-attr docutils literal notranslate"><span class="pre">url</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.response.addinfourl.info">
<span class="sig-name descname"><span class="pre">info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.response.addinfourl.info" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#urllib.response.addinfourl.headers" title="urllib.response.addinfourl.headers"><code class="xref py py-attr docutils literal notranslate"><span class="pre">headers</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="urllib.response.addinfourl.code">
<span class="sig-name descname"><span class="pre">code</span></span><a class="headerlink" href="#urllib.response.addinfourl.code" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#urllib.response.addinfourl.status" title="urllib.response.addinfourl.status"><code class="xref py py-attr docutils literal notranslate"><span class="pre">status</span></code></a>.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="urllib.response.addinfourl.getcode">
<span class="sig-name descname"><span class="pre">getcode</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#urllib.response.addinfourl.getcode" title="Link to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.9: </span>Deprecated in favor of <a class="reference internal" href="#urllib.response.addinfourl.status" title="urllib.response.addinfourl.status"><code class="xref py py-attr docutils literal notranslate"><span class="pre">status</span></code></a>.</p>
</div>
</dd></dl>

</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> — Extensible library for opening URLs</a><ul>
<li><a class="reference internal" href="#request-objects">Request Objects</a></li>
<li><a class="reference internal" href="#openerdirector-objects">OpenerDirector Objects</a></li>
<li><a class="reference internal" href="#basehandler-objects">BaseHandler Objects</a></li>
<li><a class="reference internal" href="#httpredirecthandler-objects">HTTPRedirectHandler Objects</a></li>
<li><a class="reference internal" href="#httpcookieprocessor-objects">HTTPCookieProcessor Objects</a></li>
<li><a class="reference internal" href="#proxyhandler-objects">ProxyHandler Objects</a></li>
<li><a class="reference internal" href="#httppasswordmgr-objects">HTTPPasswordMgr Objects</a></li>
<li><a class="reference internal" href="#httppasswordmgrwithpriorauth-objects">HTTPPasswordMgrWithPriorAuth Objects</a></li>
<li><a class="reference internal" href="#abstractbasicauthhandler-objects">AbstractBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httpbasicauthhandler-objects">HTTPBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#proxybasicauthhandler-objects">ProxyBasicAuthHandler Objects</a></li>
<li><a class="reference internal" href="#abstractdigestauthhandler-objects">AbstractDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httpdigestauthhandler-objects">HTTPDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#proxydigestauthhandler-objects">ProxyDigestAuthHandler Objects</a></li>
<li><a class="reference internal" href="#httphandler-objects">HTTPHandler Objects</a></li>
<li><a class="reference internal" href="#httpshandler-objects">HTTPSHandler Objects</a></li>
<li><a class="reference internal" href="#filehandler-objects">FileHandler Objects</a></li>
<li><a class="reference internal" href="#datahandler-objects">DataHandler Objects</a></li>
<li><a class="reference internal" href="#ftphandler-objects">FTPHandler Objects</a></li>
<li><a class="reference internal" href="#cacheftphandler-objects">CacheFTPHandler Objects</a></li>
<li><a class="reference internal" href="#unknownhandler-objects">UnknownHandler Objects</a></li>
<li><a class="reference internal" href="#httperrorprocessor-objects">HTTPErrorProcessor Objects</a></li>
<li><a class="reference internal" href="#examples">Examples</a></li>
<li><a class="reference internal" href="#legacy-interface">Legacy interface</a></li>
<li><a class="reference internal" href="#urllib-request-restrictions"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> Restrictions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-urllib.response"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.response</span></code> — Response classes used by urllib</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="urllib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib</span></code> — URL handling modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="urllib.parse.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.parse</span></code> — Parse URLs into components</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/urllib.request.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="urllib.parse.html" title="urllib.parse — Parse URLs into components"
             >next</a> |</li>
        <li class="right" >
          <a href="urllib.html" title="urllib — URL handling modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">urllib.request</span></code> — Extensible library for opening URLs</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>