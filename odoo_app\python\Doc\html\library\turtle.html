<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="turtle — Turtle graphics" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/turtle.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/turtle.py Introduction: Turtle graphics is an implementation of the popular geometric drawing tools introduced in Logo, developed by <PERSON>, <PERSON> and <PERSON>..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/turtle.py Introduction: Turtle graphics is an implementation of the popular geometric drawing tools introduced in Logo, developed by <PERSON>, <PERSON> and <PERSON>..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>turtle — Turtle graphics &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="cmd — Support for line-oriented command interpreters" href="cmd.html" />
    <link rel="prev" title="Program Frameworks" href="frameworks.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/turtle.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code> — Turtle graphics</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#starting-a-turtle-environment">Starting a turtle environment</a></li>
<li><a class="reference internal" href="#basic-drawing">Basic drawing</a><ul>
<li><a class="reference internal" href="#pen-control">Pen control</a></li>
<li><a class="reference internal" href="#the-turtle-s-position">The turtle’s position</a></li>
</ul>
</li>
<li><a class="reference internal" href="#making-algorithmic-patterns">Making algorithmic patterns</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to">How to…</a><ul>
<li><a class="reference internal" href="#get-started-as-quickly-as-possible">Get started as quickly as possible</a></li>
<li><a class="reference internal" href="#use-the-turtle-module-namespace">Use the <code class="docutils literal notranslate"><span class="pre">turtle</span></code> module namespace</a></li>
<li><a class="reference internal" href="#use-turtle-graphics-in-a-script">Use turtle graphics in a script</a></li>
<li><a class="reference internal" href="#use-object-oriented-turtle-graphics">Use object-oriented turtle graphics</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turtle-graphics-reference">Turtle graphics reference</a><ul>
<li><a class="reference internal" href="#turtle-methods">Turtle methods</a></li>
<li><a class="reference internal" href="#methods-of-turtlescreen-screen">Methods of TurtleScreen/Screen</a></li>
</ul>
</li>
<li><a class="reference internal" href="#methods-of-rawturtle-turtle-and-corresponding-functions">Methods of RawTurtle/Turtle and corresponding functions</a><ul>
<li><a class="reference internal" href="#turtle-motion">Turtle motion</a></li>
<li><a class="reference internal" href="#tell-turtle-s-state">Tell Turtle’s state</a></li>
<li><a class="reference internal" href="#settings-for-measurement">Settings for measurement</a></li>
<li><a class="reference internal" href="#id1">Pen control</a><ul>
<li><a class="reference internal" href="#drawing-state">Drawing state</a></li>
<li><a class="reference internal" href="#color-control">Color control</a></li>
<li><a class="reference internal" href="#filling">Filling</a></li>
<li><a class="reference internal" href="#more-drawing-control">More drawing control</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turtle-state">Turtle state</a><ul>
<li><a class="reference internal" href="#visibility">Visibility</a></li>
<li><a class="reference internal" href="#appearance">Appearance</a></li>
</ul>
</li>
<li><a class="reference internal" href="#using-events">Using events</a></li>
<li><a class="reference internal" href="#special-turtle-methods">Special Turtle methods</a></li>
<li><a class="reference internal" href="#compound-shapes">Compound shapes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#methods-of-turtlescreen-screen-and-corresponding-functions">Methods of TurtleScreen/Screen and corresponding functions</a><ul>
<li><a class="reference internal" href="#window-control">Window control</a></li>
<li><a class="reference internal" href="#animation-control">Animation control</a></li>
<li><a class="reference internal" href="#using-screen-events">Using screen events</a></li>
<li><a class="reference internal" href="#input-methods">Input methods</a></li>
<li><a class="reference internal" href="#settings-and-special-methods">Settings and special methods</a></li>
<li><a class="reference internal" href="#methods-specific-to-screen-not-inherited-from-turtlescreen">Methods specific to Screen, not inherited from TurtleScreen</a></li>
</ul>
</li>
<li><a class="reference internal" href="#public-classes">Public classes</a></li>
<li><a class="reference internal" href="#explanation">Explanation</a></li>
<li><a class="reference internal" href="#help-and-configuration">Help and configuration</a><ul>
<li><a class="reference internal" href="#how-to-use-help">How to use help</a></li>
<li><a class="reference internal" href="#translation-of-docstrings-into-different-languages">Translation of docstrings into different languages</a></li>
<li><a class="reference internal" href="#how-to-configure-screen-and-turtles">How to configure Screen and Turtles</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-turtledemo"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtledemo</span></code> — Demo scripts</a></li>
<li><a class="reference internal" href="#changes-since-python-2-6">Changes since Python 2.6</a></li>
<li><a class="reference internal" href="#changes-since-python-3-0">Changes since Python 3.0</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="frameworks.html"
                          title="previous chapter">Program Frameworks</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmd.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmd</span></code> — Support for line-oriented command interpreters</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/turtle.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmd.html" title="cmd — Support for line-oriented command interpreters"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="frameworks.html" title="Program Frameworks"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="frameworks.html" accesskey="U">Program Frameworks</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code> — Turtle graphics</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-turtle">
<span id="turtle-turtle-graphics"></span><h1><a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a> — Turtle graphics<a class="headerlink" href="#module-turtle" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/turtle.py">Lib/turtle.py</a></p>
<hr class="docutils" />
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Link to this heading">¶</a></h2>
<p>Turtle graphics is an implementation of <a class="reference external" href="https://en.wikipedia.org/wiki/Turtle_(robot)">the popular geometric drawing tools
introduced in Logo</a>, developed by Wally Feurzeig, Seymour Papert and Cynthia Solomon
in 1967.</p>
<aside class="sidebar">
<p class="sidebar-title">Turtle star</p>
<p>Turtle can draw intricate shapes using programs that repeat simple
moves.</p>
<img alt="../_images/turtle-star.png" class="align-center" src="../_images/turtle-star.png" />
</aside>
<p>In Python, turtle graphics provides a representation of a physical “turtle”
(a little robot with a pen) that draws on a sheet of paper on the floor.</p>
<p>It’s an effective and well-proven way for learners to encounter
programming concepts and interaction with software, as it provides instant,
visible feedback. It also provides convenient access to graphical output
in general.</p>
<p>Turtle drawing was originally created as an educational tool, to be used by
teachers in the classroom. For the programmer who needs to produce some
graphical output it can be a way to do that without the overhead of
introducing more complex or external libraries into their work.</p>
</section>
<section id="tutorial">
<span id="turtle-tutorial"></span><h2>Tutorial<a class="headerlink" href="#tutorial" title="Link to this heading">¶</a></h2>
<p>New users should start here. In this tutorial we’ll explore some of the
basics of turtle drawing.</p>
<section id="starting-a-turtle-environment">
<h3>Starting a turtle environment<a class="headerlink" href="#starting-a-turtle-environment" title="Link to this heading">¶</a></h3>
<p>In a Python shell, import all the objects of the <code class="docutils literal notranslate"><span class="pre">turtle</span></code> module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">turtle</span> <span class="kn">import</span> <span class="o">*</span>
</pre></div>
</div>
<p>If you run into a <code class="docutils literal notranslate"><span class="pre">No</span> <span class="pre">module</span> <span class="pre">named</span> <span class="pre">'_tkinter'</span></code> error, you’ll have to
install the <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">Tk</span> <span class="pre">interface</span> <span class="pre">package</span></code></a> on your system.</p>
</section>
<section id="basic-drawing">
<h3>Basic drawing<a class="headerlink" href="#basic-drawing" title="Link to this heading">¶</a></h3>
<p>Send the turtle forward 100 steps:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">forward</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
</pre></div>
</div>
<p>You should see (most likely, in a new window on your display) a line
drawn by the turtle, heading East. Change the direction of the turtle,
so that it turns 120 degrees left (anti-clockwise):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">left</span><span class="p">(</span><span class="mi">120</span><span class="p">)</span>
</pre></div>
</div>
<p>Let’s continue by drawing a triangle:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">forward</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="n">left</span><span class="p">(</span><span class="mi">120</span><span class="p">)</span>
<span class="n">forward</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
</pre></div>
</div>
<p>Notice how the turtle, represented by an arrow, points in different
directions as you steer it.</p>
<p>Experiment with those commands, and also with <code class="docutils literal notranslate"><span class="pre">backward()</span></code> and
<code class="docutils literal notranslate"><span class="pre">right()</span></code>.</p>
<section id="pen-control">
<h4>Pen control<a class="headerlink" href="#pen-control" title="Link to this heading">¶</a></h4>
<p>Try changing the color - for example, <code class="docutils literal notranslate"><span class="pre">color('blue')</span></code> - and
width of the line - for example, <code class="docutils literal notranslate"><span class="pre">width(3)</span></code> - and then drawing again.</p>
<p>You can also move the turtle around without drawing, by lifting up the pen:
<code class="docutils literal notranslate"><span class="pre">up()</span></code> before moving. To start drawing again, use <code class="docutils literal notranslate"><span class="pre">down()</span></code>.</p>
</section>
<section id="the-turtle-s-position">
<h4>The turtle’s position<a class="headerlink" href="#the-turtle-s-position" title="Link to this heading">¶</a></h4>
<p>Send your turtle back to its starting-point (useful if it has disappeared
off-screen):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">home</span><span class="p">()</span>
</pre></div>
</div>
<p>The home position is at the center of the turtle’s screen. If you ever need to
know them, get the turtle’s x-y co-ordinates with:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">pos</span><span class="p">()</span>
</pre></div>
</div>
<p>Home is at <code class="docutils literal notranslate"><span class="pre">(0,</span> <span class="pre">0)</span></code>.</p>
<p>And after a while, it will probably help to clear the window so we can start
anew:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">clearscreen</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="making-algorithmic-patterns">
<h3>Making algorithmic patterns<a class="headerlink" href="#making-algorithmic-patterns" title="Link to this heading">¶</a></h3>
<p>Using loops, it’s possible to build up geometric patterns:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">steps</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">100</span><span class="p">):</span>
    <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="p">(</span><span class="s1">&#39;blue&#39;</span><span class="p">,</span> <span class="s1">&#39;red&#39;</span><span class="p">,</span> <span class="s1">&#39;green&#39;</span><span class="p">):</span>
        <span class="n">color</span><span class="p">(</span><span class="n">c</span><span class="p">)</span>
        <span class="n">forward</span><span class="p">(</span><span class="n">steps</span><span class="p">)</span>
        <span class="n">right</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
</pre></div>
</div>
<p>- which of course, are limited only by the imagination!</p>
<p>Let’s draw the star shape at the top of this page. We want red lines,
filled in with yellow:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">color</span><span class="p">(</span><span class="s1">&#39;red&#39;</span><span class="p">)</span>
<span class="n">fillcolor</span><span class="p">(</span><span class="s1">&#39;yellow&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>Just as <code class="docutils literal notranslate"><span class="pre">up()</span></code> and <code class="docutils literal notranslate"><span class="pre">down()</span></code> determine whether lines will be drawn,
filling can be turned on and off:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">begin_fill</span><span class="p">()</span>
</pre></div>
</div>
<p>Next we’ll create a loop:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">forward</span><span class="p">(</span><span class="mi">200</span><span class="p">)</span>
    <span class="n">left</span><span class="p">(</span><span class="mi">170</span><span class="p">)</span>
    <span class="k">if</span> <span class="nb">abs</span><span class="p">(</span><span class="n">pos</span><span class="p">())</span> <span class="o">&lt;</span> <span class="mi">1</span><span class="p">:</span>
        <span class="k">break</span>
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">abs(pos())</span> <span class="pre">&lt;</span> <span class="pre">1</span></code> is a good way to know when the turtle is back at its
home position.</p>
<p>Finally, complete the filling:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">end_fill</span><span class="p">()</span>
</pre></div>
</div>
<p>(Note that filling only actually takes place when you give the
<code class="docutils literal notranslate"><span class="pre">end_fill()</span></code> command.)</p>
</section>
</section>
<section id="how-to">
<span id="turtle-how-to"></span><h2>How to…<a class="headerlink" href="#how-to" title="Link to this heading">¶</a></h2>
<p>This section covers some typical turtle use-cases and approaches.</p>
<section id="get-started-as-quickly-as-possible">
<h3>Get started as quickly as possible<a class="headerlink" href="#get-started-as-quickly-as-possible" title="Link to this heading">¶</a></h3>
<p>One of the joys of turtle graphics is the immediate, visual feedback that’s
available from simple commands - it’s an excellent way to introduce children
to programming ideas, with a minimum of overhead (not just children, of
course).</p>
<p>The turtle module makes this possible by exposing all its basic functionality
as functions, available with <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">turtle</span> <span class="pre">import</span> <span class="pre">*</span></code>. The <a class="reference internal" href="#turtle-tutorial"><span class="std std-ref">turtle
graphics tutorial</span></a> covers this approach.</p>
<p>It’s worth noting that many of the turtle commands also have even more terse
equivalents, such as <code class="docutils literal notranslate"><span class="pre">fd()</span></code> for <a class="reference internal" href="#turtle.forward" title="turtle.forward"><code class="xref py py-func docutils literal notranslate"><span class="pre">forward()</span></code></a>. These are especially
useful when working with learners for whom typing is not a skill.</p>
<blockquote id="note">
<div><p>You’ll need to have the <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal notranslate"><span class="pre">Tk</span> <span class="pre">interface</span> <span class="pre">package</span></code></a> installed on
your system for turtle graphics to work. Be warned that this is not
always straightforward, so check this in advance if you’re planning to
use turtle graphics with a learner.</p>
</div></blockquote>
</section>
<section id="use-the-turtle-module-namespace">
<h3>Use the <code class="docutils literal notranslate"><span class="pre">turtle</span></code> module namespace<a class="headerlink" href="#use-the-turtle-module-namespace" title="Link to this heading">¶</a></h3>
<p>Using <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">turtle</span> <span class="pre">import</span> <span class="pre">*</span></code> is convenient - but be warned that it imports a
rather large collection of objects, and if you’re doing anything but turtle
graphics you run the risk of a name conflict (this becomes even more an issue
if you’re using turtle graphics in a script where other modules might be
imported).</p>
<p>The solution is to use <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">turtle</span></code> - <code class="docutils literal notranslate"><span class="pre">fd()</span></code> becomes
<code class="docutils literal notranslate"><span class="pre">turtle.fd()</span></code>, <code class="docutils literal notranslate"><span class="pre">width()</span></code> becomes <code class="docutils literal notranslate"><span class="pre">turtle.width()</span></code> and so on. (If typing
“turtle” over and over again becomes tedious, use for example <code class="docutils literal notranslate"><span class="pre">import</span> <span class="pre">turtle</span>
<span class="pre">as</span> <span class="pre">t</span></code> instead.)</p>
</section>
<section id="use-turtle-graphics-in-a-script">
<h3>Use turtle graphics in a script<a class="headerlink" href="#use-turtle-graphics-in-a-script" title="Link to this heading">¶</a></h3>
<p>It’s recommended to use the <code class="docutils literal notranslate"><span class="pre">turtle</span></code> module namespace as described
immediately above, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">turtle</span> <span class="k">as</span> <span class="nn">t</span>
<span class="kn">from</span> <span class="nn">random</span> <span class="kn">import</span> <span class="n">random</span>

<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">100</span><span class="p">):</span>
    <span class="n">steps</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">random</span><span class="p">()</span> <span class="o">*</span> <span class="mi">100</span><span class="p">)</span>
    <span class="n">angle</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">random</span><span class="p">()</span> <span class="o">*</span> <span class="mi">360</span><span class="p">)</span>
    <span class="n">t</span><span class="o">.</span><span class="n">right</span><span class="p">(</span><span class="n">angle</span><span class="p">)</span>
    <span class="n">t</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="n">steps</span><span class="p">)</span>
</pre></div>
</div>
<p>Another step is also required though - as soon as the script ends, Python
will also close the turtle’s window. Add:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">t</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
<p>to the end of the script. The script will now wait to be dismissed and
will not exit until it is terminated, for example by closing the turtle
graphics window.</p>
</section>
<section id="use-object-oriented-turtle-graphics">
<h3>Use object-oriented turtle graphics<a class="headerlink" href="#use-object-oriented-turtle-graphics" title="Link to this heading">¶</a></h3>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#turtle-explanation"><span class="std std-ref">Explanation of the object-oriented interface</span></a></p>
</div>
<p>Other than for very basic introductory purposes, or for trying things out
as quickly as possible, it’s more usual and much more powerful to use the
object-oriented approach to turtle graphics. For example, this allows
multiple turtles on screen at once.</p>
<p>In this approach, the various turtle commands are methods of objects (mostly of
<code class="docutils literal notranslate"><span class="pre">Turtle</span></code> objects). You <em>can</em> use the object-oriented approach in the shell,
but it would be more typical in a Python script.</p>
<p>The example above then becomes:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">turtle</span> <span class="kn">import</span> <span class="n">Turtle</span>
<span class="kn">from</span> <span class="nn">random</span> <span class="kn">import</span> <span class="n">random</span>

<span class="n">t</span> <span class="o">=</span> <span class="n">Turtle</span><span class="p">()</span>
<span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">100</span><span class="p">):</span>
    <span class="n">steps</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">random</span><span class="p">()</span> <span class="o">*</span> <span class="mi">100</span><span class="p">)</span>
    <span class="n">angle</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">random</span><span class="p">()</span> <span class="o">*</span> <span class="mi">360</span><span class="p">)</span>
    <span class="n">t</span><span class="o">.</span><span class="n">right</span><span class="p">(</span><span class="n">angle</span><span class="p">)</span>
    <span class="n">t</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="n">steps</span><span class="p">)</span>

<span class="n">t</span><span class="o">.</span><span class="n">screen</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
<p>Note the last line. <code class="docutils literal notranslate"><span class="pre">t.screen</span></code> is an instance of the <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a>
that a Turtle instance exists on; it’s created automatically along with
the turtle.</p>
<p>The turtle’s screen can be customised, for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">t</span><span class="o">.</span><span class="n">screen</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="s1">&#39;Object-oriented turtle demo&#39;</span><span class="p">)</span>
<span class="n">t</span><span class="o">.</span><span class="n">screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">(</span><span class="s2">&quot;orange&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="turtle-graphics-reference">
<h2>Turtle graphics reference<a class="headerlink" href="#turtle-graphics-reference" title="Link to this heading">¶</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In the following documentation the argument list for functions is given.
Methods, of course, have the additional first argument <em>self</em> which is
omitted here.</p>
</div>
<section id="turtle-methods">
<h3>Turtle methods<a class="headerlink" href="#turtle-methods" title="Link to this heading">¶</a></h3>
<dl>
<dt>Turtle motion</dt><dd><dl>
<dt>Move and draw</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.forward" title="turtle.forward"><code class="xref py py-func docutils literal notranslate"><span class="pre">forward()</span></code></a> | <a class="reference internal" href="#turtle.fd" title="turtle.fd"><code class="xref py py-func docutils literal notranslate"><span class="pre">fd()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.backward" title="turtle.backward"><code class="xref py py-func docutils literal notranslate"><span class="pre">backward()</span></code></a> | <a class="reference internal" href="#turtle.bk" title="turtle.bk"><code class="xref py py-func docutils literal notranslate"><span class="pre">bk()</span></code></a> | <a class="reference internal" href="#turtle.back" title="turtle.back"><code class="xref py py-func docutils literal notranslate"><span class="pre">back()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.right" title="turtle.right"><code class="xref py py-func docutils literal notranslate"><span class="pre">right()</span></code></a> | <a class="reference internal" href="#turtle.rt" title="turtle.rt"><code class="xref py py-func docutils literal notranslate"><span class="pre">rt()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.left" title="turtle.left"><code class="xref py py-func docutils literal notranslate"><span class="pre">left()</span></code></a> | <a class="reference internal" href="#turtle.lt" title="turtle.lt"><code class="xref py py-func docutils literal notranslate"><span class="pre">lt()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.goto" title="turtle.goto"><code class="xref py py-func docutils literal notranslate"><span class="pre">goto()</span></code></a> | <a class="reference internal" href="#turtle.setpos" title="turtle.setpos"><code class="xref py py-func docutils literal notranslate"><span class="pre">setpos()</span></code></a> | <a class="reference internal" href="#turtle.setposition" title="turtle.setposition"><code class="xref py py-func docutils literal notranslate"><span class="pre">setposition()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.teleport" title="turtle.teleport"><code class="xref py py-func docutils literal notranslate"><span class="pre">teleport()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.setx" title="turtle.setx"><code class="xref py py-func docutils literal notranslate"><span class="pre">setx()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.sety" title="turtle.sety"><code class="xref py py-func docutils literal notranslate"><span class="pre">sety()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.setheading" title="turtle.setheading"><code class="xref py py-func docutils literal notranslate"><span class="pre">setheading()</span></code></a> | <a class="reference internal" href="#turtle.seth" title="turtle.seth"><code class="xref py py-func docutils literal notranslate"><span class="pre">seth()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.home" title="turtle.home"><code class="xref py py-func docutils literal notranslate"><span class="pre">home()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.circle" title="turtle.circle"><code class="xref py py-func docutils literal notranslate"><span class="pre">circle()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.dot" title="turtle.dot"><code class="xref py py-func docutils literal notranslate"><span class="pre">dot()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.stamp" title="turtle.stamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">stamp()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.clearstamp" title="turtle.clearstamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">clearstamp()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.clearstamps" title="turtle.clearstamps"><code class="xref py py-func docutils literal notranslate"><span class="pre">clearstamps()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.undo" title="turtle.undo"><code class="xref py py-func docutils literal notranslate"><span class="pre">undo()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.speed" title="turtle.speed"><code class="xref py py-func docutils literal notranslate"><span class="pre">speed()</span></code></a></div>
</div>
</dd>
<dt>Tell Turtle’s state</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.position" title="turtle.position"><code class="xref py py-func docutils literal notranslate"><span class="pre">position()</span></code></a> | <a class="reference internal" href="#turtle.pos" title="turtle.pos"><code class="xref py py-func docutils literal notranslate"><span class="pre">pos()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.towards" title="turtle.towards"><code class="xref py py-func docutils literal notranslate"><span class="pre">towards()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.xcor" title="turtle.xcor"><code class="xref py py-func docutils literal notranslate"><span class="pre">xcor()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.ycor" title="turtle.ycor"><code class="xref py py-func docutils literal notranslate"><span class="pre">ycor()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.heading" title="turtle.heading"><code class="xref py py-func docutils literal notranslate"><span class="pre">heading()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.distance" title="turtle.distance"><code class="xref py py-func docutils literal notranslate"><span class="pre">distance()</span></code></a></div>
</div>
</dd>
<dt>Setting and measurement</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.degrees" title="turtle.degrees"><code class="xref py py-func docutils literal notranslate"><span class="pre">degrees()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.radians" title="turtle.radians"><code class="xref py py-func docutils literal notranslate"><span class="pre">radians()</span></code></a></div>
</div>
</dd>
</dl>
</dd>
<dt>Pen control</dt><dd><dl>
<dt>Drawing state</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.pendown" title="turtle.pendown"><code class="xref py py-func docutils literal notranslate"><span class="pre">pendown()</span></code></a> | <a class="reference internal" href="#turtle.pd" title="turtle.pd"><code class="xref py py-func docutils literal notranslate"><span class="pre">pd()</span></code></a> | <a class="reference internal" href="#turtle.down" title="turtle.down"><code class="xref py py-func docutils literal notranslate"><span class="pre">down()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.penup" title="turtle.penup"><code class="xref py py-func docutils literal notranslate"><span class="pre">penup()</span></code></a> | <a class="reference internal" href="#turtle.pu" title="turtle.pu"><code class="xref py py-func docutils literal notranslate"><span class="pre">pu()</span></code></a> | <a class="reference internal" href="#turtle.up" title="turtle.up"><code class="xref py py-func docutils literal notranslate"><span class="pre">up()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.pensize" title="turtle.pensize"><code class="xref py py-func docutils literal notranslate"><span class="pre">pensize()</span></code></a> | <a class="reference internal" href="#turtle.width" title="turtle.width"><code class="xref py py-func docutils literal notranslate"><span class="pre">width()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.pen" title="turtle.pen"><code class="xref py py-func docutils literal notranslate"><span class="pre">pen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.isdown" title="turtle.isdown"><code class="xref py py-func docutils literal notranslate"><span class="pre">isdown()</span></code></a></div>
</div>
</dd>
<dt>Color control</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.color" title="turtle.color"><code class="xref py py-func docutils literal notranslate"><span class="pre">color()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.pencolor" title="turtle.pencolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">pencolor()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.fillcolor" title="turtle.fillcolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">fillcolor()</span></code></a></div>
</div>
</dd>
<dt>Filling</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.filling" title="turtle.filling"><code class="xref py py-func docutils literal notranslate"><span class="pre">filling()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.begin_fill" title="turtle.begin_fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">begin_fill()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.end_fill" title="turtle.end_fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">end_fill()</span></code></a></div>
</div>
</dd>
<dt>More drawing control</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.reset" title="turtle.reset"><code class="xref py py-func docutils literal notranslate"><span class="pre">reset()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.clear" title="turtle.clear"><code class="xref py py-func docutils literal notranslate"><span class="pre">clear()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.write" title="turtle.write"><code class="xref py py-func docutils literal notranslate"><span class="pre">write()</span></code></a></div>
</div>
</dd>
</dl>
</dd>
<dt>Turtle state</dt><dd><dl>
<dt>Visibility</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.showturtle" title="turtle.showturtle"><code class="xref py py-func docutils literal notranslate"><span class="pre">showturtle()</span></code></a> | <a class="reference internal" href="#turtle.st" title="turtle.st"><code class="xref py py-func docutils literal notranslate"><span class="pre">st()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.hideturtle" title="turtle.hideturtle"><code class="xref py py-func docutils literal notranslate"><span class="pre">hideturtle()</span></code></a> | <a class="reference internal" href="#turtle.ht" title="turtle.ht"><code class="xref py py-func docutils literal notranslate"><span class="pre">ht()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.isvisible" title="turtle.isvisible"><code class="xref py py-func docutils literal notranslate"><span class="pre">isvisible()</span></code></a></div>
</div>
</dd>
<dt>Appearance</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.shape" title="turtle.shape"><code class="xref py py-func docutils literal notranslate"><span class="pre">shape()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.resizemode" title="turtle.resizemode"><code class="xref py py-func docutils literal notranslate"><span class="pre">resizemode()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.shapesize" title="turtle.shapesize"><code class="xref py py-func docutils literal notranslate"><span class="pre">shapesize()</span></code></a> | <a class="reference internal" href="#turtle.turtlesize" title="turtle.turtlesize"><code class="xref py py-func docutils literal notranslate"><span class="pre">turtlesize()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.shearfactor" title="turtle.shearfactor"><code class="xref py py-func docutils literal notranslate"><span class="pre">shearfactor()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.settiltangle" title="turtle.settiltangle"><code class="xref py py-func docutils literal notranslate"><span class="pre">settiltangle()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.tiltangle" title="turtle.tiltangle"><code class="xref py py-func docutils literal notranslate"><span class="pre">tiltangle()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.tilt" title="turtle.tilt"><code class="xref py py-func docutils literal notranslate"><span class="pre">tilt()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.shapetransform" title="turtle.shapetransform"><code class="xref py py-func docutils literal notranslate"><span class="pre">shapetransform()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.get_shapepoly" title="turtle.get_shapepoly"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_shapepoly()</span></code></a></div>
</div>
</dd>
</dl>
</dd>
<dt>Using events</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.onclick" title="turtle.onclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">onclick()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.onrelease" title="turtle.onrelease"><code class="xref py py-func docutils literal notranslate"><span class="pre">onrelease()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.ondrag" title="turtle.ondrag"><code class="xref py py-func docutils literal notranslate"><span class="pre">ondrag()</span></code></a></div>
</div>
</dd>
<dt>Special Turtle methods</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.begin_poly" title="turtle.begin_poly"><code class="xref py py-func docutils literal notranslate"><span class="pre">begin_poly()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.end_poly" title="turtle.end_poly"><code class="xref py py-func docutils literal notranslate"><span class="pre">end_poly()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.get_poly" title="turtle.get_poly"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_poly()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.clone" title="turtle.clone"><code class="xref py py-func docutils literal notranslate"><span class="pre">clone()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.getturtle" title="turtle.getturtle"><code class="xref py py-func docutils literal notranslate"><span class="pre">getturtle()</span></code></a> | <a class="reference internal" href="#turtle.getpen" title="turtle.getpen"><code class="xref py py-func docutils literal notranslate"><span class="pre">getpen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.getscreen" title="turtle.getscreen"><code class="xref py py-func docutils literal notranslate"><span class="pre">getscreen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.setundobuffer" title="turtle.setundobuffer"><code class="xref py py-func docutils literal notranslate"><span class="pre">setundobuffer()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.undobufferentries" title="turtle.undobufferentries"><code class="xref py py-func docutils literal notranslate"><span class="pre">undobufferentries()</span></code></a></div>
</div>
</dd>
</dl>
</section>
<section id="methods-of-turtlescreen-screen">
<h3>Methods of TurtleScreen/Screen<a class="headerlink" href="#methods-of-turtlescreen-screen" title="Link to this heading">¶</a></h3>
<dl>
<dt>Window control</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.bgcolor" title="turtle.bgcolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">bgcolor()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.bgpic" title="turtle.bgpic"><code class="xref py py-func docutils literal notranslate"><span class="pre">bgpic()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.clearscreen" title="turtle.clearscreen"><code class="xref py py-func docutils literal notranslate"><span class="pre">clearscreen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.resetscreen" title="turtle.resetscreen"><code class="xref py py-func docutils literal notranslate"><span class="pre">resetscreen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.screensize" title="turtle.screensize"><code class="xref py py-func docutils literal notranslate"><span class="pre">screensize()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.setworldcoordinates" title="turtle.setworldcoordinates"><code class="xref py py-func docutils literal notranslate"><span class="pre">setworldcoordinates()</span></code></a></div>
</div>
</dd>
<dt>Animation control</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.delay" title="turtle.delay"><code class="xref py py-func docutils literal notranslate"><span class="pre">delay()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.tracer" title="turtle.tracer"><code class="xref py py-func docutils literal notranslate"><span class="pre">tracer()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.update" title="turtle.update"><code class="xref py py-func docutils literal notranslate"><span class="pre">update()</span></code></a></div>
</div>
</dd>
<dt>Using screen events</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.listen" title="turtle.listen"><code class="xref py py-func docutils literal notranslate"><span class="pre">listen()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.onkey" title="turtle.onkey"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkey()</span></code></a> | <a class="reference internal" href="#turtle.onkeyrelease" title="turtle.onkeyrelease"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkeyrelease()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.onkeypress" title="turtle.onkeypress"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkeypress()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.onclick" title="turtle.onclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">onclick()</span></code></a> | <a class="reference internal" href="#turtle.onscreenclick" title="turtle.onscreenclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">onscreenclick()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.ontimer" title="turtle.ontimer"><code class="xref py py-func docutils literal notranslate"><span class="pre">ontimer()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.mainloop" title="turtle.mainloop"><code class="xref py py-func docutils literal notranslate"><span class="pre">mainloop()</span></code></a> | <a class="reference internal" href="#turtle.done" title="turtle.done"><code class="xref py py-func docutils literal notranslate"><span class="pre">done()</span></code></a></div>
</div>
</dd>
<dt>Settings and special methods</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.mode" title="turtle.mode"><code class="xref py py-func docutils literal notranslate"><span class="pre">mode()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.colormode" title="turtle.colormode"><code class="xref py py-func docutils literal notranslate"><span class="pre">colormode()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.getcanvas" title="turtle.getcanvas"><code class="xref py py-func docutils literal notranslate"><span class="pre">getcanvas()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.getshapes" title="turtle.getshapes"><code class="xref py py-func docutils literal notranslate"><span class="pre">getshapes()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.register_shape" title="turtle.register_shape"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_shape()</span></code></a> | <a class="reference internal" href="#turtle.addshape" title="turtle.addshape"><code class="xref py py-func docutils literal notranslate"><span class="pre">addshape()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.turtles" title="turtle.turtles"><code class="xref py py-func docutils literal notranslate"><span class="pre">turtles()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.window_height" title="turtle.window_height"><code class="xref py py-func docutils literal notranslate"><span class="pre">window_height()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.window_width" title="turtle.window_width"><code class="xref py py-func docutils literal notranslate"><span class="pre">window_width()</span></code></a></div>
</div>
</dd>
<dt>Input methods</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.textinput" title="turtle.textinput"><code class="xref py py-func docutils literal notranslate"><span class="pre">textinput()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.numinput" title="turtle.numinput"><code class="xref py py-func docutils literal notranslate"><span class="pre">numinput()</span></code></a></div>
</div>
</dd>
<dt>Methods specific to Screen</dt><dd><div class="line-block">
<div class="line"><a class="reference internal" href="#turtle.bye" title="turtle.bye"><code class="xref py py-func docutils literal notranslate"><span class="pre">bye()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.exitonclick" title="turtle.exitonclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">exitonclick()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.setup" title="turtle.setup"><code class="xref py py-func docutils literal notranslate"><span class="pre">setup()</span></code></a></div>
<div class="line"><a class="reference internal" href="#turtle.title" title="turtle.title"><code class="xref py py-func docutils literal notranslate"><span class="pre">title()</span></code></a></div>
</div>
</dd>
</dl>
</section>
</section>
<section id="methods-of-rawturtle-turtle-and-corresponding-functions">
<h2>Methods of RawTurtle/Turtle and corresponding functions<a class="headerlink" href="#methods-of-rawturtle-turtle-and-corresponding-functions" title="Link to this heading">¶</a></h2>
<p>Most of the examples in this section refer to a Turtle instance called
<code class="docutils literal notranslate"><span class="pre">turtle</span></code>.</p>
<section id="turtle-motion">
<h3>Turtle motion<a class="headerlink" href="#turtle-motion" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.forward">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">forward</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.forward" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.fd">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">fd</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.fd" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>distance</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Move the turtle forward by the specified <em>distance</em>, in the direction the
turtle is headed.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(25.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="o">-</span><span class="mi">75</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(-50.00,0.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.back">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">back</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.back" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.bk">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">bk</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.bk" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.backward">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">backward</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">distance</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.backward" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>distance</strong> – a number</p>
</dd>
</dl>
<p>Move the turtle backward by <em>distance</em>, opposite to the direction the
turtle is headed.  Do not change the turtle’s heading.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">backward</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(-30.00,0.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.right">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">right</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.right" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.rt">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">rt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.rt" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>angle</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Turn turtle right by <em>angle</em> units.  (Units are by default degrees, but
can be set via the <a class="reference internal" href="#turtle.degrees" title="turtle.degrees"><code class="xref py py-func docutils literal notranslate"><span class="pre">degrees()</span></code></a> and <a class="reference internal" href="#turtle.radians" title="turtle.radians"><code class="xref py py-func docutils literal notranslate"><span class="pre">radians()</span></code></a> functions.)  Angle
orientation depends on the turtle mode, see <a class="reference internal" href="#turtle.mode" title="turtle.mode"><code class="xref py py-func docutils literal notranslate"><span class="pre">mode()</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">22.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">right</span><span class="p">(</span><span class="mi">45</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">337.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.left">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">left</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.left" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.lt">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">lt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.lt" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>angle</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Turn turtle left by <em>angle</em> units.  (Units are by default degrees, but
can be set via the <a class="reference internal" href="#turtle.degrees" title="turtle.degrees"><code class="xref py py-func docutils literal notranslate"><span class="pre">degrees()</span></code></a> and <a class="reference internal" href="#turtle.radians" title="turtle.radians"><code class="xref py py-func docutils literal notranslate"><span class="pre">radians()</span></code></a> functions.)  Angle
orientation depends on the turtle mode, see <a class="reference internal" href="#turtle.mode" title="turtle.mode"><code class="xref py py-func docutils literal notranslate"><span class="pre">mode()</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">22.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">45</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">67.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.goto">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">goto</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.goto" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.setpos">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setpos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setpos" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.setposition">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setposition</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setposition" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> – a number or a pair/vector of numbers</p></li>
<li><p><strong>y</strong> – a number or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
</ul>
</dd>
</dl>
<p>If <em>y</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, <em>x</em> must be a pair of coordinates or a <a class="reference internal" href="#turtle.Vec2D" title="turtle.Vec2D"><code class="xref py py-class docutils literal notranslate"><span class="pre">Vec2D</span></code></a>
(e.g. as returned by <a class="reference internal" href="#turtle.pos" title="turtle.pos"><code class="xref py py-func docutils literal notranslate"><span class="pre">pos()</span></code></a>).</p>
<p>Move turtle to an absolute position.  If the pen is down, draw line.  Do
not change the turtle’s orientation.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tp</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tp</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setpos</span><span class="p">(</span><span class="mi">60</span><span class="p">,</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(60.00,30.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setpos</span><span class="p">((</span><span class="mi">20</span><span class="p">,</span><span class="mi">80</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(20.00,80.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setpos</span><span class="p">(</span><span class="n">tp</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.teleport">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">teleport</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fill_gap</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.teleport" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> – a number or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
<li><p><strong>y</strong> – a number or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
<li><p><strong>fill_gap</strong> – a boolean</p></li>
</ul>
</dd>
</dl>
<p>Move turtle to an absolute position. Unlike goto(x, y), a line will not
be drawn. The turtle’s orientation does not change. If currently
filling, the polygon(s) teleported from will be filled after leaving,
and filling will begin again after teleporting. This can be disabled
with fill_gap=True, which makes the imaginary line traveled during
teleporting act as a fill barrier like in goto(x, y).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tp</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tp</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">teleport</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(60.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">teleport</span><span class="p">(</span><span class="n">y</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(60.00,10.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">teleport</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(20.00,30.00)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.setx">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setx</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setx" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>x</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Set the turtle’s first coordinate to <em>x</em>, leave second coordinate
unchanged.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,240.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setx</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(10.00,240.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.sety">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">sety</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.sety" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>y</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Set the turtle’s second coordinate to <em>y</em>, leave first coordinate unchanged.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,40.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">sety</span><span class="p">(</span><span class="o">-</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,-10.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.setheading">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setheading</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">to_angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setheading" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.seth">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">seth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">to_angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.seth" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>to_angle</strong> – a number (integer or float)</p>
</dd>
</dl>
<p>Set the orientation of the turtle to <em>to_angle</em>.  Here are some common
directions in degrees:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>standard mode</p></th>
<th class="head"><p>logo mode</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>0 - east</p></td>
<td><p>0 - north</p></td>
</tr>
<tr class="row-odd"><td><p>90 - north</p></td>
<td><p>90 - east</p></td>
</tr>
<tr class="row-even"><td><p>180 - west</p></td>
<td><p>180 - south</p></td>
</tr>
<tr class="row-odd"><td><p>270 - south</p></td>
<td><p>270 - west</p></td>
</tr>
</tbody>
</table>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setheading</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">90.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.home">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">home</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.home" title="Link to this definition">¶</a></dt>
<dd><p>Move turtle to the origin – coordinates (0,0) – and set its heading to
its start-orientation (which depends on the mode, see <a class="reference internal" href="#turtle.mode" title="turtle.mode"><code class="xref py py-func docutils literal notranslate"><span class="pre">mode()</span></code></a>).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">90.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,-10.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">0.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.circle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">circle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">radius</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">extent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">steps</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.circle" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>radius</strong> – a number</p></li>
<li><p><strong>extent</strong> – a number (or <code class="docutils literal notranslate"><span class="pre">None</span></code>)</p></li>
<li><p><strong>steps</strong> – an integer (or <code class="docutils literal notranslate"><span class="pre">None</span></code>)</p></li>
</ul>
</dd>
</dl>
<p>Draw a circle with given <em>radius</em>.  The center is <em>radius</em> units left of
the turtle; <em>extent</em> – an angle – determines which part of the circle
is drawn.  If <em>extent</em> is not given, draw the entire circle.  If <em>extent</em>
is not a full circle, one endpoint of the arc is the current pen
position.  Draw the arc in counterclockwise direction if <em>radius</em> is
positive, otherwise in clockwise direction.  Finally the direction of the
turtle is changed by the amount of <em>extent</em>.</p>
<p>As the circle is approximated by an inscribed regular polygon, <em>steps</em>
determines the number of steps to use.  If not given, it will be
calculated automatically.  May be used to draw regular polygons.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">0.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">circle</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(-0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">0.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">circle</span><span class="p">(</span><span class="mi">120</span><span class="p">,</span> <span class="mi">180</span><span class="p">)</span>  <span class="c1"># draw a semicircle</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,240.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">180.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.dot">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">dot</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">color</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.dot" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>size</strong> – an integer &gt;= 1 (if given)</p></li>
<li><p><strong>color</strong> – a colorstring or a numeric color tuple</p></li>
</ul>
</dd>
</dl>
<p>Draw a circular dot with diameter <em>size</em>, using <em>color</em>.  If <em>size</em> is
not given, the maximum of pensize+4 and 2*pensize is used.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">dot</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">);</span> <span class="n">turtle</span><span class="o">.</span><span class="n">dot</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="s2">&quot;blue&quot;</span><span class="p">);</span> <span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(100.00,-0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">0.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.stamp">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">stamp</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.stamp" title="Link to this definition">¶</a></dt>
<dd><p>Stamp a copy of the turtle shape onto the canvas at the current turtle
position.  Return a stamp_id for that stamp, which can be used to delete
it by calling <code class="docutils literal notranslate"><span class="pre">clearstamp(stamp_id)</span></code>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;blue&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">stamp_id</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">stamp</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.clearstamp">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clearstamp</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stampid</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.clearstamp" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>stampid</strong> – an integer, must be return value of previous
<a class="reference internal" href="#turtle.stamp" title="turtle.stamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">stamp()</span></code></a> call</p>
</dd>
</dl>
<p>Delete stamp with given <em>stampid</em>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(150.00,-0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;blue&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">astamp</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">stamp</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(200.00,-0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">clearstamp</span><span class="p">(</span><span class="n">astamp</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(200.00,-0.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.clearstamps">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clearstamps</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.clearstamps" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>n</strong> – an integer (or <code class="docutils literal notranslate"><span class="pre">None</span></code>)</p>
</dd>
</dl>
<p>Delete all or first/last <em>n</em> of turtle’s stamps.  If <em>n</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, delete
all stamps, if <em>n</em> &gt; 0 delete first <em>n</em> stamps, else if <em>n</em> &lt; 0 delete
last <em>n</em> stamps.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">8</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">unused_stamp_id</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">stamp</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">clearstamps</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">clearstamps</span><span class="p">(</span><span class="o">-</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">clearstamps</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.undo">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">undo</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.undo" title="Link to this definition">¶</a></dt>
<dd><p>Undo (repeatedly) the last turtle action(s).  Number of available
undo actions is determined by the size of the undobuffer.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">4</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">);</span> <span class="n">turtle</span><span class="o">.</span><span class="n">lt</span><span class="p">(</span><span class="mi">80</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">8</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">turtle</span><span class="o">.</span><span class="n">undo</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.speed">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">speed</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">speed</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.speed" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>speed</strong> – an integer in the range 0..10 or a speedstring (see below)</p>
</dd>
</dl>
<p>Set the turtle’s speed to an integer value in the range 0..10.  If no
argument is given, return current speed.</p>
<p>If input is a number greater than 10 or smaller than 0.5, speed is set
to 0.  Speedstrings are mapped to speedvalues as follows:</p>
<ul class="simple">
<li><p>“fastest”:  0</p></li>
<li><p>“fast”:  10</p></li>
<li><p>“normal”:  6</p></li>
<li><p>“slow”:  3</p></li>
<li><p>“slowest”:  1</p></li>
</ul>
<p>Speeds from 1 to 10 enforce increasingly faster animation of line drawing
and turtle turning.</p>
<p>Attention: <em>speed</em> = 0 means that <em>no</em> animation takes
place. forward/back makes turtle jump and likewise left/right make the
turtle turn instantly.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">speed</span><span class="p">()</span>
<span class="go">3</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="s1">&#39;normal&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">speed</span><span class="p">()</span>
<span class="go">6</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">9</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">speed</span><span class="p">()</span>
<span class="go">9</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="tell-turtle-s-state">
<h3>Tell Turtle’s state<a class="headerlink" href="#tell-turtle-s-state" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.position">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">position</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.position" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.pos">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pos</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pos" title="Link to this definition">¶</a></dt>
<dd><p>Return the turtle’s current location (x,y) (as a <a class="reference internal" href="#turtle.Vec2D" title="turtle.Vec2D"><code class="xref py py-class docutils literal notranslate"><span class="pre">Vec2D</span></code></a> vector).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(440.00,-0.00)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.towards">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">towards</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.towards" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> – a number or a pair/vector of numbers or a turtle instance</p></li>
<li><p><strong>y</strong> – a number if <em>x</em> is a number, else <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
</ul>
</dd>
</dl>
<p>Return the angle between the line from turtle position to position specified
by (x,y), the vector or the other turtle.  This depends on the turtle’s start
orientation which depends on the mode - “standard”/”world” or “logo”.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">goto</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">towards</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">)</span>
<span class="go">225.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.xcor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">xcor</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.xcor" title="Link to this definition">¶</a></dt>
<dd><p>Return the turtle’s x coordinate.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">()</span>
<span class="go">(64.28,76.60)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="nb">round</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">xcor</span><span class="p">(),</span> <span class="mi">5</span><span class="p">))</span>
<span class="go">64.27876</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.ycor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">ycor</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.ycor" title="Link to this definition">¶</a></dt>
<dd><p>Return the turtle’s y coordinate.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">pos</span><span class="p">())</span>
<span class="go">(50.00,86.60)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="nb">round</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">ycor</span><span class="p">(),</span> <span class="mi">5</span><span class="p">))</span>
<span class="go">86.60254</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.heading">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">heading</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.heading" title="Link to this definition">¶</a></dt>
<dd><p>Return the turtle’s current heading (value depends on the turtle mode, see
<a class="reference internal" href="#turtle.mode" title="turtle.mode"><code class="xref py py-func docutils literal notranslate"><span class="pre">mode()</span></code></a>).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">67</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">67.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.distance">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">distance</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.distance" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>x</strong> – a number or a pair/vector of numbers or a turtle instance</p></li>
<li><p><strong>y</strong> – a number if <em>x</em> is a number, else <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
</ul>
</dd>
</dl>
<p>Return the distance from the turtle to (x,y), the given vector, or the given
other turtle, in turtle step units.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">distance</span><span class="p">(</span><span class="mi">30</span><span class="p">,</span><span class="mi">40</span><span class="p">)</span>
<span class="go">50.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">distance</span><span class="p">((</span><span class="mi">30</span><span class="p">,</span><span class="mi">40</span><span class="p">))</span>
<span class="go">50.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">joe</span> <span class="o">=</span> <span class="n">Turtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">joe</span><span class="o">.</span><span class="n">forward</span><span class="p">(</span><span class="mi">77</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">distance</span><span class="p">(</span><span class="n">joe</span><span class="p">)</span>
<span class="go">77.0</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="settings-for-measurement">
<h3>Settings for measurement<a class="headerlink" href="#settings-for-measurement" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.degrees">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">degrees</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullcircle</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">360.0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.degrees" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>fullcircle</strong> – a number</p>
</dd>
</dl>
<p>Set angle measurement units, i.e. set number of “degrees” for a full circle.
Default value is 360 degrees.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">90.0</span>

<span class="go">Change angle measurement unit to grad (also known as gon,</span>
<span class="go">grade, or gradian and equals 1/100-th of the right angle.)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">degrees</span><span class="p">(</span><span class="mf">400.0</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">100.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">degrees</span><span class="p">(</span><span class="mi">360</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">90.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.radians">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">radians</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.radians" title="Link to this definition">¶</a></dt>
<dd><p>Set the angle measurement units to radians.  Equivalent to
<code class="docutils literal notranslate"><span class="pre">degrees(2*math.pi)</span></code>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">90.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">radians</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">1.5707963267948966</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="id1">
<h3>Pen control<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h3>
<section id="drawing-state">
<h4>Drawing state<a class="headerlink" href="#drawing-state" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.pendown">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pendown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pendown" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.pd">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pd</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pd" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.down">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">down</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.down" title="Link to this definition">¶</a></dt>
<dd><p>Pull the pen down – drawing when moving.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.penup">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">penup</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.penup" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.pu">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pu</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pu" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.up">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">up</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.up" title="Link to this definition">¶</a></dt>
<dd><p>Pull the pen up – no drawing when moving.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.pensize">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pensize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pensize" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.width">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">width</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.width" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>width</strong> – a positive number</p>
</dd>
</dl>
<p>Set the line thickness to <em>width</em> or return it.  If resizemode is set to
“auto” and turtleshape is a polygon, that polygon is drawn with the same line
thickness.  If no argument is given, the current pensize is returned.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pensize</span><span class="p">()</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pensize</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>   <span class="c1"># from here on lines of width 10 are drawn</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.pen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pen</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">pendict</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pen" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>pen</strong> – a dictionary with some or all of the below listed keys</p></li>
<li><p><strong>pendict</strong> – one or more keyword-arguments with the below listed keys as keywords</p></li>
</ul>
</dd>
</dl>
<p>Return or set the pen’s attributes in a “pen-dictionary” with the following
key/value pairs:</p>
<ul class="simple">
<li><p>“shown”: True/False</p></li>
<li><p>“pendown”: True/False</p></li>
<li><p>“pencolor”: color-string or color-tuple</p></li>
<li><p>“fillcolor”: color-string or color-tuple</p></li>
<li><p>“pensize”: positive number</p></li>
<li><p>“speed”: number in range 0..10</p></li>
<li><p>“resizemode”: “auto” or “user” or “noresize”</p></li>
<li><p>“stretchfactor”: (positive number, positive number)</p></li>
<li><p>“outline”: positive number</p></li>
<li><p>“tilt”: number</p></li>
</ul>
<p>This dictionary can be used as argument for a subsequent call to <a class="reference internal" href="#turtle.pen" title="turtle.pen"><code class="xref py py-func docutils literal notranslate"><span class="pre">pen()</span></code></a>
to restore the former pen-state.  Moreover one or more of these attributes
can be provided as keyword-arguments.  This can be used to set several pen
attributes in one statement.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">(</span><span class="n">fillcolor</span><span class="o">=</span><span class="s2">&quot;black&quot;</span><span class="p">,</span> <span class="n">pencolor</span><span class="o">=</span><span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="n">pensize</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">()</span><span class="o">.</span><span class="n">items</span><span class="p">())</span>
<span class="go">[(&#39;fillcolor&#39;, &#39;black&#39;), (&#39;outline&#39;, 1), (&#39;pencolor&#39;, &#39;red&#39;),</span>
<span class="go"> (&#39;pendown&#39;, True), (&#39;pensize&#39;, 10), (&#39;resizemode&#39;, &#39;noresize&#39;),</span>
<span class="go"> (&#39;shearfactor&#39;, 0.0), (&#39;shown&#39;, True), (&#39;speed&#39;, 9),</span>
<span class="go"> (&#39;stretchfactor&#39;, (1.0, 1.0)), (&#39;tilt&#39;, 0.0)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">penstate</span><span class="o">=</span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;yellow&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">penup</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">()</span><span class="o">.</span><span class="n">items</span><span class="p">())[:</span><span class="mi">3</span><span class="p">]</span>
<span class="go">[(&#39;fillcolor&#39;, &#39;&#39;), (&#39;outline&#39;, 1), (&#39;pencolor&#39;, &#39;yellow&#39;)]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">(</span><span class="n">penstate</span><span class="p">,</span> <span class="n">fillcolor</span><span class="o">=</span><span class="s2">&quot;green&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">sorted</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">pen</span><span class="p">()</span><span class="o">.</span><span class="n">items</span><span class="p">())[:</span><span class="mi">3</span><span class="p">]</span>
<span class="go">[(&#39;fillcolor&#39;, &#39;green&#39;), (&#39;outline&#39;, 1), (&#39;pencolor&#39;, &#39;red&#39;)]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.isdown">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">isdown</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.isdown" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if pen is down, <code class="docutils literal notranslate"><span class="pre">False</span></code> if it’s up.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">penup</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">isdown</span><span class="p">()</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pendown</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">isdown</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="color-control">
<h4>Color control<a class="headerlink" href="#color-control" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.pencolor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">pencolor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.pencolor" title="Link to this definition">¶</a></dt>
<dd><p>Return or set the pencolor.</p>
<p>Four input formats are allowed:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">pencolor()</span></code></dt><dd><p>Return the current pencolor as color specification string or
as a tuple (see example).  May be used as input to another
color/pencolor/fillcolor call.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">pencolor(colorstring)</span></code></dt><dd><p>Set pencolor to <em>colorstring</em>, which is a Tk color specification string,
such as <code class="docutils literal notranslate"><span class="pre">&quot;red&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;yellow&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;#33cc8c&quot;</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">pencolor((r,</span> <span class="pre">g,</span> <span class="pre">b))</span></code></dt><dd><p>Set pencolor to the RGB color represented by the tuple of <em>r</em>, <em>g</em>, and
<em>b</em>.  Each of <em>r</em>, <em>g</em>, and <em>b</em> must be in the range 0..colormode, where
colormode is either 1.0 or 255 (see <a class="reference internal" href="#turtle.colormode" title="turtle.colormode"><code class="xref py py-func docutils literal notranslate"><span class="pre">colormode()</span></code></a>).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">pencolor(r,</span> <span class="pre">g,</span> <span class="pre">b)</span></code></dt><dd><p>Set pencolor to the RGB color represented by <em>r</em>, <em>g</em>, and <em>b</em>.  Each of
<em>r</em>, <em>g</em>, and <em>b</em> must be in the range 0..colormode.</p>
</dd>
</dl>
<p>If turtleshape is a polygon, the outline of that polygon is drawn with the
newly set pencolor.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">colormode</span><span class="p">()</span>
<span class="go">1.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">&#39;red&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">(</span><span class="s2">&quot;brown&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">&#39;brown&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tup</span> <span class="o">=</span> <span class="p">(</span><span class="mf">0.2</span><span class="p">,</span> <span class="mf">0.8</span><span class="p">,</span> <span class="mf">0.55</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">(</span><span class="n">tup</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">(0.2, 0.8, 0.5490196078431373)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">colormode</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">(51.0, 204.0, 140.0)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">(</span><span class="s1">&#39;#32c18f&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">(50.0, 193.0, 143.0)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.fillcolor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">fillcolor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.fillcolor" title="Link to this definition">¶</a></dt>
<dd><p>Return or set the fillcolor.</p>
<p>Four input formats are allowed:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">fillcolor()</span></code></dt><dd><p>Return the current fillcolor as color specification string, possibly
in tuple format (see example).  May be used as input to another
color/pencolor/fillcolor call.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">fillcolor(colorstring)</span></code></dt><dd><p>Set fillcolor to <em>colorstring</em>, which is a Tk color specification string,
such as <code class="docutils literal notranslate"><span class="pre">&quot;red&quot;</span></code>, <code class="docutils literal notranslate"><span class="pre">&quot;yellow&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">&quot;#33cc8c&quot;</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">fillcolor((r,</span> <span class="pre">g,</span> <span class="pre">b))</span></code></dt><dd><p>Set fillcolor to the RGB color represented by the tuple of <em>r</em>, <em>g</em>, and
<em>b</em>.  Each of <em>r</em>, <em>g</em>, and <em>b</em> must be in the range 0..colormode, where
colormode is either 1.0 or 255 (see <a class="reference internal" href="#turtle.colormode" title="turtle.colormode"><code class="xref py py-func docutils literal notranslate"><span class="pre">colormode()</span></code></a>).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">fillcolor(r,</span> <span class="pre">g,</span> <span class="pre">b)</span></code></dt><dd><p>Set fillcolor to the RGB color represented by <em>r</em>, <em>g</em>, and <em>b</em>.  Each of
<em>r</em>, <em>g</em>, and <em>b</em> must be in the range 0..colormode.</p>
</dd>
</dl>
<p>If turtleshape is a polygon, the interior of that polygon is drawn
with the newly set fillcolor.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">(</span><span class="s2">&quot;violet&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">()</span>
<span class="go">&#39;violet&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">()</span>
<span class="go">(50.0, 193.0, 143.0)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">((</span><span class="mi">50</span><span class="p">,</span> <span class="mi">193</span><span class="p">,</span> <span class="mi">143</span><span class="p">))</span>  <span class="c1"># Integers, not floats</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">()</span>
<span class="go">(50.0, 193.0, 143.0)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">(</span><span class="s1">&#39;#ffffff&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">()</span>
<span class="go">(255.0, 255.0, 255.0)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.color">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">color</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.color" title="Link to this definition">¶</a></dt>
<dd><p>Return or set pencolor and fillcolor.</p>
<p>Several input formats are allowed.  They use 0 to 3 arguments as
follows:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">color()</span></code></dt><dd><p>Return the current pencolor and the current fillcolor as a pair of color
specification strings or tuples as returned by <a class="reference internal" href="#turtle.pencolor" title="turtle.pencolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">pencolor()</span></code></a> and
<a class="reference internal" href="#turtle.fillcolor" title="turtle.fillcolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">fillcolor()</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">color(colorstring)</span></code>, <code class="docutils literal notranslate"><span class="pre">color((r,g,b))</span></code>, <code class="docutils literal notranslate"><span class="pre">color(r,g,b)</span></code></dt><dd><p>Inputs as in <a class="reference internal" href="#turtle.pencolor" title="turtle.pencolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">pencolor()</span></code></a>, set both, fillcolor and pencolor, to the
given value.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">color(colorstring1,</span> <span class="pre">colorstring2)</span></code>, <code class="docutils literal notranslate"><span class="pre">color((r1,g1,b1),</span> <span class="pre">(r2,g2,b2))</span></code></dt><dd><p>Equivalent to <code class="docutils literal notranslate"><span class="pre">pencolor(colorstring1)</span></code> and <code class="docutils literal notranslate"><span class="pre">fillcolor(colorstring2)</span></code>
and analogously if the other input format is used.</p>
</dd>
</dl>
<p>If turtleshape is a polygon, outline and interior of that polygon is drawn
with the newly set colors.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="s2">&quot;green&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">()</span>
<span class="go">(&#39;red&#39;, &#39;green&#39;)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;#285078&quot;</span><span class="p">,</span> <span class="s2">&quot;#a0c8f0&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">color</span><span class="p">()</span>
<span class="go">((40.0, 80.0, 120.0), (160.0, 200.0, 240.0))</span>
</pre></div>
</div>
</dd></dl>

<p>See also: Screen method <a class="reference internal" href="#turtle.colormode" title="turtle.colormode"><code class="xref py py-func docutils literal notranslate"><span class="pre">colormode()</span></code></a>.</p>
</section>
<section id="filling">
<h4>Filling<a class="headerlink" href="#filling" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.filling">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">filling</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.filling" title="Link to this definition">¶</a></dt>
<dd><p>Return fillstate (<code class="docutils literal notranslate"><span class="pre">True</span></code> if filling, <code class="docutils literal notranslate"><span class="pre">False</span></code> else).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">begin_fill</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">if</span> <span class="n">turtle</span><span class="o">.</span><span class="n">filling</span><span class="p">():</span>
<span class="gp">... </span>   <span class="n">turtle</span><span class="o">.</span><span class="n">pensize</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="gp">... </span><span class="k">else</span><span class="p">:</span>
<span class="gp">... </span>   <span class="n">turtle</span><span class="o">.</span><span class="n">pensize</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.begin_fill">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">begin_fill</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.begin_fill" title="Link to this definition">¶</a></dt>
<dd><p>To be called just before drawing a shape to be filled.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.end_fill">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">end_fill</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.end_fill" title="Link to this definition">¶</a></dt>
<dd><p>Fill the shape drawn after the last call to <a class="reference internal" href="#turtle.begin_fill" title="turtle.begin_fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">begin_fill()</span></code></a>.</p>
<p>Whether or not overlap regions for self-intersecting polygons
or multiple shapes are filled depends on the operating system graphics,
type of overlap, and number of overlaps.  For example, the Turtle star
above may be either all yellow or have some white regions.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;black&quot;</span><span class="p">,</span> <span class="s2">&quot;red&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">begin_fill</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">circle</span><span class="p">(</span><span class="mi">80</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">end_fill</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="more-drawing-control">
<h4>More drawing control<a class="headerlink" href="#more-drawing-control" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.reset">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.reset" title="Link to this definition">¶</a></dt>
<dd><p>Delete the turtle’s drawings from the screen, re-center the turtle and set
variables to the default values.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">goto</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="o">-</span><span class="mi">22</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,-22.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">100.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">position</span><span class="p">()</span>
<span class="go">(0.00,0.00)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">heading</span><span class="p">()</span>
<span class="go">0.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.clear">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.clear" title="Link to this definition">¶</a></dt>
<dd><p>Delete the turtle’s drawings from the screen.  Do not move turtle.  State and
position of the turtle as well as drawings of other turtles are not affected.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.write">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">write</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">arg</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">move</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">align</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'left'</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">font</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">('Arial',</span> <span class="pre">8,</span> <span class="pre">'normal')</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.write" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>arg</strong> – object to be written to the TurtleScreen</p></li>
<li><p><strong>move</strong> – True/False</p></li>
<li><p><strong>align</strong> – one of the strings “left”, “center” or right”</p></li>
<li><p><strong>font</strong> – a triple (fontname, fontsize, fonttype)</p></li>
</ul>
</dd>
</dl>
<p>Write text - the string representation of <em>arg</em> - at the current turtle
position according to <em>align</em> (“left”, “center” or “right”) and with the given
font.  If <em>move</em> is true, the pen is moved to the bottom-right corner of the
text.  By default, <em>move</em> is <code class="docutils literal notranslate"><span class="pre">False</span></code>.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Home = &quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">,</span> <span class="n">align</span><span class="o">=</span><span class="s2">&quot;center&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">write</span><span class="p">((</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">),</span> <span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>
<section id="turtle-state">
<h3>Turtle state<a class="headerlink" href="#turtle-state" title="Link to this heading">¶</a></h3>
<section id="visibility">
<h4>Visibility<a class="headerlink" href="#visibility" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.hideturtle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">hideturtle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.hideturtle" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.ht">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">ht</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.ht" title="Link to this definition">¶</a></dt>
<dd><p>Make the turtle invisible.  It’s a good idea to do this while you’re in the
middle of doing some complex drawing, because hiding the turtle speeds up the
drawing observably.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">hideturtle</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.showturtle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">showturtle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.showturtle" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.st">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">st</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.st" title="Link to this definition">¶</a></dt>
<dd><p>Make the turtle visible.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">showturtle</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.isvisible">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">isvisible</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.isvisible" title="Link to this definition">¶</a></dt>
<dd><p>Return <code class="docutils literal notranslate"><span class="pre">True</span></code> if the Turtle is shown, <code class="docutils literal notranslate"><span class="pre">False</span></code> if it’s hidden.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">hideturtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">isvisible</span><span class="p">()</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">showturtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">isvisible</span><span class="p">()</span>
<span class="go">True</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="appearance">
<h4>Appearance<a class="headerlink" href="#appearance" title="Link to this heading">¶</a></h4>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.shape">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">shape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.shape" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> – a string which is a valid shapename</p>
</dd>
</dl>
<p>Set turtle shape to shape with given <em>name</em> or, if name is not given, return
name of current shape.  Shape with <em>name</em> must exist in the TurtleScreen’s
shape dictionary.  Initially there are the following polygon shapes: “arrow”,
“turtle”, “circle”, “square”, “triangle”, “classic”.  To learn about how to
deal with shapes see Screen method <a class="reference internal" href="#turtle.register_shape" title="turtle.register_shape"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_shape()</span></code></a>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">()</span>
<span class="go">&#39;classic&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;turtle&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">()</span>
<span class="go">&#39;turtle&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.resizemode">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">resizemode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">rmode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.resizemode" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>rmode</strong> – one of the strings “auto”, “user”, “noresize”</p>
</dd>
</dl>
<p>Set resizemode to one of the values: “auto”, “user”, “noresize”.  If <em>rmode</em>
is not given, return current resizemode.  Different resizemodes have the
following effects:</p>
<ul class="simple">
<li><p>“auto”: adapts the appearance of the turtle corresponding to the value of pensize.</p></li>
<li><p>“user”: adapts the appearance of the turtle according to the values of
stretchfactor and outlinewidth (outline), which are set by
<a class="reference internal" href="#turtle.shapesize" title="turtle.shapesize"><code class="xref py py-func docutils literal notranslate"><span class="pre">shapesize()</span></code></a>.</p></li>
<li><p>“noresize”: no adaption of the turtle’s appearance takes place.</p></li>
</ul>
<p><code class="docutils literal notranslate"><span class="pre">resizemode(&quot;user&quot;)</span></code> is called by <a class="reference internal" href="#turtle.shapesize" title="turtle.shapesize"><code class="xref py py-func docutils literal notranslate"><span class="pre">shapesize()</span></code></a> when used with arguments.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">resizemode</span><span class="p">()</span>
<span class="go">&#39;noresize&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">resizemode</span><span class="p">(</span><span class="s2">&quot;auto&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">resizemode</span><span class="p">()</span>
<span class="go">&#39;auto&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.shapesize">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">shapesize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stretch_wid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stretch_len</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.shapesize" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.turtlesize">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">turtlesize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">stretch_wid</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stretch_len</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.turtlesize" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>stretch_wid</strong> – positive number</p></li>
<li><p><strong>stretch_len</strong> – positive number</p></li>
<li><p><strong>outline</strong> – positive number</p></li>
</ul>
</dd>
</dl>
<p>Return or set the pen’s attributes x/y-stretchfactors and/or outline.  Set
resizemode to “user”.  If and only if resizemode is set to “user”, the turtle
will be displayed stretched according to its stretchfactors: <em>stretch_wid</em> is
stretchfactor perpendicular to its orientation, <em>stretch_len</em> is
stretchfactor in direction of its orientation, <em>outline</em> determines the width
of the shape’s outline.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">()</span>
<span class="go">(1.0, 1.0, 1)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">resizemode</span><span class="p">(</span><span class="s2">&quot;user&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">12</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">()</span>
<span class="go">(5, 5, 12)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="n">outline</span><span class="o">=</span><span class="mi">8</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">()</span>
<span class="go">(5, 5, 8)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.shearfactor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">shearfactor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">shear</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.shearfactor" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>shear</strong> – number (optional)</p>
</dd>
</dl>
<p>Set or return the current shearfactor. Shear the turtleshape according to
the given shearfactor shear, which is the tangent of the shear angle.
Do <em>not</em> change the turtle’s heading (direction of movement).
If shear is not given: return the current shearfactor, i. e. the
tangent of the shear angle, by which lines parallel to the
heading of the turtle are sheared.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;circle&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shearfactor</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shearfactor</span><span class="p">()</span>
<span class="go">0.5</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.tilt">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">tilt</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.tilt" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>angle</strong> – a number</p>
</dd>
</dl>
<p>Rotate the turtleshape by <em>angle</em> from its current tilt-angle, but do <em>not</em>
change the turtle’s heading (direction of movement).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;circle&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">tilt</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">tilt</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.settiltangle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">settiltangle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.settiltangle" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>angle</strong> – a number</p>
</dd>
</dl>
<p>Rotate the turtleshape to point in the direction specified by <em>angle</em>,
regardless of its current tilt-angle.  <em>Do not</em> change the turtle’s heading
(direction of movement).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;circle&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">settiltangle</span><span class="p">(</span><span class="mi">45</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">settiltangle</span><span class="p">(</span><span class="o">-</span><span class="mi">45</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.1.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.tiltangle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">tiltangle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">angle</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.tiltangle" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>angle</strong> – a number (optional)</p>
</dd>
</dl>
<p>Set or return the current tilt-angle. If angle is given, rotate the
turtleshape to point in the direction specified by angle,
regardless of its current tilt-angle. Do <em>not</em> change the turtle’s
heading (direction of movement).
If angle is not given: return the current tilt-angle, i. e. the angle
between the orientation of the turtleshape and the heading of the
turtle (its direction of movement).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;circle&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">tilt</span><span class="p">(</span><span class="mi">45</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">tiltangle</span><span class="p">()</span>
<span class="go">45.0</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.shapetransform">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">shapetransform</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">t11</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">t12</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">t21</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">t22</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.shapetransform" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>t11</strong> – a number (optional)</p></li>
<li><p><strong>t12</strong> – a number (optional)</p></li>
<li><p><strong>t21</strong> – a number (optional)</p></li>
<li><p><strong>t12</strong> – a number (optional)</p></li>
</ul>
</dd>
</dl>
<p>Set or return the current transformation matrix of the turtle shape.</p>
<p>If none of the matrix elements are given, return the transformation
matrix as a tuple of 4 elements.
Otherwise set the given elements and transform the turtleshape
according to the matrix consisting of first row t11, t12 and
second row t21, t22. The determinant t11 * t22 - t12 * t21 must not be
zero, otherwise an error is raised.
Modify stretchfactor, shearfactor and tiltangle according to the
given matrix.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span> <span class="o">=</span> <span class="n">Turtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;square&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapesize</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shearfactor</span><span class="p">(</span><span class="o">-</span><span class="mf">0.5</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapetransform</span><span class="p">()</span>
<span class="go">(4.0, -1.0, -0.0, 2.0)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.get_shapepoly">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">get_shapepoly</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.get_shapepoly" title="Link to this definition">¶</a></dt>
<dd><p>Return the current shape polygon as tuple of coordinate pairs. This
can be used to define a new shape or components of a compound shape.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;square&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">shapetransform</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">get_shapepoly</span><span class="p">()</span>
<span class="go">((50, -20), (30, 20), (-50, 20), (-30, -20))</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>
<section id="using-events">
<h3>Using events<a class="headerlink" href="#using-events" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onclick</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">btn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">add</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with two arguments which will be called with the
coordinates of the clicked point on the canvas</p></li>
<li><p><strong>btn</strong> – number of the mouse-button, defaults to 1 (left mouse button)</p></li>
<li><p><strong>add</strong> – <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> – if <code class="docutils literal notranslate"><span class="pre">True</span></code>, a new binding will be
added, otherwise it will replace a former binding</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to mouse-click events on this turtle.  If <em>fun</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
existing bindings are removed.  Example for the anonymous turtle, i.e. the
procedural way:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">turn</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">left</span><span class="p">(</span><span class="mi">180</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">onclick</span><span class="p">(</span><span class="n">turn</span><span class="p">)</span>  <span class="c1"># Now clicking into the turtle will turn it.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">onclick</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>  <span class="c1"># event-binding will be removed</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.onrelease">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onrelease</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">btn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">add</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onrelease" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with two arguments which will be called with the
coordinates of the clicked point on the canvas</p></li>
<li><p><strong>btn</strong> – number of the mouse-button, defaults to 1 (left mouse button)</p></li>
<li><p><strong>add</strong> – <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> – if <code class="docutils literal notranslate"><span class="pre">True</span></code>, a new binding will be
added, otherwise it will replace a former binding</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to mouse-button-release events on this turtle.  If <em>fun</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code>, existing bindings are removed.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyTurtle</span><span class="p">(</span><span class="n">Turtle</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">glow</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span><span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">(</span><span class="s2">&quot;red&quot;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">unglow</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span><span class="n">x</span><span class="p">,</span><span class="n">y</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">fillcolor</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span> <span class="o">=</span> <span class="n">MyTurtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">onclick</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">glow</span><span class="p">)</span>     <span class="c1"># clicking on turtle turns fillcolor red,</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">onrelease</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">unglow</span><span class="p">)</span> <span class="c1"># releasing turns it to transparent.</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.ondrag">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">ondrag</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">btn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">add</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.ondrag" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with two arguments which will be called with the
coordinates of the clicked point on the canvas</p></li>
<li><p><strong>btn</strong> – number of the mouse-button, defaults to 1 (left mouse button)</p></li>
<li><p><strong>add</strong> – <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> – if <code class="docutils literal notranslate"><span class="pre">True</span></code>, a new binding will be
added, otherwise it will replace a former binding</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to mouse-move events on this turtle.  If <em>fun</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
existing bindings are removed.</p>
<p>Remark: Every sequence of mouse-move-events on a turtle is preceded by a
mouse-click event on that turtle.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">ondrag</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">goto</span><span class="p">)</span>
</pre></div>
</div>
<p>Subsequently, clicking and dragging the Turtle will move it across
the screen thereby producing handdrawings (if pen is down).</p>
</dd></dl>

</section>
<section id="special-turtle-methods">
<h3>Special Turtle methods<a class="headerlink" href="#special-turtle-methods" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.begin_poly">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">begin_poly</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.begin_poly" title="Link to this definition">¶</a></dt>
<dd><p>Start recording the vertices of a polygon.  Current turtle position is first
vertex of polygon.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.end_poly">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">end_poly</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.end_poly" title="Link to this definition">¶</a></dt>
<dd><p>Stop recording the vertices of a polygon.  Current turtle position is last
vertex of polygon.  This will be connected with the first vertex.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.get_poly">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">get_poly</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.get_poly" title="Link to this definition">¶</a></dt>
<dd><p>Return the last recorded polygon.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">home</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">begin_poly</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">left</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">end_poly</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">p</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">get_poly</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">register_shape</span><span class="p">(</span><span class="s2">&quot;myFavouriteShape&quot;</span><span class="p">,</span> <span class="n">p</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.clone">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clone</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.clone" title="Link to this definition">¶</a></dt>
<dd><p>Create and return a clone of the turtle with same position, heading and
turtle properties.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">mick</span> <span class="o">=</span> <span class="n">Turtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">joe</span> <span class="o">=</span> <span class="n">mick</span><span class="o">.</span><span class="n">clone</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.getturtle">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">getturtle</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.getturtle" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.getpen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">getpen</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.getpen" title="Link to this definition">¶</a></dt>
<dd><p>Return the Turtle object itself.  Only reasonable use: as a function to
return the “anonymous turtle”:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">pet</span> <span class="o">=</span> <span class="n">getturtle</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pet</span><span class="o">.</span><span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">pet</span>
<span class="go">&lt;turtle.Turtle object at 0x...&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.getscreen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">getscreen</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.getscreen" title="Link to this definition">¶</a></dt>
<dd><p>Return the <a class="reference internal" href="#turtle.TurtleScreen" title="turtle.TurtleScreen"><code class="xref py py-class docutils literal notranslate"><span class="pre">TurtleScreen</span></code></a> object the turtle is drawing on.
TurtleScreen methods can then be called for that object.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span> <span class="o">=</span> <span class="n">turtle</span><span class="o">.</span><span class="n">getscreen</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span>
<span class="go">&lt;turtle._Screen object at 0x...&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">(</span><span class="s2">&quot;pink&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.setundobuffer">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setundobuffer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setundobuffer" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>size</strong> – an integer or <code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
</dl>
<p>Set or disable undobuffer.  If <em>size</em> is an integer, an empty undobuffer of
given size is installed.  <em>size</em> gives the maximum number of turtle actions
that can be undone by the <a class="reference internal" href="#turtle.undo" title="turtle.undo"><code class="xref py py-func docutils literal notranslate"><span class="pre">undo()</span></code></a> method/function.  If <em>size</em> is
<code class="docutils literal notranslate"><span class="pre">None</span></code>, the undobuffer is disabled.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">setundobuffer</span><span class="p">(</span><span class="mi">42</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.undobufferentries">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">undobufferentries</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.undobufferentries" title="Link to this definition">¶</a></dt>
<dd><p>Return number of entries in the undobuffer.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">while</span> <span class="n">undobufferentries</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">undo</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="compound-shapes">
<span id="compoundshapes"></span><h3>Compound shapes<a class="headerlink" href="#compound-shapes" title="Link to this heading">¶</a></h3>
<p>To use compound turtle shapes, which consist of several polygons of different
color, you must use the helper class <a class="reference internal" href="#turtle.Shape" title="turtle.Shape"><code class="xref py py-class docutils literal notranslate"><span class="pre">Shape</span></code></a> explicitly as described
below:</p>
<ol class="arabic">
<li><p>Create an empty Shape object of type “compound”.</p></li>
<li><p>Add as many components to this object as desired, using the
<a class="reference internal" href="#turtle.Shape.addcomponent" title="turtle.Shape.addcomponent"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addcomponent()</span></code></a> method.</p>
<p>For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">Shape</span><span class="p">(</span><span class="s2">&quot;compound&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">poly1</span> <span class="o">=</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">),(</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">),(</span><span class="mi">0</span><span class="p">,</span><span class="mi">10</span><span class="p">),(</span><span class="o">-</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">addcomponent</span><span class="p">(</span><span class="n">poly1</span><span class="p">,</span> <span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="s2">&quot;blue&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">poly2</span> <span class="o">=</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">),(</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">),(</span><span class="o">-</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">addcomponent</span><span class="p">(</span><span class="n">poly2</span><span class="p">,</span> <span class="s2">&quot;blue&quot;</span><span class="p">,</span> <span class="s2">&quot;red&quot;</span><span class="p">)</span>
</pre></div>
</div>
</li>
<li><p>Now add the Shape to the Screen’s shapelist and use it:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">register_shape</span><span class="p">(</span><span class="s2">&quot;myshape&quot;</span><span class="p">,</span> <span class="n">s</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">shape</span><span class="p">(</span><span class="s2">&quot;myshape&quot;</span><span class="p">)</span>
</pre></div>
</div>
</li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <a class="reference internal" href="#turtle.Shape" title="turtle.Shape"><code class="xref py py-class docutils literal notranslate"><span class="pre">Shape</span></code></a> class is used internally by the <a class="reference internal" href="#turtle.register_shape" title="turtle.register_shape"><code class="xref py py-func docutils literal notranslate"><span class="pre">register_shape()</span></code></a>
method in different ways.  The application programmer has to deal with the
Shape class <em>only</em> when using compound shapes like shown above!</p>
</div>
</section>
</section>
<section id="methods-of-turtlescreen-screen-and-corresponding-functions">
<h2>Methods of TurtleScreen/Screen and corresponding functions<a class="headerlink" href="#methods-of-turtlescreen-screen-and-corresponding-functions" title="Link to this heading">¶</a></h2>
<p>Most of the examples in this section refer to a TurtleScreen instance called
<code class="docutils literal notranslate"><span class="pre">screen</span></code>.</p>
<section id="window-control">
<h3>Window control<a class="headerlink" href="#window-control" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.bgcolor">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">bgcolor</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.bgcolor" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>args</strong> – a color string or three numbers in the range 0..colormode or a
3-tuple of such numbers</p>
</dd>
</dl>
<p>Set or return background color of the TurtleScreen.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">(</span><span class="s2">&quot;orange&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">()</span>
<span class="go">&#39;orange&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">(</span><span class="s2">&quot;#800080&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">()</span>
<span class="go">(128.0, 0.0, 128.0)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.bgpic">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">bgpic</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">picname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.bgpic" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>picname</strong> – a string, name of a gif-file or <code class="docutils literal notranslate"><span class="pre">&quot;nopic&quot;</span></code>, or <code class="docutils literal notranslate"><span class="pre">None</span></code></p>
</dd>
</dl>
<p>Set background image or return name of current backgroundimage.  If <em>picname</em>
is a filename, set the corresponding image as background.  If <em>picname</em> is
<code class="docutils literal notranslate"><span class="pre">&quot;nopic&quot;</span></code>, delete background image, if present.  If <em>picname</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
return the filename of the current backgroundimage.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgpic</span><span class="p">()</span>
<span class="go">&#39;nopic&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgpic</span><span class="p">(</span><span class="s2">&quot;landscape.gif&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">bgpic</span><span class="p">()</span>
<span class="go">&quot;landscape.gif&quot;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clear</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span></dt>
<dd><div class="admonition note">
<p class="admonition-title">Note</p>
<p>This TurtleScreen method is available as a global function only under the
name <code class="docutils literal notranslate"><span class="pre">clearscreen</span></code>.  The global function <code class="docutils literal notranslate"><span class="pre">clear</span></code> is a different one
derived from the Turtle method <code class="docutils literal notranslate"><span class="pre">clear</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.clearscreen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">clearscreen</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.clearscreen" title="Link to this definition">¶</a></dt>
<dd><p>Delete all drawings and all turtles from the TurtleScreen.  Reset the now
empty TurtleScreen to its initial state: white background, no background
image, no event bindings and tracing on.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">reset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span></dt>
<dd><div class="admonition note">
<p class="admonition-title">Note</p>
<p>This TurtleScreen method is available as a global function only under the
name <code class="docutils literal notranslate"><span class="pre">resetscreen</span></code>.  The global function <code class="docutils literal notranslate"><span class="pre">reset</span></code> is another one
derived from the Turtle method <code class="docutils literal notranslate"><span class="pre">reset</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.resetscreen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">resetscreen</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.resetscreen" title="Link to this definition">¶</a></dt>
<dd><p>Reset all Turtles on the Screen to their initial state.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.screensize">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">screensize</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">canvwidth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">canvheight</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bg</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.screensize" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>canvwidth</strong> – positive integer, new width of canvas in pixels</p></li>
<li><p><strong>canvheight</strong> – positive integer, new height of canvas in pixels</p></li>
<li><p><strong>bg</strong> – colorstring or color-tuple, new background color</p></li>
</ul>
</dd>
</dl>
<p>If no arguments are given, return current (canvaswidth, canvasheight).  Else
resize the canvas the turtles are drawing on.  Do not alter the drawing
window.  To observe hidden parts of the canvas, use the scrollbars. With this
method, one can make visible those parts of a drawing which were outside the
canvas before.</p>
<div class="doctest highlight-default notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">screensize</span><span class="p">()</span>
<span class="go">(400, 300)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">screensize</span><span class="p">(</span><span class="mi">2000</span><span class="p">,</span><span class="mi">1500</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">screensize</span><span class="p">()</span>
<span class="go">(2000, 1500)</span>
</pre></div>
</div>
<p>e.g. to search for an erroneously escaped turtle ;-)</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.setworldcoordinates">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setworldcoordinates</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">llx</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">lly</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">urx</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ury</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setworldcoordinates" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>llx</strong> – a number, x-coordinate of lower left corner of canvas</p></li>
<li><p><strong>lly</strong> – a number, y-coordinate of lower left corner of canvas</p></li>
<li><p><strong>urx</strong> – a number, x-coordinate of upper right corner of canvas</p></li>
<li><p><strong>ury</strong> – a number, y-coordinate of upper right corner of canvas</p></li>
</ul>
</dd>
</dl>
<p>Set up user-defined coordinate system and switch to mode “world” if
necessary.  This performs a <code class="docutils literal notranslate"><span class="pre">screen.reset()</span></code>.  If mode “world” is already
active, all drawings are redrawn according to the new coordinates.</p>
<p><strong>ATTENTION</strong>: in user-defined coordinate systems angles may appear
distorted.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">reset</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">setworldcoordinates</span><span class="p">(</span><span class="o">-</span><span class="mi">50</span><span class="p">,</span><span class="o">-</span><span class="mf">7.5</span><span class="p">,</span><span class="mi">50</span><span class="p">,</span><span class="mf">7.5</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">72</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">left</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">8</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">left</span><span class="p">(</span><span class="mi">45</span><span class="p">);</span> <span class="n">fd</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>   <span class="c1"># a regular octagon</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="animation-control">
<h3>Animation control<a class="headerlink" href="#animation-control" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.delay">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">delay</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.delay" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>delay</strong> – positive integer</p>
</dd>
</dl>
<p>Set or return the drawing <em>delay</em> in milliseconds.  (This is approximately
the time interval between two consecutive canvas updates.)  The longer the
drawing delay, the slower the animation.</p>
<p>Optional argument:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">delay</span><span class="p">()</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">delay</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">delay</span><span class="p">()</span>
<span class="go">5</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.tracer">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">tracer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">delay</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.tracer" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>n</strong> – nonnegative integer</p></li>
<li><p><strong>delay</strong> – nonnegative integer</p></li>
</ul>
</dd>
</dl>
<p>Turn turtle animation on/off and set delay for update drawings.  If
<em>n</em> is given, only each n-th regular screen update is really
performed.  (Can be used to accelerate the drawing of complex
graphics.)  When called without arguments, returns the currently
stored value of n. Second argument sets delay value (see
<a class="reference internal" href="#turtle.delay" title="turtle.delay"><code class="xref py py-func docutils literal notranslate"><span class="pre">delay()</span></code></a>).</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">tracer</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">dist</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">200</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">fd</span><span class="p">(</span><span class="n">dist</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">rt</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">dist</span> <span class="o">+=</span> <span class="mi">2</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.update">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">update</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.update" title="Link to this definition">¶</a></dt>
<dd><p>Perform a TurtleScreen update. To be used when tracer is turned off.</p>
</dd></dl>

<p>See also the RawTurtle/Turtle method <a class="reference internal" href="#turtle.speed" title="turtle.speed"><code class="xref py py-func docutils literal notranslate"><span class="pre">speed()</span></code></a>.</p>
</section>
<section id="using-screen-events">
<h3>Using screen events<a class="headerlink" href="#using-screen-events" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.listen">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">listen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">xdummy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ydummy</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.listen" title="Link to this definition">¶</a></dt>
<dd><p>Set focus on TurtleScreen (in order to collect key-events).  Dummy arguments
are provided in order to be able to pass <a class="reference internal" href="#turtle.listen" title="turtle.listen"><code class="xref py py-func docutils literal notranslate"><span class="pre">listen()</span></code></a> to the onclick method.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.onkey">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onkey</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onkey" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.onkeyrelease">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onkeyrelease</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onkeyrelease" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with no arguments or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
<li><p><strong>key</strong> – a string: key (e.g. “a”) or key-symbol (e.g. “space”)</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to key-release event of key.  If <em>fun</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, event bindings
are removed. Remark: in order to be able to register key-events, TurtleScreen
must have the focus. (See method <a class="reference internal" href="#turtle.listen" title="turtle.listen"><code class="xref py py-func docutils literal notranslate"><span class="pre">listen()</span></code></a>.)</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">lt</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">onkey</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="s2">&quot;Up&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">listen</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.onkeypress">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onkeypress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">key</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onkeypress" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with no arguments or <code class="docutils literal notranslate"><span class="pre">None</span></code></p></li>
<li><p><strong>key</strong> – a string: key (e.g. “a”) or key-symbol (e.g. “space”)</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to key-press event of key if key is given,
or to any key-press-event if no key is given.
Remark: in order to be able to register key-events, TurtleScreen
must have focus. (See method <a class="reference internal" href="#turtle.listen" title="turtle.listen"><code class="xref py py-func docutils literal notranslate"><span class="pre">listen()</span></code></a>.)</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">onkey</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="s2">&quot;Up&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">listen</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.onclick">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onclick</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">btn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">add</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onclick" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.onscreenclick">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">onscreenclick</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">btn</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">add</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.onscreenclick" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with two arguments which will be called with the
coordinates of the clicked point on the canvas</p></li>
<li><p><strong>btn</strong> – number of the mouse-button, defaults to 1 (left mouse button)</p></li>
<li><p><strong>add</strong> – <code class="docutils literal notranslate"><span class="pre">True</span></code> or <code class="docutils literal notranslate"><span class="pre">False</span></code> – if <code class="docutils literal notranslate"><span class="pre">True</span></code>, a new binding will be
added, otherwise it will replace a former binding</p></li>
</ul>
</dd>
</dl>
<p>Bind <em>fun</em> to mouse-click events on this screen.  If <em>fun</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>,
existing bindings are removed.</p>
<p>Example for a TurtleScreen instance named <code class="docutils literal notranslate"><span class="pre">screen</span></code> and a Turtle instance
named <code class="docutils literal notranslate"><span class="pre">turtle</span></code>:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">onclick</span><span class="p">(</span><span class="n">turtle</span><span class="o">.</span><span class="n">goto</span><span class="p">)</span> <span class="c1"># Subsequently clicking into the TurtleScreen will</span>
<span class="gp">&gt;&gt;&gt; </span>                            <span class="c1"># make the turtle move to the clicked point.</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">onclick</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>        <span class="c1"># remove event binding again</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This TurtleScreen method is available as a global function only under the
name <code class="docutils literal notranslate"><span class="pre">onscreenclick</span></code>.  The global function <code class="docutils literal notranslate"><span class="pre">onclick</span></code> is another one
derived from the Turtle method <code class="docutils literal notranslate"><span class="pre">onclick</span></code>.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.ontimer">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">ontimer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fun</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">t</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.ontimer" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fun</strong> – a function with no arguments</p></li>
<li><p><strong>t</strong> – a number &gt;= 0</p></li>
</ul>
</dd>
</dl>
<p>Install a timer that calls <em>fun</em> after <em>t</em> milliseconds.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">running</span> <span class="o">=</span> <span class="kc">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">f</span><span class="p">():</span>
<span class="gp">... </span>    <span class="k">if</span> <span class="n">running</span><span class="p">:</span>
<span class="gp">... </span>        <span class="n">fd</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="gp">... </span>        <span class="n">lt</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="gp">... </span>        <span class="n">screen</span><span class="o">.</span><span class="n">ontimer</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="mi">250</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">f</span><span class="p">()</span>   <span class="c1">### makes the turtle march around</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">running</span> <span class="o">=</span> <span class="kc">False</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.mainloop">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">mainloop</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.mainloop" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.done">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">done</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.done" title="Link to this definition">¶</a></dt>
<dd><p>Starts event loop - calling Tkinter’s mainloop function.
Must be the last statement in a turtle graphics program.
Must <em>not</em> be used if a script is run from within IDLE in -n mode
(No subprocess) - for interactive use of turtle graphics.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">mainloop</span><span class="p">()</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="input-methods">
<h3>Input methods<a class="headerlink" href="#input-methods" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.textinput">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">textinput</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prompt</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.textinput" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>title</strong> – string</p></li>
<li><p><strong>prompt</strong> – string</p></li>
</ul>
</dd>
</dl>
<p>Pop up a dialog window for input of a string. Parameter title is
the title of the dialog window, prompt is a text mostly describing
what information to input.
Return the string input. If the dialog is canceled, return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">textinput</span><span class="p">(</span><span class="s2">&quot;NIM&quot;</span><span class="p">,</span> <span class="s2">&quot;Name of first player:&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.numinput">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">numinput</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">title</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prompt</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">minval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">maxval</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.numinput" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>title</strong> – string</p></li>
<li><p><strong>prompt</strong> – string</p></li>
<li><p><strong>default</strong> – number (optional)</p></li>
<li><p><strong>minval</strong> – number (optional)</p></li>
<li><p><strong>maxval</strong> – number (optional)</p></li>
</ul>
</dd>
</dl>
<p>Pop up a dialog window for input of a number. title is the title of the
dialog window, prompt is a text mostly describing what numerical information
to input. default: default value, minval: minimum value for input,
maxval: maximum value for input.
The number input must be in the range minval .. maxval if these are
given. If not, a hint is issued and the dialog remains open for
correction.
Return the number input. If the dialog is canceled,  return <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">numinput</span><span class="p">(</span><span class="s2">&quot;Poker&quot;</span><span class="p">,</span> <span class="s2">&quot;Your stakes:&quot;</span><span class="p">,</span> <span class="mi">1000</span><span class="p">,</span> <span class="n">minval</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">maxval</span><span class="o">=</span><span class="mi">10000</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="settings-and-special-methods">
<h3>Settings and special methods<a class="headerlink" href="#settings-and-special-methods" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.mode">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">mode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.mode" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>mode</strong> – one of the strings “standard”, “logo” or “world”</p>
</dd>
</dl>
<p>Set turtle mode (“standard”, “logo” or “world”) and perform reset.  If mode
is not given, current mode is returned.</p>
<p>Mode “standard” is compatible with old <a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a>.  Mode “logo” is
compatible with most Logo turtle graphics.  Mode “world” uses user-defined
“world coordinates”. <strong>Attention</strong>: in this mode angles appear distorted if
<code class="docutils literal notranslate"><span class="pre">x/y</span></code> unit-ratio doesn’t equal 1.</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Mode</p></th>
<th class="head"><p>Initial turtle heading</p></th>
<th class="head"><p>positive angles</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“standard”</p></td>
<td><p>to the right (east)</p></td>
<td><p>counterclockwise</p></td>
</tr>
<tr class="row-odd"><td><p>“logo”</p></td>
<td><p>upward    (north)</p></td>
<td><p>clockwise</p></td>
</tr>
</tbody>
</table>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">mode</span><span class="p">(</span><span class="s2">&quot;logo&quot;</span><span class="p">)</span>   <span class="c1"># resets turtle heading to north</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">mode</span><span class="p">()</span>
<span class="go">&#39;logo&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.colormode">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">colormode</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cmode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.colormode" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>cmode</strong> – one of the values 1.0 or 255</p>
</dd>
</dl>
<p>Return the colormode or set it to 1.0 or 255.  Subsequently <em>r</em>, <em>g</em>, <em>b</em>
values of color triples have to be in the range 0..*cmode*.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">colormode</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">(</span><span class="mi">240</span><span class="p">,</span> <span class="mi">160</span><span class="p">,</span> <span class="mi">80</span><span class="p">)</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">    </span> <span class="o">...</span>
<span class="gr">TurtleGraphicsError</span>: <span class="n">bad color sequence: (240, 160, 80)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">colormode</span><span class="p">()</span>
<span class="go">1.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">colormode</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">colormode</span><span class="p">()</span>
<span class="go">255</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">turtle</span><span class="o">.</span><span class="n">pencolor</span><span class="p">(</span><span class="mi">240</span><span class="p">,</span><span class="mi">160</span><span class="p">,</span><span class="mi">80</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.getcanvas">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">getcanvas</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.getcanvas" title="Link to this definition">¶</a></dt>
<dd><p>Return the Canvas of this TurtleScreen.  Useful for insiders who know what to
do with a Tkinter Canvas.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">cv</span> <span class="o">=</span> <span class="n">screen</span><span class="o">.</span><span class="n">getcanvas</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cv</span>
<span class="go">&lt;turtle.ScrolledCanvas object ...&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.getshapes">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">getshapes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.getshapes" title="Link to this definition">¶</a></dt>
<dd><p>Return a list of names of all currently available turtle shapes.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">getshapes</span><span class="p">()</span>
<span class="go">[&#39;arrow&#39;, &#39;blank&#39;, &#39;circle&#39;, ..., &#39;turtle&#39;]</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.register_shape">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">register_shape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.register_shape" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.addshape">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">addshape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shape</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.addshape" title="Link to this definition">¶</a></dt>
<dd><p>There are three different ways to call this function:</p>
<ol class="arabic">
<li><p><em>name</em> is the name of a gif-file and <em>shape</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>: Install the
corresponding image shape.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">register_shape</span><span class="p">(</span><span class="s2">&quot;turtle.gif&quot;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Image shapes <em>do not</em> rotate when turning the turtle, so they do not
display the heading of the turtle!</p>
</div>
</li>
<li><p><em>name</em> is an arbitrary string and <em>shape</em> is a tuple of pairs of
coordinates: Install the corresponding polygon shape.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">register_shape</span><span class="p">(</span><span class="s2">&quot;triangle&quot;</span><span class="p">,</span> <span class="p">((</span><span class="mi">5</span><span class="p">,</span><span class="o">-</span><span class="mi">3</span><span class="p">),</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">5</span><span class="p">),</span> <span class="p">(</span><span class="o">-</span><span class="mi">5</span><span class="p">,</span><span class="o">-</span><span class="mi">3</span><span class="p">)))</span>
</pre></div>
</div>
</li>
<li><p><em>name</em> is an arbitrary string and <em>shape</em> is a (compound) <a class="reference internal" href="#turtle.Shape" title="turtle.Shape"><code class="xref py py-class docutils literal notranslate"><span class="pre">Shape</span></code></a>
object: Install the corresponding compound shape.</p></li>
</ol>
<p>Add a turtle shape to TurtleScreen’s shapelist.  Only thusly registered
shapes can be used by issuing the command <code class="docutils literal notranslate"><span class="pre">shape(shapename)</span></code>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.turtles">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">turtles</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.turtles" title="Link to this definition">¶</a></dt>
<dd><p>Return the list of turtles on the screen.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">turtle</span> <span class="ow">in</span> <span class="n">screen</span><span class="o">.</span><span class="n">turtles</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">turtle</span><span class="o">.</span><span class="n">color</span><span class="p">(</span><span class="s2">&quot;red&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.window_height">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">window_height</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.window_height" title="Link to this definition">¶</a></dt>
<dd><p>Return the height of the turtle window.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">window_height</span><span class="p">()</span>
<span class="go">480</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.window_width">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">window_width</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.window_width" title="Link to this definition">¶</a></dt>
<dd><p>Return the width of the turtle window.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">window_width</span><span class="p">()</span>
<span class="go">640</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="methods-specific-to-screen-not-inherited-from-turtlescreen">
<span id="screenspecific"></span><h3>Methods specific to Screen, not inherited from TurtleScreen<a class="headerlink" href="#methods-specific-to-screen-not-inherited-from-turtlescreen" title="Link to this heading">¶</a></h3>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.bye">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">bye</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.bye" title="Link to this definition">¶</a></dt>
<dd><p>Shut the turtlegraphics window.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.exitonclick">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">exitonclick</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#turtle.exitonclick" title="Link to this definition">¶</a></dt>
<dd><p>Bind <code class="docutils literal notranslate"><span class="pre">bye()</span></code> method to mouse clicks on the Screen.</p>
<p>If the value “using_IDLE” in the configuration dictionary is <code class="docutils literal notranslate"><span class="pre">False</span></code>
(default value), also enter mainloop.  Remark: If IDLE with the <code class="docutils literal notranslate"><span class="pre">-n</span></code> switch
(no subprocess) is used, this value should be set to <code class="docutils literal notranslate"><span class="pre">True</span></code> in
<code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code>.  In this case IDLE’s own mainloop is active also for the
client script.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.setup">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">setup</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_CFG['width']</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">height</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_CFG['height']</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">startx</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_CFG['leftright']</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">starty</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">_CFG['topbottom']</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.setup" title="Link to this definition">¶</a></dt>
<dd><p>Set the size and position of the main window.  Default values of arguments
are stored in the configuration dictionary and can be changed via a
<code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code> file.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>width</strong> – if an integer, a size in pixels, if a float, a fraction of the
screen; default is 50% of screen</p></li>
<li><p><strong>height</strong> – if an integer, the height in pixels, if a float, a fraction of
the screen; default is 75% of screen</p></li>
<li><p><strong>startx</strong> – if positive, starting position in pixels from the left
edge of the screen, if negative from the right edge, if <code class="docutils literal notranslate"><span class="pre">None</span></code>,
center window horizontally</p></li>
<li><p><strong>starty</strong> – if positive, starting position in pixels from the top
edge of the screen, if negative from the bottom edge, if <code class="docutils literal notranslate"><span class="pre">None</span></code>,
center window vertically</p></li>
</ul>
</dd>
</dl>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">setup</span> <span class="p">(</span><span class="n">width</span><span class="o">=</span><span class="mi">200</span><span class="p">,</span> <span class="n">height</span><span class="o">=</span><span class="mi">200</span><span class="p">,</span> <span class="n">startx</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">starty</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span>             <span class="c1"># sets window to 200x200 pixels, in upper left of screen</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">setup</span><span class="p">(</span><span class="n">width</span><span class="o">=</span><span class="mf">.75</span><span class="p">,</span> <span class="n">height</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">startx</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">starty</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span>             <span class="c1"># sets window to 75% of screen by 50% of screen and centers</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="turtle.title">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">title</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">titlestring</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.title" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>titlestring</strong> – a string that is shown in the titlebar of the turtle
graphics window</p>
</dd>
</dl>
<p>Set title of turtle window to <em>titlestring</em>.</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">screen</span><span class="o">.</span><span class="n">title</span><span class="p">(</span><span class="s2">&quot;Welcome to the turtle zoo!&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>
<section id="public-classes">
<h2>Public classes<a class="headerlink" href="#public-classes" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="turtle.RawTurtle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">RawTurtle</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">canvas</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.RawTurtle" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="turtle.RawPen">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">RawPen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">canvas</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.RawPen" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>canvas</strong> – a <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Canvas</span></code>, a <a class="reference internal" href="#turtle.ScrolledCanvas" title="turtle.ScrolledCanvas"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScrolledCanvas</span></code></a> or a
<a class="reference internal" href="#turtle.TurtleScreen" title="turtle.TurtleScreen"><code class="xref py py-class docutils literal notranslate"><span class="pre">TurtleScreen</span></code></a></p>
</dd>
</dl>
<p>Create a turtle.  The turtle has all methods described above as “methods of
Turtle/RawTurtle”.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.Turtle">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">Turtle</span></span><a class="headerlink" href="#turtle.Turtle" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of RawTurtle, has the same interface but draws on a default
<a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a> object created automatically when needed for the first time.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.TurtleScreen">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">TurtleScreen</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">cv</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.TurtleScreen" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>cv</strong> – a <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Canvas</span></code></p>
</dd>
</dl>
<p>Provides screen oriented methods like <a class="reference internal" href="#turtle.bgcolor" title="turtle.bgcolor"><code class="xref py py-func docutils literal notranslate"><span class="pre">bgcolor()</span></code></a> etc. that are described
above.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.Screen">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">Screen</span></span><a class="headerlink" href="#turtle.Screen" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of TurtleScreen, with <a class="reference internal" href="#screenspecific"><span class="std std-ref">four methods added</span></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.ScrolledCanvas">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">ScrolledCanvas</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">master</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.ScrolledCanvas" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>master</strong> – some Tkinter widget to contain the ScrolledCanvas, i.e.
a Tkinter-canvas with scrollbars added</p>
</dd>
</dl>
<p>Used by class Screen, which thus automatically provides a ScrolledCanvas as
playground for the turtles.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.Shape">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">Shape</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type_</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.Shape" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>type_</strong> – one of the strings “polygon”, “image”, “compound”</p>
</dd>
</dl>
<p>Data structure modeling shapes.  The pair <code class="docutils literal notranslate"><span class="pre">(type_,</span> <span class="pre">data)</span></code> must follow this
specification:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p><em>type_</em></p></th>
<th class="head"><p><em>data</em></p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>“polygon”</p></td>
<td><p>a polygon-tuple, i.e. a tuple of pairs of coordinates</p></td>
</tr>
<tr class="row-odd"><td><p>“image”</p></td>
<td><p>an image  (in this form only used internally!)</p></td>
</tr>
<tr class="row-even"><td><p>“compound”</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">None</span></code> (a compound shape has to be constructed using the
<a class="reference internal" href="#turtle.Shape.addcomponent" title="turtle.Shape.addcomponent"><code class="xref py py-meth docutils literal notranslate"><span class="pre">addcomponent()</span></code></a> method)</p></td>
</tr>
</tbody>
</table>
<dl class="py method">
<dt class="sig sig-object py" id="turtle.Shape.addcomponent">
<span class="sig-name descname"><span class="pre">addcomponent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">poly</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fill</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">outline</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.Shape.addcomponent" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>poly</strong> – a polygon, i.e. a tuple of pairs of numbers</p></li>
<li><p><strong>fill</strong> – a color the <em>poly</em> will be filled with</p></li>
<li><p><strong>outline</strong> – a color for the poly’s outline (if given)</p></li>
</ul>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">poly</span> <span class="o">=</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">),(</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">),(</span><span class="mi">0</span><span class="p">,</span><span class="mi">10</span><span class="p">),(</span><span class="o">-</span><span class="mi">10</span><span class="p">,</span><span class="o">-</span><span class="mi">5</span><span class="p">))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">Shape</span><span class="p">(</span><span class="s2">&quot;compound&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="o">.</span><span class="n">addcomponent</span><span class="p">(</span><span class="n">poly</span><span class="p">,</span> <span class="s2">&quot;red&quot;</span><span class="p">,</span> <span class="s2">&quot;blue&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="c1"># ... add more components and then use register_shape()</span>
</pre></div>
</div>
<p>See <a class="reference internal" href="#compoundshapes"><span class="std std-ref">Compound shapes</span></a>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="turtle.Vec2D">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">Vec2D</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">y</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.Vec2D" title="Link to this definition">¶</a></dt>
<dd><p>A two-dimensional vector class, used as a helper class for implementing
turtle graphics.  May be useful for turtle graphics programs too.  Derived
from tuple, so a vector is a tuple!</p>
<p>Provides (for <em>a</em>, <em>b</em> vectors, <em>k</em> number):</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">+</span> <span class="pre">b</span></code> vector addition</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">-</span> <span class="pre">b</span></code> vector subtraction</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">*</span> <span class="pre">b</span></code> inner product</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">k</span> <span class="pre">*</span> <span class="pre">a</span></code> and <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">*</span> <span class="pre">k</span></code> multiplication with scalar</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">abs(a)</span></code> absolute value of a</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">a.rotate(angle)</span></code> rotation</p></li>
</ul>
</dd></dl>

</section>
<section id="explanation">
<span id="turtle-explanation"></span><h2>Explanation<a class="headerlink" href="#explanation" title="Link to this heading">¶</a></h2>
<p>A turtle object draws on a screen object, and there a number of key classes in
the turtle object-oriented interface that can be used to create them and relate
them to each other.</p>
<p>A <a class="reference internal" href="#turtle.Turtle" title="turtle.Turtle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Turtle</span></code></a> instance will automatically create a <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a>
instance if one is not already present.</p>
<p><code class="docutils literal notranslate"><span class="pre">Turtle</span></code> is a subclass of <a class="reference internal" href="#turtle.RawTurtle" title="turtle.RawTurtle"><code class="xref py py-class docutils literal notranslate"><span class="pre">RawTurtle</span></code></a>, which <em>doesn’t</em> automatically
create a drawing surface - a <em>canvas</em> will need to be provided or created for
it. The <em>canvas</em> can be a <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Canvas</span></code>, <a class="reference internal" href="#turtle.ScrolledCanvas" title="turtle.ScrolledCanvas"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScrolledCanvas</span></code></a>
or <a class="reference internal" href="#turtle.TurtleScreen" title="turtle.TurtleScreen"><code class="xref py py-class docutils literal notranslate"><span class="pre">TurtleScreen</span></code></a>.</p>
<p><a class="reference internal" href="#turtle.TurtleScreen" title="turtle.TurtleScreen"><code class="xref py py-class docutils literal notranslate"><span class="pre">TurtleScreen</span></code></a> is the basic drawing surface for a
turtle. <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a> is a subclass of <code class="docutils literal notranslate"><span class="pre">TurtleScreen</span></code>, and
includes <a class="reference internal" href="#screenspecific"><span class="std std-ref">some additional methods</span></a> for managing its
appearance (including size and title) and behaviour. <code class="docutils literal notranslate"><span class="pre">TurtleScreen</span></code>’s
constructor needs a <code class="xref py py-class docutils literal notranslate"><span class="pre">tkinter.Canvas</span></code> or a
<a class="reference internal" href="#turtle.ScrolledCanvas" title="turtle.ScrolledCanvas"><code class="xref py py-class docutils literal notranslate"><span class="pre">ScrolledCanvas</span></code></a> as an argument.</p>
<p>The functional interface for turtle graphics uses the various methods of
<code class="docutils literal notranslate"><span class="pre">Turtle</span></code> and <code class="docutils literal notranslate"><span class="pre">TurtleScreen</span></code>/<code class="docutils literal notranslate"><span class="pre">Screen</span></code>. Behind the scenes, a screen
object is automatically created whenever a function derived from a <code class="docutils literal notranslate"><span class="pre">Screen</span></code>
method is called. Similarly, a turtle object is automatically created
whenever any of the functions derived from a Turtle method is called.</p>
<p>To use multiple turtles on a screen, the object-oriented interface must be
used.</p>
</section>
<section id="help-and-configuration">
<h2>Help and configuration<a class="headerlink" href="#help-and-configuration" title="Link to this heading">¶</a></h2>
<section id="how-to-use-help">
<h3>How to use help<a class="headerlink" href="#how-to-use-help" title="Link to this heading">¶</a></h3>
<p>The public methods of the Screen and Turtle classes are documented extensively
via docstrings.  So these can be used as online-help via the Python help
facilities:</p>
<ul>
<li><p>When using IDLE, tooltips show the signatures and first lines of the
docstrings of typed in function-/method calls.</p></li>
<li><p>Calling <a class="reference internal" href="functions.html#help" title="help"><code class="xref py py-func docutils literal notranslate"><span class="pre">help()</span></code></a> on methods or functions displays the docstrings:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="n">Screen</span><span class="o">.</span><span class="n">bgcolor</span><span class="p">)</span>
<span class="go">Help on method bgcolor in module turtle:</span>

<span class="go">bgcolor(self, *args) unbound turtle.Screen method</span>
<span class="go">    Set or return backgroundcolor of the TurtleScreen.</span>

<span class="go">    Arguments (if given): a color string or three numbers</span>
<span class="go">    in the range 0..colormode or a 3-tuple of such numbers.</span>


<span class="go">    &gt;&gt;&gt; screen.bgcolor(&quot;orange&quot;)</span>
<span class="go">    &gt;&gt;&gt; screen.bgcolor()</span>
<span class="go">    &quot;orange&quot;</span>
<span class="go">    &gt;&gt;&gt; screen.bgcolor(0.5,0,0.5)</span>
<span class="go">    &gt;&gt;&gt; screen.bgcolor()</span>
<span class="go">    &quot;#800080&quot;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="n">Turtle</span><span class="o">.</span><span class="n">penup</span><span class="p">)</span>
<span class="go">Help on method penup in module turtle:</span>

<span class="go">penup(self) unbound turtle.Turtle method</span>
<span class="go">    Pull the pen up -- no drawing when moving.</span>

<span class="go">    Aliases: penup | pu | up</span>

<span class="go">    No argument</span>

<span class="go">    &gt;&gt;&gt; turtle.penup()</span>
</pre></div>
</div>
</li>
<li><p>The docstrings of the functions which are derived from methods have a modified
form:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="n">bgcolor</span><span class="p">)</span>
<span class="go">Help on function bgcolor in module turtle:</span>

<span class="go">bgcolor(*args)</span>
<span class="go">    Set or return backgroundcolor of the TurtleScreen.</span>

<span class="go">    Arguments (if given): a color string or three numbers</span>
<span class="go">    in the range 0..colormode or a 3-tuple of such numbers.</span>

<span class="go">    Example::</span>

<span class="go">      &gt;&gt;&gt; bgcolor(&quot;orange&quot;)</span>
<span class="go">      &gt;&gt;&gt; bgcolor()</span>
<span class="go">      &quot;orange&quot;</span>
<span class="go">      &gt;&gt;&gt; bgcolor(0.5,0,0.5)</span>
<span class="go">      &gt;&gt;&gt; bgcolor()</span>
<span class="go">      &quot;#800080&quot;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">help</span><span class="p">(</span><span class="n">penup</span><span class="p">)</span>
<span class="go">Help on function penup in module turtle:</span>

<span class="go">penup()</span>
<span class="go">    Pull the pen up -- no drawing when moving.</span>

<span class="go">    Aliases: penup | pu | up</span>

<span class="go">    No argument</span>

<span class="go">    Example:</span>
<span class="go">    &gt;&gt;&gt; penup()</span>
</pre></div>
</div>
</li>
</ul>
<p>These modified docstrings are created automatically together with the function
definitions that are derived from the methods at import time.</p>
</section>
<section id="translation-of-docstrings-into-different-languages">
<h3>Translation of docstrings into different languages<a class="headerlink" href="#translation-of-docstrings-into-different-languages" title="Link to this heading">¶</a></h3>
<p>There is a utility to create a dictionary the keys of which are the method names
and the values of which are the docstrings of the public methods of the classes
Screen and Turtle.</p>
<dl class="py function">
<dt class="sig sig-object py" id="turtle.write_docstringdict">
<span class="sig-prename descclassname"><span class="pre">turtle.</span></span><span class="sig-name descname"><span class="pre">write_docstringdict</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'turtle_docstringdict'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#turtle.write_docstringdict" title="Link to this definition">¶</a></dt>
<dd><dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>filename</strong> – a string, used as filename</p>
</dd>
</dl>
<p>Create and write docstring-dictionary to a Python script with the given
filename.  This function has to be called explicitly (it is not used by the
turtle graphics classes).  The docstring dictionary will be written to the
Python script <code class="file docutils literal notranslate"><em><span class="pre">filename</span></em><span class="pre">.py</span></code>.  It is intended to serve as a template
for translation of the docstrings into different languages.</p>
</dd></dl>

<p>If you (or your students) want to use <a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a> with online help in your
native language, you have to translate the docstrings and save the resulting
file as e.g. <code class="file docutils literal notranslate"><span class="pre">turtle_docstringdict_german.py</span></code>.</p>
<p>If you have an appropriate entry in your <code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code> file this dictionary
will be read in at import time and will replace the original English docstrings.</p>
<p>At the time of this writing there are docstring dictionaries in German and in
Italian.  (Requests please to <a class="reference external" href="mailto:glingl&#37;&#52;&#48;aon&#46;at">glingl<span>&#64;</span>aon<span>&#46;</span>at</a>.)</p>
</section>
<section id="how-to-configure-screen-and-turtles">
<h3>How to configure Screen and Turtles<a class="headerlink" href="#how-to-configure-screen-and-turtles" title="Link to this heading">¶</a></h3>
<p>The built-in default configuration mimics the appearance and behaviour of the
old turtle module in order to retain best possible compatibility with it.</p>
<p>If you want to use a different configuration which better reflects the features
of this module or which better fits to your needs, e.g. for use in a classroom,
you can prepare a configuration file <code class="docutils literal notranslate"><span class="pre">turtle.cfg</span></code> which will be read at import
time and modify the configuration according to its settings.</p>
<p>The built in configuration would correspond to the following <code class="docutils literal notranslate"><span class="pre">turtle.cfg</span></code>:</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="na">width</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">0.5</span>
<span class="na">height</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">0.75</span>
<span class="na">leftright</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">None</span>
<span class="na">topbottom</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">None</span>
<span class="na">canvwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">400</span>
<span class="na">canvheight</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">300</span>
<span class="na">mode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">standard</span>
<span class="na">colormode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">1.0</span>
<span class="na">delay</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">10</span>
<span class="na">undobuffersize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">1000</span>
<span class="na">shape</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">classic</span>
<span class="na">pencolor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">black</span>
<span class="na">fillcolor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">black</span>
<span class="na">resizemode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">noresize</span>
<span class="na">visible</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">True</span>
<span class="na">language</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">english</span>
<span class="na">exampleturtle</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">turtle</span>
<span class="na">examplescreen</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">screen</span>
<span class="na">title</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">Python Turtle Graphics</span>
<span class="na">using_IDLE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">False</span>
</pre></div>
</div>
<p>Short explanation of selected entries:</p>
<ul class="simple">
<li><p>The first four lines correspond to the arguments of the <a class="reference internal" href="#turtle.setup" title="turtle.setup"><code class="xref py py-func docutils literal notranslate"><span class="pre">Screen.setup</span></code></a>
method.</p></li>
<li><p>Line 5 and 6 correspond to the arguments of the method
<a class="reference internal" href="#turtle.screensize" title="turtle.screensize"><code class="xref py py-func docutils literal notranslate"><span class="pre">Screen.screensize</span></code></a>.</p></li>
<li><p><em>shape</em> can be any of the built-in shapes, e.g: arrow, turtle, etc.  For more
info try <code class="docutils literal notranslate"><span class="pre">help(shape)</span></code>.</p></li>
<li><p>If you want to use no fill color (i.e. make the turtle transparent), you have
to write <code class="docutils literal notranslate"><span class="pre">fillcolor</span> <span class="pre">=</span> <span class="pre">&quot;&quot;</span></code> (but all nonempty strings must not have quotes in
the cfg file).</p></li>
<li><p>If you want to reflect the turtle its state, you have to use <code class="docutils literal notranslate"><span class="pre">resizemode</span> <span class="pre">=</span>
<span class="pre">auto</span></code>.</p></li>
<li><p>If you set e.g. <code class="docutils literal notranslate"><span class="pre">language</span> <span class="pre">=</span> <span class="pre">italian</span></code> the docstringdict
<code class="file docutils literal notranslate"><span class="pre">turtle_docstringdict_italian.py</span></code> will be loaded at import time (if
present on the import path, e.g. in the same directory as <a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a>).</p></li>
<li><p>The entries <em>exampleturtle</em> and <em>examplescreen</em> define the names of these
objects as they occur in the docstrings.  The transformation of
method-docstrings to function-docstrings will delete these names from the
docstrings.</p></li>
<li><p><em>using_IDLE</em>: Set this to <code class="docutils literal notranslate"><span class="pre">True</span></code> if you regularly work with IDLE and its <code class="docutils literal notranslate"><span class="pre">-n</span></code>
switch (“no subprocess”).  This will prevent <a class="reference internal" href="#turtle.exitonclick" title="turtle.exitonclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">exitonclick()</span></code></a> to enter the
mainloop.</p></li>
</ul>
<p>There can be a <code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code> file in the directory where <a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a> is
stored and an additional one in the current working directory.  The latter will
override the settings of the first one.</p>
<p>The <code class="file docutils literal notranslate"><span class="pre">Lib/turtledemo</span></code> directory contains a <code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code> file.  You can
study it as an example and see its effects when running the demos (preferably
not from within the demo-viewer).</p>
</section>
</section>
<section id="module-turtledemo">
<span id="turtledemo-demo-scripts"></span><h2><a class="reference internal" href="#module-turtledemo" title="turtledemo: A viewer for example turtle scripts"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtledemo</span></code></a> — Demo scripts<a class="headerlink" href="#module-turtledemo" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-turtledemo" title="turtledemo: A viewer for example turtle scripts"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtledemo</span></code></a> package includes a set of demo scripts.  These
scripts can be run and viewed using the supplied demo viewer as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">turtledemo</span>
</pre></div>
</div>
<p>Alternatively, you can run the demo scripts individually.  For example,</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">python</span> <span class="o">-</span><span class="n">m</span> <span class="n">turtledemo</span><span class="o">.</span><span class="n">bytedesign</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#module-turtledemo" title="turtledemo: A viewer for example turtle scripts"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtledemo</span></code></a> package directory contains:</p>
<ul class="simple">
<li><p>A demo viewer <code class="file docutils literal notranslate"><span class="pre">__main__.py</span></code> which can be used to view the sourcecode
of the scripts and run them at the same time.</p></li>
<li><p>Multiple scripts demonstrating different features of the <a class="reference internal" href="#module-turtle" title="turtle: An educational framework for simple graphics applications"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code></a>
module.  Examples can be accessed via the Examples menu.  They can also
be run standalone.</p></li>
<li><p>A <code class="file docutils literal notranslate"><span class="pre">turtle.cfg</span></code> file which serves as an example of how to write
and use such files.</p></li>
</ul>
<p>The demo scripts are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Name</p></th>
<th class="head"><p>Description</p></th>
<th class="head"><p>Features</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>bytedesign</p></td>
<td><p>complex classical
turtle graphics pattern</p></td>
<td><p><a class="reference internal" href="#turtle.tracer" title="turtle.tracer"><code class="xref py py-func docutils literal notranslate"><span class="pre">tracer()</span></code></a>, delay,
<a class="reference internal" href="#turtle.update" title="turtle.update"><code class="xref py py-func docutils literal notranslate"><span class="pre">update()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p>chaos</p></td>
<td><p>graphs Verhulst dynamics,
shows that computer’s
computations can generate
results sometimes against the
common sense expectations</p></td>
<td><p>world coordinates</p></td>
</tr>
<tr class="row-even"><td><p>clock</p></td>
<td><p>analog clock showing time
of your computer</p></td>
<td><p>turtles as clock’s
hands, ontimer</p></td>
</tr>
<tr class="row-odd"><td><p>colormixer</p></td>
<td><p>experiment with r, g, b</p></td>
<td><p><a class="reference internal" href="#turtle.ondrag" title="turtle.ondrag"><code class="xref py py-func docutils literal notranslate"><span class="pre">ondrag()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>forest</p></td>
<td><p>3 breadth-first trees</p></td>
<td><p>randomization</p></td>
</tr>
<tr class="row-odd"><td><p>fractalcurves</p></td>
<td><p>Hilbert &amp; Koch curves</p></td>
<td><p>recursion</p></td>
</tr>
<tr class="row-even"><td><p>lindenmayer</p></td>
<td><p>ethnomathematics
(indian kolams)</p></td>
<td><p>L-System</p></td>
</tr>
<tr class="row-odd"><td><p>minimal_hanoi</p></td>
<td><p>Towers of Hanoi</p></td>
<td><p>Rectangular Turtles
as Hanoi discs
(shape, shapesize)</p></td>
</tr>
<tr class="row-even"><td><p>nim</p></td>
<td><p>play the classical nim game
with three heaps of sticks
against the computer.</p></td>
<td><p>turtles as nimsticks,
event driven (mouse,
keyboard)</p></td>
</tr>
<tr class="row-odd"><td><p>paint</p></td>
<td><p>super minimalistic
drawing program</p></td>
<td><p><a class="reference internal" href="#turtle.onclick" title="turtle.onclick"><code class="xref py py-func docutils literal notranslate"><span class="pre">onclick()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>peace</p></td>
<td><p>elementary</p></td>
<td><p>turtle: appearance
and animation</p></td>
</tr>
<tr class="row-odd"><td><p>penrose</p></td>
<td><p>aperiodic tiling with
kites and darts</p></td>
<td><p><a class="reference internal" href="#turtle.stamp" title="turtle.stamp"><code class="xref py py-func docutils literal notranslate"><span class="pre">stamp()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>planet_and_moon</p></td>
<td><p>simulation of
gravitational system</p></td>
<td><p>compound shapes,
<a class="reference internal" href="#turtle.Vec2D" title="turtle.Vec2D"><code class="xref py py-class docutils literal notranslate"><span class="pre">Vec2D</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p>rosette</p></td>
<td><p>a pattern from the wikipedia
article on turtle graphics</p></td>
<td><p><a class="reference internal" href="#turtle.clone" title="turtle.clone"><code class="xref py py-func docutils literal notranslate"><span class="pre">clone()</span></code></a>,
<a class="reference internal" href="#turtle.undo" title="turtle.undo"><code class="xref py py-func docutils literal notranslate"><span class="pre">undo()</span></code></a></p></td>
</tr>
<tr class="row-even"><td><p>round_dance</p></td>
<td><p>dancing turtles rotating
pairwise in opposite
direction</p></td>
<td><p>compound shapes, clone
shapesize, tilt,
get_shapepoly, update</p></td>
</tr>
<tr class="row-odd"><td><p>sorting_animate</p></td>
<td><p>visual demonstration of
different sorting methods</p></td>
<td><p>simple alignment,
randomization</p></td>
</tr>
<tr class="row-even"><td><p>tree</p></td>
<td><p>a (graphical) breadth
first tree (using generators)</p></td>
<td><p><a class="reference internal" href="#turtle.clone" title="turtle.clone"><code class="xref py py-func docutils literal notranslate"><span class="pre">clone()</span></code></a></p></td>
</tr>
<tr class="row-odd"><td><p>two_canvases</p></td>
<td><p>simple design</p></td>
<td><p>turtles on two
canvases</p></td>
</tr>
<tr class="row-even"><td><p>yinyang</p></td>
<td><p>another elementary example</p></td>
<td><p><a class="reference internal" href="#turtle.circle" title="turtle.circle"><code class="xref py py-func docutils literal notranslate"><span class="pre">circle()</span></code></a></p></td>
</tr>
</tbody>
</table>
<p>Have fun!</p>
</section>
<section id="changes-since-python-2-6">
<h2>Changes since Python 2.6<a class="headerlink" href="#changes-since-python-2-6" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>The methods <a class="reference internal" href="#turtle.tracer" title="turtle.tracer"><code class="xref py py-func docutils literal notranslate"><span class="pre">Turtle.tracer</span></code></a>, <a class="reference internal" href="#turtle.window_width" title="turtle.window_width"><code class="xref py py-func docutils literal notranslate"><span class="pre">Turtle.window_width</span></code></a> and
<a class="reference internal" href="#turtle.window_height" title="turtle.window_height"><code class="xref py py-func docutils literal notranslate"><span class="pre">Turtle.window_height</span></code></a> have been eliminated.
Methods with these names and functionality are now available only
as methods of <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a>. The functions derived from these remain
available. (In fact already in Python 2.6 these methods were merely
duplications of the corresponding
<a class="reference internal" href="#turtle.TurtleScreen" title="turtle.TurtleScreen"><code class="xref py py-class docutils literal notranslate"><span class="pre">TurtleScreen</span></code></a>/<a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a> methods.)</p></li>
<li><p>The method <code class="xref py py-func docutils literal notranslate"><span class="pre">Turtle.fill()</span></code> has been eliminated.
The behaviour of <a class="reference internal" href="#turtle.begin_fill" title="turtle.begin_fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">begin_fill()</span></code></a> and <a class="reference internal" href="#turtle.end_fill" title="turtle.end_fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">end_fill()</span></code></a>
have changed slightly: now every filling process must be completed with an
<code class="docutils literal notranslate"><span class="pre">end_fill()</span></code> call.</p></li>
<li><p>A method <a class="reference internal" href="#turtle.filling" title="turtle.filling"><code class="xref py py-func docutils literal notranslate"><span class="pre">Turtle.filling</span></code></a> has been added. It returns a boolean
value: <code class="docutils literal notranslate"><span class="pre">True</span></code> if a filling process is under way, <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.
This behaviour corresponds to a <code class="docutils literal notranslate"><span class="pre">fill()</span></code> call without arguments in
Python 2.6.</p></li>
</ul>
</section>
<section id="changes-since-python-3-0">
<h2>Changes since Python 3.0<a class="headerlink" href="#changes-since-python-3-0" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>The <a class="reference internal" href="#turtle.Turtle" title="turtle.Turtle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Turtle</span></code></a> methods <a class="reference internal" href="#turtle.shearfactor" title="turtle.shearfactor"><code class="xref py py-func docutils literal notranslate"><span class="pre">shearfactor()</span></code></a>, <a class="reference internal" href="#turtle.shapetransform" title="turtle.shapetransform"><code class="xref py py-func docutils literal notranslate"><span class="pre">shapetransform()</span></code></a> and
<a class="reference internal" href="#turtle.get_shapepoly" title="turtle.get_shapepoly"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_shapepoly()</span></code></a> have been added. Thus the full range of
regular linear transforms is now available for transforming turtle shapes.
<a class="reference internal" href="#turtle.tiltangle" title="turtle.tiltangle"><code class="xref py py-func docutils literal notranslate"><span class="pre">tiltangle()</span></code></a> has been enhanced in functionality: it now can
be used to get or set the tilt angle. <a class="reference internal" href="#turtle.settiltangle" title="turtle.settiltangle"><code class="xref py py-func docutils literal notranslate"><span class="pre">settiltangle()</span></code></a> has been
deprecated.</p></li>
<li><p>The <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a> method <a class="reference internal" href="#turtle.onkeypress" title="turtle.onkeypress"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkeypress()</span></code></a> has been added as a complement to
<a class="reference internal" href="#turtle.onkey" title="turtle.onkey"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkey()</span></code></a>. As the latter binds actions to the key release event,
an alias: <a class="reference internal" href="#turtle.onkeyrelease" title="turtle.onkeyrelease"><code class="xref py py-func docutils literal notranslate"><span class="pre">onkeyrelease()</span></code></a> was also added for it.</p></li>
<li><p>The method <a class="reference internal" href="#turtle.mainloop" title="turtle.mainloop"><code class="xref py py-func docutils literal notranslate"><span class="pre">Screen.mainloop</span></code></a> has been added,
so there is no longer a need to use the standalone <a class="reference internal" href="#turtle.mainloop" title="turtle.mainloop"><code class="xref py py-func docutils literal notranslate"><span class="pre">mainloop()</span></code></a> function
when working with <a class="reference internal" href="#turtle.Screen" title="turtle.Screen"><code class="xref py py-class docutils literal notranslate"><span class="pre">Screen</span></code></a> and <a class="reference internal" href="#turtle.Turtle" title="turtle.Turtle"><code class="xref py py-class docutils literal notranslate"><span class="pre">Turtle</span></code></a> objects.</p></li>
<li><p>Two input methods have been added: <a class="reference internal" href="#turtle.textinput" title="turtle.textinput"><code class="xref py py-func docutils literal notranslate"><span class="pre">Screen.textinput</span></code></a> and
<a class="reference internal" href="#turtle.numinput" title="turtle.numinput"><code class="xref py py-func docutils literal notranslate"><span class="pre">Screen.numinput</span></code></a>. These pop up input dialogs and return
strings and numbers respectively.</p></li>
<li><p>Two example scripts <code class="file docutils literal notranslate"><span class="pre">tdemo_nim.py</span></code> and <code class="file docutils literal notranslate"><span class="pre">tdemo_round_dance.py</span></code>
have been added to the <code class="file docutils literal notranslate"><span class="pre">Lib/turtledemo</span></code> directory.</p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code> — Turtle graphics</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#tutorial">Tutorial</a><ul>
<li><a class="reference internal" href="#starting-a-turtle-environment">Starting a turtle environment</a></li>
<li><a class="reference internal" href="#basic-drawing">Basic drawing</a><ul>
<li><a class="reference internal" href="#pen-control">Pen control</a></li>
<li><a class="reference internal" href="#the-turtle-s-position">The turtle’s position</a></li>
</ul>
</li>
<li><a class="reference internal" href="#making-algorithmic-patterns">Making algorithmic patterns</a></li>
</ul>
</li>
<li><a class="reference internal" href="#how-to">How to…</a><ul>
<li><a class="reference internal" href="#get-started-as-quickly-as-possible">Get started as quickly as possible</a></li>
<li><a class="reference internal" href="#use-the-turtle-module-namespace">Use the <code class="docutils literal notranslate"><span class="pre">turtle</span></code> module namespace</a></li>
<li><a class="reference internal" href="#use-turtle-graphics-in-a-script">Use turtle graphics in a script</a></li>
<li><a class="reference internal" href="#use-object-oriented-turtle-graphics">Use object-oriented turtle graphics</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turtle-graphics-reference">Turtle graphics reference</a><ul>
<li><a class="reference internal" href="#turtle-methods">Turtle methods</a></li>
<li><a class="reference internal" href="#methods-of-turtlescreen-screen">Methods of TurtleScreen/Screen</a></li>
</ul>
</li>
<li><a class="reference internal" href="#methods-of-rawturtle-turtle-and-corresponding-functions">Methods of RawTurtle/Turtle and corresponding functions</a><ul>
<li><a class="reference internal" href="#turtle-motion">Turtle motion</a></li>
<li><a class="reference internal" href="#tell-turtle-s-state">Tell Turtle’s state</a></li>
<li><a class="reference internal" href="#settings-for-measurement">Settings for measurement</a></li>
<li><a class="reference internal" href="#id1">Pen control</a><ul>
<li><a class="reference internal" href="#drawing-state">Drawing state</a></li>
<li><a class="reference internal" href="#color-control">Color control</a></li>
<li><a class="reference internal" href="#filling">Filling</a></li>
<li><a class="reference internal" href="#more-drawing-control">More drawing control</a></li>
</ul>
</li>
<li><a class="reference internal" href="#turtle-state">Turtle state</a><ul>
<li><a class="reference internal" href="#visibility">Visibility</a></li>
<li><a class="reference internal" href="#appearance">Appearance</a></li>
</ul>
</li>
<li><a class="reference internal" href="#using-events">Using events</a></li>
<li><a class="reference internal" href="#special-turtle-methods">Special Turtle methods</a></li>
<li><a class="reference internal" href="#compound-shapes">Compound shapes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#methods-of-turtlescreen-screen-and-corresponding-functions">Methods of TurtleScreen/Screen and corresponding functions</a><ul>
<li><a class="reference internal" href="#window-control">Window control</a></li>
<li><a class="reference internal" href="#animation-control">Animation control</a></li>
<li><a class="reference internal" href="#using-screen-events">Using screen events</a></li>
<li><a class="reference internal" href="#input-methods">Input methods</a></li>
<li><a class="reference internal" href="#settings-and-special-methods">Settings and special methods</a></li>
<li><a class="reference internal" href="#methods-specific-to-screen-not-inherited-from-turtlescreen">Methods specific to Screen, not inherited from TurtleScreen</a></li>
</ul>
</li>
<li><a class="reference internal" href="#public-classes">Public classes</a></li>
<li><a class="reference internal" href="#explanation">Explanation</a></li>
<li><a class="reference internal" href="#help-and-configuration">Help and configuration</a><ul>
<li><a class="reference internal" href="#how-to-use-help">How to use help</a></li>
<li><a class="reference internal" href="#translation-of-docstrings-into-different-languages">Translation of docstrings into different languages</a></li>
<li><a class="reference internal" href="#how-to-configure-screen-and-turtles">How to configure Screen and Turtles</a></li>
</ul>
</li>
<li><a class="reference internal" href="#module-turtledemo"><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtledemo</span></code> — Demo scripts</a></li>
<li><a class="reference internal" href="#changes-since-python-2-6">Changes since Python 2.6</a></li>
<li><a class="reference internal" href="#changes-since-python-3-0">Changes since Python 3.0</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="frameworks.html"
                          title="previous chapter">Program Frameworks</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmd.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">cmd</span></code> — Support for line-oriented command interpreters</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/turtle.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="cmd.html" title="cmd — Support for line-oriented command interpreters"
             >next</a> |</li>
        <li class="right" >
          <a href="frameworks.html" title="Program Frameworks"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="frameworks.html" >Program Frameworks</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">turtle</span></code> — Turtle graphics</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>