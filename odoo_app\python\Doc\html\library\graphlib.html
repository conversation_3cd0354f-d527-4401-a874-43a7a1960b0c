<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="graphlib — Functionality to operate with graph-like structures" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/graphlib.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/graphlib.py Exceptions: The graphlib module defines the following exception classes:" />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/graphlib.py Exceptions: The graphlib module defines the following exception classes:" />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>graphlib — Functionality to operate with graph-like structures &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Numeric and Mathematical Modules" href="numeric.html" />
    <link rel="prev" title="enum — Support for enumerations" href="enum.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/graphlib.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a><ul>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="enum.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="numeric.html"
                          title="next chapter">Numeric and Mathematical Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/graphlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="numeric.html" title="Numeric and Mathematical Modules"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="enum.html" title="enum — Support for enumerations"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" accesskey="U">Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-graphlib">
<span id="graphlib-functionality-to-operate-with-graph-like-structures"></span><h1><a class="reference internal" href="#module-graphlib" title="graphlib: Functionality to operate with graph-like structures"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code></a> — Functionality to operate with graph-like structures<a class="headerlink" href="#module-graphlib" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/graphlib.py">Lib/graphlib.py</a></p>
<hr class="docutils" />
<dl class="py class">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">graphlib.</span></span><span class="sig-name descname"><span class="pre">TopologicalSorter</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">graph</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter" title="Link to this definition">¶</a></dt>
<dd><p>Provides functionality to topologically sort a graph of <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a> nodes.</p>
<p>A topological order is a linear ordering of the vertices in a graph such that
for every directed edge u -&gt; v from vertex u to vertex v, vertex u comes
before vertex v in the ordering. For instance, the vertices of the graph may
represent tasks to be performed, and the edges may represent constraints that
one task must be performed before another; in this example, a topological
ordering is just a valid sequence for the tasks. A complete topological
ordering is possible if and only if the graph has no directed cycles, that
is, if it is a directed acyclic graph.</p>
<p>If the optional <em>graph</em> argument is provided it must be a dictionary
representing a directed acyclic graph where the keys are nodes and the values
are iterables of all predecessors of that node in the graph (the nodes that
have edges that point to the value in the key). Additional nodes can be added
to the graph using the <a class="reference internal" href="#graphlib.TopologicalSorter.add" title="graphlib.TopologicalSorter.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add()</span></code></a> method.</p>
<p>In the general case, the steps required to perform the sorting of a given
graph are as follows:</p>
<ul class="simple">
<li><p>Create an instance of the <a class="reference internal" href="#graphlib.TopologicalSorter" title="graphlib.TopologicalSorter"><code class="xref py py-class docutils literal notranslate"><span class="pre">TopologicalSorter</span></code></a> with an optional
initial graph.</p></li>
<li><p>Add additional nodes to the graph.</p></li>
<li><p>Call <a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a> on the graph.</p></li>
<li><p>While <a class="reference internal" href="#graphlib.TopologicalSorter.is_active" title="graphlib.TopologicalSorter.is_active"><code class="xref py py-meth docutils literal notranslate"><span class="pre">is_active()</span></code></a> is <code class="docutils literal notranslate"><span class="pre">True</span></code>, iterate over
the nodes returned by <a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_ready()</span></code></a> and
process them. Call <a class="reference internal" href="#graphlib.TopologicalSorter.done" title="graphlib.TopologicalSorter.done"><code class="xref py py-meth docutils literal notranslate"><span class="pre">done()</span></code></a> on each node as it
finishes processing.</p></li>
</ul>
<p>In case just an immediate sorting of the nodes in the graph is required and
no parallelism is involved, the convenience method
<a class="reference internal" href="#graphlib.TopologicalSorter.static_order" title="graphlib.TopologicalSorter.static_order"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.static_order()</span></code></a> can be used directly:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">graph</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;D&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;B&quot;</span><span class="p">,</span> <span class="s2">&quot;C&quot;</span><span class="p">},</span> <span class="s2">&quot;C&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;A&quot;</span><span class="p">},</span> <span class="s2">&quot;B&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;A&quot;</span><span class="p">}}</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span> <span class="o">=</span> <span class="n">TopologicalSorter</span><span class="p">(</span><span class="n">graph</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">tuple</span><span class="p">(</span><span class="n">ts</span><span class="o">.</span><span class="n">static_order</span><span class="p">())</span>
<span class="go">(&#39;A&#39;, &#39;C&#39;, &#39;B&#39;, &#39;D&#39;)</span>
</pre></div>
</div>
<p>The class is designed to easily support parallel processing of the nodes as
they become ready. For instance:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">topological_sorter</span> <span class="o">=</span> <span class="n">TopologicalSorter</span><span class="p">()</span>

<span class="c1"># Add nodes to &#39;topological_sorter&#39;...</span>

<span class="n">topological_sorter</span><span class="o">.</span><span class="n">prepare</span><span class="p">()</span>
<span class="k">while</span> <span class="n">topological_sorter</span><span class="o">.</span><span class="n">is_active</span><span class="p">():</span>
    <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">topological_sorter</span><span class="o">.</span><span class="n">get_ready</span><span class="p">():</span>
        <span class="c1"># Worker threads or processes take nodes to work on off the</span>
        <span class="c1"># &#39;task_queue&#39; queue.</span>
        <span class="n">task_queue</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="n">node</span><span class="p">)</span>

    <span class="c1"># When the work for a node is done, workers put the node in</span>
    <span class="c1"># &#39;finalized_tasks_queue&#39; so we can get more nodes to work on.</span>
    <span class="c1"># The definition of &#39;is_active()&#39; guarantees that, at this point, at</span>
    <span class="c1"># least one node has been placed on &#39;task_queue&#39; that hasn&#39;t yet</span>
    <span class="c1"># been passed to &#39;done()&#39;, so this blocking &#39;get()&#39; must (eventually)</span>
    <span class="c1"># succeed.  After calling &#39;done()&#39;, we loop back to call &#39;get_ready()&#39;</span>
    <span class="c1"># again, so put newly freed nodes on &#39;task_queue&#39; as soon as</span>
    <span class="c1"># logically possible.</span>
    <span class="n">node</span> <span class="o">=</span> <span class="n">finalized_tasks_queue</span><span class="o">.</span><span class="n">get</span><span class="p">()</span>
    <span class="n">topological_sorter</span><span class="o">.</span><span class="n">done</span><span class="p">(</span><span class="n">node</span><span class="p">)</span>
</pre></div>
</div>
<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.add">
<span class="sig-name descname"><span class="pre">add</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">predecessors</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.add" title="Link to this definition">¶</a></dt>
<dd><p>Add a new node and its predecessors to the graph. Both the <em>node</em> and all
elements in <em>predecessors</em> must be <a class="reference internal" href="../glossary.html#term-hashable"><span class="xref std std-term">hashable</span></a>.</p>
<p>If called multiple times with the same node argument, the set of
dependencies will be the union of all dependencies passed in.</p>
<p>It is possible to add a node with no dependencies (<em>predecessors</em> is not
provided) or to provide a dependency twice. If a node that has not been
provided before is included among <em>predecessors</em> it will be automatically
added to the graph with no predecessors of its own.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if called after <a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.prepare">
<span class="sig-name descname"><span class="pre">prepare</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.prepare" title="Link to this definition">¶</a></dt>
<dd><p>Mark the graph as finished and check for cycles in the graph. If any cycle
is detected, <a class="reference internal" href="#graphlib.CycleError" title="graphlib.CycleError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CycleError</span></code></a> will be raised, but
<a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_ready()</span></code></a> can still be used to obtain as many
nodes as possible until cycles block more progress. After a call to this
function, the graph cannot be modified, and therefore no more nodes can be
added using <a class="reference internal" href="#graphlib.TopologicalSorter.add" title="graphlib.TopologicalSorter.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.is_active">
<span class="sig-name descname"><span class="pre">is_active</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.is_active" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">True</span></code> if more progress can be made and <code class="docutils literal notranslate"><span class="pre">False</span></code> otherwise.
Progress can be made if cycles do not block the resolution and either
there are still nodes ready that haven’t yet been returned by
<a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.get_ready()</span></code></a> or the number of nodes marked
<a class="reference internal" href="#graphlib.TopologicalSorter.done" title="graphlib.TopologicalSorter.done"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.done()</span></code></a> is less than the number that have been
returned by <a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.get_ready()</span></code></a>.</p>
<p>The <a class="reference internal" href="../reference/datamodel.html#object.__bool__" title="object.__bool__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__bool__()</span></code></a> method of this class defers to
this function, so instead of:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">ts</span><span class="o">.</span><span class="n">is_active</span><span class="p">():</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>it is possible to simply do:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">ts</span><span class="p">:</span>
    <span class="o">...</span>
</pre></div>
</div>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if called without calling
<a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a> previously.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.done">
<span class="sig-name descname"><span class="pre">done</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">*</span></span><span class="n"><span class="pre">nodes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.done" title="Link to this definition">¶</a></dt>
<dd><p>Marks a set of nodes returned by <a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.get_ready()</span></code></a> as
processed, unblocking any successor of each node in <em>nodes</em> for being
returned in the future by a call to <a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.get_ready()</span></code></a>.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if any node in <em>nodes</em> has already been marked as
processed by a previous call to this method or if a node was not added to
the graph by using <a class="reference internal" href="#graphlib.TopologicalSorter.add" title="graphlib.TopologicalSorter.add"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.add()</span></code></a>, if called without
calling <a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a> or if node has not yet been
returned by <a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_ready()</span></code></a>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.get_ready">
<span class="sig-name descname"><span class="pre">get_ready</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.get_ready" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <code class="docutils literal notranslate"><span class="pre">tuple</span></code> with all the nodes that are ready. Initially it
returns all nodes with no predecessors, and once those are marked as
processed by calling <a class="reference internal" href="#graphlib.TopologicalSorter.done" title="graphlib.TopologicalSorter.done"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.done()</span></code></a>, further calls will
return all new nodes that have all their predecessors already processed.
Once no more progress can be made, empty tuples are returned.</p>
<p>Raises <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> if called without calling
<a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a> previously.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="graphlib.TopologicalSorter.static_order">
<span class="sig-name descname"><span class="pre">static_order</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#graphlib.TopologicalSorter.static_order" title="Link to this definition">¶</a></dt>
<dd><p>Returns an iterator object which will iterate over nodes in a topological
order. When using this method, <a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">prepare()</span></code></a> and
<a class="reference internal" href="#graphlib.TopologicalSorter.done" title="graphlib.TopologicalSorter.done"><code class="xref py py-meth docutils literal notranslate"><span class="pre">done()</span></code></a> should not be called. This method is
equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">static_order</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="bp">self</span><span class="o">.</span><span class="n">prepare</span><span class="p">()</span>
    <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_active</span><span class="p">():</span>
        <span class="n">node_group</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_ready</span><span class="p">()</span>
        <span class="k">yield from</span> <span class="n">node_group</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">done</span><span class="p">(</span><span class="o">*</span><span class="n">node_group</span><span class="p">)</span>
</pre></div>
</div>
<p>The particular order that is returned may depend on the specific order in
which the items were inserted in the graph. For example:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span> <span class="o">=</span> <span class="n">TopologicalSorter</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">([</span><span class="o">*</span><span class="n">ts</span><span class="o">.</span><span class="n">static_order</span><span class="p">()])</span>
<span class="go">[2, 0, 1, 3]</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">ts2</span> <span class="o">=</span> <span class="n">TopologicalSorter</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts2</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ts2</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">([</span><span class="o">*</span><span class="n">ts2</span><span class="o">.</span><span class="n">static_order</span><span class="p">()])</span>
<span class="go">[0, 2, 1, 3]</span>
</pre></div>
</div>
<p>This is due to the fact that “0” and “2” are in the same level in the
graph (they would have been returned in the same call to
<a class="reference internal" href="#graphlib.TopologicalSorter.get_ready" title="graphlib.TopologicalSorter.get_ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_ready()</span></code></a>) and the order between them is
determined by the order of insertion.</p>
<p>If any cycle is detected, <a class="reference internal" href="#graphlib.CycleError" title="graphlib.CycleError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">CycleError</span></code></a> will be raised.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

<section id="exceptions">
<h2>Exceptions<a class="headerlink" href="#exceptions" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-graphlib" title="graphlib: Functionality to operate with graph-like structures"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code></a> module defines the following exception classes:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="graphlib.CycleError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">graphlib.</span></span><span class="sig-name descname"><span class="pre">CycleError</span></span><a class="headerlink" href="#graphlib.CycleError" title="Link to this definition">¶</a></dt>
<dd><p>Subclass of <a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> raised by <a class="reference internal" href="#graphlib.TopologicalSorter.prepare" title="graphlib.TopologicalSorter.prepare"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TopologicalSorter.prepare()</span></code></a> if cycles exist
in the working graph. If multiple cycles exist, only one undefined choice among them will
be reported and included in the exception.</p>
<p>The detected cycle can be accessed via the second element in the <a class="reference internal" href="exceptions.html#BaseException.args" title="BaseException.args"><code class="xref py py-attr docutils literal notranslate"><span class="pre">args</span></code></a>
attribute of the exception instance and consists in a list of nodes, such that each node is,
in the graph, an immediate predecessor of the next node in the list. In the reported list,
the first and the last node will be the same, to make it clear that it is cyclic.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a><ul>
<li><a class="reference internal" href="#exceptions">Exceptions</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="enum.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code> — Support for enumerations</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="numeric.html"
                          title="next chapter">Numeric and Mathematical Modules</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/graphlib.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="numeric.html" title="Numeric and Mathematical Modules"
             >next</a> |</li>
        <li class="right" >
          <a href="enum.html" title="enum — Support for enumerations"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="datatypes.html" >Data Types</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">graphlib</span></code> — Functionality to operate with graph-like structures</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>