<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="project.example.generic">
      Prioritize your tasks by marking important ones using the <a style="color: gold;" class="fa fa-star"></a> button.
      <br/><br/>
      Use the <span class="o_status d-inline-block o_status_green"></span> state to inform your colleagues that a task is approved for the next stage. 
      <br/>
      Use the <span class="o_status d-inline-block bg-warning"></span> state to indicate a request for changes or a need for discussion on a task.
      <br/><br/>
      Use the <span class="fa fa-check-circle text-success d-inline-block"></span> state to mark the task as complete.
      <br/>
      Use the <span class="fa fa-times-circle text-danger d-inline-block"></span> state to mark the task as canceled.
      <br/><br/>
      Look for the <span class="fa fa-hourglass-o d-inline-block"></span> icon to see tasks waiting on other ones. Once a task is marked as complete or canceled, all of its dependencies will be unblocked.
      <br/><br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

    <t t-name="project.example.agilescrum">
      Use the <span class="o_status d-inline-block o_status_green"></span> state to inform your colleagues that a task is approved for the next stage. 
      <br/>
      Use the <span class="o_status d-inline-block bg-warning"></span> state to indicate a request for changes or a need for discussion on a task.
      <br/><br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

    <t t-name="project.example.digitalmarketing">
      Everyone can propose ideas, and the Editor marks the best ones as <span class="o_status d-inline-block o_status_green"></span>.<br/>
      Attach all documents or links to the task directly, to have all research information centralized. 
      <br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>
  
    <t t-name="project.example.customerfeedback">
      Customers propose feedbacks by email; Odoo creates tasks automatically, and you can
      communicate on the task directly. <br/>Your managers decide which feedback is accepted
      <span class="o_status d-inline-block o_status_green"></span> and which feedback is
      moved to the "Refused" column.
      <br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

    <t t-name="project.example.consulting">
      Manage the lifecycle of your project using the kanban view. Add newly acquired projects,
      assign them and use the <span class="o_status d-inline-block o_status_green"></span> and
      <span class="o_status d-inline-block bg-warning"></span> to define if the project is
      ready for the next step.
      <br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

    <t t-name="project.example.researchproject">
      Handle your idea gathering within Tasks of your new Project and discuss them in the chatter of the tasks. <br/>Use the
      <span class="o_status d-inline-block o_status_green"></span> and <span class="o_status d-inline-block bg-warning"></span>
      to signalize what is the current status of your Idea.
      <br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

    <t t-name="project.example.tshirtprinting">
      Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from
      designers to the workers who print the t-shirt. <br/>Organize priorities amongst orders using the
      <a style="color: gold;" class="fa fa-star"></a> icon.
      <br/>
      Use the <a class="fa fa-clock-o"></a> icon to organize your daily activities.
    </t>

</templates>
