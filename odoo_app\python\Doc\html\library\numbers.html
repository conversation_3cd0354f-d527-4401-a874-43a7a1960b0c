<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="numbers — Numeric abstract base classes" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/numbers.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/numbers.py The numbers module ( PEP 3141) defines a hierarchy of numeric abstract base classes which progressively define more operations. None of the types defined in this module ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/numbers.py The numbers module ( PEP 3141) defines a hierarchy of numeric abstract base classes which progressively define more operations. None of the types defined in this module ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>numbers — Numeric abstract base classes &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="math — Mathematical functions" href="math.html" />
    <link rel="prev" title="Numeric and Mathematical Modules" href="numeric.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/numbers.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a><ul>
<li><a class="reference internal" href="#the-numeric-tower">The numeric tower</a></li>
<li><a class="reference internal" href="#notes-for-type-implementors">Notes for type implementors</a><ul>
<li><a class="reference internal" href="#adding-more-numeric-abcs">Adding More Numeric ABCs</a></li>
<li><a class="reference internal" href="#implementing-the-arithmetic-operations">Implementing the arithmetic operations</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="numeric.html"
                          title="previous chapter">Numeric and Mathematical Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="math.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/numbers.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="math.html" title="math — Mathematical functions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="numeric.html" title="Numeric and Mathematical Modules"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" accesskey="U">Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-numbers">
<span id="numbers-numeric-abstract-base-classes"></span><h1><a class="reference internal" href="#module-numbers" title="numbers: Numeric abstract base classes (Complex, Real, Integral, etc.)."><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code></a> — Numeric abstract base classes<a class="headerlink" href="#module-numbers" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/numbers.py">Lib/numbers.py</a></p>
<hr class="docutils" />
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> module (<span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3141/"><strong>PEP 3141</strong></a>) defines a hierarchy of numeric
<a class="reference internal" href="../glossary.html#term-abstract-base-class"><span class="xref std std-term">abstract base classes</span></a> which progressively define
more operations.  None of the types defined in this module are intended to be instantiated.</p>
<dl class="py class">
<dt class="sig sig-object py" id="numbers.Number">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">numbers.</span></span><span class="sig-name descname"><span class="pre">Number</span></span><a class="headerlink" href="#numbers.Number" title="Link to this definition">¶</a></dt>
<dd><p>The root of the numeric hierarchy. If you just want to check if an argument
<em>x</em> is a number, without caring what kind, use <code class="docutils literal notranslate"><span class="pre">isinstance(x,</span> <span class="pre">Number)</span></code>.</p>
</dd></dl>

<section id="the-numeric-tower">
<h2>The numeric tower<a class="headerlink" href="#the-numeric-tower" title="Link to this heading">¶</a></h2>
<dl class="py class">
<dt class="sig sig-object py" id="numbers.Complex">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">numbers.</span></span><span class="sig-name descname"><span class="pre">Complex</span></span><a class="headerlink" href="#numbers.Complex" title="Link to this definition">¶</a></dt>
<dd><p>Subclasses of this type describe complex numbers and include the operations
that work on the built-in <a class="reference internal" href="functions.html#complex" title="complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">complex</span></code></a> type. These are: conversions to
<a class="reference internal" href="functions.html#complex" title="complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">complex</span></code></a> and <a class="reference internal" href="functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a>, <a class="reference internal" href="#numbers.Complex.real" title="numbers.Complex.real"><code class="xref py py-attr docutils literal notranslate"><span class="pre">real</span></code></a>, <a class="reference internal" href="#numbers.Complex.imag" title="numbers.Complex.imag"><code class="xref py py-attr docutils literal notranslate"><span class="pre">imag</span></code></a>, <code class="docutils literal notranslate"><span class="pre">+</span></code>,
<code class="docutils literal notranslate"><span class="pre">-</span></code>, <code class="docutils literal notranslate"><span class="pre">*</span></code>, <code class="docutils literal notranslate"><span class="pre">/</span></code>, <code class="docutils literal notranslate"><span class="pre">**</span></code>, <a class="reference internal" href="functions.html#abs" title="abs"><code class="xref py py-func docutils literal notranslate"><span class="pre">abs()</span></code></a>, <a class="reference internal" href="#numbers.Complex.conjugate" title="numbers.Complex.conjugate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">conjugate()</span></code></a>, <code class="docutils literal notranslate"><span class="pre">==</span></code>, and
<code class="docutils literal notranslate"><span class="pre">!=</span></code>. All except <code class="docutils literal notranslate"><span class="pre">-</span></code> and <code class="docutils literal notranslate"><span class="pre">!=</span></code> are abstract.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="numbers.Complex.real">
<span class="sig-name descname"><span class="pre">real</span></span><a class="headerlink" href="#numbers.Complex.real" title="Link to this definition">¶</a></dt>
<dd><p>Abstract. Retrieves the real component of this number.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="numbers.Complex.imag">
<span class="sig-name descname"><span class="pre">imag</span></span><a class="headerlink" href="#numbers.Complex.imag" title="Link to this definition">¶</a></dt>
<dd><p>Abstract. Retrieves the imaginary component of this number.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="numbers.Complex.conjugate">
<em class="property"><span class="pre">abstractmethod</span> </em><span class="sig-name descname"><span class="pre">conjugate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#numbers.Complex.conjugate" title="Link to this definition">¶</a></dt>
<dd><p>Abstract. Returns the complex conjugate. For example, <code class="docutils literal notranslate"><span class="pre">(1+3j).conjugate()</span>
<span class="pre">==</span> <span class="pre">(1-3j)</span></code>.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="numbers.Real">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">numbers.</span></span><span class="sig-name descname"><span class="pre">Real</span></span><a class="headerlink" href="#numbers.Real" title="Link to this definition">¶</a></dt>
<dd><p>To <a class="reference internal" href="#numbers.Complex" title="numbers.Complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">Complex</span></code></a>, <code class="xref py py-class docutils literal notranslate"><span class="pre">Real</span></code> adds the operations that work on real
numbers.</p>
<p>In short, those are: a conversion to <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a>, <a class="reference internal" href="math.html#math.trunc" title="math.trunc"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.trunc()</span></code></a>,
<a class="reference internal" href="functions.html#round" title="round"><code class="xref py py-func docutils literal notranslate"><span class="pre">round()</span></code></a>, <a class="reference internal" href="math.html#math.floor" title="math.floor"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.floor()</span></code></a>, <a class="reference internal" href="math.html#math.ceil" title="math.ceil"><code class="xref py py-func docutils literal notranslate"><span class="pre">math.ceil()</span></code></a>, <a class="reference internal" href="functions.html#divmod" title="divmod"><code class="xref py py-func docutils literal notranslate"><span class="pre">divmod()</span></code></a>, <code class="docutils literal notranslate"><span class="pre">//</span></code>,
<code class="docutils literal notranslate"><span class="pre">%</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;=</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>, and <code class="docutils literal notranslate"><span class="pre">&gt;=</span></code>.</p>
<p>Real also provides defaults for <a class="reference internal" href="functions.html#complex" title="complex"><code class="xref py py-func docutils literal notranslate"><span class="pre">complex()</span></code></a>, <a class="reference internal" href="#numbers.Complex.real" title="numbers.Complex.real"><code class="xref py py-attr docutils literal notranslate"><span class="pre">real</span></code></a>,
<a class="reference internal" href="#numbers.Complex.imag" title="numbers.Complex.imag"><code class="xref py py-attr docutils literal notranslate"><span class="pre">imag</span></code></a>, and <a class="reference internal" href="#numbers.Complex.conjugate" title="numbers.Complex.conjugate"><code class="xref py py-meth docutils literal notranslate"><span class="pre">conjugate()</span></code></a>.</p>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="numbers.Rational">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">numbers.</span></span><span class="sig-name descname"><span class="pre">Rational</span></span><a class="headerlink" href="#numbers.Rational" title="Link to this definition">¶</a></dt>
<dd><p>Subtypes <a class="reference internal" href="#numbers.Real" title="numbers.Real"><code class="xref py py-class docutils literal notranslate"><span class="pre">Real</span></code></a> and adds <a class="reference internal" href="#numbers.Rational.numerator" title="numbers.Rational.numerator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">numerator</span></code></a> and
<a class="reference internal" href="#numbers.Rational.denominator" title="numbers.Rational.denominator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">denominator</span></code></a> properties. It also provides a default for
<a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a>.</p>
<p>The <a class="reference internal" href="#numbers.Rational.numerator" title="numbers.Rational.numerator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">numerator</span></code></a> and <a class="reference internal" href="#numbers.Rational.denominator" title="numbers.Rational.denominator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">denominator</span></code></a> values
should be instances of <a class="reference internal" href="#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">Integral</span></code></a> and should be in lowest terms with
<a class="reference internal" href="#numbers.Rational.denominator" title="numbers.Rational.denominator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">denominator</span></code></a> positive.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="numbers.Rational.numerator">
<span class="sig-name descname"><span class="pre">numerator</span></span><a class="headerlink" href="#numbers.Rational.numerator" title="Link to this definition">¶</a></dt>
<dd><p>Abstract.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="numbers.Rational.denominator">
<span class="sig-name descname"><span class="pre">denominator</span></span><a class="headerlink" href="#numbers.Rational.denominator" title="Link to this definition">¶</a></dt>
<dd><p>Abstract.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="numbers.Integral">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">numbers.</span></span><span class="sig-name descname"><span class="pre">Integral</span></span><a class="headerlink" href="#numbers.Integral" title="Link to this definition">¶</a></dt>
<dd><p>Subtypes <a class="reference internal" href="#numbers.Rational" title="numbers.Rational"><code class="xref py py-class docutils literal notranslate"><span class="pre">Rational</span></code></a> and adds a conversion to <a class="reference internal" href="functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.  Provides
defaults for <a class="reference internal" href="functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a>, <a class="reference internal" href="#numbers.Rational.numerator" title="numbers.Rational.numerator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">numerator</span></code></a>, and
<a class="reference internal" href="#numbers.Rational.denominator" title="numbers.Rational.denominator"><code class="xref py py-attr docutils literal notranslate"><span class="pre">denominator</span></code></a>.  Adds abstract methods for <a class="reference internal" href="functions.html#pow" title="pow"><code class="xref py py-func docutils literal notranslate"><span class="pre">pow()</span></code></a> with
modulus and bit-string operations: <code class="docutils literal notranslate"><span class="pre">&lt;&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&amp;</span></code>, <code class="docutils literal notranslate"><span class="pre">^</span></code>, <code class="docutils literal notranslate"><span class="pre">|</span></code>,
<code class="docutils literal notranslate"><span class="pre">~</span></code>.</p>
</dd></dl>

</section>
<section id="notes-for-type-implementors">
<h2>Notes for type implementors<a class="headerlink" href="#notes-for-type-implementors" title="Link to this heading">¶</a></h2>
<p>Implementors should be careful to make equal numbers equal and hash
them to the same values. This may be subtle if there are two different
extensions of the real numbers. For example, <a class="reference internal" href="fractions.html#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">fractions.Fraction</span></code></a>
implements <a class="reference internal" href="functions.html#hash" title="hash"><code class="xref py py-func docutils literal notranslate"><span class="pre">hash()</span></code></a> as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="fm">__hash__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">denominator</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
        <span class="c1"># Get integers right.</span>
        <span class="k">return</span> <span class="nb">hash</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">numerator</span><span class="p">)</span>
    <span class="c1"># Expensive check, but definitely correct.</span>
    <span class="k">if</span> <span class="bp">self</span> <span class="o">==</span> <span class="nb">float</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="nb">hash</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="bp">self</span><span class="p">))</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Use tuple&#39;s hash to avoid a high collision rate on</span>
        <span class="c1"># simple fractions.</span>
        <span class="k">return</span> <span class="nb">hash</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">numerator</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">denominator</span><span class="p">))</span>
</pre></div>
</div>
<section id="adding-more-numeric-abcs">
<h3>Adding More Numeric ABCs<a class="headerlink" href="#adding-more-numeric-abcs" title="Link to this heading">¶</a></h3>
<p>There are, of course, more possible ABCs for numbers, and this would
be a poor hierarchy if it precluded the possibility of adding
those. You can add <code class="docutils literal notranslate"><span class="pre">MyFoo</span></code> between <a class="reference internal" href="#numbers.Complex" title="numbers.Complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">Complex</span></code></a> and
<a class="reference internal" href="#numbers.Real" title="numbers.Real"><code class="xref py py-class docutils literal notranslate"><span class="pre">Real</span></code></a> with:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyFoo</span><span class="p">(</span><span class="n">Complex</span><span class="p">):</span> <span class="o">...</span>
<span class="n">MyFoo</span><span class="o">.</span><span class="n">register</span><span class="p">(</span><span class="n">Real</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="implementing-the-arithmetic-operations">
<span id="id1"></span><h3>Implementing the arithmetic operations<a class="headerlink" href="#implementing-the-arithmetic-operations" title="Link to this heading">¶</a></h3>
<p>We want to implement the arithmetic operations so that mixed-mode
operations either call an implementation whose author knew about the
types of both arguments, or convert both to the nearest built in type
and do the operation there. For subtypes of <a class="reference internal" href="#numbers.Integral" title="numbers.Integral"><code class="xref py py-class docutils literal notranslate"><span class="pre">Integral</span></code></a>, this
means that <a class="reference internal" href="../reference/datamodel.html#object.__add__" title="object.__add__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code></a> and <a class="reference internal" href="../reference/datamodel.html#object.__radd__" title="object.__radd__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__radd__()</span></code></a> should be
defined as:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">MyIntegral</span><span class="p">(</span><span class="n">Integral</span><span class="p">):</span>

    <span class="k">def</span> <span class="fm">__add__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">MyIntegral</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">do_my_adding_stuff</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">OtherTypeIKnowAbout</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">do_my_other_adding_stuff</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">NotImplemented</span>

    <span class="k">def</span> <span class="fm">__radd__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">MyIntegral</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">do_my_adding_stuff</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">OtherTypeIKnowAbout</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">do_my_other_adding_stuff</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">Integral</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">int</span><span class="p">(</span><span class="n">other</span><span class="p">)</span> <span class="o">+</span> <span class="nb">int</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">Real</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">float</span><span class="p">(</span><span class="n">other</span><span class="p">)</span> <span class="o">+</span> <span class="nb">float</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">other</span><span class="p">,</span> <span class="n">Complex</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">complex</span><span class="p">(</span><span class="n">other</span><span class="p">)</span> <span class="o">+</span> <span class="nb">complex</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">NotImplemented</span>
</pre></div>
</div>
<p>There are 5 different cases for a mixed-type operation on subclasses
of <a class="reference internal" href="#numbers.Complex" title="numbers.Complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">Complex</span></code></a>. I’ll refer to all of the above code that doesn’t
refer to <code class="docutils literal notranslate"><span class="pre">MyIntegral</span></code> and <code class="docutils literal notranslate"><span class="pre">OtherTypeIKnowAbout</span></code> as
“boilerplate”. <code class="docutils literal notranslate"><span class="pre">a</span></code> will be an instance of <code class="docutils literal notranslate"><span class="pre">A</span></code>, which is a subtype
of <a class="reference internal" href="#numbers.Complex" title="numbers.Complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">Complex</span></code></a> (<code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">:</span> <span class="pre">A</span> <span class="pre">&lt;:</span> <span class="pre">Complex</span></code>), and <code class="docutils literal notranslate"><span class="pre">b</span> <span class="pre">:</span> <span class="pre">B</span> <span class="pre">&lt;:</span>
<span class="pre">Complex</span></code>. I’ll consider <code class="docutils literal notranslate"><span class="pre">a</span> <span class="pre">+</span> <span class="pre">b</span></code>:</p>
<ol class="arabic simple">
<li><p>If <code class="docutils literal notranslate"><span class="pre">A</span></code> defines an <a class="reference internal" href="../reference/datamodel.html#object.__add__" title="object.__add__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code></a> which accepts <code class="docutils literal notranslate"><span class="pre">b</span></code>, all is
well.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">A</span></code> falls back to the boilerplate code, and it were to
return a value from <a class="reference internal" href="../reference/datamodel.html#object.__add__" title="object.__add__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code></a>, we’d miss the possibility
that <code class="docutils literal notranslate"><span class="pre">B</span></code> defines a more intelligent <a class="reference internal" href="../reference/datamodel.html#object.__radd__" title="object.__radd__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__radd__()</span></code></a>, so the
boilerplate should return <a class="reference internal" href="constants.html#NotImplemented" title="NotImplemented"><code class="xref py py-data docutils literal notranslate"><span class="pre">NotImplemented</span></code></a> from
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code>. (Or <code class="docutils literal notranslate"><span class="pre">A</span></code> may not implement <code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code> at
all.)</p></li>
<li><p>Then <code class="docutils literal notranslate"><span class="pre">B</span></code>’s <a class="reference internal" href="../reference/datamodel.html#object.__radd__" title="object.__radd__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__radd__()</span></code></a> gets a chance. If it accepts
<code class="docutils literal notranslate"><span class="pre">a</span></code>, all is well.</p></li>
<li><p>If it falls back to the boilerplate, there are no more possible
methods to try, so this is where the default implementation
should live.</p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">B</span> <span class="pre">&lt;:</span> <span class="pre">A</span></code>, Python tries <code class="docutils literal notranslate"><span class="pre">B.__radd__</span></code> before
<code class="docutils literal notranslate"><span class="pre">A.__add__</span></code>. This is ok, because it was implemented with
knowledge of <code class="docutils literal notranslate"><span class="pre">A</span></code>, so it can handle those instances before
delegating to <a class="reference internal" href="#numbers.Complex" title="numbers.Complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">Complex</span></code></a>.</p></li>
</ol>
<p>If <code class="docutils literal notranslate"><span class="pre">A</span> <span class="pre">&lt;:</span> <span class="pre">Complex</span></code> and <code class="docutils literal notranslate"><span class="pre">B</span> <span class="pre">&lt;:</span> <span class="pre">Real</span></code> without sharing any other knowledge,
then the appropriate shared operation is the one involving the built
in <a class="reference internal" href="functions.html#complex" title="complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">complex</span></code></a>, and both <a class="reference internal" href="../reference/datamodel.html#object.__radd__" title="object.__radd__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__radd__()</span></code></a> s land there, so <code class="docutils literal notranslate"><span class="pre">a+b</span>
<span class="pre">==</span> <span class="pre">b+a</span></code>.</p>
<p>Because most of the operations on any given type will be very similar,
it can be useful to define a helper function which generates the
forward and reverse instances of any given operator. For example,
<a class="reference internal" href="fractions.html#fractions.Fraction" title="fractions.Fraction"><code class="xref py py-class docutils literal notranslate"><span class="pre">fractions.Fraction</span></code></a> uses:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">_operator_fallbacks</span><span class="p">(</span><span class="n">monomorphic_operator</span><span class="p">,</span> <span class="n">fallback_operator</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">forward</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="n">Fraction</span><span class="p">)):</span>
            <span class="k">return</span> <span class="n">monomorphic_operator</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="nb">float</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">fallback_operator</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">a</span><span class="p">),</span> <span class="n">b</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="nb">complex</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">fallback_operator</span><span class="p">(</span><span class="nb">complex</span><span class="p">(</span><span class="n">a</span><span class="p">),</span> <span class="n">b</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">NotImplemented</span>
    <span class="n">forward</span><span class="o">.</span><span class="vm">__name__</span> <span class="o">=</span> <span class="s1">&#39;__&#39;</span> <span class="o">+</span> <span class="n">fallback_operator</span><span class="o">.</span><span class="vm">__name__</span> <span class="o">+</span> <span class="s1">&#39;__&#39;</span>
    <span class="n">forward</span><span class="o">.</span><span class="vm">__doc__</span> <span class="o">=</span> <span class="n">monomorphic_operator</span><span class="o">.</span><span class="vm">__doc__</span>

    <span class="k">def</span> <span class="nf">reverse</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="n">a</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">Rational</span><span class="p">):</span>
            <span class="c1"># Includes ints.</span>
            <span class="k">return</span> <span class="n">monomorphic_operator</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">Real</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">fallback_operator</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">a</span><span class="p">),</span> <span class="nb">float</span><span class="p">(</span><span class="n">b</span><span class="p">))</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">Complex</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">fallback_operator</span><span class="p">(</span><span class="nb">complex</span><span class="p">(</span><span class="n">a</span><span class="p">),</span> <span class="nb">complex</span><span class="p">(</span><span class="n">b</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">NotImplemented</span>
    <span class="n">reverse</span><span class="o">.</span><span class="vm">__name__</span> <span class="o">=</span> <span class="s1">&#39;__r&#39;</span> <span class="o">+</span> <span class="n">fallback_operator</span><span class="o">.</span><span class="vm">__name__</span> <span class="o">+</span> <span class="s1">&#39;__&#39;</span>
    <span class="n">reverse</span><span class="o">.</span><span class="vm">__doc__</span> <span class="o">=</span> <span class="n">monomorphic_operator</span><span class="o">.</span><span class="vm">__doc__</span>

    <span class="k">return</span> <span class="n">forward</span><span class="p">,</span> <span class="n">reverse</span>

<span class="k">def</span> <span class="nf">_add</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;a + b&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">Fraction</span><span class="p">(</span><span class="n">a</span><span class="o">.</span><span class="n">numerator</span> <span class="o">*</span> <span class="n">b</span><span class="o">.</span><span class="n">denominator</span> <span class="o">+</span>
                    <span class="n">b</span><span class="o">.</span><span class="n">numerator</span> <span class="o">*</span> <span class="n">a</span><span class="o">.</span><span class="n">denominator</span><span class="p">,</span>
                    <span class="n">a</span><span class="o">.</span><span class="n">denominator</span> <span class="o">*</span> <span class="n">b</span><span class="o">.</span><span class="n">denominator</span><span class="p">)</span>

<span class="fm">__add__</span><span class="p">,</span> <span class="fm">__radd__</span> <span class="o">=</span> <span class="n">_operator_fallbacks</span><span class="p">(</span><span class="n">_add</span><span class="p">,</span> <span class="n">operator</span><span class="o">.</span><span class="n">add</span><span class="p">)</span>

<span class="c1"># ...</span>
</pre></div>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a><ul>
<li><a class="reference internal" href="#the-numeric-tower">The numeric tower</a></li>
<li><a class="reference internal" href="#notes-for-type-implementors">Notes for type implementors</a><ul>
<li><a class="reference internal" href="#adding-more-numeric-abcs">Adding More Numeric ABCs</a></li>
<li><a class="reference internal" href="#implementing-the-arithmetic-operations">Implementing the arithmetic operations</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="numeric.html"
                          title="previous chapter">Numeric and Mathematical Modules</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="math.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">math</span></code> — Mathematical functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/numbers.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="math.html" title="math — Mathematical functions"
             >next</a> |</li>
        <li class="right" >
          <a href="numeric.html" title="Numeric and Mathematical Modules"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="numeric.html" >Numeric and Mathematical Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">numbers</span></code> — Numeric abstract base classes</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>