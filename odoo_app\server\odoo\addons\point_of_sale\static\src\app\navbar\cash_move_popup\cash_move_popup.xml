<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.CashMovePopup">
        <div class="popup cash-move-popup">
            <main class="modal-body">
                <div class="cash-move d-flex flex-column">
                    <div class="input-amount d-flex mb-2 gap-2">
                        <div class="input-group">
                            <button t-on-click="() => this.onClickButton('in')" class="input-type btn btn-secondary flex-fill py-2 fw-bolder" t-att-class="{ 'highlight btn-success': state.type === 'in' }">
                                Cash In
                            </button>
                            <button t-on-click="() => this.onClickButton('out')" class="input-type btn btn-secondary flex-fill py-2 fw-bolder" t-att-class="{ 'red-highlight btn-danger': state.type === 'out' }">
                                Cash Out
                            </button>
                        </div>
                        <Input tModel="[state, 'amount']"
                            icon="{type: 'string', value: pos.currency.symbol}"
                            iconOnLeftSide="pos.currency.position === 'before'"
                            isValid.bind="env.utils.isValidFloat"
                            autofocus="true"
                            getRef="(ref) => this.inputRef = ref" />
                    </div>
                    <div class="form-floating">
                        <textarea class="form-control" placeholder="Leave a reason here" name="reason" id="reason" t-model="state.reason" style="height:100px;" />
                        <label for="reason">Reason</label>
                    </div>
                </div>
            </main>
            <footer class="footer cash-move modal-footer">
                <button class="button confirm btn btn-lg btn-primary"
                    t-on-click="confirm"
                    t-att-disabled="!env.utils.isValidFloat(state.amount)">
                    Confirm <span t-esc="format(state.amount)"/>
                </button>
                <button class="button cancel btn btn-lg btn-secondary" t-on-click="cancel">
                    Discard
                </button>
            </footer>
        </div>
    </t>

</templates>
