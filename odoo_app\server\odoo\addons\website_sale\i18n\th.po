# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON>il <PERSON>doo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" หมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"\"ไม่บังคับ\" "
"จะอนุญาตให้ผู้ชมลงทะเบียนจากอีเมลยืนยันคำสั่งซื้อเพื่อติดตามคำสั่งซื้อของพวกเขา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s รีวิว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s รีวิว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "&amp; Shipping"
msgstr "และการจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "&amp;nbsp;(<i>Your shipping address will be requested later)</i>"
msgstr "&amp;nbsp;(<i>ที่อยู่สำหรับจัดส่งของคุณจะถูกขอในภายหลัง)</i>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "กำลังแสดงผลลัพธ์สำหรับ "

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100 เปอร์เซ็นต์"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "50 เปอร์เซ็นต์"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 %"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "66 เปอร์เซ็นต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">สรุปการสั่งซื้อ</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
msgid "<b>Categories</b>"
msgstr "<b>หมวดหมู่</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>การสื่อสาร: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>ช่วงราคา</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>รายการราคา</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Shipping: </b>"
msgstr "<b>การจัดส่ง: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>เรียงตาม</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>แท็ก</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                รับประกันคืนเงินภายใน 30 วัน<br/>\n"
"               การจัดส่งสินค้า: 2-3 วันทำการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    กลับไปที่การจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                ซื้อตอนนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/>หยิบลงตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    ซื้อตอนนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"เว็บไซต์\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>แก้ไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.row_addresses
msgid ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-inline\">Add "
"address</span>"
msgstr ""
"<i class=\"fa fa-plus me-md-2\"/><span class=\"d-none d-md-"
"inline\">เพิ่มที่อยู่</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>พิมพ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    สั่งซื้ออีกครั้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                หยิบลงตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>ละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<option value=\"\" selected=\"true\">-</option>"
msgstr "<option value=\"\" selected=\"true\">-</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">ประเทศ...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">รัฐ / จังหวัด...</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        คุณสามารถค้นหารถเข็นที่ถูกละทิ้งทั้งหมดได้ที่นี่ เช่น รถเข็นที่สร้างขึ้นโดยผู้เยี่ยมชมเว็บไซต์ของคุณเมื่อหนึ่งชั่วโมงที่แล้วที่ยังไม่ได้รับการยืนยัน</p>\n"
"                        <p>คุณควรส่งอีเมลถึงลูกค้าเพื่อเชิญชวนพวกเขา!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">เรียงตาม:</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-"
"muted\">ไม่อนุญาตให้เปลี่ยนชื่อบริษัทหรือหมายเลข VAT "
"เมื่อออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                        <i class=\"oi oi-close\"/>"
msgstr ""
"<small class=\"mx-auto\"><b>ล้างตัวกรอง</b></small>\n"
"                        <i class=\"oi oi-close\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">หยิบใส่ตะกร้า</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะเว็บไซต์\" "
"groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">โดย</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"Remove this location\" title=\"Remove this location\"/>"
msgstr ""
"<span class=\"o_order_location\">\n"
"                    <b class=\"o_order_location_name\"/>\n"
"                    <br/>\n"
"                    <i class=\"o_order_location_address\"/>\n"
"                </span>\n"
"                <span class=\"fa fa-times ms-2 o_remove_order_location\" aria-label=\"ลบสถานที่นี้\" title=\"ลบสถานที่นี้\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery_methods
msgid ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">Select to compute delivery rate</span>"
msgstr ""
"<span class=\"o_wsale_delivery_badge_price float-end fw-bold\" "
"name=\"price\">เลือกเพื่อคำนวณอัตราการจัดส่ง</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"oi oi-chevron-left fa-2x oe_unmovable\" role=\"รูปภาพ\" aria-"
"label=\"ก่อนหน้า\" title=\"ก่อนหน้า\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"img\" aria-"
"label=\"Next\" title=\"Next\"/>"
msgstr ""
"<span class=\"oi oi-chevron-right fa-2x oe_unmovable\" role=\"รูปภาพ\" aria-"
"label=\"ถัดไป\" title=\"ถัดไป\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">หรือ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span "
"class=\"s_website_form_label_content\">ให้ข้อเสนอแนะของคุณกับเรา</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr "<span class=\"s_website_form_label_content\">อัปโหลดเอกสาร</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">ข้อมูลอ้างอิงของคุณ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= "
"1\">ขึ้นอยู่กับตัวแปร</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">ตัวกรองเปิดใช้งานอยู่</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>คำสั่ง</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Show on product page</span>"
msgstr "<span>แสดงบนหน้าผลิตภัณฑ์</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>ขออภัย ไม่สามารถยืนยันคำสั่งซื้อของคุณได้ เนื่องจากจำนวนเงินที่คุณชำระไม่ตรงกับจำนวนในรถเข็น\n"
"                        โปรดติดต่อผู้รับผิดชอบของร้านค้าเพื่อขอข้อมูลเพิ่มเติม</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>ดูตัวอย่างวิดีโอ</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong><i class=\"oi oi-arrow-right\"/> View alternatives</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> ดูทางเลือกอื่น</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<strong>No suitable payment option could be found.</strong><br/>"
msgstr "<strong>ไม่พบตัวเลือกการชำระเงินที่เหมาะสม</strong><br/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>ทั้งหมด:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>ทั้งหมด</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>คำเตือน!</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- เนื้อหา -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">มีบางอย่างอยู่ในรถเข็นของคุณ</h1>\n"
"                    คุณต้องการดำเนินการซื้อให้เสร็จสิ้นหรือไม่<br><br>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\">\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] ชุดโต๊ะทำงาน</strong><br><t t-out=\"line.name or ''\">[FURN_7800] ชุดโต๊ะทำงาน ชุดโต๊ะทำงาน สีน้ำตาลดำ: เก้าอี้ + โต๊ะ + ลิ้นชัก</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">หน่วย</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            สั่งต่อ\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"></t>\n"
"                    <div style=\"text-align: center;\"><strong>ขอบคุณสำหรับการช้อปปิ้งกับ <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "<u>เงื่อนไขและข้อกำหนด</u>"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"คำอธิบายของผลิตภัณฑ์ที่คุณต้องการสื่อสารกับลูกค้าของคุณ "
"คำอธิบายนี้จะถูกคัดลอกไปยังทุกใบสั่งขาย ใบส่งสินค้า "
"และใบกำกับสินค้า/ใบลดหนี้ลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"คำอธิบายโดยละเอียดที่จัดรูปแบบเพื่อโปรโมทผลิตภัณฑ์ของคุณในหน้านี้ ใช้ '/' "
"เพื่อค้นหาฟีเจอร์เพิ่มเติม"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"สินค้าสามารถเป็นได้ทั้งสินค้าที่จับต้องได้หรือบริการที่คุณขายให้กับลูกค้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "คำอธิบายสั้น ๆ ที่จะปรากฏบนเอกสารด้วย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "ละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "ตะกร้าที่ถูกละทิ้ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "ตะกร้าที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "กู้คืนตะกร้าที่ถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "ความล่าช้าที่ถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "อีเมลที่ถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "เกี่ยวกับการขายสินค้าไขว้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "ยอมรับข้อกำหนดและเงื่อนไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "ขนาดไฟล์ที่รองรับ"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "อุปกรณ์เสริมสำหรับผลิตภัณฑ์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"สินค้าเสริมจะปรากฏขึ้นเมื่อลูกค้าตรวจสอบตะกร้าก่อนชำระเงิน "
"(กลยุทธ์การขายไขว้)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "สินค้าเสริม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "การดำเนินการ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "เพิ่ม"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Add To Cart"
msgstr "เพิ่มในตะกร้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "การดำเนินการเพิ่มลงตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "เพิ่มสื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr "เพิ่มแบบฟอร์มที่ปรับแต่งได้ระหว่างการชำระเงิน (หลังที่อยู่)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr "เพิ่มราคาอ้างอิงต่อ UoM ของผลิตภัณฑ์ (เช่น $/kg) นอกเหนือจากราคาขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "เพิ่มราคาขีดฆ่าเพื่อการเปรียบเทียบ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Add one"
msgstr "เพิ่มหนึ่ง"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#, python-format
msgid "Add to Cart"
msgstr "เพิ่มในตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "ปุ่มเพิ่มลงตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Address"
msgstr "ที่อยู่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "ที่อยู่เติมข้อความอัตโนมัติ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "สินค้าทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "รายการราคาทั้งหมด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow customers to pay in person at your stores"
msgstr "ให้ลูกค้าชำระเงินด้วยตนเองที่ร้านค้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "อนุญาตให้ผู้ซื้อเปรียบเทียบสินค้าตามคุณลักษณะของสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr "อนุญาตให้ผู้ใช้ที่ลงชื่อเข้าใช้บันทึกผลิตภัณฑ์ในสิ่งที่อยากได้"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "อนุญาตให้ผู้ใช้เลือกรายการราคานี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"อนุญาตให้ลูกค้าของคุณเพิ่มสินค้าจากคำสั่งซื้อก่อนหน้าลงในตะกร้าของพวกเขา"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "สินค้าทางเลือก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "จำนวนตะกร้าที่ถูกทิ้ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "Anonymous express checkout partner for order %s"
msgstr "พาร์ทเนอร์ชำระเงินด่วนแบบไม่ระบุชื่อสำหรับคำสั่งซื้อ %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "อพาร์ทเมนต์ ห้องชุด ฯลฯ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "นำไปใช้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Are you sure you want to delete this badge?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบเหรียญรางวัลนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "งานที่มอบหมาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "มอบหมายคำสั่งออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "คุณลักษณะ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "ส่งอีเมลการชำระเงินที่ถูกละทิ้งโดยอัตโนมัติ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "คะแนนเฉลี่ย"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Back to cart"
msgstr "กลับไปที่ตะกร้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "พื้นหลัง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Badge"
msgstr "เหรียญรางวัล"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "Badge Text"
msgstr "ข้อความเหรียญรางวัล"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "จำนวนหน่วยฐาน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "ชื่อหน่วยฐาน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "ราคาต่อหน่วยฐาน"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "หน่วยฐาน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "ระวัง!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "ใหญ่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing"
msgstr "ทำรายการบิล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Billing address"
msgstr "ที่อยู่เรียกเก็บเงิน"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "ถังขยะ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "ถังขยะ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"เพิ่มยอดขายของคุณด้วยโปรแกรมหลายประเภท: คูปอง โปรโมชั่น บัตรของขวัญ "
"บัตรสมาชิก สามารถกำหนดเงื่อนไขเฉพาะได้ (สินค้า, ลูกค้า, จำนวนซื้อขั้นต่ำ, "
"ระยะเวลา) รางวัลอาจเป็นส่วนลด (% หรือจำนวน) หรือแถมสินค้าฟรี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "ทั้งสองอย่าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "ด้านล่าง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "กล่อง"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "กล่อง"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "แบรนด์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button URL"
msgstr "ปุ่ม URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "ปุ่ม url"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "ปุ่ม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "ซื้อตอนนี้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#, python-format
msgid "Buy now"
msgstr "ซื้อตอนนี้"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "ตู้เก็บของ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "สามารถขยายภาพ 1024 ได้หรือไม่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "สามารถเผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "การ์ด"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "ม้าหมุน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "ตะกร้าสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "ปริมาณตะกร้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "อีเมลกู้คืนตะกร้าสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "ส่งอีเมลกู้คืนตะกร้าสินค้าแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "รถเข็นจะถูกตั้งสถานะว่าละทิ้งหลังจากเกิดความล่าช้านี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "หมวดหมู่"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"หมวดหมู่ที่ใช้เพื่อเรียกดูผลิตภัณฑ์ของคุณผ่านทาง\n"
"          อินเตอร์เฟซหน้าจอสัมผัส"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "หมวดหมู่:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "หมวดหมู่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "คำอธิบายหมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "หมวดหมู่:"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "เก้าอี้"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "เก้าอี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ไม่อนุญาตให้เปลี่ยนหมายเลข VAT เมื่อออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"ไม่อนุญาตให้เปลี่ยนชื่อบริษัทเมื่อมีการออกเอกสารสำหรับบัญชีของคุณแล้ว "
"โปรดติดต่อเราโดยตรงสำหรับการดำเนินการนี้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"ไม่อนุญาตให้เปลี่ยนชื่อของคุณเมื่อมีการออกใบแจ้งหนี้สำหรับบัญชีของคุณแล้ว "
"กรุณาติดต่อเราโดยตรงสำหรับการดำเนินการนี้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Checkout"
msgstr "เช็คเอาท์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "หน้าชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "หมวดหมู่ย่อย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_delivery
msgid "Choose a delivery method"
msgstr "เลือกวิธีการจัดส่ง"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "คริสต์มาส"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "เมือง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Clear Filters"
msgstr "ล้างตัวกรอง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr "คลิก <i>'ใหม่'</i> ที่มุมบนขวาเพื่อสร้างสินค้าชิ้นแรกของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "คลิกที่นี้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click here to open the reporting menu"
msgstr "คลิกที่นี่เพื่อเปิดเมนูการรายงาน"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Save</em> to create the product."
msgstr "คลิกที่<em>บันทึก</em>เพื่อสร้างผลิตภัณฑ์"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr "คลิกที่ปุ่มเพื่อให้ลูกค้าของคุณเห็น"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#, python-format
msgid "Close"
msgstr "ปิด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr "ยุบหมวดหมู่แบบเรียกซ้ำ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "สี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "คอลัมน์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr ""
"รายการของชื่อผลิตภัณฑ์ บาร์โค้ด "
"หรือข้อมูลอ้างอิงภายในที่คั่นด้วยเครื่องหมายจุลภาค"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Company Name"
msgstr "ชื่อบริษัท"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "เทียบกับราคา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "เปรียบเทียบราคา"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "ส่วนประกอบ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "คำนวณค่าขนส่งและจัดส่งด้วย Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "คํานวณค่าใช้จ่ายการขนส่งและจัดส่งกับ bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "คำนวณค่าจัดส่งตามคำสั่ง"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "กำหนดค่าแบบฟอร์ม"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Confirm"
msgstr "ยืนยัน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "ยืนยันการสั่งซื้อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "ยืนยันคำสั่งซื้อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "ยืนยันคำสั่ง"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "ติดต่อเรา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "URL ปุ่มติดต่อเรา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Content"
msgstr "เนื้อหา"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
#, python-format
msgid "Continue checkout"
msgstr "ไปที่การชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"ดำเนินการชำระเงิน\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Continue shopping"
msgstr "ชอปปิ้งต่อ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "โซฟา"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "ประเทศ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "สร้าง"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "สร้างสินค้าใหม่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "วันที่สร้าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "หมวดหมุ่ปัจจุบันหรือทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "หน่วยวัดแบบกำหนดเอง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "ลูกค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "บัญชีของลูกค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "บัญชีลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "ประเทศลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "รีวิวของลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"ลูกค้าจำเป็นต้องลงชื่อเข้าใช้ ไม่เช่นนั้นจะไม่ทราบที่อยู่อีเมล\n"
"\n"
"- หากผู้มีโอกาสเป็นลูกค้าสร้างขั้นตอนการชำระเงินที่ถูกละทิ้งอย่างน้อยหนึ่งรายการ แล้วทำการชำระเงินให้เสร็จสิ้นก่อนที่จะส่งอีเมลสำรอง อีเมลจะไม่ถูกส่ง\n"
"\n"
"- หากผู้ใช้ส่งอีเมลสำรองด้วยตนเอง อีเมลจะไม่ถูกส่งเป็นครั้งที่สอง\n"
"\n"
"- หากเกิดข้อผิดพลาดในการประมวลผลการชำระเงินเมื่อลูกค้าพยายามชำระเงินให้เสร็จสิ้น อีเมลจะไม่ถูกส่ง\n"
"\n"
"- หากร้านค้าของคุณไม่รองรับการจัดส่งไปยังที่อยู่ของลูกค้า อีเมลจะไม่ถูกส่ง\n"
"\n"
"- หากไม่มีผลิตภัณฑ์ใดในขั้นตอนการชำระเงินพร้อมสำหรับการซื้อ (เช่น สินค้าคงคลังว่างเปล่า) อีเมลจะไม่ถูกส่ง\n"
"\n"
"- หากสินค้าทั้งหมดในขั้นตอนการชำระเงินไม่มีค่าใช้จ่าย และลูกค้าไม่ได้ไปที่หน้าการจัดส่งเพื่อเพิ่มค่าธรรมเนียมการจัดส่ง หรือค่าธรรมเนียมการจัดส่งฟรี อีเมลจะไม่ถูกส่ง"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "ลูกค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "ปรับแต่งเทมเพลตอีเมลที่ถูกละทิ้ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
#: code:addons/website_sale/models/res_config_settings.py:0
#, python-format
msgid "Customize Email Templates"
msgstr "ปรับแต่งเทมเพลตอีเมล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "ตัวเชื่อมต่อ DHL Express"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "วิธีการการจัดส่ง DHL "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr "วางบล็อกสำเร็จรูปที่นี่เพื่อให้สามารถใช้ได้ในทุกผลิตภัณฑ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "เริ่มต้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "ค่าเริ่มต้น (1/1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "สกุลเงินเริ่มต้น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr "รายการราคาเริ่มต้น ถ้ามี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "ชนิดค่าเริ่มต้น"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr "กำหนดหน่วยแบบกำหนดเองเพื่อแสดงราคาต่อหน่วยของฟิลด์หน่วยวัด"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "กำหนดหมวดหมู่ใหม่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Badge"
msgstr "ลบเหรียญรางวัล"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery"
msgstr "การจัดส่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "จำนวนการจัดส่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__access_point_address
msgid "Delivery Point Address"
msgstr "ที่อยู่จุดส่งสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_delivery
msgid "Delivery will be updated after choosing a new delivery method"
msgstr "การจัดส่งจะได้รับการอัปเดตหลังจากเลือกวิธีการจัดส่งใหม่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "คำอธิบาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "คำอธิบายที่แสดงบนอีคอมเมิร์ซและในใบเสนอราคาออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "รายละเอียดสำหรับการเสนอราคาทางออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "รายละเอียดสำหรับเว็บไซต์"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "โต๊ะ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "กำหนดลำดับการแสดงผลในเว็บไซต์อีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "ไดเจส"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "ปิดการใช้งาน (ซื้อในฐานะผู้เยี่ยมชม)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "รหัสส่วนลด..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "ส่วนลด บัตรสมาชิก และบัตรของขวัญ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "แสดงราคาสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "ประเภทการแสดง"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"แสดงราคาต่อหน่วยฐานในหน้าอีคอมเมิร์ซของคุณ ตั้งค่าเป็น 0 "
"เพื่อซ่อนสำหรับผลิตภัณฑ์นี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "แสดงอยู่ด้านล่างของหน้าสินค้า"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"แสดงหน่วยที่กำหนดเองสำหรับผลิตภัณฑ์หากมีการกำหนดไว้หรือหน่วยวัดที่เลือกเป็นอย่างอื่น"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "ไม่มีสิทธิ์เข้าถึง ข้ามข้อมูลนี้สำหรับอีเมลไดเจสของผู้ใช้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "คุณต้องการล้างตะกร้าสินค้าของคุณก่อนที่จะเพิ่มสินค้าลงไปหรือไม่?"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "เอกสาร"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
#, python-format
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr "เอกสารที่แสดงบนหน้าผลิตภัณฑ์ไม่สามารถจำกัดเฉพาะตัวเลือกสินค้าเฉพาะได้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr "ดับเบิลคลิกที่นี้เพื่อกำหนดรูปภาพที่อธิบายผลิตภัณฑ์ของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "ลากบล็อกสำเร็จรูปมาที่นี้เพื่อปรับแต่งส่วนหัวสำหรับ \""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "ลากบล็อกเว็บไซต์นี้แล้ววางลงในหน้าเว็บของคุณ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "ลิ้นชัก"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "ลิ้นชัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "ฟิลด์เสริมอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "ข้อมูลเสริมอีคอมเมิร์ซแสดงบนหน้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "อีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "โค้ดโปรโมชั่นอีคอมเมิร์ซ"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "วิธีการการจัดส่ง Easypost "

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "อีคอมเมิร์ซ: การกู้คืนตะกร้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "แก้ไข"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr "แก้ไขราคาสินค้าโดยคลิกที่จำนวนเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "แก้ไขที่อยู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "อีเมล"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "ฝังโค้ด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_delivery
msgid "Enable Shipping"
msgstr "เปิดใช้งานการจัดส่ง"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "ใส่ชื่อสำหรับสินค้าใหม่ของคุณ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "เกิดข้อผิดพลาด! คุณไม่สามารถสร้างหมวดหมู่แบบเรียกซ้ำได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr "รูปภาพเสริม"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Extra Info"
msgstr "ข้อมูลเพิ่มเติม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "สื่อสินค้าพิเศษ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "ขั้นตอนเสริม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "ขั้นตอนเพิ่มเติมระหว่างการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "รูปภาพหลากหลายแบบพิเศษ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "สื่อตัวแปรพิเศษ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "ข้อมูลเสริม"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Featured"
msgstr "ฟีเจอร์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "วิธีการการจัดส่ง FedEx "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "ฟิลด์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "ป้ายฟิลด์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "ชื่อฟิลด์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "เติม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "กรอกที่อยู่ของคุณ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "ฐานะทางการเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Free"
msgstr "ฟรี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "จากเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "ชื่อเต็ม"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "เฟอร์นิเจอร์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "สร้างใบแจ้งหนี้โดยอัตโนมัติเมื่อยืนยันการชำระเงินออนไลน์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "ให้ลำดับการเรียงลำดับเมื่อแสดงรายการประเภทสินค้า"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
#, python-format
msgid "Go to cart"
msgstr "ไปที่ตะกร้าสินค้า"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "ตาราง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "ซ่อนแล้ว"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "ซ่อน 'หยิบลงตะกร้า' เมื่อราคา = 0"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "ชั่วโมง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "ขนาดไฟล์ใหญ่ รูปภาพควรได้รับการปรับให้เหมาะสม/ลดขนาด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "ฉันเห็นด้วยกับ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "ไอดี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr "หากมีการตั้งค่ารูปภาพ สีจะไม่ถูกใช้บนอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr "หากราคาสินค้าเท่ากับ 0 ให้แทนที่ 'หยิบลงตะกร้า' ด้วย 'ติดต่อเรา'"

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"หากตั้งค่าไว้ "
"จะถูกส่งไปยังผู้เยี่ยมชมที่มีการยืนยันตัวตนที่ได้ละทิ้งตะกร้าสินค้าของพวกเขา"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"หากคุณกำลังสั่งซื้อสำหรับบุคคลภายนอก โปรดสั่งซื้อผ่านทางแบ็กเอนด์ "
"หากคุณต้องการเปลี่ยนชื่อหรือที่อยู่อีเมลของคุณ "
"โปรดดำเนินการในการตั้งค่าบัญชีหรือติดต่อผู้ดูแลระบบของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "หากคุณคิดว่าเกิดข้อผิดพลาด โปรดติดต่อผู้ดูแลเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "รูปภาพ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "รูปภาพ 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "รูปภาพ 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "รูปภาพ 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "รูปภาพ 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "ชื่อรูปภาพ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "ระยะห่างของภาพ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "ซูมภาพ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "ขนาดรูปภาพ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "ความกว้างของรูปภาพ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "ชำระเงินทันทีแทนที่จะเพิ่มลงตะกร้า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "อีเมลไม่ถูกต้อง! กรุณาใส่อีเมล์ที่ถูกต้อง."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid image"
msgstr "รูปภาพไม่ถูกต้อง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "ออกใบแจ้งหนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "นโยบายการแจ้งหนี้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "เผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "ออกใบแจ้งหนี้ให้กับลูกค้า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "ห้ามแก้ไขใบสั่งขายที่ไม่อยู่ในสถานะร่าง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"ดูเหมือนว่าวิธีการจัดส่งจะเข้ากันไม่ได้กับที่อยู่ของคุณ "
"โปรดรีเฟรชหน้าแล้วลองอีกครั้ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid ""
"It seems that there is already a transaction for your order, you can not "
"change the delivery method anymore"
msgstr ""
"ดูเหมือนว่ามีการทำธุรกรรมสำหรับการสั่งซื้อของคุณแล้ว "
"คุณไม่สามารถเปลี่ยนวิธีการจัดส่งได้อีกต่อไป"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#, python-format
msgid "Item(s) added to your cart"
msgstr "เพิ่มสินค้าลงในตะกร้าสินค้าของคุณแล้ว"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกประจำวัน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "ยอดขายรวมเว็บไซต์ Kpi "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "โคมไฟ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "โคมไฟ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "แนวนอน (4/3)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "เดือนที่แล้ว"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "คำสั่งขายออนไลน์ล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "สัปดาห์ที่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "ปีที่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "เลย์เอาต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "ซ้าย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "ให้ลูกค้ากรอกที่อยู่สำหรับจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "ให้ลูกค้าเลือกจุดจัดส่งของ Mondial Relay"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "เริ่มสร้างสินค้าแรกของคุณ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"ตอนนี้เรามาดูแดชบอร์ดอีคอมเมิร์ซของคุณเพื่อเตรียมเว็บไซต์อีคอมเมิร์ซของคุณให้พร้อมในเวลาอันรวดเร็ว"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "หลอดไฟแยก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "แสดงหัวรายงานแบบบรรทัด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "เชื่อมโยงไลน์คำสั่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "รายการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "แว่นขยายเมื่อวางเมาส์เหนือ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"จดหมายส่งถึงลูกค้าที่ลงชื่อเข้าใช้แล้วซึ่งมีสินค้าพร้อมจำหน่ายในตะกร้าสินค้าเท่านั้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main image"
msgstr "ภาพหลัก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr "จัดการโปรโมชั่น คูปอง บัตรสมาชิก บัตรของขวัญ และ eWallet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr "จัดการรายการราคาเพื่อใช้ราคาเฉพาะต่อประเทศ ลูกค้า ผลิตภัณฑ์ ฯลฯ"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr "บังคับ (ไม่มีผู้เยี่ยมชมที่ชำระเงิน)"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "วิธีที่เข้ามา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "Mondial Relay"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "ตัวเชื่อมต่อรีเลย์ Mondial"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "เลื่อนขึ้นเป็นลำดับแรกสุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "ย้ายไปลำดับสุดดท้าย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "ถัดไป"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "ก่อนหน้า"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "มัลติมีเดีย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "ตะกร้าของฉัน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Name"
msgstr "ชื่อ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Name (A-Z)"
msgstr "ชื่อ (A-Z)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "ชื่อสั้น"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
#, python-format
msgid "New"
msgstr "ใหม่"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "สินค้าใหม่"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "ใหม่!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Newest Arrivals"
msgstr "สินค้ามาใหม่"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "สินค้าใหม่ล่าสุด"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "ถัดไป (ลูกศรขวา)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "No"
msgstr "ไม่"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "ไม่พบตะกร้าที่ถูกทอดทิ้ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
#, python-format
msgid "No pick-up point available for that shipping address"
msgstr "ไม่มีจุดรับสำหรับที่อยู่จัดส่งนั้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "ไม่ได้กำหนดสินค้าไว้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr "ไม่มีผลิตภัณฑ์ที่กำหนดไว้ในหมวดหมู่นี้"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "ยังไม่มีการดูสินค้าสำหรับผู้เยี่ยมชมรายนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "ไม่มีผลลัพธ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr " ไม่พบผลลัพธ์สำหรับ \" "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "ไม่พบผลลัพธ์สำหรับ "

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"ไม่มีวิธีการจัดส่งสำหรับคำสั่งซื้อปัจจุบันและที่อยู่สำหรับการจัดส่งของคุณ "
"โปรดติดต่อเราเพื่อขอข้อมูลเพิ่มเติม"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "No shipping method is selected."
msgstr "ไม่ได้เลือกวิธีการจัดส่ง"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "ไม่มี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "ไม่เผยแพร่"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "ไม่สามารถใช้งานได้กับ %s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "จำนวนตะกร้าที่ถูกละทิ้ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "จำนวนคอลัมน์ตารางในร้านค้า"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "จำนวนสินค้าในตารางของร้าน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_picking
msgid "On Site Payments & Picking"
msgstr "การชำระเงินและการเลือกสินค้าบนเว็บไซต์"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "บนล้อ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "เมื่อคุณคลิกที่ <b>บันทึก</b> สินค้าของคุณจะได้รับการอัปเดต"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "ผลิตภัณฑ์หนึ่งอาจมีแอตทริบิวต์ที่แตกต่างกัน (ขนาด, สี, ...)"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "การขายออนไลน์"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "วิเคราะห์การขายออนไลน์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "เฉาะบริการ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"อนุญาตเฉพาะเว็บไซต์ของบริษัทเท่านั้น\n"
"ปล่อยให้ช่องบริษัทว่างหรือเลือกเว็บไซต์จากบริษัทนั้น"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid "Open Sale Orders"
msgstr "เปิดคำสั่งขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "โอเพ่นซอร์สอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"จำเป็นต้องเพิ่มประสิทธิภาพ! ลดขนาดภาพหรือเพิ่มการตั้งค่าการบีบอัดของคุณ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option for: %s"
msgstr "ตัวเลือกสำหรับ: %s"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order_line.py:0
#, python-format
msgid "Option: %s"
msgstr "ตัวเลือก: %s"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "เลือกได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "ตัวเลือก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "ตัวเลือกลิงก์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "หรือสแกนฉันด้วยแอปธนาคารของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "วันที่สั่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "รายการการสั่งซื้อที่แสดงบนเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "ภาพรวมการสั่งซื้อ"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "คำสั่ง"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "คำสั่งซื้อเป็นใบแจ้งหนี้"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "สินค้าหมด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "หมวดหมู่หลัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "เส้นทางหลัก"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "หลักและของตัวเอง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Pay now"
msgstr "ชำระเงินตอนนี้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Payment"
msgstr "การชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "ข้อมูลการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "วิธีการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "โทเค็นการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "โทเค็นการชำระเงิน"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "ธุรกรรมการชำระเงิน"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Payment is already being processed."
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "ระบบเปิดแบบแป้นเหยียบ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "โทรศัพท์"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr "ทรงยา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "โปรดป้อน URL ของวิดีโอที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "กรุณาดำเนินการตะกร้าสินค้าปัจจุบันของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "ป๊อปอัปเมื่อคลิก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "แนวตั้ง (4/5)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "ตำแหน่ง"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "ป้องกันการขายสินค้าราคาศูนย์"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "ก่อนหน้า (ลูกศรซ้าย)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#, python-format
msgid "Price"
msgstr "ราคา"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - High to Low"
msgstr "ราคา - มากไปน้อย"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Price - Low to High"
msgstr "ราคา - น้อยไปมาก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "ตัวกรองราคา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "ราคาต่อหนึ่งหน่วย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "รายการราคาสำหรับอีคอมเมิร์ซ/เว็บไซต์นี้"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "รายการราคา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_pricelist
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "รายการราคา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "ราคาที่แสดงบนอีคอมเมิร์ซของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "พิมพ์"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "ดำเนินการสั่งซื้อเมื่อได้รับการชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "คำอธิบายสินค้า"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "สินค้า"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "อุปกรณ์เสริมสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "คุณลักษณะสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "ม้าหมุนสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "หมวดหมู่สินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "เครื่องมือเปรียบเทียบสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "เอกสารสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "รูปภาพสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "รูปภาพสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "Product Name"
msgstr "ชื่อสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "หน้าผลิตภัณฑ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr " ฟิลด์พิเศษหน้าผลิตภัณฑ์ "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "คอลัมน์ตารางหน้าผลิตภัณฑ์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "เค้าโครงรูปภาพหน้าผลิตภัณฑ์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr "ระยะห่างระหว่างรูปภาพหน้าผลิตภัณฑ์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "ความกว้างของรูปภาพหน้าผลิตภัณฑ์"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "หน้าผลิตภัณฑ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "หมวดหมู่สินค้าสาธารณะ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "ราคาอ้างอิงผลิตภัณฑ์"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "แท็กสินค้า"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "แท็กสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "ตัวกรองแท็กสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "รายการคุณลักษณะเทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "ค่าคุณสมบัติของเทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "เทมเพลตสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "ตัวแปรสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "ตัวแปรสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "ดูสินค้า"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "ประวัตืการดูสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "ชื่อสินค้า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Product not found"
msgstr "ไม่พบสินค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "สินค้า ribbon"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "สินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "รายการสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "หน้าผลิตภัณฑ์"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "สินค้าที่เพิ่งขายด้วย"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "สินค้าที่เพิ่งขายพร้อมสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Products Ribbon"
msgstr "ริบบิ้นสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "ดูสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "รหัสโปรโมชั่น"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"URL วิดีโอที่ระบุสำหรับ '%s' ไม่ถูกต้อง โปรดป้อน URL ของวิดีโอที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "เผยแพร่แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "ดันลง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "ดันลงไปด้านล่าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "ดันขึ้นไปด้านบน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "ดันขึ้น"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Quantity"
msgstr "ปริมาณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "คลื่นวิทยุ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "การให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "ข้อความเฉลี่ยการให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "การให้คะแนนผลตอบรับล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "การให้คะแนนรูปภาพ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "ค่าการให้คะแนนล่าสุด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "การให้คะแนนความพึงพอใจ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "ข้อความการให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "จำนวนการให้คะแนน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Re-Order"
msgstr "สั่งซื้ออีกครั้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "คำสั่งใหม่"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "สั่งซื้อใหม่จากพอร์ทัล"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "สินค้าที่ขายล่าสุด"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "สินค้าที่ดูล่าสุด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "อีเมลกู้คืนถูกส่งแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "อีเมลกู้คืนที่จะส่ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "เสริมแรงสำหรับการบรรทุกหนัก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "นำออก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "ลบทั้งหมด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "นำออกจากตะกร้า"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
#, python-format
msgid "Remove one"
msgstr "นำออกหนึ่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "แทนที่"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "จำกัดการเผยแพร่ในเว็บไซต์นี้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Resume Order"
msgstr "ดำเนินการสั่งซื้อต่อ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Return to shipping"
msgstr "กลับไปที่การจัดส่ง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Review Order"
msgstr "รีวิวคำสั่งซื้อ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
msgid "Ribbon"
msgstr "Ribbon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr " สีพื้นหลัง Ribbon "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "คลาส Ribbon"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Ribbon html"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "สีข้อความ Ribbon"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "ขวา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "ปรับ SEO ให้เหมาะสม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "การขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "วิเคราะห์การขาย"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "การขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "วิเคราะห์การขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "รายงานวิเคราะห์การขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "รายการคำสั่งขาย"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "ทีมขาย"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "พนักงานขาย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "ตัวอย่าง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "บันทึกที่อยู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "ค้นหาใบสั่งขายที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search bar"
msgstr "แถบค้นหา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "เลือก"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"เลือก <b>สินค้าใหม่</b>เพื่อสร้างและจัดการคุณสมบัติเพื่อเพิ่มยอดขายของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "เลือกปริมาณ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "Select a pick-up point"
msgstr "เลือกจุดรับสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "สามารถเลือกได้"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Send After"
msgstr "ส่งหลังจาก"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "ส่งอีเมลการกู้คืนตะกร้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "ส่งอีเมลการกู้คืน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "ส่งหลังจาก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "ส่งโดยอีเมล"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "ส่งอีเมลถึงลูกค้าที่ละทิ้งตะกร้าสินค้าของพวกเขา"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "ชื่อ Seo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "บริการ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "แชร์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Ship to the same address"
msgstr "จัดส่งไปยังที่อยู่เดียวกัน"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#, python-format
msgid "Shipping"
msgstr "การจัดส่งสินค้า"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "ที่อยู่สำหรับจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "ค่าใช้จ่ายการจัดส่ง"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "วิธีการการจัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Shipping address"
msgstr "ที่อยู่จัดส่ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "วิธีการขนส่ง Shiprocket"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "ร้านค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "ร้านค้า - ชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "ร้านค้า - ขั้นตอนการชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "ร้านค้า - ยืนยันแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "ร้านค้า - สินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "ร้านค้า - เลือกวิธีการชำระเงิน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr "การเรียงลำดับร้านค้าเริ่มต้น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "ตะกร้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "แสดงว่างเปล่า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show b2b Fields"
msgstr "แสดงฟิลด์ b2b"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "แสดงบนอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Show on product page"
msgstr "แสดงบนหน้าผลิตภัณฑ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "แสดง/ซ่อนตะกร้าสินค้า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#, python-format
msgid "Sign In"
msgstr "เข้าสู่ระบบ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "ลงชื่อ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "ลงชื่อเข้าใช้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "ลงชื่อเข้าใช้/ลงทะเบียนเมื่อชำระเงิน"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "นั่งสบาย ๆ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "ไซส์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "ไซส์ X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "ไซส์ Y"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "เอียง"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "เล็ก"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "ขายหมดแล้ว"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "ช่องที่ต้องกรอกบางช่องว่างเปล่า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr "ขออภัย เราไม่สามารถจัดส่งคำสั่งซื้อของคุณได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "เรียงลำดับตาม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "ระบุหน่วย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "รัฐ / จังหวัด"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "สถานะ"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "อยู่ในหน้าผลิตภัณฑ์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "ถนนและหมายเลข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"Stripe Connect is not available in your country, please use another payment "
"provider."
msgstr ""
"Stripe Connect ไม่สามารถใช้งานได้ในประเทศของคุณ "
"โปรดใช้ผู้ให้บริการชำระเงินรายอื่น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "สไตล์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "ยอดรวมย่อย"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"เสนอทางเลือกอื่นให้กับลูกค้าของคุณ (กลยุทธ์การขายต่อยอด) "
"ผลิตภัณฑ์เหล่านั้นจะปรากฏบนหน้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "อุปกรณ์เสริมที่แนะนำ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "อุปกรณ์เสริมที่แนะนำ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "อุปกรณ์เสริมที่แนะนำในตะกร้าสินค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "แท็ก"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "แท็ก"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax Excluded"
msgstr "ไม่รวมภาษี"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
msgid "Tax Included"
msgstr "รวมภาษี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "การระบุภาษี"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr "รวมหรือไม่รวมภาษี ขึ้นอยู่กับการกำหนดค่าของเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "ภาษี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "ข้อความ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "ข้อความที่จะแสดงแทนราคา"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "ขอบคุณสำหรับการสั่งซื้อของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "อันดับ #1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "โทเค็นการเข้าถึงไม่ถูกต้อง"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
msgid ""
"The amount will be displayed strikethroughed on the eCommerce product page"
msgstr "จำนวนเงินจะแสดงขีดทับบนหน้าผลิตภัณฑ์อีคอมเมิร์ซ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has already been paid. Please refresh the page."
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "ตะกร้าสินค้าได้รับการอัปเดตแล้ว กรุณารีเฟรชหน้า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The company of the website you are trying to sale from (%s) is different "
"than the one you want to use (%s)"
msgstr ""
"บริษัทของเว็บไซต์ที่คุณพยายามขาย (%s) แตกต่างจากบริษัทที่คุณต้องการใช้ (%s)"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "URL แบบเต็มเพื่อเข้าถึงเอกสารผ่านเว็บไซต์"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr "ไม่มีชุดค่าผสมที่ให้มา ดังนั้นจึงไม่สามารถเพิ่มลงในตะกร้าได้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr "ไม่มีสินค้าที่ระบุ จึงไม่สามารถหยิบใส่ตะกร้าได้"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr "สินค้าที่ระบุไม่มีราคา จึงไม่สามารถเพิ่มลงตะกร้าได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"โหมดที่เลือกที่นี่้จะนำไปใช้เป็นนโยบายการออกใบแจ้งหนี้สำหรับสินค้าใหม่ใด ๆ "
"ที่สร้างขึ้นแต่ไม่ใช่ของสินค้าที่มีอยู่แล้ว"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "คำสั่งซื้อถูกยกเลิกแล้ว"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""
"สินค้าจะมีจำหน่ายในแต่ละหมวดหมู่อีคอมเมิร์ซดังกล่าว ไปที่ร้านค้า > แก้ไข "
"คลิกบนหน้าและเปิดใช้งาน 'หมวดหมู่' เพื่อดูหมวดหมู่อีคอมเมิร์ซทั้งหมด"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"เวลาสำหรับการทำเครื่องหมายตะกร้าว่าละทิ้งสามารถเปลี่ยนแปลงได้ในการตั้งค่า"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"ค่าของจำนวนหน่ายฐาน ต้องมากกว่า 0 ใช้ 0 เพื่อซ่อนราคาต่อหน่วยสำหรับสินค้านี้"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "ไม่มีการยืนยันคำสั่งซื้อจากเว็บไซต์"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "ยังไม่มีการสั่งซื้อที่ค้างชำระจากเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "ไม่มีการรวมนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "นี่คือตะกร้าปัจจุบันของคุณ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
#, python-format
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"พาร์ทเนอร์รายนี้มีตะกร้าที่มีสินค้าค้างอยู่ "
"โปรดทราบว่ารายการราคาจะไม่ได้รับการอัปเดตบนตะกร้านั้น นอกจากนี้ "
"ตะกร้าอาจไม่แสดงให้ลูกค้าเห็นจนกว่าคุณจะอัปเดตรายการราคาของตะกร้านั้น"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
#, python-format
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "สินค้านี้ไม่มีอยู่ จึงไม่สามารถเพิ่มลงตะกร้าได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "สินค้านี้ไม่มีการรวมกันที่ถูกต้อง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "สินค้านี้ไม่มีจำหน่ายแล้ว"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
#, python-format
msgid "This product is not available for purchase."
msgstr "สินค้านี้ไม่มีจำหน่าย"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "สินค้านี้ไม่ได้รับการเผยแพร่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "ไม่มีรหัสโปรโมชั่นนี้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "Thumbnails"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"หากต้องการส่งคำเชิญในโหมด B2B "
"ให้เปิดรายชื่อผู้ติดต่อหรือเลือกหลายรายการในมุมมองรายการแล้วคลิกตัวเลือก "
"'จัดการการเข้าถึงพอร์ทัล' ในเมนูดรอปดาวน์ *การดำเนินการ*"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "บน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "แถบบน"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Total"
msgstr "รวม"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "จำนวนการรับชมสินค้าทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "จำนวนเข้าชมสินค้าทั้งหมด"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"จริงเฉพาะตัวกรองผลิตภัณฑ์ที่ต้องการ product_id "
"เนื่องจากเกี่ยวข้องกับการขายไขว้"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "การแสดงราคา UOM สำหรับอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL ของวิดีโอสำหรับแสดงสินค้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "วิธีการการจัดส่ง USPS "

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "หน่วยวัดราคาต่อหน่วยของสินค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "ยังไม่ได้ชำระ"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "คำสั่งซื้อที่ยังไม่ได้ชำระเงิน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "ยกเลิกการเผยแพร่"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "อัปโหลดไฟล์จากห้องสมุดท้องถิ่นของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr "ใช้ Google Places API เพื่อตรวจสอบที่อยู่ที่ป้อนโดยผู้เยี่ยมชมของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "VAT"
msgstr "ภาษีมูลค่าเพิ่ม (VAT)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Excluded"
msgstr "ไม่รวมภาษีมูลค่าเพิ่ม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "VAT Included"
msgstr "รวมภาษีมูลค่าเพิ่ม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "ตัวแปร"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__ribbon_id
msgid "Variant Ribbon"
msgstr "ริบบิ้นตัวแปรอื่น"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "ตัวแปร"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "แนวตั้ง (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "วิดีโอ URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "ดูสินค้า"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
#, python-format
msgid "View cart"
msgstr "ดูตะกร้าสินค้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "ดูสินค้า"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#, python-format
msgid "Viewer"
msgstr "ผู้ดู"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "การมองเห็น"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "สามารถมองเห็น"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "ปรากฏบนเว็บไซต์ปัจจุบัน"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "มองเห็นได้บนอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "หน้าที่เข้าชม"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "สินค้าที่ได้รับการเยี่ยมชม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "การเข้าชมสินค้าของผู้เยี่ยมชม"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "ประวัติการเข้าชมสินค้าของผู้เยี่ยมชม"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
#, python-format
msgid "Warning"
msgstr "คำเตือน"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "การรับประกัน"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"การรับประกัน ซึ่งออกให้แก่ผู้ซื้อโดยตรงจากผู้ผลิต "
"โดยสัญญาว่าจะซ่อมแซมหรือเปลี่ยนใหม่หากจำเป็นภายในระยะเวลาที่กำหนด"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "เว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "หมวดหมู่สินค้าเว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "หมวดหมู่สาธารณะเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "ลำดับเว็บไซต์"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "เว็บไซต์ร้านค้า"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "ตัวกรอง Snippet ของเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "เว็บไซต์ URL"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "ผู้เยี่ยมชมเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "คำอธิบายเนื้อหาเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "คำสำคัญในคำอธิบายเนื้อหาเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "ชื่อข้อมูลเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "รูป Opengraph บนเว็บไซต์"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr "เว็บไซต์ที่ใช้สร้างใบแจ้งหนี้นี้สำหรับคำสั่งซื้ออีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr "เว็บไซต์ที่ใช้ส่งคำสั่งซื้อนี้สำหรับคำสั่งซื้ออีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "เว็บไซต์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "\"หยิบลงตะกร้า\" ควรทำอย่างไร?"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "ไม่ว่าแท็กจะแสดงบนอีคอมเมิร์ซหรือไม่"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "ไวท์บอร์ด"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "รายการที่อยากได้"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"ด้วยโหมดแรก คุณสามารถกำหนดราคาได้หลายแบบในแบบฟอร์มการกำหนดค่าผลิตภัณฑ์ "
"(จากแท็บการขาย) ในอันที่สอง คุณจะตั้งราคาและกฎการคำนวณจากรายการราคา"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "ด้วยสามขา"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#, python-format
msgid "Yes"
msgstr "ใช่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"คุณกำลังแก้ไขที่อยู่ <b>สำหรับการเรียกเก็บเงินและที่อยู่สำหรับจัดส่ง</b> ในเวลาเดียวกัน!<br/>\n"
"                                            หากคุณต้องการแก้ไขที่อยู่สำหรับจัดส่ง ให้สร้าง"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "You can't use a video as the product's main image."
msgstr "คุณไม่สามารถใช้วิดีโอเป็นรูปภาพหลักของผลิตภัณฑ์ได้"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "คุณไม่มีคำสั่งซื้อใด ๆ จากเว็บไซต์"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "คุณไม่มีคำสั่งซื้อใด ๆ ที่จะออกใบแจ้งหนี้จากเว็บไซต์"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "คุณทิ้งสินค้าไว้ในตะกร้าของคุณ!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"คุณจะพบตะกร้าทั้งหมดที่ผู้เยี่ยมชมของคุณละทิ้งที่นี้\n"
"               หากพวกเขากรอกที่อยู่เรียบร้อยแล้ว คุณควรส่งอีเมลกู้คืนให้พวกเขา!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""
"ข้อมูลโค้ดแบบไดนามิกของคุณจะแสดงที่นี่...\n"
"                                ข้อความนี้แสดงขึ้นเนื่องจากคุณไม่ได้ระบุทั้งตัวกรองและเทมเพลตที่จะใช้"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "อีเมลของคุณ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "ชื่อของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "ตะกร้าของคุณว่างเปล่า!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr "ตะกร้าของคุณไม่พร้อมที่จะชำระเงิน โปรดตรวจสอบขั้นตอนก่อนหน้า"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "การชำระเงินของคุณได้รับการอนุมัติแล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "ตะกร้าก่อนหน้าของคุณเสร็จสมบูรณ์แล้ว"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "รหัสไปรษณีย์"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "คำนำหน้า Zip"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "วิธีการการจัดส่ง bpost "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_add
msgid "e.g. Cheese Burger"
msgstr "เช่น ชีสเบอร์เกอร์"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "เช่น โคมไฟ ถังขยะ"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "อีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "หมวดหมู่อีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "คำอธิบายอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "การมองเห็นตัวกรองอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "การขายอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "ร้านค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "ตะกร้าสินค้าอีคอมเมิร์ซ"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr "อีคอมเมิร์ซ: ส่งอีเมลถึงลูกค้าเกี่ยวกับตะกร้าสินค้าที่ถูกละทิ้ง"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "หากคุณต้องการตะกร้าสินค้าก่อนหน้าของคุณเข้ากับตะกร้าสินค้าปัจจุบัน"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"หากคุณต้องการคืนค่าตะกร้าก่อนหน้าของคุณ "
"ตะกร้าปัจจุบันของคุณจะถูกแทนที่ด้วยตะกร้าก่อนหน้าของคุณ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "ในหมวดหมู่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "ที่อยู่ใหม่"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "or"
msgstr "หรือ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "นำออก"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_delivery.js:0
#, python-format
msgid "select to see available Pick-Up Locations"
msgstr "เลือกเพื่อดูสถานที่รับสินค้าที่มี"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "ข้อกำหนดและเงื่อนไข"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "เพื่อปฏิบัติตามคำสั่งของคุณ"
