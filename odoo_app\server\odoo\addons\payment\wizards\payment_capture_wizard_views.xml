<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_capture_wizard_view_form" model="ir.ui.view">
        <field name="name">payment.capture.wizard.form</field>
        <field name="model">payment.capture.wizard</field>
        <field name="arch" type="xml">
            <form string="Capture">
                <field name="transaction_ids" invisible="1"/>
                <field name="is_amount_to_capture_valid" invisible="1"/>
                <field name="currency_id" invisible="1"/>
                <field name="support_partial_capture" invisible="1"/>
                <field name="has_draft_children" invisible="1"/>
                <field name="has_remaining_amount" invisible="1"/>
                <div id="alert_draft_capture_tx"
                     role="alert"
                     class="alert alert-warning"
                     invisible="not has_draft_children">
                    <strong>Warning!</strong> There is a partial capture pending. Please wait a
                    moment for it to be processed. Check your payment provider configuration if
                    the capture is still pending after a few minutes.
                </div>
                <group name="readonly_fields">
                    <field name="authorized_amount"/>
                    <field name="captured_amount"
                           invisible="captured_amount &lt;= 0"/>
                    <field name="voided_amount"
                           invisible="voided_amount &lt;= 0"/>
                </group>
                <hr/>
                <group name="input_fields">
                    <label for="amount_to_capture" class="oe_inline"/>
                    <div class="o_row">
                        <field name="amount_to_capture"
                               class="oe_inline"
                               readonly="support_partial_capture == 'full_only'"/>
                        <i class="fa fa-info-circle oe_inline"
                           invisible="support_partial_capture != 'full_only'"
                           title="Some of the transactions you intend to capture can only be captured in full. Handle the transactions individually to capture a partial amount."/>
                    </div>
                    <field name="void_remaining_amount" readonly="not has_remaining_amount"/>
                </group>
                <div id="alert_amount_to_capture_above_authorized_amount"
                     role="alert"
                     class="alert alert-warning mb-2"
                     invisible="is_amount_to_capture_valid">
                    <strong>Warning!</strong> You can not capture a negative amount nor more
                    than <field name='available_amount' class='oe_inline' widget='monetary'/>.
                </div>
                <footer>
                    <button string="Capture" type="object" name="action_capture" class="btn-primary"/>
                    <button string="Close" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
