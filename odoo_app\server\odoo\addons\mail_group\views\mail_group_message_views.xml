<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="mail_group_message_view_list" model="ir.ui.view">
        <field name="name">mail.group.message.view.list</field>
        <field name="model">mail.group.message</field>
        <field name="arch" type="xml">
            <tree sample="1">
                <field name="create_date"/>
                <field name="author_id"/>
                <field name="email_from"/>
                <field name="subject"/>
                <field name="mail_group_id"/>
                <field name="moderation_status"/>
                <field name="is_group_moderated" column_invisible="True"/>
                <button name="action_moderate_accept" string="Accept" title="Accept"
                    type="object" class="btn btn-primary"
                    invisible="moderation_status != 'pending_moderation' or not is_group_moderated"/>
                <button name="%(mail_group_message_reject_action)d"
                    string="Reject" title="Remove message with explanation"
                    type="action" class="btn btn-secondary"
                    invisible="moderation_status != 'pending_moderation' or not is_group_moderated"
                    context="{'default_mail_group_message_id': id, 'default_action': 'reject'}" />
                <button name="action_moderate_allow" string="Whitelist"
                    title="Add this email address to white list of people and accept all pending messages from the same author."
                    type="object" class="btn btn-secondary"
                    invisible="moderation_status != 'pending_moderation' or not is_group_moderated"/>
                <button name="%(mail_group_message_reject_action)d"
                    string="Ban" title="Ban this email address and reject all pending messages from the same author and send an email to the author"
                    type="action" class="btn btn-secondary"
                    invisible="moderation_status != 'pending_moderation' or not is_group_moderated"
                    context="{'default_mail_group_message_id': id, 'default_action': 'ban'}" />
                <button name="action_moderate_accept" string="Send" title="Send"
                    type="object" class="btn btn-primary"
                    invisible="moderation_status != 'pending_moderation' or is_group_moderated"/>
            </tree>
        </field>
    </record>
    <record id="mail_group_message_view_form" model="ir.ui.view">
        <field name="name">mail.group.message.view.form</field>
        <field name="model">mail.group.message</field>
        <field name="arch" type="xml">
            <form string="Group Message" class="o_mail_group_message_form">
                <header>
                    <button name="action_moderate_accept" string="Accept" title="Accept"
                        type="object" class="btn btn-primary"
                        invisible="moderation_status != 'pending_moderation' or not is_group_moderated"/>
                    <button name="%(mail_group_message_reject_action)d"
                        string="Reject" title="Remove message with explanation"
                        type="action" class="btn btn-secondary"
                        invisible="moderation_status != 'pending_moderation' or not is_group_moderated"
                        context="{'default_mail_group_message_id': id, 'default_action': 'reject'}" />
                    <button name="action_moderate_allow" string="Whitelist"
                        title="Add this email address to white list of people and accept all pending messages from the same author."
                        type="object" class="btn btn-secondary"
                        invisible="not is_group_moderated"/>
                    <button name="%(mail_group_message_reject_action)d"
                        string="Ban" title="Ban this email address and reject all pending messages from the same author and send an email to the author"
                        type="action" class="btn btn-secondary"
                        invisible="moderation_status != 'pending_moderation' or not is_group_moderated"
                        context="{'default_mail_group_message_id': id, 'default_action': 'ban'}" />
                    <button name="action_moderate_accept" string="Send" title="Send"
                        type="object" class="btn btn-primary"
                        invisible="moderation_status != 'pending_moderation' or is_group_moderated"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Rejected" bg_color="text-bg-danger"
                        invisible="moderation_status != 'rejected'"/>
                    <widget name="web_ribbon" title="Accepted" bg_color="text-bg-success"
                        invisible="moderation_status != 'accepted'"/>
                    <group>
                        <field name="mail_message_id" invisible="1"/>
                        <field name="moderation_status" invisible="1"/>
                        <field name="is_group_moderated" invisible="1"/>
                        <field name="subject"/>
                        <field name="author_id"/>
                        <label for="email_from" string="From"/>
                        <div>
                            <field name="email_from" nolabel="1"/>
                            <span class="ms-2 badge text-bg-success" invisible="author_moderation != 'allow'">Whitelisted</span>
                            <span class="ms-2 badge text-bg-danger" invisible="author_moderation != 'ban'">Banned</span>
                            <field name="author_moderation" invisible="1"/>
                        </div>
                        <field name="mail_group_id"/>
                        <field name="create_date"/>
                        <field name="attachment_ids" widget="many2many_binary"/>
                        <field name="body" options="{'style-inline': true}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="mail_group_message_view_search" model="ir.ui.view">
        <field name="name">mail.group.message.view.search</field>
        <field name="model">mail.group.message</field>
        <field name="arch" type="xml">
            <search string="Search Group Message">
                <field name="mail_group_id"/>
                <field name="email_from"/>
                <field name="author_id"/>
                <field name="moderation_status"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="group" name="group_by_group" context="{'group_by': 'mail_group_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="mail_group_message_action" model="ir.actions.act_window">
        <field name="name">Messages</field>
        <field name="res_model">mail.group.message</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">No Messages in this list yet!</p>
            <p>When people send an email to the alias of the list, they will appear here.</p>
        </field>
    </record>
</odoo>
