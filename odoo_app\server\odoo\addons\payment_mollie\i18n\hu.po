# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_mollie
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_mollie
#: model_terms:ir.ui.view,arch_db:payment_mollie.payment_provider_form
msgid "API Key"
msgstr "API kulcs"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Canceled payment with status: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__code
msgid "Code"
msgstr "Kód"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields.selection,name:payment_mollie.selection__payment_provider__code__mollie
msgid "Mollie"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,field_description:payment_mollie.field_payment_provider__mollie_api_key
msgid "Mollie API Key"
msgstr ""

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_provider
msgid "Payment Provider"
msgstr "Fizetési szolgáltató"

#. module: payment_mollie
#: model:ir.model,name:payment_mollie.model_payment_transaction
msgid "Payment Transaction"
msgstr "Fizetési tranzakció"

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__mollie_api_key
msgid ""
"The Test or Live API Key depending on the configuration of the provider"
msgstr ""

#. module: payment_mollie
#. odoo-python
#: code:addons/payment_mollie/models/payment_provider.py:0
#, python-format
msgid ""
"The communication with the API failed. Mollie gave us the following "
"information: %s"
msgstr ""

#. module: payment_mollie
#: model:ir.model.fields,help:payment_mollie.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""
