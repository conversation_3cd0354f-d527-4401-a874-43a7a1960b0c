<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="textwrap — Text wrapping and filling" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/textwrap.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/textwrap.py The textwrap module provides some convenience functions, as well as TextWrapper, the class that does all the work. If you’re just wrapping or filling one or two text st..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/textwrap.py The textwrap module provides some convenience functions, as well as TextWrapper, the class that does all the work. If you’re just wrapping or filling one or two text st..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>textwrap — Text wrapping and filling &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="unicodedata — Unicode Database" href="unicodedata.html" />
    <link rel="prev" title="difflib — Helpers for computing deltas" href="difflib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/textwrap.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="difflib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">difflib</span></code> — Helpers for computing deltas</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unicodedata.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/textwrap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unicodedata.html" title="unicodedata — Unicode Database"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="difflib.html" title="difflib — Helpers for computing deltas"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" accesskey="U">Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code> — Text wrapping and filling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-textwrap">
<span id="textwrap-text-wrapping-and-filling"></span><h1><a class="reference internal" href="#module-textwrap" title="textwrap: Text wrapping and filling"><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code></a> — Text wrapping and filling<a class="headerlink" href="#module-textwrap" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/textwrap.py">Lib/textwrap.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-textwrap" title="textwrap: Text wrapping and filling"><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code></a> module provides some convenience functions,
as well as <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a>, the class that does all the work.
If you’re just wrapping or filling one or two text strings, the convenience
functions should be good enough; otherwise, you should use an instance of
<a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> for efficiency.</p>
<dl class="py function">
<dt class="sig sig-object py" id="textwrap.wrap">
<span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">wrap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">70</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial_indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subsequent_indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expand_tabs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replace_whitespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fix_sentence_endings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_long_words</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">drop_whitespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_on_hyphens</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tabsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">8</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">placeholder</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">[...]'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.wrap" title="Link to this definition">¶</a></dt>
<dd><p>Wraps the single paragraph in <em>text</em> (a string) so every line is at most
<em>width</em> characters long.  Returns a list of output lines, without final
newlines.</p>
<p>Optional keyword arguments correspond to the instance attributes of
<a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a>, documented below.</p>
<p>See the <a class="reference internal" href="#textwrap.TextWrapper.wrap" title="textwrap.TextWrapper.wrap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">TextWrapper.wrap()</span></code></a> method for additional details on how
<a class="reference internal" href="#textwrap.wrap" title="textwrap.wrap"><code class="xref py py-func docutils literal notranslate"><span class="pre">wrap()</span></code></a> behaves.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="textwrap.fill">
<span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">fill</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">70</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">initial_indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subsequent_indent</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">expand_tabs</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">replace_whitespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fix_sentence_endings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_long_words</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">drop_whitespace</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_on_hyphens</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tabsize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">8</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_lines</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">placeholder</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">[...]'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.fill" title="Link to this definition">¶</a></dt>
<dd><p>Wraps the single paragraph in <em>text</em>, and returns a single string containing the
wrapped paragraph.  <a class="reference internal" href="#textwrap.fill" title="textwrap.fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">fill()</span></code></a> is shorthand for</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">wrap</span><span class="p">(</span><span class="n">text</span><span class="p">,</span> <span class="o">...</span><span class="p">))</span>
</pre></div>
</div>
<p>In particular, <a class="reference internal" href="#textwrap.fill" title="textwrap.fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">fill()</span></code></a> accepts exactly the same keyword arguments as
<a class="reference internal" href="#textwrap.wrap" title="textwrap.wrap"><code class="xref py py-func docutils literal notranslate"><span class="pre">wrap()</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="textwrap.shorten">
<span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">shorten</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">width</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fix_sentence_endings</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_long_words</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">break_on_hyphens</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">True</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">placeholder</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">'</span> <span class="pre">[...]'</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.shorten" title="Link to this definition">¶</a></dt>
<dd><p>Collapse and truncate the given <em>text</em> to fit in the given <em>width</em>.</p>
<p>First the whitespace in <em>text</em> is collapsed (all whitespace is replaced by
single spaces).  If the result fits in the <em>width</em>, it is returned.
Otherwise, enough words are dropped from the end so that the remaining words
plus the <em>placeholder</em> fit within <em>width</em>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">textwrap</span><span class="o">.</span><span class="n">shorten</span><span class="p">(</span><span class="s2">&quot;Hello  world!&quot;</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">12</span><span class="p">)</span>
<span class="go">&#39;Hello world!&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">textwrap</span><span class="o">.</span><span class="n">shorten</span><span class="p">(</span><span class="s2">&quot;Hello  world!&quot;</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">11</span><span class="p">)</span>
<span class="go">&#39;Hello [...]&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">textwrap</span><span class="o">.</span><span class="n">shorten</span><span class="p">(</span><span class="s2">&quot;Hello world&quot;</span><span class="p">,</span> <span class="n">width</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">placeholder</span><span class="o">=</span><span class="s2">&quot;...&quot;</span><span class="p">)</span>
<span class="go">&#39;Hello...&#39;</span>
</pre></div>
</div>
<p>Optional keyword arguments correspond to the instance attributes of
<a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a>, documented below.  Note that the whitespace is
collapsed before the text is passed to the <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> <a class="reference internal" href="#textwrap.fill" title="textwrap.fill"><code class="xref py py-meth docutils literal notranslate"><span class="pre">fill()</span></code></a>
function, so changing the value of <a class="reference internal" href="#textwrap.TextWrapper.tabsize" title="textwrap.TextWrapper.tabsize"><code class="xref py py-attr docutils literal notranslate"><span class="pre">tabsize</span></code></a>, <a class="reference internal" href="#textwrap.TextWrapper.expand_tabs" title="textwrap.TextWrapper.expand_tabs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">expand_tabs</span></code></a>,
<a class="reference internal" href="#textwrap.TextWrapper.drop_whitespace" title="textwrap.TextWrapper.drop_whitespace"><code class="xref py py-attr docutils literal notranslate"><span class="pre">drop_whitespace</span></code></a>, and <a class="reference internal" href="#textwrap.TextWrapper.replace_whitespace" title="textwrap.TextWrapper.replace_whitespace"><code class="xref py py-attr docutils literal notranslate"><span class="pre">replace_whitespace</span></code></a> will have no effect.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="textwrap.dedent">
<span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">dedent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.dedent" title="Link to this definition">¶</a></dt>
<dd><p>Remove any common leading whitespace from every line in <em>text</em>.</p>
<p>This can be used to make triple-quoted strings line up with the left edge of the
display, while still presenting them in the source code in indented form.</p>
<p>Note that tabs and spaces are both treated as whitespace, but they are not
equal: the lines <code class="docutils literal notranslate"><span class="pre">&quot;</span>&#160; <span class="pre">hello&quot;</span></code> and <code class="docutils literal notranslate"><span class="pre">&quot;\thello&quot;</span></code> are considered to have no
common leading whitespace.</p>
<p>Lines containing only whitespace are ignored in the input and normalized to a
single newline character in the output.</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">test</span><span class="p">():</span>
    <span class="c1"># end first line with \ to avoid the empty line!</span>
    <span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;&#39;&#39;</span><span class="se">\</span>
<span class="s1">    hello</span>
<span class="s1">      world</span>
<span class="s1">    &#39;&#39;&#39;</span>
    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">s</span><span class="p">))</span>          <span class="c1"># prints &#39;    hello\n      world\n    &#39;</span>
    <span class="nb">print</span><span class="p">(</span><span class="nb">repr</span><span class="p">(</span><span class="n">dedent</span><span class="p">(</span><span class="n">s</span><span class="p">)))</span>  <span class="c1"># prints &#39;hello\n  world\n&#39;</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="textwrap.indent">
<span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">indent</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">predicate</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.indent" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>prefix</em> to the beginning of selected lines in <em>text</em>.</p>
<p>Lines are separated by calling <code class="docutils literal notranslate"><span class="pre">text.splitlines(True)</span></code>.</p>
<p>By default, <em>prefix</em> is added to all lines that do not consist
solely of whitespace (including any line endings).</p>
<p>For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="s1">&#39;hello</span><span class="se">\n\n</span><span class="s1"> </span><span class="se">\n</span><span class="s1">world&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">indent</span><span class="p">(</span><span class="n">s</span><span class="p">,</span> <span class="s1">&#39;  &#39;</span><span class="p">)</span>
<span class="go">&#39;  hello\n\n \n  world&#39;</span>
</pre></div>
</div>
<p>The optional <em>predicate</em> argument can be used to control which lines
are indented. For example, it is easy to add <em>prefix</em> to even empty
and whitespace-only lines:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">indent</span><span class="p">(</span><span class="n">s</span><span class="p">,</span> <span class="s1">&#39;+ &#39;</span><span class="p">,</span> <span class="k">lambda</span> <span class="n">line</span><span class="p">:</span> <span class="kc">True</span><span class="p">))</span>
<span class="go">+ hello</span>
<span class="go">+</span>
<span class="go">+</span>
<span class="go">+ world</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#textwrap.wrap" title="textwrap.wrap"><code class="xref py py-func docutils literal notranslate"><span class="pre">wrap()</span></code></a>, <a class="reference internal" href="#textwrap.fill" title="textwrap.fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">fill()</span></code></a> and <a class="reference internal" href="#textwrap.shorten" title="textwrap.shorten"><code class="xref py py-func docutils literal notranslate"><span class="pre">shorten()</span></code></a> work by creating a
<a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> instance and calling a single method on it.  That
instance is not reused, so for applications that process many text
strings using <a class="reference internal" href="#textwrap.wrap" title="textwrap.wrap"><code class="xref py py-func docutils literal notranslate"><span class="pre">wrap()</span></code></a> and/or <a class="reference internal" href="#textwrap.fill" title="textwrap.fill"><code class="xref py py-func docutils literal notranslate"><span class="pre">fill()</span></code></a>, it may be more efficient to
create your own <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> object.</p>
<p>Text is preferably wrapped on whitespaces and right after the hyphens in
hyphenated words; only then will long words be broken if necessary, unless
<a class="reference internal" href="#textwrap.TextWrapper.break_long_words" title="textwrap.TextWrapper.break_long_words"><code class="xref py py-attr docutils literal notranslate"><span class="pre">TextWrapper.break_long_words</span></code></a> is set to false.</p>
<dl class="py class">
<dt class="sig sig-object py" id="textwrap.TextWrapper">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">textwrap.</span></span><span class="sig-name descname"><span class="pre">TextWrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="o"><span class="pre">**</span></span><span class="n"><span class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.TextWrapper" title="Link to this definition">¶</a></dt>
<dd><p>The <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> constructor accepts a number of optional keyword
arguments.  Each keyword argument corresponds to an instance attribute, so
for example</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">wrapper</span> <span class="o">=</span> <span class="n">TextWrapper</span><span class="p">(</span><span class="n">initial_indent</span><span class="o">=</span><span class="s2">&quot;* &quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>is the same as</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">wrapper</span> <span class="o">=</span> <span class="n">TextWrapper</span><span class="p">()</span>
<span class="n">wrapper</span><span class="o">.</span><span class="n">initial_indent</span> <span class="o">=</span> <span class="s2">&quot;* &quot;</span>
</pre></div>
</div>
<p>You can re-use the same <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> object many times, and you can
change any of its options through direct assignment to instance attributes
between uses.</p>
<p>The <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> instance attributes (and keyword arguments to the
constructor) are as follows:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.width">
<span class="sig-name descname"><span class="pre">width</span></span><a class="headerlink" href="#textwrap.TextWrapper.width" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">70</span></code>) The maximum length of wrapped lines.  As long as there
are no individual words in the input text longer than <a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a>,
<a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> guarantees that no output line will be longer than
<a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a> characters.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.expand_tabs">
<span class="sig-name descname"><span class="pre">expand_tabs</span></span><a class="headerlink" href="#textwrap.TextWrapper.expand_tabs" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">True</span></code>) If true, then all tab characters in <em>text</em> will be
expanded to spaces using the <a class="reference internal" href="stdtypes.html#str.expandtabs" title="str.expandtabs"><code class="xref py py-meth docutils literal notranslate"><span class="pre">expandtabs()</span></code></a> method of <em>text</em>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.tabsize">
<span class="sig-name descname"><span class="pre">tabsize</span></span><a class="headerlink" href="#textwrap.TextWrapper.tabsize" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">8</span></code>) If <a class="reference internal" href="#textwrap.TextWrapper.expand_tabs" title="textwrap.TextWrapper.expand_tabs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">expand_tabs</span></code></a> is true, then all tab characters
in <em>text</em> will be expanded to zero or more spaces, depending on the
current column and the given tab size.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.replace_whitespace">
<span class="sig-name descname"><span class="pre">replace_whitespace</span></span><a class="headerlink" href="#textwrap.TextWrapper.replace_whitespace" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">True</span></code>) If true, after tab expansion but before wrapping,
the <a class="reference internal" href="#textwrap.wrap" title="textwrap.wrap"><code class="xref py py-meth docutils literal notranslate"><span class="pre">wrap()</span></code></a> method will replace each whitespace character
with a single space.  The whitespace characters replaced are
as follows: tab, newline, vertical tab, formfeed, and carriage
return (<code class="docutils literal notranslate"><span class="pre">'\t\n\v\f\r'</span></code>).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <a class="reference internal" href="#textwrap.TextWrapper.expand_tabs" title="textwrap.TextWrapper.expand_tabs"><code class="xref py py-attr docutils literal notranslate"><span class="pre">expand_tabs</span></code></a> is false and <a class="reference internal" href="#textwrap.TextWrapper.replace_whitespace" title="textwrap.TextWrapper.replace_whitespace"><code class="xref py py-attr docutils literal notranslate"><span class="pre">replace_whitespace</span></code></a> is true,
each tab character will be replaced by a single space, which is <em>not</em>
the same as tab expansion.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <a class="reference internal" href="#textwrap.TextWrapper.replace_whitespace" title="textwrap.TextWrapper.replace_whitespace"><code class="xref py py-attr docutils literal notranslate"><span class="pre">replace_whitespace</span></code></a> is false, newlines may appear in the
middle of a line and cause strange output. For this reason, text should
be split into paragraphs (using <a class="reference internal" href="stdtypes.html#str.splitlines" title="str.splitlines"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.splitlines()</span></code></a> or similar)
which are wrapped separately.</p>
</div>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.drop_whitespace">
<span class="sig-name descname"><span class="pre">drop_whitespace</span></span><a class="headerlink" href="#textwrap.TextWrapper.drop_whitespace" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">True</span></code>) If true, whitespace at the beginning and ending of
every line (after wrapping but before indenting) is dropped.
Whitespace at the beginning of the paragraph, however, is not dropped
if non-whitespace follows it.  If whitespace being dropped takes up an
entire line, the whole line is dropped.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.initial_indent">
<span class="sig-name descname"><span class="pre">initial_indent</span></span><a class="headerlink" href="#textwrap.TextWrapper.initial_indent" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">''</span></code>) String that will be prepended to the first line of
wrapped output.  Counts towards the length of the first line.  The empty
string is not indented.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.subsequent_indent">
<span class="sig-name descname"><span class="pre">subsequent_indent</span></span><a class="headerlink" href="#textwrap.TextWrapper.subsequent_indent" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">''</span></code>) String that will be prepended to all lines of wrapped
output except the first.  Counts towards the length of each line except
the first.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.fix_sentence_endings">
<span class="sig-name descname"><span class="pre">fix_sentence_endings</span></span><a class="headerlink" href="#textwrap.TextWrapper.fix_sentence_endings" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">False</span></code>) If true, <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> attempts to detect
sentence endings and ensure that sentences are always separated by exactly
two spaces.  This is generally desired for text in a monospaced font.
However, the sentence detection algorithm is imperfect: it assumes that a
sentence ending consists of a lowercase letter followed by one of <code class="docutils literal notranslate"><span class="pre">'.'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'!'</span></code>, or <code class="docutils literal notranslate"><span class="pre">'?'</span></code>, possibly followed by one of <code class="docutils literal notranslate"><span class="pre">'&quot;'</span></code> or <code class="docutils literal notranslate"><span class="pre">&quot;'&quot;</span></code>,
followed by a space.  One problem with this algorithm is that it is
unable to detect the difference between “Dr.” in</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="o">...</span><span class="p">]</span> <span class="n">Dr</span><span class="o">.</span> <span class="n">Frankenstein</span><span class="s1">&#39;s monster [...]</span>
</pre></div>
</div>
<p>and “Spot.” in</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="o">...</span><span class="p">]</span> <span class="n">See</span> <span class="n">Spot</span><span class="o">.</span> <span class="n">See</span> <span class="n">Spot</span> <span class="n">run</span> <span class="p">[</span><span class="o">...</span><span class="p">]</span>
</pre></div>
</div>
<p><a class="reference internal" href="#textwrap.TextWrapper.fix_sentence_endings" title="textwrap.TextWrapper.fix_sentence_endings"><code class="xref py py-attr docutils literal notranslate"><span class="pre">fix_sentence_endings</span></code></a> is false by default.</p>
<p>Since the sentence detection algorithm relies on <code class="docutils literal notranslate"><span class="pre">string.lowercase</span></code> for
the definition of “lowercase letter”, and a convention of using two spaces
after a period to separate sentences on the same line, it is specific to
English-language texts.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.break_long_words">
<span class="sig-name descname"><span class="pre">break_long_words</span></span><a class="headerlink" href="#textwrap.TextWrapper.break_long_words" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">True</span></code>) If true, then words longer than <a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a> will be
broken in order to ensure that no lines are longer than <a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a>.  If
it is false, long words will not be broken, and some lines may be longer
than <a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a>.  (Long words will be put on a line by themselves, in
order to minimize the amount by which <a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a> is exceeded.)</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.break_on_hyphens">
<span class="sig-name descname"><span class="pre">break_on_hyphens</span></span><a class="headerlink" href="#textwrap.TextWrapper.break_on_hyphens" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">True</span></code>) If true, wrapping will occur preferably on whitespaces
and right after hyphens in compound words, as it is customary in English.
If false, only whitespaces will be considered as potentially good places
for line breaks, but you need to set <a class="reference internal" href="#textwrap.TextWrapper.break_long_words" title="textwrap.TextWrapper.break_long_words"><code class="xref py py-attr docutils literal notranslate"><span class="pre">break_long_words</span></code></a> to false if
you want truly insecable words.  Default behaviour in previous versions
was to always allow breaking hyphenated words.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="textwrap.TextWrapper.max_lines">
<span class="sig-name descname"><span class="pre">max_lines</span></span><a class="headerlink" href="#textwrap.TextWrapper.max_lines" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">None</span></code>) If not <code class="docutils literal notranslate"><span class="pre">None</span></code>, then the output will contain at most
<em>max_lines</em> lines, with <em>placeholder</em> appearing at the end of the output.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<dl class="py attribute" id="index-0">
<dt class="sig sig-object py" id="textwrap.TextWrapper.placeholder">
<span class="sig-name descname"><span class="pre">placeholder</span></span><a class="headerlink" href="#textwrap.TextWrapper.placeholder" title="Link to this definition">¶</a></dt>
<dd><p>(default: <code class="docutils literal notranslate"><span class="pre">'</span> <span class="pre">[...]'</span></code>) String that will appear at the end of the output
text if it has been truncated.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
</dd></dl>

<p><a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> also provides some public methods, analogous to the
module-level convenience functions:</p>
<dl class="py method">
<dt class="sig sig-object py" id="textwrap.TextWrapper.wrap">
<span class="sig-name descname"><span class="pre">wrap</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.TextWrapper.wrap" title="Link to this definition">¶</a></dt>
<dd><p>Wraps the single paragraph in <em>text</em> (a string) so every line is at most
<a class="reference internal" href="#textwrap.TextWrapper.width" title="textwrap.TextWrapper.width"><code class="xref py py-attr docutils literal notranslate"><span class="pre">width</span></code></a> characters long.  All wrapping options are taken from
instance attributes of the <a class="reference internal" href="#textwrap.TextWrapper" title="textwrap.TextWrapper"><code class="xref py py-class docutils literal notranslate"><span class="pre">TextWrapper</span></code></a> instance.  Returns a list
of output lines, without final newlines.  If the wrapped output has no
content, the returned list is empty.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="textwrap.TextWrapper.fill">
<span class="sig-name descname"><span class="pre">fill</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">text</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#textwrap.TextWrapper.fill" title="Link to this definition">¶</a></dt>
<dd><p>Wraps the single paragraph in <em>text</em>, and returns a single string
containing the wrapped paragraph.</p>
</dd></dl>

</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="difflib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">difflib</span></code> — Helpers for computing deltas</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unicodedata.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">unicodedata</span></code> — Unicode Database</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/textwrap.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="unicodedata.html" title="unicodedata — Unicode Database"
             >next</a> |</li>
        <li class="right" >
          <a href="difflib.html" title="difflib — Helpers for computing deltas"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="text.html" >Text Processing Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">textwrap</span></code> — Text wrapping and filling</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>