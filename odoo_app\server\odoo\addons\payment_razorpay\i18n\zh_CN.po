# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_razorpay
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> CHEN <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-22 15:54+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        Enable recurring payments on Razorpay"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                        在 Razorpay 启用循环支付"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr "在处理您的付款过程中发生了一个错误。请再试一次。"

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__code
msgid "Code"
msgstr "代码"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "无法建立与API的连接。"

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Key Id"
msgstr "密钥 ID"

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Key Secret"
msgstr "密钥"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "没有发现与参考文献%s相匹配的交易。"

#. module: payment_razorpay
#: model:ir.model,name:payment_razorpay.model_payment_provider
msgid "Payment Provider"
msgstr "支付提供商"

#. module: payment_razorpay
#: model:ir.model,name:payment_razorpay.model_payment_transaction
msgid "Payment Transaction"
msgstr "支付交易"

#. module: payment_razorpay
#. odoo-javascript
#: code:addons/payment_razorpay/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "支付处理失败"

#. module: payment_razorpay
#: model:ir.model.fields.selection,name:payment_razorpay.selection__payment_provider__code__razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_key_id
msgid "Razorpay Key Id"
msgstr "Razorpay 密钥 ID"

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_key_secret
msgid "Razorpay Key Secret"
msgstr "Razorpay 密钥"

#. module: payment_razorpay
#: model:ir.model.fields,field_description:payment_razorpay.field_payment_provider__razorpay_webhook_secret
msgid "Razorpay Webhook Secret"
msgstr "Razorpay Webhook 密钥"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_provider.py:0
#, python-format
msgid "Razorpay gave us the following information: '%s'"
msgstr "Razorpay 给出了以下信息：'%s'"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid status: %s"
msgstr "接收到的数据状态无效：%s"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing entity id."
msgstr "收到的数据缺少实体 ID。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference."
msgstr "收到的数据缺少参考编号。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing status."
msgstr "收到的数据中缺少状态。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Received incomplete refund data."
msgstr "收到不完整的退款数据。"

#. module: payment_razorpay
#: model:ir.model.fields,help:payment_razorpay.field_payment_provider__razorpay_key_id
msgid "The key solely used to identify the account with Razorpay."
msgstr "用于识别 Razorpay 账户的唯一密钥。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The phone number is invalid."
msgstr "电话号码无效。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The phone number is missing."
msgstr "电话号码遗失。"

#. module: payment_razorpay
#: model:ir.model.fields,help:payment_razorpay.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "该支付提供商的技术代码。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "该交易没有与令牌挂钩。"

#. module: payment_razorpay
#. odoo-python
#: code:addons/payment_razorpay/models/payment_transaction.py:0
#, python-format
msgid "Transactions processed by Razorpay can't be manually voided from Odoo."
msgstr "由 Razorpay 处理的交易无法在 ERP 中手动取消."

#. module: payment_razorpay
#: model_terms:ir.ui.view,arch_db:payment_razorpay.payment_provider_form_razorpay
msgid "Webhook Secret"
msgstr "Webhook 密钥"
