# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_partner
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_partner
#: model:ir.model,name:website_partner.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO优化"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__seo_name
msgid "Seo name"
msgstr "Seo 名称"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_description
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_description
msgid "Website Partner Full Description"
msgstr "网站业务伙伴的详细说明"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_short_description
#: model:ir.model.fields,field_description:website_partner.field_res_users__website_short_description
msgid "Website Partner Short Description"
msgstr "网站业务伙伴简介"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_description
msgid "Website meta description"
msgstr "网站原说明"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_keywords
msgid "Website meta keywords"
msgstr "网站meta关键词"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_title
msgid "Website meta title"
msgstr "网站标题meta元素"

#. module: website_partner
#: model:ir.model.fields,field_description:website_partner.field_res_partner__website_meta_og_img
msgid "Website opengraph image"
msgstr "网站opengraph图像"
