<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="gettext — Multilingual internationalization services" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/gettext.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/gettext.py The gettext module provides internationalization (I18N) and localization (L10N) services for your Python modules and applications. It supports both the GNU gettext messa..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/gettext.py The gettext module provides internationalization (I18N) and localization (L10N) services for your Python modules and applications. It supports both the GNU gettext messa..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>gettext — Multilingual internationalization services &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="locale — Internationalization services" href="locale.html" />
    <link rel="prev" title="Internationalization" href="i18n.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/gettext.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a><ul>
<li><a class="reference internal" href="#gnu-gettext-api">GNU <strong class="program">gettext</strong> API</a></li>
<li><a class="reference internal" href="#class-based-api">Class-based API</a><ul>
<li><a class="reference internal" href="#the-nulltranslations-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code> class</a></li>
<li><a class="reference internal" href="#the-gnutranslations-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code> class</a></li>
<li><a class="reference internal" href="#solaris-message-catalog-support">Solaris message catalog support</a></li>
<li><a class="reference internal" href="#the-catalog-constructor">The Catalog constructor</a></li>
</ul>
</li>
<li><a class="reference internal" href="#internationalizing-your-programs-and-modules">Internationalizing your programs and modules</a><ul>
<li><a class="reference internal" href="#localizing-your-module">Localizing your module</a></li>
<li><a class="reference internal" href="#localizing-your-application">Localizing your application</a></li>
<li><a class="reference internal" href="#changing-languages-on-the-fly">Changing languages on the fly</a></li>
<li><a class="reference internal" href="#deferred-translations">Deferred translations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#acknowledgements">Acknowledgements</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="i18n.html"
                          title="previous chapter">Internationalization</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="locale.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/gettext.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="locale.html" title="locale — Internationalization services"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="i18n.html" title="Internationalization"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="i18n.html" accesskey="U">Internationalization</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-gettext">
<span id="gettext-multilingual-internationalization-services"></span><h1><a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> — Multilingual internationalization services<a class="headerlink" href="#module-gettext" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/gettext.py">Lib/gettext.py</a></p>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module provides internationalization (I18N) and localization
(L10N) services for your Python modules and applications. It supports both the
GNU <strong class="program">gettext</strong> message catalog API and a higher level, class-based API that may
be more appropriate for Python files.  The interface described below allows you
to write your module and application messages in one natural language, and
provide a catalog of translated messages for running under different natural
languages.</p>
<p>Some hints on localizing your Python modules and applications are also given.</p>
<section id="gnu-gettext-api">
<h2>GNU <strong class="program">gettext</strong> API<a class="headerlink" href="#gnu-gettext-api" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module defines the following API, which is very similar to
the GNU <strong class="program">gettext</strong> API.  If you use this API you will affect the
translation of your entire application globally.  Often this is what you want if
your application is monolingual, with the choice of language dependent on the
locale of your user.  If you are localizing a Python module, or if your
application needs to switch languages on the fly, you probably want to use the
class-based API instead.</p>
<dl class="py function">
<dt class="sig sig-object py" id="gettext.bindtextdomain">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">bindtextdomain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localedir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.bindtextdomain" title="Link to this definition">¶</a></dt>
<dd><p>Bind the <em>domain</em> to the locale directory <em>localedir</em>.  More concretely,
<a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> will look for binary <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> files for the given domain using
the path (on Unix): <code class="file docutils literal notranslate"><em><span class="pre">localedir</span></em><span class="pre">/</span><em><span class="pre">language</span></em><span class="pre">/LC_MESSAGES/</span><em><span class="pre">domain</span></em><span class="pre">.mo</span></code>, where
<em>language</em> is searched for in the environment variables <span class="target" id="index-0"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANGUAGE</span></code>,
<span class="target" id="index-1"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LC_ALL</span></code>, <span class="target" id="index-2"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LC_MESSAGES</span></code>, and <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANG</span></code> respectively.</p>
<p>If <em>localedir</em> is omitted or <code class="docutils literal notranslate"><span class="pre">None</span></code>, then the current binding for <em>domain</em> is
returned. <a class="footnote-reference brackets" href="#id3" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a></p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.textdomain">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">textdomain</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.textdomain" title="Link to this definition">¶</a></dt>
<dd><p>Change or query the current global domain.  If <em>domain</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, then the
current global domain is returned, otherwise the global domain is set to
<em>domain</em>, which is returned.</p>
</dd></dl>

<dl class="py function" id="index-4">
<dt class="sig sig-object py" id="gettext.gettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">gettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.gettext" title="Link to this definition">¶</a></dt>
<dd><p>Return the localized translation of <em>message</em>, based on the current global
domain, language, and locale directory.  This function is usually aliased as
<code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> in the local namespace (see examples below).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.dgettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">dgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.dgettext" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#gettext.gettext" title="gettext.gettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">gettext()</span></code></a>, but look the message up in the specified <em>domain</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.ngettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">ngettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.ngettext" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#gettext.gettext" title="gettext.gettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">gettext()</span></code></a>, but consider plural forms. If a translation is found,
apply the plural formula to <em>n</em>, and return the resulting message (some
languages have more than two plural forms). If no translation is found, return
<em>singular</em> if <em>n</em> is 1; return <em>plural</em> otherwise.</p>
<p>The Plural formula is taken from the catalog header. It is a C or Python
expression that has a free variable <em>n</em>; the expression evaluates to the index
of the plural in the catalog. See
<a class="reference external" href="https://www.gnu.org/software/gettext/manual/gettext.html">the GNU gettext documentation</a>
for the precise syntax to be used in <code class="file docutils literal notranslate"><span class="pre">.po</span></code> files and the
formulas for a variety of languages.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.dngettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">dngettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.dngettext" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#gettext.ngettext" title="gettext.ngettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">ngettext()</span></code></a>, but look the message up in the specified <em>domain</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.pgettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">pgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.pgettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.dpgettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">dpgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.dpgettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.npgettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">npgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.npgettext" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.dnpgettext">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">dnpgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.dnpgettext" title="Link to this definition">¶</a></dt>
<dd><p>Similar to the corresponding functions without the <code class="docutils literal notranslate"><span class="pre">p</span></code> in the prefix (that
is, <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-func docutils literal notranslate"><span class="pre">gettext()</span></code></a>, <a class="reference internal" href="#gettext.dgettext" title="gettext.dgettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">dgettext()</span></code></a>, <a class="reference internal" href="#gettext.ngettext" title="gettext.ngettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">ngettext()</span></code></a>, <a class="reference internal" href="#gettext.dngettext" title="gettext.dngettext"><code class="xref py py-func docutils literal notranslate"><span class="pre">dngettext()</span></code></a>),
but the translation is restricted to the given message <em>context</em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<p>Note that GNU <strong class="program">gettext</strong> also defines a <code class="xref py py-func docutils literal notranslate"><span class="pre">dcgettext()</span></code> method, but
this was deemed not useful and so it is currently unimplemented.</p>
<p>Here’s an example of typical usage for this API:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">gettext</span><span class="o">.</span><span class="n">bindtextdomain</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">,</span> <span class="s1">&#39;/path/to/my/language/directory&#39;</span><span class="p">)</span>
<span class="n">gettext</span><span class="o">.</span><span class="n">textdomain</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">)</span>
<span class="n">_</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">gettext</span>
<span class="c1"># ...</span>
<span class="nb">print</span><span class="p">(</span><span class="n">_</span><span class="p">(</span><span class="s1">&#39;This is a translatable string.&#39;</span><span class="p">))</span>
</pre></div>
</div>
</section>
<section id="class-based-api">
<h2>Class-based API<a class="headerlink" href="#class-based-api" title="Link to this heading">¶</a></h2>
<p>The class-based API of the <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module gives you more flexibility and
greater convenience than the GNU <strong class="program">gettext</strong> API.  It is the recommended
way of localizing your Python applications and modules.  <code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> defines
a <a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a> class which implements the parsing of GNU <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> format
files, and has methods for returning strings. Instances of this class can also
install themselves in the built-in namespace as the function <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="gettext.find">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">find</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localedir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">languages</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">all</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.find" title="Link to this definition">¶</a></dt>
<dd><p>This function implements the standard <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file search algorithm.  It
takes a <em>domain</em>, identical to what <a class="reference internal" href="#gettext.textdomain" title="gettext.textdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">textdomain()</span></code></a> takes.  Optional
<em>localedir</em> is as in <a class="reference internal" href="#gettext.bindtextdomain" title="gettext.bindtextdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">bindtextdomain()</span></code></a>. Optional <em>languages</em> is a list of
strings, where each string is a language code.</p>
<p>If <em>localedir</em> is not given, then the default system locale directory is used.
<a class="footnote-reference brackets" href="#id4" id="id2" role="doc-noteref"><span class="fn-bracket">[</span>2<span class="fn-bracket">]</span></a>  If <em>languages</em> is not given, then the following environment variables are
searched: <span class="target" id="index-5"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANGUAGE</span></code>, <span class="target" id="index-6"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LC_ALL</span></code>, <span class="target" id="index-7"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LC_MESSAGES</span></code>, and
<span class="target" id="index-8"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">LANG</span></code>.  The first one returning a non-empty value is used for the
<em>languages</em> variable. The environment variables should contain a colon separated
list of languages, which will be split on the colon to produce the expected list
of language code strings.</p>
<p><a class="reference internal" href="#gettext.find" title="gettext.find"><code class="xref py py-func docutils literal notranslate"><span class="pre">find()</span></code></a> then expands and normalizes the languages, and then iterates
through them, searching for an existing file built of these components:</p>
<p><code class="file docutils literal notranslate"><em><span class="pre">localedir</span></em><span class="pre">/</span><em><span class="pre">language</span></em><span class="pre">/LC_MESSAGES/</span><em><span class="pre">domain</span></em><span class="pre">.mo</span></code></p>
<p>The first such file name that exists is returned by <a class="reference internal" href="#gettext.find" title="gettext.find"><code class="xref py py-func docutils literal notranslate"><span class="pre">find()</span></code></a>. If no such
file is found, then <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned. If <em>all</em> is given, it returns a list
of all file names, in the order in which they appear in the languages list or
the environment variables.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.translation">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">translation</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localedir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">languages</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">class_</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fallback</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.translation" title="Link to this definition">¶</a></dt>
<dd><p>Return a <code class="docutils literal notranslate"><span class="pre">*Translations</span></code> instance based on the <em>domain</em>, <em>localedir</em>,
and <em>languages</em>, which are first passed to <a class="reference internal" href="#gettext.find" title="gettext.find"><code class="xref py py-func docutils literal notranslate"><span class="pre">find()</span></code></a> to get a list of the
associated <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file paths.  Instances with identical <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file
names are cached.  The actual class instantiated is <em>class_</em> if
provided, otherwise <a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a>.  The class’s constructor must
take a single <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> argument.</p>
<p>If multiple files are found, later files are used as fallbacks for earlier ones.
To allow setting the fallback, <a class="reference internal" href="copy.html#copy.copy" title="copy.copy"><code class="xref py py-func docutils literal notranslate"><span class="pre">copy.copy()</span></code></a> is used to clone each
translation object from the cache; the actual instance data is still shared with
the cache.</p>
<p>If no <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file is found, this function raises <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a> if
<em>fallback</em> is false (which is the default), and returns a
<a class="reference internal" href="#gettext.NullTranslations" title="gettext.NullTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code></a> instance if <em>fallback</em> is true.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span><a class="reference internal" href="exceptions.html#IOError" title="IOError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">IOError</span></code></a> used to be raised, it is now an alias of <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><em>codeset</em> parameter is removed.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="gettext.install">
<span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">install</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">localedir</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.install" title="Link to this definition">¶</a></dt>
<dd><p>This installs the function <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> in Python’s builtins namespace, based on
<em>domain</em> and <em>localedir</em> which are passed to the function <a class="reference internal" href="#gettext.translation" title="gettext.translation"><code class="xref py py-func docutils literal notranslate"><span class="pre">translation()</span></code></a>.</p>
<p>For the <em>names</em> parameter, please see the description of the translation
object’s <a class="reference internal" href="#gettext.NullTranslations.install" title="gettext.NullTranslations.install"><code class="xref py py-meth docutils literal notranslate"><span class="pre">install()</span></code></a> method.</p>
<p>As seen below, you usually mark the strings in your application that are
candidates for translation, by wrapping them in a call to the <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code>
function, like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nb">print</span><span class="p">(</span><span class="n">_</span><span class="p">(</span><span class="s1">&#39;This string will be translated.&#39;</span><span class="p">))</span>
</pre></div>
</div>
<p>For convenience, you want the <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> function to be installed in Python’s
builtins namespace, so it is easily accessible in all modules of your
application.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11: </span><em>names</em> is now a keyword-only parameter.</p>
</div>
</dd></dl>

<section id="the-nulltranslations-class">
<h3>The <a class="reference internal" href="#gettext.NullTranslations" title="gettext.NullTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code></a> class<a class="headerlink" href="#the-nulltranslations-class" title="Link to this heading">¶</a></h3>
<p>Translation classes are what actually implement the translation of original
source file message strings to translated message strings. The base class used
by all translation classes is <a class="reference internal" href="#gettext.NullTranslations" title="gettext.NullTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code></a>; this provides the basic
interface you can use to write your own specialized translation classes.  Here
are the methods of <code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code>:</p>
<dl class="py class">
<dt class="sig sig-object py" id="gettext.NullTranslations">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">NullTranslations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations" title="Link to this definition">¶</a></dt>
<dd><p>Takes an optional <a class="reference internal" href="../glossary.html#term-file-object"><span class="xref std std-term">file object</span></a> <em>fp</em>, which is ignored by the base class.
Initializes “protected” instance variables <em>_info</em> and <em>_charset</em> which are set
by derived classes, as well as <em>_fallback</em>, which is set through
<a class="reference internal" href="#gettext.NullTranslations.add_fallback" title="gettext.NullTranslations.add_fallback"><code class="xref py py-meth docutils literal notranslate"><span class="pre">add_fallback()</span></code></a>.  It then calls <code class="docutils literal notranslate"><span class="pre">self._parse(fp)</span></code> if <em>fp</em> is not
<code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations._parse">
<span class="sig-name descname"><span class="pre">_parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations._parse" title="Link to this definition">¶</a></dt>
<dd><p>No-op in the base class, this method takes file object <em>fp</em>, and reads
the data from the file, initializing its message catalog.  If you have an
unsupported message catalog file format, you should override this method
to parse your format.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.add_fallback">
<span class="sig-name descname"><span class="pre">add_fallback</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fallback</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.add_fallback" title="Link to this definition">¶</a></dt>
<dd><p>Add <em>fallback</em> as the fallback object for the current translation object.
A translation object should consult the fallback if it cannot provide a
translation for a given message.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.gettext">
<span class="sig-name descname"><span class="pre">gettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.gettext" title="Link to this definition">¶</a></dt>
<dd><p>If a fallback has been set, forward <code class="xref py py-meth docutils literal notranslate"><span class="pre">gettext()</span></code> to the fallback.
Otherwise, return <em>message</em>.  Overridden in derived classes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.ngettext">
<span class="sig-name descname"><span class="pre">ngettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.ngettext" title="Link to this definition">¶</a></dt>
<dd><p>If a fallback has been set, forward <code class="xref py py-meth docutils literal notranslate"><span class="pre">ngettext()</span></code> to the fallback.
Otherwise, return <em>singular</em> if <em>n</em> is 1; return <em>plural</em> otherwise.
Overridden in derived classes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.pgettext">
<span class="sig-name descname"><span class="pre">pgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.pgettext" title="Link to this definition">¶</a></dt>
<dd><p>If a fallback has been set, forward <a class="reference internal" href="#gettext.pgettext" title="gettext.pgettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pgettext()</span></code></a> to the fallback.
Otherwise, return the translated message.  Overridden in derived classes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.npgettext">
<span class="sig-name descname"><span class="pre">npgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.npgettext" title="Link to this definition">¶</a></dt>
<dd><p>If a fallback has been set, forward <a class="reference internal" href="#gettext.npgettext" title="gettext.npgettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">npgettext()</span></code></a> to the fallback.
Otherwise, return the translated message.  Overridden in derived classes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.info">
<span class="sig-name descname"><span class="pre">info</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.info" title="Link to this definition">¶</a></dt>
<dd><p>Return a dictionary containing
the metadata found in the message catalog file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.charset">
<span class="sig-name descname"><span class="pre">charset</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.charset" title="Link to this definition">¶</a></dt>
<dd><p>Return the encoding of the message catalog file.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.NullTranslations.install">
<span class="sig-name descname"><span class="pre">install</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">names</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.NullTranslations.install" title="Link to this definition">¶</a></dt>
<dd><p>This method installs <a class="reference internal" href="#gettext.NullTranslations.gettext" title="gettext.NullTranslations.gettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">gettext()</span></code></a> into the built-in namespace,
binding it to <code class="docutils literal notranslate"><span class="pre">_</span></code>.</p>
<p>If the <em>names</em> parameter is given, it must be a sequence containing the
names of functions you want to install in the builtins namespace in
addition to <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code>.  Supported names are <code class="docutils literal notranslate"><span class="pre">'gettext'</span></code>, <code class="docutils literal notranslate"><span class="pre">'ngettext'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'pgettext'</span></code>, and <code class="docutils literal notranslate"><span class="pre">'npgettext'</span></code>.</p>
<p>Note that this is only one way, albeit the most convenient way, to make
the <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> function available to your application.  Because it affects
the entire application globally, and specifically the built-in namespace,
localized modules should never install <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code>. Instead, they should use
this code to make <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> available to their module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">t</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">translation</span><span class="p">(</span><span class="s1">&#39;mymodule&#39;</span><span class="p">,</span> <span class="o">...</span><span class="p">)</span>
<span class="n">_</span> <span class="o">=</span> <span class="n">t</span><span class="o">.</span><span class="n">gettext</span>
</pre></div>
</div>
<p>This puts <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> only in the module’s global namespace and so only
affects calls within this module.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Added <code class="docutils literal notranslate"><span class="pre">'pgettext'</span></code> and <code class="docutils literal notranslate"><span class="pre">'npgettext'</span></code>.</p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="the-gnutranslations-class">
<h3>The <a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a> class<a class="headerlink" href="#the-gnutranslations-class" title="Link to this heading">¶</a></h3>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> module provides one additional class derived from
<a class="reference internal" href="#gettext.NullTranslations" title="gettext.NullTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code></a>: <a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a>.  This class overrides
<code class="xref py py-meth docutils literal notranslate"><span class="pre">_parse()</span></code> to enable reading GNU <strong class="program">gettext</strong> format <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> files
in both big-endian and little-endian format.</p>
<p><a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a> parses optional metadata out of the translation
catalog. It is convention with GNU <strong class="program">gettext</strong> to include metadata as
the translation for the empty string. This metadata is in <span class="target" id="index-9"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc822.html"><strong>RFC 822</strong></a>-style
<code class="docutils literal notranslate"><span class="pre">key:</span> <span class="pre">value</span></code> pairs, and should contain the <code class="docutils literal notranslate"><span class="pre">Project-Id-Version</span></code> key.  If the
key <code class="docutils literal notranslate"><span class="pre">Content-Type</span></code> is found, then the <code class="docutils literal notranslate"><span class="pre">charset</span></code> property is used to
initialize the “protected” <code class="xref py py-attr docutils literal notranslate"><span class="pre">_charset</span></code> instance variable, defaulting to
<code class="docutils literal notranslate"><span class="pre">None</span></code> if not found.  If the charset encoding is specified, then all message
ids and message strings read from the catalog are converted to Unicode using
this encoding, else ASCII is assumed.</p>
<p>Since message ids are read as Unicode strings too, all <code class="docutils literal notranslate"><span class="pre">*gettext()</span></code> methods
will assume message ids as Unicode strings, not byte strings.</p>
<p>The entire set of key/value pairs are placed into a dictionary and set as the
“protected” <code class="xref py py-attr docutils literal notranslate"><span class="pre">_info</span></code> instance variable.</p>
<p>If the <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file’s magic number is invalid, the major version number is
unexpected, or if other problems occur while reading the file, instantiating a
<a class="reference internal" href="#gettext.GNUTranslations" title="gettext.GNUTranslations"><code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code></a> class can raise <a class="reference internal" href="exceptions.html#OSError" title="OSError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">OSError</span></code></a>.</p>
<dl class="py class">
<dt class="sig sig-object py" id="gettext.GNUTranslations">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">gettext.</span></span><span class="sig-name descname"><span class="pre">GNUTranslations</span></span><a class="headerlink" href="#gettext.GNUTranslations" title="Link to this definition">¶</a></dt>
<dd><p>The following methods are overridden from the base class implementation:</p>
<dl class="py method">
<dt class="sig sig-object py" id="gettext.GNUTranslations.gettext">
<span class="sig-name descname"><span class="pre">gettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.GNUTranslations.gettext" title="Link to this definition">¶</a></dt>
<dd><p>Look up the <em>message</em> id in the catalog and return the corresponding message
string, as a Unicode string.  If there is no entry in the catalog for the
<em>message</em> id, and a fallback has been set, the look up is forwarded to the
fallback’s <a class="reference internal" href="#gettext.NullTranslations.gettext" title="gettext.NullTranslations.gettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">gettext()</span></code></a> method.  Otherwise, the
<em>message</em> id is returned.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.GNUTranslations.ngettext">
<span class="sig-name descname"><span class="pre">ngettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.GNUTranslations.ngettext" title="Link to this definition">¶</a></dt>
<dd><p>Do a plural-forms lookup of a message id.  <em>singular</em> is used as the message id
for purposes of lookup in the catalog, while <em>n</em> is used to determine which
plural form to use.  The returned message string is a Unicode string.</p>
<p>If the message id is not found in the catalog, and a fallback is specified,
the request is forwarded to the fallback’s <a class="reference internal" href="#gettext.NullTranslations.ngettext" title="gettext.NullTranslations.ngettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ngettext()</span></code></a>
method.  Otherwise, when <em>n</em> is 1 <em>singular</em> is returned, and <em>plural</em> is
returned in all other cases.</p>
<p>Here is an example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">n</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">listdir</span><span class="p">(</span><span class="s1">&#39;.&#39;</span><span class="p">))</span>
<span class="n">cat</span> <span class="o">=</span> <span class="n">GNUTranslations</span><span class="p">(</span><span class="n">somefile</span><span class="p">)</span>
<span class="n">message</span> <span class="o">=</span> <span class="n">cat</span><span class="o">.</span><span class="n">ngettext</span><span class="p">(</span>
    <span class="s1">&#39;There is </span><span class="si">%(num)d</span><span class="s1"> file in this directory&#39;</span><span class="p">,</span>
    <span class="s1">&#39;There are </span><span class="si">%(num)d</span><span class="s1"> files in this directory&#39;</span><span class="p">,</span>
    <span class="n">n</span><span class="p">)</span> <span class="o">%</span> <span class="p">{</span><span class="s1">&#39;num&#39;</span><span class="p">:</span> <span class="n">n</span><span class="p">}</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.GNUTranslations.pgettext">
<span class="sig-name descname"><span class="pre">pgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">message</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.GNUTranslations.pgettext" title="Link to this definition">¶</a></dt>
<dd><p>Look up the <em>context</em> and <em>message</em> id in the catalog and return the
corresponding message string, as a Unicode string.  If there is no
entry in the catalog for the <em>message</em> id and <em>context</em>, and a fallback
has been set, the look up is forwarded to the fallback’s
<a class="reference internal" href="#gettext.pgettext" title="gettext.pgettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">pgettext()</span></code></a> method.  Otherwise, the <em>message</em> id is returned.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="gettext.GNUTranslations.npgettext">
<span class="sig-name descname"><span class="pre">npgettext</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">singular</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">plural</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#gettext.GNUTranslations.npgettext" title="Link to this definition">¶</a></dt>
<dd><p>Do a plural-forms lookup of a message id.  <em>singular</em> is used as the
message id for purposes of lookup in the catalog, while <em>n</em> is used to
determine which plural form to use.</p>
<p>If the message id for <em>context</em> is not found in the catalog, and a
fallback is specified, the request is forwarded to the fallback’s
<a class="reference internal" href="#gettext.npgettext" title="gettext.npgettext"><code class="xref py py-meth docutils literal notranslate"><span class="pre">npgettext()</span></code></a> method.  Otherwise, when <em>n</em> is 1 <em>singular</em> is
returned, and <em>plural</em> is returned in all other cases.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
</dd></dl>

</dd></dl>

</section>
<section id="solaris-message-catalog-support">
<h3>Solaris message catalog support<a class="headerlink" href="#solaris-message-catalog-support" title="Link to this heading">¶</a></h3>
<p>The Solaris operating system defines its own binary <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> file format, but
since no documentation can be found on this format, it is not supported at this
time.</p>
</section>
<section id="the-catalog-constructor">
<h3>The Catalog constructor<a class="headerlink" href="#the-catalog-constructor" title="Link to this heading">¶</a></h3>
<p id="index-10">GNOME uses a version of the <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module by James Henstridge, but this
version has a slightly different API.  Its documented usage was:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">cat</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">Catalog</span><span class="p">(</span><span class="n">domain</span><span class="p">,</span> <span class="n">localedir</span><span class="p">)</span>
<span class="n">_</span> <span class="o">=</span> <span class="n">cat</span><span class="o">.</span><span class="n">gettext</span>
<span class="nb">print</span><span class="p">(</span><span class="n">_</span><span class="p">(</span><span class="s1">&#39;hello world&#39;</span><span class="p">))</span>
</pre></div>
</div>
<p>For compatibility with this older module, the function <code class="xref py py-func docutils literal notranslate"><span class="pre">Catalog()</span></code> is an
alias for the <a class="reference internal" href="#gettext.translation" title="gettext.translation"><code class="xref py py-func docutils literal notranslate"><span class="pre">translation()</span></code></a> function described above.</p>
<p>One difference between this module and Henstridge’s: his catalog objects
supported access through a mapping API, but this appears to be unused and so is
not currently supported.</p>
</section>
</section>
<section id="internationalizing-your-programs-and-modules">
<span id="i18n-howto"></span><h2>Internationalizing your programs and modules<a class="headerlink" href="#internationalizing-your-programs-and-modules" title="Link to this heading">¶</a></h2>
<p>Internationalization (I18N) refers to the operation by which a program is made
aware of multiple languages.  Localization (L10N) refers to the adaptation of
your program, once internationalized, to the local language and cultural habits.
In order to provide multilingual messages for your Python programs, you need to
take the following steps:</p>
<ol class="arabic simple">
<li><p>prepare your program or module by specially marking translatable strings</p></li>
<li><p>run a suite of tools over your marked files to generate raw messages catalogs</p></li>
<li><p>create language-specific translations of the message catalogs</p></li>
<li><p>use the <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module so that message strings are properly translated</p></li>
</ol>
<p>In order to prepare your code for I18N, you need to look at all the strings in
your files.  Any string that needs to be translated should be marked by wrapping
it in <code class="docutils literal notranslate"><span class="pre">_('...')</span></code> — that is, a call to the function <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-func docutils literal notranslate"><span class="pre">_</span></code></a>.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">filename</span> <span class="o">=</span> <span class="s1">&#39;mylog.txt&#39;</span>
<span class="n">message</span> <span class="o">=</span> <span class="n">_</span><span class="p">(</span><span class="s1">&#39;writing a log message&#39;</span><span class="p">)</span>
<span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="s1">&#39;w&#39;</span><span class="p">)</span> <span class="k">as</span> <span class="n">fp</span><span class="p">:</span>
    <span class="n">fp</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">message</span><span class="p">)</span>
</pre></div>
</div>
<p>In this example, the string <code class="docutils literal notranslate"><span class="pre">'writing</span> <span class="pre">a</span> <span class="pre">log</span> <span class="pre">message'</span></code> is marked as a candidate
for translation, while the strings <code class="docutils literal notranslate"><span class="pre">'mylog.txt'</span></code> and <code class="docutils literal notranslate"><span class="pre">'w'</span></code> are not.</p>
<p>There are a few tools to extract the strings meant for translation.
The original GNU <strong class="program">gettext</strong> only supported C or C++ source
code but its extended version <strong class="program">xgettext</strong> scans code written
in a number of languages, including Python, to find strings marked as
translatable.  <a class="reference external" href="https://babel.pocoo.org/">Babel</a> is a Python
internationalization library that includes a <code class="file docutils literal notranslate"><span class="pre">pybabel</span></code> script to
extract and compile message catalogs.  François Pinard’s program
called <strong class="program">xpot</strong> does a similar job and is available as part of
his <a class="reference external" href="https://github.com/pinard/po-utils">po-utils package</a>.</p>
<p>(Python also includes pure-Python versions of these programs, called
<strong class="program">pygettext.py</strong> and <strong class="program">msgfmt.py</strong>; some Python distributions
will install them for you.  <strong class="program">pygettext.py</strong> is similar to
<strong class="program">xgettext</strong>, but only understands Python source code and
cannot handle other programming languages such as C or C++.
<strong class="program">pygettext.py</strong> supports a command-line interface similar to
<strong class="program">xgettext</strong>; for details on its use, run <code class="docutils literal notranslate"><span class="pre">pygettext.py</span>
<span class="pre">--help</span></code>.  <strong class="program">msgfmt.py</strong> is binary compatible with GNU
<strong class="program">msgfmt</strong>.  With these two programs, you may not need the GNU
<strong class="program">gettext</strong> package to internationalize your Python
applications.)</p>
<p><strong class="program">xgettext</strong>, <strong class="program">pygettext</strong>, and similar tools generate
<code class="file docutils literal notranslate"><span class="pre">.po</span></code> files that are message catalogs.  They are structured
human-readable files that contain every marked string in the source
code, along with a placeholder for the translated versions of these
strings.</p>
<p>Copies of these <code class="file docutils literal notranslate"><span class="pre">.po</span></code> files are then handed over to the
individual human translators who write translations for every
supported natural language.  They send back the completed
language-specific versions as a <code class="file docutils literal notranslate"><span class="pre">&lt;language-name&gt;.po</span></code> file that’s
compiled into a machine-readable <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> binary catalog file using
the <strong class="program">msgfmt</strong> program.  The <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> files are used by the
<a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module for the actual translation processing at
run-time.</p>
<p>How you use the <a class="reference internal" href="#module-gettext" title="gettext: Multilingual internationalization services."><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code></a> module in your code depends on whether you are
internationalizing a single module or your entire application. The next two
sections will discuss each case.</p>
<section id="localizing-your-module">
<h3>Localizing your module<a class="headerlink" href="#localizing-your-module" title="Link to this heading">¶</a></h3>
<p>If you are localizing your module, you must take care not to make global
changes, e.g. to the built-in namespace. You should not use the GNU <strong class="program">gettext</strong>
API but instead the class-based API.</p>
<p>Let’s say your module is called “spam” and the module’s various natural language
translation <code class="file docutils literal notranslate"><span class="pre">.mo</span></code> files reside in <code class="file docutils literal notranslate"><span class="pre">/usr/share/locale</span></code> in GNU
<strong class="program">gettext</strong> format.  Here’s what you would put at the top of your
module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">t</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">translation</span><span class="p">(</span><span class="s1">&#39;spam&#39;</span><span class="p">,</span> <span class="s1">&#39;/usr/share/locale&#39;</span><span class="p">)</span>
<span class="n">_</span> <span class="o">=</span> <span class="n">t</span><span class="o">.</span><span class="n">gettext</span>
</pre></div>
</div>
</section>
<section id="localizing-your-application">
<h3>Localizing your application<a class="headerlink" href="#localizing-your-application" title="Link to this heading">¶</a></h3>
<p>If you are localizing your application, you can install the <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> function
globally into the built-in namespace, usually in the main driver file of your
application.  This will let all your application-specific files just use
<code class="docutils literal notranslate"><span class="pre">_('...')</span></code> without having to explicitly install it in each file.</p>
<p>In the simple case then, you need only add the following bit of code to the main
driver file of your application:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">gettext</span><span class="o">.</span><span class="n">install</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>If you need to set the locale directory, you can pass it into the
<a class="reference internal" href="#gettext.install" title="gettext.install"><code class="xref py py-func docutils literal notranslate"><span class="pre">install()</span></code></a> function:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>
<span class="n">gettext</span><span class="o">.</span><span class="n">install</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">,</span> <span class="s1">&#39;/usr/share/locale&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="changing-languages-on-the-fly">
<h3>Changing languages on the fly<a class="headerlink" href="#changing-languages-on-the-fly" title="Link to this heading">¶</a></h3>
<p>If your program needs to support many languages at the same time, you may want
to create multiple translation instances and then switch between them
explicitly, like so:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gettext</span>

<span class="n">lang1</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">translation</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">,</span> <span class="n">languages</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;en&#39;</span><span class="p">])</span>
<span class="n">lang2</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">translation</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">,</span> <span class="n">languages</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;fr&#39;</span><span class="p">])</span>
<span class="n">lang3</span> <span class="o">=</span> <span class="n">gettext</span><span class="o">.</span><span class="n">translation</span><span class="p">(</span><span class="s1">&#39;myapplication&#39;</span><span class="p">,</span> <span class="n">languages</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;de&#39;</span><span class="p">])</span>

<span class="c1"># start by using language1</span>
<span class="n">lang1</span><span class="o">.</span><span class="n">install</span><span class="p">()</span>

<span class="c1"># ... time goes by, user selects language 2</span>
<span class="n">lang2</span><span class="o">.</span><span class="n">install</span><span class="p">()</span>

<span class="c1"># ... more time goes by, user selects language 3</span>
<span class="n">lang3</span><span class="o">.</span><span class="n">install</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="deferred-translations">
<h3>Deferred translations<a class="headerlink" href="#deferred-translations" title="Link to this heading">¶</a></h3>
<p>In most coding situations, strings are translated where they are coded.
Occasionally however, you need to mark strings for translation, but defer actual
translation until later.  A classic example is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">animals</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;mollusk&#39;</span><span class="p">,</span>
           <span class="s1">&#39;albatross&#39;</span><span class="p">,</span>
           <span class="s1">&#39;rat&#39;</span><span class="p">,</span>
           <span class="s1">&#39;penguin&#39;</span><span class="p">,</span>
           <span class="s1">&#39;python&#39;</span><span class="p">,</span> <span class="p">]</span>
<span class="c1"># ...</span>
<span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">animals</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">a</span><span class="p">)</span>
</pre></div>
</div>
<p>Here, you want to mark the strings in the <code class="docutils literal notranslate"><span class="pre">animals</span></code> list as being
translatable, but you don’t actually want to translate them until they are
printed.</p>
<p>Here is one way you can handle this situation:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">_</span><span class="p">(</span><span class="n">message</span><span class="p">):</span> <span class="k">return</span> <span class="n">message</span>

<span class="n">animals</span> <span class="o">=</span> <span class="p">[</span><span class="n">_</span><span class="p">(</span><span class="s1">&#39;mollusk&#39;</span><span class="p">),</span>
           <span class="n">_</span><span class="p">(</span><span class="s1">&#39;albatross&#39;</span><span class="p">),</span>
           <span class="n">_</span><span class="p">(</span><span class="s1">&#39;rat&#39;</span><span class="p">),</span>
           <span class="n">_</span><span class="p">(</span><span class="s1">&#39;penguin&#39;</span><span class="p">),</span>
           <span class="n">_</span><span class="p">(</span><span class="s1">&#39;python&#39;</span><span class="p">),</span> <span class="p">]</span>

<span class="k">del</span> <span class="n">_</span>

<span class="c1"># ...</span>
<span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">animals</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">_</span><span class="p">(</span><span class="n">a</span><span class="p">))</span>
</pre></div>
</div>
<p>This works because the dummy definition of <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> simply returns the string
unchanged.  And this dummy definition will temporarily override any definition
of <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> in the built-in namespace (until the <a class="reference internal" href="../reference/simple_stmts.html#del"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">del</span></code></a> command). Take
care, though if you have a previous definition of <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> in the local
namespace.</p>
<p>Note that the second use of <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code> will not identify “a” as being
translatable to the <strong class="program">gettext</strong> program, because the parameter
is not a string literal.</p>
<p>Another way to handle this is with the following example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">N_</span><span class="p">(</span><span class="n">message</span><span class="p">):</span> <span class="k">return</span> <span class="n">message</span>

<span class="n">animals</span> <span class="o">=</span> <span class="p">[</span><span class="n">N_</span><span class="p">(</span><span class="s1">&#39;mollusk&#39;</span><span class="p">),</span>
           <span class="n">N_</span><span class="p">(</span><span class="s1">&#39;albatross&#39;</span><span class="p">),</span>
           <span class="n">N_</span><span class="p">(</span><span class="s1">&#39;rat&#39;</span><span class="p">),</span>
           <span class="n">N_</span><span class="p">(</span><span class="s1">&#39;penguin&#39;</span><span class="p">),</span>
           <span class="n">N_</span><span class="p">(</span><span class="s1">&#39;python&#39;</span><span class="p">),</span> <span class="p">]</span>

<span class="c1"># ...</span>
<span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">animals</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">_</span><span class="p">(</span><span class="n">a</span><span class="p">))</span>
</pre></div>
</div>
<p>In this case, you are marking translatable strings with the function
<code class="xref py py-func docutils literal notranslate"><span class="pre">N_()</span></code>, which won’t conflict with any definition of <code class="xref py py-func docutils literal notranslate"><span class="pre">_()</span></code>.
However, you will need to teach your message extraction program to
look for translatable strings marked with <code class="xref py py-func docutils literal notranslate"><span class="pre">N_()</span></code>. <strong class="program">xgettext</strong>,
<strong class="program">pygettext</strong>, <code class="docutils literal notranslate"><span class="pre">pybabel</span> <span class="pre">extract</span></code>, and <strong class="program">xpot</strong> all
support this through the use of the <code class="xref std std-option docutils literal notranslate"><span class="pre">-k</span></code> command-line switch.
The choice of <code class="xref py py-func docutils literal notranslate"><span class="pre">N_()</span></code> here is totally arbitrary; it could have just
as easily been <code class="xref py py-func docutils literal notranslate"><span class="pre">MarkThisStringForTranslation()</span></code>.</p>
</section>
</section>
<section id="acknowledgements">
<h2>Acknowledgements<a class="headerlink" href="#acknowledgements" title="Link to this heading">¶</a></h2>
<p>The following people contributed code, feedback, design suggestions, previous
implementations, and valuable experience to the creation of this module:</p>
<ul class="simple">
<li><p>Peter Funk</p></li>
<li><p>James Henstridge</p></li>
<li><p>Juan David Ibáñez Palomar</p></li>
<li><p>Marc-André Lemburg</p></li>
<li><p>Martin von Löwis</p></li>
<li><p>François Pinard</p></li>
<li><p>Barry Warsaw</p></li>
<li><p>Gustavo Niemeyer</p></li>
</ul>
<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id3" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>The default locale directory is system dependent; for example, on Red Hat Linux
it is <code class="file docutils literal notranslate"><span class="pre">/usr/share/locale</span></code>, but on Solaris it is <code class="file docutils literal notranslate"><span class="pre">/usr/lib/locale</span></code>.
The <code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> module does not try to support these system dependent
defaults; instead its default is <code class="file docutils literal notranslate"><em><span class="pre">sys.base_prefix</span></em><span class="pre">/share/locale</span></code> (see
<a class="reference internal" href="sys.html#sys.base_prefix" title="sys.base_prefix"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.base_prefix</span></code></a>). For this reason, it is always best to call
<a class="reference internal" href="#gettext.bindtextdomain" title="gettext.bindtextdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">bindtextdomain()</span></code></a> with an explicit absolute path at the start of your
application.</p>
</aside>
<aside class="footnote brackets" id="id4" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id2">2</a><span class="fn-bracket">]</span></span>
<p>See the footnote for <a class="reference internal" href="#gettext.bindtextdomain" title="gettext.bindtextdomain"><code class="xref py py-func docutils literal notranslate"><span class="pre">bindtextdomain()</span></code></a> above.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a><ul>
<li><a class="reference internal" href="#gnu-gettext-api">GNU <strong class="program">gettext</strong> API</a></li>
<li><a class="reference internal" href="#class-based-api">Class-based API</a><ul>
<li><a class="reference internal" href="#the-nulltranslations-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">NullTranslations</span></code> class</a></li>
<li><a class="reference internal" href="#the-gnutranslations-class">The <code class="xref py py-class docutils literal notranslate"><span class="pre">GNUTranslations</span></code> class</a></li>
<li><a class="reference internal" href="#solaris-message-catalog-support">Solaris message catalog support</a></li>
<li><a class="reference internal" href="#the-catalog-constructor">The Catalog constructor</a></li>
</ul>
</li>
<li><a class="reference internal" href="#internationalizing-your-programs-and-modules">Internationalizing your programs and modules</a><ul>
<li><a class="reference internal" href="#localizing-your-module">Localizing your module</a></li>
<li><a class="reference internal" href="#localizing-your-application">Localizing your application</a></li>
<li><a class="reference internal" href="#changing-languages-on-the-fly">Changing languages on the fly</a></li>
<li><a class="reference internal" href="#deferred-translations">Deferred translations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#acknowledgements">Acknowledgements</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="i18n.html"
                          title="previous chapter">Internationalization</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="locale.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">locale</span></code> — Internationalization services</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/gettext.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="locale.html" title="locale — Internationalization services"
             >next</a> |</li>
        <li class="right" >
          <a href="i18n.html" title="Internationalization"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="i18n.html" >Internationalization</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">gettext</span></code> — Multilingual internationalization services</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>