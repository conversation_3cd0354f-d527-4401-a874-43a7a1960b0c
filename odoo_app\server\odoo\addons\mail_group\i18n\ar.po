# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_group
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid ") <span invisible=\"not send_email\">and send them an email</span>."
msgstr ""
") <span invisible=\"not send_email\">ثم قم بإرسال بريد إلكتروني "
"إليهم</span>. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "<br/> You'll be notified as soon as some new content is posted."
msgstr "<br/> سوف يتم إشعارك بمجرد أن يتم نشر محتوى جديد. "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_subscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be subscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                مرحباً،<br><br>\n"
"                لقد طلبت إلغاء الاشتراك في القائمة البريدية <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                للتأكيد، يرجى زيارة الرابط التالي: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>\n"
"                <br><br>\n"
"                إذا كان ذلك عن طريق الخطأ أو إذا لم تطلب تنفيذ هذا الإجراء، يرجى تجاهل هذه الرسالة.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_list_unsubscribe
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                Hello,<br><br>\n"
"                You have requested to be unsubscribed to the mailing list <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                To confirm, please visit the following link: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>.\n"
"                <br><br>\n"
"                If this was a mistake or you did not requested this action, please ignore this message.\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                مرحباً،<br><br>\n"
"                لقد طلبت إلغاء الاشتراك في القائمة البريدية <strong t-out=\"object.name or ''\"></strong>.\n"
"                <br><br>\n"
"                للتأكيد، يرجى زيارة الرابط التالي: <strong t-if=\"ctx.get('token_url')\"><a t-att-href=\"ctx['token_url']\"><t t-out=\"ctx['token_url'] or ''\"></t></a></strong>.\n"
"                <br><br>\n"
"                إذا كان ذلك عن طريق الخطأ أو إذا لم تطلب تنفيذ هذا الإجراء، يرجى تجاهل هذه الرسالة.\n"
"            </div>\n"
"        "

#. module: mail_group
#: model:mail.template,body_html:mail_group.mail_template_guidelines
msgid ""
"<div>\n"
"                <p>Hello <t t-out=\"object.partner_id.name or ''\"></t>,</p>\n"
"                <p>Please find below the guidelines of the <t t-out=\"object.mail_group_id.name\"></t> mailing list.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"></t></p>\n"
"            </div>\n"
"        "
msgstr ""
"<div>\n"
"                <p>مرحباً <t t-out=\"object.partner_id.name or ''\"></t>،</p>\n"
"                <p>ستجد أدناه لوائح القائمة البريدية <t t-out=\"object.mail_group_id.name\"></t>.</p>\n"
"                <p><t t-out=\"object.mail_group_id.moderation_guidelines_msg or ''\"></t></p>\n"
"            </div>\n"
"        "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"التاريخ \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o me-1\" role=\"img\" aria-label=\"Alias\" "
"title=\"اللقب \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_name
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"اللقب \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"Recipients\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-user\" role=\"img\" aria-label=\"Recipients\" "
"title=\"المستلمين \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"Previous message\"/>"
msgstr ""
"<i class=\"oi oi-arrow-left\" role=\"img\" aria-label=\"Previous message\" "
"title=\"الرسالة السابقة \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"Next message\"/>"
msgstr ""
"<i class=\"oi oi-arrow-right\" role=\"img\" aria-label=\"Next message\" "
"title=\"الرسالة التالية \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"Show attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show attachments\""
" title=\"إظهار المرفقات \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"Show replies\"/>"
msgstr ""
"<i class=\"oi oi-chevron-down\" role=\"img\" aria-label=\"Show replies\" "
"title=\"إظهار الردود \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"Hide attachments\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide "
"attachments\" title=\"إخفاء المرفقات \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"Hide replies\"/>"
msgstr ""
"<i class=\"oi oi-chevron-right\" role=\"img\" aria-label=\"Hide replies\" "
"title=\"إخفاء الردود \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "<span class=\"bg-warning\">Pending</span>"
msgstr "<span class=\"bg-warning\">معلق</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">Whitelisted</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">Banned</span>"
msgstr ""
"<span class=\"ms-2 badge text-bg-success\" invisible=\"author_moderation != 'allow'\">تم رفع الحظر</span>\n"
"                            <span class=\"ms-2 badge text-bg-danger\" invisible=\"author_moderation != 'ban'\">محظور</span> "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "<span class=\"mx-2\">-</span>"
msgstr "<span class=\"mx-2\">-</span>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "<span>By thread</span>"
msgstr "<span>حسب المحادثة</span> "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"<span>No Mail Group yet.</span>\n"
"                    <br/>"
msgstr ""
"<span>لا توجد مجموعة بريد إلكتروني بعد.</span>\n"
"                    <br/>"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"قاموس بايثون الذي سيتم تقييمه لتوفير قيم افتراضية عند إنشاء سجلات جديدة لهذا"
" اللقب. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Accept"
msgstr "قبول"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__accepted
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Accepted"
msgstr "تم القبول"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__action
msgid "Action"
msgstr "إجراء"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__active
msgid "Active"
msgstr "نشط"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Add this email address to white list of people and accept all pending "
"messages from the same author."
msgstr ""
"قم بإضافة عنوان البريد الإلكتروني هذا لرفع الحظر عن الأشخاص وقبول كافة "
"الرسائل المعلقة من نفس الكاتب. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Alias"
msgstr "لقب"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_contact
msgid "Alias Contact Security"
msgstr "أمان ألقاب جهات الاتصال "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain_id
msgid "Alias Domain"
msgstr "نطاق اللقب "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_domain
msgid "Alias Domain Name"
msgstr "اسم نطاق اللقب "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_full_name
msgid "Alias Email"
msgstr "لقب البريد الإلكتروني "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_status
msgid "Alias Status"
msgstr "حالة اللقب "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_status
msgid "Alias status assessed on the last message received."
msgstr "حالة اللقب مقيّمة في آخر رسالة مستلمة. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_model_id
msgid "Aliased Model"
msgstr "النموذج الملقب "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "All messages of this group"
msgstr "كافة رسائل هذه المجموعة "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Allowed Emails"
msgstr "رسائل البريد الإلكتروني المسموح بها "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Alone we can do so little, together we can do so much"
msgstr "وحدنا يمكننا أن نحقق القليل. أما معاً، فسنبلغ النجوم"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__allow
msgid "Always Allow"
msgstr "السماح دائمًا"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "An email with instructions has been sent."
msgstr "تم إرسال بريد إلكتروني يحتوي على الإرشادات. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Archived"
msgstr "مؤرشف"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "Archives"
msgstr "الأرشيف"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__attachment_ids
msgid "Attachments"
msgstr "المرفقات "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_id
msgid "Author"
msgstr "الكاتب "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__author_moderation
msgid "Author Moderation Status"
msgstr "حالة إشراف الكاتب "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"كاتب الرسالة. إذا لم يتم تعيينه، قد يحتوي الحقل email_from على عنوان بريد "
"إلكتروني لا ينتمي لأي شريك في النظام. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_group_id
msgid "Authorized Group"
msgstr "المجموعة المصرح لها "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify
msgid "Automatic notification"
msgstr "إشعار تلقائي"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__ban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Ban"
msgstr "حظر"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Ban the author of the message ("
msgstr "حظر كاتب الرسالة ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid ""
"Ban this email address and reject all pending messages from the same author "
"and send an email to the author"
msgstr ""
"حظر عنوان البريد الإلكتروني هذا ورفض كافة الرسائل المعلقة من نفس الكاتب "
"وإرسال بريداً إلكترونياً إلى الكاتب "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__ban
msgid "Banned"
msgstr "محظور "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Banned Emails"
msgstr "عناوين البريد الإلكتروني المحظورة"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_archive_menu
msgid "By date"
msgstr "حسب التاريخ"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__can_manage_group
msgid "Can Manage"
msgstr "يمكن إدارة "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__can_manage_group
msgid "Can manage the members"
msgstr "يمكن إدارة الأعضاء "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_child_ids
msgid "Children"
msgstr "الفروع"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Choose or configure a custom domain"
msgstr "قم بتحديد أو تهيئة نطاق مخصص "

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_subscribe
msgid "Confirm subscription to {{ object.name }}"
msgstr "تأكيد الاشتراك في {{ object.name }} "

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_list_unsubscribe
msgid "Confirm unsubscription to {{ object.name }}"
msgstr "تأكيد الاشتراك في  {{ object.name }} "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__body
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__body
msgid "Contents"
msgstr "المحتويات"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid "Create a Mail Group"
msgstr "إنشاء مجموعة بريدية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Create a new group"
msgstr "إنشاء مجموعة جديدة "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__create_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__is_moderator
msgid "Current user is a moderator of the group"
msgstr "المستخدم الحالي مشرف لهذه المجموعة "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "الرسالة المرتدة المخصصة"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_defaults
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__description
msgid "Description"
msgstr "الوصف"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Discard"
msgstr "إهمال "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__display_name
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Email %s is invalid"
msgstr "البريد الإلكتروني %s غير صالح "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_email
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Email Alias"
msgstr "لقب البريد الإلكتروني"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__email_from_normalized
msgid "Email From"
msgstr "بريد إلكتروني من "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"عنوان البريد الإلكتروني للمرسل. يتم استخدام هذا الحقل عندما يتعذر وجود شريك "
"مطابق ويستبدل حقل author_id في المحادثات."

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "نطاق البريد الإلكتروني مثال: 'example.com' في '<EMAIL>' "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails"
msgstr "رسائل البريد الإلكتروني"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Emails waiting an action for this group"
msgstr "رسائل البريد الإلكتروني بانتظار إجراء في هذه المجموعة "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__public
msgid "Everyone"
msgstr "الجميع"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Follow-Ups"
msgstr "المتابعات"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "From"
msgstr "من"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_group_id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__mail_group_id
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Group"
msgstr "المجموعة"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Group Message"
msgstr "رسالة جماعية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Group Name"
msgstr "اسم المجموعة"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Group message can only be linked to mail group. Current model is %s."
msgstr ""
"يمكن ربط الرسائل الجماعية فقط إلى مجموعة البريد. النموذج الحالي هو %s. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines_msg
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Guidelines"
msgstr "الإرشادات "

#. module: mail_group
#: model:mail.template,subject:mail_group.mail_template_guidelines
msgid "Guidelines of group {{ object.mail_group_id.name }}"
msgstr "إرشادات المجموعة {{ object.mail_group_id.name }} "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Hello"
msgstr "مرحباً "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__id
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__id
msgid "ID"
msgstr "المُعرف"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"معرف السجل الأصل الذي يحتوي على اللقب (مثال: المشروع الذي يحتوي على اللقب "
"لإنشاء المهمة) "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"إذا كان محدداً، سوف يتم إرسال هذا المحتوى تلقائياً إلى المستخدمين غير المصرح"
" لهم عوضاً عن الرسالة الافتراضية. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__image_128
msgid "Image"
msgstr "صورة"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Invalid action for URL generation (%s)"
msgstr "إجراء غير صالح لإنشاء رابط URL (%s) "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_moderation.py:0
#: code:addons/mail_group/models/mail_group_moderation.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "عنوان البريد الإلكتروني غير صالح %r"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.invalid_token_subscription
msgid "Invalid or expired confirmation link."
msgstr "رابط التأكيد غير صالح أو انتهت صلاحيته. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Allowed"
msgstr "هو مسموح به"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Is Banned"
msgstr "هو محظور"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__is_group_moderated
msgid "Is Group Moderated"
msgstr "المجموعة مُشرَف عليها "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_member
msgid "Is Member"
msgstr "عضو "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Join"
msgstr "انضمام"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_uid
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__write_date
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Leave"
msgstr "مغادرة"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "Let people subscribe to your list online or manually add them here."
msgstr ""
"أتح للأفراد الاشتراك في قائمتك عبر الإنترنت أو قم بإضافتهم هنا يدوياً "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "الكشف عن الوارد على أساس الجزء المحلي"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Mail Group"
msgstr "مجموعة البريد الإلكتروني "

#. module: mail_group
#: model:res.groups,name:mail_group.group_mail_group_manager
msgid "Mail Group Administrator"
msgstr "مدير مجموعة البريد الإلكتروني "

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_subscribe
msgid "Mail Group: Mailing List Subscription"
msgstr "المجموعة البريدية: اشتراك القائمة البريدية "

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_list_unsubscribe
msgid "Mail Group: Mailing List Unsubscription"
msgstr "المجموعة البريدية: إلغاء الاشتراك في القائمة البريدية "

#. module: mail_group
#: model:mail.template,name:mail_group.mail_template_guidelines
msgid "Mail Group: Send Guidelines"
msgstr "المجموعة البريدية: إرسال الإرشادات "

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_action
#: model:ir.ui.menu,name:mail_group.mail_group_menu
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Mail Groups"
msgstr "المجموعات البريدية "

#. module: mail_group
#: model:ir.actions.server,name:mail_group.ir_cron_mail_notify_group_moderators_ir_actions_server
msgid "Mail List: Notify group moderators"
msgstr "القائمة البريدية: إخطار مشرفي المجموعات "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__mail_message_id
msgid "Mail Message"
msgstr "رسالة البريد الإلكتروني "

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_member
msgid "Mailing List Member"
msgstr "عضو القائمة البريدية "

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message
msgid "Mailing List Message"
msgstr "رسالة القائمة البريدية "

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_moderation
msgid "Mailing List black/white list"
msgstr "الحظر/إلغاء الحظر في القائمة البريدية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.portal_breadcrumbs_group
msgid "Mailing Lists"
msgstr "القوائم البريدية"

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_action
msgid ""
"Mailing groups are communities that like to discuss a specific topic "
"together."
msgstr "المجموعات البريدية هي مجتمعات تحبذ مناقشة موضوع محدد معاً. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Mailing-List:"
msgstr "قائمة-بريدية: "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
msgid "Member"
msgstr "العضو"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_member_action
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_kanban
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
msgid "Members"
msgstr "الأعضاء"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_count
msgid "Members Count"
msgstr "عدد الأعضاء "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Members of this group"
msgstr "أعضاء هذه المجموعة "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__members
msgid "Members only"
msgstr "الأعضاء فقط "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__mail_group_message_id
msgid "Message"
msgstr "الرسالة"

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_reject_action
msgid "Message Rejection Explanation"
msgstr "تبرير رفض الرسالة "

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_message_action
msgid "Messages"
msgstr "الرسائل"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_count
msgid "Messages Count"
msgstr "عدد الرسائل "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_last_month_count
msgid "Messages Per Month"
msgstr "الرسائل لكل شهر "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Messages are pending moderation"
msgstr "الرسائل بانتظار الإشراف "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Messages that need an action"
msgstr "الرسائل التي تتطلب اتخاذ إجراء "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Moderate Messages"
msgstr "الإشراف على الرسائل "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation
msgid "Moderate this group"
msgstr "الإشراف على هذه المجموعة "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_list
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderated"
msgstr "تم الإشراف "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderator_id
msgid "Moderated By"
msgstr "تم الإشراف بواسطة "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_ids
msgid "Moderated Emails"
msgstr "رسائل البريد الإلكتروني التي يتم الإشراف عليها "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_rule_count
msgid "Moderated emails count"
msgstr "عدد رسائل البريد الإلكتروني التي يتم الإشراف عليها "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderated emails in this group"
msgstr "الإشراف على رسائل البريد الإلكتروني في هذه المجموعة "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderated group must have moderators."
msgstr "يجب أن تحتوي المجموعات المُشرَف عليها على مشرفين. "

#. module: mail_group
#: model:ir.actions.act_window,name:mail_group.mail_group_moderation_action
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Moderation"
msgstr "الإشراف "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_tree
msgid "Moderation Lists"
msgstr "قوائم الإشراف "

#. module: mail_group
#: model:ir.ui.menu,name:mail_group.mail_group_moderation_menu
msgid "Moderation Rules"
msgstr "قواعد الإشراف "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderations"
msgstr "عمليات الإشراف "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__is_moderator
msgid "Moderator"
msgstr "المشرف"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderator_ids
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Moderators"
msgstr "المشرفون"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Moderators must have an email address."
msgstr "يجب أن يكون لدى المشرفين عنوان بريد إلكتروني."

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_1
msgid "My Company News"
msgstr "أخبار شركتي "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__name
msgid "Name"
msgstr "الاسم"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"Need to unsubscribe? <br/>It's right here! <span class=\"oi fa-2x oi-arrow-"
"down float-end\" role=\"img\" aria-label=\"\" title=\"Read this !\"/>"
msgstr ""
"بحاجة إلى إلغاء اشتراكك؟ <br/>إنه هنا! <span class=\"oi fa-2x oi-arrow-down "
"float-end\" role=\"img\" aria-label=\"\" title=\"اقرأ ذلك! \"/> "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_guidelines
msgid ""
"Newcomers on this moderated group will automatically receive the guidelines."
msgstr "سوف يستلم المُنضَمون الجدد الإرشادات في هذه المجموعة المُشرَف عليها "

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_member_action
msgid "No Members in this list yet!"
msgstr "لا يوجد أعضاء في هذه القائمة بعد! "

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid "No Messages in this list yet!"
msgstr "لا توجد رسائل في هذه المجموعة بعد! "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__email_normalized
msgid "Normalized Email"
msgstr "البريد الإلكتروني الطبيعي"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__email_from_normalized
msgid "Normalized From"
msgstr "الاستمارة العادية "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_notify_msg
msgid "Notification message"
msgstr "رسالة الإشعار"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Notify Members"
msgstr "إشعار الأعضاء "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__mail_group_message_count
msgid "Number of message in this group"
msgstr "عدد الرسائل في هذه المجموعة "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Only an administrator or a moderator can send guidelines to group members."
msgstr "وحده المدير أو المشرف بإمكانه إرسال الإرشادات إلى أعضاء المجموعة. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only members can send email to the mailing list."
msgstr "وحدهم الأعضاء بوسعهم إرسال رسائل بريد إلكتروني إلى القائمة البريدية. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "Only selected groups of users can send email to the mailing list."
msgstr ""
"يمكن فقط لأعضاء المجموعة المحددين إرسال رسائل بريد إلكتروني إلى القائمة "
"البريدية. "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"معرف اختياري لمناقشة (سجل) سيتم إرفاق كافة رسائل البريد الإلكتروني الواردة "
"فيه، حتى لو لم يتم الرد عليها. إذا تم تعيين قيمة له، سيعطل هذا إنشاء السجلات"
" الجديدة بالكامل. "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__group_message_parent_id
msgid "Parent"
msgstr "الأصل"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_model_id
msgid "Parent Model"
msgstr "النموذج الأصلي "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "معرف مناقشة السجل الرئيسي "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"النموذج الرئيسي الذي يحتفظ بلقب البريد الإلكتروني. ليس بالضرورة أن يكون "
"النموذج الذي يحتفظ بمرجع لقب البريد الإلكتروني هو النموذج المحدد في الحقل "
"alias_model_id (مثال: المشروع (parent_model) والمهمة (model))"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_member__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__member_partner_ids
msgid "Partners Member"
msgstr "الأعضاء الشركاء "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_ids
msgid "Pending Messages"
msgstr "الرسائل المعلقة "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__mail_group_message_moderation_count
msgid "Pending Messages Count"
msgstr "عدد الرسائل المعلقة "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__pending_moderation
msgid "Pending Moderation"
msgstr "بانتظار الإشراف "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__moderation_notify
msgid ""
"People receive an automatic notification about their message being waiting "
"for moderation."
msgstr "يتلقى الأشخاص إشعاراً تلقائياً لإبلاغهم أن رسالتهم بانتظار الإشراف. "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_moderation__status__ban
msgid "Permanent Ban"
msgstr "حظر دائم"

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"سياسة لنشر رسالة في المستند باستخدام بوابة البريد الإلكتروني.\n"
"- الجميع: يمكن للجميع النشر\n"
"- الشركاء: الشركاء المعتمدون فقط\n"
"- المتابعون: فقط متابعو المستند ذي الصلة أو أعضاء القنوات التالية.\n"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Post to:"
msgstr "النشر في: "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__create_date
msgid "Posted"
msgstr "مُرحّل "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__access_mode
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Privacy"
msgstr "الخصوصية"

#. module: mail_group
#: model:mail.group,name:mail_group.mail_group_2
msgid "Public Mailing List"
msgstr "القائمة البريدية العامة "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/wizard/mail_group_message_reject.py:0
#, python-format
msgid "Re: %s"
msgstr "رد: %s"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__alias_force_thread_id
msgid "Record Thread ID"
msgstr "معرف مناقشة السجل"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.group_message
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message_reject__action__reject
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Reject"
msgstr "رفض"

#. module: mail_group
#: model:ir.model,name:mail_group.model_mail_group_message_reject
msgid "Reject Group Message"
msgstr "رفض الرسالة الجماعية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject Silently"
msgstr "ارفض بصمت"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Reject the message"
msgstr "رفض الرسالة "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__moderation_status__rejected
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
msgid "Rejected"
msgstr "تم الرفض "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Remove message with explanation"
msgstr "إزالة الرسالة وتفسير السبب"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Responsible Users"
msgstr "المستخدمين المسؤولين "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "Search Group Message"
msgstr "البحث في الرسائل الجماعية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_member_view_search
msgid "Search Mail Group Member"
msgstr "البحث عن عضو في المجموعة البريدية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_search
msgid "Search Mail group"
msgstr "البحث في المجموعة البريدية "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Search Moderation List"
msgstr "البحث في قائمة الإشراف"

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group__access_mode__groups
msgid "Selected group of users"
msgstr "مجموعة المستخدمين المحددة"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Send"
msgstr "إرسال"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Ban"
msgstr "إرسال وحظر  "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "Send & Reject"
msgstr "إرسال ورفض "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__send_email
msgid "Send Email"
msgstr "إرسال بريد إلكتروني"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "Send Guidelines"
msgstr "إرسال الإرشادات "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group_message_reject__send_email
msgid "Send an email to the author of the message"
msgstr "إرسال بريد إلكتروني إلى كاتب الرسالة "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group__moderation_guidelines
msgid "Send guidelines to new subscribers"
msgstr "إرسال إرشادات للمشتركين الجدد"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_guidelines
msgid "Sent to people who subscribed to a mailing group with group guidelines"
msgstr ""
"يتم إرسالها إلى الأشخاص المشتركين في قائمة بريدية وتحوي على توجيهات المجموعة"
" "

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_unsubscribe
msgid "Sent to people who unsubscribed from a mailing group"
msgstr "يتم إرسالها إلى الأشخاص الذين ألغوا اشتراكهم من مجموعة بريدية "

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__moderation_status
#: model:ir.model.fields,field_description:mail_group.field_mail_group_moderation__status
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_moderation_view_search
msgid "Status"
msgstr "الحالة"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "Stay in touch with our Community"
msgstr "ابق على تواصل مع مجتمعنا"

#. module: mail_group
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message__subject
#: model:ir.model.fields,field_description:mail_group.field_mail_group_message_reject__subject
msgid "Subject"
msgstr "الموضوع "

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Subscribe"
msgstr "اشتراك"

#. module: mail_group
#: model:mail.template,description:mail_group.mail_template_list_subscribe
msgid "Subscription confirmation to a mailing group"
msgstr "رسالة تأكيد الاشتراك إلى مجموعة بريدية "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid ""
"Template \"mail_group.mail_template_guidelines\" was not found. No email has"
" been sent. Please contact an administrator to fix this issue."
msgstr ""
"لم يتم العثور على القالب \"mail.mail_channel_send_guidelines\". لم يتم إرسال"
" أي بريد إلكتروني. يرجى التواصل مع المسؤول لحل هذه المشكلة. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "Thank you!"
msgstr "شكرًا لك!"

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The \"Authorized Group\" is missing."
msgstr "هناك \"مجموعة مصرح لها\" مفقودة "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "The email"
msgstr "البريد الإلكتروني "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The email \"%s\" is not valid."
msgstr "البريد الإلكتروني \"%s\" غير صالح. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The group of the message do not match."
msgstr "مجموعة الرسالة غير مطابقة "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is empty."
msgstr "وصف الإرشادات فارغ. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The guidelines description is missing."
msgstr "وصف الإرشادات مفقود. "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"النموذج (مستندات أودو) الذي يقترن به هذا اللقب. أي رسالة واردة لا ترد على "
"سجل موجود ستقوم بإنشاء سجل جديد من نفس نوع هذا النموذج (مثلًا: مهمة مشروع) "

#. module: mail_group
#: model:ir.model.fields,help:mail_group.field_mail_group__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"اسم لقب البريد الإلكتروني، مثلًا: 'وظائف' إذا كنت ترغب في جمع الرسائل "
"المرسلة لـ<<EMAIL>> "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The notification message is missing."
msgstr "رسالة الإشعار مفقودة. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group.py:0
#, python-format
msgid "The partner can not be found."
msgstr "تعذر العثور على الشريك. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "The record of the message should be the group."
msgstr "يجب أن يكون سجل الرسالة هو المجموعة. "

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is already subscribed."
msgstr "هذا البريد الإلكتروني مشترك بالفعل. "

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#, python-format
msgid "This email is not subscribed."
msgstr "هذا البريد الإلكتروني غير مشترك. "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "This message can not be moderated"
msgstr "لا يمكن الإشراف على هذه الرسالة "

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_member_unique_partner
msgid "This partner is already subscribed to the group"
msgstr "هذا الشريك مشترك في المجموعة بالفعل "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Those messages can not be moderated: %s."
msgstr "لا يمكن الإشراف على هذه الرسائل: %s. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "To Review"
msgstr "للمراجعة"

#. module: mail_group
#. odoo-javascript
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: code:addons/mail_group/static/src/js/mail_group.js:0
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
#, python-format
msgid "Unsubscribe"
msgstr "إلغاء الاشتراك"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "Unsubscribe:"
msgstr "إلغاء الاشتراك: "

#. module: mail_group
#: model_terms:ir.actions.act_window,help:mail_group.mail_group_message_action
msgid ""
"When people send an email to the alias of the list, they will appear here."
msgstr "عندما يرسل الأفراد بريداً إلكترونياً للقب القائمة، سوف يظهرون هنا. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_form
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_list
msgid "Whitelist"
msgstr "رفع الحظر "

#. module: mail_group
#: model:ir.model.fields.selection,name:mail_group.selection__mail_group_message__author_moderation__allow
msgid "Whitelisted"
msgstr "تم رفع الحظر "

#. module: mail_group
#. odoo-python
#: code:addons/mail_group/models/mail_group_message.py:0
#, python-format
msgid "Wrong status (%s)"
msgstr "الحالة خاطئة (%s) "

#. module: mail_group
#: model:ir.model.constraint,message:mail_group.constraint_mail_group_moderation_mail_group_email_uniq
msgid "You can create only one rule for a given email address in a group."
msgstr "بإمكانك إنشاء قاعدة واحدة فقط لكل عنوان بريد إلكتروني في مجموعة. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_notify_moderation
msgid "You have messages to moderate, please go for the proceedings."
msgstr "لديك رسائل للإشراف عليها، يرجى الانتقال إلى الإجراءات. "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_footer
msgid "_______________________________________________"
msgstr "_______________________________________________"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_reject_form
msgid "and send an email to the author ("
msgstr "وأرسل بريداً إلكترونياً إلى الكاتب ("

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_attachments
msgid "attachments"
msgstr "المرفقات "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.message_footer
msgid "by"
msgstr "بواسطة"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. \"Newsletter\""
msgstr "مثال: \"النشرة الأخبارية\" "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_view_form
msgid "e.g. domain.com"
msgstr "مثال: domain.com "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_group_message_view_search
msgid "group"
msgstr "المجموعة "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "has been"
msgstr "كانت "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid ""
"members<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"Traffic\"/>"
msgstr ""
"الأعضاء<br/>\n"
"                        <i class=\"fa fa-fw fa-envelope-o\" role=\"img\" aria-label=\"Traffic\" title=\"الزيارات \"/>"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "messages / month"
msgstr "رسائل / شهر"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "more replies"
msgstr "ردود أكثر"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.messages_short
msgid "replies"
msgstr "ردود"

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "subscribed to"
msgstr "مشتركاً في "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "the list"
msgstr "القائمة "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.confirmation_subscription
msgid "unsubscribed from"
msgstr "إلغاء الاشتراك من "

#. module: mail_group
#: model_terms:ir.ui.view,arch_db:mail_group.mail_groups
msgid "your email..."
msgstr "بريدك الإلكتروني..."
