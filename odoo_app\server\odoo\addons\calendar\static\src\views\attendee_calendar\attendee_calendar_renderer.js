/** @odoo-module **/

import { Calendar<PERSON>enderer } from "@web/views/calendar/calendar_renderer";
import { AttendeeCalendarCommonRenderer } from "@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer";
import { AttendeeCalendarYearRenderer } from "@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer";

export class AttendeeCalendarRenderer extends CalendarRenderer {}
AttendeeCalendarRenderer.components = {
    ...CalendarRenderer.components,
    day: AttendeeCalendarCommonRenderer,
    week: AttendeeCalendarCommonRenderer,
    month: Attendee<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>monRenderer,
    year: Attendee<PERSON>ale<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
};
