<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Introduction" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/intro.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The “Python library” contains several different kinds of components. It contains data types that would normally be considered part of the “core” of a language, such as numbers and lists. For these ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The “Python library” contains several different kinds of components. It contains data types that would normally be considered part of the “core” of a language, such as numbers and lists. For these ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Introduction &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Built-in Functions" href="functions.html" />
    <link rel="prev" title="The Python Standard Library" href="index.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/intro.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Introduction</a><ul>
<li><a class="reference internal" href="#notes-on-availability">Notes on availability</a><ul>
<li><a class="reference internal" href="#webassembly-platforms">WebAssembly platforms</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="index.html"
                          title="previous chapter">The Python Standard Library</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functions.html"
                          title="next chapter">Built-in Functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/intro.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functions.html" title="Built-in Functions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="index.html" title="The Python Standard Library"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Introduction</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="introduction">
<span id="library-intro"></span><h1>Introduction<a class="headerlink" href="#introduction" title="Link to this heading">¶</a></h1>
<p>The “Python library” contains several different kinds of components.</p>
<p>It contains data types that would normally be considered part of the “core” of a
language, such as numbers and lists.  For these types, the Python language core
defines the form of literals and places some constraints on their semantics, but
does not fully define the semantics.  (On the other hand, the language core does
define syntactic properties like the spelling and priorities of operators.)</p>
<p>The library also contains built-in functions and exceptions — objects that can
be used by all Python code without the need of an <a class="reference internal" href="../reference/simple_stmts.html#import"><code class="xref std std-keyword docutils literal notranslate"><span class="pre">import</span></code></a> statement.
Some of these are defined by the core language, but many are not essential for
the core semantics and are only described here.</p>
<p>The bulk of the library, however, consists of a collection of modules. There are
many ways to dissect this collection.  Some modules are written in C and built
in to the Python interpreter; others are written in Python and imported in
source form.  Some modules provide interfaces that are highly specific to
Python, like printing a stack trace; some provide interfaces that are specific
to particular operating systems, such as access to specific hardware; others
provide interfaces that are specific to a particular application domain, like
the World Wide Web. Some modules are available in all versions and ports of
Python; others are only available when the underlying system supports or
requires them; yet others are available only when a particular configuration
option was chosen at the time when Python was compiled and installed.</p>
<p>This manual is organized “from the inside out:” it first describes the built-in
functions, data types and exceptions, and finally the modules, grouped in
chapters of related modules.</p>
<p>This means that if you start reading this manual from the start, and skip to the
next chapter when you get bored, you will get a reasonable overview of the
available modules and application areas that are supported by the Python
library.  Of course, you don’t <em>have</em> to read it like a novel — you can also
browse the table of contents (in front of the manual), or look for a specific
function, module or term in the index (in the back).  And finally, if you enjoy
learning about random subjects, you choose a random page number (see module
<a class="reference internal" href="random.html#module-random" title="random: Generate pseudo-random numbers with various common distributions."><code class="xref py py-mod docutils literal notranslate"><span class="pre">random</span></code></a>) and read a section or two.  Regardless of the order in which you
read the sections of this manual, it helps to start with chapter
<a class="reference internal" href="functions.html#built-in-funcs"><span class="std std-ref">Built-in Functions</span></a>, as the remainder of the manual assumes familiarity with
this material.</p>
<p>Let the show begin!</p>
<section id="notes-on-availability">
<span id="availability"></span><h2>Notes on availability<a class="headerlink" href="#notes-on-availability" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>An “Availability: Unix” note means that this function is commonly found on
Unix systems.  It does not make any claims about its existence on a specific
operating system.</p></li>
<li><p>If not separately noted, all functions that claim “Availability: Unix” are
supported on macOS, which builds on a Unix core.</p></li>
<li><p>If an availability note contains both a minimum Kernel version and a minimum
libc version, then both conditions must hold. For example a feature with note
<em>Availability: Linux &gt;= 3.17 with glibc &gt;= 2.27</em> requires both Linux 3.17 or
newer and glibc 2.27 or newer.</p></li>
</ul>
<section id="webassembly-platforms">
<span id="wasm-availability"></span><h3>WebAssembly platforms<a class="headerlink" href="#webassembly-platforms" title="Link to this heading">¶</a></h3>
<p>The <a class="reference external" href="https://webassembly.org/">WebAssembly</a> platforms <code class="docutils literal notranslate"><span class="pre">wasm32-emscripten</span></code> (<a class="reference external" href="https://emscripten.org/">Emscripten</a>) and
<code class="docutils literal notranslate"><span class="pre">wasm32-wasi</span></code> (<a class="reference external" href="https://wasi.dev/">WASI</a>) provide a subset of POSIX APIs. WebAssembly runtimes
and browsers are sandboxed and have limited access to the host and external
resources. Any Python standard library module that uses processes, threading,
networking, signals, or other forms of inter-process communication (IPC), is
either not available or may not work as on other Unix-like systems. File I/O,
file system, and Unix permission-related functions are restricted, too.
Emscripten does not permit blocking I/O. Other blocking operations like
<a class="reference internal" href="time.html#time.sleep" title="time.sleep"><code class="xref py py-func docutils literal notranslate"><span class="pre">sleep()</span></code></a> block the browser event loop.</p>
<p>The properties and behavior of Python on WebAssembly platforms depend on the
<a class="reference external" href="https://emscripten.org/">Emscripten</a>-SDK or <a class="reference external" href="https://wasi.dev/">WASI</a>-SDK version, WASM runtimes (browser, NodeJS,
<a class="reference external" href="https://wasmtime.dev/">wasmtime</a>), and Python build time flags. WebAssembly, Emscripten, and WASI
are evolving standards; some features like networking may be
supported in the future.</p>
<p>For Python in the browser, users should consider <a class="reference external" href="https://pyodide.org/">Pyodide</a> or <a class="reference external" href="https://pyscript.net/">PyScript</a>.
PyScript is built on top of Pyodide, which itself is built on top of
CPython and Emscripten. Pyodide provides access to browsers’ JavaScript and
DOM APIs as well as limited networking capabilities with JavaScript’s
<code class="docutils literal notranslate"><span class="pre">XMLHttpRequest</span></code> and <code class="docutils literal notranslate"><span class="pre">Fetch</span></code> APIs.</p>
<ul class="simple">
<li><p>Process-related APIs are not available or always fail with an error. That
includes APIs that spawn new processes (<a class="reference internal" href="os.html#os.fork" title="os.fork"><code class="xref py py-func docutils literal notranslate"><span class="pre">fork()</span></code></a>,
<a class="reference internal" href="os.html#os.execve" title="os.execve"><code class="xref py py-func docutils literal notranslate"><span class="pre">execve()</span></code></a>), wait for processes (<a class="reference internal" href="os.html#os.waitpid" title="os.waitpid"><code class="xref py py-func docutils literal notranslate"><span class="pre">waitpid()</span></code></a>), send signals
(<a class="reference internal" href="os.html#os.kill" title="os.kill"><code class="xref py py-func docutils literal notranslate"><span class="pre">kill()</span></code></a>), or otherwise interact with processes. The
<a class="reference internal" href="subprocess.html#module-subprocess" title="subprocess: Subprocess management."><code class="xref py py-mod docutils literal notranslate"><span class="pre">subprocess</span></code></a> is importable but does not work.</p></li>
<li><p>The <a class="reference internal" href="socket.html#module-socket" title="socket: Low-level networking interface."><code class="xref py py-mod docutils literal notranslate"><span class="pre">socket</span></code></a> module is available, but is limited and behaves
differently from other platforms. On Emscripten, sockets are always
non-blocking and require additional JavaScript code and helpers on the
server to proxy TCP through WebSockets; see <a class="reference external" href="https://emscripten.org/docs/porting/networking.html">Emscripten Networking</a>
for more information. WASI snapshot preview 1 only permits sockets from an
existing file descriptor.</p></li>
<li><p>Some functions are stubs that either don’t do anything and always return
hardcoded values.</p></li>
<li><p>Functions related to file descriptors, file permissions, file ownership, and
links are limited and don’t support some operations. For example, WASI does
not permit symlinks with absolute file names.</p></li>
</ul>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Introduction</a><ul>
<li><a class="reference internal" href="#notes-on-availability">Notes on availability</a><ul>
<li><a class="reference internal" href="#webassembly-platforms">WebAssembly platforms</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="index.html"
                          title="previous chapter">The Python Standard Library</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functions.html"
                          title="next chapter">Built-in Functions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/intro.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functions.html" title="Built-in Functions"
             >next</a> |</li>
        <li class="right" >
          <a href="index.html" title="The Python Standard Library"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Introduction</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>