<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_send_availability_email" model="ir.cron">
        <field name="name">eCommerce: send email to customers about their abandoned cart</field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
        <field name="model_id" ref="model_website"/>
        <field name="code">model._send_abandoned_cart_email()</field>
        <field name="state">code</field>
    </record>
</odoo>
