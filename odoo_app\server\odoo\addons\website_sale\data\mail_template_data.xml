<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_sale_cart_recovery" model="mail.template">
            <field name="name">Ecommerce: Cart Recovery</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">You left items in your cart!</field>
            <field name="email_from">{{ (object.user_id.email_formatted or object.company_id.email_formatted or user.email_formatted or '') }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">If the setting is set, sent to authenticated visitors who abandoned their cart</field>
            <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="padding: 0px; background-color: white; color: #454748; border-collapse:separate;">
<tbody>
    <!-- CONTENT -->
    <tr>
        <td align="center" style="min-width: 590px;">
            <table border="0" cellpadding="0" cellspacing="0" width="590" style="min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;">
                <tr><td valign="top" style="font-size: 13px;">
                    <h1 style="color:#A9A9A9;">THERE'S SOMETHING IN YOUR CART.</h1>
                    Would you like to complete your purchase?<br/><br/>
                    <t t-if="object.order_line">
                        <t t-foreach="object.website_order_line" t-as="line">
                            <hr/>
                            <table width="100%">
                                <tr>
                                    <td style="padding: 10px; width:150px;">
                                        <img t-attf-src="/web/image/product.product/{{ line.product_id.id }}/image_128" style="width: 100px; height: 100px; object-fit: contain;" alt="Product image"></img>
                                    </td>
                                    <td>
                                        <strong t-out="line.product_id.display_name or ''">[FURN_7800] Desk Combination</strong><br/><t t-out="line.name or ''">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>
                                    </td>
                                    <td width="100px" align="right">
                                        <t t-out="int(line.product_uom_qty) or ''">10000</t> <t t-out="line.product_uom.name or ''">Units</t>
                                    </td>
                                </tr>
                            </table>
                        </t>
                        <hr/>
                    </t>
                    <div style="text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;">
                        <a t-attf-href="{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}"
                            target="_blank"
                            style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;">
                            Resume order
                        </a>
                    </div>
                    <t t-set="company" t-value="object.company_id or object.user_id.company_id or user.company_id"/>
                    <div style="text-align: center;"><strong>Thank you for shopping with <t t-out="company.name or ''">My Company (San Francisco)</t>!</strong></div>
                </td></tr>
            </table>
        </td>
    </tr>
</tbody>
</table>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="False"/>
        </record>
    </data>
</odoo>
