<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.ScaleScreen">
        <div class="scale-screen screen">
            <div class="screen-content d-flex flex-column h-100">
                <div class="top-content d-flex align-items-center p-2 border-bottom text-center">
                    <button class="button back btn btn-lg btn-secondary" t-on-click="back">
                        <i class="fa fa-angle-double-left me-2"></i>
                        Back
                    </button>
                    <h1 class="product-name flex-grow-1 m-0">
                        <t t-esc="productName" />
                    </h1>
                </div>
                <div class="centered-content w-100 mx-auto mt-3 border-start border-end text-center overflow-x-hidden overflow-y-auto">
                    <div class="weight js-weight m-2 p-4 rounded bg-view text-center fs-1">
                        <t t-esc="productWeightString" />
                    </div>
                    <div class="d-flex flex-row gap-2 m-2">
                        <div class="product-price d-flex align-items-center justify-content-center flex-grow-1 rounded text-bg-info bg-opacity-25 text-info fs-2">
                            <t
                                t-esc="env.utils.formatCurrency(productPrice) + '/' + productUom" />
                        </div>
                        <div class="computed-price flex-grow-1 p-3 bg-view rounded text-center fs-2 fw-bold">
                            <t t-esc="computedPriceString" />
                        </div>
                    </div>
                    <div class="buy-product btn btn-lg btn-primary d-flex align-items-center justify-content-center mx-2 mb-2 cursor-pointer" t-on-click="confirm">
                        Order
                        <i class="fa fa-angle-double-right ms-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
