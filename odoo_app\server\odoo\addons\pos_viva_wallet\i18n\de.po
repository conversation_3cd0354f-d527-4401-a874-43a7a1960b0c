# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_viva_wallet
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>-Nesselbosch, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2024-02-07 08:49+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
msgid "API Key"
msgstr "API-Schlüssel"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Cannot process transactions with negative amount."
msgstr "Transaktion mit negativem Betrag kann nicht durchgeführt werden."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid "Client ID"
msgstr "Client-ID"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_secret
msgid "Client secret"
msgstr "Client-Geheimnis"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Verbindung zum Odoo-Server konnte nicht hergestellt werden, bitte prüfen Sie"
" Ihre Internetverbindung und versuchen Sie es erneut."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "It is essential to provide API key for the use of viva wallet"
msgstr ""
"Für die Nutzung von Viva Wallet ist die Angabe eines API-Schlüssels "
"erforderlich."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid "Merchant ID"
msgstr "Händler-ID"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Message from Viva Wallet: %s"
msgstr "Nachricht von Viva Wallet: %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to cancel a Viva Wallet payment"
msgstr "Nur 'group_pos_user' dürfen Zahlungen von Viva Wallet stornieren"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Only 'group_pos_user' are allowed to get latest transaction status"
msgstr "Nur 'group_pos_user' darf den aktuellen Transaktionsstatus abrufen"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to get the payment status from Viva Wallet"
msgstr ""
"Nur 'group_pos_user' dürfen den Zahlungsstatus von Viva Wallet abrufen"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only 'group_pos_user' are allowed to send a Viva Wallet payment request"
msgstr ""
"Nur 'group_pos_user' dürfen Zahlungsanfragen von Viva Wallet versenden"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Zahlungsmethoden des Kassensystems"

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_session
msgid "Point of Sale Session"
msgstr "Kassensitzung"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Run transactions in the test environment."
msgstr "Transaktionen in der Testumgebung ausführen."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "Terminal ID"
msgstr "Terminal ID"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Test mode"
msgstr "Testmodus"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later. %s"
msgstr ""
"Es gibt Probleme zwischen uns und Viva Wallet, versuchen Sie es später noch "
"einmal. %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Viva Wallet, try again later.%s)"
msgstr ""
"Es gibt Probleme zwischen uns und Viva Wallet, versuchen Sie es später noch "
"einmal. %s)"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid ""
"Unable to retrieve Viva Wallet Bearer Token: Please verify that the Client "
"ID and Client Secret are correct"
msgstr ""
"Inhabertoken von Viva Wallet konnte nicht abgerufen werden: Bitte überprüfen"
" Sie, ob die Client-ID und das Client-Geheimnis korrekt sind."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"
msgstr ""
"Wird bei der Verbindung mit Viva Wallet verwendet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"
msgstr ""
"Wird bei der Verbindung mit Viva Wallet verwendet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_bearer_token
msgid "Viva Wallet Bearer Token"
msgstr "Inhabertoken von Viva Wallet"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
#, python-format
msgid "Viva Wallet Error"
msgstr "Fehler von Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_latest_response
msgid "Viva Wallet Latest Response"
msgstr "Letzte Antwort von Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_endpoint
msgid "Viva Wallet Webhook Endpoint"
msgstr "Webhook-Endpunkt von Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_verification_key
msgid "Viva Wallet Webhook Verification Key"
msgstr "Webhook-Verifizierungsschlüssel von Viva Wallet"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
#, python-format
msgid "Your transaction with Viva Wallet failed. Please try again later."
msgstr ""
"Ihre Transaktion mit Viva Wallet ist fehlgeschlagen. Bitte versuchen Sie es "
"später erneut."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "[Terminal ID of the Viva Wallet terminal], for example: 16002169"
msgstr "[Terminal-ID des Viva-Wallet-Terminals], zum Beispiel: 16002169"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a pos payment provider not registered."
msgstr ""
"hat eine Nachricht für einen nicht registrierten Kassenzahlungsanbieter "
"erhalten."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
#, python-format
msgid "received a message for a terminal not registered in Odoo: %s"
msgstr ""
"hat eine Nachricht für ein nicht in Odoo registriertes Terminal erhalten: %s"
