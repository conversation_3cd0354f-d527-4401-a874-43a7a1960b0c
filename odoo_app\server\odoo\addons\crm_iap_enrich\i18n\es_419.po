# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# Wil <PERSON>doo, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""
"<span>No se encontraron datos de la empresa según la dirección de correo "
"electrónico o la dirección de correo es de un proveedor de correos genérico."
" No se consumieron créditos. </span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""
"<span>No se pudo hacer el enriquecimiento ya que la dirección de correo no "
"es válida.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Permitir enriquecimiento manual"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "An error occurred during lead enrichment"
msgstr "Ocurrió un error al enriquecer los leads."

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: enriquecer leads (compras dentro de la aplicación)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Enriquecer"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "Enriquecer lead con información de la empresa"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "Enriquecer oportunidad con información de la empresa"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "Enriquecimiento terminado"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "Enriquecimiento de leads (según la dirección de correo electrónico)"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Lead enriched based on email address"
msgstr "Lead enriquecido según la dirección de correo electrónico"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/oportunidad"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Not enough credits for Lead Enrichment"
msgstr "No cuenta con créditos suficientes para enriquecer sus leads"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "The leads/opportunities have successfully been enriched"
msgstr "Los leads y las oportunidades se enriquecieron con éxito."

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""
"Si el servicio de compras dentro de la aplicación para el enriquecimiento de"
" leads basado en correo electrónico se ha realizado en este lead."
