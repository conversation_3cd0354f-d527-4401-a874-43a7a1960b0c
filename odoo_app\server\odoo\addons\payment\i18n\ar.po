# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "البيانات المحضرة "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>يرجى الدفع إلى: </h3><ul><li>البنك: %s</li><li>رقم الحساب: "
"%s</li><li>صاحب الحساب: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> تم ضبط هذه الخصائص على\n"
"                                مطابقة سلوك مقدمي الخدمة وتكاملهم معهم\n"
"                                Odoo فيما يتعلق بطريقة الدفع هذه. أي تغيير قد يؤدي إلى أخطاء\n"
"                                ويجب اختباره على قاعدة بيانات اختبارية أولاً. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"الرئيسية \" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"بعض المعاملات التي تنوي تحصيلها يمكن تحصيلها فقط "
"بشكل كامل. قم بالتعامل مع المعاملات بشكل فردي حتى تتمكن من تحصيل المبالغ "
"الجزئية. \"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"حذف طريقة الدفع \" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr "<i class=\"oi oi-arrow-right me-1\"></i> قم بتهيئة مزود الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            قم بتمكين طرق الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">احفظ تفاصيل الدفع الخاصة بي</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">غير منشور</span> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">منشور</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">طرق الدفع المحفوظة</span> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                كافة الدول مدعومة.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                كافة الدول مدعومة.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> محمي بواسطة</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid ""
"<span><i class=\"oi oi-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""
"<span><i class=\"oi oi-arrow-right\"/> كيفية تهيئة حسابك على PayPal</span> "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"<strong>No suitable payment method could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website\n"
"                                administrator."
msgstr ""
"<strong>لم يتم العثور على طريقة دفع مناسبة.</strong><br/>\n"
"                                إذا كنت تظن أنه هناك خطأ ما، يرجى التواصل مع مدير\n"
"                                الموقع الإلكتروني. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>تحذير!</strong> توجد عملية تحصيل دفع جزئي قيد الانتظار. يرجى الانتظار\n"
"                    قليلاً حتى تتم معالجتها. تحقق من تهيئة مزود الدفع إذا كانت\n"
"                    عملية تحصيل الدفع لا تزال قيد الانتظار بعد عدة دقائق. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>تحذير!</strong> لا يمكنك تحصيل مبلغ بقيمة سالبة لأكثر\n"
"                    من "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>تحذير</strong> إنشاء مزود دفع باستخدام رز <em>إنشاء</em> غير مدعوم.\n"
"                        يرجى استخدام إجراء <em>استنساخ</em> عوضاً عن ذلك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>تحذير</strong> تأكد من أنك قد قمت بتسجيل دخولك\n"
"                                    كالشريك الصحيح قبل القيام بالدفع. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>تحذير</strong> العملة غير موجودة أو غير صحيحة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>تحذير</strong> عليك تسجيل دخولك لإكمال الدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"تم إرسال طلب استرداد أموال لـ %(amount)s. سوف يتم إنشاء الدفع قريباً. مرجع "
"معاملة استرداد الأموال: %(ref)s (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "A token cannot be unarchived once it has been archived."
msgstr "لا يمكن إلغاء أرشفة الرمز بعد أن قد تمت أرشفته. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr "لقد بدأت معاملة لها المرجع %(ref)s (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"لقد بدأت معاملة لها المرجع %(ref)s لحفظ طريقة دفع جديدة (%(provider_name)s)."
" "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"لقد بدأت معاملة لها المرجع %(ref)s باستخدام طريقة الدفع %(token)s "
"(%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "الحساب "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "رقم الحساب"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "تفعيل"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "نشط"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "العنوان"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "السماح بالدفع والخروج والسريع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "السماح بحفظ طرق الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "تم تحصيل الدفع بالفعل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "تم إبطاله بالفعل "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "خدمات دفع Amazon "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#, python-format
msgid "Amount"
msgstr "مبلغ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "الحد الأقصى للمبلغ "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "المبلغ لتحصيله "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "An error occurred during the processing of your payment."
msgstr "حدث خطأ أثناء معالجة عملية الدفع الخاصة بك. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "تطبيق"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "مؤرشف"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "هل أنت متأكد من أنك ترغب في حذف طريقة الدفع هذه؟ "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"هل أنت متأكد أنك تريد إبطال المعاملة المُصرح بها؟ لا يمكن التراجع عن هذا "
"الإجراء. "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "رسالة التصريح "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "مصرح به "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "المبلغ المصرح به "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "التوافر"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "الطرق المتاحة "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "البنك"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "اسم البنك"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "العلامات التجارية "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "نموذج مستند رد الاتصال "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "تم رد الاتصال "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "تشفير رد الاتصال "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "طريقة رد الاتصال"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "معرف سجل رد الاتصال "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "إلغاء"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "تم الإلغاء "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Canceled Message"
msgstr "الرسالة الملغية "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot delete payment method"
msgstr "لا يمكن حذف طريقة الدفع "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot save payment method"
msgstr "لا يمكن حفظ طريقة الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#, python-format
msgid "Capture"
msgstr "تحصيل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "تحصيل المبلغ يدوياً "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "تسجيل المعاملة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"قم بالتقاط المبلغ من أودو، عندما يتم احتساب التوصيل. \n"
"استخدم ذلك إذا كنت ترغب في تغيير بطاقات عملائك، فقط عندما \n"
"تكون متأكداً من قدرتك على شحن البضاعة إليهم. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "المعاملات التابعة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "المعاملات التابعة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "اختر طريقة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "اختر طريقة أخرى <i class=\"oi oi-arrow-down\"/> "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "المدينة"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "إغلاق"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "رمز "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "اللون"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "الشركة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "التهيئة "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "تأكيد الحذف"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "تم التأكيد "

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "التطبيق المقابل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "الدول"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "الدولة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "إنشاء رمز "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "إنشاء مزود دفع جديد "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Creating a transaction from an archived token is forbidden."
msgstr "يحظر إنشاء معاملة من رمز قد تمت أرشفته. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "بيانات الاعتماد"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "البطاقة الائتمانية وبطاقة الخصم (عن طريق Stripe) "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "العملات"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "العملة"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "تعليمات الدفع المخصصة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "العميل"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "تحديد ترتيب العرض "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "النسخة التجريبية"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "معطل"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "رسالة الانتهاء "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "مسودة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "ممكن "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "للمؤسسات "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "خطأ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Error: %s"
msgstr "خطأ: %s "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "قالب استمارة الدفع والخروج السريع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout Supported"
msgstr "يدعم خاصية الدفع والخروج السريع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"يسمح الدفع السريع للعملاء بالدفع بشكل أسرع باستخدام طريقة دفع توفر جميع "
"معلومات الفوترة والشحن المطلوبة، مما يسمح بتخطي عملية الدفع. "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "كامل فقط "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "إنشاء رابط الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "إنشاء رابط دفع المبيعات "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "إنشاء ونسخ رابط الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "الذهاب إلى حسابي <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "به توابع بحالة المسودة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "يحتوي على مبلغ متبقي "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "هل تجاوزت عملية الدفع المعالجة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "رسالة المساعدة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "المُعرف"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "إذا لم يتم تأكيد الدفع بعد، بإمكانك التواصل معنا. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "صورة"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"في وضع الاختبار، تتم معالجة دفع مزيف عن طريق واجهة دفع تجريبية. \n"
"يُنصح بهذه الوضعية عند ضبط مزود الدفع. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "قالب استمارة مضمنة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "تثبيت"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "حالة التثبيت"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "تم التثبيت "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "خطأ في الخادم الداخلي "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "المبلغ الذي يجب تحصيله صالح "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "بعد مرحلة المعالجة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "طريقة الدفع الرئيسية "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "مرتبط حالياً بالمستندات التالية: "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "المسار النهائي "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "اللغة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "تاريخ آخر تغيير للحالة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "فلنقم بذلك "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr "لا يمكن تقديم طلب للمزود لأن المزود قد تم تعطيله. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "إدارة طرق السداد"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "يدوي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "يدعم عملية التحصيل اليدوية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "الحد الأقصى للمبلغ "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "الحد الأقصى المسموح به لتحصيل الأموال "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "الرسالة"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "الرسائل"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "الطريقة "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "الاسم"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "لم يتم تعيين مزود "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"لم يتم العثور على طريقة دفع يدوية لهذه الشركة. يرجى إنشاء واحدة من قائمة "
"مزودالدفع. "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr "لم يتم العثور على طرق دفع لمزودي الدفع لديك. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "No token can be assigned to the public partner."
msgstr "لا يمكن تعيين رمز للشريك العام. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "تطبيق أودو للمؤسسات "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "الدفع دون الاتصال بالإنترنت عن طريق الرمز "

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "خطة تمهيدية "

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "صورة خطوة التهيئة "

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "المدفوعات عبر الإنترنت "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "الدفع المباشر عبر الإنترنت "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "الدفع عبر الإنترنت عن طريق الرمز "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "الدفع عبر الإنترنت مع إعادة التوجيه "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "وحدهم المدراء يُسمح لهم بالوصول إلى هذه البيانات. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "وحدها المعاملات المصرح بها يمكن إبطالها. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "وحدها المعاملات المؤكدة يمكن استرداد الأموال فيها. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "العملية"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid "Operation not supported."
msgstr "العملية غير مدعومة."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "غير ذلك"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "طرق الدفع الأخرى "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "رمز هوية PDT "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Partial"
msgstr "جزئي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "الشريك"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "اسم الشريك"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "الدفع "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "معالج تحصيل الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "تفاصيل الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "متابعة الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "استمارة الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "تعليمات الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "رابط الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "طريقة الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "كود طريقة الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#, python-format
msgid "Payment Methods"
msgstr "طرق الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "مزودي الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "رمز الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "عدد رموز الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "رموز الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "معاملات الدفع "

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "معاملات الدفع المرتبطة برمز "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "Payment details saved on %(date)s"
msgstr "تفاصيل الدفع المخزنة في %(date)s "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "طرق الدفع "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "فشلت معالجة عملية الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "مزود الدفع "

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "معالج تهيئة مزود الدفع "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "الدفعات"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "قيد الانتظار "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "رسالة مُعلقة"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr ""
"يرجى التأكد من أن طريقة الدفع %(payment_method)s مدعومة من قِبَل "
"%(provider)s. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set a positive amount."
msgstr "يرجى تحديد مبلغ إيجابي. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount lower than %s."
msgstr "يرجى تعيين مبلغ أقل من %s. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "يرجى التبديل إلى شركة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "طريقة الدفع الرئيسية "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "تمت المعالجة بواسطة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "المزود"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "كود المزود "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "مرجع المزود "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "المزودون"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "تم النشر "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr "السبب: %s "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "إعادة التوجيه من القالب "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#, python-format
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "يجب أن يكون الرقم المرجعي فريداً! "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#, python-format
msgid "Refund"
msgstr "استرداد الأموال "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"استرداد الأموال هي ميزة تتيح استرداد الأموال للعملاء مباشرةً من خلال الدفع "
"في Odoo. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "الاستردادات "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "عدد عمليات استرداد الأموال "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "معرف المستند ذي الصلة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "نموذج المستند ذي الصلة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__require_currency
msgid "Require Currency"
msgstr "العملة المطلوبة "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "خصم SEPA المباشر"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "حفظ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "قم بتحديد الدول، أو اتركه فارغاً للسماح بأي دولة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr "قم بتحديد الدول. اتركه فارغاً لجعله متاحاً في كل مكان. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr "قم بتحديد العملات. اتركها فارغة حتى لا تقتصر على أي واحدة. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "قم بتحديد العملات، أو اتركه فارغاً للسماح بأي عملة. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "طريقة تهيئة الدفع المحددة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_allow_express_checkout
msgid "Show Allow Express Checkout"
msgstr "إظهار السماح بالدفع والخروج والسريع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr "إظهار السماح بالترميز الآلي "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_auth_msg
msgid "Show Auth Msg"
msgstr "إظهار رسالة المصادقة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "إظهار رسالة الإلغاء "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_credentials_page
msgid "Show Credentials Page"
msgstr "إظهار صفحة بيانات الاعتماد "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_done_msg
msgid "Show Done Msg"
msgstr "إظهار رسالة الانتهاء "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_pending_msg
msgid "Show Pending Msg"
msgstr "إظهار الرسالة المعلقة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_pre_msg
msgid "Show Pre Msg"
msgstr "إظهار الرسالة السابقة "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Skip"
msgstr "تخطي"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
#, python-format
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"بعض المعاملات التي تنوى تحصيلها يمكن تحصيلها بشكل كامل فقط. قم بمعالجة "
"المعاملات كل على حدة لتحصيل المبلغ الجزئي. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "المعاملة المصدرية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "الولاية "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "الحالة"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "تم اكتمال الخطوة!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "دعم التحصيل التلقائي للمدفوعات "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
msgid "Supported Countries"
msgstr "الدول المدعومة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
msgid "Supported Currencies"
msgstr "العملات المدعومة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "طرق الدفع المدعومة "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "مدعوم من قِبَل "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "وضع الاختبار "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Thank you!"
msgstr "شكرًا لك!"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "رمز الوصول غير صالح. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
#, python-format
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr "يجب أن يكون المبلغ المراد تحصيله موجباً ولا يمكن أن يتخطى %s. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr "الصورة الأساسية المستخدمة لطريقة الدفع هذه؛ بتنسيق 64x64 px. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr "العلامات التجارية لطرق الدفع التي سيتم عرضها في نموذج الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "المعاملات التابعة للمعاملة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "الجزء الواضح من تفاصيل دفع طريقة الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "لون البطاقة في عرض كانبان "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "رسالة المعلومات التكميلية عن الحالة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"الدول التي يكون فيها مزود الدفع هذا متاحاً. اتركه فارغاً لجعله متاحاً في "
"كافة الدول. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"العملات المتاحة مع مزود الدفع هذا. اتركه فارغاً حتى لا تقتصر على أي واحدة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "يجب ملء الحقول التالية: %s "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The following kwargs are not whitelisted: %s"
msgstr "لم يتم إدراج kwargs التالية في القائمة البيضاء: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "مرجع المعاملة الداخلي "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"قائمة البلدان التي يمكن استخدام طريقة الدفع هذه فيها (إذا كان المزود يسمح "
"بذلك). وفي بلدان أخرى، لا تتوفر طريقة الدفع هذه للعملاء. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"قائمة العملات التي تدعمها طريقة الدفع هذه (إذا كان المزود يسمح بذلك). عند "
"الدفع بعملة أخرى، لن تكون طريقة الدفع هذه متاحة للعملاء. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "قائمة مزودي الدفع الذين يدعمون طريقة الدفع هذه. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr "عملة الشركة الرئيسية، تُستخدم لعرض الحقول النقدية. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"المبلغ الأقصى للدفع الذي يكون مزود الدفع هذا متاحاً فيه. اتركه فارغاً لجعله "
"متاحاً لأي مبلغ دفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "الرسالة المعروضة إذا كان الدفع مصرحاً به "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr "الرسالة المعروضة إذا تم إلغاء الطلب أثناء معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr "الرسالة المعروضة إذا تم إكمال الطلب بنجاح بعد معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "الرسالة المعروضة إذا كان الطلب معلقاً بعد معالجة الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "الرسالة المعروضة لشرح ومساعدة عملية الدفع "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"يجب أن يكون الدفع إما مباشراً، أو عن طريق إعادة التوجيه أو عن طريق رمز. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"طريقة الدفع الأساسية لطريقة الدفع الحالية، إذا كانت الثاني عبارة عن علامة تجارية.\n"
"على سبيل المثال، \"البطاقة\" هي طريقة الدفع الأساسية للعلامة التجارية للبطاقة \"VISA\". "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "مرجع مزود الدفع لرمز المعاملة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "مرجع مزود الدفع للمعاملة "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "الصورة التي تم تغيير حجمها والمعروضة على استمارة الدفع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "المسار الذي تتم إعادة توجيه المستخدم إليه بعد المعاملة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "المعاملة المصدرية للمعاملات التابعة ذات الصلة "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "الكود التقني لطريقة الدفع هذه. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "الكود التقني لمزود الدفع هذا. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"القالب يقوم بتكوين استمارة يتم إرسالها لإعادة توجيه المستخدم عند الدفع "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "القالب الذي يقوم بتكوين استمارة الدفع والخروج السريع. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr "القالب يقوم بتكوين استمارة الدفع الضمني عند إكمال عملية دفع مباشر "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr ""
"القالب يقوم بتكوين استمارة الدفع الضمني عند إكمال عملية دفع عن طريق الرمز. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"المعاملة مع المرجع %(ref)s لـ %(amount)s واجهت خطأً (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"المعاملة مع المرجع %(ref)s لـ %(amount)s تم تفويضها (%(provider_name)s). "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"المعاملات مع المرجع %(ref)s لـ %(amount)s تم تأكيدها (%(provider_name)s). "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "لا توجد معاملات لعرضها "

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "لم يتم إنشاء رمز بعد. "

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "There is nothing to be paid."
msgstr "لا يوجد شيء لدفعه. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "لا يوجد شيء لدفعه. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method. Archiving tokens is irreversible."
msgstr ""
"سيقوم هذا الإجراء أيضاً بأرشفة الرموز %s المسجلة مع طريقة الدفع هذه. لا "
"يمكنك التراجع عن أرشفة الرمز. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. Archiving tokens is irreversible."
msgstr ""
"سيقوم هذا الإجراء أيضاً بأرشفة الرموز %s المسجلة مع مزود الدفع هذا. لا يمكنك"
" التراجع عن أرشفة الرمز. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"يتحكم ذلك بإمكانية حفظ العملاء لطرق الدفع كرموز دفع. \n"
"رمز الدفع هو رابط مجهول المصدر لتفاصيل طريقة الدفع المحفوظة في \n"
"قاعدة بيانات مزود الدفع، مما يتيح للعميل إعادة استخدامها لعملية الشراء القادمة. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"يتحكم ذلك بإمكانية استخدام العملاء طرق الدفع السريعة. يتيح الدفع والخروج "
"السريع للعملاء الدفع عن طريق Google Pay وApple Pay والتي يتم جمع معلومات "
"العنوان منها عند الدفع. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"ليس لهذا الشريك عنوان بريد إلكتروني، مما قد يسبب المشاكل مع بعض مزودي الدفع.\n"
"                     ننصح بتعيين بريد إلكتروني لهذا الشريك. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"تتطلب طريقة الدفع هذه شريكاً في الجريمة؛ سيتوجب عليك تمكين مزود الدفع الذي "
"يدعم هذه الطريقة أولاً. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"لقد تم تأكيد هذه المعاملة بعد معالجة عمليات تحصيل الدفع الجزئي والمعاملات "
"الجزية الباطلة (%(provider)s). "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "قالب استمارة مضمنة للرمز "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization Supported"
msgstr "الترميز الآلي مدعوم "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"الترميز الآلي هو عملية حفظ تفاصيل الدفع كرمز يمكن إعادة استخدامه لاحقاً دون "
"الحاجة إلى إدخال تفاصيل الدفع مرة أخرى. "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "معاملة"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr "تفويض المعاملات غير مدعوم من قِبَل مزودي الدفع التالين: %s "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
msgid "Type of Refund Supported"
msgstr "نوع عمليات الاسترداد المدعومة "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the server. Please wait."
msgstr "تعذّر الاتصال بالخادم. يرجى الانتظار. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "غير منشور"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "ترقية"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "تصديق طريقة الدفع "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "المبلغ المتبقي المبطل "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "إبطال المعاملة"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "Warning"
msgstr "تحذير"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "رسالة تحذير"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Warning!"
msgstr "تحذير!"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "لم نتمكن من العثور على عملية الدفع الحاصة بك، لكن لا تقلق. "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment. Please wait."
msgstr "جاري معالجة الدفع. يرجى الانتظار. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "ما إذا كان رمز الدفع يجب إنشاؤه عند معالجة المعاملة أم لا "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "ما إذا كان مزود كل من المعاملات يدعم التحصيل الجزئي للمدفوعات أم لا. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "ما إذا كان قد تم تنفيذ الاستدعاء بالفعل أم لا "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"ما إذا كان مزود الدفع مرئياً على الموقع الإلكتروني أم لا. سيكون بإمكانك "
"استخدام الرموز ولكن ستكون مرئية فقط في إدارة الاستمارات. "

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "تحويل بنكي"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr "لا يمكنك تغيير شركة مزود دفع يحتوي على معاملات موجودة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr "لا يمكنك حذف مزود الدفع %s؛ قم بتعطيله أو إلغاء تثبيته عوضاً عن ذلك. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "You cannot publish a disabled provider."
msgstr "لا يمكنك نشر مزود دفع تم تعطيله. "

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr "لا تملك صلاحية الوصول إلى رمز الدفع هذا. "

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr "سوف تتلقى رسالة بريد إلكتروني لتأكيد الدفع خلال بضعة دقائق. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sips
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been authorized."
msgstr "تم التصريح بالدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sips
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been cancelled."
msgstr "لقد تم إلغاء الدفع. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sips
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "لقد تمت معالجة الدفع بنجاح ولكن بانتظار الموافقة. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_sips
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been successfully processed."
msgstr "لقد تمت معالجة الدفع بنجاح. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Your payment has not been processed yet."
msgstr "لم تتم معالجة عملية الدفع بعد. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr "طرق الدفع الخاصة بك "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "الرمز البريدي"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "الرمز البريدي"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "طريقة الدفع "

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "payment: post-process transactions"
msgstr "الدفع: معاملات ما بعد العملية "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "مزود "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"لإتمام عملية\n"
"                    الدفع. "
