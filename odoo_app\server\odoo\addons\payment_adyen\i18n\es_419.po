# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_adyen
# 
# Translators:
# Wil O<PERSON>, 2023
# Iran Villalobos López, 2023
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid ""
"<strong>Warning:</strong> To capture the amount manually, you also need to set\n"
"                    the Capture Delay to manual on your Adyen account settings."
msgstr ""
"<strong>Advertencia:</strong> Para capturar el importe de forma manual es necesario que configure\n"
"                   el retraso de la captura con la opción manual en los ajustes de su cuenta de Adyen."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "A request was sent to void the transaction with reference %s (%s)."
msgstr ""
"Se envió una solicitud para anular la transacción con la referencia %s (%s)."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_key
msgid "API Key"
msgstr "Clave API"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "API URL Prefix"
msgstr "Prefijo de la URL de la API"

#. module: payment_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_provider__code__adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr "Ocurrió un error al procesar su pago. Inténtelo de nuevo."

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot display the payment form"
msgstr "No se puede mostrar el formulario de pago"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_client_key
msgid "Client Key"
msgstr "Clave de cliente"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "No se pudo establecer la conexión con la API."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "HMAC Key"
msgstr "Clave HMAC"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_capture_wizard__has_adyen_tx
msgid "Has Adyen Tx"
msgstr "Tiene Adyen Tx"

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect payment details"
msgstr "Detalles de pago incorrectos "

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_provider_form
msgid "Learn More"
msgstr "Más información"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "Merchant Account"
msgstr "Cuenta de comerciante"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "Asistente de captura de pagos "

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_provider
msgid "Payment Provider"
msgstr "Proveedor de pago"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_token
msgid "Payment Token"
msgstr "Token de pago"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_adyen
#. odoo-javascript
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "Error al procesar el pago"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data for child transaction with missing transaction values"
msgstr ""
"Datos recibidos para transacción secundaria con valores de transacción "
"faltante"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment state: %s"
msgstr "Datos recibidos con un estado de pago no válido: %s"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "Datos recibidos en los que falta la referencia del vendedor"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr "Datos recibidos con estado de pago pendiente."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "Datos recibidos de solicitud de pago manipulados."

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "Shopper Reference"
msgstr "Referencia del comprador"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_key
msgid "The API key of the webservice user"
msgstr "La clave API del usuario del servicio web"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_hmac_key
msgid "The HMAC key of the webhook"
msgstr "La clave HMAC del webhook"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"The amount processed by Adyen for the transaction %s is different than the "
"one requested. Another transaction is created with the correct amount."
msgstr ""
"El importe procesado por Adyen para la transacción %s es distinto al "
"solicitado. Se creó otra transacción con el importe correcto."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_api_url_prefix
msgid "The base URL for the API endpoints"
msgstr "La URL base para los puntos de conexión de la API"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The capture of the transaction with reference %s failed."
msgstr "Falló la captura de la transacción con la referencia %s."

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.payment_capture_wizard_view_form
msgid ""
"The capture or void of the transaction might take a few minutes to be\n"
"                    processed by Adyen and reflected in Odoo."
msgstr ""
"Es posible que Adyen tarde unos minutos en procesar la captura\n"
"                    o anulación de la transacción para que se refleje en Odoo."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"The capture request of %(amount)s for the transaction with reference %(ref)s"
" has been requested (%(provider_name)s)."
msgstr ""
"Se solicitó la captura de %(amount)s para la transacción con referencia "
"%(ref)s (%(provider_name)s)."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_client_key
msgid "The client key of the webservice user"
msgstr "La clave de cliente del usuario del servicio web"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__adyen_merchant_account
msgid "The code of the merchant account to use with this provider"
msgstr "El código de la cuenta comercial que se debe usar con este proveedor"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_provider.py:0
#, python-format
msgid "The communication with the API failed. Details: %s"
msgstr "Ocurrió un error en la comunicación con la API. Detalles: %s"

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "El código técnico de este proveedor de pagos."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "La transacción no está vinculada a un token."

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "The unique reference of the partner owning this token"
msgstr "La referencia única del contacto propietario de este token"

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The void of the transaction with reference %s failed."
msgstr "Falló la anulación de la transacción con la referencia %s."

#. module: payment_adyen
#. odoo-python
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Your payment was refused. Please try again."
msgstr "Su pago fue rechazado, vuelva a intentarlo."
