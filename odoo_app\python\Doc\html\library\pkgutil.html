<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="pkgutil — Package extension utility" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pkgutil.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/pkgutil.py This module provides utilities for the import system, in particular package support." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/pkgutil.py This module provides utilities for the import system, in particular package support." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>pkgutil — Package extension utility &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="modulefinder — Find modules used by a script" href="modulefinder.html" />
    <link rel="prev" title="zipimport — Import modules from Zip archives" href="zipimport.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pkgutil.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipimport.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipimport</span></code> — Import modules from Zip archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="modulefinder.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pkgutil.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="modulefinder.html" title="modulefinder — Find modules used by a script"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="zipimport.html" title="zipimport — Import modules from Zip archives"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" accesskey="U">Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pkgutil</span></code> — Package extension utility</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-pkgutil">
<span id="pkgutil-package-extension-utility"></span><h1><a class="reference internal" href="#module-pkgutil" title="pkgutil: Utilities for the import system."><code class="xref py py-mod docutils literal notranslate"><span class="pre">pkgutil</span></code></a> — Package extension utility<a class="headerlink" href="#module-pkgutil" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/pkgutil.py">Lib/pkgutil.py</a></p>
<hr class="docutils" />
<p>This module provides utilities for the import system, in particular package
support.</p>
<dl class="py class">
<dt class="sig sig-object py" id="pkgutil.ModuleInfo">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">ModuleInfo</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module_finder</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">ispkg</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.ModuleInfo" title="Link to this definition">¶</a></dt>
<dd><p>A namedtuple that holds a brief summary of a module’s info.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.extend_path">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">extend_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.extend_path" title="Link to this definition">¶</a></dt>
<dd><p>Extend the search path for the modules which comprise a package.  Intended
use is to place the following code in a package’s <code class="file docutils literal notranslate"><span class="pre">__init__.py</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">pkgutil</span> <span class="kn">import</span> <span class="n">extend_path</span>
<span class="n">__path__</span> <span class="o">=</span> <span class="n">extend_path</span><span class="p">(</span><span class="n">__path__</span><span class="p">,</span> <span class="vm">__name__</span><span class="p">)</span>
</pre></div>
</div>
<p>For each directory on <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> that has a subdirectory that matches the
package name, add the subdirectory to the package’s <a class="reference internal" href="../reference/import.html#path__" title="__path__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__path__</span></code></a>.  This is useful
if one wants to distribute different parts of a single logical package as multiple
directories.</p>
<p>It also looks for <code class="file docutils literal notranslate"><span class="pre">*.pkg</span></code> files beginning where <code class="docutils literal notranslate"><span class="pre">*</span></code> matches the
<em>name</em> argument.  This feature is similar to <code class="file docutils literal notranslate"><span class="pre">*.pth</span></code> files (see the
<a class="reference internal" href="site.html#module-site" title="site: Module responsible for site-specific configuration."><code class="xref py py-mod docutils literal notranslate"><span class="pre">site</span></code></a> module for more information), except that it doesn’t special-case
lines starting with <code class="docutils literal notranslate"><span class="pre">import</span></code>.  A <code class="file docutils literal notranslate"><span class="pre">*.pkg</span></code> file is trusted at face
value: apart from checking for duplicates, all entries found in a
<code class="file docutils literal notranslate"><span class="pre">*.pkg</span></code> file are added to the path, regardless of whether they exist
on the filesystem.  (This is a feature.)</p>
<p>If the input path is not a list (as is the case for frozen packages) it is
returned unchanged.  The input path is not modified; an extended copy is
returned.  Items are only appended to the copy at the end.</p>
<p>It is assumed that <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> is a sequence.  Items of <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>
that are not strings referring to existing directories are ignored. Unicode
items on <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a> that cause errors when used as filenames may cause
this function to raise an exception (in line with <a class="reference internal" href="os.path.html#os.path.isdir" title="os.path.isdir"><code class="xref py py-func docutils literal notranslate"><span class="pre">os.path.isdir()</span></code></a>
behavior).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.find_loader">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">find_loader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.find_loader" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve a module <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> for the given <em>fullname</em>.</p>
<p>This is a backwards compatibility wrapper around
<a class="reference internal" href="importlib.html#importlib.util.find_spec" title="importlib.util.find_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.find_spec()</span></code></a> that converts most failures to
<a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> and only returns the loader rather than the full
<a class="reference internal" href="importlib.html#importlib.machinery.ModuleSpec" title="importlib.machinery.ModuleSpec"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.ModuleSpec</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Updated to be based on <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-0451/"><strong>PEP 451</strong></a></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="importlib.html#importlib.util.find_spec" title="importlib.util.find_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.find_spec()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.get_importer">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">get_importer</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path_item</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.get_importer" title="Link to this definition">¶</a></dt>
<dd><p>Retrieve a <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> for the given <em>path_item</em>.</p>
<p>The returned finder is cached in <a class="reference internal" href="sys.html#sys.path_importer_cache" title="sys.path_importer_cache"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_importer_cache</span></code></a> if it was
newly created by a path hook.</p>
<p>The cache (or part of it) can be cleared manually if a rescan of
<a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a> is necessary.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.get_loader">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">get_loader</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">module_or_name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.get_loader" title="Link to this definition">¶</a></dt>
<dd><p>Get a <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> object for <em>module_or_name</em>.</p>
<p>If the module or package is accessible via the normal import mechanism, a
wrapper around the relevant part of that machinery is returned.  Returns
<code class="docutils literal notranslate"><span class="pre">None</span></code> if the module cannot be found or imported.  If the named module is
not already imported, its containing package (if any) is imported, in order
to establish the package <code class="docutils literal notranslate"><span class="pre">__path__</span></code>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-3"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Updated to be based on <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-0451/"><strong>PEP 451</strong></a></p>
</div>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.12, will be removed in version 3.14: </span>Use <a class="reference internal" href="importlib.html#importlib.util.find_spec" title="importlib.util.find_spec"><code class="xref py py-func docutils literal notranslate"><span class="pre">importlib.util.find_spec()</span></code></a> instead.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.iter_importers">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">iter_importers</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fullname</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.iter_importers" title="Link to this definition">¶</a></dt>
<dd><p>Yield <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> objects for the given module name.</p>
<p>If fullname contains a <code class="docutils literal notranslate"><span class="pre">'.'</span></code>, the finders will be for the package
containing fullname, otherwise they will be all registered top level
finders (i.e. those on both <a class="reference internal" href="sys.html#sys.meta_path" title="sys.meta_path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.meta_path</span></code></a> and <a class="reference internal" href="sys.html#sys.path_hooks" title="sys.path_hooks"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path_hooks</span></code></a>).</p>
<p>If the named module is in a package, that package is imported as a side
effect of invoking this function.</p>
<p>If no module name is specified, all top level finders are produced.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.iter_modules">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">iter_modules</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.iter_modules" title="Link to this definition">¶</a></dt>
<dd><p>Yields <a class="reference internal" href="#pkgutil.ModuleInfo" title="pkgutil.ModuleInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleInfo</span></code></a> for all submodules on <em>path</em>, or, if
<em>path</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, all top-level modules on <a class="reference internal" href="sys.html#sys.path" title="sys.path"><code class="xref py py-data docutils literal notranslate"><span class="pre">sys.path</span></code></a>.</p>
<p><em>path</em> should be either <code class="docutils literal notranslate"><span class="pre">None</span></code> or a list of paths to look for modules in.</p>
<p><em>prefix</em> is a string to output on the front of every module name on output.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only works for a <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> which defines an <code class="docutils literal notranslate"><span class="pre">iter_modules()</span></code>
method. This interface is non-standard, so the module also provides
implementations for <a class="reference internal" href="importlib.html#importlib.machinery.FileFinder" title="importlib.machinery.FileFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.FileFinder</span></code></a> and
<a class="reference internal" href="zipimport.html#zipimport.zipimporter" title="zipimport.zipimporter"><code class="xref py py-class docutils literal notranslate"><span class="pre">zipimport.zipimporter</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-6"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.walk_packages">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">walk_packages</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">prefix</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">''</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">onerror</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.walk_packages" title="Link to this definition">¶</a></dt>
<dd><p>Yields <a class="reference internal" href="#pkgutil.ModuleInfo" title="pkgutil.ModuleInfo"><code class="xref py py-class docutils literal notranslate"><span class="pre">ModuleInfo</span></code></a> for all modules recursively on
<em>path</em>, or, if <em>path</em> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, all accessible modules.</p>
<p><em>path</em> should be either <code class="docutils literal notranslate"><span class="pre">None</span></code> or a list of paths to look for modules in.</p>
<p><em>prefix</em> is a string to output on the front of every module name on output.</p>
<p>Note that this function must import all <em>packages</em> (<em>not</em> all modules!) on
the given <em>path</em>, in order to access the <code class="docutils literal notranslate"><span class="pre">__path__</span></code> attribute to find
submodules.</p>
<p><em>onerror</em> is a function which gets called with one argument (the name of the
package which was being imported) if any exception occurs while trying to
import a package.  If no <em>onerror</em> function is supplied, <a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a>s
are caught and ignored, while all other exceptions are propagated,
terminating the search.</p>
<p>Examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="c1"># list all modules python can access</span>
<span class="n">walk_packages</span><span class="p">()</span>

<span class="c1"># list all submodules of ctypes</span>
<span class="n">walk_packages</span><span class="p">(</span><span class="n">ctypes</span><span class="o">.</span><span class="n">__path__</span><span class="p">,</span> <span class="n">ctypes</span><span class="o">.</span><span class="vm">__name__</span> <span class="o">+</span> <span class="s1">&#39;.&#39;</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Only works for a <a class="reference internal" href="../glossary.html#term-finder"><span class="xref std std-term">finder</span></a> which defines an <code class="docutils literal notranslate"><span class="pre">iter_modules()</span></code>
method. This interface is non-standard, so the module also provides
implementations for <a class="reference internal" href="importlib.html#importlib.machinery.FileFinder" title="importlib.machinery.FileFinder"><code class="xref py py-class docutils literal notranslate"><span class="pre">importlib.machinery.FileFinder</span></code></a> and
<a class="reference internal" href="zipimport.html#zipimport.zipimporter" title="zipimport.zipimporter"><code class="xref py py-class docutils literal notranslate"><span class="pre">zipimport.zipimporter</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.3: </span>Updated to be based directly on <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> rather than relying
on the package internal <span class="target" id="index-7"></span><a class="pep reference external" href="https://peps.python.org/pep-0302/"><strong>PEP 302</strong></a> import emulation.</p>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.get_data">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">get_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">package</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">resource</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.get_data" title="Link to this definition">¶</a></dt>
<dd><p>Get a resource from a package.</p>
<p>This is a wrapper for the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a>
<a class="reference internal" href="importlib.html#importlib.abc.ResourceLoader.get_data" title="importlib.abc.ResourceLoader.get_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_data</span></code></a> API.  The
<em>package</em> argument should be the name of a package, in standard module format
(<code class="docutils literal notranslate"><span class="pre">foo.bar</span></code>).  The <em>resource</em> argument should be in the form of a relative
filename, using <code class="docutils literal notranslate"><span class="pre">/</span></code> as the path separator.  The parent directory name
<code class="docutils literal notranslate"><span class="pre">..</span></code> is not allowed, and nor is a rooted name (starting with a <code class="docutils literal notranslate"><span class="pre">/</span></code>).</p>
<p>The function returns a binary string that is the contents of the specified
resource.</p>
<p>For packages located in the filesystem, which have already been imported,
this is the rough equivalent of:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">d</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">dirname</span><span class="p">(</span><span class="n">sys</span><span class="o">.</span><span class="n">modules</span><span class="p">[</span><span class="n">package</span><span class="p">]</span><span class="o">.</span><span class="vm">__file__</span><span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="nb">open</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">d</span><span class="p">,</span> <span class="n">resource</span><span class="p">),</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
</pre></div>
</div>
<p>If the package cannot be located or loaded, or it uses a <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a>
which does not support <a class="reference internal" href="importlib.html#importlib.abc.ResourceLoader.get_data" title="importlib.abc.ResourceLoader.get_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_data</span></code></a>,
then <code class="docutils literal notranslate"><span class="pre">None</span></code> is returned.  In particular, the <a class="reference internal" href="../glossary.html#term-loader"><span class="xref std std-term">loader</span></a> for
<a class="reference internal" href="../glossary.html#term-namespace-package"><span class="xref std std-term">namespace packages</span></a> does not support
<a class="reference internal" href="importlib.html#importlib.abc.ResourceLoader.get_data" title="importlib.abc.ResourceLoader.get_data"><code class="xref py py-meth docutils literal notranslate"><span class="pre">get_data</span></code></a>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="pkgutil.resolve_name">
<span class="sig-prename descclassname"><span class="pre">pkgutil.</span></span><span class="sig-name descname"><span class="pre">resolve_name</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#pkgutil.resolve_name" title="Link to this definition">¶</a></dt>
<dd><p>Resolve a name to an object.</p>
<p>This functionality is used in numerous places in the standard library (see
<a class="reference external" href="https://bugs.python.org/issue?&#64;action=redirect&amp;bpo=12915">bpo-12915</a>) - and equivalent functionality is also in widely used
third-party packages such as setuptools, Django and Pyramid.</p>
<p>It is expected that <em>name</em> will be a string in one of the following
formats, where W is shorthand for a valid Python identifier and dot stands
for a literal period in these pseudo-regexes:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">W(.W)*</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">W(.W)*:(W(.W)*)?</span></code></p></li>
</ul>
<p>The first form is intended for backward compatibility only. It assumes that
some part of the dotted name is a package, and the rest is an object
somewhere within that package, possibly nested inside other objects.
Because the place where the package stops and the object hierarchy starts
can’t be inferred by inspection, repeated attempts to import must be done
with this form.</p>
<p>In the second form, the caller makes the division point clear through the
provision of a single colon: the dotted name to the left of the colon is a
package to be imported, and the dotted name to the right is the object
hierarchy within that package. Only one import is needed in this form. If
it ends with the colon, then a module object is returned.</p>
<p>The function will return an object (which might be a module), or raise one
of the following exceptions:</p>
<p><a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> – if <em>name</em> isn’t in a recognised format.</p>
<p><a class="reference internal" href="exceptions.html#ImportError" title="ImportError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ImportError</span></code></a> – if an import failed when it shouldn’t have.</p>
<p><a class="reference internal" href="exceptions.html#AttributeError" title="AttributeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">AttributeError</span></code></a> – If a failure occurred when traversing the object
hierarchy within the imported package to get to the desired object.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="zipimport.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">zipimport</span></code> — Import modules from Zip archives</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="modulefinder.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">modulefinder</span></code> — Find modules used by a script</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pkgutil.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="modulefinder.html" title="modulefinder — Find modules used by a script"
             >next</a> |</li>
        <li class="right" >
          <a href="zipimport.html" title="zipimport — Import modules from Zip archives"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="modules.html" >Importing Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pkgutil</span></code> — Package extension utility</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>