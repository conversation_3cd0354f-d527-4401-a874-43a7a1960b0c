<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Product Tags -->
    <record id="product_tag_form_view_inherit_website_sale" model="ir.ui.view">
        <field name="name">product.tag.form.inherit.website.sale</field>
        <field name="model">product.tag</field>
        <field name="inherit_id" ref="product.product_tag_form_view"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="visible_on_ecommerce"/>
                <field name="color"
                       widget='color'
                       invisible="not visible_on_ecommerce"/>
                <field name="image"
                       widget="image"
                       options="{'size': [0, 55]}"
                       invisible="not visible_on_ecommerce"/>
                <p class="text-muted" colspan="2" invisible="not visible_on_ecommerce">
                    If an image is set, the color will not be used on eCommerce.
                </p>
            </field>
        </field>
    </record>

    <record id="product_tag_tree_view_inherit_website_sale" model="ir.ui.view">
        <field name="name">product.tag.tree.inherit.website.sale</field>
        <field name="model">product.tag</field>
        <field name="inherit_id" ref="product.product_tag_tree_view"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="visible_on_ecommerce" optional="show"/>
                <field name="color"
                       widget="color"
                       optional="hide"
                       readonly="1"
                       invisible="not visible_on_ecommerce"/>
                <field name="image"
                       widget="image"
                       options="{'size': [0, 30]}"
                       optional="hide"
                       invisible="not visible_on_ecommerce"/>
            </field>
        </field>
    </record>

</odoo>
