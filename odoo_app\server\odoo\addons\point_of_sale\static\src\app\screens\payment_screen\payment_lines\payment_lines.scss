.paymentlines .electronic_payment {
    background: $gray-200;
    border-collapse: unset;
    font-size: 16px;
    padding-right: 0;
}

.paymentline .electronic_status {
    flex-grow: 1;
    margin-left: 10px;
    margin-right: 10px;
    /* make the name of the payment line to be truncated with ellipsis */
    white-space: nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    padding: map-get($spacers, 3);
}

.paymentlines .electronic_payment div:first-child {
    flex-grow: 2;
    margin-left: 10px;
    margin-right: 10px;
}

.paymentlines .electronic_payment div:last-child {
    flex-grow: 1;
    text-align: center;
    padding: map-get($spacers, 3);
}
