# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Malay (https://app.transifex.com/odoo/teams/41243/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Check our Documentation"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Aktifkan"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Activated"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Syarikat"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Tetapan Konfigurasi"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "Connect"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Dicipta oleh"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Dicipta pada"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Mata wang"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Custom"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Setiap hari"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Deactivated"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Hadam"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr ""

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr ""

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Title"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Nama paparan"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentasi"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Email Address"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Kumpulan mengikut"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#. odoo-python
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "Terakhir Diubah suai pada"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Kemas Kini Terakhir oleh"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Kemas Kini Terakhir pada"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages Sent"
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Bulanan"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Nama"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Mailing Date"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Suku tahunan"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Penerima"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "Perangkaan"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "Status"

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "Title"
msgstr "Tajuk"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "User"
msgstr "pengguna"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Want to add your own KPIs?<br/>"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from:<br/>"
msgstr ""

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr ""

#. module: digest
#. odoo-python
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr ""
