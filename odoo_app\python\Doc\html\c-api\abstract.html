<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Abstract Objects Layer" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/abstract.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The functions in this chapter interact with Python objects regardless of their type, or with wide classes of object types (e.g. all numerical types, or all sequence types). When used on object type..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The functions in this chapter interact with Python objects regardless of their type, or with wide classes of object types (e.g. all numerical types, or all sequence types). When used on object type..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Abstract Objects Layer &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Object Protocol" href="object.html" />
    <link rel="prev" title="Support for Perf Maps" href="perfmaps.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/abstract.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="perfmaps.html"
                          title="previous chapter">Support for Perf Maps</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="object.html"
                          title="next chapter">Object Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/abstract.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="object.html" title="Object Protocol"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="perfmaps.html" title="Support for Perf Maps"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Abstract Objects Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="abstract-objects-layer">
<span id="abstract"></span><h1>Abstract Objects Layer<a class="headerlink" href="#abstract-objects-layer" title="Link to this heading">¶</a></h1>
<p>The functions in this chapter interact with Python objects regardless of their
type, or with wide classes of object types (e.g. all numerical types, or all
sequence types).  When used on object types for which they do not apply, they
will raise a Python exception.</p>
<p>It is not possible to use these functions on objects that are not properly
initialized, such as a list object that has been created by <a class="reference internal" href="list.html#c.PyList_New" title="PyList_New"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyList_New()</span></code></a>,
but whose items have not been set to some non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> value yet.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="object.html">Object Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="call.html">Call Protocol</a><ul>
<li class="toctree-l2"><a class="reference internal" href="call.html#the-tp-call-protocol">The <em>tp_call</em> Protocol</a></li>
<li class="toctree-l2"><a class="reference internal" href="call.html#the-vectorcall-protocol">The Vectorcall Protocol</a><ul>
<li class="toctree-l3"><a class="reference internal" href="call.html#recursion-control">Recursion Control</a></li>
<li class="toctree-l3"><a class="reference internal" href="call.html#vectorcall-support-api">Vectorcall Support API</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="call.html#object-calling-api">Object Calling API</a></li>
<li class="toctree-l2"><a class="reference internal" href="call.html#call-support-api">Call Support API</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="number.html">Number Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="sequence.html">Sequence Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="mapping.html">Mapping Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="iter.html">Iterator Protocol</a></li>
<li class="toctree-l1"><a class="reference internal" href="buffer.html">Buffer Protocol</a><ul>
<li class="toctree-l2"><a class="reference internal" href="buffer.html#buffer-structure">Buffer structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="buffer.html#buffer-request-types">Buffer request types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#request-independent-fields">request-independent fields</a></li>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#readonly-format">readonly, format</a></li>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#shape-strides-suboffsets">shape, strides, suboffsets</a></li>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#contiguity-requests">contiguity requests</a></li>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#compound-requests">compound requests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="buffer.html#complex-arrays">Complex arrays</a><ul>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#numpy-style-shape-and-strides">NumPy-style: shape and strides</a></li>
<li class="toctree-l3"><a class="reference internal" href="buffer.html#pil-style-shape-strides-and-suboffsets">PIL-style: shape, strides and suboffsets</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="buffer.html#buffer-related-functions">Buffer-related functions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="objbuffer.html">Old Buffer Protocol</a></li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="perfmaps.html"
                          title="previous chapter">Support for Perf Maps</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="object.html"
                          title="next chapter">Object Protocol</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/abstract.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="object.html" title="Object Protocol"
             >next</a> |</li>
        <li class="right" >
          <a href="perfmaps.html" title="Support for Perf Maps"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Abstract Objects Layer</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>