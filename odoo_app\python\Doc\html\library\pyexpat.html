<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="xml.parsers.expat — Fast XML parsing using Expat" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/pyexpat.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="The xml.parsers.expat module is a Python interface to the Expat non-validating XML parser. The module provides a single extension type, xmlparser, that represents the current state of an XML parser..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="The xml.parsers.expat module is a Python interface to the Expat non-validating XML parser. The module provides a single extension type, xmlparser, that represents the current state of an XML parser..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>xml.parsers.expat — Fast XML parsing using Expat &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Internet Protocols and Support" href="internet.html" />
    <link rel="prev" title="xml.sax.xmlreader — Interface for XML parsers" href="xml.sax.reader.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/pyexpat.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a><ul>
<li><a class="reference internal" href="#xmlparser-objects">XMLParser Objects</a></li>
<li><a class="reference internal" href="#expaterror-exceptions">ExpatError Exceptions</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#module-xml.parsers.expat.model">Content Model Descriptions</a></li>
<li><a class="reference internal" href="#module-xml.parsers.expat.errors">Expat error constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.reader.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="internet.html"
                          title="next chapter">Internet Protocols and Support</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pyexpat.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="internet.html" title="Internet Protocols and Support"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="xml.sax.reader.html" title="xml.sax.xmlreader — Interface for XML parsers"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" accesskey="U">Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-xml.parsers.expat">
<span id="xml-parsers-expat-fast-xml-parsing-using-expat"></span><h1><a class="reference internal" href="#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> — Fast XML parsing using Expat<a class="headerlink" href="#module-xml.parsers.expat" title="Link to this heading">¶</a></h1>
<hr class="docutils" />
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The <code class="xref py py-mod docutils literal notranslate"><span class="pre">pyexpat</span></code> module is not secure against maliciously
constructed data.  If you need to parse untrusted or unauthenticated data see
<a class="reference internal" href="xml.html#xml-vulnerabilities"><span class="std std-ref">XML vulnerabilities</span></a>.</p>
</div>
<p id="index-0">The <a class="reference internal" href="#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> module is a Python interface to the Expat
non-validating XML parser. The module provides a single extension type,
<code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code>, that represents the current state of an XML parser.  After
an <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object has been created, various attributes of the object
can be set to handler functions.  When an XML document is then fed to the
parser, the handler functions are called for the character data and markup in
the XML document.</p>
<p id="index-1">This module uses the <code class="xref py py-mod docutils literal notranslate"><span class="pre">pyexpat</span></code> module to provide access to the Expat
parser.  Direct use of the <code class="xref py py-mod docutils literal notranslate"><span class="pre">pyexpat</span></code> module is deprecated.</p>
<p>This module provides one exception and one type object:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="xml.parsers.expat.ExpatError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.</span></span><span class="sig-name descname"><span class="pre">ExpatError</span></span><a class="headerlink" href="#xml.parsers.expat.ExpatError" title="Link to this definition">¶</a></dt>
<dd><p>The exception raised when Expat reports an error.  See section
<a class="reference internal" href="#expaterror-objects"><span class="std std-ref">ExpatError Exceptions</span></a> for more information on interpreting Expat errors.</p>
</dd></dl>

<dl class="py exception">
<dt class="sig sig-object py" id="xml.parsers.expat.error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.</span></span><span class="sig-name descname"><span class="pre">error</span></span><a class="headerlink" href="#xml.parsers.expat.error" title="Link to this definition">¶</a></dt>
<dd><p>Alias for <a class="reference internal" href="#xml.parsers.expat.ExpatError" title="xml.parsers.expat.ExpatError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExpatError</span></code></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.XMLParserType">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.</span></span><span class="sig-name descname"><span class="pre">XMLParserType</span></span><a class="headerlink" href="#xml.parsers.expat.XMLParserType" title="Link to this definition">¶</a></dt>
<dd><p>The type of the return values from the <a class="reference internal" href="#xml.parsers.expat.ParserCreate" title="xml.parsers.expat.ParserCreate"><code class="xref py py-func docutils literal notranslate"><span class="pre">ParserCreate()</span></code></a> function.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> module contains two functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="xml.parsers.expat.ErrorString">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.</span></span><span class="sig-name descname"><span class="pre">ErrorString</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">errno</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.ErrorString" title="Link to this definition">¶</a></dt>
<dd><p>Returns an explanatory string for a given error number <em>errno</em>.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="xml.parsers.expat.ParserCreate">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.</span></span><span class="sig-name descname"><span class="pre">ParserCreate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">encoding</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">namespace_separator</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.ParserCreate" title="Link to this definition">¶</a></dt>
<dd><p>Creates and returns a new <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object.   <em>encoding</em>, if specified,
must be a string naming the encoding  used by the XML data.  Expat doesn’t
support as many encodings as Python does, and its repertoire of encodings can’t
be extended; it supports UTF-8, UTF-16, ISO-8859-1 (Latin1), and ASCII.  If
<em>encoding</em> <a class="footnote-reference brackets" href="#id3" id="id1" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a> is given it will override the implicit or explicit encoding of the
document.</p>
<p>Expat can optionally do XML namespace processing for you, enabled by providing a
value for <em>namespace_separator</em>.  The value must be a one-character string; a
<a class="reference internal" href="exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> will be raised if the string has an illegal length (<code class="docutils literal notranslate"><span class="pre">None</span></code>
is considered the same as omission).  When namespace processing is enabled,
element type names and attribute names that belong to a namespace will be
expanded.  The element name passed to the element handlers
<code class="xref py py-attr docutils literal notranslate"><span class="pre">StartElementHandler</span></code> and <code class="xref py py-attr docutils literal notranslate"><span class="pre">EndElementHandler</span></code> will be the
concatenation of the namespace URI, the namespace separator character, and the
local part of the name.  If the namespace separator is a zero byte (<code class="docutils literal notranslate"><span class="pre">chr(0)</span></code>)
then the namespace URI and the local part will be concatenated without any
separator.</p>
<p>For example, if <em>namespace_separator</em> is set to a space character (<code class="docutils literal notranslate"><span class="pre">'</span> <span class="pre">'</span></code>) and
the following document is parsed:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="nt">&lt;root</span><span class="w"> </span><span class="na">xmlns    =</span><span class="w"> </span><span class="s">&quot;http://default-namespace.org/&quot;</span>
<span class="w">      </span><span class="na">xmlns:py =</span><span class="w"> </span><span class="s">&quot;http://www.python.org/ns/&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;py:elem1</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">  </span><span class="nt">&lt;elem2</span><span class="w"> </span><span class="na">xmlns=</span><span class="s">&quot;&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="nt">&lt;/root&gt;</span>
</pre></div>
</div>
<p><code class="xref py py-attr docutils literal notranslate"><span class="pre">StartElementHandler</span></code> will receive the following strings for each
element:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">http</span><span class="p">:</span><span class="o">//</span><span class="n">default</span><span class="o">-</span><span class="n">namespace</span><span class="o">.</span><span class="n">org</span><span class="o">/</span> <span class="n">root</span>
<span class="n">http</span><span class="p">:</span><span class="o">//</span><span class="n">www</span><span class="o">.</span><span class="n">python</span><span class="o">.</span><span class="n">org</span><span class="o">/</span><span class="n">ns</span><span class="o">/</span> <span class="n">elem1</span>
<span class="n">elem2</span>
</pre></div>
</div>
<p>Due to limitations in the <code class="docutils literal notranslate"><span class="pre">Expat</span></code> library used by <code class="xref py py-mod docutils literal notranslate"><span class="pre">pyexpat</span></code>,
the <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> instance returned can only be used to parse a single
XML document.  Call <code class="docutils literal notranslate"><span class="pre">ParserCreate</span></code> for each document to provide unique
parser instances.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><a class="reference external" href="http://www.libexpat.org/">The Expat XML Parser</a></dt><dd><p>Home page of the Expat project.</p>
</dd>
</dl>
</div>
<section id="xmlparser-objects">
<span id="id2"></span><h2>XMLParser Objects<a class="headerlink" href="#xmlparser-objects" title="Link to this heading">¶</a></h2>
<p><code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> objects have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.Parse">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">Parse</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">isfinal</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.Parse" title="Link to this definition">¶</a></dt>
<dd><p>Parses the contents of the string <em>data</em>, calling the appropriate handler
functions to process the parsed data.  <em>isfinal</em> must be true on the final call
to this method; it allows the parsing of a single file in fragments,
not the submission of multiple files.
<em>data</em> can be the empty string at any time.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ParseFile">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ParseFile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ParseFile" title="Link to this definition">¶</a></dt>
<dd><p>Parse XML data reading from the object <em>file</em>.  <em>file</em> only needs to provide
the <code class="docutils literal notranslate"><span class="pre">read(nbytes)</span></code> method, returning the empty string when there’s no more
data.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.SetBase">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">SetBase</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">base</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.SetBase" title="Link to this definition">¶</a></dt>
<dd><p>Sets the base to be used for resolving relative URIs in system identifiers in
declarations.  Resolving relative identifiers is left to the application: this
value will be passed through as the <em>base</em> argument to the
<a class="reference internal" href="#xml.parsers.expat.xmlparser.ExternalEntityRefHandler" title="xml.parsers.expat.xmlparser.ExternalEntityRefHandler"><code class="xref py py-func docutils literal notranslate"><span class="pre">ExternalEntityRefHandler()</span></code></a>, <a class="reference internal" href="#xml.parsers.expat.xmlparser.NotationDeclHandler" title="xml.parsers.expat.xmlparser.NotationDeclHandler"><code class="xref py py-func docutils literal notranslate"><span class="pre">NotationDeclHandler()</span></code></a>, and
<a class="reference internal" href="#xml.parsers.expat.xmlparser.UnparsedEntityDeclHandler" title="xml.parsers.expat.xmlparser.UnparsedEntityDeclHandler"><code class="xref py py-func docutils literal notranslate"><span class="pre">UnparsedEntityDeclHandler()</span></code></a> functions.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.GetBase">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">GetBase</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.GetBase" title="Link to this definition">¶</a></dt>
<dd><p>Returns a string containing the base set by a previous call to <a class="reference internal" href="#xml.parsers.expat.xmlparser.SetBase" title="xml.parsers.expat.xmlparser.SetBase"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetBase()</span></code></a>,
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if  <a class="reference internal" href="#xml.parsers.expat.xmlparser.SetBase" title="xml.parsers.expat.xmlparser.SetBase"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetBase()</span></code></a> hasn’t been called.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.GetInputContext">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">GetInputContext</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.GetInputContext" title="Link to this definition">¶</a></dt>
<dd><p>Returns the input data that generated the current event as a string. The data is
in the encoding of the entity which contains the text. When called while an
event handler is not active, the return value is <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ExternalEntityParserCreate">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ExternalEntityParserCreate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em><span class="optional">[</span>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ExternalEntityParserCreate" title="Link to this definition">¶</a></dt>
<dd><p>Create a “child” parser which can be used to parse an external parsed entity
referred to by content parsed by the parent parser.  The <em>context</em> parameter
should be the string passed to the <a class="reference internal" href="#xml.parsers.expat.xmlparser.ExternalEntityRefHandler" title="xml.parsers.expat.xmlparser.ExternalEntityRefHandler"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ExternalEntityRefHandler()</span></code></a> handler
function, described below. The child parser is created with the
<a class="reference internal" href="#xml.parsers.expat.xmlparser.ordered_attributes" title="xml.parsers.expat.xmlparser.ordered_attributes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ordered_attributes</span></code></a> and <a class="reference internal" href="#xml.parsers.expat.xmlparser.specified_attributes" title="xml.parsers.expat.xmlparser.specified_attributes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">specified_attributes</span></code></a> set to the values of
this parser.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.SetParamEntityParsing">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">SetParamEntityParsing</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.SetParamEntityParsing" title="Link to this definition">¶</a></dt>
<dd><p>Control parsing of parameter entities (including the external DTD subset).
Possible <em>flag</em> values are <code class="xref py py-const docutils literal notranslate"><span class="pre">XML_PARAM_ENTITY_PARSING_NEVER</span></code>,
<code class="xref py py-const docutils literal notranslate"><span class="pre">XML_PARAM_ENTITY_PARSING_UNLESS_STANDALONE</span></code> and
<code class="xref py py-const docutils literal notranslate"><span class="pre">XML_PARAM_ENTITY_PARSING_ALWAYS</span></code>.  Return true if setting the flag
was successful.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.UseForeignDTD">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">UseForeignDTD</span></span><span class="sig-paren">(</span><span class="optional">[</span><em class="sig-param"><span class="n"><span class="pre">flag</span></span></em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.UseForeignDTD" title="Link to this definition">¶</a></dt>
<dd><p>Calling this with a true value for <em>flag</em> (the default) will cause Expat to call
the <a class="reference internal" href="#xml.parsers.expat.xmlparser.ExternalEntityRefHandler" title="xml.parsers.expat.xmlparser.ExternalEntityRefHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ExternalEntityRefHandler</span></code></a> with <a class="reference internal" href="constants.html#None" title="None"><code class="xref py py-const docutils literal notranslate"><span class="pre">None</span></code></a> for all arguments to
allow an alternate DTD to be loaded.  If the document does not contain a
document type declaration, the <a class="reference internal" href="#xml.parsers.expat.xmlparser.ExternalEntityRefHandler" title="xml.parsers.expat.xmlparser.ExternalEntityRefHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ExternalEntityRefHandler</span></code></a> will still be
called, but the <a class="reference internal" href="#xml.parsers.expat.xmlparser.StartDoctypeDeclHandler" title="xml.parsers.expat.xmlparser.StartDoctypeDeclHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StartDoctypeDeclHandler</span></code></a> and
<a class="reference internal" href="#xml.parsers.expat.xmlparser.EndDoctypeDeclHandler" title="xml.parsers.expat.xmlparser.EndDoctypeDeclHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EndDoctypeDeclHandler</span></code></a> will not be called.</p>
<p>Passing a false value for <em>flag</em> will cancel a previous call that passed a true
value, but otherwise has no effect.</p>
<p>This method can only be called before the <a class="reference internal" href="#xml.parsers.expat.xmlparser.Parse" title="xml.parsers.expat.xmlparser.Parse"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Parse()</span></code></a> or <a class="reference internal" href="#xml.parsers.expat.xmlparser.ParseFile" title="xml.parsers.expat.xmlparser.ParseFile"><code class="xref py py-meth docutils literal notranslate"><span class="pre">ParseFile()</span></code></a>
methods are called; calling it after either of those have been called causes
<a class="reference internal" href="#xml.parsers.expat.ExpatError" title="xml.parsers.expat.ExpatError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExpatError</span></code></a> to be raised with the <a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-attr docutils literal notranslate"><span class="pre">code</span></code></a> attribute set to
<code class="docutils literal notranslate"><span class="pre">errors.codes[errors.XML_ERROR_CANT_CHANGE_FEATURE_ONCE_PARSING]</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.SetReparseDeferralEnabled">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">SetReparseDeferralEnabled</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">enabled</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.SetReparseDeferralEnabled" title="Link to this definition">¶</a></dt>
<dd><div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Calling <code class="docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled(False)</span></code> has security implications,
as detailed below; please make sure to understand these consequences
prior to using the <code class="docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled</span></code> method.</p>
</div>
<p>Expat 2.6.0 introduced a security mechanism called “reparse deferral”
where instead of causing denial of service through quadratic runtime
from reparsing large tokens, reparsing of unfinished tokens is now delayed
by default until a sufficient amount of input is reached.
Due to this delay, registered handlers may — depending of the sizing of
input chunks pushed to Expat — no longer be called right after pushing new
input to the parser.  Where immediate feedback and taking over responsiblity
of protecting against denial of service from large tokens are both wanted,
calling <code class="docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled(False)</span></code> disables reparse deferral
for the current Expat parser instance, temporarily or altogether.
Calling <code class="docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled(True)</span></code> allows re-enabling reparse
deferral.</p>
<p>Note that <a class="reference internal" href="#xml.parsers.expat.xmlparser.SetReparseDeferralEnabled" title="xml.parsers.expat.xmlparser.SetReparseDeferralEnabled"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled()</span></code></a> has been backported to some
prior releases of CPython as a security fix.  Check for availability of
<a class="reference internal" href="#xml.parsers.expat.xmlparser.SetReparseDeferralEnabled" title="xml.parsers.expat.xmlparser.SetReparseDeferralEnabled"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetReparseDeferralEnabled()</span></code></a> using <a class="reference internal" href="functions.html#hasattr" title="hasattr"><code class="xref py py-func docutils literal notranslate"><span class="pre">hasattr()</span></code></a> if used in code
running across a variety of Python versions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.3.</span></p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.GetReparseDeferralEnabled">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">GetReparseDeferralEnabled</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.GetReparseDeferralEnabled" title="Link to this definition">¶</a></dt>
<dd><p>Returns whether reparse deferral is currently enabled for the given
Expat parser instance.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.3.</span></p>
</div>
</dd></dl>

<p><code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> objects have the following attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.buffer_size">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">buffer_size</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.buffer_size" title="Link to this definition">¶</a></dt>
<dd><p>The size of the buffer used when <a class="reference internal" href="#xml.parsers.expat.xmlparser.buffer_text" title="xml.parsers.expat.xmlparser.buffer_text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">buffer_text</span></code></a> is true.
A new buffer size can be set by assigning a new integer value
to this attribute.
When the size is changed, the buffer will be flushed.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.buffer_text">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">buffer_text</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.buffer_text" title="Link to this definition">¶</a></dt>
<dd><p>Setting this to true causes the <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object to buffer textual
content returned by Expat to avoid multiple calls to the
<a class="reference internal" href="#xml.parsers.expat.xmlparser.CharacterDataHandler" title="xml.parsers.expat.xmlparser.CharacterDataHandler"><code class="xref py py-meth docutils literal notranslate"><span class="pre">CharacterDataHandler()</span></code></a> callback whenever possible.  This can improve
performance substantially since Expat normally breaks character data into chunks
at every line ending.  This attribute is false by default, and may be changed at
any time. Note that when it is false, data that does not contain newlines
may be chunked too.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.buffer_used">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">buffer_used</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.buffer_used" title="Link to this definition">¶</a></dt>
<dd><p>If <a class="reference internal" href="#xml.parsers.expat.xmlparser.buffer_text" title="xml.parsers.expat.xmlparser.buffer_text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">buffer_text</span></code></a> is enabled, the number of bytes stored in the buffer.
These bytes represent UTF-8 encoded text.  This attribute has no meaningful
interpretation when <a class="reference internal" href="#xml.parsers.expat.xmlparser.buffer_text" title="xml.parsers.expat.xmlparser.buffer_text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">buffer_text</span></code></a> is false.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ordered_attributes">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ordered_attributes</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ordered_attributes" title="Link to this definition">¶</a></dt>
<dd><p>Setting this attribute to a non-zero integer causes the attributes to be
reported as a list rather than a dictionary.  The attributes are presented in
the order found in the document text.  For each attribute, two list entries are
presented: the attribute name and the attribute value.  (Older versions of this
module also used this format.)  By default, this attribute is false; it may be
changed at any time.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.specified_attributes">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">specified_attributes</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.specified_attributes" title="Link to this definition">¶</a></dt>
<dd><p>If set to a non-zero integer, the parser will report only those attributes which
were specified in the document instance and not those which were derived from
attribute declarations.  Applications which set this need to be especially
careful to use what additional information is available from the declarations as
needed to comply with the standards for the behavior of XML processors.  By
default, this attribute is false; it may be changed at any time.</p>
</dd></dl>

<p>The following attributes contain values relating to the most recent error
encountered by an <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object, and will only have correct values
once a call to <code class="xref py py-meth docutils literal notranslate"><span class="pre">Parse()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">ParseFile()</span></code> has raised an
<a class="reference internal" href="#xml.parsers.expat.ExpatError" title="xml.parsers.expat.ExpatError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">xml.parsers.expat.ExpatError</span></code></a> exception.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ErrorByteIndex">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ErrorByteIndex</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ErrorByteIndex" title="Link to this definition">¶</a></dt>
<dd><p>Byte index at which an error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ErrorCode">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ErrorCode</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ErrorCode" title="Link to this definition">¶</a></dt>
<dd><p>Numeric code specifying the problem.  This value can be passed to the
<a class="reference internal" href="#xml.parsers.expat.ErrorString" title="xml.parsers.expat.ErrorString"><code class="xref py py-func docutils literal notranslate"><span class="pre">ErrorString()</span></code></a> function, or compared to one of the constants defined in the
<code class="docutils literal notranslate"><span class="pre">errors</span></code> object.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ErrorColumnNumber">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ErrorColumnNumber</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ErrorColumnNumber" title="Link to this definition">¶</a></dt>
<dd><p>Column number at which an error occurred.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ErrorLineNumber">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ErrorLineNumber</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ErrorLineNumber" title="Link to this definition">¶</a></dt>
<dd><p>Line number at which an error occurred.</p>
</dd></dl>

<p>The following attributes contain values relating to the current parse location
in an <code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object.  During a callback reporting a parse event they
indicate the location of the first of the sequence of characters that generated
the event.  When called outside of a callback, the position indicated will be
just past the last parse event (regardless of whether there was an associated
callback).</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.CurrentByteIndex">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">CurrentByteIndex</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.CurrentByteIndex" title="Link to this definition">¶</a></dt>
<dd><p>Current byte index in the parser input.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.CurrentColumnNumber">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">CurrentColumnNumber</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.CurrentColumnNumber" title="Link to this definition">¶</a></dt>
<dd><p>Current column number in the parser input.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.CurrentLineNumber">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">CurrentLineNumber</span></span><a class="headerlink" href="#xml.parsers.expat.xmlparser.CurrentLineNumber" title="Link to this definition">¶</a></dt>
<dd><p>Current line number in the parser input.</p>
</dd></dl>

<p>Here is the list of handlers that can be set.  To set a handler on an
<code class="xref py py-class docutils literal notranslate"><span class="pre">xmlparser</span></code> object <em>o</em>, use <code class="docutils literal notranslate"><span class="pre">o.handlername</span> <span class="pre">=</span> <span class="pre">func</span></code>.  <em>handlername</em> must
be taken from the following list, and <em>func</em> must be a callable object accepting
the correct number of arguments.  The arguments are all strings, unless
otherwise stated.</p>
<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.XmlDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">XmlDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">version</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">encoding</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">standalone</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.XmlDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called when the XML declaration is parsed.  The XML declaration is the
(optional) declaration of the applicable version of the XML recommendation, the
encoding of the document text, and an optional “standalone” declaration.
<em>version</em> and <em>encoding</em> will be strings, and <em>standalone</em> will be <code class="docutils literal notranslate"><span class="pre">1</span></code> if the
document is declared standalone, <code class="docutils literal notranslate"><span class="pre">0</span></code> if it is declared not to be standalone,
or <code class="docutils literal notranslate"><span class="pre">-1</span></code> if the standalone clause was omitted. This is only available with
Expat version 1.95.0 or newer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.StartDoctypeDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">StartDoctypeDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">doctypeName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">has_internal_subset</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.StartDoctypeDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called when Expat begins parsing the document type declaration (<code class="docutils literal notranslate"><span class="pre">&lt;!DOCTYPE</span>
<span class="pre">...</span></code>).  The <em>doctypeName</em> is provided exactly as presented.  The <em>systemId</em> and
<em>publicId</em> parameters give the system and public identifiers if specified, or
<code class="docutils literal notranslate"><span class="pre">None</span></code> if omitted.  <em>has_internal_subset</em> will be true if the document
contains and internal document declaration subset. This requires Expat version
1.2 or newer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.EndDoctypeDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">EndDoctypeDeclHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.EndDoctypeDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called when Expat is done parsing the document type declaration. This requires
Expat version 1.2 or newer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ElementDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ElementDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">model</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ElementDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called once for each element type declaration.  <em>name</em> is the name of the
element type, and <em>model</em> is a representation of the content model.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.AttlistDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">AttlistDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">elname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attname</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">default</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">required</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.AttlistDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for each declared attribute for an element type.  If an attribute list
declaration declares three attributes, this handler is called three times, once
for each attribute.  <em>elname</em> is the name of the element to which the
declaration applies and <em>attname</em> is the name of the attribute declared.  The
attribute type is a string passed as <em>type</em>; the possible values are
<code class="docutils literal notranslate"><span class="pre">'CDATA'</span></code>, <code class="docutils literal notranslate"><span class="pre">'ID'</span></code>, <code class="docutils literal notranslate"><span class="pre">'IDREF'</span></code>, … <em>default</em> gives the default value for
the attribute used when the attribute is not specified by the document instance,
or <code class="docutils literal notranslate"><span class="pre">None</span></code> if there is no default value (<code class="docutils literal notranslate"><span class="pre">#IMPLIED</span></code> values).  If the
attribute is required to be given in the document instance, <em>required</em> will be
true. This requires Expat version 1.95.0 or newer.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.StartElementHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">StartElementHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">attributes</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.StartElementHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for the start of every element.  <em>name</em> is a string containing the
element name, and <em>attributes</em> is the element attributes. If
<a class="reference internal" href="#xml.parsers.expat.xmlparser.ordered_attributes" title="xml.parsers.expat.xmlparser.ordered_attributes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ordered_attributes</span></code></a> is true, this is a list (see
<a class="reference internal" href="#xml.parsers.expat.xmlparser.ordered_attributes" title="xml.parsers.expat.xmlparser.ordered_attributes"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ordered_attributes</span></code></a> for a full description). Otherwise it’s a
dictionary mapping names to values.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.EndElementHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">EndElementHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.EndElementHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for the end of every element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ProcessingInstructionHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ProcessingInstructionHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">target</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ProcessingInstructionHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for every processing instruction.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.CharacterDataHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">CharacterDataHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.CharacterDataHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for character data.  This will be called for normal character data, CDATA
marked content, and ignorable whitespace.  Applications which must distinguish
these cases can use the <a class="reference internal" href="#xml.parsers.expat.xmlparser.StartCdataSectionHandler" title="xml.parsers.expat.xmlparser.StartCdataSectionHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StartCdataSectionHandler</span></code></a>,
<a class="reference internal" href="#xml.parsers.expat.xmlparser.EndCdataSectionHandler" title="xml.parsers.expat.xmlparser.EndCdataSectionHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EndCdataSectionHandler</span></code></a>, and <a class="reference internal" href="#xml.parsers.expat.xmlparser.ElementDeclHandler" title="xml.parsers.expat.xmlparser.ElementDeclHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">ElementDeclHandler</span></code></a> callbacks to
collect the required information. Note that the character data may be
chunked even if it is short and so you may receive more than one call to
<a class="reference internal" href="#xml.parsers.expat.xmlparser.CharacterDataHandler" title="xml.parsers.expat.xmlparser.CharacterDataHandler"><code class="xref py py-meth docutils literal notranslate"><span class="pre">CharacterDataHandler()</span></code></a>. Set the <a class="reference internal" href="#xml.parsers.expat.xmlparser.buffer_text" title="xml.parsers.expat.xmlparser.buffer_text"><code class="xref py py-attr docutils literal notranslate"><span class="pre">buffer_text</span></code></a> instance attribute
to <code class="docutils literal notranslate"><span class="pre">True</span></code> to avoid that.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.UnparsedEntityDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">UnparsedEntityDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">entityName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">notationName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.UnparsedEntityDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for unparsed (NDATA) entity declarations.  This is only present for
version 1.2 of the Expat library; for more recent versions, use
<a class="reference internal" href="#xml.parsers.expat.xmlparser.EntityDeclHandler" title="xml.parsers.expat.xmlparser.EntityDeclHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EntityDeclHandler</span></code></a> instead.  (The underlying function in the Expat
library has been declared obsolete.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.EntityDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">EntityDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">entityName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_parameter_entity</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">value</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">notationName</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.EntityDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for all entity declarations.  For parameter and internal entities,
<em>value</em> will be a string giving the declared contents of the entity; this will
be <code class="docutils literal notranslate"><span class="pre">None</span></code> for external entities.  The <em>notationName</em> parameter will be
<code class="docutils literal notranslate"><span class="pre">None</span></code> for parsed entities, and the name of the notation for unparsed
entities. <em>is_parameter_entity</em> will be true if the entity is a parameter entity
or false for general entities (most applications only need to be concerned with
general entities). This is only available starting with version 1.95.0 of the
Expat library.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.NotationDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">NotationDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">notationName</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.NotationDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for notation declarations.  <em>notationName</em>, <em>base</em>, and <em>systemId</em>, and
<em>publicId</em> are strings if given.  If the public identifier is omitted,
<em>publicId</em> will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.StartNamespaceDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">StartNamespaceDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">uri</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.StartNamespaceDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called when an element contains a namespace declaration.  Namespace declarations
are processed before the <a class="reference internal" href="#xml.parsers.expat.xmlparser.StartElementHandler" title="xml.parsers.expat.xmlparser.StartElementHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StartElementHandler</span></code></a> is called for the element
on which declarations are placed.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.EndNamespaceDeclHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">EndNamespaceDeclHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">prefix</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.EndNamespaceDeclHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called when the closing tag is reached for an element  that contained a
namespace declaration.  This is called once for each namespace declaration on
the element in the reverse of the order for which the
<a class="reference internal" href="#xml.parsers.expat.xmlparser.StartNamespaceDeclHandler" title="xml.parsers.expat.xmlparser.StartNamespaceDeclHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">StartNamespaceDeclHandler</span></code></a> was called to indicate the start of each
namespace declaration’s scope.  Calls to this handler are made after the
corresponding <a class="reference internal" href="#xml.parsers.expat.xmlparser.EndElementHandler" title="xml.parsers.expat.xmlparser.EndElementHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EndElementHandler</span></code></a> for the end of the element.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.CommentHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">CommentHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.CommentHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for comments.  <em>data</em> is the text of the comment, excluding the leading
<code class="docutils literal notranslate"><span class="pre">'&lt;!-</span></code><code class="docutils literal notranslate"><span class="pre">-'</span></code> and trailing <code class="docutils literal notranslate"><span class="pre">'-</span></code><code class="docutils literal notranslate"><span class="pre">-&gt;'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.StartCdataSectionHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">StartCdataSectionHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.StartCdataSectionHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called at the start of a CDATA section.  This and <a class="reference internal" href="#xml.parsers.expat.xmlparser.EndCdataSectionHandler" title="xml.parsers.expat.xmlparser.EndCdataSectionHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">EndCdataSectionHandler</span></code></a>
are needed to be able to identify the syntactical start and end for CDATA
sections.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.EndCdataSectionHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">EndCdataSectionHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.EndCdataSectionHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called at the end of a CDATA section.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.DefaultHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">DefaultHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.DefaultHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for any characters in the XML document for which no applicable handler
has been specified.  This means characters that are part of a construct which
could be reported, but for which no handler has been supplied.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.DefaultHandlerExpand">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">DefaultHandlerExpand</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.DefaultHandlerExpand" title="Link to this definition">¶</a></dt>
<dd><p>This is the same as the <a class="reference internal" href="#xml.parsers.expat.xmlparser.DefaultHandler" title="xml.parsers.expat.xmlparser.DefaultHandler"><code class="xref py py-func docutils literal notranslate"><span class="pre">DefaultHandler()</span></code></a>,  but doesn’t inhibit expansion
of internal entities. The entity reference will not be passed to the default
handler.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.NotStandaloneHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">NotStandaloneHandler</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.NotStandaloneHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called if the XML document hasn’t been declared as being a standalone document.
This happens when there is an external subset or a reference to a parameter
entity, but the XML declaration does not set standalone to <code class="docutils literal notranslate"><span class="pre">yes</span></code> in an XML
declaration.  If this handler returns <code class="docutils literal notranslate"><span class="pre">0</span></code>, then the parser will raise an
<code class="xref py py-const docutils literal notranslate"><span class="pre">XML_ERROR_NOT_STANDALONE</span></code> error.  If this handler is not set, no
exception is raised by the parser for this condition.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="xml.parsers.expat.xmlparser.ExternalEntityRefHandler">
<span class="sig-prename descclassname"><span class="pre">xmlparser.</span></span><span class="sig-name descname"><span class="pre">ExternalEntityRefHandler</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">context</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">base</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">systemId</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">publicId</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#xml.parsers.expat.xmlparser.ExternalEntityRefHandler" title="Link to this definition">¶</a></dt>
<dd><p>Called for references to external entities.  <em>base</em> is the current base, as set
by a previous call to <a class="reference internal" href="#xml.parsers.expat.xmlparser.SetBase" title="xml.parsers.expat.xmlparser.SetBase"><code class="xref py py-meth docutils literal notranslate"><span class="pre">SetBase()</span></code></a>.  The public and system identifiers,
<em>systemId</em> and <em>publicId</em>, are strings if given; if the public identifier is not
given, <em>publicId</em> will be <code class="docutils literal notranslate"><span class="pre">None</span></code>.  The <em>context</em> value is opaque and should
only be used as described below.</p>
<p>For external entities to be parsed, this handler must be implemented. It is
responsible for creating the sub-parser using
<code class="docutils literal notranslate"><span class="pre">ExternalEntityParserCreate(context)</span></code>, initializing it with the appropriate
callbacks, and parsing the entity.  This handler should return an integer; if it
returns <code class="docutils literal notranslate"><span class="pre">0</span></code>, the parser will raise an
<code class="xref py py-const docutils literal notranslate"><span class="pre">XML_ERROR_EXTERNAL_ENTITY_HANDLING</span></code> error, otherwise parsing will
continue.</p>
<p>If this handler is not provided, external entities are reported by the
<a class="reference internal" href="#xml.parsers.expat.xmlparser.DefaultHandler" title="xml.parsers.expat.xmlparser.DefaultHandler"><code class="xref py py-attr docutils literal notranslate"><span class="pre">DefaultHandler</span></code></a> callback, if provided.</p>
</dd></dl>

</section>
<section id="expaterror-exceptions">
<span id="expaterror-objects"></span><h2>ExpatError Exceptions<a class="headerlink" href="#expaterror-exceptions" title="Link to this heading">¶</a></h2>
<p><a class="reference internal" href="#xml.parsers.expat.ExpatError" title="xml.parsers.expat.ExpatError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ExpatError</span></code></a> exceptions have a number of interesting attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.ExpatError.code">
<span class="sig-prename descclassname"><span class="pre">ExpatError.</span></span><span class="sig-name descname"><span class="pre">code</span></span><a class="headerlink" href="#xml.parsers.expat.ExpatError.code" title="Link to this definition">¶</a></dt>
<dd><p>Expat’s internal error number for the specific error.  The
<a class="reference internal" href="#xml.parsers.expat.errors.messages" title="xml.parsers.expat.errors.messages"><code class="xref py py-data docutils literal notranslate"><span class="pre">errors.messages</span></code></a> dictionary maps
these error numbers to Expat’s error messages.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">xml.parsers.expat</span> <span class="kn">import</span> <span class="n">ParserCreate</span><span class="p">,</span> <span class="n">ExpatError</span><span class="p">,</span> <span class="n">errors</span>

<span class="n">p</span> <span class="o">=</span> <span class="n">ParserCreate</span><span class="p">()</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">p</span><span class="o">.</span><span class="n">Parse</span><span class="p">(</span><span class="n">some_xml_document</span><span class="p">)</span>
<span class="k">except</span> <span class="n">ExpatError</span> <span class="k">as</span> <span class="n">err</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Error:&quot;</span><span class="p">,</span> <span class="n">errors</span><span class="o">.</span><span class="n">messages</span><span class="p">[</span><span class="n">err</span><span class="o">.</span><span class="n">code</span><span class="p">])</span>
</pre></div>
</div>
<p>The <a class="reference internal" href="#module-xml.parsers.expat.errors" title="xml.parsers.expat.errors"><code class="xref py py-mod docutils literal notranslate"><span class="pre">errors</span></code></a> module also provides error message
constants and a dictionary <a class="reference internal" href="#xml.parsers.expat.errors.codes" title="xml.parsers.expat.errors.codes"><code class="xref py py-data docutils literal notranslate"><span class="pre">codes</span></code></a> mapping
these messages back to the error codes, see below.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.ExpatError.lineno">
<span class="sig-prename descclassname"><span class="pre">ExpatError.</span></span><span class="sig-name descname"><span class="pre">lineno</span></span><a class="headerlink" href="#xml.parsers.expat.ExpatError.lineno" title="Link to this definition">¶</a></dt>
<dd><p>Line number on which the error was detected.  The first line is numbered <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="xml.parsers.expat.ExpatError.offset">
<span class="sig-prename descclassname"><span class="pre">ExpatError.</span></span><span class="sig-name descname"><span class="pre">offset</span></span><a class="headerlink" href="#xml.parsers.expat.ExpatError.offset" title="Link to this definition">¶</a></dt>
<dd><p>Character offset into the line where the error occurred.  The first column is
numbered <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

</section>
<section id="example">
<span id="expat-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>The following program defines three handlers that just print out their
arguments.</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">xml.parsers.expat</span>

<span class="c1"># 3 handler functions</span>
<span class="k">def</span> <span class="nf">start_element</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">attrs</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Start element:&#39;</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">attrs</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">end_element</span><span class="p">(</span><span class="n">name</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;End element:&#39;</span><span class="p">,</span> <span class="n">name</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">char_data</span><span class="p">(</span><span class="n">data</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Character data:&#39;</span><span class="p">,</span> <span class="nb">repr</span><span class="p">(</span><span class="n">data</span><span class="p">))</span>

<span class="n">p</span> <span class="o">=</span> <span class="n">xml</span><span class="o">.</span><span class="n">parsers</span><span class="o">.</span><span class="n">expat</span><span class="o">.</span><span class="n">ParserCreate</span><span class="p">()</span>

<span class="n">p</span><span class="o">.</span><span class="n">StartElementHandler</span> <span class="o">=</span> <span class="n">start_element</span>
<span class="n">p</span><span class="o">.</span><span class="n">EndElementHandler</span> <span class="o">=</span> <span class="n">end_element</span>
<span class="n">p</span><span class="o">.</span><span class="n">CharacterDataHandler</span> <span class="o">=</span> <span class="n">char_data</span>

<span class="n">p</span><span class="o">.</span><span class="n">Parse</span><span class="p">(</span><span class="s2">&quot;&quot;&quot;&lt;?xml version=&quot;1.0&quot;?&gt;</span>
<span class="s2">&lt;parent id=&quot;top&quot;&gt;&lt;child1 name=&quot;paul&quot;&gt;Text goes here&lt;/child1&gt;</span>
<span class="s2">&lt;child2 name=&quot;fred&quot;&gt;More text&lt;/child2&gt;</span>
<span class="s2">&lt;/parent&gt;&quot;&quot;&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>The output from this program is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Start</span> <span class="n">element</span><span class="p">:</span> <span class="n">parent</span> <span class="p">{</span><span class="s1">&#39;id&#39;</span><span class="p">:</span> <span class="s1">&#39;top&#39;</span><span class="p">}</span>
<span class="n">Start</span> <span class="n">element</span><span class="p">:</span> <span class="n">child1</span> <span class="p">{</span><span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;paul&#39;</span><span class="p">}</span>
<span class="n">Character</span> <span class="n">data</span><span class="p">:</span> <span class="s1">&#39;Text goes here&#39;</span>
<span class="n">End</span> <span class="n">element</span><span class="p">:</span> <span class="n">child1</span>
<span class="n">Character</span> <span class="n">data</span><span class="p">:</span> <span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span>
<span class="n">Start</span> <span class="n">element</span><span class="p">:</span> <span class="n">child2</span> <span class="p">{</span><span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="s1">&#39;fred&#39;</span><span class="p">}</span>
<span class="n">Character</span> <span class="n">data</span><span class="p">:</span> <span class="s1">&#39;More text&#39;</span>
<span class="n">End</span> <span class="n">element</span><span class="p">:</span> <span class="n">child2</span>
<span class="n">Character</span> <span class="n">data</span><span class="p">:</span> <span class="s1">&#39;</span><span class="se">\n</span><span class="s1">&#39;</span>
<span class="n">End</span> <span class="n">element</span><span class="p">:</span> <span class="n">parent</span>
</pre></div>
</div>
</section>
<section id="module-xml.parsers.expat.model">
<span id="content-model-descriptions"></span><span id="expat-content-models"></span><h2>Content Model Descriptions<a class="headerlink" href="#module-xml.parsers.expat.model" title="Link to this heading">¶</a></h2>
<p>Content models are described using nested tuples.  Each tuple contains four
values: the type, the quantifier, the name, and a tuple of children.  Children
are simply additional content model descriptions.</p>
<p>The values of the first two fields are constants defined in the
<a class="reference internal" href="#module-xml.parsers.expat.model" title="xml.parsers.expat.model"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat.model</span></code></a> module.  These constants can be collected in two
groups: the model type group and the quantifier group.</p>
<p>The constants in the model type group are:</p>
<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_ANY</span></span></dt>
<dd><p>The element named by the model name was declared to have a content model of
<code class="docutils literal notranslate"><span class="pre">ANY</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_CHOICE</span></span></dt>
<dd><p>The named element allows a choice from a number of options; this is used for
content models such as <code class="docutils literal notranslate"><span class="pre">(A</span> <span class="pre">|</span> <span class="pre">B</span> <span class="pre">|</span> <span class="pre">C)</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_EMPTY</span></span></dt>
<dd><p>Elements which are declared to be <code class="docutils literal notranslate"><span class="pre">EMPTY</span></code> have this model type.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_MIXED</span></span></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_NAME</span></span></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CTYPE_SEQ</span></span></dt>
<dd><p>Models which represent a series of models which follow one after the other are
indicated with this model type.  This is used for models such as <code class="docutils literal notranslate"><span class="pre">(A,</span> <span class="pre">B,</span> <span class="pre">C)</span></code>.</p>
</dd></dl>

<p>The constants in the quantifier group are:</p>
<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CQUANT_NONE</span></span></dt>
<dd><p>No modifier is given, so it can appear exactly once, as for <code class="docutils literal notranslate"><span class="pre">A</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CQUANT_OPT</span></span></dt>
<dd><p>The model is optional: it can appear once or not at all, as for <code class="docutils literal notranslate"><span class="pre">A?</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CQUANT_PLUS</span></span></dt>
<dd><p>The model must occur one or more times (like <code class="docutils literal notranslate"><span class="pre">A+</span></code>).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.model.</span></span><span class="sig-name descname"><span class="pre">XML_CQUANT_REP</span></span></dt>
<dd><p>The model must occur zero or more times, as for <code class="docutils literal notranslate"><span class="pre">A*</span></code>.</p>
</dd></dl>

</section>
<section id="module-xml.parsers.expat.errors">
<span id="expat-error-constants"></span><span id="expat-errors"></span><h2>Expat error constants<a class="headerlink" href="#module-xml.parsers.expat.errors" title="Link to this heading">¶</a></h2>
<p>The following constants are provided in the <a class="reference internal" href="#module-xml.parsers.expat.errors" title="xml.parsers.expat.errors"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat.errors</span></code></a>
module.  These constants are useful in interpreting some of the attributes of
the <code class="xref py py-exc docutils literal notranslate"><span class="pre">ExpatError</span></code> exception objects raised when an error has occurred.
Since for backwards compatibility reasons, the constants’ value is the error
<em>message</em> and not the numeric error <em>code</em>, you do this by comparing its
<a class="reference internal" href="code.html#module-code" title="code: Facilities to implement read-eval-print loops."><code class="xref py py-attr docutils literal notranslate"><span class="pre">code</span></code></a> attribute with
<code class="samp docutils literal notranslate"><span class="pre">errors.codes[errors.XML_ERROR_</span><em><span class="pre">CONSTANT_NAME</span></em><span class="pre">]</span></code>.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">errors</span></code> module has the following attributes:</p>
<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.codes">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">codes</span></span><a class="headerlink" href="#xml.parsers.expat.errors.codes" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping string descriptions to their error codes.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.messages">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">messages</span></span><a class="headerlink" href="#xml.parsers.expat.errors.messages" title="Link to this definition">¶</a></dt>
<dd><p>A dictionary mapping numeric error codes to their string descriptions.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_ASYNC_ENTITY">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_ASYNC_ENTITY</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_ASYNC_ENTITY" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_ATTRIBUTE_EXTERNAL_ENTITY_REF">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_ATTRIBUTE_EXTERNAL_ENTITY_REF</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_ATTRIBUTE_EXTERNAL_ENTITY_REF" title="Link to this definition">¶</a></dt>
<dd><p>An entity reference in an attribute value referred to an external entity instead
of an internal entity.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_BAD_CHAR_REF">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_BAD_CHAR_REF</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_BAD_CHAR_REF" title="Link to this definition">¶</a></dt>
<dd><p>A character reference referred to a character which is illegal in XML (for
example, character <code class="docutils literal notranslate"><span class="pre">0</span></code>, or ‘<code class="docutils literal notranslate"><span class="pre">&amp;#0;</span></code>’).</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_BINARY_ENTITY_REF">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_BINARY_ENTITY_REF</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_BINARY_ENTITY_REF" title="Link to this definition">¶</a></dt>
<dd><p>An entity reference referred to an entity which was declared with a notation, so
cannot be parsed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_DUPLICATE_ATTRIBUTE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_DUPLICATE_ATTRIBUTE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_DUPLICATE_ATTRIBUTE" title="Link to this definition">¶</a></dt>
<dd><p>An attribute was used more than once in a start tag.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_INCORRECT_ENCODING">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_INCORRECT_ENCODING</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_INCORRECT_ENCODING" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_INVALID_TOKEN">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_INVALID_TOKEN</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_INVALID_TOKEN" title="Link to this definition">¶</a></dt>
<dd><p>Raised when an input byte could not properly be assigned to a character; for
example, a NUL byte (value <code class="docutils literal notranslate"><span class="pre">0</span></code>) in a UTF-8 input stream.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_JUNK_AFTER_DOC_ELEMENT">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_JUNK_AFTER_DOC_ELEMENT</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_JUNK_AFTER_DOC_ELEMENT" title="Link to this definition">¶</a></dt>
<dd><p>Something other than whitespace occurred after the document element.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_MISPLACED_XML_PI">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_MISPLACED_XML_PI</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_MISPLACED_XML_PI" title="Link to this definition">¶</a></dt>
<dd><p>An XML declaration was found somewhere other than the start of the input data.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_NO_ELEMENTS">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_NO_ELEMENTS</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_NO_ELEMENTS" title="Link to this definition">¶</a></dt>
<dd><p>The document contains no elements (XML requires all documents to contain exactly
one top-level element)..</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_NO_MEMORY">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_NO_MEMORY</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_NO_MEMORY" title="Link to this definition">¶</a></dt>
<dd><p>Expat was not able to allocate memory internally.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_PARAM_ENTITY_REF">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_PARAM_ENTITY_REF</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_PARAM_ENTITY_REF" title="Link to this definition">¶</a></dt>
<dd><p>A parameter entity reference was found where it was not allowed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_PARTIAL_CHAR">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_PARTIAL_CHAR</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_PARTIAL_CHAR" title="Link to this definition">¶</a></dt>
<dd><p>An incomplete character was found in the input.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_RECURSIVE_ENTITY_REF">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_RECURSIVE_ENTITY_REF</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_RECURSIVE_ENTITY_REF" title="Link to this definition">¶</a></dt>
<dd><p>An entity reference contained another reference to the same entity; possibly via
a different name, and possibly indirectly.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_SYNTAX">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_SYNTAX</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_SYNTAX" title="Link to this definition">¶</a></dt>
<dd><p>Some unspecified syntax error was encountered.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_TAG_MISMATCH">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_TAG_MISMATCH</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_TAG_MISMATCH" title="Link to this definition">¶</a></dt>
<dd><p>An end tag did not match the innermost open start tag.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNCLOSED_TOKEN">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNCLOSED_TOKEN</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNCLOSED_TOKEN" title="Link to this definition">¶</a></dt>
<dd><p>Some token (such as a start tag) was not closed before the end of the stream or
the next token was encountered.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNDEFINED_ENTITY">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNDEFINED_ENTITY</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNDEFINED_ENTITY" title="Link to this definition">¶</a></dt>
<dd><p>A reference was made to an entity which was not defined.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNKNOWN_ENCODING">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNKNOWN_ENCODING</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNKNOWN_ENCODING" title="Link to this definition">¶</a></dt>
<dd><p>The document encoding is not supported by Expat.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNCLOSED_CDATA_SECTION">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNCLOSED_CDATA_SECTION</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNCLOSED_CDATA_SECTION" title="Link to this definition">¶</a></dt>
<dd><p>A CDATA marked section was not closed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_EXTERNAL_ENTITY_HANDLING">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_EXTERNAL_ENTITY_HANDLING</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_EXTERNAL_ENTITY_HANDLING" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_NOT_STANDALONE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_NOT_STANDALONE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_NOT_STANDALONE" title="Link to this definition">¶</a></dt>
<dd><p>The parser determined that the document was not “standalone” though it declared
itself to be in the XML declaration, and the <code class="xref py py-attr docutils literal notranslate"><span class="pre">NotStandaloneHandler</span></code> was
set and returned <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNEXPECTED_STATE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNEXPECTED_STATE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNEXPECTED_STATE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_ENTITY_DECLARED_IN_PE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_ENTITY_DECLARED_IN_PE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_ENTITY_DECLARED_IN_PE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_FEATURE_REQUIRES_XML_DTD">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_FEATURE_REQUIRES_XML_DTD</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_FEATURE_REQUIRES_XML_DTD" title="Link to this definition">¶</a></dt>
<dd><p>An operation was requested that requires DTD support to be compiled in, but
Expat was configured without DTD support.  This should never be reported by a
standard build of the <a class="reference internal" href="#module-xml.parsers.expat" title="xml.parsers.expat: An interface to the Expat non-validating XML parser."><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code></a> module.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_CANT_CHANGE_FEATURE_ONCE_PARSING">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_CANT_CHANGE_FEATURE_ONCE_PARSING</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_CANT_CHANGE_FEATURE_ONCE_PARSING" title="Link to this definition">¶</a></dt>
<dd><p>A behavioral change was requested after parsing started that can only be changed
before parsing has started.  This is (currently) only raised by
<code class="xref py py-meth docutils literal notranslate"><span class="pre">UseForeignDTD()</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNBOUND_PREFIX">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNBOUND_PREFIX</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNBOUND_PREFIX" title="Link to this definition">¶</a></dt>
<dd><p>An undeclared prefix was found when namespace processing was enabled.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_UNDECLARING_PREFIX">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_UNDECLARING_PREFIX</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_UNDECLARING_PREFIX" title="Link to this definition">¶</a></dt>
<dd><p>The document attempted to remove the namespace declaration associated with a
prefix.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_INCOMPLETE_PE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_INCOMPLETE_PE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_INCOMPLETE_PE" title="Link to this definition">¶</a></dt>
<dd><p>A parameter entity contained incomplete markup.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_XML_DECL">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_XML_DECL</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_XML_DECL" title="Link to this definition">¶</a></dt>
<dd><p>The document contained no document element at all.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_TEXT_DECL">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_TEXT_DECL</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_TEXT_DECL" title="Link to this definition">¶</a></dt>
<dd><p>There was an error parsing a text declaration in an external entity.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_PUBLICID">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_PUBLICID</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_PUBLICID" title="Link to this definition">¶</a></dt>
<dd><p>Characters were found in the public id that are not allowed.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_SUSPENDED">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_SUSPENDED</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_SUSPENDED" title="Link to this definition">¶</a></dt>
<dd><p>The requested operation was made on a suspended parser, but isn’t allowed.  This
includes attempts to provide additional input or to stop the parser.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_NOT_SUSPENDED">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_NOT_SUSPENDED</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_NOT_SUSPENDED" title="Link to this definition">¶</a></dt>
<dd><p>An attempt to resume the parser was made when the parser had not been suspended.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_ABORTED">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_ABORTED</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_ABORTED" title="Link to this definition">¶</a></dt>
<dd><p>This should not be reported to Python applications.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_FINISHED">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_FINISHED</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_FINISHED" title="Link to this definition">¶</a></dt>
<dd><p>The requested operation was made on a parser which was finished parsing input,
but isn’t allowed.  This includes attempts to provide additional input or to
stop the parser.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_SUSPEND_PE">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_SUSPEND_PE</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_SUSPEND_PE" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_RESERVED_PREFIX_XML">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_RESERVED_PREFIX_XML</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_RESERVED_PREFIX_XML" title="Link to this definition">¶</a></dt>
<dd><p>An attempt was made to
undeclare reserved namespace prefix <code class="docutils literal notranslate"><span class="pre">xml</span></code>
or to bind it to another namespace URI.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_RESERVED_PREFIX_XMLNS">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_RESERVED_PREFIX_XMLNS</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_RESERVED_PREFIX_XMLNS" title="Link to this definition">¶</a></dt>
<dd><p>An attempt was made to declare or undeclare reserved namespace prefix <code class="docutils literal notranslate"><span class="pre">xmlns</span></code>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_RESERVED_NAMESPACE_URI">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_RESERVED_NAMESPACE_URI</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_RESERVED_NAMESPACE_URI" title="Link to this definition">¶</a></dt>
<dd><p>An attempt was made to bind the URI of one the reserved namespace
prefixes <code class="docutils literal notranslate"><span class="pre">xml</span></code> and <code class="docutils literal notranslate"><span class="pre">xmlns</span></code> to another namespace prefix.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_INVALID_ARGUMENT">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_INVALID_ARGUMENT</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_INVALID_ARGUMENT" title="Link to this definition">¶</a></dt>
<dd><p>This should not be reported to Python applications.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_NO_BUFFER">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_NO_BUFFER</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_NO_BUFFER" title="Link to this definition">¶</a></dt>
<dd><p>This should not be reported to Python applications.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="xml.parsers.expat.errors.XML_ERROR_AMPLIFICATION_LIMIT_BREACH">
<span class="sig-prename descclassname"><span class="pre">xml.parsers.expat.errors.</span></span><span class="sig-name descname"><span class="pre">XML_ERROR_AMPLIFICATION_LIMIT_BREACH</span></span><a class="headerlink" href="#xml.parsers.expat.errors.XML_ERROR_AMPLIFICATION_LIMIT_BREACH" title="Link to this definition">¶</a></dt>
<dd><p>The limit on input amplification factor (from DTD and entities)
has been breached.</p>
</dd></dl>

<p class="rubric">Footnotes</p>
<aside class="footnote-list brackets">
<aside class="footnote brackets" id="id3" role="doc-footnote">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id1">1</a><span class="fn-bracket">]</span></span>
<p>The encoding string included in XML output should conform to the
appropriate standards. For example, “UTF-8” is valid, but “UTF8” is
not. See <a class="reference external" href="https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl">https://www.w3.org/TR/2006/REC-xml11-20060816/#NT-EncodingDecl</a>
and <a class="reference external" href="https://www.iana.org/assignments/character-sets/character-sets.xhtml">https://www.iana.org/assignments/character-sets/character-sets.xhtml</a>.</p>
</aside>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a><ul>
<li><a class="reference internal" href="#xmlparser-objects">XMLParser Objects</a></li>
<li><a class="reference internal" href="#expaterror-exceptions">ExpatError Exceptions</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#module-xml.parsers.expat.model">Content Model Descriptions</a></li>
<li><a class="reference internal" href="#module-xml.parsers.expat.errors">Expat error constants</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="xml.sax.reader.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.sax.xmlreader</span></code> — Interface for XML parsers</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="internet.html"
                          title="next chapter">Internet Protocols and Support</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/pyexpat.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="internet.html" title="Internet Protocols and Support"
             >next</a> |</li>
        <li class="right" >
          <a href="xml.sax.reader.html" title="xml.sax.xmlreader — Interface for XML parsers"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="markup.html" >Structured Markup Processing Tools</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">xml.parsers.expat</span></code> — Fast XML parsing using Expat</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>