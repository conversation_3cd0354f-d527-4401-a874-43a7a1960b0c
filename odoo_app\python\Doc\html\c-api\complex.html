<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Complex Number Objects" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/complex.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Python’s complex number objects are implemented as two distinct types when viewed from the C API: one is the Python object exposed to Python programs, and the other is a C structure which represent..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Python’s complex number objects are implemented as two distinct types when viewed from the C API: one is the Python object exposed to Python programs, and the other is a C structure which represent..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Complex Number Objects &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Bytes Objects" href="bytes.html" />
    <link rel="prev" title="Floating Point Objects" href="float.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/complex.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Complex Number Objects</a><ul>
<li><a class="reference internal" href="#complex-numbers-as-c-structures">Complex Numbers as C Structures</a></li>
<li><a class="reference internal" href="#complex-numbers-as-python-objects">Complex Numbers as Python Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="float.html"
                          title="previous chapter">Floating Point Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="bytes.html"
                          title="next chapter">Bytes Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/complex.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="bytes.html" title="Bytes Objects"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="float.html" title="Floating Point Objects"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" accesskey="U">Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Complex Number Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="complex-number-objects">
<span id="complexobjects"></span><h1>Complex Number Objects<a class="headerlink" href="#complex-number-objects" title="Link to this heading">¶</a></h1>
<p id="index-0">Python’s complex number objects are implemented as two distinct types when
viewed from the C API:  one is the Python object exposed to Python programs, and
the other is a C structure which represents the actual complex number value.
The API provides functions for working with both.</p>
<section id="complex-numbers-as-c-structures">
<h2>Complex Numbers as C Structures<a class="headerlink" href="#complex-numbers-as-c-structures" title="Link to this heading">¶</a></h2>
<p>Note that the functions which accept these structures as parameters and return
them as results do so <em>by value</em> rather than dereferencing them through
pointers.  This is consistent throughout the API.</p>
<dl class="c type">
<dt class="sig sig-object c" id="c.Py_complex">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">Py_complex</span></span></span><a class="headerlink" href="#c.Py_complex" title="Link to this definition">¶</a><br /></dt>
<dd><p>The C structure which corresponds to the value portion of a Python complex
number object.  Most of the functions for dealing with complex number objects
use structures of this type as input or output values, as appropriate.  It is
defined as:</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="k">typedef</span><span class="w"> </span><span class="k">struct</span><span class="w"> </span><span class="p">{</span>
<span class="w">   </span><span class="kt">double</span><span class="w"> </span><span class="n">real</span><span class="p">;</span>
<span class="w">   </span><span class="kt">double</span><span class="w"> </span><span class="n">imag</span><span class="p">;</span>
<span class="p">}</span><span class="w"> </span><span class="n">Py_complex</span><span class="p">;</span>
</pre></div>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_sum">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_sum</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">left</span></span>, <a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">right</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_sum" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the sum of two complex numbers, using the C <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a>
representation.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_diff">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_diff</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">left</span></span>, <a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">right</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_diff" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the difference between two complex numbers, using the C
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a> representation.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_neg">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_neg</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">num</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_neg" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the negation of the complex number <em>num</em>, using the C
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a> representation.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_prod">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_prod</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">left</span></span>, <a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">right</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_prod" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the product of two complex numbers, using the C <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a>
representation.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_quot">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_quot</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">dividend</span></span>, <a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">divisor</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_quot" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the quotient of two complex numbers, using the C <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a>
representation.</p>
<p>If <em>divisor</em> is null, this method returns zero and sets
<code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> to <code class="xref c c-macro docutils literal notranslate"><span class="pre">EDOM</span></code>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c._Py_c_pow">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">_Py_c_pow</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">num</span></span>, <a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">exp</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c._Py_c_pow" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the exponentiation of <em>num</em> by <em>exp</em>, using the C <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a>
representation.</p>
<p>If <em>num</em> is null and <em>exp</em> is not a positive real number,
this method returns zero and sets <code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> to <code class="xref c c-macro docutils literal notranslate"><span class="pre">EDOM</span></code>.</p>
</dd></dl>

</section>
<section id="complex-numbers-as-python-objects">
<h2>Complex Numbers as Python Objects<a class="headerlink" href="#complex-numbers-as-python-objects" title="Link to this heading">¶</a></h2>
<dl class="c type">
<dt class="sig sig-object c" id="c.PyComplexObject">
<span class="k"><span class="pre">type</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplexObject</span></span></span><a class="headerlink" href="#c.PyComplexObject" title="Link to this definition">¶</a><br /></dt>
<dd><p>This subtype of <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyObject</span></code></a> represents a Python complex number object.</p>
</dd></dl>

<dl class="c var">
<dt class="sig sig-object c" id="c.PyComplex_Type">
<a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><span class="n"><span class="pre">PyTypeObject</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_Type</span></span></span><a class="headerlink" href="#c.PyComplex_Type" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>This instance of <a class="reference internal" href="type.html#c.PyTypeObject" title="PyTypeObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyTypeObject</span></code></a> represents the Python complex number
type. It is the same object as <a class="reference internal" href="../library/functions.html#complex" title="complex"><code class="xref py py-class docutils literal notranslate"><span class="pre">complex</span></code></a> in the Python layer.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_Check">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_Check</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_Check" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if its argument is a <a class="reference internal" href="#c.PyComplexObject" title="PyComplexObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyComplexObject</span></code></a> or a subtype of
<a class="reference internal" href="#c.PyComplexObject" title="PyComplexObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyComplexObject</span></code></a>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_CheckExact">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_CheckExact</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">p</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_CheckExact" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return true if its argument is a <a class="reference internal" href="#c.PyComplexObject" title="PyComplexObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyComplexObject</span></code></a>, but not a subtype of
<a class="reference internal" href="#c.PyComplexObject" title="PyComplexObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyComplexObject</span></code></a>.  This function always succeeds.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_FromCComplex">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_FromCComplex</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="n"><span class="pre">v</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_FromCComplex" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><p>Create a new Python complex number object from a C <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a> value.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_FromDoubles">
<a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_FromDoubles</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">real</span></span>, <span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">imag</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_FromDoubles" title="Link to this definition">¶</a><br /></dt>
<dd><em class="refcount">Return value: New reference.</em><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return a new <a class="reference internal" href="#c.PyComplexObject" title="PyComplexObject"><code class="xref c c-type docutils literal notranslate"><span class="pre">PyComplexObject</span></code></a> object from <em>real</em> and <em>imag</em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_RealAsDouble">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_RealAsDouble</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_RealAsDouble" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the real part of <em>op</em> as a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_ImagAsDouble">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_ImagAsDouble</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_ImagAsDouble" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Return the imaginary part of <em>op</em> as a C <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyComplex_AsCComplex">
<a class="reference internal" href="#c.Py_complex" title="Py_complex"><span class="n"><span class="pre">Py_complex</span></span></a><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyComplex_AsCComplex</span></span></span><span class="sig-paren">(</span><a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">op</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyComplex_AsCComplex" title="Link to this definition">¶</a><br /></dt>
<dd><p>Return the <a class="reference internal" href="#c.Py_complex" title="Py_complex"><code class="xref c c-type docutils literal notranslate"><span class="pre">Py_complex</span></code></a> value of the complex number <em>op</em>.</p>
<p>If <em>op</em> is not a Python complex number object but has a <a class="reference internal" href="../reference/datamodel.html#object.__complex__" title="object.__complex__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__complex__()</span></code></a>
method, this method will first be called to convert <em>op</em> to a Python complex
number object.  If <code class="xref py py-meth docutils literal notranslate"><span class="pre">__complex__()</span></code> is not defined then it falls back to
<a class="reference internal" href="../reference/datamodel.html#object.__float__" title="object.__float__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code></a>.  If <code class="xref py py-meth docutils literal notranslate"><span class="pre">__float__()</span></code> is not defined then it falls back
to <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a>.  Upon failure, this method returns <code class="docutils literal notranslate"><span class="pre">-1.0</span></code> as a real
value.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>Use <a class="reference internal" href="../reference/datamodel.html#object.__index__" title="object.__index__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__index__()</span></code></a> if available.</p>
</div>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Complex Number Objects</a><ul>
<li><a class="reference internal" href="#complex-numbers-as-c-structures">Complex Numbers as C Structures</a></li>
<li><a class="reference internal" href="#complex-numbers-as-python-objects">Complex Numbers as Python Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="float.html"
                          title="previous chapter">Floating Point Objects</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="bytes.html"
                          title="next chapter">Bytes Objects</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/complex.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="bytes.html" title="Bytes Objects"
             >next</a> |</li>
        <li class="right" >
          <a href="float.html" title="Floating Point Objects"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="concrete.html" >Concrete Objects Layer</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Complex Number Objects</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>