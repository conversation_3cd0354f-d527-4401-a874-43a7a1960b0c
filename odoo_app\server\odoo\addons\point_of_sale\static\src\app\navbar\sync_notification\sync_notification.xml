<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.SyncNotification">
        <div class="oe_status btn btn-light rounded-0" t-on-click="onClick">
            <span t-if="sync.pending" class="js_msg">
                <t t-esc="sync.pending" />
                <span> </span>
            </span>
            <div t-if="sync.status === 'connected'" class="js_connected oe_icon text-success">
                <i class="fa fa-fw fa-wifi" role="img" aria-label="Synchronisation Connected"
                   title="Synchronisation Connected"></i>
            </div>
            <div t-if="sync.status === 'connecting'" class="js_connecting oe_icon text-white">
                <i class="fa fa-fw fa-spin fa-circle-o-notch" role="img"
                   aria-label="Synchronisation Connecting" title="Synchronisation Connecting"></i>
            </div>
            <div t-if="sync.status === 'disconnected'" class="js_disconnected oe_icon text-warning">
                <i class="fa fa-fw fa-wifi" role="img" aria-label="Synchronisation Disconnected"
                   title="Synchronisation Disconnected"></i>
            </div>
            <div t-if="sync.status === 'error'" class="js_error oe_icon text-danger">
                <i class="fa fa-fw fa-warning" role="img" aria-label="Synchronisation Error"
                   title="Synchronisation Error"></i>
            </div>
        </div>
    </t>

</templates>
