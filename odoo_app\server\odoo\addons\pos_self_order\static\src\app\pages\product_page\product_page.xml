<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_self_order.ProductPage">
        <div class="d-flex flex-column bg-view flex-grow-1 h-100">
            <div class="d-flex align-items-center flex-shrink-0 justify-content-between gap-3 px-3 py-2 w-100 bg-view border-bottom overflow-x-auto z-index-1">
                <button class="btn btn-secondary btn-lg px-3 text-nowrap" t-on-click="back">
                    <i class="oi oi-chevron-left" aria-hidden="true"/><span class="ms-2 d-none d-md-inline">Discard</span>
                </button>
                <h1 class="mb-0 text-nowrap"><strong t-esc="product.name"/> options</h1>
                <span class="d-none d-md-inline px-5"/> <!-- Spacer -->
            </div>

            <div class="pos_self_order_product_page_content d-flex flex-column flex-grow-1 overflow-y-auto">
                <div class="o-so-product-details d-flex flex-row align-items-start p-3 gap-3">
                    <div class="o-so-product-details-image ratio ratio-1x1 w-25 flex-shrink-0">
                        <div class="placeholder-glow">
                            <div class="placeholder w-100 h-100 bg-300 rounded"/>
                        </div>
                        <img
                            class="o_self_order_item_card_image w-100 rounded"
                            t-attf-src="/menu/get-image/{{ props.product.id }}/512?unique={{props.product.write_date}}"
                            alt="Product image"
                            loading="lazy"
                            onerror="this.remove()"/>
                    </div>
                    <div class="o-so-product-details-description">
                        <h2 t-esc="product.name"/>
                        <small t-if="product.description_self_order"
                            class="o_self_order_main_desc d-block mb-3 text-muted"
                            t-out="product.description_self_order"
                        />
                        <span class="fs-3" t-esc="selfOrder.formatMonetary(selfOrder.getProductDisplayPrice(product))"/>
                    </div>
                </div>
                <AttributeSelection
                    t-if="this.product.attributes.length"
                    product="product"/>
            </div>

            <div t-if="showQtyButtons and selfOrder.ordering" class="p-3 text-end">
                <div class="o_self_order_incr_button btn-group " role="group" aria-label="Quantity select">
                    <button type="button"
                        t-on-click = "() => this.changeQuantity(false)"
                        t-attf-class="{{ !this.env.editable ? 'disabled' : '' }} btn btn-secondary btn-lg"><span class="fs-2 lh-1 fa-fw d-inline-block">－</span></button>
                    <div class="o-so-tabular-nums d-flex align-items-center px-3 text-bg-200" t-esc="state.qty"/>
                    <button type="button"
                        t-on-click = "() => this.changeQuantity(true)"
                        class="btn btn-secondary btn-lg"><span class="fs-2 lh-1 fa-fw d-inline-block">＋</span></button>
                </div>
            </div>

            <div t-if="showQtyButtons and !props.onValidate" class="page-buttons d-flex justify-content-end p-3 gap-3 bg-view border-top">
                <button t-if="showQtyButtons and !props.onValidate and selfOrder.ordering" class="btn btn-primary btn-lg" t-att-class="{ 'disabled': this.isEveryValueSelected() }" t-on-click="addToCart">Add to cart</button>
            </div>
        </div>
    </t>
</templates>
