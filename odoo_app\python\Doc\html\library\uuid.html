<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="uuid — UUID objects according to RFC 4122" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/uuid.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/uuid.py This module provides immutable UUID objects (the UUID class) and the functions uuid1(), uuid3(), uuid4(), uuid5() for generating version 1, 3, 4, and 5 UUIDs as specified i..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/uuid.py This module provides immutable UUID objects (the UUID class) and the functions uuid1(), uuid3(), uuid4(), uuid5() for generating version 1, 3, 4, and 5 UUIDs as specified i..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>uuid — UUID objects according to RFC 4122 &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="socketserver — A framework for network servers" href="socketserver.html" />
    <link rel="prev" title="smtplib — SMTP protocol client" href="smtplib.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/uuid.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a><ul>
<li><a class="reference internal" href="#command-line-usage">Command-Line Usage</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#command-line-example">Command-Line Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="smtplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="socketserver.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code> — A framework for network servers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/uuid.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="socketserver.html" title="socketserver — A framework for network servers"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="smtplib.html" title="smtplib — SMTP protocol client"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" accesskey="U">Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-uuid">
<span id="uuid-uuid-objects-according-to-rfc-4122"></span><h1><a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> — UUID objects according to <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a><a class="headerlink" href="#module-uuid" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/uuid.py">Lib/uuid.py</a></p>
<hr class="docutils" />
<p>This module provides immutable <a class="reference internal" href="#uuid.UUID" title="uuid.UUID"><code class="xref py py-class docutils literal notranslate"><span class="pre">UUID</span></code></a> objects (the <a class="reference internal" href="#uuid.UUID" title="uuid.UUID"><code class="xref py py-class docutils literal notranslate"><span class="pre">UUID</span></code></a> class)
and the functions <a class="reference internal" href="#uuid.uuid1" title="uuid.uuid1"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid1()</span></code></a>, <a class="reference internal" href="#uuid.uuid3" title="uuid.uuid3"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid3()</span></code></a>, <a class="reference internal" href="#uuid.uuid4" title="uuid.uuid4"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid4()</span></code></a>, <a class="reference internal" href="#uuid.uuid5" title="uuid.uuid5"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid5()</span></code></a> for
generating version 1, 3, 4, and 5 UUIDs as specified in <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>.</p>
<p>If all you want is a unique ID, you should probably call <a class="reference internal" href="#uuid.uuid1" title="uuid.uuid1"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid1()</span></code></a> or
<a class="reference internal" href="#uuid.uuid4" title="uuid.uuid4"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid4()</span></code></a>.  Note that <a class="reference internal" href="#uuid.uuid1" title="uuid.uuid1"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid1()</span></code></a> may compromise privacy since it creates
a UUID containing the computer’s network address.  <a class="reference internal" href="#uuid.uuid4" title="uuid.uuid4"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid4()</span></code></a> creates a
random UUID.</p>
<p>Depending on support from the underlying platform, <a class="reference internal" href="#uuid.uuid1" title="uuid.uuid1"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid1()</span></code></a> may or may
not return a “safe” UUID.  A safe UUID is one which is generated using
synchronization methods that ensure no two processes can obtain the same
UUID.  All instances of <a class="reference internal" href="#uuid.UUID" title="uuid.UUID"><code class="xref py py-class docutils literal notranslate"><span class="pre">UUID</span></code></a> have an <a class="reference internal" href="#uuid.UUID.is_safe" title="uuid.UUID.is_safe"><code class="xref py py-attr docutils literal notranslate"><span class="pre">is_safe</span></code></a> attribute
which relays any information about the UUID’s safety, using this enumeration:</p>
<dl class="py class">
<dt class="sig sig-object py" id="uuid.SafeUUID">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">SafeUUID</span></span><a class="headerlink" href="#uuid.SafeUUID" title="Link to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.SafeUUID.safe">
<span class="sig-name descname"><span class="pre">safe</span></span><a class="headerlink" href="#uuid.SafeUUID.safe" title="Link to this definition">¶</a></dt>
<dd><p>The UUID was generated by the platform in a multiprocessing-safe way.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.SafeUUID.unsafe">
<span class="sig-name descname"><span class="pre">unsafe</span></span><a class="headerlink" href="#uuid.SafeUUID.unsafe" title="Link to this definition">¶</a></dt>
<dd><p>The UUID was not generated in a multiprocessing-safe way.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.SafeUUID.unknown">
<span class="sig-name descname"><span class="pre">unknown</span></span><a class="headerlink" href="#uuid.SafeUUID.unknown" title="Link to this definition">¶</a></dt>
<dd><p>The platform does not provide information on whether the UUID was
generated safely or not.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="uuid.UUID">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">UUID</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hex</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bytes</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">bytes_le</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">fields</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">int</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">version</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="o"><span class="pre">*</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_safe</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">SafeUUID.unknown</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#uuid.UUID" title="Link to this definition">¶</a></dt>
<dd><p>Create a UUID from either a string of 32 hexadecimal digits, a string of 16
bytes in big-endian order as the <em>bytes</em> argument, a string of 16 bytes in
little-endian order as the <em>bytes_le</em> argument, a tuple of six integers
(32-bit <em>time_low</em>, 16-bit <em>time_mid</em>, 16-bit <em>time_hi_version</em>,
8-bit <em>clock_seq_hi_variant</em>, 8-bit <em>clock_seq_low</em>, 48-bit <em>node</em>) as the
<em>fields</em> argument, or a single 128-bit integer as the <em>int</em> argument.
When a string of hex digits is given, curly braces, hyphens,
and a URN prefix are all optional.  For example, these
expressions all yield the same UUID:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">UUID</span><span class="p">(</span><span class="s1">&#39;{12345678-1234-5678-1234-************}&#39;</span><span class="p">)</span>
<span class="n">UUID</span><span class="p">(</span><span class="s1">&#39;1234************1234************&#39;</span><span class="p">)</span>
<span class="n">UUID</span><span class="p">(</span><span class="s1">&#39;urn:uuid:12345678-1234-5678-1234-************&#39;</span><span class="p">)</span>
<span class="n">UUID</span><span class="p">(</span><span class="nb">bytes</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x12\x34\x56\x78</span><span class="s1">&#39;</span><span class="o">*</span><span class="mi">4</span><span class="p">)</span>
<span class="n">UUID</span><span class="p">(</span><span class="n">bytes_le</span><span class="o">=</span><span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x78\x56\x34\x12\x34\x12\x78\x56</span><span class="s1">&#39;</span> <span class="o">+</span>
              <span class="sa">b</span><span class="s1">&#39;</span><span class="se">\x12\x34\x56\x78\x12\x34\x56\x78</span><span class="s1">&#39;</span><span class="p">)</span>
<span class="n">UUID</span><span class="p">(</span><span class="n">fields</span><span class="o">=</span><span class="p">(</span><span class="mh">0x12345678</span><span class="p">,</span> <span class="mh">0x1234</span><span class="p">,</span> <span class="mh">0x5678</span><span class="p">,</span> <span class="mh">0x12</span><span class="p">,</span> <span class="mh">0x34</span><span class="p">,</span> <span class="mh">0x************</span><span class="p">))</span>
<span class="n">UUID</span><span class="p">(</span><span class="nb">int</span><span class="o">=</span><span class="mh">0x1234************1234************</span><span class="p">)</span>
</pre></div>
</div>
<p>Exactly one of <em>hex</em>, <em>bytes</em>, <em>bytes_le</em>, <em>fields</em>, or <em>int</em> must be given.
The <em>version</em> argument is optional; if given, the resulting UUID will have its
variant and version number set according to <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>, overriding bits in the
given <em>hex</em>, <em>bytes</em>, <em>bytes_le</em>, <em>fields</em>, or <em>int</em>.</p>
<p>Comparison of UUID objects are made by way of comparing their
<a class="reference internal" href="#uuid.UUID.int" title="uuid.UUID.int"><code class="xref py py-attr docutils literal notranslate"><span class="pre">UUID.int</span></code></a> attributes.  Comparison with a non-UUID object
raises a <a class="reference internal" href="exceptions.html#TypeError" title="TypeError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">TypeError</span></code></a>.</p>
<p><code class="docutils literal notranslate"><span class="pre">str(uuid)</span></code> returns a string in the form
<code class="docutils literal notranslate"><span class="pre">12345678-1234-5678-1234-************</span></code> where the 32 hexadecimal digits
represent the UUID.</p>
</dd></dl>

<p><a class="reference internal" href="#uuid.UUID" title="uuid.UUID"><code class="xref py py-class docutils literal notranslate"><span class="pre">UUID</span></code></a> instances have these read-only attributes:</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.bytes">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">bytes</span></span><a class="headerlink" href="#uuid.UUID.bytes" title="Link to this definition">¶</a></dt>
<dd><p>The UUID as a 16-byte string (containing the six integer fields in big-endian
byte order).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.bytes_le">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">bytes_le</span></span><a class="headerlink" href="#uuid.UUID.bytes_le" title="Link to this definition">¶</a></dt>
<dd><p>The UUID as a 16-byte string (with <em>time_low</em>, <em>time_mid</em>, and <em>time_hi_version</em>
in little-endian byte order).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.fields">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">fields</span></span><a class="headerlink" href="#uuid.UUID.fields" title="Link to this definition">¶</a></dt>
<dd><p>A tuple of the six integer fields of the UUID, which are also available as six
individual attributes and two derived attributes:</p>
</dd></dl>

<table class="docutils align-default">
<tbody>
<tr class="row-odd"><td><p>Field</p></td>
<td><p>Meaning</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.time_low">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">time_low</span></span><a class="headerlink" href="#uuid.UUID.time_low" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The first 32 bits of the UUID.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.time_mid">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">time_mid</span></span><a class="headerlink" href="#uuid.UUID.time_mid" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The next 16 bits of the UUID.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.time_hi_version">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">time_hi_version</span></span><a class="headerlink" href="#uuid.UUID.time_hi_version" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The next 16 bits of the UUID.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.clock_seq_hi_variant">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">clock_seq_hi_variant</span></span><a class="headerlink" href="#uuid.UUID.clock_seq_hi_variant" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The next 8 bits of the UUID.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.clock_seq_low">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">clock_seq_low</span></span><a class="headerlink" href="#uuid.UUID.clock_seq_low" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The next 8 bits of the UUID.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.node">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">node</span></span><a class="headerlink" href="#uuid.UUID.node" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The last 48 bits of the UUID.</p></td>
</tr>
<tr class="row-even"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.time">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">time</span></span><a class="headerlink" href="#uuid.UUID.time" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The 60-bit timestamp.</p></td>
</tr>
<tr class="row-odd"><td><dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.clock_seq">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">clock_seq</span></span><a class="headerlink" href="#uuid.UUID.clock_seq" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

</td>
<td><p>The 14-bit sequence number.</p></td>
</tr>
</tbody>
</table>
<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.hex">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">hex</span></span><a class="headerlink" href="#uuid.UUID.hex" title="Link to this definition">¶</a></dt>
<dd><p>The UUID as a 32-character lowercase hexadecimal string.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.int">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">int</span></span><a class="headerlink" href="#uuid.UUID.int" title="Link to this definition">¶</a></dt>
<dd><p>The UUID as a 128-bit integer.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.urn">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">urn</span></span><a class="headerlink" href="#uuid.UUID.urn" title="Link to this definition">¶</a></dt>
<dd><p>The UUID as a URN as specified in <span class="target" id="index-3"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.variant">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">variant</span></span><a class="headerlink" href="#uuid.UUID.variant" title="Link to this definition">¶</a></dt>
<dd><p>The UUID variant, which determines the internal layout of the UUID. This will be
one of the constants <a class="reference internal" href="#uuid.RESERVED_NCS" title="uuid.RESERVED_NCS"><code class="xref py py-const docutils literal notranslate"><span class="pre">RESERVED_NCS</span></code></a>, <a class="reference internal" href="#uuid.RFC_4122" title="uuid.RFC_4122"><code class="xref py py-const docutils literal notranslate"><span class="pre">RFC_4122</span></code></a>,
<a class="reference internal" href="#uuid.RESERVED_MICROSOFT" title="uuid.RESERVED_MICROSOFT"><code class="xref py py-const docutils literal notranslate"><span class="pre">RESERVED_MICROSOFT</span></code></a>, or <a class="reference internal" href="#uuid.RESERVED_FUTURE" title="uuid.RESERVED_FUTURE"><code class="xref py py-const docutils literal notranslate"><span class="pre">RESERVED_FUTURE</span></code></a>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.version">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">version</span></span><a class="headerlink" href="#uuid.UUID.version" title="Link to this definition">¶</a></dt>
<dd><p>The UUID version number (1 through 5, meaningful only when the variant is
<a class="reference internal" href="#uuid.RFC_4122" title="uuid.RFC_4122"><code class="xref py py-const docutils literal notranslate"><span class="pre">RFC_4122</span></code></a>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="uuid.UUID.is_safe">
<span class="sig-prename descclassname"><span class="pre">UUID.</span></span><span class="sig-name descname"><span class="pre">is_safe</span></span><a class="headerlink" href="#uuid.UUID.is_safe" title="Link to this definition">¶</a></dt>
<dd><p>An enumeration of <a class="reference internal" href="#uuid.SafeUUID" title="uuid.SafeUUID"><code class="xref py py-class docutils literal notranslate"><span class="pre">SafeUUID</span></code></a> which indicates whether the platform
generated the UUID in a multiprocessing-safe way.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
</dd></dl>

<p>The <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="uuid.getnode">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">getnode</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#uuid.getnode" title="Link to this definition">¶</a></dt>
<dd><p>Get the hardware address as a 48-bit positive integer.  The first time this
runs, it may launch a separate program, which could be quite slow.  If all
attempts to obtain the hardware address fail, we choose a random 48-bit
number with the multicast bit (least significant bit of the first octet)
set to 1 as recommended in <span class="target" id="index-4"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>.  “Hardware address” means the MAC
address of a network interface.  On a machine with multiple network
interfaces, universally administered MAC addresses (i.e. where the second
least significant bit of the first octet is <em>unset</em>) will be preferred over
locally administered MAC addresses, but with no other ordering guarantees.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>Universally administered MAC addresses are preferred over locally
administered MAC addresses, since the former are guaranteed to be
globally unique, while the latter are not.</p>
</div>
</dd></dl>

<dl class="py function" id="index-5">
<dt class="sig sig-object py" id="uuid.uuid1">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">uuid1</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">node</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">clock_seq</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#uuid.uuid1" title="Link to this definition">¶</a></dt>
<dd><p>Generate a UUID from a host ID, sequence number, and the current time. If <em>node</em>
is not given, <a class="reference internal" href="#uuid.getnode" title="uuid.getnode"><code class="xref py py-func docutils literal notranslate"><span class="pre">getnode()</span></code></a> is used to obtain the hardware address. If
<em>clock_seq</em> is given, it is used as the sequence number; otherwise a random
14-bit sequence number is chosen.</p>
</dd></dl>

<dl class="py function" id="index-6">
<dt class="sig sig-object py" id="uuid.uuid3">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">uuid3</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#uuid.uuid3" title="Link to this definition">¶</a></dt>
<dd><p>Generate a UUID based on the MD5 hash of a namespace identifier (which is a
UUID) and a name (which is a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object or a string
that will be encoded using UTF-8).</p>
</dd></dl>

<dl class="py function" id="index-7">
<dt class="sig sig-object py" id="uuid.uuid4">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">uuid4</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#uuid.uuid4" title="Link to this definition">¶</a></dt>
<dd><p>Generate a random UUID.</p>
</dd></dl>

<dl class="py function" id="index-8">
<dt class="sig sig-object py" id="uuid.uuid5">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">uuid5</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">namespace</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#uuid.uuid5" title="Link to this definition">¶</a></dt>
<dd><p>Generate a UUID based on the SHA-1 hash of a namespace identifier (which is a
UUID) and a name (which is a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object or a string
that will be encoded using UTF-8).</p>
</dd></dl>

<p id="index-9">The <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> module defines the following namespace identifiers for use with
<a class="reference internal" href="#uuid.uuid3" title="uuid.uuid3"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid3()</span></code></a> or <a class="reference internal" href="#uuid.uuid5" title="uuid.uuid5"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid5()</span></code></a>.</p>
<dl class="py data">
<dt class="sig sig-object py" id="uuid.NAMESPACE_DNS">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">NAMESPACE_DNS</span></span><a class="headerlink" href="#uuid.NAMESPACE_DNS" title="Link to this definition">¶</a></dt>
<dd><p>When this namespace is specified, the <em>name</em> string is a fully qualified domain
name.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.NAMESPACE_URL">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">NAMESPACE_URL</span></span><a class="headerlink" href="#uuid.NAMESPACE_URL" title="Link to this definition">¶</a></dt>
<dd><p>When this namespace is specified, the <em>name</em> string is a URL.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.NAMESPACE_OID">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">NAMESPACE_OID</span></span><a class="headerlink" href="#uuid.NAMESPACE_OID" title="Link to this definition">¶</a></dt>
<dd><p>When this namespace is specified, the <em>name</em> string is an ISO OID.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.NAMESPACE_X500">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">NAMESPACE_X500</span></span><a class="headerlink" href="#uuid.NAMESPACE_X500" title="Link to this definition">¶</a></dt>
<dd><p>When this namespace is specified, the <em>name</em> string is an X.500 DN in DER or a
text output format.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> module defines the following constants for the possible values
of the <a class="reference internal" href="#uuid.UUID.variant" title="uuid.UUID.variant"><code class="xref py py-attr docutils literal notranslate"><span class="pre">variant</span></code></a> attribute:</p>
<dl class="py data">
<dt class="sig sig-object py" id="uuid.RESERVED_NCS">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">RESERVED_NCS</span></span><a class="headerlink" href="#uuid.RESERVED_NCS" title="Link to this definition">¶</a></dt>
<dd><p>Reserved for NCS compatibility.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.RFC_4122">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">RFC_4122</span></span><a class="headerlink" href="#uuid.RFC_4122" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the UUID layout given in <span class="target" id="index-10"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a>.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.RESERVED_MICROSOFT">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">RESERVED_MICROSOFT</span></span><a class="headerlink" href="#uuid.RESERVED_MICROSOFT" title="Link to this definition">¶</a></dt>
<dd><p>Reserved for Microsoft compatibility.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="uuid.RESERVED_FUTURE">
<span class="sig-prename descclassname"><span class="pre">uuid.</span></span><span class="sig-name descname"><span class="pre">RESERVED_FUTURE</span></span><a class="headerlink" href="#uuid.RESERVED_FUTURE" title="Link to this definition">¶</a></dt>
<dd><p>Reserved for future definition.</p>
</dd></dl>

<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt><span class="target" id="index-11"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc4122.html"><strong>RFC 4122</strong></a> - A Universally Unique IDentifier (UUID) URN Namespace</dt><dd><p>This specification defines a Uniform Resource Name namespace for UUIDs, the
internal format of UUIDs, and methods of generating UUIDs.</p>
</dd>
</dl>
</div>
<section id="command-line-usage">
<span id="uuid-cli"></span><h2>Command-Line Usage<a class="headerlink" href="#command-line-usage" title="Link to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>The <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> module can be executed as a script from the command line.</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>-m<span class="w"> </span>uuid<span class="w"> </span><span class="o">[</span>-h<span class="o">]</span><span class="w"> </span><span class="o">[</span>-u<span class="w"> </span><span class="o">{</span>uuid1,uuid3,uuid4,uuid5<span class="o">}]</span><span class="w"> </span><span class="o">[</span>-n<span class="w"> </span>NAMESPACE<span class="o">]</span><span class="w"> </span><span class="o">[</span>-N<span class="w"> </span>NAME<span class="o">]</span>
</pre></div>
</div>
<p>The following options are accepted:</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-uuid-h">
<span id="cmdoption-uuid-help"></span><span class="sig-name descname"><span class="pre">-h</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--help</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-uuid-h" title="Link to this definition">¶</a></dt>
<dd><p>Show the help message and exit.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-uuid-u">
<span class="sig-name descname"><span class="pre">-u</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;uuid&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-u" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-uuid-uuid">
<span class="sig-name descname"><span class="pre">--uuid</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;uuid&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-uuid" title="Link to this definition">¶</a></dt>
<dd><p>Specify the function name to use to generate the uuid. By default <a class="reference internal" href="#uuid.uuid4" title="uuid.uuid4"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid4()</span></code></a>
is used.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-uuid-n">
<span class="sig-name descname"><span class="pre">-n</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;namespace&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-n" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-uuid-namespace">
<span class="sig-name descname"><span class="pre">--namespace</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;namespace&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-namespace" title="Link to this definition">¶</a></dt>
<dd><p>The namespace is a <code class="docutils literal notranslate"><span class="pre">UUID</span></code>, or <code class="docutils literal notranslate"><span class="pre">&#64;ns</span></code> where <code class="docutils literal notranslate"><span class="pre">ns</span></code> is a well-known predefined UUID
addressed by namespace name. Such as <code class="docutils literal notranslate"><span class="pre">&#64;dns</span></code>, <code class="docutils literal notranslate"><span class="pre">&#64;url</span></code>, <code class="docutils literal notranslate"><span class="pre">&#64;oid</span></code>, and <code class="docutils literal notranslate"><span class="pre">&#64;x500</span></code>.
Only required for <a class="reference internal" href="#uuid.uuid3" title="uuid.uuid3"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid3()</span></code></a> / <a class="reference internal" href="#uuid.uuid5" title="uuid.uuid5"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid5()</span></code></a> functions.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-uuid-N">
<span class="sig-name descname"><span class="pre">-N</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;name&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-N" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-uuid-name">
<span class="sig-name descname"><span class="pre">--name</span></span><span class="sig-prename descclassname"> <span class="pre">&lt;name&gt;</span></span><a class="headerlink" href="#cmdoption-uuid-name" title="Link to this definition">¶</a></dt>
<dd><p>The name used as part of generating the uuid. Only required for
<a class="reference internal" href="#uuid.uuid3" title="uuid.uuid3"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid3()</span></code></a> / <a class="reference internal" href="#uuid.uuid5" title="uuid.uuid5"><code class="xref py py-func docutils literal notranslate"><span class="pre">uuid5()</span></code></a> functions.</p>
</dd></dl>

</section>
<section id="example">
<span id="uuid-example"></span><h2>Example<a class="headerlink" href="#example" title="Link to this heading">¶</a></h2>
<p>Here are some examples of typical usage of the <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> module:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">uuid</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a UUID based on the host ID and current time</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid1</span><span class="p">()</span>
<span class="go">UUID(&#39;a8098c1a-f86e-11da-bd1a-00112444be1e&#39;)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a UUID using an MD5 hash of a namespace UUID and a name</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid3</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">NAMESPACE_DNS</span><span class="p">,</span> <span class="s1">&#39;python.org&#39;</span><span class="p">)</span>
<span class="go">UUID(&#39;6fa459ea-ee8a-3ca4-894e-db77e160355e&#39;)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a random UUID</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">()</span>
<span class="go">UUID(&#39;16fd2706-8baf-433b-82eb-8c7fada847da&#39;)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a UUID using a SHA-1 hash of a namespace UUID and a name</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid5</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">NAMESPACE_DNS</span><span class="p">,</span> <span class="s1">&#39;python.org&#39;</span><span class="p">)</span>
<span class="go">UUID(&#39;886313e1-3b8a-5372-9b90-0c9aee199e5d&#39;)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a UUID from a string of hex digits (braces and hyphens ignored)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span> <span class="o">=</span> <span class="n">uuid</span><span class="o">.</span><span class="n">UUID</span><span class="p">(</span><span class="s1">&#39;{00010203-0405-0607-0809-0a0b0c0d0e0f}&#39;</span><span class="p">)</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># convert a UUID to a string of hex digits in standard form</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
<span class="go">&#39;00010203-0405-0607-0809-0a0b0c0d0e0f&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># get the raw 16 bytes of the UUID</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">x</span><span class="o">.</span><span class="n">bytes</span>
<span class="go">b&#39;\x00\x01\x02\x03\x04\x05\x06\x07\x08\t\n\x0b\x0c\r\x0e\x0f&#39;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="c1"># make a UUID from a 16-byte string</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">uuid</span><span class="o">.</span><span class="n">UUID</span><span class="p">(</span><span class="nb">bytes</span><span class="o">=</span><span class="n">x</span><span class="o">.</span><span class="n">bytes</span><span class="p">)</span>
<span class="go">UUID(&#39;00010203-0405-0607-0809-0a0b0c0d0e0f&#39;)</span>
</pre></div>
</div>
</section>
<section id="command-line-example">
<span id="uuid-cli-example"></span><h2>Command-Line Example<a class="headerlink" href="#command-line-example" title="Link to this heading">¶</a></h2>
<p>Here are some examples of typical usage of the <a class="reference internal" href="#module-uuid" title="uuid: UUID objects (universally unique identifiers) according to RFC 4122"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code></a> command line interface:</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="c1"># generate a random uuid - by default uuid4() is used</span>
$<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>uuid

<span class="c1"># generate a uuid using uuid1()</span>
$<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>uuid<span class="w"> </span>-u<span class="w"> </span>uuid1

<span class="c1"># generate a uuid using uuid5</span>
$<span class="w"> </span>python<span class="w"> </span>-m<span class="w"> </span>uuid<span class="w"> </span>-u<span class="w"> </span>uuid5<span class="w"> </span>-n<span class="w"> </span>@url<span class="w"> </span>-N<span class="w"> </span>example.com
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a><ul>
<li><a class="reference internal" href="#command-line-usage">Command-Line Usage</a></li>
<li><a class="reference internal" href="#example">Example</a></li>
<li><a class="reference internal" href="#command-line-example">Command-Line Example</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="smtplib.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">smtplib</span></code> — SMTP protocol client</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="socketserver.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">socketserver</span></code> — A framework for network servers</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/uuid.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="socketserver.html" title="socketserver — A framework for network servers"
             >next</a> |</li>
        <li class="right" >
          <a href="smtplib.html" title="smtplib — SMTP protocol client"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="internet.html" >Internet Protocols and Support</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">uuid</span></code> — UUID objects according to <strong>RFC 4122</strong></a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>