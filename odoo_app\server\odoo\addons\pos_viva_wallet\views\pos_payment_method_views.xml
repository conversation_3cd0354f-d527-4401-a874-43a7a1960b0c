<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_payment_method_view_form_inherit_pos_viva_wallet" model="ir.ui.view">
        <field name="name">pos.payment.method.form.inherit.viva.wallet</field>
        <field name="model">pos.payment.method</field>
        <field name="inherit_id" ref="point_of_sale.pos_payment_method_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_payment_terminal']" position="after">
                <!-- Viva Wallet -->
                <field name="viva_wallet_merchant_id" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_api_key" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_client_id" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_client_secret" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_test_mode" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_terminal_id" invisible="use_payment_terminal != 'viva_wallet'" required="use_payment_terminal == 'viva_wallet'"/>
                <field name="viva_wallet_webhook_endpoint" invisible="use_payment_terminal != 'viva_wallet' or not id" required="use_payment_terminal == 'viva_wallet'" widget="CopyClipboardChar"/>
            </xpath>
        </field>
    </record>
</odoo>
