<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="py_compile — Compile Python source files" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/py_compile.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/py_compile.py The py_compile module provides a function to generate a byte-code file from a source file, and another function used when the module source file is invoked as a scrip..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/py_compile.py The py_compile module provides a function to generate a byte-code file from a source file, and another function used when the module source file is invoked as a scrip..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>py_compile — Compile Python source files &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="compileall — Byte-compile Python libraries" href="compileall.html" />
    <link rel="prev" title="pyclbr — Python module browser support" href="pyclbr.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/py_compile.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a><ul>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pyclbr.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="compileall.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code> — Byte-compile Python libraries</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/py_compile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="compileall.html" title="compileall — Byte-compile Python libraries"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="pyclbr.html" title="pyclbr — Python module browser support"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" accesskey="U">Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-py_compile">
<span id="py-compile-compile-python-source-files"></span><h1><a class="reference internal" href="#module-py_compile" title="py_compile: Generate byte-code files from Python source files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code></a> — Compile Python source files<a class="headerlink" href="#module-py_compile" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/py_compile.py">Lib/py_compile.py</a></p>
<hr class="docutils" id="index-0" />
<p>The <a class="reference internal" href="#module-py_compile" title="py_compile: Generate byte-code files from Python source files."><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code></a> module provides a function to generate a byte-code file
from a source file, and another function used when the module source file is
invoked as a script.</p>
<p>Though not often needed, this function can be useful when installing modules for
shared use, especially if some of the users may not have permission to write the
byte-code cache files in the directory containing the source code.</p>
<dl class="py exception">
<dt class="sig sig-object py" id="py_compile.PyCompileError">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">py_compile.</span></span><span class="sig-name descname"><span class="pre">PyCompileError</span></span><a class="headerlink" href="#py_compile.PyCompileError" title="Link to this definition">¶</a></dt>
<dd><p>Exception raised when an error occurs while attempting to compile the file.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="py_compile.compile">
<span class="sig-prename descclassname"><span class="pre">py_compile.</span></span><span class="sig-name descname"><span class="pre">compile</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">cfile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">dfile</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">doraise</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">False</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">optimize</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">-1</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">invalidation_mode</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">PycInvalidationMode.TIMESTAMP</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">quiet</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#py_compile.compile" title="Link to this definition">¶</a></dt>
<dd><p>Compile a source file to byte-code and write out the byte-code cache file.
The source code is loaded from the file named <em>file</em>.  The byte-code is
written to <em>cfile</em>, which defaults to the <span class="target" id="index-1"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a>/<span class="target" id="index-2"></span><a class="pep reference external" href="https://peps.python.org/pep-0488/"><strong>PEP 488</strong></a> path, ending
in <code class="docutils literal notranslate"><span class="pre">.pyc</span></code>.
For example, if <em>file</em> is <code class="docutils literal notranslate"><span class="pre">/foo/bar/baz.py</span></code> <em>cfile</em> will default to
<code class="docutils literal notranslate"><span class="pre">/foo/bar/__pycache__/baz.cpython-32.pyc</span></code> for Python 3.2.  If <em>dfile</em> is
specified, it is used instead of <em>file</em> as the name of the source file from
which source lines are obtained for display in exception tracebacks.
If <em>doraise</em> is true, a <a class="reference internal" href="#py_compile.PyCompileError" title="py_compile.PyCompileError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PyCompileError</span></code></a> is raised
when an error is encountered while compiling <em>file</em>. If <em>doraise</em> is false
(the default), an error string is written to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>, but no exception
is raised.  This function returns the path to byte-compiled file, i.e.
whatever <em>cfile</em> value was used.</p>
<p>The <em>doraise</em> and <em>quiet</em> arguments determine how errors are handled while
compiling file. If <em>quiet</em> is 0 or 1, and <em>doraise</em> is false, the default
behaviour is enabled: an error string is written to <code class="docutils literal notranslate"><span class="pre">sys.stderr</span></code>, and the
function returns <code class="docutils literal notranslate"><span class="pre">None</span></code> instead of a path. If <em>doraise</em> is true,
a <a class="reference internal" href="#py_compile.PyCompileError" title="py_compile.PyCompileError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">PyCompileError</span></code></a> is raised instead. However if <em>quiet</em> is 2,
no message is written, and <em>doraise</em> has no effect.</p>
<p>If the path that <em>cfile</em> becomes (either explicitly specified or computed)
is a symlink or non-regular file, <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> will be raised.
This is to act as a warning that import will turn those paths into regular
files if it is allowed to write byte-compiled files to those paths. This is
a side-effect of import using file renaming to place the final byte-compiled
file into place to prevent concurrent file writing issues.</p>
<p><em>optimize</em> controls the optimization level and is passed to the built-in
<a class="reference internal" href="functions.html#compile" title="compile"><code class="xref py py-func docutils literal notranslate"><span class="pre">compile()</span></code></a> function.  The default of <code class="docutils literal notranslate"><span class="pre">-1</span></code> selects the optimization
level of the current interpreter.</p>
<p><em>invalidation_mode</em> should be a member of the <a class="reference internal" href="#py_compile.PycInvalidationMode" title="py_compile.PycInvalidationMode"><code class="xref py py-class docutils literal notranslate"><span class="pre">PycInvalidationMode</span></code></a>
enum and controls how the generated bytecode cache is invalidated at
runtime.  The default is <a class="reference internal" href="#py_compile.PycInvalidationMode.CHECKED_HASH" title="py_compile.PycInvalidationMode.CHECKED_HASH"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PycInvalidationMode.CHECKED_HASH</span></code></a> if
the <span class="target" id="index-3"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SOURCE_DATE_EPOCH</span></code> environment variable is set, otherwise
the default is <a class="reference internal" href="#py_compile.PycInvalidationMode.TIMESTAMP" title="py_compile.PycInvalidationMode.TIMESTAMP"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PycInvalidationMode.TIMESTAMP</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Changed default value of <em>cfile</em> to be <span class="target" id="index-4"></span><a class="pep reference external" href="https://peps.python.org/pep-3147/"><strong>PEP 3147</strong></a>-compliant.  Previous
default was <em>file</em> + <code class="docutils literal notranslate"><span class="pre">'c'</span></code> (<code class="docutils literal notranslate"><span class="pre">'o'</span></code> if optimization was enabled).
Also added the <em>optimize</em> parameter.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Changed code to use <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a> for the byte-code cache file writing.
This means file creation/writing semantics now match what <a class="reference internal" href="importlib.html#module-importlib" title="importlib: The implementation of the import machinery."><code class="xref py py-mod docutils literal notranslate"><span class="pre">importlib</span></code></a>
does, e.g. permissions, write-and-move semantics, etc. Also added the
caveat that <a class="reference internal" href="exceptions.html#FileExistsError" title="FileExistsError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">FileExistsError</span></code></a> is raised if <em>cfile</em> is a symlink or
non-regular file.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7: </span>The <em>invalidation_mode</em> parameter was added as specified in <span class="target" id="index-5"></span><a class="pep reference external" href="https://peps.python.org/pep-0552/"><strong>PEP 552</strong></a>.
If the <span class="target" id="index-6"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SOURCE_DATE_EPOCH</span></code> environment variable is set,
<em>invalidation_mode</em> will be forced to
<a class="reference internal" href="#py_compile.PycInvalidationMode.CHECKED_HASH" title="py_compile.PycInvalidationMode.CHECKED_HASH"><code class="xref py py-attr docutils literal notranslate"><span class="pre">PycInvalidationMode.CHECKED_HASH</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.7.2: </span>The <span class="target" id="index-7"></span><code class="xref std std-envvar docutils literal notranslate"><span class="pre">SOURCE_DATE_EPOCH</span></code> environment variable no longer
overrides the value of the <em>invalidation_mode</em> argument, and determines
its default value instead.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>The <em>quiet</em> parameter was added.</p>
</div>
</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="py_compile.PycInvalidationMode">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">py_compile.</span></span><span class="sig-name descname"><span class="pre">PycInvalidationMode</span></span><a class="headerlink" href="#py_compile.PycInvalidationMode" title="Link to this definition">¶</a></dt>
<dd><p>A enumeration of possible methods the interpreter can use to determine
whether a bytecode file is up to date with a source file. The <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file
indicates the desired invalidation mode in its header. See
<a class="reference internal" href="../reference/import.html#pyc-invalidation"><span class="std std-ref">Cached bytecode invalidation</span></a> for more information on how Python invalidates
<code class="docutils literal notranslate"><span class="pre">.pyc</span></code> files at runtime.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<dl class="py attribute">
<dt class="sig sig-object py" id="py_compile.PycInvalidationMode.TIMESTAMP">
<span class="sig-name descname"><span class="pre">TIMESTAMP</span></span><a class="headerlink" href="#py_compile.PycInvalidationMode.TIMESTAMP" title="Link to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file includes the timestamp and size of the source file,
which Python will compare against the metadata of the source file at
runtime to determine if the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file needs to be regenerated.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="py_compile.PycInvalidationMode.CHECKED_HASH">
<span class="sig-name descname"><span class="pre">CHECKED_HASH</span></span><a class="headerlink" href="#py_compile.PycInvalidationMode.CHECKED_HASH" title="Link to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file includes a hash of the source file content, which Python
will compare against the source at runtime to determine if the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code>
file needs to be regenerated.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="py_compile.PycInvalidationMode.UNCHECKED_HASH">
<span class="sig-name descname"><span class="pre">UNCHECKED_HASH</span></span><a class="headerlink" href="#py_compile.PycInvalidationMode.UNCHECKED_HASH" title="Link to this definition">¶</a></dt>
<dd><p>Like <a class="reference internal" href="#py_compile.PycInvalidationMode.CHECKED_HASH" title="py_compile.PycInvalidationMode.CHECKED_HASH"><code class="xref py py-attr docutils literal notranslate"><span class="pre">CHECKED_HASH</span></code></a>, the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file includes a hash of the source
file content. However, Python will at runtime assume the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> file is
up to date and not validate the <code class="docutils literal notranslate"><span class="pre">.pyc</span></code> against the source file at all.</p>
<p>This option is useful when the <code class="docutils literal notranslate"><span class="pre">.pycs</span></code> are kept up to date by some
system external to Python like a build system.</p>
</dd></dl>

</dd></dl>

<section id="command-line-interface">
<span id="py-compile-cli"></span><h2>Command-Line Interface<a class="headerlink" href="#command-line-interface" title="Link to this heading">¶</a></h2>
<p>This module can be invoked as a script to compile several source
files.  The files named in <em>filenames</em> are compiled and the resulting
bytecode is cached in the normal manner.  This program does not search
a directory structure to locate source files; it only compiles files
named explicitly. The exit status is nonzero if one of the files could
not be compiled.</p>
<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-python-m-py_compile-arg-file">
<span class="sig-name descname"><span class="pre">&lt;file&gt;</span></span><span class="sig-prename descclassname"> <span class="pre">...</span> <span class="pre">&lt;fileN&gt;</span></span><a class="headerlink" href="#cmdoption-python-m-py_compile-arg-file" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object std" id="cmdoption-python-m-py_compile">
<span class="sig-name descname"><span class="pre">-</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-python-m-py_compile" title="Link to this definition">¶</a></dt>
<dd><p>Positional arguments are files to compile.  If <code class="docutils literal notranslate"><span class="pre">-</span></code> is the only
parameter, the list of files is taken from standard input.</p>
</dd></dl>

<dl class="std option">
<dt class="sig sig-object std" id="cmdoption-python-m-py_compile-q">
<span id="cmdoption-python-m-py_compile-quiet"></span><span class="sig-name descname"><span class="pre">-q</span></span><span class="sig-prename descclassname"></span><span class="sig-prename descclassname"><span class="pre">,</span> </span><span class="sig-name descname"><span class="pre">--quiet</span></span><span class="sig-prename descclassname"></span><a class="headerlink" href="#cmdoption-python-m-py_compile-q" title="Link to this definition">¶</a></dt>
<dd><p>Suppress errors output.</p>
</dd></dl>

<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>Added support for <code class="docutils literal notranslate"><span class="pre">-</span></code>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.10: </span>Added support for <a class="reference internal" href="#cmdoption-python-m-py_compile-q"><code class="xref std std-option docutils literal notranslate"><span class="pre">-q</span></code></a>.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<dl class="simple">
<dt>Module <a class="reference internal" href="compileall.html#module-compileall" title="compileall: Tools for byte-compiling all Python source files in a directory tree."><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code></a></dt><dd><p>Utilities to compile all Python source files in a directory tree.</p>
</dd>
</dl>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a><ul>
<li><a class="reference internal" href="#command-line-interface">Command-Line Interface</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="pyclbr.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">pyclbr</span></code> — Python module browser support</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="compileall.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">compileall</span></code> — Byte-compile Python libraries</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/py_compile.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="compileall.html" title="compileall — Byte-compile Python libraries"
             >next</a> |</li>
        <li class="right" >
          <a href="pyclbr.html" title="pyclbr — Python module browser support"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="language.html" >Python Language Services</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">py_compile</span></code> — Compile Python source files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>