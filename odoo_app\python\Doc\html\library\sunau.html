<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="sunau — Read and write Sun AU files" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/library/sunau.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Source code: Lib/sunau.py The sunau module provides a convenient interface to the Sun AU sound format. Note that this module is interface-compatible with the modules aifc and wave. An audio file co..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Source code: Lib/sunau.py The sunau module provides a convenient interface to the Sun AU sound format. Note that this module is interface-compatible with the modules aifc and wave. An audio file co..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>sunau — Read and write Sun AU files &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="telnetlib — Telnet client" href="telnetlib.html" />
    <link rel="prev" title="spwd — The shadow password database" href="spwd.html" />
    <link rel="canonical" href="https://docs.python.org/3/library/sunau.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a><ul>
<li><a class="reference internal" href="#au-read-objects">AU_read Objects</a></li>
<li><a class="reference internal" href="#au-write-objects">AU_write Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="spwd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">spwd</span></code> — The shadow password database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="telnetlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sunau.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="telnetlib.html" title="telnetlib — Telnet client"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="spwd.html" title="spwd — The shadow password database"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" accesskey="U">Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="module-sunau">
<span id="sunau-read-and-write-sun-au-files"></span><h1><a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> — Read and write Sun AU files<a class="headerlink" href="#module-sunau" title="Link to this heading">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.12/Lib/sunau.py">Lib/sunau.py</a></p>
<div class="deprecated-removed">
<p><span class="versionmodified">Deprecated since version 3.11, will be removed in version 3.13: </span>The <a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> module is deprecated
(see <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-0594/#sunau"><strong>PEP 594</strong></a> for details).</p>
</div>
<hr class="docutils" />
<p>The <a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> module provides a convenient interface to the Sun AU sound
format.  Note that this module is interface-compatible with the modules
<a class="reference internal" href="aifc.html#module-aifc" title="aifc: Read and write audio files in AIFF or AIFC format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">aifc</span></code></a> and <a class="reference internal" href="wave.html#module-wave" title="wave: Provide an interface to the WAV sound format."><code class="xref py py-mod docutils literal notranslate"><span class="pre">wave</span></code></a>.</p>
<p>An audio file consists of a header followed by the data.  The fields of the
header are:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Field</p></th>
<th class="head"><p>Contents</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>magic word</p></td>
<td><p>The four bytes <code class="docutils literal notranslate"><span class="pre">.snd</span></code>.</p></td>
</tr>
<tr class="row-odd"><td><p>header size</p></td>
<td><p>Size of the header, including info, in bytes.</p></td>
</tr>
<tr class="row-even"><td><p>data size</p></td>
<td><p>Physical size of the data, in bytes.</p></td>
</tr>
<tr class="row-odd"><td><p>encoding</p></td>
<td><p>Indicates how the audio samples are encoded.</p></td>
</tr>
<tr class="row-even"><td><p>sample rate</p></td>
<td><p>The sampling rate.</p></td>
</tr>
<tr class="row-odd"><td><p># of channels</p></td>
<td><p>The number of channels in the samples.</p></td>
</tr>
<tr class="row-even"><td><p>info</p></td>
<td><p>ASCII string giving a description of the
audio file (padded with null bytes).</p></td>
</tr>
</tbody>
</table>
<p>Apart from the info field, all header fields are 4 bytes in size. They are all
32-bit unsigned integers encoded in big-endian byte order.</p>
<p>The <a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> module defines the following functions:</p>
<dl class="py function">
<dt class="sig sig-object py" id="sunau.open">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">open</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">mode</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.open" title="Link to this definition">¶</a></dt>
<dd><p>If <em>file</em> is a string, open the file by that name, otherwise treat it as a
seekable file-like object. <em>mode</em> can be any of</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">'r'</span></code></dt><dd><p>Read only mode.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">'w'</span></code></dt><dd><p>Write only mode.</p>
</dd>
</dl>
<p>Note that it does not allow read/write files.</p>
<p>A <em>mode</em> of <code class="docutils literal notranslate"><span class="pre">'r'</span></code> returns an <code class="xref py py-class docutils literal notranslate"><span class="pre">AU_read</span></code> object, while a <em>mode</em> of <code class="docutils literal notranslate"><span class="pre">'w'</span></code>
or <code class="docutils literal notranslate"><span class="pre">'wb'</span></code> returns an <code class="xref py py-class docutils literal notranslate"><span class="pre">AU_write</span></code> object.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> module defines the following exception:</p>
<dl class="py exception">
<dt class="sig sig-object py" id="sunau.Error">
<em class="property"><span class="pre">exception</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">Error</span></span><a class="headerlink" href="#sunau.Error" title="Link to this definition">¶</a></dt>
<dd><p>An error raised when something is impossible because of Sun AU specs or
implementation deficiency.</p>
</dd></dl>

<p>The <a class="reference internal" href="#module-sunau" title="sunau: Provide an interface to the Sun AU sound format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code></a> module defines the following data items:</p>
<dl class="py data">
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_MAGIC">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_MAGIC</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_MAGIC" title="Link to this definition">¶</a></dt>
<dd><p>An integer every valid Sun AU file begins with, stored in big-endian form.  This
is the string <code class="docutils literal notranslate"><span class="pre">.snd</span></code> interpreted as an integer.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_MULAW_8">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_MULAW_8</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_MULAW_8" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_LINEAR_8">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_LINEAR_8</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_LINEAR_8" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_LINEAR_16">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_LINEAR_16</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_LINEAR_16" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_LINEAR_24">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_LINEAR_24</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_LINEAR_24" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_LINEAR_32">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_LINEAR_32</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_LINEAR_32" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_ALAW_8">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_ALAW_8</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_ALAW_8" title="Link to this definition">¶</a></dt>
<dd><p>Values of the encoding field from the AU header which are supported by this
module.</p>
</dd></dl>

<dl class="py data">
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_FLOAT">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_FLOAT</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_FLOAT" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_DOUBLE">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_DOUBLE</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_DOUBLE" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_ADPCM_G721">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_ADPCM_G721</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_ADPCM_G721" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_ADPCM_G722">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_ADPCM_G722</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_ADPCM_G722" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_ADPCM_G723_3">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_ADPCM_G723_3</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_ADPCM_G723_3" title="Link to this definition">¶</a></dt>
<dt class="sig sig-object py" id="sunau.AUDIO_FILE_ENCODING_ADPCM_G723_5">
<span class="sig-prename descclassname"><span class="pre">sunau.</span></span><span class="sig-name descname"><span class="pre">AUDIO_FILE_ENCODING_ADPCM_G723_5</span></span><a class="headerlink" href="#sunau.AUDIO_FILE_ENCODING_ADPCM_G723_5" title="Link to this definition">¶</a></dt>
<dd><p>Additional known values of the encoding field from the AU header, but which are
not supported by this module.</p>
</dd></dl>

<section id="au-read-objects">
<span id="id1"></span><h2>AU_read Objects<a class="headerlink" href="#au-read-objects" title="Link to this heading">¶</a></h2>
<p>AU_read objects, as returned by <a class="reference internal" href="#sunau.open" title="sunau.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> above, have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.close">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.close" title="Link to this definition">¶</a></dt>
<dd><p>Close the stream, and make the instance unusable. (This is  called automatically
on deletion.)</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getnchannels">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getnchannels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getnchannels" title="Link to this definition">¶</a></dt>
<dd><p>Returns number of audio channels (1 for mono, 2 for stereo).</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getsampwidth">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getsampwidth</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getsampwidth" title="Link to this definition">¶</a></dt>
<dd><p>Returns sample width in bytes.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getframerate">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getframerate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getframerate" title="Link to this definition">¶</a></dt>
<dd><p>Returns sampling frequency.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getnframes">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getnframes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getnframes" title="Link to this definition">¶</a></dt>
<dd><p>Returns number of audio frames.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getcomptype">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getcomptype</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getcomptype" title="Link to this definition">¶</a></dt>
<dd><p>Returns compression type. Supported compression types are <code class="docutils literal notranslate"><span class="pre">'ULAW'</span></code>, <code class="docutils literal notranslate"><span class="pre">'ALAW'</span></code>
and <code class="docutils literal notranslate"><span class="pre">'NONE'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getcompname">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getcompname</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getcompname" title="Link to this definition">¶</a></dt>
<dd><p>Human-readable version of <a class="reference internal" href="#sunau.AU_read.getcomptype" title="sunau.AU_read.getcomptype"><code class="xref py py-meth docutils literal notranslate"><span class="pre">getcomptype()</span></code></a>.  The supported types have the
respective names <code class="docutils literal notranslate"><span class="pre">'CCITT</span> <span class="pre">G.711</span> <span class="pre">u-law'</span></code>, <code class="docutils literal notranslate"><span class="pre">'CCITT</span> <span class="pre">G.711</span> <span class="pre">A-law'</span></code> and <code class="docutils literal notranslate"><span class="pre">'not</span>
<span class="pre">compressed'</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getparams">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getparams</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getparams" title="Link to this definition">¶</a></dt>
<dd><p>Returns a <a class="reference internal" href="collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-func docutils literal notranslate"><span class="pre">namedtuple()</span></code></a> <code class="docutils literal notranslate"><span class="pre">(nchannels,</span> <span class="pre">sampwidth,</span>
<span class="pre">framerate,</span> <span class="pre">nframes,</span> <span class="pre">comptype,</span> <span class="pre">compname)</span></code>, equivalent to output of the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">get*()</span></code> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.readframes">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">readframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.readframes" title="Link to this definition">¶</a></dt>
<dd><p>Reads and returns at most <em>n</em> frames of audio, as a <a class="reference internal" href="stdtypes.html#bytes" title="bytes"><code class="xref py py-class docutils literal notranslate"><span class="pre">bytes</span></code></a> object.  The data
will be returned in linear format.  If the original data is in u-LAW format, it
will be converted.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.rewind">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">rewind</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.rewind" title="Link to this definition">¶</a></dt>
<dd><p>Rewind the file pointer to the beginning of the audio stream.</p>
</dd></dl>

<p>The following two methods define a term “position” which is compatible between
them, and is otherwise implementation dependent.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.setpos">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">setpos</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">pos</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.setpos" title="Link to this definition">¶</a></dt>
<dd><p>Set the file pointer to the specified position.  Only values returned from
<a class="reference internal" href="#sunau.AU_read.tell" title="sunau.AU_read.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">tell()</span></code></a> should be used for <em>pos</em>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.tell">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return current file pointer position.  Note that the returned value has nothing
to do with the actual position in the file.</p>
</dd></dl>

<p>The following two functions are defined for compatibility with the  <a class="reference internal" href="aifc.html#module-aifc" title="aifc: Read and write audio files in AIFF or AIFC format. (deprecated)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">aifc</span></code></a>,
and don’t do anything interesting.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getmarkers">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getmarkers</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getmarkers" title="Link to this definition">¶</a></dt>
<dd><p>Returns <code class="docutils literal notranslate"><span class="pre">None</span></code>.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_read.getmark">
<span class="sig-prename descclassname"><span class="pre">AU_read.</span></span><span class="sig-name descname"><span class="pre">getmark</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">id</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_read.getmark" title="Link to this definition">¶</a></dt>
<dd><p>Raise an error.</p>
</dd></dl>

</section>
<section id="au-write-objects">
<span id="id2"></span><h2>AU_write Objects<a class="headerlink" href="#au-write-objects" title="Link to this heading">¶</a></h2>
<p>AU_write objects, as returned by <a class="reference internal" href="#sunau.open" title="sunau.open"><code class="xref py py-func docutils literal notranslate"><span class="pre">open()</span></code></a> above, have the following methods:</p>
<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setnchannels">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setnchannels</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setnchannels" title="Link to this definition">¶</a></dt>
<dd><p>Set the number of channels.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setsampwidth">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setsampwidth</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setsampwidth" title="Link to this definition">¶</a></dt>
<dd><p>Set the sample width (in bytes.)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Added support for 24-bit samples.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setframerate">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setframerate</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setframerate" title="Link to this definition">¶</a></dt>
<dd><p>Set the frame rate.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setnframes">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setnframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">n</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setnframes" title="Link to this definition">¶</a></dt>
<dd><p>Set the number of frames. This can be later changed, when and if more  frames
are written.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setcomptype">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setcomptype</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">type</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setcomptype" title="Link to this definition">¶</a></dt>
<dd><p>Set the compression type and description. Only <code class="docutils literal notranslate"><span class="pre">'NONE'</span></code> and <code class="docutils literal notranslate"><span class="pre">'ULAW'</span></code> are
supported on output.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.setparams">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">setparams</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">tuple</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.setparams" title="Link to this definition">¶</a></dt>
<dd><p>The <em>tuple</em> should be <code class="docutils literal notranslate"><span class="pre">(nchannels,</span> <span class="pre">sampwidth,</span> <span class="pre">framerate,</span> <span class="pre">nframes,</span> <span class="pre">comptype,</span>
<span class="pre">compname)</span></code>, with values valid for the <code class="xref py py-meth docutils literal notranslate"><span class="pre">set*()</span></code> methods.  Set all
parameters.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.tell">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">tell</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.tell" title="Link to this definition">¶</a></dt>
<dd><p>Return current position in the file, with the same disclaimer for the
<a class="reference internal" href="#sunau.AU_read.tell" title="sunau.AU_read.tell"><code class="xref py py-meth docutils literal notranslate"><span class="pre">AU_read.tell()</span></code></a> and <a class="reference internal" href="#sunau.AU_read.setpos" title="sunau.AU_read.setpos"><code class="xref py py-meth docutils literal notranslate"><span class="pre">AU_read.setpos()</span></code></a> methods.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.writeframesraw">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">writeframesraw</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.writeframesraw" title="Link to this definition">¶</a></dt>
<dd><p>Write audio frames, without correcting <em>nframes</em>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Any <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.writeframes">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">writeframes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">data</span></span></em><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.writeframes" title="Link to this definition">¶</a></dt>
<dd><p>Write audio frames and make sure <em>nframes</em> is correct.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4: </span>Any <a class="reference internal" href="../glossary.html#term-bytes-like-object"><span class="xref std std-term">bytes-like object</span></a> is now accepted.</p>
</div>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sunau.AU_write.close">
<span class="sig-prename descclassname"><span class="pre">AU_write.</span></span><span class="sig-name descname"><span class="pre">close</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sunau.AU_write.close" title="Link to this definition">¶</a></dt>
<dd><p>Make sure <em>nframes</em> is correct, and close the file.</p>
<p>This method is called upon deletion.</p>
</dd></dl>

<p>Note that it is invalid to set any parameters after calling  <code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframes()</span></code>
or <code class="xref py py-meth docutils literal notranslate"><span class="pre">writeframesraw()</span></code>.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#"><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a><ul>
<li><a class="reference internal" href="#au-read-objects">AU_read Objects</a></li>
<li><a class="reference internal" href="#au-write-objects">AU_write Objects</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="spwd.html"
                          title="previous chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">spwd</span></code> — The shadow password database</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="telnetlib.html"
                          title="next chapter"><code class="xref py py-mod docutils literal notranslate"><span class="pre">telnetlib</span></code> — Telnet client</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/library/sunau.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="telnetlib.html" title="telnetlib — Telnet client"
             >next</a> |</li>
        <li class="right" >
          <a href="spwd.html" title="spwd — The shadow password database"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="superseded.html" >Superseded Modules</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">sunau</span></code> — Read and write Sun AU files</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>