# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_ldap
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap_create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr ""

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_company
msgid "Company"
msgstr "Compañía"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_create_user
msgid "Create User"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_create_date
msgid "Created on"
msgstr "Creado en"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_id
msgid "ID"
msgstr "ID (identificación)"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings_ldaps
msgid "LDAP Parameters"
msgstr ""

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_server
msgid "LDAP Server address"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_server_port
msgid "LDAP Server port"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_base
msgid "LDAP base"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_binddn
msgid "LDAP binddn"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_filter
msgid "LDAP filter"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_password
msgid "LDAP password"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr ""

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap_ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr ""

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_user
msgid "Template User"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap_ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap_ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap_ldap_tls
msgid "Use TLS"
msgstr ""

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr ""

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap_user
msgid "User to copy when creating new users"
msgstr ""

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "Users"
msgstr ""

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "res.company.ldap"
msgstr ""

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "res.config.settings"
msgstr ""
