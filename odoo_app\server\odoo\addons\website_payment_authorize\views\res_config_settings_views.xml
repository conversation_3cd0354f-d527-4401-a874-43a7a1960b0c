<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.payment.authorize</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="website_payment.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <setting id="website_payment" position="after">
                <setting help="Charge order directly or authorize at the order and capture the payment later on, manually.">
                    <field name="authorize_capture_method" class="w-75" widget="radio" />
                </setting>
            </setting>
        </field>
    </record>
</odoo>
