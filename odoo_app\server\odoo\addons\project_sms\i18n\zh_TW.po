# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_sms
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_project_stage__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the project reaches this stage."
msgstr "若設定，當專案到達此階段時，會自動向客戶發送 SMS 文字短訊。"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_task_type__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the task reaches this stage."
msgstr "若設定，當任務到達此階段時，會自動向客戶發送 SMS 文字短訊。"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project
msgid "Project"
msgstr "專案"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project_stage
msgid "Project Stage"
msgstr "專案階段"

#. module: project_sms
#: model:ir.model.fields,field_description:project_sms.field_project_project_stage__sms_template_id
#: model:ir.model.fields,field_description:project_sms.field_project_task_type__sms_template_id
msgid "SMS Template"
msgstr "簡訊範本"

#. module: project_sms
#: model:ir.actions.act_window,name:project_sms.project_project_act_window_sms_composer
#: model:ir.actions.act_window,name:project_sms.project_task_act_window_sms_composer
msgid "Send SMS Text Message"
msgstr "傳送簡訊"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task
msgid "Task"
msgstr "任務"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task_type
msgid "Task Stage"
msgstr "任務階段"
