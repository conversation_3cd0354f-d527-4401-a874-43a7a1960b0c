# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_sms
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_project_stage__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the project reaches this stage."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة نصية قصيرة إلى العميل تلقائياً عند وصول "
"المشروع إلى هذه المرحلة. "

#. module: project_sms
#: model:ir.model.fields,help:project_sms.field_project_task_type__sms_template_id
msgid ""
"If set, an SMS Text Message will be automatically sent to the customer when "
"the task reaches this stage."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة نصية قصيرة إلى العميل تلقائياً عند وصول "
"المهمة إلى هذه المرحلة. "

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project
msgid "Project"
msgstr "المشروع"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_project_stage
msgid "Project Stage"
msgstr "مرحلة المشروع "

#. module: project_sms
#: model:ir.model.fields,field_description:project_sms.field_project_project_stage__sms_template_id
#: model:ir.model.fields,field_description:project_sms.field_project_task_type__sms_template_id
msgid "SMS Template"
msgstr "قالب الرسائل النصية القصيرة "

#. module: project_sms
#: model:ir.actions.act_window,name:project_sms.project_project_act_window_sms_composer
#: model:ir.actions.act_window,name:project_sms.project_task_act_window_sms_composer
msgid "Send SMS Text Message"
msgstr "إرسال رسالة نصية قصيرة "

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task
msgid "Task"
msgstr "المهمة"

#. module: project_sms
#: model:ir.model,name:project_sms.model_project_task_type
msgid "Task Stage"
msgstr "مرحلة المهمة"
