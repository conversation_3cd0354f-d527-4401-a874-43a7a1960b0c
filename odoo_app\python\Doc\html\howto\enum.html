<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="Enum HOWTO" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/howto/enum.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="An Enum is a set of symbolic names bound to unique values. They are similar to global variables, but they offer a more useful repr(), grouping, type-safety, and a few other features. They are most ..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="An Enum is a set of symbolic names bound to unique values. They are similar to global variables, but they offer a more useful repr(), grouping, type-safety, and a few other features. They are most ..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>Enum HOWTO &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="Functional Programming HOWTO" href="functional.html" />
    <link rel="prev" title="Debugging C API extensions and CPython Internals with GDB" href="gdb_helpers.html" />
    <link rel="canonical" href="https://docs.python.org/3/howto/enum.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Enum HOWTO</a><ul>
<li><a class="reference internal" href="#programmatic-access-to-enumeration-members-and-their-attributes">Programmatic access to enumeration members and their attributes</a></li>
<li><a class="reference internal" href="#duplicating-enum-members-and-values">Duplicating enum members and values</a></li>
<li><a class="reference internal" href="#ensuring-unique-enumeration-values">Ensuring unique enumeration values</a></li>
<li><a class="reference internal" href="#using-automatic-values">Using automatic values</a></li>
<li><a class="reference internal" href="#iteration">Iteration</a></li>
<li><a class="reference internal" href="#comparisons">Comparisons</a></li>
<li><a class="reference internal" href="#allowed-members-and-attributes-of-enumerations">Allowed members and attributes of enumerations</a></li>
<li><a class="reference internal" href="#restricted-enum-subclassing">Restricted Enum subclassing</a></li>
<li><a class="reference internal" href="#dataclass-support">Dataclass support</a></li>
<li><a class="reference internal" href="#pickling">Pickling</a></li>
<li><a class="reference internal" href="#functional-api">Functional API</a></li>
<li><a class="reference internal" href="#derived-enumerations">Derived Enumerations</a><ul>
<li><a class="reference internal" href="#intenum">IntEnum</a></li>
<li><a class="reference internal" href="#strenum">StrEnum</a></li>
<li><a class="reference internal" href="#intflag">IntFlag</a></li>
<li><a class="reference internal" href="#flag">Flag</a></li>
<li><a class="reference internal" href="#others">Others</a></li>
</ul>
</li>
<li><a class="reference internal" href="#when-to-use-new-vs-init">When to use <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> vs. <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a><ul>
<li><a class="reference internal" href="#finer-points">Finer Points</a><ul>
<li><a class="reference internal" href="#supported-dunder-names">Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names</a></li>
<li><a class="reference internal" href="#supported-sunder-names">Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names</a></li>
<li><a class="reference internal" href="#private-names">_Private__names</a></li>
<li><a class="reference internal" href="#enum-member-type"><code class="docutils literal notranslate"><span class="pre">Enum</span></code> member type</a></li>
<li><a class="reference internal" href="#creating-members-that-are-mixed-with-other-data-types">Creating members that are mixed with other data types</a></li>
<li><a class="reference internal" href="#boolean-value-of-enum-classes-and-members">Boolean value of <code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes and members</a></li>
<li><a class="reference internal" href="#enum-classes-with-methods"><code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes with methods</a></li>
<li><a class="reference internal" href="#combining-members-of-flag">Combining members of <code class="docutils literal notranslate"><span class="pre">Flag</span></code></a></li>
<li><a class="reference internal" href="#flag-and-intflag-minutia"><code class="docutils literal notranslate"><span class="pre">Flag</span></code> and <code class="docutils literal notranslate"><span class="pre">IntFlag</span></code> minutia</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#how-are-enums-and-flags-different">How are Enums and Flags different?</a><ul>
<li><a class="reference internal" href="#enum-classes">Enum Classes</a></li>
<li><a class="reference internal" href="#flag-classes">Flag Classes</a></li>
<li><a class="reference internal" href="#enum-members-aka-instances">Enum Members (aka instances)</a></li>
<li><a class="reference internal" href="#flag-members">Flag Members</a></li>
</ul>
</li>
<li><a class="reference internal" href="#enum-cookbook">Enum Cookbook</a><ul>
<li><a class="reference internal" href="#omitting-values">Omitting values</a><ul>
<li><a class="reference internal" href="#using-auto">Using <code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a></li>
<li><a class="reference internal" href="#using-object">Using <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></li>
<li><a class="reference internal" href="#using-a-descriptive-string">Using a descriptive string</a></li>
<li><a class="reference internal" href="#using-a-custom-new">Using a custom <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#orderedenum">OrderedEnum</a></li>
<li><a class="reference internal" href="#duplicatefreeenum">DuplicateFreeEnum</a></li>
<li><a class="reference internal" href="#planet">Planet</a></li>
<li><a class="reference internal" href="#timeperiod">TimePeriod</a></li>
</ul>
</li>
<li><a class="reference internal" href="#subclassing-enumtype">Subclassing EnumType</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gdb_helpers.html"
                          title="previous chapter">Debugging C API extensions and CPython Internals with GDB</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functional.html"
                          title="next chapter">Functional Programming HOWTO</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/howto/enum.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functional.html" title="Functional Programming HOWTO"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="gdb_helpers.html" title="Debugging C API extensions and CPython Internals with GDB"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">Python HOWTOs</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Enum HOWTO</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="enum-howto">
<h1>Enum HOWTO<a class="headerlink" href="#enum-howto" title="Link to this heading">¶</a></h1>
<p id="enum-basic-tutorial">An <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> is a set of symbolic names bound to unique values.  They are
similar to global variables, but they offer a more useful <a class="reference internal" href="../library/functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>,
grouping, type-safety, and a few other features.</p>
<p>They are most useful when you have a variable that can take one of a limited
selection of values.  For example, the days of the week:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TUESDAY</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">WEDNESDAY</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">THURSDAY</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">FRIDAY</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">... </span>    <span class="n">SATURDAY</span> <span class="o">=</span> <span class="mi">6</span>
<span class="gp">... </span>    <span class="n">SUNDAY</span> <span class="o">=</span> <span class="mi">7</span>
</pre></div>
</div>
<p>Or perhaps the RGB primary colors:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">3</span>
</pre></div>
</div>
<p>As you can see, creating an <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> is as simple as writing a class that
inherits from <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> itself.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Case of Enum Members</p>
<p>Because Enums are used to represent constants, and to help avoid issues
with name clashes between mixin-class methods/attributes and enum names,
we strongly recommend using UPPER_CASE names for members, and will be using
that style in our examples.</p>
</div>
<p>Depending on the nature of the enum a member’s value may or may not be
important, but either way that value can be used to get the corresponding
member:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Weekday</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="go">&lt;Weekday.WEDNESDAY: 3&gt;</span>
</pre></div>
</div>
<p>As you can see, the <code class="docutils literal notranslate"><span class="pre">repr()</span></code> of a member shows the enum name, the member name,
and the value.  The <code class="docutils literal notranslate"><span class="pre">str()</span></code> of a member shows only the enum name and member
name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">Weekday</span><span class="o">.</span><span class="n">THURSDAY</span><span class="p">)</span>
<span class="go">Weekday.THURSDAY</span>
</pre></div>
</div>
<p>The <em>type</em> of an enumeration member is the enum it belongs to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">Weekday</span><span class="o">.</span><span class="n">MONDAY</span><span class="p">)</span>
<span class="go">&lt;enum &#39;Weekday&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">isinstance</span><span class="p">(</span><span class="n">Weekday</span><span class="o">.</span><span class="n">FRIDAY</span><span class="p">,</span> <span class="n">Weekday</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Enum members have an attribute that contains just their <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">Weekday</span><span class="o">.</span><span class="n">TUESDAY</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
<span class="go">TUESDAY</span>
</pre></div>
</div>
<p>Likewise, they have an attribute for their <code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Weekday</span><span class="o">.</span><span class="n">WEDNESDAY</span><span class="o">.</span><span class="n">value</span>
<span class="go">3</span>
</pre></div>
</div>
<p>Unlike many languages that treat enumerations solely as name/value pairs,
Python Enums can have behavior added.  For example, <a class="reference internal" href="../library/datetime.html#datetime.date" title="datetime.date"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime.date</span></code></a>
has two methods for returning the weekday: <code class="xref py py-meth docutils literal notranslate"><span class="pre">weekday()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">isoweekday()</span></code>.
The difference is that one of them counts from 0-6 and the other from 1-7.
Rather than keep track of that ourselves we can add a method to the <code class="xref py py-class docutils literal notranslate"><span class="pre">Weekday</span></code>
enum to extract the day from the <code class="xref py py-class docutils literal notranslate"><span class="pre">date</span></code> instance and return the matching
enum member:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="nd">@classmethod</span>
<span class="k">def</span> <span class="nf">from_date</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">date</span><span class="p">):</span>
    <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">date</span><span class="o">.</span><span class="n">isoweekday</span><span class="p">())</span>
</pre></div>
</div>
<p>The complete <code class="xref py py-class docutils literal notranslate"><span class="pre">Weekday</span></code> enum now looks like this:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TUESDAY</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">WEDNESDAY</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">THURSDAY</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">FRIDAY</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">... </span>    <span class="n">SATURDAY</span> <span class="o">=</span> <span class="mi">6</span>
<span class="gp">... </span>    <span class="n">SUNDAY</span> <span class="o">=</span> <span class="mi">7</span>
<span class="gp">... </span>    <span class="c1">#</span>
<span class="gp">... </span>    <span class="nd">@classmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">from_date</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">date</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="n">date</span><span class="o">.</span><span class="n">isoweekday</span><span class="p">())</span>
</pre></div>
</div>
<p>Now we can find out what today is!  Observe:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">date</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Weekday</span><span class="o">.</span><span class="n">from_date</span><span class="p">(</span><span class="n">date</span><span class="o">.</span><span class="n">today</span><span class="p">())</span>     
<span class="go">&lt;Weekday.TUESDAY: 2&gt;</span>
</pre></div>
</div>
<p>Of course, if you’re reading this on some other day, you’ll see that day instead.</p>
<p>This <code class="xref py py-class docutils literal notranslate"><span class="pre">Weekday</span></code> enum is great if our variable only needs one day, but
what if we need several?  Maybe we’re writing a function to plot chores during
a week, and don’t want to use a <a class="reference internal" href="../library/stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> – we could use a different type
of <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TUESDAY</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">WEDNESDAY</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">THURSDAY</span> <span class="o">=</span> <span class="mi">8</span>
<span class="gp">... </span>    <span class="n">FRIDAY</span> <span class="o">=</span> <span class="mi">16</span>
<span class="gp">... </span>    <span class="n">SATURDAY</span> <span class="o">=</span> <span class="mi">32</span>
<span class="gp">... </span>    <span class="n">SUNDAY</span> <span class="o">=</span> <span class="mi">64</span>
</pre></div>
</div>
<p>We’ve changed two things: we’re inherited from <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a>, and the values are
all powers of 2.</p>
<p>Just like the original <code class="xref py py-class docutils literal notranslate"><span class="pre">Weekday</span></code> enum above, we can have a single selection:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">first_week_day</span> <span class="o">=</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">MONDAY</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">first_week_day</span>
<span class="go">&lt;Weekday.MONDAY: 1&gt;</span>
</pre></div>
</div>
<p>But <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> also allows us to combine several members into a single
variable:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">weekend</span> <span class="o">=</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">SATURDAY</span> <span class="o">|</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">SUNDAY</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">weekend</span>
<span class="go">&lt;Weekday.SATURDAY|SUNDAY: 96&gt;</span>
</pre></div>
</div>
<p>You can even iterate over a <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> variable:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">day</span> <span class="ow">in</span> <span class="n">weekend</span><span class="p">:</span>
<span class="gp">... </span>    <span class="nb">print</span><span class="p">(</span><span class="n">day</span><span class="p">)</span>
<span class="go">Weekday.SATURDAY</span>
<span class="go">Weekday.SUNDAY</span>
</pre></div>
</div>
<p>Okay, let’s get some chores set up:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">chores_for_ethan</span> <span class="o">=</span> <span class="p">{</span>
<span class="gp">... </span>    <span class="s1">&#39;feed the cat&#39;</span><span class="p">:</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">MONDAY</span> <span class="o">|</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">WEDNESDAY</span> <span class="o">|</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">FRIDAY</span><span class="p">,</span>
<span class="gp">... </span>    <span class="s1">&#39;do the dishes&#39;</span><span class="p">:</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">TUESDAY</span> <span class="o">|</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">THURSDAY</span><span class="p">,</span>
<span class="gp">... </span>    <span class="s1">&#39;answer SO questions&#39;</span><span class="p">:</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">SATURDAY</span><span class="p">,</span>
<span class="gp">... </span>    <span class="p">}</span>
</pre></div>
</div>
<p>And a function to display the chores for a given day:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">show_chores</span><span class="p">(</span><span class="n">chores</span><span class="p">,</span> <span class="n">day</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">for</span> <span class="n">chore</span><span class="p">,</span> <span class="n">days</span> <span class="ow">in</span> <span class="n">chores</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="n">day</span> <span class="ow">in</span> <span class="n">days</span><span class="p">:</span>
<span class="gp">... </span>            <span class="nb">print</span><span class="p">(</span><span class="n">chore</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">show_chores</span><span class="p">(</span><span class="n">chores_for_ethan</span><span class="p">,</span> <span class="n">Weekday</span><span class="o">.</span><span class="n">SATURDAY</span><span class="p">)</span>
<span class="go">answer SO questions</span>
</pre></div>
</div>
<p>In cases where the actual values of the members do not matter, you can save
yourself some work and use <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-func docutils literal notranslate"><span class="pre">auto()</span></code></a> for the values:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Weekday</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MONDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">TUESDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">WEDNESDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">THURSDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">FRIDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SATURDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SUNDAY</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">WEEKEND</span> <span class="o">=</span> <span class="n">SATURDAY</span> <span class="o">|</span> <span class="n">SUNDAY</span>
</pre></div>
</div>
<section id="programmatic-access-to-enumeration-members-and-their-attributes">
<span id="enum-advanced-tutorial"></span><h2>Programmatic access to enumeration members and their attributes<a class="headerlink" href="#programmatic-access-to-enumeration-members-and-their-attributes" title="Link to this heading">¶</a></h2>
<p>Sometimes it’s useful to access members in enumerations programmatically (i.e.
situations where <code class="docutils literal notranslate"><span class="pre">Color.RED</span></code> won’t do because the exact color is not known
at program-writing time).  <code class="docutils literal notranslate"><span class="pre">Enum</span></code> allows such access:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="go">&lt;Color.RED: 1&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="go">&lt;Color.BLUE: 3&gt;</span>
</pre></div>
</div>
<p>If you want to access enum members by <em>name</em>, use item access:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">[</span><span class="s1">&#39;RED&#39;</span><span class="p">]</span>
<span class="go">&lt;Color.RED: 1&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">[</span><span class="s1">&#39;GREEN&#39;</span><span class="p">]</span>
<span class="go">&lt;Color.GREEN: 2&gt;</span>
</pre></div>
</div>
<p>If you have an enum member and need its <code class="xref py py-attr docutils literal notranslate"><span class="pre">name</span></code> or <code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">member</span> <span class="o">=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">member</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;RED&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">member</span><span class="o">.</span><span class="n">value</span>
<span class="go">1</span>
</pre></div>
</div>
</section>
<section id="duplicating-enum-members-and-values">
<h2>Duplicating enum members and values<a class="headerlink" href="#duplicating-enum-members-and-values" title="Link to this heading">¶</a></h2>
<p>Having two enum members with the same name is invalid:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Shape</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">SQUARE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">SQUARE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&#39;SQUARE&#39; already defined as 2</span>
</pre></div>
</div>
<p>However, an enum member can have other names associated with it.  Given two
entries <code class="docutils literal notranslate"><span class="pre">A</span></code> and <code class="docutils literal notranslate"><span class="pre">B</span></code> with the same value (and <code class="docutils literal notranslate"><span class="pre">A</span></code> defined first), <code class="docutils literal notranslate"><span class="pre">B</span></code>
is an alias for the member <code class="docutils literal notranslate"><span class="pre">A</span></code>.  By-value lookup of the value of <code class="docutils literal notranslate"><span class="pre">A</span></code> will
return the member <code class="docutils literal notranslate"><span class="pre">A</span></code>.  By-name lookup of <code class="docutils literal notranslate"><span class="pre">A</span></code> will return the member <code class="docutils literal notranslate"><span class="pre">A</span></code>.
By-name lookup of <code class="docutils literal notranslate"><span class="pre">B</span></code> will also return the member <code class="docutils literal notranslate"><span class="pre">A</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Shape</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">SQUARE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">DIAMOND</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">CIRCLE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">ALIAS_FOR_SQUARE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="o">.</span><span class="n">SQUARE</span>
<span class="go">&lt;Shape.SQUARE: 2&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="o">.</span><span class="n">ALIAS_FOR_SQUARE</span>
<span class="go">&lt;Shape.SQUARE: 2&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="go">&lt;Shape.SQUARE: 2&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Attempting to create a member with the same name as an already
defined attribute (another member, a method, etc.) or attempting to create
an attribute with the same name as a member is not allowed.</p>
</div>
</section>
<section id="ensuring-unique-enumeration-values">
<h2>Ensuring unique enumeration values<a class="headerlink" href="#ensuring-unique-enumeration-values" title="Link to this heading">¶</a></h2>
<p>By default, enumerations allow multiple names as aliases for the same value.
When this behavior isn’t desired, you can use the <a class="reference internal" href="../library/enum.html#enum.unique" title="enum.unique"><code class="xref py py-func docutils literal notranslate"><span class="pre">unique()</span></code></a> decorator:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">unique</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@unique</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">Mistake</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ONE</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">TWO</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">THREE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">FOUR</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">duplicate values found in &lt;enum &#39;Mistake&#39;&gt;: FOUR -&gt; THREE</span>
</pre></div>
</div>
</section>
<section id="using-automatic-values">
<h2>Using automatic values<a class="headerlink" href="#using-automatic-values" title="Link to this heading">¶</a></h2>
<p>If the exact value is unimportant you can use <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Enum</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">member</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">member</span> <span class="ow">in</span> <span class="n">Color</span><span class="p">]</span>
<span class="go">[1, 2, 3]</span>
</pre></div>
</div>
<p>The values are chosen by <code class="xref py py-func docutils literal notranslate"><span class="pre">_generate_next_value_()</span></code>, which can be
overridden:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">AutoName</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="nd">@staticmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">_generate_next_value_</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">count</span><span class="p">,</span> <span class="n">last_values</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">name</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Ordinal</span><span class="p">(</span><span class="n">AutoName</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">NORTH</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">SOUTH</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">EAST</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">WEST</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">member</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">member</span> <span class="ow">in</span> <span class="n">Ordinal</span><span class="p">]</span>
<span class="go">[&#39;NORTH&#39;, &#39;SOUTH&#39;, &#39;EAST&#39;, &#39;WEST&#39;]</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">_generate_next_value_()</span></code> method must be defined before any members.</p>
</div>
</section>
<section id="iteration">
<h2>Iteration<a class="headerlink" href="#iteration" title="Link to this heading">¶</a></h2>
<p>Iterating over the members of an enum does not provide the aliases:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Shape</span><span class="p">)</span>
<span class="go">[&lt;Shape.SQUARE: 2&gt;, &lt;Shape.DIAMOND: 1&gt;, &lt;Shape.CIRCLE: 3&gt;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Weekday</span><span class="p">)</span>
<span class="go">[&lt;Weekday.MONDAY: 1&gt;, &lt;Weekday.TUESDAY: 2&gt;, &lt;Weekday.WEDNESDAY: 4&gt;, &lt;Weekday.THURSDAY: 8&gt;, &lt;Weekday.FRIDAY: 16&gt;, &lt;Weekday.SATURDAY: 32&gt;, &lt;Weekday.SUNDAY: 64&gt;]</span>
</pre></div>
</div>
<p>Note that the aliases <code class="docutils literal notranslate"><span class="pre">Shape.ALIAS_FOR_SQUARE</span></code> and <code class="docutils literal notranslate"><span class="pre">Weekday.WEEKEND</span></code> aren’t shown.</p>
<p>The special attribute <code class="docutils literal notranslate"><span class="pre">__members__</span></code> is a read-only ordered mapping of names
to members.  It includes all names defined in the enumeration, including the
aliases:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">member</span> <span class="ow">in</span> <span class="n">Shape</span><span class="o">.</span><span class="n">__members__</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
<span class="gp">... </span>    <span class="n">name</span><span class="p">,</span> <span class="n">member</span>
<span class="gp">...</span>
<span class="go">(&#39;SQUARE&#39;, &lt;Shape.SQUARE: 2&gt;)</span>
<span class="go">(&#39;DIAMOND&#39;, &lt;Shape.DIAMOND: 1&gt;)</span>
<span class="go">(&#39;CIRCLE&#39;, &lt;Shape.CIRCLE: 3&gt;)</span>
<span class="go">(&#39;ALIAS_FOR_SQUARE&#39;, &lt;Shape.SQUARE: 2&gt;)</span>
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">__members__</span></code> attribute can be used for detailed programmatic access to
the enumeration members.  For example, finding all the aliases:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">name</span> <span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">member</span> <span class="ow">in</span> <span class="n">Shape</span><span class="o">.</span><span class="n">__members__</span><span class="o">.</span><span class="n">items</span><span class="p">()</span> <span class="k">if</span> <span class="n">member</span><span class="o">.</span><span class="n">name</span> <span class="o">!=</span> <span class="n">name</span><span class="p">]</span>
<span class="go">[&#39;ALIAS_FOR_SQUARE&#39;]</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Aliases for flags include values with multiple flags set, such as <code class="docutils literal notranslate"><span class="pre">3</span></code>,
and no flags set, i.e. <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</div>
</section>
<section id="comparisons">
<h2>Comparisons<a class="headerlink" href="#comparisons" title="Link to this heading">¶</a></h2>
<p>Enumeration members are compared by identity:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="ow">is</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="ow">is</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="ow">is</span> <span class="ow">not</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Ordered comparisons between enumeration values are <em>not</em> supported.  Enum
members are not integers (but see <a class="reference internal" href="#intenum">IntEnum</a> below):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">&lt;</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="gt">Traceback (most recent call last):</span>
  File <span class="nb">&quot;&lt;stdin&gt;&quot;</span>, line <span class="m">1</span>, in <span class="n">&lt;module&gt;</span>
<span class="gr">TypeError</span>: <span class="n">&#39;&lt;&#39; not supported between instances of &#39;Color&#39; and &#39;Color&#39;</span>
</pre></div>
</div>
<p>Equality comparisons are defined though:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span> <span class="o">==</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span> <span class="o">!=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span> <span class="o">==</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">True</span>
</pre></div>
</div>
<p>Comparisons against non-enumeration values will always compare not equal
(again, <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> was explicitly designed to behave differently, see
below):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span> <span class="o">==</span> <span class="mi">2</span>
<span class="go">False</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>It is possible to reload modules – if a reloaded module contains
enums, they will be recreated, and the new members may not
compare identical/equal to the original members.</p>
</div>
</section>
<section id="allowed-members-and-attributes-of-enumerations">
<h2>Allowed members and attributes of enumerations<a class="headerlink" href="#allowed-members-and-attributes-of-enumerations" title="Link to this heading">¶</a></h2>
<p>Most of the examples above use integers for enumeration values.  Using integers
is short and handy (and provided by default by the <a class="reference internal" href="#functional-api">Functional API</a>), but not
strictly enforced.  In the vast majority of use-cases, one doesn’t care what
the actual value of an enumeration is.  But if the value <em>is</em> important,
enumerations can have arbitrary values.</p>
<p>Enumerations are Python classes, and can have methods and special methods as
usual.  If we have this enumeration:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Mood</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">FUNKY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">HAPPY</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">...</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">describe</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="c1"># self is the member here</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span>
<span class="gp">...</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__str__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="s1">&#39;my custom str! </span><span class="si">{0}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">... </span>    <span class="nd">@classmethod</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">favorite_mood</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="gp">... </span>        <span class="c1"># cls here is the enumeration</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">cls</span><span class="o">.</span><span class="n">HAPPY</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>Then:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Mood</span><span class="o">.</span><span class="n">favorite_mood</span><span class="p">()</span>
<span class="go">&lt;Mood.HAPPY: 3&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Mood</span><span class="o">.</span><span class="n">HAPPY</span><span class="o">.</span><span class="n">describe</span><span class="p">()</span>
<span class="go">(&#39;HAPPY&#39;, 3)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">str</span><span class="p">(</span><span class="n">Mood</span><span class="o">.</span><span class="n">FUNKY</span><span class="p">)</span>
<span class="go">&#39;my custom str! 1&#39;</span>
</pre></div>
</div>
<p>The rules for what is allowed are as follows: names that start and end with
a single underscore are reserved by enum and cannot be used; all other
attributes defined within an enumeration will become members of this
enumeration, with the exception of special methods (<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__add__()</span></code>, etc.), descriptors (methods are also descriptors), and
variable names listed in <code class="xref py py-attr docutils literal notranslate"><span class="pre">_ignore_</span></code>.</p>
<p>Note:  if your enumeration defines <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> and/or <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>,
any value(s) given to the enum member will be passed into those methods.
See <a class="reference internal" href="#planet">Planet</a> for an example.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> method, if defined, is used during creation of the Enum
members; it is then replaced by Enum’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> which is used after
class creation for lookup of existing members.  See <a class="reference internal" href="#new-vs-init"><span class="std std-ref">When to use __new__() vs. __init__()</span></a> for
more details.</p>
</div>
</section>
<section id="restricted-enum-subclassing">
<h2>Restricted Enum subclassing<a class="headerlink" href="#restricted-enum-subclassing" title="Link to this heading">¶</a></h2>
<p>A new <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> class must have one base enum class, up to one concrete
data type, and as many <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a>-based mixin classes as needed.  The
order of these base classes is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">EnumName</span><span class="p">([</span><span class="n">mix</span><span class="o">-</span><span class="ow">in</span><span class="p">,</span> <span class="o">...</span><span class="p">,]</span> <span class="p">[</span><span class="n">data</span><span class="o">-</span><span class="nb">type</span><span class="p">,]</span> <span class="n">base</span><span class="o">-</span><span class="n">enum</span><span class="p">):</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p>Also, subclassing an enumeration is allowed only if the enumeration does not define
any members.  So this is forbidden:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MoreColor</span><span class="p">(</span><span class="n">Color</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">PINK</span> <span class="o">=</span> <span class="mi">17</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">&lt;enum &#39;MoreColor&#39;&gt; cannot extend &lt;enum &#39;Color&#39;&gt;</span>
</pre></div>
</div>
<p>But this is allowed:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Foo</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">some_behavior</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">pass</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Bar</span><span class="p">(</span><span class="n">Foo</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">HAPPY</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">SAD</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>Allowing subclassing of enums that define members would lead to a violation of
some important invariants of types and instances.  On the other hand, it makes
sense to allow sharing some common behavior between a group of enumerations.
(See <a class="reference internal" href="#orderedenum">OrderedEnum</a> for an example.)</p>
</section>
<section id="dataclass-support">
<span id="enum-dataclass-support"></span><h2>Dataclass support<a class="headerlink" href="#dataclass-support" title="Link to this heading">¶</a></h2>
<p>When inheriting from a <a class="reference internal" href="../library/dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-class docutils literal notranslate"><span class="pre">dataclass</span></code></a>,
the <a class="reference internal" href="../library/enum.html#enum.Enum.__repr__" title="enum.Enum.__repr__"><code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code></a> omits the inherited class’ name.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span><span class="p">,</span> <span class="n">field</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nd">@dataclass</span>
<span class="gp">... </span><span class="k">class</span> <span class="nc">CreatureDataMixin</span><span class="p">:</span>
<span class="gp">... </span>    <span class="n">size</span><span class="p">:</span> <span class="nb">str</span>
<span class="gp">... </span>    <span class="n">legs</span><span class="p">:</span> <span class="nb">int</span>
<span class="gp">... </span>    <span class="n">tail</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">field</span><span class="p">(</span><span class="nb">repr</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Creature</span><span class="p">(</span><span class="n">CreatureDataMixin</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">BEETLE</span> <span class="o">=</span> <span class="s1">&#39;small&#39;</span><span class="p">,</span> <span class="mi">6</span>
<span class="gp">... </span>    <span class="n">DOG</span> <span class="o">=</span> <span class="s1">&#39;medium&#39;</span><span class="p">,</span> <span class="mi">4</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Creature</span><span class="o">.</span><span class="n">DOG</span>
<span class="go">&lt;Creature.DOG: size=&#39;medium&#39;, legs=4&gt;</span>
</pre></div>
</div>
<p>Use the <code class="xref py py-func docutils literal notranslate"><span class="pre">dataclass()</span></code> argument <code class="docutils literal notranslate"><span class="pre">repr=False</span></code>
to use the standard <a class="reference internal" href="../library/functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a>.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.12: </span>Only the dataclass fields are shown in the value area, not the dataclass’
name.</p>
</div>
</section>
<section id="pickling">
<h2>Pickling<a class="headerlink" href="#pickling" title="Link to this heading">¶</a></h2>
<p>Enumerations can be pickled and unpickled:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">test.test_enum</span> <span class="kn">import</span> <span class="n">Fruit</span>
<span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">pickle</span> <span class="kn">import</span> <span class="n">dumps</span><span class="p">,</span> <span class="n">loads</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Fruit</span><span class="o">.</span><span class="n">TOMATO</span> <span class="ow">is</span> <span class="n">loads</span><span class="p">(</span><span class="n">dumps</span><span class="p">(</span><span class="n">Fruit</span><span class="o">.</span><span class="n">TOMATO</span><span class="p">))</span>
<span class="go">True</span>
</pre></div>
</div>
<p>The usual restrictions for pickling apply: picklable enums must be defined in
the top level of a module, since unpickling requires them to be importable
from that module.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>With pickle protocol version 4 it is possible to easily pickle enums
nested in other classes.</p>
</div>
<p>It is possible to modify how enum members are pickled/unpickled by defining
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__reduce_ex__()</span></code> in the enumeration class.  The default method is by-value,
but enums with complicated values may want to use by-name:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">enum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyEnum</span><span class="p">(</span><span class="n">enum</span><span class="o">.</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">__reduce_ex__</span> <span class="o">=</span> <span class="n">enum</span><span class="o">.</span><span class="n">pickle_by_enum_name</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Using by-name for flags is not recommended, as unnamed aliases will
not unpickle.</p>
</div>
</section>
<section id="functional-api">
<h2>Functional API<a class="headerlink" href="#functional-api" title="Link to this heading">¶</a></h2>
<p>The <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> class is callable, providing the following functional API:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Animal</span> <span class="o">=</span> <span class="n">Enum</span><span class="p">(</span><span class="s1">&#39;Animal&#39;</span><span class="p">,</span> <span class="s1">&#39;ANT BEE CAT DOG&#39;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Animal</span>
<span class="go">&lt;enum &#39;Animal&#39;&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Animal</span><span class="o">.</span><span class="n">ANT</span>
<span class="go">&lt;Animal.ANT: 1&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Animal</span><span class="p">)</span>
<span class="go">[&lt;Animal.ANT: 1&gt;, &lt;Animal.BEE: 2&gt;, &lt;Animal.CAT: 3&gt;, &lt;Animal.DOG: 4&gt;]</span>
</pre></div>
</div>
<p>The semantics of this API resemble <a class="reference internal" href="../library/collections.html#collections.namedtuple" title="collections.namedtuple"><code class="xref py py-class docutils literal notranslate"><span class="pre">namedtuple</span></code></a>. The first
argument of the call to <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> is the name of the enumeration.</p>
<p>The second argument is the <em>source</em> of enumeration member names.  It can be a
whitespace-separated string of names, a sequence of names, a sequence of
2-tuples with key/value pairs, or a mapping (e.g. dictionary) of names to
values.  The last two options enable assigning arbitrary values to
enumerations; the others auto-assign increasing integers starting with 1 (use
the <code class="docutils literal notranslate"><span class="pre">start</span></code> parameter to specify a different starting value).  A
new class derived from <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> is returned.  In other words, the above
assignment to <code class="xref py py-class docutils literal notranslate"><span class="pre">Animal</span></code> is equivalent to:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Animal</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">ANT</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">BEE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">CAT</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">DOG</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>The reason for defaulting to <code class="docutils literal notranslate"><span class="pre">1</span></code> as the starting number and not <code class="docutils literal notranslate"><span class="pre">0</span></code> is
that <code class="docutils literal notranslate"><span class="pre">0</span></code> is <code class="docutils literal notranslate"><span class="pre">False</span></code> in a boolean sense, but by default enum members all
evaluate to <code class="docutils literal notranslate"><span class="pre">True</span></code>.</p>
<p>Pickling enums created with the functional API can be tricky as frame stack
implementation details are used to try and figure out which module the
enumeration is being created in (e.g. it will fail if you use a utility
function in a separate module, and also may not work on IronPython or Jython).
The solution is to specify the module name explicitly as follows:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Animal</span> <span class="o">=</span> <span class="n">Enum</span><span class="p">(</span><span class="s1">&#39;Animal&#39;</span><span class="p">,</span> <span class="s1">&#39;ANT BEE CAT DOG&#39;</span><span class="p">,</span> <span class="n">module</span><span class="o">=</span><span class="vm">__name__</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>If <code class="docutils literal notranslate"><span class="pre">module</span></code> is not supplied, and Enum cannot determine what it is,
the new Enum members will not be unpicklable; to keep errors closer to
the source, pickling will be disabled.</p>
</div>
<p>The new pickle protocol 4 also, in some circumstances, relies on
<a class="reference internal" href="../library/stdtypes.html#definition.__qualname__" title="definition.__qualname__"><code class="xref py py-attr docutils literal notranslate"><span class="pre">__qualname__</span></code></a> being set to the location where pickle will be able
to find the class.  For example, if the class was made available in class
SomeData in the global scope:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Animal</span> <span class="o">=</span> <span class="n">Enum</span><span class="p">(</span><span class="s1">&#39;Animal&#39;</span><span class="p">,</span> <span class="s1">&#39;ANT BEE CAT DOG&#39;</span><span class="p">,</span> <span class="n">qualname</span><span class="o">=</span><span class="s1">&#39;SomeData.Animal&#39;</span><span class="p">)</span>
</pre></div>
</div>
<p>The complete signature is:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">Enum</span><span class="p">(</span>
    <span class="n">value</span><span class="o">=</span><span class="s1">&#39;NewEnumName&#39;</span><span class="p">,</span>
    <span class="n">names</span><span class="o">=&lt;...&gt;</span><span class="p">,</span>
    <span class="o">*</span><span class="p">,</span>
    <span class="n">module</span><span class="o">=</span><span class="s1">&#39;...&#39;</span><span class="p">,</span>
    <span class="n">qualname</span><span class="o">=</span><span class="s1">&#39;...&#39;</span><span class="p">,</span>
    <span class="nb">type</span><span class="o">=&lt;</span><span class="n">mixed</span><span class="o">-</span><span class="ow">in</span> <span class="n">class</span><span class="o">&gt;</span><span class="p">,</span>
    <span class="n">start</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
    <span class="p">)</span>
</pre></div>
</div>
<ul>
<li><p><em>value</em>: What the new enum class will record as its name.</p></li>
<li><p><em>names</em>: The enum members.  This can be a whitespace- or comma-separated string
(values will start at 1 unless otherwise specified):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;RED GREEN BLUE&#39;</span> <span class="o">|</span> <span class="s1">&#39;RED,GREEN,BLUE&#39;</span> <span class="o">|</span> <span class="s1">&#39;RED, GREEN, BLUE&#39;</span>
</pre></div>
</div>
<p>or an iterator of names:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[</span><span class="s1">&#39;RED&#39;</span><span class="p">,</span> <span class="s1">&#39;GREEN&#39;</span><span class="p">,</span> <span class="s1">&#39;BLUE&#39;</span><span class="p">]</span>
</pre></div>
</div>
<p>or an iterator of (name, value) pairs:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">[(</span><span class="s1">&#39;CYAN&#39;</span><span class="p">,</span> <span class="mi">4</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;MAGENTA&#39;</span><span class="p">,</span> <span class="mi">5</span><span class="p">),</span> <span class="p">(</span><span class="s1">&#39;YELLOW&#39;</span><span class="p">,</span> <span class="mi">6</span><span class="p">)]</span>
</pre></div>
</div>
<p>or a mapping:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="p">{</span><span class="s1">&#39;CHARTREUSE&#39;</span><span class="p">:</span> <span class="mi">7</span><span class="p">,</span> <span class="s1">&#39;SEA_GREEN&#39;</span><span class="p">:</span> <span class="mi">11</span><span class="p">,</span> <span class="s1">&#39;ROSEMARY&#39;</span><span class="p">:</span> <span class="mi">42</span><span class="p">}</span>
</pre></div>
</div>
</li>
<li><p><em>module</em>: name of module where new enum class can be found.</p></li>
<li><p><em>qualname</em>: where in module new enum class can be found.</p></li>
<li><p><em>type</em>: type to mix in to new enum class.</p></li>
<li><p><em>start</em>: number to start counting at if only names are passed in.</p></li>
</ul>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5: </span>The <em>start</em> parameter was added.</p>
</div>
</section>
<section id="derived-enumerations">
<h2>Derived Enumerations<a class="headerlink" href="#derived-enumerations" title="Link to this heading">¶</a></h2>
<section id="intenum">
<h3>IntEnum<a class="headerlink" href="#intenum" title="Link to this heading">¶</a></h3>
<p>The first variation of <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> that is provided is also a subclass of
<a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.  Members of an <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> can be compared to integers;
by extension, integer enumerations of different types can also be compared
to each other:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">IntEnum</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Shape</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">CIRCLE</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">SQUARE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Request</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">POST</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GET</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span> <span class="o">==</span> <span class="mi">1</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="o">.</span><span class="n">CIRCLE</span> <span class="o">==</span> <span class="mi">1</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="o">.</span><span class="n">CIRCLE</span> <span class="o">==</span> <span class="n">Request</span><span class="o">.</span><span class="n">POST</span>
<span class="go">True</span>
</pre></div>
</div>
<p>However, they still can’t be compared to standard <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> enumerations:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Shape</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">CIRCLE</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">SQUARE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Shape</span><span class="o">.</span><span class="n">CIRCLE</span> <span class="o">==</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">False</span>
</pre></div>
</div>
<p><a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> values behave like integers in other ways you’d expect:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">int</span><span class="p">(</span><span class="n">Shape</span><span class="o">.</span><span class="n">CIRCLE</span><span class="p">)</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="s1">&#39;a&#39;</span><span class="p">,</span> <span class="s1">&#39;b&#39;</span><span class="p">,</span> <span class="s1">&#39;c&#39;</span><span class="p">][</span><span class="n">Shape</span><span class="o">.</span><span class="n">CIRCLE</span><span class="p">]</span>
<span class="go">&#39;b&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">Shape</span><span class="o">.</span><span class="n">SQUARE</span><span class="p">)]</span>
<span class="go">[0, 1]</span>
</pre></div>
</div>
</section>
<section id="strenum">
<h3>StrEnum<a class="headerlink" href="#strenum" title="Link to this heading">¶</a></h3>
<p>The second variation of <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> that is provided is also a subclass of
<a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>.  Members of a <a class="reference internal" href="../library/enum.html#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a> can be compared to strings;
by extension, string enumerations of different types can also be compared
to each other.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</section>
<section id="intflag">
<h3>IntFlag<a class="headerlink" href="#intflag" title="Link to this heading">¶</a></h3>
<p>The next variation of <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> provided, <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>, is also based
on <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.  The difference being <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> members can be combined
using the bitwise operators (&amp;, |, ^, ~) and the result is still an
<a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> member, if possible.  Like <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>
members are also integers and can be used wherever an <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> is used.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Any operation on an <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> member besides the bit-wise operations will
lose the <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> membership.</p>
<p>Bit-wise operations that result in invalid <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> values will lose the
<a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> membership.  See <a class="reference internal" href="../library/enum.html#enum.FlagBoundary" title="enum.FlagBoundary"><code class="xref py py-class docutils literal notranslate"><span class="pre">FlagBoundary</span></code></a> for
details.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.</span></p>
</div>
<p>Sample <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">IntFlag</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Perm</span><span class="p">(</span><span class="n">IntFlag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">R</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">W</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">X</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="o">|</span> <span class="n">Perm</span><span class="o">.</span><span class="n">W</span>
<span class="go">&lt;Perm.R|W: 6&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="o">+</span> <span class="n">Perm</span><span class="o">.</span><span class="n">W</span>
<span class="go">6</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">RW</span> <span class="o">=</span> <span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="o">|</span> <span class="n">Perm</span><span class="o">.</span><span class="n">W</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="ow">in</span> <span class="n">RW</span>
<span class="go">True</span>
</pre></div>
</div>
<p>It is also possible to name the combinations:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Perm</span><span class="p">(</span><span class="n">IntFlag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">R</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">W</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">X</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">RWX</span> <span class="o">=</span> <span class="mi">7</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">RWX</span>
<span class="go">&lt;Perm.RWX: 7&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">Perm</span><span class="o">.</span><span class="n">RWX</span>
<span class="go">&lt;Perm: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>
<span class="go">&lt;Perm.RWX: 7&gt;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Named combinations are considered aliases.  Aliases do not show up during
iteration, but can be returned from by-value lookups.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.</span></p>
</div>
<p>Another important difference between <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> and <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> is that
if no flags are set (the value is 0), its boolean evaluation is <a class="reference internal" href="../library/constants.html#False" title="False"><code class="xref py py-data docutils literal notranslate"><span class="pre">False</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="o">&amp;</span> <span class="n">Perm</span><span class="o">.</span><span class="n">X</span>
<span class="go">&lt;Perm: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span> <span class="o">&amp;</span> <span class="n">Perm</span><span class="o">.</span><span class="n">X</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Because <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> members are also subclasses of <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> they can
be combined with them (but may lose <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> membership:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">X</span> <span class="o">|</span> <span class="mi">4</span>
<span class="go">&lt;Perm.R|X: 5&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Perm</span><span class="o">.</span><span class="n">X</span> <span class="o">+</span> <span class="mi">8</span>
<span class="go">9</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The negation operator, <code class="docutils literal notranslate"><span class="pre">~</span></code>, always returns an <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> member with a
positive value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="o">~</span><span class="n">Perm</span><span class="o">.</span><span class="n">X</span><span class="p">)</span><span class="o">.</span><span class="n">value</span> <span class="o">==</span> <span class="p">(</span><span class="n">Perm</span><span class="o">.</span><span class="n">R</span><span class="o">|</span><span class="n">Perm</span><span class="o">.</span><span class="n">W</span><span class="p">)</span><span class="o">.</span><span class="n">value</span> <span class="o">==</span> <span class="mi">6</span>
<span class="go">True</span>
</pre></div>
</div>
</div>
<p><a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> members can also be iterated over:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">RW</span><span class="p">)</span>
<span class="go">[&lt;Perm.R: 4&gt;, &lt;Perm.W: 2&gt;]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
</section>
<section id="flag">
<h3>Flag<a class="headerlink" href="#flag" title="Link to this heading">¶</a></h3>
<p>The last variation is <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a>.  Like <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a>
members can be combined using the bitwise operators (&amp;, |, ^, ~).  Unlike
<a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>, they cannot be combined with, nor compared against, any
other <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> enumeration, nor <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.  While it is possible to
specify the values directly it is recommended to use <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> as the
value and let <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> select an appropriate value.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Like <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>, if a combination of <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> members results in no
flags being set, the boolean evaluation is <a class="reference internal" href="../library/constants.html#False" title="False"><code class="xref py py-data docutils literal notranslate"><span class="pre">False</span></code></a>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">enum</span> <span class="kn">import</span> <span class="n">Flag</span><span class="p">,</span> <span class="n">auto</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">&amp;</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">&amp;</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p>Individual flags should have values that are powers of two (1, 2, 4, 8, …),
while combinations of flags will not:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">WHITE</span> <span class="o">=</span> <span class="n">RED</span> <span class="o">|</span> <span class="n">BLUE</span> <span class="o">|</span> <span class="n">GREEN</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">WHITE</span>
<span class="go">&lt;Color.WHITE: 7&gt;</span>
</pre></div>
</div>
<p>Giving a name to the “no flags set” condition does not change its boolean
value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">BLACK</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLACK</span>
<span class="go">&lt;Color.BLACK: 0&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">bool</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">BLACK</span><span class="p">)</span>
<span class="go">False</span>
</pre></div>
</div>
<p><a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> members can also be iterated over:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">purple</span> <span class="o">=</span> <span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">purple</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;, &lt;Color.BLUE: 2&gt;]</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For the majority of new code, <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> and <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> are strongly
recommended, since <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> and <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> break some
semantic promises of an enumeration (by being comparable to integers, and
thus by transitivity to other unrelated enumerations).  <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>
and <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> should be used only in cases where <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> and
<a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> will not do; for example, when integer constants are replaced
with enumerations, or for interoperability with other systems.</p>
</div>
</section>
<section id="others">
<h3>Others<a class="headerlink" href="#others" title="Link to this heading">¶</a></h3>
<p>While <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> is part of the <a class="reference internal" href="../library/enum.html#module-enum" title="enum: Implementation of an enumeration class."><code class="xref py py-mod docutils literal notranslate"><span class="pre">enum</span></code></a> module, it would be very
simple to implement independently:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">IntEnum</span><span class="p">(</span><span class="nb">int</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p>This demonstrates how similar derived enumerations can be defined; for example
a <code class="xref py py-class docutils literal notranslate"><span class="pre">FloatEnum</span></code> that mixes in <a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-class docutils literal notranslate"><span class="pre">float</span></code></a> instead of <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>.</p>
<p>Some rules:</p>
<ol class="arabic simple">
<li><p>When subclassing <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>, mix-in types must appear before
<a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> itself in the sequence of bases, as in the <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>
example above.</p></li>
<li><p>Mix-in types must be subclassable. For example, <a class="reference internal" href="../library/functions.html#bool" title="bool"><code class="xref py py-class docutils literal notranslate"><span class="pre">bool</span></code></a> and
<a class="reference internal" href="../library/stdtypes.html#range" title="range"><code class="xref py py-class docutils literal notranslate"><span class="pre">range</span></code></a> are not subclassable and will throw an error during Enum
creation if used as the mix-in type.</p></li>
<li><p>While <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> can have members of any type, once you mix in an
additional type, all the members must have values of that type, e.g.
<a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> above.  This restriction does not apply to mix-ins which only
add methods and don’t specify another type.</p></li>
<li><p>When another data type is mixed in, the <code class="xref py py-attr docutils literal notranslate"><span class="pre">value</span></code> attribute is <em>not the
same</em> as the enum member itself, although it is equivalent and will compare
equal.</p></li>
<li><p>A <code class="docutils literal notranslate"><span class="pre">data</span> <span class="pre">type</span></code> is a mixin that defines <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code>, or a
<a class="reference internal" href="../library/dataclasses.html#dataclasses.dataclass" title="dataclasses.dataclass"><code class="xref py py-class docutils literal notranslate"><span class="pre">dataclass</span></code></a></p></li>
<li><p>%-style formatting:  <code class="docutils literal notranslate"><span class="pre">%s</span></code> and <code class="docutils literal notranslate"><span class="pre">%r</span></code> call the <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> class’s
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code> respectively; other codes (such as
<code class="docutils literal notranslate"><span class="pre">%i</span></code> or <code class="docutils literal notranslate"><span class="pre">%h</span></code> for IntEnum) treat the enum member as its mixed-in type.</p></li>
<li><p><a class="reference internal" href="../reference/lexical_analysis.html#f-strings"><span class="std std-ref">Formatted string literals</span></a>, <a class="reference internal" href="../library/stdtypes.html#str.format" title="str.format"><code class="xref py py-meth docutils literal notranslate"><span class="pre">str.format()</span></code></a>,
and <a class="reference internal" href="../library/functions.html#format" title="format"><code class="xref py py-func docutils literal notranslate"><span class="pre">format()</span></code></a> will use the enum’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code> method.</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Because <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a>, and <a class="reference internal" href="../library/enum.html#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a> are
designed to be drop-in replacements for existing constants, their
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code> method has been reset to their data types’
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code> method.</p>
</div>
</section>
</section>
<section id="when-to-use-new-vs-init">
<span id="new-vs-init"></span><h2>When to use <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> vs. <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code><a class="headerlink" href="#when-to-use-new-vs-init" title="Link to this heading">¶</a></h2>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> must be used whenever you want to customize the actual value of
the <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> member.  Any other modifications may go in either
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code>, with <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> being preferred.</p>
<p>For example, if you want to pass several items to the constructor, but only
want one of them to be the value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Coordinate</span><span class="p">(</span><span class="nb">bytes</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span><span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="gp">... </span><span class="sd">    Coordinate with binary codes that can be indexed by the int code.</span>
<span class="gp">... </span><span class="sd">    &quot;&quot;&quot;</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">label</span><span class="p">,</span> <span class="n">unit</span><span class="p">):</span>
<span class="gp">... </span>        <span class="n">obj</span> <span class="o">=</span> <span class="nb">bytes</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="p">[</span><span class="n">value</span><span class="p">])</span>
<span class="gp">... </span>        <span class="n">obj</span><span class="o">.</span><span class="n">_value_</span> <span class="o">=</span> <span class="n">value</span>
<span class="gp">... </span>        <span class="n">obj</span><span class="o">.</span><span class="n">label</span> <span class="o">=</span> <span class="n">label</span>
<span class="gp">... </span>        <span class="n">obj</span><span class="o">.</span><span class="n">unit</span> <span class="o">=</span> <span class="n">unit</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">obj</span>
<span class="gp">... </span>    <span class="n">PX</span> <span class="o">=</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="s1">&#39;P.X&#39;</span><span class="p">,</span> <span class="s1">&#39;km&#39;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">PY</span> <span class="o">=</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="s1">&#39;P.Y&#39;</span><span class="p">,</span> <span class="s1">&#39;km&#39;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">VX</span> <span class="o">=</span> <span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="s1">&#39;V.X&#39;</span><span class="p">,</span> <span class="s1">&#39;km/s&#39;</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">VY</span> <span class="o">=</span> <span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="s1">&#39;V.Y&#39;</span><span class="p">,</span> <span class="s1">&#39;km/s&#39;</span><span class="p">)</span>
<span class="gp">...</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">Coordinate</span><span class="p">[</span><span class="s1">&#39;PY&#39;</span><span class="p">])</span>
<span class="go">Coordinate.PY</span>

<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">Coordinate</span><span class="p">(</span><span class="mi">3</span><span class="p">))</span>
<span class="go">Coordinate.VY</span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><em>Do not</em> call <code class="docutils literal notranslate"><span class="pre">super().__new__()</span></code>, as the lookup-only <code class="docutils literal notranslate"><span class="pre">__new__</span></code> is the one
that is found; instead, use the data type directly.</p>
</div>
<section id="finer-points">
<h3>Finer Points<a class="headerlink" href="#finer-points" title="Link to this heading">¶</a></h3>
<section id="supported-dunder-names">
<h4>Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names<a class="headerlink" href="#supported-dunder-names" title="Link to this heading">¶</a></h4>
<p><code class="xref py py-attr docutils literal notranslate"><span class="pre">__members__</span></code> is a read-only ordered mapping of <code class="docutils literal notranslate"><span class="pre">member_name</span></code>:<code class="docutils literal notranslate"><span class="pre">member</span></code>
items.  It is only available on the class.</p>
<p><code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code>, if specified, must create and return the enum members; it is
also a very good idea to set the member’s <code class="xref py py-attr docutils literal notranslate"><span class="pre">_value_</span></code> appropriately.  Once
all the members are created it is no longer used.</p>
</section>
<section id="supported-sunder-names">
<h4>Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names<a class="headerlink" href="#supported-sunder-names" title="Link to this heading">¶</a></h4>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">_name_</span></code> – name of the member</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">_value_</span></code> – value of the member; can be set / modified in <code class="docutils literal notranslate"><span class="pre">__new__</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">_missing_</span></code> – a lookup function used when a value is not found; may be
overridden</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">_ignore_</span></code> – a list of names, either as a <a class="reference internal" href="../library/stdtypes.html#list" title="list"><code class="xref py py-class docutils literal notranslate"><span class="pre">list</span></code></a> or a <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>,
that will not be transformed into members, and will be removed from the final
class</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">_order_</span></code> – used in Python 2/3 code to ensure member order is consistent
(class attribute, removed during class creation)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">_generate_next_value_</span></code> – used by the <a class="reference internal" href="#functional-api">Functional API</a> and by
<a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> to get an appropriate value for an enum member; may be
overridden</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>For standard <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> classes the next value chosen is the last value seen
incremented by one.</p>
<p>For <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> classes the next value chosen will be the next highest
power-of-two, regardless of the last value seen.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span><code class="docutils literal notranslate"><span class="pre">_missing_</span></code>, <code class="docutils literal notranslate"><span class="pre">_order_</span></code>, <code class="docutils literal notranslate"><span class="pre">_generate_next_value_</span></code></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span><code class="docutils literal notranslate"><span class="pre">_ignore_</span></code></p>
</div>
<p>To help keep Python 2 / Python 3 code in sync an <code class="xref py py-attr docutils literal notranslate"><span class="pre">_order_</span></code> attribute can
be provided.  It will be checked against the actual order of the enumeration
and raise an error if the two do not match:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">_order_</span> <span class="o">=</span> <span class="s1">&#39;RED GREEN BLUE&#39;</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="c">...</span>
<span class="gr">TypeError</span>: <span class="n">member order does not match _order_:</span>
<span class="x">  [&#39;RED&#39;, &#39;BLUE&#39;, &#39;GREEN&#39;]</span>
<span class="x">  [&#39;RED&#39;, &#39;GREEN&#39;, &#39;BLUE&#39;]</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In Python 2 code the <code class="xref py py-attr docutils literal notranslate"><span class="pre">_order_</span></code> attribute is necessary as definition
order is lost before it can be recorded.</p>
</div>
</section>
<section id="private-names">
<h4>_Private__names<a class="headerlink" href="#private-names" title="Link to this heading">¶</a></h4>
<p><a class="reference internal" href="../reference/expressions.html#private-name-mangling"><span class="std std-ref">Private names</span></a> are not converted to enum members,
but remain normal attributes.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.11.</span></p>
</div>
</section>
<section id="enum-member-type">
<h4><code class="docutils literal notranslate"><span class="pre">Enum</span></code> member type<a class="headerlink" href="#enum-member-type" title="Link to this heading">¶</a></h4>
<p>Enum members are instances of their enum class, and are normally accessed as
<code class="docutils literal notranslate"><span class="pre">EnumClass.member</span></code>.  In certain situations, such as writing custom enum
behavior, being able to access one member directly from another is useful,
and is supported; however, in order to avoid name clashes between member names
and attributes/methods from mixed-in classes, upper-case names are strongly
recommended.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.5.</span></p>
</div>
</section>
<section id="creating-members-that-are-mixed-with-other-data-types">
<h4>Creating members that are mixed with other data types<a class="headerlink" href="#creating-members-that-are-mixed-with-other-data-types" title="Link to this heading">¶</a></h4>
<p>When subclassing other data types, such as <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> or <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, with
an <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>, all values after the <code class="docutils literal notranslate"><span class="pre">=</span></code> are passed to that data type’s
constructor.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">MyEnum</span><span class="p">(</span><span class="n">IntEnum</span><span class="p">):</span>      <span class="c1"># help(int) -&gt; int(x, base=10) -&gt; integer</span>
<span class="gp">... </span>    <span class="n">example</span> <span class="o">=</span> <span class="s1">&#39;11&#39;</span><span class="p">,</span> <span class="mi">16</span>      <span class="c1"># so x=&#39;11&#39; and base=16</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">MyEnum</span><span class="o">.</span><span class="n">example</span><span class="o">.</span><span class="n">value</span>        <span class="c1"># and hex(11) is...</span>
<span class="go">17</span>
</pre></div>
</div>
</section>
<section id="boolean-value-of-enum-classes-and-members">
<h4>Boolean value of <code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes and members<a class="headerlink" href="#boolean-value-of-enum-classes-and-members" title="Link to this heading">¶</a></h4>
<p>Enum classes that are mixed with non-<a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> types (such as
<a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a>, <a class="reference internal" href="../library/stdtypes.html#str" title="str"><code class="xref py py-class docutils literal notranslate"><span class="pre">str</span></code></a>, etc.) are evaluated according to the mixed-in
type’s rules; otherwise, all members evaluate as <a class="reference internal" href="../library/constants.html#True" title="True"><code class="xref py py-data docutils literal notranslate"><span class="pre">True</span></code></a>.  To make your
own enum’s boolean evaluation depend on the member’s value add the following to
your class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="fm">__bool__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="k">return</span> <span class="nb">bool</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
</pre></div>
</div>
<p>Plain <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> classes always evaluate as <a class="reference internal" href="../library/constants.html#True" title="True"><code class="xref py py-data docutils literal notranslate"><span class="pre">True</span></code></a>.</p>
</section>
<section id="enum-classes-with-methods">
<h4><code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes with methods<a class="headerlink" href="#enum-classes-with-methods" title="Link to this heading">¶</a></h4>
<p>If you give your enum subclass extra methods, like the <a class="reference internal" href="#planet">Planet</a>
class below, those methods will show up in a <a class="reference internal" href="../library/functions.html#dir" title="dir"><code class="xref py py-func docutils literal notranslate"><span class="pre">dir()</span></code></a> of the member,
but not of the class:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">Planet</span><span class="p">)</span>                         
<span class="go">[&#39;EARTH&#39;, &#39;JUPITER&#39;, &#39;MARS&#39;, &#39;MERCURY&#39;, &#39;NEPTUNE&#39;, &#39;SATURN&#39;, &#39;URANUS&#39;, &#39;VENUS&#39;, &#39;__class__&#39;, &#39;__doc__&#39;, &#39;__members__&#39;, &#39;__module__&#39;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">dir</span><span class="p">(</span><span class="n">Planet</span><span class="o">.</span><span class="n">EARTH</span><span class="p">)</span>                   
<span class="go">[&#39;__class__&#39;, &#39;__doc__&#39;, &#39;__module__&#39;, &#39;mass&#39;, &#39;name&#39;, &#39;radius&#39;, &#39;surface_gravity&#39;, &#39;value&#39;]</span>
</pre></div>
</div>
</section>
<section id="combining-members-of-flag">
<h4>Combining members of <code class="docutils literal notranslate"><span class="pre">Flag</span></code><a class="headerlink" href="#combining-members-of-flag" title="Link to this heading">¶</a></h4>
<p>Iterating over a combination of <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> members will only return the members that
are comprised of a single bit:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Flag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">MAGENTA</span> <span class="o">=</span> <span class="n">RED</span> <span class="o">|</span> <span class="n">BLUE</span>
<span class="gp">... </span>    <span class="n">YELLOW</span> <span class="o">=</span> <span class="n">RED</span> <span class="o">|</span> <span class="n">GREEN</span>
<span class="gp">... </span>    <span class="n">CYAN</span> <span class="o">=</span> <span class="n">GREEN</span> <span class="o">|</span> <span class="n">BLUE</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>  <span class="c1"># named combination</span>
<span class="go">&lt;Color.YELLOW: 3&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>      <span class="c1"># not named combination</span>
<span class="go">&lt;Color.RED|GREEN|BLUE: 7&gt;</span>
</pre></div>
</div>
</section>
<section id="flag-and-intflag-minutia">
<h4><code class="docutils literal notranslate"><span class="pre">Flag</span></code> and <code class="docutils literal notranslate"><span class="pre">IntFlag</span></code> minutia<a class="headerlink" href="#flag-and-intflag-minutia" title="Link to this heading">¶</a></h4>
<p>Using the following snippet for our examples:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">IntFlag</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">BLACK</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">PURPLE</span> <span class="o">=</span> <span class="n">RED</span> <span class="o">|</span> <span class="n">BLUE</span>
<span class="gp">... </span>    <span class="n">WHITE</span> <span class="o">=</span> <span class="n">RED</span> <span class="o">|</span> <span class="n">GREEN</span> <span class="o">|</span> <span class="n">BLUE</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>the following are true:</p>
<ul>
<li><p>single-bit flags are canonical</p></li>
<li><p>multi-bit and zero-bit flags are aliases</p></li>
<li><p>only canonical flags are returned during iteration:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">WHITE</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;, &lt;Color.GREEN: 2&gt;, &lt;Color.BLUE: 4&gt;]</span>
</pre></div>
</div>
</li>
<li><p>negating a flag or flag set returns a new flag/flag set with the
corresponding positive integer value:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">&lt;Color.BLUE: 4&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">&lt;Color.RED|GREEN: 3&gt;</span>
</pre></div>
</div>
</li>
<li><p>names of pseudo-flags are constructed from their members’ names:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
<span class="go">&#39;RED|GREEN&#39;</span>
</pre></div>
</div>
</li>
<li><p>multi-bit flags, aka aliases, can be returned from operations:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span> <span class="o">|</span> <span class="n">Color</span><span class="o">.</span><span class="n">BLUE</span>
<span class="go">&lt;Color.PURPLE: 5&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span>  <span class="c1"># or Color(-1)</span>
<span class="go">&lt;Color.WHITE: 7&gt;</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="go">&lt;Color.BLACK: 0&gt;</span>
</pre></div>
</div>
</li>
<li><p>membership / containment checking: zero-valued flags are always considered
to be contained:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">BLACK</span> <span class="ow">in</span> <span class="n">Color</span><span class="o">.</span><span class="n">WHITE</span>
<span class="go">True</span>
</pre></div>
</div>
<p>otherwise, only if all bits of one flag are in the other flag will True
be returned:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">PURPLE</span> <span class="ow">in</span> <span class="n">Color</span><span class="o">.</span><span class="n">WHITE</span>
<span class="go">True</span>

<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span> <span class="ow">in</span> <span class="n">Color</span><span class="o">.</span><span class="n">PURPLE</span>
<span class="go">False</span>
</pre></div>
</div>
</li>
</ul>
<p>There is a new boundary mechanism that controls how out-of-range / invalid
bits are handled: <code class="docutils literal notranslate"><span class="pre">STRICT</span></code>, <code class="docutils literal notranslate"><span class="pre">CONFORM</span></code>, <code class="docutils literal notranslate"><span class="pre">EJECT</span></code>, and <code class="docutils literal notranslate"><span class="pre">KEEP</span></code>:</p>
<ul class="simple">
<li><p>STRICT –&gt; raises an exception when presented with invalid values</p></li>
<li><p>CONFORM –&gt; discards any invalid bits</p></li>
<li><p>EJECT –&gt; lose Flag status and become a normal int with the given value</p></li>
<li><p>KEEP –&gt; keep the extra bits</p>
<ul>
<li><p>keeps Flag status and extra bits</p></li>
<li><p>extra bits do not show up in iteration</p></li>
<li><p>extra bits do show up in repr() and str()</p></li>
</ul>
</li>
</ul>
<p>The default for Flag is <code class="docutils literal notranslate"><span class="pre">STRICT</span></code>, the default for <code class="docutils literal notranslate"><span class="pre">IntFlag</span></code> is <code class="docutils literal notranslate"><span class="pre">EJECT</span></code>,
and the default for <code class="docutils literal notranslate"><span class="pre">_convert_</span></code> is <code class="docutils literal notranslate"><span class="pre">KEEP</span></code> (see <code class="docutils literal notranslate"><span class="pre">ssl.Options</span></code> for an
example of when <code class="docutils literal notranslate"><span class="pre">KEEP</span></code> is needed).</p>
</section>
</section>
</section>
<section id="how-are-enums-and-flags-different">
<span id="enum-class-differences"></span><h2>How are Enums and Flags different?<a class="headerlink" href="#how-are-enums-and-flags-different" title="Link to this heading">¶</a></h2>
<p>Enums have a custom metaclass that affects many aspects of both derived <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>
classes and their instances (members).</p>
<section id="enum-classes">
<h3>Enum Classes<a class="headerlink" href="#enum-classes" title="Link to this heading">¶</a></h3>
<p>The <a class="reference internal" href="../library/enum.html#enum.EnumType" title="enum.EnumType"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumType</span></code></a> metaclass is responsible for providing the
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__contains__()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">__dir__()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">__iter__()</span></code> and other methods that
allow one to do things with an <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> class that fail on a typical
class, such as <code class="docutils literal notranslate"><span class="pre">list(Color)</span></code> or <code class="docutils literal notranslate"><span class="pre">some_enum_var</span> <span class="pre">in</span> <span class="pre">Color</span></code>.  <a class="reference internal" href="../library/enum.html#enum.EnumType" title="enum.EnumType"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumType</span></code></a> is
responsible for ensuring that various other methods on the final <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>
class are correct (such as <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code>, <code class="xref py py-meth docutils literal notranslate"><span class="pre">__getnewargs__()</span></code>,
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__str__()</span></code> and <code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code>).</p>
</section>
<section id="flag-classes">
<h3>Flag Classes<a class="headerlink" href="#flag-classes" title="Link to this heading">¶</a></h3>
<p>Flags have an expanded view of aliasing: to be canonical, the value of a flag
needs to be a power-of-two value, and not a duplicate name.  So, in addition to the
<a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> definition of alias, a flag with no value (a.k.a. <code class="docutils literal notranslate"><span class="pre">0</span></code>) or with more than one
power-of-two value (e.g. <code class="docutils literal notranslate"><span class="pre">3</span></code>) is considered an alias.</p>
</section>
<section id="enum-members-aka-instances">
<h3>Enum Members (aka instances)<a class="headerlink" href="#enum-members-aka-instances" title="Link to this heading">¶</a></h3>
<p>The most interesting thing about enum members is that they are singletons.
<a class="reference internal" href="../library/enum.html#enum.EnumType" title="enum.EnumType"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumType</span></code></a> creates them all while it is creating the enum class itself,
and then puts a custom <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> in place to ensure that no new ones are
ever instantiated by returning only the existing member instances.</p>
</section>
<section id="flag-members">
<h3>Flag Members<a class="headerlink" href="#flag-members" title="Link to this heading">¶</a></h3>
<p>Flag members can be iterated over just like the <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a> class, and only the
canonical members will be returned.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Color</span><span class="p">)</span>
<span class="go">[&lt;Color.RED: 1&gt;, &lt;Color.GREEN: 2&gt;, &lt;Color.BLUE: 4&gt;]</span>
</pre></div>
</div>
<p>(Note that <code class="docutils literal notranslate"><span class="pre">BLACK</span></code>, <code class="docutils literal notranslate"><span class="pre">PURPLE</span></code>, and <code class="docutils literal notranslate"><span class="pre">WHITE</span></code> do not show up.)</p>
<p>Inverting a flag member returns the corresponding positive value,
rather than a negative value — for example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="o">~</span><span class="n">Color</span><span class="o">.</span><span class="n">RED</span>
<span class="go">&lt;Color.GREEN|BLUE: 6&gt;</span>
</pre></div>
</div>
<p>Flag members have a length corresponding to the number of power-of-two values
they contain.  For example:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">Color</span><span class="o">.</span><span class="n">PURPLE</span><span class="p">)</span>
<span class="go">2</span>
</pre></div>
</div>
</section>
</section>
<section id="enum-cookbook">
<span id="id1"></span><h2>Enum Cookbook<a class="headerlink" href="#enum-cookbook" title="Link to this heading">¶</a></h2>
<p>While <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.StrEnum" title="enum.StrEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">StrEnum</span></code></a>, <a class="reference internal" href="../library/enum.html#enum.Flag" title="enum.Flag"><code class="xref py py-class docutils literal notranslate"><span class="pre">Flag</span></code></a>, and
<a class="reference internal" href="../library/enum.html#enum.IntFlag" title="enum.IntFlag"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntFlag</span></code></a> are expected to cover the majority of use-cases, they cannot
cover them all.  Here are recipes for some different types of enumerations
that can be used directly, or as examples for creating one’s own.</p>
<section id="omitting-values">
<h3>Omitting values<a class="headerlink" href="#omitting-values" title="Link to this heading">¶</a></h3>
<p>In many use-cases, one doesn’t care what the actual value of an enumeration
is. There are several ways to define this type of simple enumeration:</p>
<ul class="simple">
<li><p>use instances of <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> for the value</p></li>
<li><p>use instances of <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> as the value</p></li>
<li><p>use a descriptive string as the value</p></li>
<li><p>use a tuple as the value and a custom <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> to replace the
tuple with an <a class="reference internal" href="../library/functions.html#int" title="int"><code class="xref py py-class docutils literal notranslate"><span class="pre">int</span></code></a> value</p></li>
</ul>
<p>Using any of these methods signifies to the user that these values are not
important, and also enables one to add, remove, or reorder members without
having to renumber the remaining members.</p>
<section id="using-auto">
<h4>Using <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a><a class="headerlink" href="#using-auto" title="Link to this heading">¶</a></h4>
<p>Using <a class="reference internal" href="../library/enum.html#enum.auto" title="enum.auto"><code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a> would look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="n">auto</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.GREEN: 3&gt;</span>
</pre></div>
</div>
</section>
<section id="using-object">
<h4>Using <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a><a class="headerlink" href="#using-object" title="Link to this heading">¶</a></h4>
<p>Using <a class="reference internal" href="../library/functions.html#object" title="object"><code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a> would look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>                         
<span class="go">&lt;Color.GREEN: &lt;object object at 0x...&gt;&gt;</span>
</pre></div>
</div>
<p>This is also a good example of why you might want to write your own
<code class="xref py py-meth docutils literal notranslate"><span class="pre">__repr__()</span></code>:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="nb">object</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__repr__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="s2">&quot;&lt;</span><span class="si">%s</span><span class="s2">.</span><span class="si">%s</span><span class="s2">&gt;&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span><span class="o">.</span><span class="vm">__name__</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_name_</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.GREEN&gt;</span>
</pre></div>
</div>
</section>
<section id="using-a-descriptive-string">
<h4>Using a descriptive string<a class="headerlink" href="#using-a-descriptive-string" title="Link to this heading">¶</a></h4>
<p>Using a string as the value would look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="s1">&#39;stop&#39;</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="s1">&#39;go&#39;</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="s1">&#39;too fast!&#39;</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.GREEN: &#39;go&#39;&gt;</span>
</pre></div>
</div>
</section>
<section id="using-a-custom-new">
<h4>Using a custom <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code><a class="headerlink" href="#using-a-custom-new" title="Link to this heading">¶</a></h4>
<p>Using an auto-numbering <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> would look like:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">AutoNumber</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="gp">... </span>        <span class="n">value</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">__members__</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">... </span>        <span class="n">obj</span> <span class="o">=</span> <span class="nb">object</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>
<span class="gp">... </span>        <span class="n">obj</span><span class="o">.</span><span class="n">_value_</span> <span class="o">=</span> <span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">obj</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">AutoNumber</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="p">()</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="p">()</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="p">()</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Color</span><span class="o">.</span><span class="n">GREEN</span>
<span class="go">&lt;Color.GREEN: 2&gt;</span>
</pre></div>
</div>
<p>To make a more general purpose <code class="docutils literal notranslate"><span class="pre">AutoNumber</span></code>, add <code class="docutils literal notranslate"><span class="pre">*args</span></code> to the signature:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">AutoNumber</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span>      <span class="c1"># this is the only change from above</span>
<span class="gp">... </span>        <span class="n">value</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">__members__</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">... </span>        <span class="n">obj</span> <span class="o">=</span> <span class="nb">object</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>
<span class="gp">... </span>        <span class="n">obj</span><span class="o">.</span><span class="n">_value_</span> <span class="o">=</span> <span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">obj</span>
<span class="gp">...</span>
</pre></div>
</div>
<p>Then when you inherit from <code class="docutils literal notranslate"><span class="pre">AutoNumber</span></code> you can write your own <code class="docutils literal notranslate"><span class="pre">__init__</span></code>
to handle any extra arguments:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Swatch</span><span class="p">(</span><span class="n">AutoNumber</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">pantone</span><span class="o">=</span><span class="s1">&#39;unknown&#39;</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">pantone</span> <span class="o">=</span> <span class="n">pantone</span>
<span class="gp">... </span>    <span class="n">AUBURN</span> <span class="o">=</span> <span class="s1">&#39;3497&#39;</span>
<span class="gp">... </span>    <span class="n">SEA_GREEN</span> <span class="o">=</span> <span class="s1">&#39;1246&#39;</span>
<span class="gp">... </span>    <span class="n">BLEACHED_CORAL</span> <span class="o">=</span> <span class="p">()</span> <span class="c1"># New color, no Pantone code yet!</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Swatch</span><span class="o">.</span><span class="n">SEA_GREEN</span>
<span class="go">&lt;Swatch.SEA_GREEN: 2&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Swatch</span><span class="o">.</span><span class="n">SEA_GREEN</span><span class="o">.</span><span class="n">pantone</span>
<span class="go">&#39;1246&#39;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Swatch</span><span class="o">.</span><span class="n">BLEACHED_CORAL</span><span class="o">.</span><span class="n">pantone</span>
<span class="go">&#39;unknown&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> method, if defined, is used during creation of the Enum
members; it is then replaced by Enum’s <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> which is used after
class creation for lookup of existing members.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><em>Do not</em> call <code class="docutils literal notranslate"><span class="pre">super().__new__()</span></code>, as the lookup-only <code class="docutils literal notranslate"><span class="pre">__new__</span></code> is the one
that is found; instead, use the data type directly – e.g.:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="n">obj</span> <span class="o">=</span> <span class="nb">int</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
</pre></div>
</div>
</div>
</section>
</section>
<section id="orderedenum">
<h3>OrderedEnum<a class="headerlink" href="#orderedenum" title="Link to this heading">¶</a></h3>
<p>An ordered enumeration that is not based on <a class="reference internal" href="../library/enum.html#enum.IntEnum" title="enum.IntEnum"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntEnum</span></code></a> and so maintains
the normal <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> invariants (such as not being comparable to other
enumerations):</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">OrderedEnum</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__ge__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span> <span class="ow">is</span> <span class="n">other</span><span class="o">.</span><span class="vm">__class__</span><span class="p">:</span>
<span class="gp">... </span>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">&gt;=</span> <span class="n">other</span><span class="o">.</span><span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">NotImplemented</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__gt__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span> <span class="ow">is</span> <span class="n">other</span><span class="o">.</span><span class="vm">__class__</span><span class="p">:</span>
<span class="gp">... </span>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">&gt;</span> <span class="n">other</span><span class="o">.</span><span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">NotImplemented</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__le__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span> <span class="ow">is</span> <span class="n">other</span><span class="o">.</span><span class="vm">__class__</span><span class="p">:</span>
<span class="gp">... </span>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">&lt;=</span> <span class="n">other</span><span class="o">.</span><span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">NotImplemented</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__lt__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">other</span><span class="p">):</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span> <span class="ow">is</span> <span class="n">other</span><span class="o">.</span><span class="vm">__class__</span><span class="p">:</span>
<span class="gp">... </span>            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">&lt;</span> <span class="n">other</span><span class="o">.</span><span class="n">value</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="bp">NotImplemented</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Grade</span><span class="p">(</span><span class="n">OrderedEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">A</span> <span class="o">=</span> <span class="mi">5</span>
<span class="gp">... </span>    <span class="n">B</span> <span class="o">=</span> <span class="mi">4</span>
<span class="gp">... </span>    <span class="n">C</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">D</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">F</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Grade</span><span class="o">.</span><span class="n">C</span> <span class="o">&lt;</span> <span class="n">Grade</span><span class="o">.</span><span class="n">A</span>
<span class="go">True</span>
</pre></div>
</div>
</section>
<section id="duplicatefreeenum">
<h3>DuplicateFreeEnum<a class="headerlink" href="#duplicatefreeenum" title="Link to this heading">¶</a></h3>
<p>Raises an error if a duplicate member value is found instead of creating an
alias:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">DuplicateFreeEnum</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">cls</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="vm">__class__</span>
<span class="gp">... </span>        <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span> <span class="o">==</span> <span class="n">e</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">e</span> <span class="ow">in</span> <span class="bp">cls</span><span class="p">):</span>
<span class="gp">... </span>            <span class="n">a</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">name</span>
<span class="gp">... </span>            <span class="n">e</span> <span class="o">=</span> <span class="bp">cls</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">value</span><span class="p">)</span><span class="o">.</span><span class="n">name</span>
<span class="gp">... </span>            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span>
<span class="gp">... </span>                <span class="s2">&quot;aliases not allowed in DuplicateFreeEnum:  </span><span class="si">%r</span><span class="s2"> --&gt; </span><span class="si">%r</span><span class="s2">&quot;</span>
<span class="gp">... </span>                <span class="o">%</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">e</span><span class="p">))</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Color</span><span class="p">(</span><span class="n">DuplicateFreeEnum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">RED</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">... </span>    <span class="n">GREEN</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">... </span>    <span class="n">BLUE</span> <span class="o">=</span> <span class="mi">3</span>
<span class="gp">... </span>    <span class="n">GRENE</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">...</span>
<span class="gt">Traceback (most recent call last):</span>
<span class="w">  </span><span class="c">...</span>
<span class="gr">ValueError</span>: <span class="n">aliases not allowed in DuplicateFreeEnum:  &#39;GRENE&#39; --&gt; &#39;GREEN&#39;</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This is a useful example for subclassing Enum to add or change other
behaviors as well as disallowing aliases.  If the only desired change is
disallowing aliases, the <a class="reference internal" href="../library/enum.html#enum.unique" title="enum.unique"><code class="xref py py-func docutils literal notranslate"><span class="pre">unique()</span></code></a> decorator can be used instead.</p>
</div>
</section>
<section id="planet">
<h3>Planet<a class="headerlink" href="#planet" title="Link to this heading">¶</a></h3>
<p>If <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> or <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code> is defined, the value of the enum member
will be passed to those methods:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Planet</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="n">MERCURY</span> <span class="o">=</span> <span class="p">(</span><span class="mf">3.303e+23</span><span class="p">,</span> <span class="mf">2.4397e6</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">VENUS</span>   <span class="o">=</span> <span class="p">(</span><span class="mf">4.869e+24</span><span class="p">,</span> <span class="mf">6.0518e6</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">EARTH</span>   <span class="o">=</span> <span class="p">(</span><span class="mf">5.976e+24</span><span class="p">,</span> <span class="mf">6.37814e6</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">MARS</span>    <span class="o">=</span> <span class="p">(</span><span class="mf">6.421e+23</span><span class="p">,</span> <span class="mf">3.3972e6</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">JUPITER</span> <span class="o">=</span> <span class="p">(</span><span class="mf">1.9e+27</span><span class="p">,</span>   <span class="mf">7.1492e7</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">SATURN</span>  <span class="o">=</span> <span class="p">(</span><span class="mf">5.688e+26</span><span class="p">,</span> <span class="mf">6.0268e7</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">URANUS</span>  <span class="o">=</span> <span class="p">(</span><span class="mf">8.686e+25</span><span class="p">,</span> <span class="mf">2.5559e7</span><span class="p">)</span>
<span class="gp">... </span>    <span class="n">NEPTUNE</span> <span class="o">=</span> <span class="p">(</span><span class="mf">1.024e+26</span><span class="p">,</span> <span class="mf">2.4746e7</span><span class="p">)</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">mass</span><span class="p">,</span> <span class="n">radius</span><span class="p">):</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">mass</span> <span class="o">=</span> <span class="n">mass</span>       <span class="c1"># in kilograms</span>
<span class="gp">... </span>        <span class="bp">self</span><span class="o">.</span><span class="n">radius</span> <span class="o">=</span> <span class="n">radius</span>   <span class="c1"># in meters</span>
<span class="gp">... </span>    <span class="nd">@property</span>
<span class="gp">... </span>    <span class="k">def</span> <span class="nf">surface_gravity</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="gp">... </span>        <span class="c1"># universal gravitational constant  (m3 kg-1 s-2)</span>
<span class="gp">... </span>        <span class="n">G</span> <span class="o">=</span> <span class="mf">6.67300E-11</span>
<span class="gp">... </span>        <span class="k">return</span> <span class="n">G</span> <span class="o">*</span> <span class="bp">self</span><span class="o">.</span><span class="n">mass</span> <span class="o">/</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">radius</span> <span class="o">*</span> <span class="bp">self</span><span class="o">.</span><span class="n">radius</span><span class="p">)</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Planet</span><span class="o">.</span><span class="n">EARTH</span><span class="o">.</span><span class="n">value</span>
<span class="go">(5.976e+24, 6378140.0)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">Planet</span><span class="o">.</span><span class="n">EARTH</span><span class="o">.</span><span class="n">surface_gravity</span>
<span class="go">9.802652743337129</span>
</pre></div>
</div>
</section>
<section id="timeperiod">
<span id="enum-time-period"></span><h3>TimePeriod<a class="headerlink" href="#timeperiod" title="Link to this heading">¶</a></h3>
<p>An example to show the <code class="xref py py-attr docutils literal notranslate"><span class="pre">_ignore_</span></code> attribute in use:</p>
<div class="highlight-python3 notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">timedelta</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Period</span><span class="p">(</span><span class="n">timedelta</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
<span class="gp">... </span>    <span class="s2">&quot;different lengths of time&quot;</span>
<span class="gp">... </span>    <span class="n">_ignore_</span> <span class="o">=</span> <span class="s1">&#39;Period i&#39;</span>
<span class="gp">... </span>    <span class="n">Period</span> <span class="o">=</span> <span class="nb">vars</span><span class="p">()</span>
<span class="gp">... </span>    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">367</span><span class="p">):</span>
<span class="gp">... </span>        <span class="n">Period</span><span class="p">[</span><span class="s1">&#39;day_</span><span class="si">%d</span><span class="s1">&#39;</span> <span class="o">%</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">i</span>
<span class="gp">...</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Period</span><span class="p">)[:</span><span class="mi">2</span><span class="p">]</span>
<span class="go">[&lt;Period.day_0: datetime.timedelta(0)&gt;, &lt;Period.day_1: datetime.timedelta(days=1)&gt;]</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">list</span><span class="p">(</span><span class="n">Period</span><span class="p">)[</span><span class="o">-</span><span class="mi">2</span><span class="p">:]</span>
<span class="go">[&lt;Period.day_365: datetime.timedelta(days=365)&gt;, &lt;Period.day_366: datetime.timedelta(days=366)&gt;]</span>
</pre></div>
</div>
</section>
</section>
<section id="subclassing-enumtype">
<span id="enumtype-examples"></span><h2>Subclassing EnumType<a class="headerlink" href="#subclassing-enumtype" title="Link to this heading">¶</a></h2>
<p>While most enum needs can be met by customizing <a class="reference internal" href="../library/enum.html#enum.Enum" title="enum.Enum"><code class="xref py py-class docutils literal notranslate"><span class="pre">Enum</span></code></a> subclasses,
either with class decorators or custom functions, <a class="reference internal" href="../library/enum.html#enum.EnumType" title="enum.EnumType"><code class="xref py py-class docutils literal notranslate"><span class="pre">EnumType</span></code></a> can be
subclassed to provide a different Enum experience.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Enum HOWTO</a><ul>
<li><a class="reference internal" href="#programmatic-access-to-enumeration-members-and-their-attributes">Programmatic access to enumeration members and their attributes</a></li>
<li><a class="reference internal" href="#duplicating-enum-members-and-values">Duplicating enum members and values</a></li>
<li><a class="reference internal" href="#ensuring-unique-enumeration-values">Ensuring unique enumeration values</a></li>
<li><a class="reference internal" href="#using-automatic-values">Using automatic values</a></li>
<li><a class="reference internal" href="#iteration">Iteration</a></li>
<li><a class="reference internal" href="#comparisons">Comparisons</a></li>
<li><a class="reference internal" href="#allowed-members-and-attributes-of-enumerations">Allowed members and attributes of enumerations</a></li>
<li><a class="reference internal" href="#restricted-enum-subclassing">Restricted Enum subclassing</a></li>
<li><a class="reference internal" href="#dataclass-support">Dataclass support</a></li>
<li><a class="reference internal" href="#pickling">Pickling</a></li>
<li><a class="reference internal" href="#functional-api">Functional API</a></li>
<li><a class="reference internal" href="#derived-enumerations">Derived Enumerations</a><ul>
<li><a class="reference internal" href="#intenum">IntEnum</a></li>
<li><a class="reference internal" href="#strenum">StrEnum</a></li>
<li><a class="reference internal" href="#intflag">IntFlag</a></li>
<li><a class="reference internal" href="#flag">Flag</a></li>
<li><a class="reference internal" href="#others">Others</a></li>
</ul>
</li>
<li><a class="reference internal" href="#when-to-use-new-vs-init">When to use <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code> vs. <code class="xref py py-meth docutils literal notranslate"><span class="pre">__init__()</span></code></a><ul>
<li><a class="reference internal" href="#finer-points">Finer Points</a><ul>
<li><a class="reference internal" href="#supported-dunder-names">Supported <code class="docutils literal notranslate"><span class="pre">__dunder__</span></code> names</a></li>
<li><a class="reference internal" href="#supported-sunder-names">Supported <code class="docutils literal notranslate"><span class="pre">_sunder_</span></code> names</a></li>
<li><a class="reference internal" href="#private-names">_Private__names</a></li>
<li><a class="reference internal" href="#enum-member-type"><code class="docutils literal notranslate"><span class="pre">Enum</span></code> member type</a></li>
<li><a class="reference internal" href="#creating-members-that-are-mixed-with-other-data-types">Creating members that are mixed with other data types</a></li>
<li><a class="reference internal" href="#boolean-value-of-enum-classes-and-members">Boolean value of <code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes and members</a></li>
<li><a class="reference internal" href="#enum-classes-with-methods"><code class="docutils literal notranslate"><span class="pre">Enum</span></code> classes with methods</a></li>
<li><a class="reference internal" href="#combining-members-of-flag">Combining members of <code class="docutils literal notranslate"><span class="pre">Flag</span></code></a></li>
<li><a class="reference internal" href="#flag-and-intflag-minutia"><code class="docutils literal notranslate"><span class="pre">Flag</span></code> and <code class="docutils literal notranslate"><span class="pre">IntFlag</span></code> minutia</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#how-are-enums-and-flags-different">How are Enums and Flags different?</a><ul>
<li><a class="reference internal" href="#enum-classes">Enum Classes</a></li>
<li><a class="reference internal" href="#flag-classes">Flag Classes</a></li>
<li><a class="reference internal" href="#enum-members-aka-instances">Enum Members (aka instances)</a></li>
<li><a class="reference internal" href="#flag-members">Flag Members</a></li>
</ul>
</li>
<li><a class="reference internal" href="#enum-cookbook">Enum Cookbook</a><ul>
<li><a class="reference internal" href="#omitting-values">Omitting values</a><ul>
<li><a class="reference internal" href="#using-auto">Using <code class="xref py py-class docutils literal notranslate"><span class="pre">auto</span></code></a></li>
<li><a class="reference internal" href="#using-object">Using <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></a></li>
<li><a class="reference internal" href="#using-a-descriptive-string">Using a descriptive string</a></li>
<li><a class="reference internal" href="#using-a-custom-new">Using a custom <code class="xref py py-meth docutils literal notranslate"><span class="pre">__new__()</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#orderedenum">OrderedEnum</a></li>
<li><a class="reference internal" href="#duplicatefreeenum">DuplicateFreeEnum</a></li>
<li><a class="reference internal" href="#planet">Planet</a></li>
<li><a class="reference internal" href="#timeperiod">TimePeriod</a></li>
</ul>
</li>
<li><a class="reference internal" href="#subclassing-enumtype">Subclassing EnumType</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="gdb_helpers.html"
                          title="previous chapter">Debugging C API extensions and CPython Internals with GDB</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="functional.html"
                          title="next chapter">Functional Programming HOWTO</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/howto/enum.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="functional.html" title="Functional Programming HOWTO"
             >next</a> |</li>
        <li class="right" >
          <a href="gdb_helpers.html" title="Debugging C API extensions and CPython Internals with GDB"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python HOWTOs</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Enum HOWTO</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>