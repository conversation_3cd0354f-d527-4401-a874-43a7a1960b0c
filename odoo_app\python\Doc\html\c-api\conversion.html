<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta property="og:title" content="String conversion and formatting" />
<meta property="og:type" content="website" />
<meta property="og:url" content="https://docs.python.org/3/c-api/conversion.html" />
<meta property="og:site_name" content="Python documentation" />
<meta property="og:description" content="Functions for number conversion and formatted string output. PyOS_snprintf() and PyOS_vsnprintf() wrap the Standard C library functions snprintf() and vsnprintf(). Their purpose is to guarantee con..." />
<meta property="og:image" content="https://docs.python.org/3/_static/og-image.png" />
<meta property="og:image:alt" content="Python documentation" />
<meta name="description" content="Functions for number conversion and formatted string output. PyOS_snprintf() and PyOS_vsnprintf() wrap the Standard C library functions snprintf() and vsnprintf(). Their purpose is to guarantee con..." />
<meta property="og:image:width" content="200" />
<meta property="og:image:height" content="200" />
<meta name="theme-color" content="#3776ab" />

    <title>String conversion and formatting &#8212; Python 3.12.3 documentation</title><meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
    <link rel="stylesheet" type="text/css" href="../_static/pydoctheme.css?v=bb723527" />
    <link id="pygments_dark_css" media="(prefers-color-scheme: dark)" rel="stylesheet" type="text/css" href="../_static/pygments_dark.css?v=b20cc3f5" />
    
    <script src="../_static/documentation_options.js?v=2c828074"></script>
    <script src="../_static/doctools.js?v=888ff710"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <script src="../_static/sidebar.js"></script>
    
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.12.3 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="next" title="PyHash API" href="hash.html" />
    <link rel="prev" title="Parsing arguments and building values" href="arg.html" />
    <link rel="canonical" href="https://docs.python.org/3/c-api/conversion.html" />
    
      
    

    
    <style>
      @media only screen {
        table.full-width-table {
            width: 100%;
        }
      }
    </style>
<link rel="stylesheet" href="../_static/pydoctheme_dark.css" media="(prefers-color-scheme: dark)" id="pydoctheme_dark_css">
    <link rel="shortcut icon" type="image/png" href="../_static/py.svg" />
            <script type="text/javascript" src="../_static/copybutton.js"></script>
            <script type="text/javascript" src="../_static/menu.js"></script>
            <script type="text/javascript" src="../_static/search-focus.js"></script>
            <script type="text/javascript" src="../_static/themetoggle.js"></script> 

  </head>
<body>
<div class="mobile-nav">
    <input type="checkbox" id="menuToggler" class="toggler__input" aria-controls="navigation"
           aria-pressed="false" aria-expanded="false" role="button" aria-label="Menu" />
    <nav class="nav-content" role="navigation">
        <label for="menuToggler" class="toggler__label">
            <span></span>
        </label>
        <span class="nav-items-wrapper">
            <a href="https://www.python.org/" class="nav-logo">
                <img src="../_static/py.svg" alt="Python logo"/>
            </a>
            <span class="version_switcher_placeholder"></span>
            <form role="search" class="search" action="../search.html" method="get">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" class="search-icon">
                    <path fill-rule="nonzero" fill="currentColor" d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 001.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 00-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 005.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
                </svg>
                <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" />
                <input type="submit" value="Go"/>
            </form>
        </span>
    </nav>
    <div class="menu-wrapper">
        <nav class="menu" role="navigation" aria-label="main navigation">
            <div class="language_switcher_placeholder"></div>
            
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="arg.html"
                          title="previous chapter">Parsing arguments and building values</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="hash.html"
                          title="next chapter">PyHash API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/conversion.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </nav>
    </div>
</div>

  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="hash.html" title="PyHash API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="arg.html" title="Parsing arguments and building values"
             accesskey="P">previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" accesskey="U">Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">String conversion and formatting</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>    

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="string-conversion-and-formatting">
<span id="string-conversion"></span><h1>String conversion and formatting<a class="headerlink" href="#string-conversion-and-formatting" title="Link to this heading">¶</a></h1>
<p>Functions for number conversion and formatted string output.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_snprintf">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_snprintf</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">size</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">format</span></span>, <span class="p"><span class="pre">...</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_snprintf" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Output not more than <em>size</em> bytes to <em>str</em> according to the format string
<em>format</em> and the extra arguments. See the Unix man page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/snprintf(3)">snprintf(3)</a></em>.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_vsnprintf">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_vsnprintf</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="n"><span class="pre">size_t</span></span><span class="w"> </span><span class="n"><span class="pre">size</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">format</span></span>, <span class="n"><span class="pre">va_list</span></span><span class="w"> </span><span class="n"><span class="pre">va</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_vsnprintf" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Output not more than <em>size</em> bytes to <em>str</em> according to the format string
<em>format</em> and the variable argument list <em>va</em>. Unix man page
<em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/vsnprintf(3)">vsnprintf(3)</a></em>.</p>
</dd></dl>

<p><a class="reference internal" href="#c.PyOS_snprintf" title="PyOS_snprintf"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_snprintf()</span></code></a> and <a class="reference internal" href="#c.PyOS_vsnprintf" title="PyOS_vsnprintf"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_vsnprintf()</span></code></a> wrap the Standard C library
functions <code class="xref c c-func docutils literal notranslate"><span class="pre">snprintf()</span></code> and <code class="xref c c-func docutils literal notranslate"><span class="pre">vsnprintf()</span></code>. Their purpose is to
guarantee consistent behavior in corner cases, which the Standard C functions do
not.</p>
<p>The wrappers ensure that <code class="docutils literal notranslate"><span class="pre">str[size-1]</span></code> is always <code class="docutils literal notranslate"><span class="pre">'\0'</span></code> upon return. They
never write more than <em>size</em> bytes (including the trailing <code class="docutils literal notranslate"><span class="pre">'\0'</span></code>) into str.
Both functions require that <code class="docutils literal notranslate"><span class="pre">str</span> <span class="pre">!=</span> <span class="pre">NULL</span></code>, <code class="docutils literal notranslate"><span class="pre">size</span> <span class="pre">&gt;</span> <span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">format</span> <span class="pre">!=</span> <span class="pre">NULL</span></code>
and <code class="docutils literal notranslate"><span class="pre">size</span> <span class="pre">&lt;</span> <span class="pre">INT_MAX</span></code>. Note that this means there is no equivalent to the C99
<code class="docutils literal notranslate"><span class="pre">n</span> <span class="pre">=</span> <span class="pre">snprintf(NULL,</span> <span class="pre">0,</span> <span class="pre">...)</span></code> which would determine the necessary buffer size.</p>
<p>The return value (<em>rv</em>) for these functions should be interpreted as follows:</p>
<ul class="simple">
<li><p>When <code class="docutils literal notranslate"><span class="pre">0</span> <span class="pre">&lt;=</span> <span class="pre">rv</span> <span class="pre">&lt;</span> <span class="pre">size</span></code>, the output conversion was successful and <em>rv</em>
characters were written to <em>str</em> (excluding the trailing <code class="docutils literal notranslate"><span class="pre">'\0'</span></code> byte at
<code class="docutils literal notranslate"><span class="pre">str[rv]</span></code>).</p></li>
<li><p>When <code class="docutils literal notranslate"><span class="pre">rv</span> <span class="pre">&gt;=</span> <span class="pre">size</span></code>, the output conversion was truncated and a buffer with
<code class="docutils literal notranslate"><span class="pre">rv</span> <span class="pre">+</span> <span class="pre">1</span></code> bytes would have been needed to succeed. <code class="docutils literal notranslate"><span class="pre">str[size-1]</span></code> is <code class="docutils literal notranslate"><span class="pre">'\0'</span></code>
in this case.</p></li>
<li><p>When <code class="docutils literal notranslate"><span class="pre">rv</span> <span class="pre">&lt;</span> <span class="pre">0</span></code>, “something bad happened.” <code class="docutils literal notranslate"><span class="pre">str[size-1]</span></code> is <code class="docutils literal notranslate"><span class="pre">'\0'</span></code> in
this case too, but the rest of <em>str</em> is undefined. The exact cause of the error
depends on the underlying platform.</p></li>
</ul>
<p>The following functions provide locale-independent string to number conversions.</p>
<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_strtoul">
<span class="kt"><span class="pre">unsigned</span></span><span class="w"> </span><span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_strtoul</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ptr</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">base</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_strtoul" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Convert the initial part of the string in <code class="docutils literal notranslate"><span class="pre">str</span></code> to an <span class="c-expr sig sig-inline c"><span class="kt">unsigned</span><span class="w"> </span><span class="kt">long</span></span> value according to the given <code class="docutils literal notranslate"><span class="pre">base</span></code>, which must be between <code class="docutils literal notranslate"><span class="pre">2</span></code> and
<code class="docutils literal notranslate"><span class="pre">36</span></code> inclusive, or be the special value <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>Leading white space and case of characters are ignored.  If <code class="docutils literal notranslate"><span class="pre">base</span></code> is zero
it looks for a leading <code class="docutils literal notranslate"><span class="pre">0b</span></code>, <code class="docutils literal notranslate"><span class="pre">0o</span></code> or <code class="docutils literal notranslate"><span class="pre">0x</span></code> to tell which base.  If
these are absent it defaults to <code class="docutils literal notranslate"><span class="pre">10</span></code>.  Base must be 0 or between 2 and 36
(inclusive).  If <code class="docutils literal notranslate"><span class="pre">ptr</span></code> is non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code> it will contain a pointer to the
end of the scan.</p>
<p>If the converted value falls out of range of corresponding return type,
range error occurs (<code class="xref c c-data docutils literal notranslate"><span class="pre">errno</span></code> is set to <code class="xref c c-macro docutils literal notranslate"><span class="pre">ERANGE</span></code>) and
<code class="xref c c-macro docutils literal notranslate"><span class="pre">ULONG_MAX</span></code> is returned.  If no conversion can be performed, <code class="docutils literal notranslate"><span class="pre">0</span></code>
is returned.</p>
<p>See also the Unix man page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/strtoul(3)">strtoul(3)</a></em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_strtol">
<span class="kt"><span class="pre">long</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_strtol</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">str</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ptr</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">base</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_strtol" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Convert the initial part of the string in <code class="docutils literal notranslate"><span class="pre">str</span></code> to an <span class="c-expr sig sig-inline c"><span class="kt">long</span></span> value
according to the given <code class="docutils literal notranslate"><span class="pre">base</span></code>, which must be between <code class="docutils literal notranslate"><span class="pre">2</span></code> and <code class="docutils literal notranslate"><span class="pre">36</span></code>
inclusive, or be the special value <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
<p>Same as <a class="reference internal" href="#c.PyOS_strtoul" title="PyOS_strtoul"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_strtoul()</span></code></a>, but return a <span class="c-expr sig sig-inline c"><span class="kt">long</span></span> value instead
and <code class="xref c c-macro docutils literal notranslate"><span class="pre">LONG_MAX</span></code> on overflows.</p>
<p>See also the Unix man page <em class="manpage"><a class="manpage reference external" href="https://manpages.debian.org/strtol(3)">strtol(3)</a></em>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_string_to_double">
<span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_string_to_double</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">s</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">endptr</span></span>, <a class="reference internal" href="structures.html#c.PyObject" title="PyObject"><span class="n"><span class="pre">PyObject</span></span></a><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">overflow_exception</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_string_to_double" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Convert a string <code class="docutils literal notranslate"><span class="pre">s</span></code> to a <span class="c-expr sig sig-inline c"><span class="kt">double</span></span>, raising a Python
exception on failure.  The set of accepted strings corresponds to
the set of strings accepted by Python’s <a class="reference internal" href="../library/functions.html#float" title="float"><code class="xref py py-func docutils literal notranslate"><span class="pre">float()</span></code></a> constructor,
except that <code class="docutils literal notranslate"><span class="pre">s</span></code> must not have leading or trailing whitespace.
The conversion is independent of the current locale.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">endptr</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, convert the whole string.  Raise
<a class="reference internal" href="../library/exceptions.html#ValueError" title="ValueError"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> and return <code class="docutils literal notranslate"><span class="pre">-1.0</span></code> if the string is not a valid
representation of a floating-point number.</p>
<p>If endptr is not <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, convert as much of the string as
possible and set <code class="docutils literal notranslate"><span class="pre">*endptr</span></code> to point to the first unconverted
character.  If no initial segment of the string is the valid
representation of a floating-point number, set <code class="docutils literal notranslate"><span class="pre">*endptr</span></code> to point
to the beginning of the string, raise ValueError, and return
<code class="docutils literal notranslate"><span class="pre">-1.0</span></code>.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">s</span></code> represents a value that is too large to store in a float
(for example, <code class="docutils literal notranslate"><span class="pre">&quot;1e500&quot;</span></code> is such a string on many platforms) then
if <code class="docutils literal notranslate"><span class="pre">overflow_exception</span></code> is <code class="docutils literal notranslate"><span class="pre">NULL</span></code> return <code class="docutils literal notranslate"><span class="pre">Py_HUGE_VAL</span></code> (with
an appropriate sign) and don’t set any exception.  Otherwise,
<code class="docutils literal notranslate"><span class="pre">overflow_exception</span></code> must point to a Python exception object;
raise that exception and return <code class="docutils literal notranslate"><span class="pre">-1.0</span></code>.  In both cases, set
<code class="docutils literal notranslate"><span class="pre">*endptr</span></code> to point to the first character after the converted value.</p>
<p>If any other error occurs during the conversion (for example an
out-of-memory error), set the appropriate Python exception and
return <code class="docutils literal notranslate"><span class="pre">-1.0</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_double_to_string">
<span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_double_to_string</span></span></span><span class="sig-paren">(</span><span class="kt"><span class="pre">double</span></span><span class="w"> </span><span class="n"><span class="pre">val</span></span>, <span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="n"><span class="pre">format_code</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">precision</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="n"><span class="pre">flags</span></span>, <span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">ptype</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_double_to_string" title="Link to this definition">¶</a><br /></dt>
<dd><em class="stableabi"> Part of the <a class="reference internal" href="stable.html#stable"><span class="std std-ref">Stable ABI</span></a>.</em><p>Convert a <span class="c-expr sig sig-inline c"><span class="kt">double</span></span> <em>val</em> to a string using supplied
<em>format_code</em>, <em>precision</em>, and <em>flags</em>.</p>
<p><em>format_code</em> must be one of <code class="docutils literal notranslate"><span class="pre">'e'</span></code>, <code class="docutils literal notranslate"><span class="pre">'E'</span></code>, <code class="docutils literal notranslate"><span class="pre">'f'</span></code>, <code class="docutils literal notranslate"><span class="pre">'F'</span></code>,
<code class="docutils literal notranslate"><span class="pre">'g'</span></code>, <code class="docutils literal notranslate"><span class="pre">'G'</span></code> or <code class="docutils literal notranslate"><span class="pre">'r'</span></code>.  For <code class="docutils literal notranslate"><span class="pre">'r'</span></code>, the supplied <em>precision</em>
must be 0 and is ignored.  The <code class="docutils literal notranslate"><span class="pre">'r'</span></code> format code specifies the
standard <a class="reference internal" href="../library/functions.html#repr" title="repr"><code class="xref py py-func docutils literal notranslate"><span class="pre">repr()</span></code></a> format.</p>
<p><em>flags</em> can be zero or more of the values <code class="docutils literal notranslate"><span class="pre">Py_DTSF_SIGN</span></code>,
<code class="docutils literal notranslate"><span class="pre">Py_DTSF_ADD_DOT_0</span></code>, or <code class="docutils literal notranslate"><span class="pre">Py_DTSF_ALT</span></code>, or-ed together:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Py_DTSF_SIGN</span></code> means to always precede the returned string with a sign
character, even if <em>val</em> is non-negative.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Py_DTSF_ADD_DOT_0</span></code> means to ensure that the returned string will not look
like an integer.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Py_DTSF_ALT</span></code> means to apply “alternate” formatting rules.  See the
documentation for the <a class="reference internal" href="#c.PyOS_snprintf" title="PyOS_snprintf"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyOS_snprintf()</span></code></a> <code class="docutils literal notranslate"><span class="pre">'#'</span></code> specifier for
details.</p></li>
</ul>
<p>If <em>ptype</em> is non-<code class="docutils literal notranslate"><span class="pre">NULL</span></code>, then the value it points to will be set to one of
<code class="docutils literal notranslate"><span class="pre">Py_DTST_FINITE</span></code>, <code class="docutils literal notranslate"><span class="pre">Py_DTST_INFINITE</span></code>, or <code class="docutils literal notranslate"><span class="pre">Py_DTST_NAN</span></code>, signifying that
<em>val</em> is a finite number, an infinite number, or not a number, respectively.</p>
<p>The return value is a pointer to <em>buffer</em> with the converted string or
<code class="docutils literal notranslate"><span class="pre">NULL</span></code> if the conversion failed. The caller is responsible for freeing the
returned string by calling <a class="reference internal" href="memory.html#c.PyMem_Free" title="PyMem_Free"><code class="xref c c-func docutils literal notranslate"><span class="pre">PyMem_Free()</span></code></a>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_stricmp">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_stricmp</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">s1</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">s2</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_stricmp" title="Link to this definition">¶</a><br /></dt>
<dd><p>Case insensitive comparison of strings. The function works almost
identically to <code class="xref c c-func docutils literal notranslate"><span class="pre">strcmp()</span></code> except that it ignores the case.</p>
</dd></dl>

<dl class="c function">
<dt class="sig sig-object c" id="c.PyOS_strnicmp">
<span class="kt"><span class="pre">int</span></span><span class="w"> </span><span class="sig-name descname"><span class="n"><span class="pre">PyOS_strnicmp</span></span></span><span class="sig-paren">(</span><span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">s1</span></span>, <span class="k"><span class="pre">const</span></span><span class="w"> </span><span class="kt"><span class="pre">char</span></span><span class="w"> </span><span class="p"><span class="pre">*</span></span><span class="n"><span class="pre">s2</span></span>, <a class="reference internal" href="intro.html#c.Py_ssize_t" title="Py_ssize_t"><span class="n"><span class="pre">Py_ssize_t</span></span></a><span class="w"> </span><span class="n"><span class="pre">size</span></span><span class="sig-paren">)</span><a class="headerlink" href="#c.PyOS_strnicmp" title="Link to this definition">¶</a><br /></dt>
<dd><p>Case insensitive comparison of strings. The function works almost
identically to <code class="xref c c-func docutils literal notranslate"><span class="pre">strncmp()</span></code> except that it ignores the case.</p>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="arg.html"
                          title="previous chapter">Parsing arguments and building values</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="hash.html"
                          title="next chapter">PyHash API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li>
        <a href="https://github.com/python/cpython/blob/main/Doc/c-api/conversion.rst"
            rel="nofollow">Show Source
        </a>
      </li>
    </ul>
  </div>
        </div>
<div id="sidebarbutton" title="Collapse sidebar">
<span>«</span>
</div>

      </div>
      <div class="clearer"></div>
    </div>  
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="hash.html" title="PyHash API"
             >next</a> |</li>
        <li class="right" >
          <a href="arg.html" title="Parsing arguments and building values"
             >previous</a> |</li>

          <li><img src="../_static/py.svg" alt="Python logo" style="vertical-align: middle; margin-top: -1px"/></li>
          <li><a href="https://www.python.org/">Python</a> &#187;</li>
          <li class="switchers">
            <div class="language_switcher_placeholder"></div>
            <div class="version_switcher_placeholder"></div>
          </li>
          <li>
              
          </li>
    <li id="cpython-language-and-version">
      <a href="../index.html">3.12.3 Documentation</a> &#187;
    </li>

          <li class="nav-item nav-item-1"><a href="index.html" >Python/C API Reference Manual</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="utilities.html" >Utilities</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">String conversion and formatting</a></li>
                <li class="right">
                    

    <div class="inline-search" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" aria-label="Quick search" type="search" name="q" id="search-box" />
          <input type="submit" value="Go" />
        </form>
    </div>
                     |
                </li>
            <li class="right">
<label class="theme-selector-label">
    Theme
    <select class="theme-selector" oninput="activateTheme(this.value)">
        <option value="auto" selected>Auto</option>
        <option value="light">Light</option>
        <option value="dark">Dark</option>
    </select>
</label> |</li>
            
      </ul>
    </div>  
    <div class="footer">
    &copy; 
      <a href="../copyright.html">
    
    Copyright
    
      </a>
     2001-2024, Python Software Foundation.
    <br />
    This page is licensed under the Python Software Foundation License Version 2.
    <br />
    Examples, recipes, and other code in the documentation are additionally licensed under the Zero Clause BSD License.
    <br />
    
      See <a href="/license.html">History and License</a> for more information.<br />
    
    
    <br />

    The Python Software Foundation is a non-profit corporation.
<a href="https://www.python.org/psf/donations/">Please donate.</a>
<br />
    <br />
      Last updated on Apr 09, 2024 (13:47 UTC).
    
      <a href="/bugs.html">Found a bug</a>?
    
    <br />

    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.2.6.
    </div>

  </body>
</html>