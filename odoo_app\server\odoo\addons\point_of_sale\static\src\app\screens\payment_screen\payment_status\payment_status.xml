<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="point_of_sale.PaymentScreenStatus">
        <section class="paymentlines-container bg-view p-3 mb-1 border-bottom">
            <div t-if="props.order.get_paymentlines().length === 0" class="paymentlines-empty">
                <div class="total text-center py-2 text-success">
                    <t t-esc="totalDueText" />
                </div>
                <div class="message text-center">
            Please select a payment method.
                </div>
            </div>

            <t t-else="">
                <div class="payment-status-container d-flex flex-column-reverse flex-lg-row justify-content-between fs-2">
                    <div>
                        <div class="payment-status-remaining">
                            <span class="label pe-2">Remaining</span>
                            <span class="amount" t-att-class="{ 'highlight text-primary fw-bolder': props.order.get_due() > 0 }">
                                <t t-esc="remainingText" />
                            </span>
                        </div>
                        <div class="payment-status-total-due py-2 fs-3 text-muted">
                            <span class="label pe-2">Total Due</span>
                            <span>
                                <t t-esc="totalDueText" />
                            </span>
                        </div>
                    </div>
                    <div>
                        <div class="payment-status-change">
                            <span class="label pe-2">Change</span>
                            <span class="amount" t-att-class="{ 'highlight text-primary fw-bolder': props.order.get_change() > 0 }">
                                <t t-esc="changeText" />
                            </span>
                        </div>
                    </div>
                </div>
            </t>
        </section>
    </t>
</templates>
