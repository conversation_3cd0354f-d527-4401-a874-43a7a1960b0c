<?xml version="1.0"?>
<odoo>
    <!-- Activity types config -->
    <record id="mail_activity_type_action_config_maintenance" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">['|', ('res_model', '=', False), ('res_model', '=', 'maintenance.request')]</field>
        <field name="context">{'default_res_model': 'maintenance.request'}</field>
    </record>
    <menuitem id="maintenance_menu_config_activity_type"
        action="mail_activity_type_action_config_maintenance"
        parent="menu_maintenance_configuration"
        sequence="20"
        groups="base.group_no_one"/>
</odoo>