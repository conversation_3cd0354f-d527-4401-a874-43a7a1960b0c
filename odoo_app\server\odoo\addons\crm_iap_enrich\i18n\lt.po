# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# <PERSON><PERSON> <linask<PERSON><EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Leisti praturtinti rankiniu būdu"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "An error occurred during lead enrichment"
msgstr ""

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: Praturtinti iniciatyvas (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Praturtinti"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr ""

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr ""

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr ""

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr ""

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Lead enriched based on email address"
msgstr ""

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Iniciatyva/Galimybė"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Not enough credits for Lead Enrichment"
msgstr ""

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "The leads/opportunities have successfully been enriched"
msgstr ""

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""
